rror("cannot write without any engine.", "resource://gre/modules/SearchSettings.sys.mjs", 280))
DEBUG: LocationBase::SetURI called - triggering JavaScript breakpoint
DEBUG: Using page's global JavaScript object
DEBUG: About to execute script with debugger statement in page context
DEBUG: Script with debugger statement executed in page context
DEBUG: URL changed to: https://www.baidu.com/
console.error: "Unable to find target with innerWindowId:10737418241"
NetworkHelper.parseSecurityInfo threw an exception: Security state 0 has no known STATE_IS_* flags.
console.error: "Security state 0 has no known STATE_IS_* flags."
Crash Annotation GraphicsCriticalError: |[0][GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt (t=100.235) [GFX1-]: RenderCompositorSWGL failed mapping default framebuffer, no dt
JavaScript error: , line 0: NotFoundError: No such JSProcessActor 'DevToolsProcess'
JavaScript error: , line 0: NotFoundError: No such JSProcessActor 'DevToolsProcess'
JavaScript error: , line 0: NotFoundError: No such JSProcessActor 'DevToolsProcess'
JavaScript error: chrome://juggler/content/Helper.js, line 82: NS_ERROR_FAILURE: Component returned failure code: 0x80004005 (NS_ERROR_FAILURE) [nsIWebProgress.removeProgressListener]

