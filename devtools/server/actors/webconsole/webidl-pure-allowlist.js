/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

// This file is automatically generated by the GenerateDataFromWebIdls.py
// script. Do not modify it manually.
"use strict";

module.exports = {
  DOMTokenList: {
    prototype: ["item", "contains"],
  },
  Document: {
    prototype: [
      "getSelection",
      "hasStorageAccess",
      "getElementsByTagName",
      "getElementsByTagNameNS",
      "getElementsByClassName",
      "getElementsByName",
      "getElementById",
      "querySelector",
      "querySelectorAll",
      "createNSResolver",
    ],
  },
  Element: {
    prototype: [
      "getAttributeNames",
      "getAttribute",
      "getAttributeNS",
      "hasAttribute",
      "hasAttributeNS",
      "hasAttributes",
      "closest",
      "matches",
      "webkitMatchesSelector",
      "getElementsByTagName",
      "getElementsByTagNameNS",
      "getElementsByClassName",
      "mozMatchesSelector",
      "getAsFlexContainer",
      "getGridFragments",
      "hasGridFragments",
      "getElementsWithGrid",
      "querySelector",
      "querySelectorAll",
    ],
  },
  FormData: {
    prototype: ["entries", "keys", "values"],
  },
  Headers: {
    prototype: ["entries", "keys", "values"],
  },
  Node: {
    prototype: [
      "getRootNode",
      "hasChildNodes",
      "isSameNode",
      "isEqualNode",
      "compareDocumentPosition",
      "contains",
      "lookupPrefix",
      "lookupNamespaceURI",
      "isDefaultNamespace",
    ],
  },
  Performance: {
    prototype: ["now"],
  },
  Range: {
    prototype: [
      "isPointInRange",
      "comparePoint",
      "intersectsNode",
      "getClientRects",
      "getBoundingClientRect",
    ],
  },
  Selection: {
    prototype: ["getRangeAt", "containsNode"],
  },
  URLSearchParams: {
    prototype: ["entries", "keys", "values"],
  },
};
