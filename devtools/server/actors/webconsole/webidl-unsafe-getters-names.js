/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

// This file is automatically generated by the GenerateDataFromWebIdls.py
// script. Do not modify it manually.
"use strict";

module.exports = [
  "InstallTrigger",
  "farthestViewportElement",
  "mozInputSource",
  "mozPressure",
  "nearestViewportElement",
  "onmouseenter",
  "onmouseleave",
  "onmozfullscreenchange",
  "onmozfullscreenerror",
  "onreadystatechange",
];
