/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

/**
 * Do not use this interface. Instead, write:
 *     const { addDebuggerToGlobal } = ChromeUtils.importESModule(
 *       "resource://gre/modules/jsdebugger.sys.mjs"
 *     );
 *     addDebuggerToGlobal(global);
 */
[scriptable, uuid(a36fa816-31da-4b23-bc97-6412771f0867)]
interface IJSDebugger : nsISupports
{
  /**
   * Define the global Debugger constructor on a given global.
   */
  [implicit_jscontext]
  void addClass(in jsval global);
};
