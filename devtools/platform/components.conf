# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{ec5aa99c-7abb-4142-ac5f-aab2419e38e2}',
        'contract_ids': ['@mozilla.org/jsinspector;1'],
        'type': 'mozilla::jsinspector::nsJSInspector',
        'headers': ['/devtools/platform/nsJSInspector.h'],
    },
    {
        'cid': '{0365cbd5-d46e-4e94-a39f-83b63cd1a963}',
        'contract_ids': ['@mozilla.org/jsdebugger;1'],
        'type': 'mozilla::jsdebugger::JSDebugger',
        'headers': ['/devtools/platform/JSDebugger.h'],
    },
]
