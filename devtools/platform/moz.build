# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

XPCSHELL_TESTS_MANIFESTS += ["tests/xpcshell/xpcshell.toml"]

XPIDL_SOURCES += [
    "IJSDebugger.idl",
    "nsIJSInspector.idl",
]

XPIDL_MODULE = "jsdevtools"

SOURCES += [
    "JSDebugger.cpp",
    "nsJSInspector.cpp",
]

XPCOM_MANIFESTS += [
    "components.conf",
]

EXTRA_JS_MODULES += [
    "jsdebugger.sys.mjs",
]

# The /devtools/moz.build opted in to treatment of devtools as a browser
# component, but for this platform integration code it is simpler not too.
DIST_SUBDIR = ""

FINAL_LIBRARY = "xul"
