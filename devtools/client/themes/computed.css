/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

 .theme-dark {
  --row-striped-background: rgba(255, 255, 255, 0.05);
 }

.theme-light {
  --row-striped-background: rgba(247, 247, 247, 0.8);
}

#sidebar-panel-computedview {
  margin: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

/* Reset the global rotation of the icon done for RTL layout.
   Computed view is always LTR */
#sidebar-panel-computedview .theme-twisty:not(.open):dir(rtl),
#sidebar-panel-computedview .theme-twisty:not([open]):-moz-locale-dir(rtl) {
  transform: rotate(-90deg);
}

#computedview-panel .inspector-tabpanel {
  min-width: 0;
}

#computedview-panel #browser-style-checkbox-label {
  overflow: hidden;
  text-overflow: ellipsis;
}

#computed-container {
  overflow: auto;
  height: 100%;
}

/* This extra wrapper only serves as a way to get the content of the view focusable.
   So that when the user reaches it either via keyboard or mouse, we know that the view
   is focused and therefore can handle shortcuts.
   However, for accessibility reasons, tabindex is set to -1 to avoid having to tab
   through it, and the outline is hidden. */
#computed-container-focusable {
  height: 100%;
  outline: none;
}

#computed-toolbar {
  display: flex;
  align-items: center;
  user-select: none;
}

#browser-style-checkbox {
  /* Bug 1200073 - extra space before the browser styles checkbox so
     they aren't squished together in a small window. Put also
     an extra space after. */
  margin-inline-start: 5px;
  margin-inline-end: 0;
}

#browser-style-checkbox-label {
  white-space: nowrap;
  padding-inline-start: 5px;
  margin-inline-end: 5px;
}

#computed-property-container {
  user-select: text;
  overflow-y: auto;
  overflow-x: hidden;
  list-style: none;
  margin: 0;
  padding: 0;
  /* Ajust outline to make it visible, otherwise it could be clipped, as the container takes the whole window size */
  outline-offset: -2px;
}

#computed-property-container > :nth-child(2n of .computed-property-view) {
  background: var(--row-striped-background);
}

.computed-property-hidden {
  display: none;
}

.computed-property-view {
  padding: 2px 0;
  padding-inline-start: 5px;
  display: flex;
  flex-wrap: wrap;
  /* Ajust outline to make it visible as the list items take the the whole window width */
  outline-offset: -2px;
}

.computed-property-name-container {
  width: 230px;
}

.computed-property-value-container {
  display: flex;
  flex: 1 1 168px;
  overflow: hidden;
}

.computed-property-name-container > *,
.computed-property-value-container > * {
  display: inline-block;
  vertical-align: middle;
}

.computed-property-name {
  outline: 0 !important;
}

.computed-other-property-value::before {
  content: "";
  display: inline-block;
  vertical-align: -0.5px;
  width: 8px;
  height: 8px;
  margin: 0 1px;
  background-image: url(images/arrow-e.svg);
  background-repeat: no-repeat;
  background-size: contain;
  -moz-context-properties: fill;
  fill: var(--theme-icon-dimmed-color);
}

.computed-other-property-value:dir(rtl)::before {
  transform: scaleX(-1);
}

.computed-property-value {
  padding-inline-start: 14px;
  outline: 0 !important;
}

.invalid-at-computed-value-time-warning {
  display: inline-block;
  width: 12px;
  height: 12px;
  vertical-align: middle;
  margin-inline-start: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  -moz-context-properties: fill;
  background-image: var(--invalid-at-computed-value-time-icon);
  fill: var(--invalid-at-computed-value-time-fill-color);

  &[hidden] {
    display: none;
  }
}

.matchedselectors {
  width: 100%;
  padding-inline-start: 12px;

  /*
   * Matched selectors element is always created, but is empty until the arrow is expanded.
   * We only want the margin to appear when the matched selectors are visible.
   */
  &:not(:empty) {
    margin-block-start: 4px;
  }
}

/* Bug 1360238 - getSelection displays an extra "\n" on multiple sibling block elements.
   We rely on this behavior to add an extra "\n" between matched selectors (Bug 1222737).
   Therefore we use <div> elements around matched selectors and need this class
   to keep them inline. We do that to avoid doing any formatting logic in JS.
   Once Bug 1360238 will be fixed, we'll probably have to change the behavior
   and remove this class. */
.fix-get-selection {
  display: inline;
}

.visually-hidden {
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  width: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
}

/* From skin */
.computed-expander {
  visibility: hidden;
}

.computed-expandable {
  visibility: visible;
}

.match {
  visibility: hidden;
}

.matchedselectors > p {
  clear: both;
  margin: 0;
  margin-inline-end: 2px;
  padding: 2px;
  overflow-x: hidden;
  border-style: dotted;
  border-color: var(--theme-splitter-color);
  border-width: 1px 1px 0 1px;

  /* Make the border more visible in High Contrast Mode */
  :root[forced-colors-active]  &{
    border-style: solid;
  }
}

.matchedselectors > p:last-of-type {
  border-bottom-width: 1px;
}

.rule-text.matched {
  text-decoration: line-through;
}


#computed-no-results {
  height: 100%;
}

.onlyuserstyles {
  cursor: pointer;
}

.legendKey {
  margin: 0 5px;
}

.computed-link {
  padding: 0 3px;
  cursor: pointer;
  float: inline-end;
  color: var(--theme-internal-link-color);
}

/* Take away these two :visited rules to get a core dumper     */
/* See https://bugzilla.mozilla.org/show_bug.cgi?id=575675#c30 */

.computed-link {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}

.computed-property-value .inspector-colorswatch {
  --swatch-size: 0.9em;
}
