/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* This file is loaded by both browser.xhtml and toolbox.xhtml. Therefore, rules
   defined here can not rely on toolbox.xhtml variables. */

/* Splitters */

:root {
  --devtools-splitter-color: #e0e0e2  /* --grey-25 */;
  /* Use :where() so we don't get a higher specificity for this rule */
  &:where([devtoolstheme="dark"]) {
    --devtools-splitter-color: #38383d /* --grey-70 */;
  }

  /* We can't use (forced-colors: active) as forced-colors doesn't apply as is,
     but @media (forced-colors) should be enough to detect HCM */
  @media (forced-colors) {
    --devtools-splitter-color: CanvasText;
  }

  /* Draggable splitter element size */
  --devtools-splitter-element-size: 1px;
  --devtools-emphasized-splitter-element-size: 2px;

  /* Define the widths of the draggable areas on each side of a splitter. top
     and bottom widths are used for horizontal splitters, inline-start and
     inline-end for side splitters.*/

  --devtools-splitter-top-width: 2px;
  --devtools-splitter-bottom-width: 2px;
  --devtools-horizontal-splitter-min-height: calc(var(--devtools-splitter-top-width) +
                                                  var(--devtools-splitter-bottom-width) +
                                                  var(--devtools-splitter-element-size));
  --devtools-emphasized-horizontal-splitter-min-height: calc(var(--devtools-splitter-top-width) +
                                                             var(--devtools-splitter-bottom-width) +
                                                             var(--devtools-emphasized-splitter-element-size));

  /* Small draggable area on inline-start to avoid overlaps on scrollbars.*/
  --devtools-splitter-inline-start-width: 1px;
  --devtools-splitter-inline-end-width: 4px;

  --devtools-vertical-splitter-min-width: calc(var(--devtools-splitter-inline-start-width) +
                                               var(--devtools-splitter-inline-end-width) +
                                               var(--devtools-splitter-element-size));
}

splitter.devtools-horizontal-splitter,
.devtools-side-splitter {
  appearance: none;
  background-image: none;
  border: 0;
  border-style: solid;
  border-color: transparent;
  background-color: var(--devtools-splitter-color);
  background-clip: content-box;
  position: relative;

  box-sizing: border-box;

  z-index: var(--browser-stack-z-index-devtools-splitter, 1);

  /* opt-out of forced colors because the transparent borders are turned into
     opaque ones in High Contrast Mode. --devtools-splitter-color has specific value
     in High Contrast Mode so we should be fine */
     forced-color-adjust: none;
}

splitter.devtools-horizontal-splitter {
  min-height: var(--devtools-horizontal-splitter-min-height);

  border-top-width: var(--devtools-splitter-top-width);
  border-bottom-width: var(--devtools-splitter-bottom-width);

  margin-top: calc(-1 * var(--devtools-splitter-top-width) - 1px);
  margin-bottom: calc(-1 * var(--devtools-splitter-bottom-width));

  cursor: ns-resize;
}

#toolbox-deck ~ splitter.devtools-horizontal-splitter {
  min-height: var(--devtools-emphasized-horizontal-splitter-min-height);
}

/**
 * Emphasized splitter has the hover style.
 * This emphasized splitter affect splitter console only.
 */
#toolbox-deck ~ splitter.devtools-horizontal-splitter:hover {
  background-color: var(--theme-emphasized-splitter-color-hover);
}

.devtools-side-splitter {
  min-width: var(--devtools-vertical-splitter-min-width);

  border-inline-start-width: var(--devtools-splitter-inline-start-width);
  border-inline-end-width: var(--devtools-splitter-inline-end-width);

  margin-inline-start: calc(-1 * var(--devtools-splitter-inline-start-width) - 1px);
  margin-inline-end: calc(-1 * var(--devtools-splitter-inline-end-width));

  cursor: ew-resize;
}

.devtools-horizontal-splitter.disabled,
.devtools-side-splitter.disabled {
  pointer-events: none;
}
