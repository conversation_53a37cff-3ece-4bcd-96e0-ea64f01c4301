/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* Import stylesheets for specific tooltip widgets */
@import url(chrome://devtools/skin/accessibility-color-contrast.css);
@import url(chrome://devtools/skin/badge.css);
@import url(chrome://devtools/skin/inspector-shared.css);
@import url(chrome://devtools/skin/variables.css);
@import url(chrome://devtools/content/shared/widgets/cubic-bezier.css);
@import url(chrome://devtools/content/shared/widgets/filter-widget.css);
@import url(chrome://devtools/content/shared/widgets/linear-widget.css);
@import url(chrome://devtools/content/shared/widgets/spectrum.css);
/*
 * compatibility.css is primarily used by the Compatibility panel, so import it in a layer
 * to give it a lower importance than the rule here and mitigate styling mismatch.
 */
@import url(chrome://devtools/skin/compatibility.css) layer(shared);

/* The following properties values are consumed by the client for drawing in canvas
 * (e.g. for the CubicBezierWidget).
 * Since they're defined in :root with light-dark(), we need to register them so we'll get
 * the actual final (rgb) color, and not the declaration with substituted variables
 */
 @property --bezier-diagonal-color {
  syntax: '<color>';
  inherits: true;
  initial-value: transparent;
}

@property --timing-function-control-point-background {
  syntax: '<color>';
  inherits: true;
  initial-value: transparent;
}

:root {
  --theme-arrowpanel-border-radius: 3.5px;

  --bezier-diagonal-color: light-dark(rgba(0, 0, 0, 0.2), #eee);
  --timing-function-grid-color: light-dark(rgba(0, 0, 0, 0.05), rgba(255, 255, 255, 0.1));
  --timing-function-line-color: #4C9ED9;
  --timing-function-preview-scale: light-dark(var(--grey-43), var(--grey-40));
  --timing-function-preview-dot-border: white;
  --timing-function-control-point-background: light-dark(var(--grey-55), var(--grey-20));

  /* Tooltips */
  --theme-tooltip-color: light-dark(var(--theme-body-color), var(--theme-text-color-strong));
  --theme-tooltip-background: light-dark(rgb(255, 255, 255), var(--theme-popup-background));
  --theme-tooltip-shadow-color: light-dark(var(--grey-90-a10), rgba(25, 25, 25, 0.76));

  /* Doorhangers */
  /* These colors are based on the colors used for doorhangers elsewhere in Firefox. */
  --theme-arrowpanel-background: var(--theme-popup-background);
  --theme-arrowpanel-color: var(--theme-popup-color);
  --theme-arrowpanel-border-color: var(--theme-popup-border-color);
  --theme-arrowpanel-separator: light-dark(ThreeDShadow, rgba(249,249,250,.1));
  --theme-arrowpanel-active-background: light-dark(hsla(0,0%,80%,.45), rgba(249,249,250,.15));
  --theme-arrowpanel-active-outline-color: transparent;
  --theme-arrowpanel-disabled-color: light-dark(GrayText, rgba(249,249,250,.5));

  &[forced-colors-active] {
    /* The active state have a different background but with an outline in HCM */
    --theme-arrowpanel-active-background: SelectedItem;
    --theme-arrowpanel-active-color: SelectedItemText;
    --theme-arrowpanel-active-outline-color: ButtonText;

    /* This is used in a repeated gradient to create a grid pattern.
       Using GrayText directly makes it hard to distinguish the
       other elements in the chart, so lower the color opacity a bit.
       It should be fine as it's not an important element. Also, this can
       work even if we don't have a resulting system color, as the
       consumers of this variable already need to opt out of forced colors
       so it can be used in the background gradient */
    --timing-function-grid-color: light-dark(
      /* In light mode, the grid appears much more contrasted, so lower the opacity even more */
      rgb(from GrayText r g b / 0.4),
      rgb(from GrayText r g b / 0.7)
    );
    --timing-function-line-color: var(--theme-highlight-blue);
    --timing-function-preview-dot-border: CanvasText;
    --timing-function-control-point-background: ButtonText;
    --bezier-diagonal-color: CanvasText;
  }
}

:root[platform="mac"].theme-light {
  --theme-arrowpanel-separator: hsla(210,4%,10%,.14);
}

/*
  There's a specific color-scheme defined for panels in https://searchfox.org/mozilla-central/rev/02841791400cf7cf5760c0cfaf31f5d772624253/toolkit/themes/shared/popup.css#9-13
  This could create issues when the Firefox theme is at odd with the DevTools one (e.g.
  if Firefox uses a dark theme but the user has a DevTools light theme.
  Unset the color-scheme on those element so it uses the one we set on :root in common.css,
  which matches our light/dark theme.
*/
menupopup, panel {
  color-scheme: unset
}

strong {
  font-weight: bold;
}

/* Tooltip: CSS variables tooltip */

.tooltip-container:has(.devtools-tooltip-css-variable) {
  /* By default, in dark mode, the tooltip background color is lighter than the background
     color of the Rules view. Since we're using the same color than in the Rules view
     for CSS values, this can lead to poor contrast.
     To prevent this, we apply the same background color than in the Rules view */
  --theme-tooltip-background: var(--theme-body-background);
}

.devtools-tooltip-css-variable {
  --block-padding: 8px;
  color: var(--theme-body-color);
  padding: var(--block-padding) 8px;
  direction: ltr;
  overflow-wrap: break-word;
  /* Try to show the content of the tooltip on a single line */
  inline-size: max-content;
  min-inline-size: 60px;
  max-inline-size: min(500px, 100vw);

  /* If only the "top" section is displayed, to avoid smaller text (e.g. `10px`) to look unbalanced */
  .variable-value:not(:has(+ .variable-tooltip-section)) {
    text-align: center;
  }
  /* If there are other sections, add some space below the value */
  .variable-value:has(+ .variable-tooltip-section) {
    padding-block-end: var(--block-padding);
  }

  .variable-tooltip-section {
    border-block-start: 1px solid var(--theme-splitter-color);
    margin: 0;
    padding-block: var(--block-padding);

    h2 {
      margin-block: 0;
      padding-block-start: 0;
      padding-block-end: var(--block-padding);
    }

    dl {
      padding: 0;
      margin: 0;
    }

    dt,dd {
      display: inline;
    }

    dt {
      color: var(--theme-highlight-blue);
      padding-inline-end: 4px;
    }

    dd {
      color: var(--theme-highlight-red);
      margin: 0;
      padding: 0;
    }
  }
}


/* Tooltip: Compatibility tooltip */

.devtools-tooltip-css-compatibility {
  color: var(--theme-body-color);
  padding: 2px;
  direction: ltr;
}

/* Tooltip: JS Evaluation Context */

#webconsole-input-evaluationsButton > .tooltip-panel {
  /* helps the JS Context selector to have scrollbars when content exceed the vertical size of the popup */
  overflow-y: auto;
}


/* Tooltip: Inactive CSS tooltip */

.devtools-tooltip-inactive-css,
.devtools-tooltip-css-compatibility {
  color: var(--theme-arrowpanel-color);
  margin: 0;
  padding: 10px 14px 12px 14px;
  font-size: 12px;
}

.devtools-tooltip-inactive-css,
.devtools-tooltip-css-compatibility,
.devtools-tooltip-inactive-css strong,
.devtools-tooltip-css-compatibility strong {
  user-select: text;
  -moz-user-focus: normal;
}

.devtools-tooltip-inactive-css p,
.devtools-tooltip-css-compatibility p {
  margin-block-start: 0;
  margin-block-end: calc(1em - 4px);
}

.devtools-tooltip-inactive-css p:last-child,
.devtools-tooltip-css-compatibility p:last-child {
  margin-block-end: 0;
}

.devtools-tooltip-inactive-css .link,
.devtools-tooltip-css-compatibility .link {
  color: var(--theme-link-color);
  cursor: pointer;
}

/* Tooltip: Compatibility tooltip */

.tooltip-container[type="doorhanger"] .tooltip-panel ul.compatibility-unsupported-browser-list {
  width: 100%;
  padding: 0;
  display: flex;
  justify-content: start;
}

/* Tooltip: query container info */
.devtools-tooltip-query-container {
  color: var(--theme-arrowpanel-color);
  margin: 0;
  padding: 10px 14px 12px;
  font-size: 12px;
  user-select: text;
  -moz-user-focus: normal;
}

.devtools-tooltip-query-container header {
  --block-end-space: 5px;
  border-block-end: 1px solid var(--theme-popup-dimmed);
  padding-block-end: var(--block-end-space);
  margin-block-end: var(--block-end-space);
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--theme-body-color);
}

.devtools-tooltip-query-container ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.devtools-tooltip-query-container .objectBox-node .tag-name {
  color: var(--theme-highlight-blue);
}

.devtools-tooltip-query-container .objectBox-node .attribute-name {
  color: var(--theme-highlight-red);
}

.devtools-tooltip-query-container .property-name {
  color: var(--theme-highlight-blue);
}

.devtools-tooltip-query-container .property-value {
  color: var(--theme-highlight-red);
}


/* Tooltip: Tiles */

.devtools-tooltip-tiles {
  background-color: #eee;
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc),
    linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.tooltip-container {
  display: none;
  position: fixed;
  z-index: 9999;
  background: transparent;
  pointer-events: none;
  overflow: clip;
  filter: drop-shadow(0 2px 8px var(--theme-tooltip-shadow-color));
}

.tooltip-xul-wrapper {
  /* All these are drawn by the tooltip-container, effectively */
  appearance: none;
  background: transparent;
  border: none;
  padding: 0;
  -moz-window-shadow: none;
  --panel-border-radius: 0px;
  --panel-padding: 0px;
  --panel-background: transparent;
  --panel-border-color: transparent;
  --panel-shadow: none;

  /* This is enough room for the shadow of the tooltip-container */
  --panel-shadow-margin: 10px;

  &::part(content) {
    border: none;
    /* Avoid clipping the drop-shadow */
    overflow: visible;
  }

  &.non-interactive-toggle {
    /* We need to make toggled-on-hover panels transparent to events, otherwise
     * hovering over the panel shadow would toggle the panel. */
    pointer-events: none;
  }

  /* The panel is absolutely positioned itself. */
  > .tooltip-container {
    position: relative;
  }
}

.tooltip-top {
  flex-direction: column;
}

.tooltip-bottom {
  flex-direction: column-reverse;
}

.tooltip-panel {
  background-color: var(--theme-tooltip-background);
  pointer-events: all;
  flex-grow: 1;
}

/* Adding the scrollbar in the RDM tooltip */
#device-selector-menu .tooltip-panel {
  max-height: 500px;
}

#device-selector-menu .tooltip-panel > .checkbox-container {
  height: 100%;
  overflow-y: auto;
}

.tooltip-visible {
  display: flex;
}

.tooltip-hidden {
  display: flex;
  visibility: hidden;
}

/* Tooltip : flexible height styles */

.tooltip-flexible-height .tooltip-panel {
  /* In flexible mode the tooltip panel should only grow according to its content. */
  flex-grow: 0;
}

.tooltip-flexible-height .tooltip-filler {
  /* In flexible mode the filler should grow as much as possible. */
  flex-grow: 1;
}

/* Tooltip : arrow style */

.tooltip-container[type="arrow"] > .tooltip-panel {
  position: relative;
  min-height: 10px;
  box-sizing: border-box;
  width: 100%;
  border-radius: var(--theme-arrowpanel-border-radius);
}

.tooltip-top[type="arrow"] .tooltip-panel {
  top: 0;
}

.tooltip-bottom[type="arrow"] .tooltip-panel {
  bottom: 0;
}

.tooltip-arrow {
  position: relative;
  box-sizing: border-box;
  height: 16px;
  width: 32px;
  overflow: hidden;
  flex-shrink: 0;
}

/* In RTL locales and context, only use RTL on the tooltip content, keep LTR for positioning */
.tooltip-container:-moz-locale-dir(rtl),
.tooltip-container:dir(rtl) {
  direction: ltr;
}

.tooltip-panel:-moz-locale-dir(rtl),
.tooltip-panel:dir(rtl) {
  direction: rtl;
}

.tooltip-top .tooltip-arrow {
  /**
   * The -1px margin is there to make sure the middle of the arrow overlaps with
   * the border of the tooltip container.
   * The -2px is there because the rotated arrow is not visually as tall as its
   * container. Since the positioning logic relies on measuring the size of the
   * tooltip, this -2px ensures the measured size matches the visuals (and not
   * simply the box model).
   */
  margin-bottom: -2px;
  margin-top: -1px;
}

.tooltip-bottom .tooltip-arrow {
  /* See comment in .tooltip-top .tooltip-arrow (inverted here) */
  margin-bottom: -1px;
  margin-top: -2px;
}

.tooltip-arrow::before {
  content: "";
  position: absolute;
  width: 21px;
  height: 21px;
  margin-left: 4px;
  background: var(--theme-tooltip-background);
  border: 0 none;
  pointer-events: all;
  box-sizing: border-box;
}

.tooltip-bottom .tooltip-arrow::before {
  margin-top: 5px;
  transform: rotate(225deg);
}

.tooltip-top .tooltip-arrow::before {
  margin-top: -12px;
  transform: rotate(45deg);
}

/* XUL panels have a default border, but pure HTML tooltips don't have one. */
.tooltip-container[type="arrow"] > .tooltip-panel,
.tooltip-container[type="arrow"] > .tooltip-arrow::before {
  border: 1px solid var(--theme-arrowpanel-border-color);
}

/* Tooltip : doorhanger style */

.tooltip-container[type="doorhanger"] {
  > .tooltip-panel {
    color: var(--theme-arrowpanel-color);
    margin: 0;
    padding: 0;
    max-width: 320px;
  }

  > .tooltip-panel,
  > .tooltip-arrow::before {
    background: var(--theme-arrowpanel-background);
    border: 1px solid var(--theme-arrowpanel-border-color);
    border-radius: var(--theme-arrowpanel-border-radius);
    box-shadow: 0 0 4px hsla(210,4%,10%,.2);
  }

  > .tooltip-arrow {
    /* Desired width of the arrow */
    --arrow-width: 20px;

    /* Amount of room to allow for the shadow. Should be about half the radius. */
    --shadow-radius: 4px;
    --shadow-margin: calc(var(--shadow-radius) / 2);

    /*
     * Crop the arrow region to show half the arrow plus allow room for margins.
     *
     * The ARROW_WIDTH in HTMLTooltip.js needs to match the following value.
     */
    width: calc(var(--arrow-width) + 2 * var(--shadow-margin));
    height: calc(var(--arrow-width) / 2 + var(--shadow-margin));

    /**
     * The rotated box slightly overlaps the left edge of the arrow container.
     * This means the arrow is not centered in its own box by default.
     */
    padding-left: 1px;

    &::before {
      /* Don't inherit any rounded corners. */
      border-radius: 0;

      /*
       * When the box is rotated, it should have width <arrow-width>.
       * That makes the length of one side of the box equal to:
       *
       *    (<arrow-width> / 2) / sin 45
       */
      --sin-45: 0.707106781;
      --square-side: calc(var(--arrow-width) / 2 / var(--sin-45));
      width: var(--square-side);
      height: var(--square-side);

      /*
       * The rotated square will overshoot the left side
       * and need to be shifted in by:
       *
       *   (<arrow-width> - square side) / 2
       *
       * But we also want to shift it in so that the box-shadow
       * is not clipped when we clip the parent so we add
       * a suitable margin for that.
       */
      --overhang: calc((var(--arrow-width) - var(--square-side)) / 2);
      margin-left: calc(var(--overhang) + var(--shadow-margin));
    }
  }

  &.tooltip-top > .tooltip-arrow {
    /* Overlap the arrow with the 1px border of the doorhanger */
    margin-top: -1px;

    &::before {
      /* Show only the bottom half of the box */
      margin-top: calc(var(--square-side) / -2);
    }
  }

  &.tooltip-bottom > .tooltip-arrow {
    /* Overlap the arrow with the 1px border of the doorhanger */
    margin-bottom: -1px;

    &::before {
      /* Shift the rotated box in so that it is not clipped */
      margin-top: calc(var(--overhang) + var(--shadow-margin));
    }
  }

  .menu-standard-padding {
    margin: 0;
    padding: 6px 0;
  }

  .tooltip-panel ul {
    /* Override the display: flex declaration in xul.css
     * or else menu items won't stack. */
    display: block;
    list-style: none;
  }

  .tooltip-panel li {
    list-style: none;
  }

  .menuitem > .command {
    display: flex;
    align-items: baseline;
    margin: 0;
    padding: 4px 12px;
    /*
     * It doesn't really make sense, since if this is just about making the items
     * easier to click we should be using min-width instead.
     */
    margin-inline-end: 15px;

    &:focus-visible {
      --theme-outline-offset: -2px;
    }

    &:not([disabled],[open],:active):hover {
      background-color: var(--theme-popup-hover-background);
      color: var(--theme-popup-hover-color);
    }

    &:not([disabled]):is([open],:hover:active) {
      background-color: var(--theme-arrowpanel-active-background);
      color: var(--theme-arrowpanel-active-color);
      box-shadow: 0 1px 0 hsla(210,4%,10%,.03) inset;
      outline: 1px solid var(--theme-arrowpanel-active-outline-color);
    }

    &[disabled] {
      color: var(--theme-text-color-inactive);
    }

    &[aria-checked="true"] {
      list-style-image: none;
      -moz-context-properties: fill;
      fill: currentColor;
      background: url(chrome://devtools/skin/images/check.svg) no-repeat transparent;
      background-position: 7px center;

      &:dir(rtl),
      &:-moz-locale-dir(rtl) {
        background-position-x: right 7px;
      }
    }

    > .label {
      flex: 1;
      font: menu;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    > .accelerator {
      margin-inline-start: 10px;
      color: var(--theme-arrowpanel-disabled-color);
      font: message-box;
    }
  }

  /* Use :where to avoid having a high specificity that would take over the rule above */
  .menuitem > button.command:where(
    [role="menuitem"],
    [role="menuitemcheckbox"],
    [role="link"]
  ) {
    appearance: none;
    border: none;
    background-color: transparent;
    text-align: start;
    width: 100%;

    &:not([disabled]) {
      color: var(--theme-arrowpanel-color);
    }
  }

  .checkbox-container .menuitem > .command > .label {
    padding-inline-start: 16px;
  }

  hr {
    display: block;
    border: none;
    border-top: 1px solid var(--theme-arrowpanel-separator);
    margin: 6px 0;
    padding: 0;
  }

  [role="menuseparator"] {
    border: none;
    border-bottom: 1px solid #cacdd3;
    width: 100%;
    height: 2px;
    display: block;
    background: none;
    pointer-events: none;
  }
}

.menu-button--iconic::before,
.tooltip-container[type="doorhanger"] .menuitem > .command.iconic > .label::before {
  content: " ";
  display: inline-block;
  margin-inline-end: 8px;
  width: 16px;
  height: 16px;
  /* Better optical alignment than with 'vertical-align: middle'.
     Works well with font sizes between 12px and 16px. */
  vertical-align: -3px;
  -moz-context-properties: fill;
  fill: currentColor;
  background-image: var(--menuitem-icon-image);
  background-size: contain;
  background-repeat: no-repeat;
  /*
   * The icons in the sidebar menu have opacity: 0.8 here, but those in the
   * hamburger menu don't. For now we match the hamburger menu styling,
   * especially because the 80% opacity makes the icons look dull in dark mode.
   */
}

/* Tooltip: Events */

.devtools-tooltip-events-container {
  border-radius: var(--theme-arrowpanel-border-radius);
  height: 100%;
  overflow-y: auto;
  margin: 0;
  padding: 0;
}

@media (-moz-overlay-scrollbars) {
  .devtools-tooltip-events-container {
    padding-inline: calc(env(scrollbar-inline-size) / 2);
  }
}

.devtools-tooltip-events-container .event-header {
  display: flex;
  align-items: center;
  box-sizing: content-box;
  height: 24px;
  padding: 0 4px;
  cursor: pointer;
  overflow: hidden;
  color: var(--theme-tooltip-color);
  background-color: var(--theme-tooltip-background);
}

.devtools-tooltip-events-container > li + li .event-header {
  border-top: 1px solid var(--theme-splitter-color);
}

.event-header .theme-twisty {
  flex: none;
  width: 12px;
  height: 12px;
  margin-inline-end: 4px;
  fill: currentColor;
  border: none;
  background-color: transparent;
}

.event-header .theme-twisty[aria-expanded="true"] {
  transform: none;
}

.event-tooltip-listener-toggle-checkbox {
  flex-shrink: 0;
}

.event-tooltip-debugger-icon {
  -moz-context-properties: stroke;
  stroke: var(--theme-icon-color);
  background-image: url("chrome://devtools/content/shared/components/reps/images/jump-definition.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  border: none;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  margin-inline-end: 4px;
  flex-shrink: 0;
  cursor: pointer;
}

.event-tooltip-debugger-icon:hover {
  background-color: var(--toolbarbutton-hover-background);
}

.event-tooltip-event-type,
.event-tooltip-filename,
.event-tooltip-attributes {
  margin-inline-start: 0;
  flex-shrink: 0;
  cursor: pointer;
}

.event-tooltip-event-type {
  font-weight: bold;
  font-size: 12px;
  line-height: 16px;
}

.event-tooltip-filename {
  margin: 0 5px;
  font-size: 100%;
  flex-shrink: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Force ellipsis to be displayed on the left */
  direction: rtl;
}

.event-tooltip-content-box {
  display: none;
  height: 100px;
  overflow: hidden;
  margin-inline-end: 0;
  border: 1px solid var(--theme-splitter-color);
  border-width: 1px 0 0 0;
}

.event-toolbox-content-box iframe {
  height: 100%;
  border-style: none;
}

.event-tooltip-content-box[open] {
  display: block;
}

.event-tooltip-source-container {
  margin-top: 5px;
  margin-bottom: 10px;
  margin-inline-start: 5px;
  margin-inline-end: 0;
}

.event-tooltip-source {
  margin-bottom: 0;
}

.event-tooltip-attributes-container {
  display: flex;
  flex-shrink: 0;
  flex-grow: 1;
  justify-content: flex-end;
}

.event-tooltip-attributes-box {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  height: 14px;
  border-radius: 3px;
  padding: 1px 3px;
  margin-inline-start: 4px;
  background-color: var(--theme-text-color-alt);
  color: var(--theme-body-background);
}

.event-tooltip-attributes {
  margin: 0;
  font-size: 9px;
  line-height: 14px;
}

.event-tooltip-editor-frame {
  border-style: none;
  height: 100%;
  width: 100%;
}

/* Tooltip: HTML Search */

#searchbox-panel-listbox {
  width: 250px;
  max-width: 250px;
  overflow-x: hidden;
}

#searchbox-panel-listbox .autocomplete-item,
#searchbox-panel-listbox .autocomplete-item[selected] {
  overflow-x: hidden;
}

/* Tooltip: Image tooltip */

/*
 * Overview of the Image Tooltip layout.
 *
 * ┌─────────────────────────────────────────┐
 * │ .devtools-tooltip-image-container       │
 * │ ┌─────────────────────────────────────┐ │
 * │ │ .devtools-tooltip-image-wrapper     │ │
 * │ │ ┌─────────────────────────────────┐ │ │
 * │ │ │ .devtools-tooltip-image         │ │ │
 * │ │ │ (the actual image tag)          │ │ │
 * │ │ └─────────────────────────────────┘ │ │
 * │ └─────────────────────────────────────┘ │
 * │ ┌─────────────────────────────────────┐ │
 * │ │ .devtools-tooltip-image-dimensions  │ │
 * │ └─────────────────────────────────────┘ │
 * └─────────────────────────────────────────┘
 *
 */

.devtools-tooltip-image-container {
  /* Saved as variables to be synchronized easily with ImageTooltipHelper.js */
  --image-tooltip-image-padding: 4px;
  --image-tooltip-label-height: 20px;

  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 100px;
  text-align: center;
}

.devtools-tooltip-image-wrapper {
  align-items: center;
  display: flex;
  flex: 1;
  padding: var(--image-tooltip-image-padding);
  justify-content: center;
  min-height: 1px
}

.devtools-tooltip-image {
  max-height: 100%;
}

.devtools-tooltip-image-dimensions {
  height: var(--image-tooltip-label-height);
  text-align: center;
}

.devtools-tooltip-image-broken {
  box-sizing: border-box;
  height: 100%;
  padding: 7px;
}

/* Tooltip: Invoke getter confirm Tooltip */

.invoke-confirm {
  color: var(--theme-popup-color);
  border: 1px solid rgba(0,0,0, 0.1);
  max-width: 212px;
}

.invoke-confirm .close-confirm-dialog-button::before {
  background-image: url("chrome://devtools/skin/images/close.svg");
}

.invoke-confirm .confirm-label {
  margin: 0;
  padding: 4px;
  background-color: var(--theme-toolbar-background-alt);
  display: flex;
  align-items: start;
}

.invoke-confirm .confirm-label p {
  margin: 0;
  padding: 0;
  flex-grow: 1;
  hyphens: auto;
}

.invoke-confirm .emphasized {
  font-family: var(--monospace-font-family);
  font-weight: bold;
  overflow-wrap: break-word;
}

.invoke-confirm .close-confirm-dialog-button {
  padding: 0;
  margin: 0;
  flex-grow: 0;
}

.invoke-confirm .confirm-button {
  background-color: var(--theme-selection-background);
  color: white;
  border: none;
  padding: 6px;
  display: block;
  width: 100%;
  text-align: left;
}

/* The button already has a "selected" style, we can remove the focus rings. */
.confirm-button:focus-visible {
  outline: none;
}

.invoke-confirm .learn-more-link {
  color: var(--theme-link-color);
  padding-inline-end: 4px;
  display: flex;
  align-items: center;
  justify-content: end;
  min-height: 20px;
  cursor: pointer;
}

.invoke-confirm .learn-more-link::after {
  content: "";
  width: 14px;
  height: 14px;
  fill: currentColor;
  -moz-context-properties: fill;
  background-image: url(chrome://devtools/skin/images/help.svg);
  background-size: contain;
  background-repeat: no-repeat;
  margin-inline-start: 4px;
}

/* Tooltip: Rule Preview */

.rule-preview-tooltip-container {
  display: flex;
  flex-direction: column;
  max-width: 200px;
  color: var(--theme-body-color);
  padding: 5px;
}

.rule-preview-tooltip-message {
  white-space: pre-wrap;
}

.rule-preview-tooltip-source-rule-footer {
  align-self: center;
  border-top: 1px solid var(--theme-body-color);
  margin-top: 5px;
  margin-right: 5px;
  margin-left: 5px;
  padding: 3px;
}

/* Tooltip: Selector warning */
.devtools-tooltip-selector-warnings {
  margin: 0;
  padding: 10px;
  list-style: none;

  & > li {
    padding: 0;

    & + li {
      border-block-start: 1px solid var(--theme-splitter-color);
    }
  }
}

/* Indent the content script items */
.webconsole-evaluation-selector-item.indented::before {
  content: "\2514\2500" !important;
  z-index:1000;
  margin-inline-start: 20px;
}
.checkbox-container .menuitem > .command.indented > .label {
  padding-inline-start: 4px;
}
