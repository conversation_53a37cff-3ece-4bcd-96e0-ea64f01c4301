/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

.perf {
  max-width: 670px;
  margin: 10vh auto;
  padding: 0 5%;
}

/* Intro */

.perf-intro-row {
  margin: 1.7em 0;
  display: flex;
  line-height: 1.8em;
  align-items: center;
}

.perf-intro-icon {
  width: 4em;
  height: 4em;
  margin-inline-end: 1.3em;
  background-image: url(chrome://devtools/skin/images/tool-profiler.svg);
  background-size: 100%;
  -moz-context-properties: fill;
  fill: currentColor;
  opacity: 0.5;
}

/* Shared rules */

.perf-toggle-label {
  padding: 7px 0;
  display: grid;
  grid-template-columns: max-content auto;
  max-width: max-content;
}

.perf-toggle-text-label {
  display: flex;
  align-items: center;
}

.perf-toggle-description {
  font-size: 13px;
  line-height: 1.8;
  color: var(--text-color-deemphasized);
  grid-column: 2;
}

/* Presets section */

.perf-presets {
  margin: 2em 0;
  border-bottom: 1px solid var(--in-content-border-color);
  padding-bottom: 1em;
}

/* Features and settings */

.perf-settings-row {
  display: flex;
  line-height: 1.8;
}

.perf-settings-range-row {
  display: grid;
  grid-template-columns: 140px 1fr 90px;
  align-items: center;
  min-height: 30px;
}

.perf-settings-range-input {
  margin: 0;
}

.perf-settings-range-value {
  text-align: end;
}

.perf-settings-thread-columns {
  display: flex;
  line-height: 1.3;
}

.perf-settings-thread-column {
  flex: 1;
}

.perf-settings-thread-label {
  margin-block: 12px;
}

.perf-settings-all-threads {
  margin-block: 12px;
}

.perf-settings-text-label {
  flex: 1;
}

.perf-settings-text-input {
  width: 100%;
  box-sizing: border-box;
}

#perftools-settings-thread-text {
  margin-inline: 0;
}

.perf-settings-checkbox-label-disabled > :not(input) {
  /* The checkboxes already get their opacity set to 0.5 in common.inc.css,
     so only target the text. The descriptions get their text deemphasized,
     so set a value a bit higher than 0.5 to compensate for that. */
  opacity: 0.65;
}

/* Local build section */

.perf-settings-dir-list {
  box-sizing: border-box;
  width: 100%;
  height: 100px;
  border: 1px solid var(--in-content-border-color);
  margin: 0;
  padding: 0;
  overflow-y: auto;
  background-color: var(--background-color-box);
}

.perf-settings-dir-list:hover {
  border-color: var(--border-color-interactive-hover);
}

.pref-settings-dir-list-item {
  display: flex;
  padding: 3px 5px;
}

.pref-settings-dir-list-item::before {
  content: url(chrome://devtools/skin/images/folder.svg);
  display: inline-flex;
  align-self: center;
  width: 12px;
  height: 12px;
  margin-inline-end: 5px;
  -moz-context-properties: fill;
  fill: currentColor;
}

.perf-settings-dir-list-button-group {
  padding-block: 4px;
}

.perf-settings-dir-list-button-group > button {
  margin-inline: 0 8px;
}

/* Back button */

.perf-back {
  margin-block-end: 13px;
}

.perf-back-button {
  /* Remove the inherited margins */
  margin-inline: 0;
}
