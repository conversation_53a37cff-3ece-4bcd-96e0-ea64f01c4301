/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

.perf {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.perf-devtools {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  margin-top: 10vh;
  padding-inline: 5vh;
}

.perf-button-image {
  vertical-align: middle;
  padding-inline-start: 8px;
  width: 13px;
  -moz-context-properties: fill;
  fill: #fff;
}

.perf-button-container {
  display: flex;
  flex-flow: column;
  align-items: center;
}

.perf-additional-message {
  font-size: 13px;
  font-weight: bold;
  font-style: italic;
}

.perf > * {
  max-width: 440px;
}

.perf-description {
  font-size: 13px;
  line-height: 1.4;
}

.perf-external-link {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  color: var(--theme-link-color);
  text-decoration: underline;
  white-space: nowrap;
  cursor: pointer;
  font-size: inherit;
}

/* See https://design.firefox.com/photon/components/buttons.html for the spec */
.perf-photon-button {
  padding: 0 8px;
  border: none;
  margin: 0;

  /* reset default styles */
  background: none;

  /* photon styles */
  background-color: var(--toolbarbutton-background);
  border-radius: 2px;
  color: var(--theme-body-color);
  font: inherit;
}

/* This is a Firefox-specific style because Firefox adds a focusring at a bad
 * position. We're adding our own below. */
.perf-photon-button::-moz-focus-inner {
  border: none;
}

.perf-photon-button:hover:active:not([disabled]) {
  background-color: var(--grey-90-a30);
}

.perf-photon-button-primary,
.perf-photon-button-default {
  min-width: 132px;
  height: 32px;
  padding: 0 8px;
  font-size: 13px;
}

.perf-photon-button-primary {
  background-color: var(--blue-60);
  color: #fff;
}

.perf-photon-button-primary:focus-visible {
  /* Avoid the outline to conflict with the blue background */
  outline-offset: 2px;
}

.perf-photon-button-primary:hover:not([disabled]) {
  background-color: var(--blue-70);
}

.perf-photon-button-primary:hover:active:not([disabled]) {
  background-color: var(--blue-80);
}

.perf-photon-button[disabled] {
  opacity: 0.6;
}

.perf-photon-button.perf-button {
  margin: 10px;
}

.perf-photon-button-ghost {
  width: 32px;
  height: 32px;
  background-color: transparent;
}

.perf-photon-button-ghost:hover:enabled {
  background-color: var(--theme-toolbarbutton-hover-background);
}

.perf-photon-button-ghost:hover:active:enabled {
  background-color: var(--theme-toolbarbutton-active-background);
}

.perf-photon-button-micro {
  height: 24px;
}

.perf-presets-hr {
  width: 100%;
  border: 1px solid var(--theme-splitter-color);
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

.perf-presets {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  width: 100%;
  margin-block: 12px;
}

.perf-presets-settings {
  margin-inline-end: 17px;
  margin-block: 3px;
  font-size: 13px;
  font-weight: bold;
}

.perf-presets-description {
  margin-block: 13px;
}

.perf-presets-select {
  /* Layout */
  position: relative;
  min-width: 186px;
  padding-block: 3px;
  padding-inline: 5px;

  /* Presentational: */
  appearance: none;
  color: var(--theme-select-color);
  background-color: var(--theme-select-background);
  background-image: url('chrome://devtools/skin/images/arrow-dropdown-12.svg');
  background-position: right 4px center;
  background-repeat: no-repeat;

  /* adapt the SVG color using the text color */
  -moz-context-properties: fill;
  fill: currentColor;

  border-radius: 2px;
  border: 1px solid transparent;
  font-size: 12px;
  font-weight: 400;
  text-decoration: none;
}

.perf-presets-select:hover {
  border: 1px solid var(--theme-select-hover-border-color);
}

.perf-presets-custom {
  padding-inline: 10px;
  margin-block: 13px;
  line-height: 1.3;
}

.perf-presets-custom-bold {
  font-weight: bold;
}

/* OnboardingMessage component styles */

/*
 * The grid layout of the onboarding message is:
 *
 *  +-----------------------------+---+
 *  |     onboarding message      | X |
 *  +-----------------------------+---+
 */
.perf-onboarding {
  background-color: var(--theme-body-alternate-emphasized-background);
  border-bottom: 1px solid var(--theme-splitter-color);
  display: grid;
  font-size: 13px;
  grid-template-columns: auto 36px;
  /* Override the max-width set on ".perf > *" */
  max-width: unset;
  width: 100%;
}

.perf-onboarding + .perf-devtools {
  margin-top: 15px;
}

.perf-onboarding-message {
  background-image: url(chrome://devtools/skin/images/fox-smiling.svg);
  background-position: 12px center;
  background-repeat: no-repeat;
  background-size: 22px;
  justify-self: center;
  margin: 12px 0;
  padding-inline-start: 46px;
}

.perf-onboarding-message:dir(rtl) {
  background-position-x: right 12px;
}

.perf-onboarding-message-row {
  margin: 6px 0;
}

.perf-onboarding-close-button {
  background-image: url("chrome://devtools/skin/images/close.svg");
  background-repeat: no-repeat;
  background-position: center;
  -moz-context-properties: fill;
  fill: currentColor;
  margin-top: 4px;
}
