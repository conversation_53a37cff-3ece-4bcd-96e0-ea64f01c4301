/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* The following properties values are consumed by the client for drawing in canvas
 * (for the Netmonitor waterfall ticks, the Font preview, …).
 * Since they're defined in :root with light-dark(), we need to register them so we'll get
 * the actual final (rgb) color, and not the declaration with subtituted variables
 */
@property --theme-highlight-blue {
  syntax: '<color>';
  inherits: true;
  initial-value: transparent;
}

@property --theme-highlight-red {
  syntax: '<color>';
  inherits: true;
  initial-value: transparent;
}

@property --theme-body-color {
  syntax: '<color>';
  inherits: true;
  initial-value: transparent;
}

:root {
  /* Text sizes */
  --theme-body-font-size: 11px;
  --theme-code-font-size: 11px;
  --theme-code-line-height: calc(15 / 11);

  /* Toolbar size (excluding borders) */
  --theme-toolbar-height: 24px;
  --theme-toolbar-tall-height: 28px;

  /* For accessibility purposes we want to enhance the focus styling. This
   * should improve keyboard navigation usability. */
  --theme-focus-outline-color: var(--blue-50);
  --theme-focus-textinput-outline-color: var(--theme-focus-outline-color);
  --theme-focus-outline-size: 2px;
  --theme-focus-outline: var(--theme-focus-outline-size) solid var(--theme-focus-outline-color);
  --theme-outline-offset: 0px;
    /**
   * we want the box shadow to be 2px bigger than the offset so we have a "double border"
   * (without it impacting the element layout), which should work whatever
   * the background-color is (e.g. a clickable element in a "selected" blue-background container)
   **/
  --theme-outline-box-shadow-size: calc(2px + var(--theme-focus-outline-size) + var(--theme-outline-offset));
  --theme-outline-box-shadow: 0 0 0 var(--theme-outline-box-shadow-size) var(--theme-body-background);

  /* Standardizes the height of items in the Watch Expressions and XHR Breakpoints panes */
  --expression-item-height: 20.5px;

  /* The photon animation curve */
  --animation-curve: cubic-bezier(0.07, 0.95, 0, 1);

  /* This should be similar to --highlighter-box-content-color from highlighter.css,
   * with lower opacity than the 0.6 we apply on highlighter so this can be used as
   * background color without being too bold
   */
  --dimmed-highlighter-box-content-color:hsl(197,71%,73%,.3);

  /*
   * Photon Colors CSS Variables v3.3.2
   * - Colors are taken from https://github.com/FirefoxUX/photon-colors/blob/master/photon-colors.css
   * - We only add Photon color variables that we are actually using; unused
   *   variables will fail browser/base/content/test/static/browser_parsable_css.js
   * - We added a few unofficial colors: a few intermediary values (e.g. Blue 45),
   *   and lighter variants for the dark theme (e.g. Red 20, Red 40).
   */
  --magenta-50: #ff1ad9;
  --magenta-65: #dd00a9;
  --magenta-70: #b5007f;

  --purple-50: #9400ff;
  --purple-60: #8000d7;
  --purple-30: #c069ff;

  --blue-30: #75baff;
  --blue-40: #45a1ff;
  --blue-50: #0a84ff;
  --blue-50-a30: rgba(10, 132, 255, 0.3);
  --blue-55: #0074e8;
  --blue-60: #0060df;
  --blue-70: #003eaa;
  --blue-80: #002275;

  --teal-60: #00c8d7;
  --teal-70: #008ea4;

  --green-50: #30e60b;
  --green-60: #12bc00;
  --green-70: #058b00;

  --yellow-50: #ffe900;
  --yellow-60: #d7b600;
  --yellow-65: #be9b00;
  --yellow-70: #a47f00;
  /* interpolated from yellow-70 and yellow-80 */
  --yellow-75: #8b6801;
  --yellow-80: #715100;

  --red-05: #ffe6e8;
  --red-20: #ffb3d2;
  --red-40: #ff3b6b;
  --red-40-a90: rgba(255, 59, 107, 0.9);
  --red-50: #ff0039;
  --red-60: #d70022;
  --red-70: #a4000f;

  --grey-10: #f9f9fa;
  --grey-10-a20: rgba(249, 249, 250, 0.2);
  --grey-10-a25: rgba(249, 249, 250, 0.25);
  --grey-10-a30: rgba(249, 249, 250, 0.3);
  --grey-20: #ededf0;
  --grey-25: #e0e0e2;
  --grey-30: #d7d7db;
  --grey-40: #b1b1b3;
  --grey-43: #a4a4a4;
  --grey-45: #939395;
  --grey-50: #737373;
  --grey-55: #5c5c5f;
  --grey-60: #4a4a4f;
  --grey-70: #38383d;
  --grey-80: #2a2a2e;
  --grey-85: #1b1b1d;
  --grey-90: #0c0c0d;
  --grey-90-a10: rgba(12, 12, 13, 0.1);
  --grey-90-a20: rgba(12, 12, 13, 0.2);
  --grey-90-a30: rgba(12, 12, 13, 0.3);

  --theme-body-background: light-dark(white, #232327);
  --theme-body-emphasized-background: light-dark(var(--grey-10), var(--grey-70));
  --theme-body-alternate-emphasized-background: light-dark(#f0f9fe, #353b48);
  --theme-sidebar-background: light-dark(white, #18181a);

  /* Toolbar */
  --theme-tab-toolbar-background: light-dark(var(--grey-10), var(--grey-90));
  --theme-toolbar-background: light-dark(var(--grey-10), #18181a);
  --theme-toolbar-alternate-background: light-dark(var(--grey-10), #232327);
  --theme-toolbar-color: light-dark(var(--grey-90), var(--grey-40));
  --theme-toolbar-selected-color: light-dark(var(--blue-60), white);
  --theme-toolbar-selected-background: transparent;
  --theme-toolbar-highlighted-color: light-dark(var(--green-60), var(--green-50));
  --theme-toolbar-background-hover: light-dark(rgba(221, 225, 228, 0.66), #232327);
  --theme-toolbar-background-alt: light-dark(#f5f5f5, var(--grey-85));
  --theme-toolbar-background-highlighted: light-dark(#b9dcff, var(--blue-60));
  --theme-toolbar-error-background: light-dark(var(--red-05), var(--red-70));
  --theme-toolbar-hover: light-dark(var(--grey-20), #232327);
  --theme-toolbar-hover-color: var(--theme-toolbar-color);
  --theme-toolbar-alternate-hover: light-dark(var(--grey-20), var(--grey-80));
  --theme-toolbar-hover-active: light-dark(var(--grey-20), #252526);
  --theme-toolbar-selected-hover: var(--theme-toolbar-hover);
  --theme-toolbar-selected-hover-color: var(--theme-toolbar-selected-color);
  --theme-toolbar-separator: light-dark(var(--grey-90-a10), var(--grey-10-a20));
  --theme-toolbar-reordering-background: var(--theme-toolbar-hover);
  --theme-toolbar-reordering-color: var(--theme-toolbar-hover-color);

  /* Toolbar buttons */
  --toolbarbutton-background: light-dark(var(--grey-10), var(--grey-70));
  --toolbarbutton-hover-background: light-dark(var(--grey-20), var(--grey-70));
  --toolbarbutton-hover-color: inherit;
  --toolbarbutton-checked-background: var(--toolbarbutton-hover-background);
  --toolbarbutton-checked-color: var(--theme-icon-checked-color);

  /* Buttons */
  --theme-button-background: light-dark(rgba(12, 12, 13, 0.05), rgba(249, 249, 250, 0.1));
  --theme-button-active-background: light-dark(rgba(12, 12, 13, 0.1), rgba(249, 249, 250, 0.15));

  /* Accordion headers */
  --theme-accordion-header-background: var(--theme-toolbar-alternate-background);
  --theme-accordion-header-color: inherit;
  --theme-accordion-header-hover-background: var(--theme-toolbar-alternate-hover);
  --theme-accordion-header-hover-color: inherit;

  /* Selection */
  --theme-selection-background: light-dark(var(--blue-55), #204e8a);
  --theme-selection-background-hover: light-dark(#f0f9fe, #353b48);
  --theme-selection-focus-background: light-dark(var(--toolbarbutton-hover-background), var(--grey-60));
  --theme-selection-color: #ffffff;

  /* Text Selection/Highlight */
  --theme-text-selection-background: light-dark(rgb(185, 215, 253), #353b48);
  --theme-text-selection-unfocused-background: light-dark(rgb(210, 210, 210), var(--theme-text-selection-background));

  /* Border color that splits the toolbars/panels/headers. */
  --theme-splitter-color: light-dark(var(--grey-25), var(--grey-70));
  --theme-emphasized-splitter-color: light-dark(var(--grey-30), var(--grey-60));
  --theme-emphasized-splitter-color-hover: light-dark(var(--grey-40), var(--grey-50));

  /* Icon colors */
  --theme-icon-color: light-dark(rgba(12, 12, 13, 0.8), rgba(249, 249, 250, 0.7));
  --theme-icon-hover-color: var(--theme-icon-color);
  --theme-icon-dimmed-color: light-dark(rgba(135, 135, 137, 0.9), rgba(147, 147, 149, 0.9));
  --theme-icon-disabled-color: var(--theme-icon-dimmed-color);
  --theme-icon-checked-color: light-dark(var(--blue-60), var(--blue-30));
  --theme-icon-error-color: light-dark(var(--red-60), var(--red-40));
  --theme-icon-warning-color: light-dark(var(--yellow-65), var(--yellow-60));
  /* Some buttons changes the icon color to blue on hover */
  --theme-icon-alternate-hover-color: var(--theme-icon-checked-color);

  /* Text color */
  --theme-comment: light-dark(var(--grey-55), var(--grey-45));
  --theme-body-color: light-dark(var(--grey-70), var(--grey-40));
  --theme-link-color: light-dark(var(--blue-60), #75bfff);
  --theme-internal-link-color: light-dark(var(--blue-70), var(--blue-40));
  --theme-text-color-alt: light-dark(var(--grey-50), var(--grey-45));
  --theme-text-color-inactive: light-dark(var(--grey-50), var(--grey-45));
  --theme-text-color-error: light-dark(var(--red-60), var(--grey-10));
  --theme-text-color-strong: light-dark(var(--grey-90), var(--grey-30));
  --theme-stack-trace-text: light-dark(var(--red-70), var(--red-20));

  --theme-highlight-green: light-dark(var(--green-70), #86de74);
  --theme-highlight-blue: light-dark(var(--blue-55), #75bfff);
  --theme-highlight-purple: light-dark(var(--blue-70), #b98eff);
  --theme-highlight-red: light-dark(var(--magenta-65), #ff7de9);
  --theme-highlight-yellow: #fff89e;

  /* These theme-highlight color variables have not been photonized. */
  --theme-highlight-bluegrey: light-dark(#0072ab, #5e88b0);
  --theme-highlight-lightorange: light-dark(#d97e00, #d99b28);
  --theme-highlight-orange: light-dark(#f13c00, #d96629);
  --theme-highlight-pink: light-dark(#b82ee5, #df80ff);
  --theme-highlight-gray: light-dark(#dde1e4, #e9f4fe);

  /* Colors that were used in Graphs in the old performance tools, which was removed.
   * They're also used on other panels and should be renamed (See Bug 1767617) */
  --theme-graphs-purple: light-dark(#b693eb, #df80ff);
  --theme-graphs-yellow: light-dark(#efc052, #d99b28);
  --theme-graphs-orange: light-dark(#d97e00, #d96629);
  --theme-graphs-grey: light-dark(#cccccc, #757873);
  --theme-graphs-full-red: #f00;

  /* Common popup styles(used by HTMLTooltip and autocomplete) */
  --theme-popup-background: light-dark(Field, var(--grey-60));
  --theme-popup-color: light-dark(FieldText, rgb(249, 249, 250));
  --theme-popup-border-color: light-dark(ThreeDShadow, #27272b);
  --theme-popup-dimmed: light-dark(hsla(0, 0%, 80%, 0.3), rgba(249, 249, 250, 0.1));
  --theme-popup-hover-background: var(--theme-popup-dimmed);
  --theme-popup-hover-color: var(--theme-popup-color);

  /* Styling for devtool buttons */
  --theme-toolbarbutton-background: none;
  --theme-toolbarbutton-color: light-dark(var(--grey-70), var(--grey-40));
  --theme-toolbarbutton-hover-background: color-mix(in srgb, currentColor 14%, transparent);
  --theme-toolbarbutton-hover-color: var(--theme-toolbarbutton-color);
  --theme-toolbarbutton-checked-background: color-mix(in srgb,var(--theme-toolbarbutton-checked-color) 5%,transparent);
  --theme-toolbarbutton-checked-color: light-dark(var(--blue-60), var(--blue-30));
  --theme-toolbarbutton-checked-hover-background: var(--theme-toolbarbutton-hover-background);
  --theme-toolbarbutton-checked-hover-color: var(--theme-toolbarbutton-color);
  --theme-toolbarbutton-active-background: color-mix(in srgb,var(--theme-toolbarbutton-checked-color) 20%,transparent);


  /* Used for select elements */
  /* Note: we don't use the pair of system properties Field/FieldText here (like
   * above), because this lightgrey background makes it possible to remove the
   * border, which is more stylish. The downsides is that we don't adjust with
   * the OS' dark mode setting. Hopefully this trade-off is OK.
   */
  --theme-select-background: light-dark(var(--grey-20), var(--grey-60));
  --theme-select-color: light-dark(#0c0c0d, #fff);
  --theme-select-hover-border-color: light-dark(var(--grey-30), var(--grey-50));

  /* Warning colors */
  --theme-warning-background: light-dark(hsl(54, 100%, 92%), hsl(42, 37%, 19%));
  --theme-warning-border: light-dark(
      /* Yellow 60 + opacity */
      rgba(215, 182, 0, 0.28) ,
      hsl(60, 30%, 26%)
    );
  --theme-warning-color: light-dark(var(--yellow-80), hsl(43, 94%, 81%));

/* Flashing colors used to highlight updates */
  --theme-contrast-background: light-dark(
    /* = Yellow 50-a40 on white */
    #fff699,
    /* = Yellow 50-a20 on body background */
    #4f4b1f
  );
  --theme-contrast-background-alpha: light-dark(
    /* Yellow 50-a40 */
    rgba(255, 233, 0, 0.4),
    /* Yellow 50-a15 */
    rgba(255, 233, 0, 0.15)
  );
  --theme-contrast-color: light-dark(black, white);
  /* This is used for search/filter results underline and need to have a 3:1 contrast against the background */
  --theme-contrast-border: light-dark(var(--yellow-70), var(--yellow-65));

  --theme-search-results-background: var(--theme-contrast-background);
  --theme-search-results-color: var(--theme-contrast-color);
  --theme-search-results-border-color: var(--theme-contrast-border);

  &[forced-colors-active] {
    /* Color picked from HCM palette created by a11y team */
    --theme-highlight-blue: light-dark(#0060DF, #00DDFF);
    --theme-highlight-red: light-dark(#C50042, #FF9AA2);
    --theme-highlight-green: light-dark(#005E5E, #3FE1B0);
    --theme-highlight-purple: #952BB9;
    --theme-highlight-purple-text: #F7E2FF;
    --theme-highlight-yellow: light-dark(#FFFFCC, #FFEA80);
    --theme-highlight-orange: light-dark(#CC3D00, #FFB587);
    --theme-highlight-pink: light-dark(#FF9AA2, #FFDFE7);
    --theme-highlight-gray: light-dark(#5B5B66, #F0F0F4);

    --theme-body-background: Canvas;

    /* Toolbar */
    --theme-toolbar-background: ButtonFace;
    --theme-toolbar-selected-color: SelectedItemText;
    --theme-toolbar-selected-background: SelectedItem;
    --theme-toolbar-hover: SelectedItemText;
    --theme-toolbar-hover-color: SelectedItem;
    --theme-toolbar-hover-active: var(--theme-toolbar-hover);
    --theme-toolbar-selected-hover: var(--theme-toolbar-selected-background);
    --theme-toolbar-selected-hover-color: var(--theme-toolbar-selected-color);
    --theme-toolbar-reordering-background: var(--theme-toolbar-selected-background);
    --theme-toolbar-reordering-color: var(--theme-toolbar-selected-color);

    /* Accordion headers */
    --theme-accordion-header-background: ButtonFace;
    --theme-accordion-header-color: ButtonText;
    --theme-accordion-header-hover-background: ButtonFace;
    --theme-accordion-header-hover-color: SelectedItem;

    /* Toolbar buttons */
    --toolbarbutton-background: ButtonFace;
    --toolbarbutton-hover-background: SelectedItemText;
    --toolbarbutton-hover-color: SelectedItem;
    --toolbarbutton-checked-background: SelectedItem;
    --toolbarbutton-checked-color: SelectedItemText;

    /* Styling for devtool buttons */
    --theme-toolbarbutton-background: ButtonFace;
    --theme-toolbarbutton-color: ButtonText;
    --theme-toolbarbutton-hover-background: SelectedItemText;
    --theme-toolbarbutton-hover-color: SelectedItem;
    --theme-toolbarbutton-checked-background: SelectedItem;
    --theme-toolbarbutton-checked-color: SelectedItemText;
    --theme-toolbarbutton-checked-hover-background: var(--theme-toolbarbutton-checked-background);
    --theme-toolbarbutton-checked-hover-color: var(--theme-toolbarbutton-checked-color);

    /* Common popup styles(used by HTMLTooltip and autocomplete) */
    --theme-popup-background: Canvas;
    --theme-popup-color: CanvasText;
    --theme-popup-border-color: CanvasText;

    /* Text color */
    --theme-selection-background: SelectedItem;
    --theme-selection-color: SelectedItemText;
    --theme-text-color-alt: CanvasText;
    --theme-text-color-strong: CanvasText;
    --theme-text-color-inactive: GrayText;
    --theme-text-selection-background: Highlight;
    --theme-text-selection-color: HighlightText;
    --theme-text-selection-unfocused-background: Highlight;
    --theme-body-color: CanvasText;
    --theme-link-color: LinkText;
    --theme-internal-link-color: LinkText;


    --theme-focus-outline-color: CanvasText;
    --theme-focus-textinput-outline-color: SelectedItem;
    --theme-icon-color: ButtonText;
    --theme-icon-hover-color: SelectedItem;
    /* We don't want dimmed colors in high Contrast Mode, use the same colors as non-dimmed icons */
    --theme-icon-dimmed-color: var(--theme-icon-color);
    --theme-icon-disabled-color: GrayText;
    --theme-icon-error-color: ButtonText;
    --theme-icon-warning-color: ButtonText;
    --theme-icon-checked-color: SelectedItemText;
    --theme-icon-alternate-hover-color: SelectedItem;

    --theme-search-results-background: Mark;
    --theme-search-results-color: MarkText;
    --theme-search-results-border-color: MarkText;

    /* Menu/Autocomplete items */
    --theme-popup-hover-background: SelectedItem;
    --theme-popup-hover-color: SelectedItemText;

    --theme-splitter-color: CanvasText;
    --theme-emphasized-splitter-color: CanvasText;
    --theme-emphasized-splitter-color-hover: SelectedItem;

    /* Flashing colors used to highlight updates */
    --theme-contrast-background: Mark;
    --theme-contrast-background-alpha: var(--theme-contrast-background);
    --theme-contrast-color: MarkText;
  }
}
