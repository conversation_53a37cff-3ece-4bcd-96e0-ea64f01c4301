/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root {
  --markup-hidden-attr-name-color: light-dark(var(--grey-43), #787878);
  --markup-hidden-attr-value-color: light-dark(var(--grey-55), #a4a4a4);
  --markup-hidden-punctuation-color: light-dark(var(--grey-43), #787878);
  --markup-pseudoclass-disk-color: #e9c600;
  --markup-hidden-tag-color: light-dark(var(--grey-50), var(--grey-45));
  --markup-outline: light-dark(var(--theme-splitter-color), var(--theme-selection-background));
  --markup-drag-line: light-dark(var(--grey-40), var(--grey-55));
  --markup-drop-line: light-dark(var(--blue-55), var(--blue-50));
  --markup-overflow-causing-background-color: light-dark(rgba(128, 0, 215, 0.15), rgba(148, 0, 255, 0.38));
  --markup-new-attr-size: 2ch;
  --markup-new-attr-focused-margin-inline: 1ch 2px;
  --markup-selected-focus-visible-non-highlighted-color: light-dark(var(--theme-body-color), var(--theme-text-color-strong));

  &[forced-colors-active] {
    --markup-hidden-attr-name-color: GrayText;
    --markup-hidden-attr-value-color: GrayText;
    --markup-hidden-punctuation-color: GrayText;
    --markup-hidden-tag-color: GrayText;
    /* In regular mode, this variable relies on --theme-body-color, which is already set
       to CanvasText for HCM, BUT: we still need to force it here because of Bug 1917523 */
    --markup-selected-focus-visible-non-highlighted-color: CanvasText;
    --markup-drag-line: GrayText;
    --markup-drop-line: SelectedItem;
  }
}

* {
  padding: 0;
  margin: 0;
}

:root {
  -moz-control-character-visibility: visible;
}

body {
  user-select: none;
}

/* Force height and width (possibly overflowing) from inline elements.
 * This allows long overflows of text or input fields to still be styled with
 * the container, rather than the background disappearing when scrolling */
#root {
  float: left; /* The markup view should always be LTR */
  min-width: 100%;
  box-sizing: border-box;

  & > ul[role="tree"] {
    /* Reserve extra space (in addition to the indent size) for DOM mutation
    * breakpoint and pseudo icons. */
    padding-inline-start: 6px;
    outline-offset: -2px;
  }
}

/* Reset the global rotation of the icon done for RTL layout.
   Markup view is always LTR */
#root .theme-twisty:not(.open):dir(rtl),
#root .theme-twisty:not([open]):-moz-locale-dir(rtl) {
  transform: rotate(-90deg);
}

/* Don't display a parent-child outline for the root elements */
#root > ul > li > .children {
  background: none;
}

html.dragging {
  overflow-x: hidden;
}

body.dragging .tag-line {
  cursor: grabbing;
}

#root-wrapper:after {
   content: "";
   display: block;
   clear: both;
   position:relative;
}

.html-editor {
  display: none;
  position: absolute;
  z-index: 2;

  /* Use the same margin/padding trick used by .child tags to ensure that
   * the editor covers up any content to the left (including expander arrows
   * and hover effects). */
  margin-left: -1000em;
  padding-left: 1000em;
}

.html-editor-inner {
  border: solid .1px;
  flex: 1 1 auto;

  /* Keep the editor away from the markup view floating scrollbars */
  margin-inline-end: 12px;
}

.html-editor iframe {
  height: 100%;
  width: 100%;
  border: none;
  margin: 0;
  padding: 0;
}

.children {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Tags are organized in a UL/LI tree and indented thanks to a left padding.
 * A very large padding is used in combination with a slightly smaller margin
 * to make sure childs actually span from edge-to-edge. */
.child {
  margin-left: -10000px;
  padding-left: 10011px;
}

/* Normally this element takes space in the layout even if it's position: relative
 * by adding height: 0 we let surrounding elements to fill the blank space */
.child.dragging {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
  z-index: 1;
  height: 0;

  :root[forced-colors-active] & {
    opacity: 1;
    z-index: 2;
  }
}

/* Indicates a tag-line in the markup-view as being an active drop target by
 * drawing a horizontal line where the dragged element would be inserted if
 * dropped here */
.tag-line.drop-target::before,
.tag-line.drag-target::before {
  content: "";
  position: absolute;
  top: -1px;
  height: 2px;
  /* Offset these by 1000px to make sure they cover the full width of the view */
  width: calc(100% + 1000px);
  left: -1000px;
  background-color: var(--markup-drag-line);
}

.tag-line.drop-target::before {
  background-color: var(--markup-drop-line);
}

/* In case the indicator is put on the closing .tag-line, the indentation level
 * will become misleading, so we push it forward to match the indentation level */
ul.children + .tag-line::before {
  margin-left: 14px;
}

/* A tag line should have a height of 16px, with a line-height of 14px,
 * (assuming a font-size of 11px). */
.tag-line {
  font-size: var(--theme-code-font-size);
  min-height: 1.2727em;
  line-height: 1.2727em;
  padding-block: 1px;
  position: relative;
  cursor: default;
}

/* Change selected line style when editing an attribute */
.tag-line[selected]:has(.styleinspector-propertyeditor:focus) .theme-selected {
  background-color: var(--theme-body-alternate-emphasized-background);
  color: unset;

  /* revert the icon to the color it has when the line doesn't have the "selection" background */
  & ~ .theme-twisty {
    fill: var(--theme-icon-dimmed-color);
  }
}

.tag-line[selected] + .children,
.tag-line[selected] ~ .tag-line {
  background-image: linear-gradient(to top, var(--markup-outline), var(--markup-outline));
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 1.5px 100%;
  padding-left: 8px;
  margin-left: -8px;
}

.tag-line[selected] + .children {
  background-position-y: 2px;
}

.tag-line[selected] ~ .tag-line {
  background-position-y: -2px;
  /* Unset transition-property to prevent the markup outline from horizontal shifting */
  transition-property: none;
}

/**
 * Positioning the mutation marker on the viewport's left edge:
 * - left: walk back the indentation level up
 * - margin-left: walk back the #root's 6px padding-left
 *   (plus hide the leftmost 2px of the icon outside the viewport)
 */
.markup-tag-mutation-marker {
  display: none;
  position: absolute;
  top: 2px;
  left: calc(var(--markup-level, 1) * -11px);
  margin-left: -8px;
  height: 12px;
  width: 12px;
  background-image: url(chrome://devtools/content/debugger/images/markup-breakpoint.svg);
  background-repeat: no-repeat;
  background-size: 12px 12px;
  background-position: right center;
  -moz-context-properties: fill, stroke;
  fill: var(--blue-50);
  fill-opacity: 1;
  opacity: 1;
  stroke: none;
  z-index: 2;
}

.markup-tag-mutation-marker.has-mutations {
  display: block;
}

.markup-tag-mutation-marker.has-mutations.mutation-breakpoint-disabled {
  opacity: 0.6;
}

.tag-line[selected] .markup-tag-mutation-marker.mutation-breakpoint-disabled {
  fill: #fff;
  stroke: none;
}

.tag-line[selected] .markup-tag-mutation-marker {
  stroke: #fff;
}

.html-editor-container {
  position: relative;
  min-height: 200px;
}

/* This extra element placed in each tag is positioned absolutely to cover the
 * whole tag line and is used for background styling (when a selection is made
 * or when the tag is flashing) */
.tag-line .tag-state {
  position: absolute;
  left: -1000em;
  right: 0;
  top: 0;
  height: 100%;
  z-index: 0;
}

.tag-line .tag-hover:not(.theme-selected) {
  background: var(--theme-selection-background-hover);
  z-index: -1;
}

.expander {
  display: inline-block;
  vertical-align: top;
  width: 14px;
  height: 16px;
  margin: -1px 0 -1px -14px;
  /* Make sure the expander still appears above the tag-state */
  position: relative;
  z-index: 1;
}

.child.collapsed .child, .child.collapsed .children {
  display: none;
}

.child > .tag-line:first-child .close {
  display: none;
}

.child.collapsed > .tag-line:first-child .close {
  display: inline;
}

.child.collapsed > .tag-line ~ .tag-line {
  display: none;
}

.child.collapsed .close {
  display: inline;
}

.expandable.collapsed .markup-expand-badge {
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  height: 16px;
  margin: -1px 0;
  padding: 3px 2px;
}

.expandable.collapsed .markup-expand-badge::before {
  /* Display an ellipsis character in collapsed nodes that can be expanded. */
  content: "";
  background-color: var(--badge-interactive-background-color);
  background-image: url(chrome://devtools/skin/images/more.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 8px;
  border: 1px solid var(--badge-interactive-border-color);
  color: var(--badge-interactive-color);
  fill: currentColor;
  display: block;
  width: 12px;
  height: 8px;
  line-height: 8px;
  border-radius: 3px;
  -moz-context-properties: fill;
}

.expandable.collapsed .markup-expand-badge:focus::before,
.expandable.collapsed .markup-expand-badge:hover::before {
  background-color: var(--badge-hover-interactive-background-color);
  color: var(--badge-hover-interactive-color);
}

/* Hide HTML void elements (img, hr, br, …) closing tag when the element is not
 * expanded (it can be if it has pseudo-elements attached) */
.child.collapsed > .tag-line .void-element .close {
  display: none;
}

.tag-line .tag-state.overflow-causing-highlighted:not(.theme-selected) {
  background-color: var(--markup-overflow-causing-background-color);
}

/* Also display a small dot next to the overflow badge with the color of the
   scroll badge. This makes us not rely solely on color to convey a state change,
   as well as handle High Contrast Mode where the distinct background isn't visible */
.tag-state.overflow-causing-highlighted ~ .editor .inspector-badge.overflow-badge::before {
  content: "";
  display: inline-block;
  width: 1ch;
  aspect-ratio: 1 / 1;
  margin-inline-end: 4px;
  border-radius: 50%;
  background-color: var(--badge-scrollable-active-background-color);
  /* Use an outline to avoid impacting the layout more */
  outline: 1px solid var(--badge-scrollable-active-color);
  /* Opt out of forced colors so the dot is visible in High Contrast Mode */
  forced-color-adjust: none;
}


.closing-bracket {
  pointer-events: none;
}

.newattr {
  /* Place the new attr "button" over the ">" char */
  width: 1ch;
  margin-inline-end: -1ch;

  /* Make the button 1 char wide (it's easier to do it with a pseudo element as the element
     is inline and we don't want to mess the vertical alignment) */
  &::before {
    content: "\00a0" / "";
  }

  &:focus-visible {
    margin-inline: var(--markup-new-attr-focused-margin-inline);
    background-image: url("chrome://devtools/skin/images/add.svg");
    background-repeat: no-repeat;
    background-size: 90%;
    background-position: center;
    -moz-context-properties: fill;
    fill: var(--theme-icon-color);

    /* When focused, make the button 2 chars wide */
    &::before {
      content: "\00a0\00a0" / "";
    }
  }
}

.attr-value .link {
  text-decoration: underline;
}

.attr-value button.select-node {
  display: inline-block;
  vertical-align: middle;
  height: 15px;
  width: 15px;
  /* small spacing between attribute name and the icon */
  margin-inline-start: 2px;
  /* prevent the icon from impacting node height */
  margin-block-start: -2px;
  padding: 0;
  border: none;
  background: url("chrome://devtools/content/shared/components/reps/images/open-inspector.svg") no-repeat;
  fill: var(--theme-icon-color);
  -moz-context-properties: fill;
  /* consistenty with the other buttons in the markup view */
  cursor: pointer;

  &:is(:hover, :focus) {
    fill: var(--theme-icon-checked-color);
  }

  [role="treeitem"][aria-selected="true"] & {
    fill: var(--theme-selection-color);
  }
}

.flash-out {
  transition: background,color .5s;
}

.editor {
  /* Make sure the editor still appears above the tag-state */
  position: relative;
  z-index: 1;
}

.editor.comment:not(.doctype) {
  color: var(--theme-highlight-green);
  /* opt-out of forced color so comments gets a distinct color. This is safe as
     --theme-highlight-green has a High Contrast specific declaration. */
  forced-color-adjust: none;
}

.editor.text {
  display: inline-block;
}

.editor.text pre,
.editor.comment pre {
  font: inherit;

  /* In the Markup view, the background color is set on .theme-selected, which is not
     an ancestor of those <pre>, so we get backplating which doesn't play well with the
     selected color. So for those, force the SelectedItem background color */
  :root[forced-colors-active] .tag-line[selected] & {
    background-color: SelectedItem;
  }
}

/* Only override the color on non High Contrast dark theme so we can keep the inherited
   colors in HCM */
:root.theme-dark:not([forced-colors-active]) .editor.text pre {
  color: var(--theme-text-color-strong);
}

.more-nodes {
  padding-left: 16px;
}

.styleinspector-propertyeditor {
  vertical-align: top;
  border: none;

  &.newattr-input {
    margin-inline: var(--markup-new-attr-focused-margin-inline);
    /* Apply the same size as the new attr button so we don't shift the layout when dislaying the input */
    min-width: var(--markup-new-attr-size);
  }
}

.reveal-link {
  cursor: pointer;
  background: url("chrome://devtools/skin/images/reveal.svg") no-repeat;
  display: inline-block;
  width: 16px;
  height: 8px;

  -moz-context-properties: stroke;
  stroke: currentColor;
}

/* Draw a circle next to nodes that have a pseudo class lock.
   Center vertically with the 1.4em line height on .tag-line */
.child.pseudoclass-locked::before {
  content: "";
  background: var(--markup-pseudoclass-disk-color);
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin-top: 5px;
  left: 8px;
  position: absolute;
  z-index: 1;
}

.not-displayed {
  color: var(--markup-hidden-punctuation-color);
}

.not-displayed .attr-name {
  color: var(--markup-hidden-attr-name-color);
}

.not-displayed .attr-value {
  color: var(--markup-hidden-attr-value-color);
}

.not-displayed .tag {
  color: var(--markup-hidden-tag-color);
}

@layer markup-base {
  .tag {
    color: var(--theme-highlight-blue);
  }
  .attr-name {
    color: var(--theme-highlight-red);
  }
  .attr-value {
    color: var(--theme-highlight-purple);
  }
  .pseudo {
    color: var(--theme-highlight-blue);
  }

  /* In High Contrast Mode, we need to override all the colors so we can properly handle
     the selected + focused-visible style */
  :root[forced-colors-active] :is(
    .tag,
    .pseudo
  ) {
    color: SelectedItem;
  }

  :root[forced-colors-active] .attr-name {
    color: CanvasText;
  }

  :root[forced-colors-active] .attr-value {
    /* opt-out of forced color so attribute values gets a distinct color. This is safe as
      --theme-highlight-blue has a High Contrast specific declaration. */
    forced-color-adjust: none;
    color: var(--theme-highlight-blue);
  }
}

/* Make focusable elements stand out a bit more when they're focused */
.tag-line :is(
  [editable],
  /* tag is not always editable (e.g. for `html`, `DOCTYPE`, …), but is always focusable */
  .tag
):focus-visible {
  border-radius: 2px;
  outline: var(--theme-focus-outline);
  outline-offset: var(--theme-outline-offset);
}

/* Selected nodes in the tree should have light selected text (unless there's an active
   inplace editor input).
   theme-selected doesn't work in this case since the text is a
   sibling of the class, not a child. */
.tag-line[selected]:not(:has(.styleinspector-propertyeditor:focus)) :is(
  .editor,
  .editor.comment,
  .editor :is(.attr-name, .attr-value, .tag, .pseudo)
) {
  color: var(--theme-selection-color);

  & :is([editable], .tag):focus-visible {
    /* When an editable item in the markup view is focused, we want to make it stand out
       Since the node is selected, we have a blue background on the whole node, so we set
       the default body background, and set the original color we have when the line is
       not selected.
       This is done by using revert-layer, so we'll get colors set in <@layer markup-base>,
       and not the one set in this rule's parent rule.
    */
    color: revert-layer;
    background-color: var(--theme-body-background);

    outline-offset: 2px;

    /* Better style for attributes spanning multiple lines */
    box-decoration-break: clone;
    mix-blend-mode: lighten;
    .theme-dark & {
      mix-blend-mode: darken;
    }

    /* We also need to take care of items that don't have their color set in <@layer markup-base> */
    &:not(.attr-name, .attr-value, .tag, .pseudo) {
      color: var(--markup-selected-focus-visible-non-highlighted-color);
    }

    & * {
      color: revert-layer;
    }
  }
}

/* Selected nodes being flashed in the tree should have dark selected text. Here we target
   nodes with theme-selected text colors and apply a dark, accessible text color for when
   the yellow flashing background is applied. */
.tag-line[selected] :is(
  .theme-selected.theme-bg-contrast ~ .editor,
  .theme-selected ~ .editor .theme-fg-contrast
) {
  color: var(--theme-contrast-color) !important;
}


/* Applicable to the DOCTYPE */
.doctype {
  font-style: italic;
}
