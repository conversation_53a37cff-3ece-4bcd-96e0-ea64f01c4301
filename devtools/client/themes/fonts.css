/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* CSS Variables specific to the font editor that aren't defined by the themes */
:root {
  --highlight-color: var(--blue-55);
  --input-background-color: white;
  --input-border-color: var(--grey-30);
  --preview-input-background: var(--theme-toolbar-background);
  --slider-thumb-color: var(--grey-50);
  --slider-track-color: var(--grey-30);
}

:root.theme-dark {
  --input-background-color: var(--grey-70);
  --input-border-color: var(--grey-70);
  --preview-input-background: #222225;
  --slider-thumb-color: var(--grey-40);
  --slider-track-color: var(--grey-60);
}

#sidebar-panel-fontinspector {
  margin: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
}

#font-container, #font-editor {
  min-width: var(--min-container-width);
}

#font-container {
  flex: auto;
}

#font-editor {
  padding-bottom: .5em;
}

#font-editor summary {
  user-select: none;
  cursor: pointer;
  margin-bottom: .4em;
  width: -moz-fit-content;
}

#font-editor details {
  padding-bottom: .5em;
}

#font-editor details .label-open,
#font-editor details .label-close {
  display: none;
}

#font-editor details[open] .label-close,
#font-editor details:not([open]) .label-open {
  display: inline-block;
}

#font-editor .devtools-sidepanel-no-result {
  padding-bottom: 0;
}

.fonts-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.font {
  border: 1px solid var(--theme-splitter-color);
  border-width: 0 0 1px 0;
  display: grid;
  grid-template-columns: 1fr auto;
  grid-column-gap: 10px;
  padding: 10px 20px;
  overflow: auto;
}

#font-container .theme-twisty {
  display: inline-block;
  cursor: pointer;
  vertical-align: bottom;
}

#font-preview-input-container {
  background: var(--preview-input-background);
  border-bottom: 1px solid var(--theme-splitter-color);
  display: flex;
  height: 25px;
}

#font-preview-input-container input {
  background-image: none;
  flex: 1;
  padding-inline: 19px;
}

.font-preview {
  grid-column: 2;
  grid-row: 1 / span 2;
  object-fit: contain;
  height: 50px;
  width: 100%;
}

.font-name,
.font-family-name {
  font-weight: normal;
  white-space: nowrap;
}

.font-name {
  display: inline-block;
  margin-bottom: 0.6em;
  font-size: 1em;
  color: var(--theme-text-color-alt);
}

.font-family-name {
  margin-bottom: 0.2em;
  font-size: 1.2em;
}

.font-group {
  margin-bottom: .5em;
}

.font-group .font-name {
  white-space: unset;
}

.font-group .font-name::after {
  content: ",";
}

.font-group .font-name:nth-last-child(1)::after {
  content: "";
}

/* Make font-name hoverable as doing so highlights text runs in the page.
   The hit target is 3px around the font name, which is why we offset them with a -3px
   horizontal margin so they appear at the same place as they normall would. */

.font-name {
  padding: 3px;
  margin-inline-start: -3px;
  border-radius: 3px;
}

.font-name:hover {
  background-color: var(--theme-selection-background-hover);
}

.font-css-code {
  /* Force text direction in LTR and RTL */
  direction: ltr;
  text-align: left;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-text-color-strong);
  grid-column: span 2;
}

.font-css-code .theme-twisty {
  margin-inline-start: -3px;
}

/* Force arrow direction in LTR and RTL */
.font-css-code .theme-twisty:not([open]) {
  transform: rotate(-90deg) !important;
}

.font-css-code-expander::before {
  content: "\2026";
  display: inline-block;
  width: 12px;
  height: 8px;
  margin: 0 2px;
  line-height: 3px;
  color: var(--theme-icon-dimmed-color);
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  text-align: center;
  vertical-align: middle;
}

.font-control {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  margin: .6em 0;
}

/* Style *all* axis controls with a top separator. See reset below. */
.font-control-axis {
  border-top: 1px solid var(--theme-splitter-color);
  padding-top: 1.1em;
}

/* Remove styles form all axis controls aside from the first one.
   Workaround for :first-of-type which doesn't work with class names. */
.font-control-axis ~ .font-control-axis {
  border-top: unset;
  padding-top: unset;
}

.font-control-used-fonts {
  align-items: flex-start;
  border-bottom: 1px solid var(--theme-splitter-color);
  margin-block: 0 1em;
  padding-top: 1em;
}

.font-control-box,
.font-control-input {
  flex: 4;
  min-width: 100px;
}

.font-control-input {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

.font-control-input .devtools-checkbox-toggle {
  margin: 2px 0;
}

.font-control-label {
  display: inline-block;
  flex: 1;
  font-size: 12px;
  min-width: 70px;
  margin-inline-end: 10px;
  user-select: none;
}

.font-control-label-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.font-control-label-detail {
  color: var(--theme-text-color-alt);
  font-size: smaller;
}

.font-value-input {
  text-align: right;
  width: 60px;
  padding: 2px 3px;
  padding-inline-end: 5px;
}

.font-value-input,
.font-value-select {
  color: var(--theme-text-color-strong);
  border: 1px solid var(--input-border-color);
  background-color: var(--input-background-color);
}

/* Styles for disabled input fields */
.font-value-input[disabled],
.font-value-select[disabled],
.font-value-slider[disabled] {
  opacity: 0.5;
}

/* Do not use browser "invalid" state */
.font-value-slider:-moz-ui-invalid,
.font-value-input:-moz-ui-invalid {
  box-shadow: none;
}

/* Do not show dotted line focus outline */
.font-value-input:-moz-focusring {
  outline: none;
}

/* Make native number steppers disappear by treating it as text field*/
.font-value-input[type=number] {
  appearance: textfield;
}

/* Swap around order of value input and unit dropdown for RTL */
.font-value-input:dir(rtl) {
  order: 3;
}

.font-value-label {
  /* Combined width of .font-value-input and .font-value-select */
  width: calc(60px + 3.8em);
  padding-block: 2px 4px;
}

/* Mock separator because inputs don't have distinguishable borders in dark theme */
.theme-dark .font-value-input + .font-value-select:dir(ltr) {
  margin-inline-start: 2px;
}
.theme-dark .font-value-input + .font-value-select:dir(rtl) {
  margin-inline-end: 2px;
}

/* Custom styles for <select> elements within the font editor. */
.font-value-select {
  background-image: url(chrome://devtools/skin/images/select-arrow.svg);
  background-repeat: no-repeat;
  background-position: right 4px center;
  fill: var(--theme-icon-color);
  -moz-context-properties: fill;
  appearance: none;
  box-shadow: none;
  padding: 1px 10px 1px 2px;
  min-width: 3.8em;
}

.font-value-input + .font-value-select:dir(ltr) {
  border-inline-start: none;
}

/* Swap around order of value input and unit dropdown for RTL */
.font-value-input + .font-value-select:dir(rtl) {
  border-inline-end: none;
  order: 2;
  text-align: left;
}

.font-value-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--theme-text-color-strong);
}

.font-value-input:focus,
.font-value-select:focus {
  outline: 1px solid var(--highlight-color);
  outline-offset: -1px;
}

.font-value-slider-container {
  flex: 1;
  min-width: 50px;
  position: relative;
  margin-inline-end: 10px;
}

/* Firefox doesn't support pseudo-elements on inputs. Using the container instead. */
.font-value-slider-container::before,
.font-value-slider-container::after {
  user-select: none;
  color: var(--theme-text-color-alt);
  font-size: smaller;
  position: absolute;
  bottom: -.6em;
  visibility: hidden;
}

.font-control-input:hover .font-value-slider-container::before,
.font-control-input:hover .font-value-slider-container::after,
.font-control-input:focus-within .font-value-slider-container::before,
.font-control-input:focus-within .font-value-slider-container::after {
  visibility: visible;
}

.font-value-slider-container::before {
  content: attr(data-min);
  inset-inline-start: .3em;
}

.font-value-slider-container::after {
  content: attr(data-max);
  inset-inline-end: .3em;
}

.font-value-slider {
  background: transparent;
  width: 100%;
  margin: 0;
}

/*
  The value of font-weight goes from 100 to 900 in increments of 100.
  Decorate the slider for font-weight to have 9 vertical notches using a linear gradient.
*/
.font-value-slider[name=font-weight] {
  --notch-size: 3px;
  /* Draw a vertical line to get one notch per background-image instance */
  background-image: linear-gradient(90deg, var(--slider-track-color) var(--notch-size),
   transparent 0);
  /* Offset the background so the notch aligns with the center of the slider thumb */
  background-position: 5px center;
  /* Repeat the background-image horizontally */
  background-repeat: repeat-x;
  /* Size the background to get nine visible notch instances. */
  background-size: calc(12.5% - var(--notch-size) / 2) 7px;
}

.font-value-slider:-moz-focusring {
  outline: none;
}

.font-value-slider::-moz-range-thumb {
  background-color: var(--slider-thumb-color);
  border: 0;
}

.font-value-slider:focus::-moz-range-thumb {
  background-color: var(--highlight-color);
}

.font-value-slider::-moz-range-track {
  background-color: var(--slider-track-color);
  height: 3px;
}

.font-origin {
  margin-top: -.25em;
  color: var(--theme-comment);
  justify-self: start;
}

.font-origin.system {
  text-transform: capitalize;
}

.font-origin.remote {
  display: grid;
  grid-template-columns: 1fr 20px;
}

.font-origin.remote .url {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  direction: ltr;
}

.font-origin .copy-icon {
  border: 0;
  padding: 0;
  position: relative;
  cursor: pointer;
  width: 12px;
  height: 12px;
  place-self: center;

  background: url(chrome://devtools/skin/images/copy.svg) no-repeat;
  background-size: 12px;
  background-position-x: -1px;
  -moz-context-properties: fill;
  fill: var(--grey-50);
}

#font-container .accordion {
  border-top: 1px solid var(--theme-splitter-color);
}

#font-container .accordion ._content {
  padding: 0;
}

#font-container .accordion + .accordion {
  border-top: none;
}
