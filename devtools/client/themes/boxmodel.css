/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/ */

:root {
  --position-text-color: light-dark(var(--grey-90), var(--grey-30));
  --position-border-color: light-dark(var(--grey-50), var(--grey-40));

  --margin-box-color: light-dark(#FDFFDF, #73764A);
  --margin-box-text-color: light-dark(var(--grey-90), var(--grey-10));
  --margin-box-border-color: light-dark(#D8E60A, #BDCA00);

  --border-box-color: light-dark(var(--grey-50), var(--grey-70));
  --border-box-text-color: light-dark(var(--theme-selection-color), var(--grey-10));

  --padding-box-color: light-dark(#E3DCFF, #6657A6);
  --padding-box-text-color: light-dark(var(--grey-90), var(--grey-10));

  --content-box-color: light-dark(#CFF0FB, #407AA4);
  --content-box-border-color: light-dark(#54A9FF, #00B8FF);

  --box-border-width: 1px;

  &[forced-colors-active] {
    --margin-box-color: Canvas;

    --border-box-color: CanvasText;
    --border-box-text-color: Canvas;

    --padding-box-color: Canvas;

    --box-border-width: 2px;
  }
}

.boxmodel-container {
  overflow: auto;
  padding-bottom: 4px;
  max-width: 600px;
  margin: 0 auto;
}

/* Header */

.boxmodel-header,
.boxmodel-info {
  display: flex;
  align-items: center;
  padding: 4px 19px;
}

.layout-geometry-editor::before {
  background: url(images/geometry-editor.svg) no-repeat center center / 16px 16px;
}

/* Main: contains the box-model regions */

.boxmodel-main {
  position: relative;
  /* Make sure there is some space between the window's edges and the regions */
  margin: 14px auto;
  width: calc(100% - 2 * 14px);
  min-width: 240px;
  /* The view will grow bigger as the window gets resized, until 400px */
  max-width: 400px;
}

.boxmodel-box {
  margin: 25px;
}

.boxmodel-size {
  color: var(--grey-90);
}

.boxmodel-position,
.boxmodel-margin,
.boxmodel-padding {
  color: var(--grey-50);
}

.theme-dark .boxmodel-margin,
.theme-dark .boxmodel-border,
.theme-dark .boxmodel-padding,
.theme-dark .boxmodel-size {
  color: var(--grey-10);
}

.theme-dark .boxmodel-position {
  color: var(--grey-30);
}

/* Regions are 3 nested elements with wide borders and outlines */

.boxmodel-contents {
  height: 18px;
  outline: dashed var(--box-border-width) var(--content-box-border-color);
}

.boxmodel-margins {
  border-width: 22px;
  border-style: solid;
  outline: dashed var(--box-border-width) var(--margin-box-border-color);
}

.boxmodel-borders {
  border-width: 5px;
  border-style: solid;
}

.boxmodel-paddings {
  border-width: 27px;
  border-style: solid;
}

/* Regions colors */

.boxmodel-margins {
  border-color: var(--margin-box-color);
}

.boxmodel-borders {
  border-color: var(--border-box-color);
}

.boxmodel-paddings {
  border-color: var(--padding-box-color);
}

.boxmodel-contents {
  background-color: var(--content-box-color);
}

/* Editable region sizes are contained in absolutely positioned <p> */

.boxmodel-main > p,
.boxmodel-size {
  position: absolute;
  pointer-events: none;
  margin: 0;
  text-align: center;
  direction: ltr;
}

.boxmodel-main > p > span,
.boxmodel-main > p > input,
.boxmodel-content,
.boxmodel-size > span {
  vertical-align: middle;
  pointer-events: auto;
}

/* Coordinates for the region sizes */

.boxmodel-top,
.boxmodel-bottom {
  width: calc(100% - 2px);
  text-align: center;
  z-index: 1; /* workaround for bug 1559706 */
}

.boxmodel-padding.boxmodel-top {
  top: 34px;
}

.boxmodel-padding.boxmodel-bottom {
  bottom: 35px;
}

.boxmodel-border.boxmodel-top {
  top: 17px;
}

.boxmodel-border.boxmodel-bottom {
  bottom: 17px;
}

.boxmodel-margin.boxmodel-top {
  top: 0;
}

.boxmodel-margin.boxmodel-bottom {
  bottom: 1px;
}

.boxmodel-size,
.boxmodel-position.boxmodel-left,
.boxmodel-position.boxmodel-right,
.boxmodel-margin.boxmodel-left,
.boxmodel-margin.boxmodel-right,
.boxmodel-border.boxmodel-left,
.boxmodel-border.boxmodel-right,
.boxmodel-padding.boxmodel-left,
.boxmodel-padding.boxmodel-right {
  top: 22px;
  line-height: 80px;
  z-index: 1; /* workaround for bug 1559706 */
}

.boxmodel-size {
  width: calc(100% - 2px);
}

.boxmodel-position.boxmodel-top,
.boxmodel-position.boxmodel-bottom,
.boxmodel-position.boxmodel-left,
.boxmodel-position.boxmodel-right,
.boxmodel-margin.boxmodel-right,
.boxmodel-margin.boxmodel-left,
.boxmodel-border.boxmodel-right,
.boxmodel-border.boxmodel-left,
.boxmodel-padding.boxmodel-right,
.boxmodel-padding.boxmodel-left {
  width: 21px;
}

.boxmodel-padding.boxmodel-left {
  left: 56px;
}

.boxmodel-padding.boxmodel-right {
  right: 56px;
}

.boxmodel-border.boxmodel-left {
  left: 39px;
}

.boxmodel-border.boxmodel-right {
  right: 39px;
}

.boxmodel-margin.boxmodel-right {
  right: 24px;
}

.boxmodel-margin.boxmodel-left {
  left: 24px;
}

.boxmodel-rotate.boxmodel-left:not(.boxmodel-editing) {
  transform: rotate(-90deg);
}

.boxmodel-rotate.boxmodel-right:not(.boxmodel-editing) {
  transform: rotate(90deg);
}

.boxmodel-rotate.boxmodel-left.boxmodel-position:not(.boxmodel-editing) {
  border-top: none;
  border-right: 1px solid var(--theme-highlight-purple);
  width: auto;
  height: 30px;
}

.boxmodel-size > p {
  display: inline-block;
  margin: auto;
  line-height: 0;
}

.boxmodel-rotate.boxmodel-right.boxmodel-position:not(.boxmodel-editing) {
  border-top: none;
  border-left: 1px solid var(--theme-highlight-purple);
  width: auto;
  height: 30px;
}

/* Box Model Positioning: contains top, right, bottom, left */

.boxmodel-position.boxmodel-top,
.boxmodel-position.boxmodel-bottom {
  border-left: 1px solid var(--position-border-color);
  left: calc(50% - 2px);
  padding-left: 1px;
}

.boxmodel-position.boxmodel-right,
.boxmodel-position.boxmodel-left {
  border-top: 1px solid var(--position-border-color);
  line-height: 15px;
  top: calc(50% - 1px);
  width: 30px;
}

.boxmodel-position.boxmodel-top {
  top: -18px;
}

.boxmodel-position.boxmodel-right {
  right: -9px;
}

.boxmodel-position.boxmodel-bottom {
  bottom: -18px;
}

.boxmodel-position.boxmodel-left {
  left: -9px;
}

/* Legend: displayed inside regions */

.boxmodel-legend {
  position: absolute;
  z-index: 1;
  cursor: default;
}

.boxmodel-legend[data-box="margin"] {
  margin-left: 9px;
  margin-top: 4px;
  color: var(--margin-box-text-color);
}

.boxmodel-legend[data-box="position"] {
  color: var(--position-text-color);
  margin: -18px 13px;
}

.boxmodel-legend[data-box="padding"] {
  margin-top: 12px;
  margin-left: 12px;
  color: var(--padding-box-text-color);
}

.boxmodel-legend[data-box="border"] {
  background-color: var(--border-box-color);
  color: var(--border-box-text-color);
  height: 15px;
  padding-top: 1px;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 3px;
  margin: 0 0;
}

/* Editable fields */

.boxmodel-editable {
  position: relative;
  border: 1px dashed transparent;
  user-select: none;
  white-space: nowrap;
  cursor: pointer;
}

.boxmodel-editable[data-box="border"] {
  background-color: var(--border-box-color);
  color: var(--border-box-text-color);
  border-radius: 3px;
  padding: 0 2px;
}

.boxmodel-editable:hover {
  border-bottom-color: hsl(0, 0%, 50%);
}

.boxmodel-editable:active {
  border: 1px solid var(--blue-50);
  outline: none;
}

.boxmodel-editable[data-box="margin"]:hover {
  background-color: var(--margin-box-border-color);
}

.boxmodel-editable[data-box="padding"]:hover {
  background-color: #c78fc7b3;
}

.boxmodel-editable[data-box="border"]:hover {
  background-color: #545454;
}

.boxmodel-editable[data-box="content"]:hover {
  background-color: var(--content-box-border-color);
}

.boxmodel-editable[data-box]:hover {
  border-radius: 3px;
}

:root[forced-colors-active] .boxmodel-editable[data-box] {
  background-color: ButtonFace;
  color: ButtonText;
  border-style: solid;
  border-color: ButtonText;

  &:hover:not(:active) {
    background-color: SelectedItem;
    color: SelectedItemText;
  }
}

.boxmodel-size > span {
  cursor: default;
}

/* Box Model Info: contains the position and size of the element */

.boxmodel-element-size {
  flex: 1;
  direction: ltr;
  text-align: match-parent;
}

.boxmodel-position-group {
  display: flex;
  align-items: center;
}

/* Tag displayed next to DOM Node previews (used to display reference elements) */

.boxmodel-container .reference-element {
  margin-inline-start: 14px;
  margin-block-start: 4px;
  display: block;
}

.boxmodel-container .reference-element-type {
  background: var(--theme-highlight-purple);
  color: light-dark(white, black);
  padding: 1px 2px;
  border-radius: 2px;
  font-size: 9px;
  margin-inline-end: 5px;
}

/* Box Model Main - Offset Parent */

.boxmodel-offset-parent {
  position: absolute;
  top: -20px;
  right: -10px;
  color: var(--theme-highlight-purple);
}
