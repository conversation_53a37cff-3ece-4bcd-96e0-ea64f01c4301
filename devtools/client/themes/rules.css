/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* CSS Variables specific to this panel that aren't defined by the themes */
:root {
  --rule-highlight-propertyname: light-dark(var(--blue-60), var(--theme-highlight-blue));
  --rule-highlight-propertyvalue: light-dark(var(--magenta-70), var(--theme-highlight-red));
  --rule-highlight-unmatched-unused-color: light-dark(var(--grey-55), var(--grey-40));
  --rule-header-background-color: light-dark(var(--theme-toolbar-background), #222225);
  --rule-computed-margin-inline-start: 35px;
  --rule-enableproperty-size: 12px;
  --rule-enableproperty-margin-inline-start: 3px;
  --rule-enableproperty-margin-inline-end: 9px;

  &:is([platform="win"], [platform="linux"]) {
    --rule-enableproperty-size: 14px;
    /* it's important to keep the px unit here as this is used in a `calc()` */
    --rule-enableproperty-margin-inline-start: 0px;
    --rule-enableproperty-margin-inline-end: 10px;
  }

  &[forced-colors-active] {
    --rule-highlight-propertyname: var(--theme-contrast-color);
    --rule-highlight-propertyvalue: var(--theme-contrast-color);
    --rule-highlight-unmatched-unused-color: var(--theme-contrast-color);
  }
}

/* Rule View Tabpanel */

#sidebar-panel-ruleview {
  margin: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  /* Override the min-width from .inspector-tabpanel, as the rule panel can support small
     widths */
  min-width: 100px;
}

/* Rule View Toolbar */

#ruleview-toolbar-container {
  line-height: var(--theme-toolbar-height);
}

#ruleview-toolbar {
  flex-wrap: wrap;
  height: auto;
}

#ruleview-toolbar .devtools-searchbox {
  flex-basis: 7em;
  height: var(--theme-toolbar-height);
}

#ruleview-command-toolbar {
  display: flex;
}

.ruleview-reveal-panel {
  border-bottom: 1px solid var(--theme-splitter-color);
  display: flex;
  overflow: hidden;
  flex-wrap: wrap;
  margin: 2px;
}

.ruleview-reveal-panel[hidden] {
  display: none;
}

.ruleview-reveal-panel .add-class {
  height: var(--theme-toolbar-height);
  line-height: normal;
}

.ruleview-reveal-panel label {
  user-select: none;
  display: flex;
  align-items: center;
}

#pseudo-class-panel,
#ruleview-class-panel .classes {
  padding: 0 3px;
}

/* Pseudo Class toggle panel */

#pseudo-class-panel {
  grid-template-columns: repeat(auto-fit, minmax(100px, max-content));
}

#pseudo-class-panel:not([hidden]) {
  display: grid;
}

/* Class toggle panel */

#ruleview-class-panel {
  flex-direction: column;
}

#ruleview-class-panel .classes {
  border-top: 1px solid var(--theme-splitter-color);
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  max-height: 100px;
  overflow-y: auto;
}

#ruleview-class-panel .classes label {
  flex: 0 0;
  max-width: 50%;
  min-width: 100px;
}

#ruleview-class-panel .classes label span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

#ruleview-class-panel .no-classes {
  flex: 1;
  color: var(--theme-text-color-inactive);
  margin: 0;
  text-align: center;
}

/* Rule View Container */

#ruleview-container {
  user-select: text;
  overflow: auto;
  flex: auto;
  height: 100%;
  /* Adjust outline so it's visible */
  outline-offset: -2px;
}

/* This extra wrapper only serves as a way to get the content of the view focusable.
   So that when the user reaches it either via keyboard or mouse, we know that the view
   is focused and therefore can handle shortcuts.
   However, for accessibility reasons, tabindex is set to -1 to avoid having to tab
   through it, and the outline is hidden. */
#ruleview-container-focusable {
  height: 100%;
  outline: none;
}

#ruleview-container.non-interactive {
  pointer-events: none;
  visibility: collapse;
  transition: visibility 0.25s;
}

#ruleview-container .accordion ._content {
  padding: 0;
}

.ruleview-property:not(:hover) > .ruleview-enableproperty {
  pointer-events: none;
}

.ruleview-expandable-container {
  display: block;
}

.ruleview-namecontainer {
  cursor: text;
}

.ruleview-computed {
  margin-inline-start: var(--rule-computed-margin-inline-start);
  /* Add a small indent for wrapping lines */
  text-indent: 1em hanging;
}

.ruleview-computed > .ruleview-namecontainer {
  margin: 0;
}

.ruleview-propertyvaluecontainer {
  cursor: text;
  padding-right: 5px;
}

.ruleview-propertyvaluecontainer a {
  color: var(--theme-highlight-purple);
  cursor: pointer;
}

.ruleview-computedlist,
[hidden]:is(
  .ruleview-expandable-container,
  .ruleview-overridden-items,
  .ruleview-overridden-rule-filter,
  .ruleview-warning,
  .ruleview-unused-warning,
  .ruleview-compatibility-warning,
  .ruleview-used,
  .ruleview-invalid-at-computed-value-time-warning,
),
.ruleview-overridden .inspector-grid {
  display: none;
}

.ruleview-computedlist[user-open],
.ruleview-computedlist[filter-open],
.ruleview-overridden-items {
  display: block;
}

.ruleview-rule-source {
  text-align: end;
  float: right;
  max-width: 100%;

  /* Force RTL direction to crop the source link at the beginning. */
  direction: rtl;
  overflow: hidden;
  text-overflow: ellipsis;

  user-select: none;
  margin-bottom: 2px;
  margin-left: 1ch;

  /* Because of the overflow hidden, the outline that would be set on the child link
     would not be visible, so we need to set it here */
  &:focus-within:has(a:focus-visible) {
    outline: var(--theme-focus-outline);
  }
}

.ruleview-rule-source-label {
  white-space: nowrap;
  margin: 0;
  cursor: pointer;

  /* Create an LTR embed to avoid special characters being shifted to the start due to the
     parent node direction: rtl; */
  direction: ltr;
  unicode-bidi: embed;

  /* We don't always have a link (e.g. for inline style, it's a simple span), and in such
     case, we shouldn't set the color the same as link color */
  &:is(a) {
    color: var(--theme-internal-link-color);

    /* The outline is set on ruleview-rule-source */
    &:focus-visible {
      outline: none;
    }

    &:visited {
      color: var(--theme-internal-link-color);
    }
  }
}

.ruleview-rule-source.disabled > .ruleview-rule-source-label,
.ruleview-rule-source[unselectable] > .ruleview-rule-source-label {
  cursor: default;
}

.ruleview-rule-source:not([unselectable]) {
  text-decoration: underline;
  text-decoration-skip-ink: none;
}

.ruleview-header {
  --ruleview-header-padding: 4px;
  background: var(--rule-header-background-color);
  border-bottom: 1px solid var(--theme-splitter-color);
  color: var(--theme-toolbar-color);
  font-size: 12px;
  line-height: 16px;
  padding: var(--ruleview-header-padding);
  width: 100%;
  align-items: center;
  display: flex;
  user-select: none;
}

.ruleview-expandable-header {
  cursor: pointer;
  /* Reset padding set on ruleview-header since we're going to put it on the button instead */
  padding: 0;

  & button[aria-expanded] {
    display: flex;
    align-items: center;
    /* Space between arrow and label */
    gap: 2px;
    width: 100%;
    padding: var(--ruleview-header-padding);

    background-color: var(--theme-accordion-header-background);
    color: var(--theme-accordion-header-color);

    /* Reset button-specific styles */
    border: none;
    text-align: start;
    /* Apply same font-size as set on ruleview-header */
    font-size: 1em;
    outline-offset: -2px;

    &:hover {
      background-color: var(--theme-accordion-header-hover-background);
      color: var(--theme-accordion-header-hover-color);

      :root[forced-colors-active] & .theme-twisty {
        color: currentColor;
      }
    }

    &[aria-expanded="true"] .theme-twisty {
      rotate: 90deg;
    }
  }
}

.ruleview-rule-pseudo-element {
  padding-left: 20px;
  border-left: solid 10px;
}

.ruleview-rule {
  border-bottom: 1px solid var(--theme-splitter-color);
  padding: 2px 4px;
  direction: ltr;
}

#ruleview-container-focusable > .ruleview-rule:last-child {
  border-bottom: none;
}

/**
 * Display rules that don't match the current selected element and uneditable
 * user agent styles differently
 */
.ruleview-rule.unmatched,
.ruleview-rule.uneditable,
.ruleview-rule[unmatched=true],
.ruleview-rule[uneditable=true] {
  background: var(--theme-tab-toolbar-background);
}

:root[forced-colors-active] .ruleview-rule:is(
  .unmatched,
  [unmatched=true]
) {
  /* Set an inactive color in High Contrast Mode as we don't have the
     distinct background that is set in regular mode */
  color: var(--theme-text-color-inactive);
}

.ruleview-rule.uneditable :focus,
.ruleview-rule[uneditable=true] :focus {
  outline: none;
}

.ruleview-rule.uneditable .theme-link,
.ruleview-rule[uneditable=true] .theme-link {
  color: var(--theme-highlight-bluegrey);
}

.ruleview-rule.uneditable .ruleview-enableproperty,
.ruleview-rule[uneditable=true] .ruleview-enableproperty {
  visibility: hidden;
}

.ruleview-rule.uneditable .inspector-swatch,
.ruleview-rule[uneditable=true] .inspector-swatch {
  cursor: default;
}

.ruleview-rule.uneditable .ruleview-namecontainer > .ruleview-propertyname,
.ruleview-rule.uneditable .ruleview-propertyvaluecontainer >
.ruleview-propertyvalue,
.ruleview-rule[uneditable=true] .ruleview-namecontainer > .ruleview-propertyname,
.ruleview-rule[uneditable=true] .ruleview-propertyvaluecontainer >
.ruleview-propertyvalue {
  border-bottom-color: transparent;
}

.ruleview-overridden-rule-filter {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-inline-start: 3px;
  background-image: url(chrome://devtools/skin/images/filter-small.svg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 12px;
  -moz-context-properties: fill;
  fill: var(--theme-icon-dimmed-color);
  background-color: transparent;
  border: none;

  &:is(:hover, :focus-visible) {
    fill: var(--theme-icon-color);
  }
}

.ruleview-ruleopen {
  padding-inline-end: 5px;
}

.ruleview-ruleclose {
  clear: both;
  cursor: text;
  padding-right: 20px;
}

.ruleview-ancestor-ruleopen,
.ruleview-ancestor-ruleclose {
  color: var(--theme-comment);
}

.ruleview-rule-indent, .ruleview-ancestor-ruleclose {
  white-space: pre;
  /*
  * We do want to keep the underlying chars for the indent for user to copy/paste,
  * but we can reduce the inline space it takes.
  * Tweaking word-spacing instead of using font-size as the latter could be odd when
  * the text is selected.
  * Avoid using letter-spacing as this can cause visual glitches on low-dpi screen (see Bug 1845517).
  */
  word-spacing: -0.5ch;
}

.ruleview-rule-indent.clipboard-only {
  position: absolute;
  left: -9999px;
  overflow: hidden;
  width: 0;
  height: 0;
  padding: 0;
  border: 0;
  margin: 0;
}

.ruleview-propertylist {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ruleview-expandable-container.registered-properties .ruleview-propertylist {
  /* Since we don't have a toggle checkbox for registered property definitions, we set
     a padding so they have the same indent as properties in regular rules */
  padding-inline-start: 28px;
}

.ruleview-enableproperty {
  position: relative;
  margin-top: 0;
  margin-bottom: 0;
}

.ruleview-enableproperty {
  box-sizing: border-box;
  margin-inline-start: var(--rule-enableproperty-margin-inline-start);
  margin-inline-end: var(--rule-enableproperty-margin-inline-end);
  width: var(--rule-enableproperty-size);
  height: var(--rule-enableproperty-size);

  /* The SVG checkbox on Windows and Linux */
  :root:is([platform="win"], [platform="linux"]) & {
    appearance: none;
    float: left;
    box-sizing: content-box;
    display: inline-block;
    border: 0;
    padding: 0;
    background: url("chrome://devtools/skin/images/checkbox.svg") center no-repeat content-box;
    /* Using fill to paint the border, and stroke for the tick */
    -moz-context-properties: fill, stroke;
    fill: rgba(0,0,0,.3);
    stroke: transparent;
  }

  :root[platform="mac"] & {
    appearance: auto;
    -moz-default-appearance: checkbox;
  }
}

:root[platform="win"].theme-dark .ruleview-enableproperty,
:root[platform="linux"].theme-dark .ruleview-enableproperty {
  fill: rgba(255,255,255,.4);
}

:root[platform="win"] .ruleview-enableproperty:checked,
:root[platform="linux"] .ruleview-enableproperty:checked {
  stroke: rgba(0,0,0,.6);
}

:root[platform="win"].theme-dark .ruleview-enableproperty:checked,
:root[platform="linux"].theme-dark .ruleview-enableproperty:checked {
  stroke: rgba(255,255,255,.8);
}

.ruleview-warning,
.ruleview-unused-warning,
.ruleview-compatibility-warning,
.ruleview-invalid-at-computed-value-time-warning {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-size: 12px;
  background-repeat: no-repeat;
  -moz-context-properties: fill;
}

.ruleview-warning {
  background-image: url(resource://devtools-shared-images/alert-small.svg);
  fill: var(--yellow-60);
}

.ruleview-invalid-at-computed-value-time-warning {
    background-image: var(--invalid-at-computed-value-time-icon);
    fill: var(--invalid-at-computed-value-time-fill-color);
}


.ruleview-unused-warning {
  background-image: url(resource://devtools-shared-images/info-small.svg);
  background-color: var(--theme-sidebar-background);
  fill: var(--theme-icon-dimmed-color);
}

.ruleview-compatibility-warning {
  background-image: url(chrome://devtools/skin/images/report.svg);
  background-color: var(--theme-sidebar-background);
  fill: var(--theme-icon-dimmed-color);
}

.ruleview-unused-warning:hover,
.ruleview-compatibility-warning:hover {
  fill: var(--theme-icon-color);
  stroke: var(--theme-icon-color);
}

.ruleview-rule:not(:hover, :focus-within) .ruleview-enableproperty {
  /* Use opacity to hide the checkbox instead of visibility:hidden,
     so that it's possible to focus it with the keyboard. */
  opacity: 0;
}

.ruleview-expander {
  vertical-align: middle;
}

.ruleview-propertycontainer .ruleview-expander {
  margin-left: -6px;
}

.ruleview-propertycontainer .ruleview-expander:dir(rtl):not([open]) {
  /* for preventing .theme-twisty's wrong direction in rtl; Bug 1296648 */
  transform: rotate(-90deg);
}

.ruleview-newproperty {
  /* As we don't have the checkbox displayed before the input, we need to align it
     with the property name above it. */
  margin-inline-start: calc(
    var(--rule-enableproperty-size) +
    var(--rule-enableproperty-margin-inline-start) +
    var(--rule-enableproperty-margin-inline-end)
  );
}

.ruleview-namecontainer,
.ruleview-propertyvaluecontainer,
.ruleview-propertyname,
.ruleview-propertyvalue {
  text-decoration: inherit;
}

.ruleview-computedlist {
  list-style: none;
  padding: 0;
}

.ruleview-overridden-items {
  margin-block: 0;
  list-style: none;
  line-height: 1.5em;
}

.ruleview-overridden-item {
  position: relative;
}

.ruleview-overridden-item::before,
.ruleview-overridden-item::after {
  content: "";
  position: absolute;
  display: block;
  border: 0 solid var(--theme-text-color-alt);
}

.ruleview-overridden-item::before {
  top: 0;
  left: -15px;
  height: 0.8em;
  width: 10px;
  border-left-width: 0.5px;
  border-bottom-width: 0.5px;
}

.ruleview-overridden-item::after {
  left: -15px;
  bottom: -7px;
  height: 100%;
  border-left-width: 0.5px;
}

.ruleview-overridden-item:last-child::after {
  display: none;
}

.ruleview-overridden-item .ruleview-namecontainer {
  margin-left: 0;
}

.ruleview-overridden {
  text-decoration: line-through solid var(--theme-text-color-inactive);
}

/**
 * Hide swatches (tool icons) from properties that are overwritten by higher specificity * rules.
 * .inspector-swatch is a base class for many tool swatches (shapes, color, bezier curves)
 * .inspector-flex and .inspector-grid are custom
 */
.ruleview-overridden .inspector-flex,
.ruleview-overridden .inspector-grid,
.ruleview-overridden .inspector-shapeswatch,
.ruleview-overridden .inspector-swatch {
  display: none;
}

.ruleview-font-family.used-font {
  text-decoration: underline;
}

.styleinspector-propertyeditor {
  padding: 0;
  margin-block: -1px;
  border: none;
  outline-offset: 1px;
  &:focus-visible {
    /* Unset the outline box shadow to prevent overlapping previous/next properties.
       This is safe since those input won't be this part of the UI won't get */
    --theme-outline-box-shadow: initial;
  }
}

.ruleview-property {
  border-left: 3px solid transparent;
  clear: both;
  overflow-wrap: break-word;
  /* When the text of the property wraps, we want the wrapping lines to have an indent so
     they don't go under the checkbox */
  text-indent: var(--rule-computed-margin-inline-start) hanging;

  :root[forced-colors-active] & {
    /* We opt-out of forced-colors because we want the left border to be actually transparent
       in High Contrast Mode when the declaration isn't new/updated, and green when it is */
    forced-color-adjust: none;
    /* But we opt-in forced colors on direct child so we have expected High Contrast colors
       for the children */
    & > * {
      forced-color-adjust: auto;
    }
  }
}

.ruleview-propertycontainer  > * {
  vertical-align: middle;
}

.ruleview-property.ruleview-changed,
.ruleview-property[dirty] {
  border-left-color: var(--theme-highlight-green);
}

.ruleview-highlight {
  /*
   * If we'd rely solely on background color change for highlighted element, we would need
   * to pick a color that would bring at least 3:1 ratio (see 1.4.11 Non-text Contrast).
   * As this would be too disturbing for the user, we need to indicate impacted element
   * with another graphical mean.
   * So here we'll use a bottom box-shadow (not a border to avoid impacting the layout)
   * which will match the style we have in Debugger for search results.
   */
  background-color: var(--inspector-highlight-background-color);
  /* Put the box-shadow inside the element so it's still visible when sibling elements are highlighted  */
  box-shadow: inset 0 -1px 0 0 var(--theme-contrast-border);


  /* property name and value and unmatched/inactive colors need to be adjusted
     in order to have enough contrast against the highlighted background */
  & .ruleview-propertyname {
    color: var(--rule-highlight-propertyname);
  }

  & .ruleview-propertyvalue {
    color: var(--rule-highlight-propertyvalue);
  }

  &.ruleview-selector.unmatched,
  & .inspector-unmatched,
  .ruleview-property:is(.unused, .ruleview-overridden) & :is(.ruleview-propertyname, .ruleview-propertyvalue) {
    color: var(--rule-highlight-unmatched-unused-color);
  }

  /* In High Contrast Mode, we need to specifiy an additional color for elements which
     don't have specific highlighted colors (selectors, semi colons, stylesheet location, ...) */
  :root[forced-colors-active] & {
    color: var(--theme-contrast-color);
    a, .theme-twisty {
      color: var(--theme-contrast-color);
    }

    /* the overridden class is set on a parent element of the ruleview-highlight one */
    .ruleview-overridden:has(&) {
      text-decoration-color: var(--theme-contrast-color);
    }
  }
}

.ruleview-namecontainer:hover > .ruleview-propertyname,
.ruleview-propertyvaluecontainer:hover > .ruleview-propertyvalue {
  text-decoration: 1px underline dashed hsl(0,0%,50%);
}

/* ancestor rules info element (parent at-rules, parent of nested rules, …) */
.ruleview-rule-ancestor-data {
  max-width: 100%;
  display: inline-block;
  list-style: none;
  padding: 0;
  margin: 0;
}

.ruleview-rule-ancestor-data [role="listitem"] {
  /* When there's a search and this element is highlighted, this prevents the background
     color to be applied to the whole width of the parent ul block */
  max-width: max-content;
}

.ruleview-rule-ancestor-data  [role="listitem"].has-tooltip:hover .container-query-declaration {
  text-decoration: underline;
  text-decoration-color: var(--theme-highlight-blue);
  text-decoration-style: dotted;
  text-decoration-skip-ink: none;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

.ruleview-rule-ancestor-data  [role="listitem"] :is(button,[role="button"]).open-inspector {
  /* There's a space character after the button, so add a 1ch margin-inline-start to be consistent */
  margin-inline: 1ch 0;
  user-select: auto;
}

.ruleview-selectors-container,
.ruleview-registered-property-name {
  word-wrap: break-word;
  cursor: text;
}

.ruleview-selector-separator {
  color: var(--theme-text-color-alt);
}

.ruleview-rule:is(.unmatched, [unmatched=true]) .ruleview-selector,
.ruleview-selector.unmatched,
.ruleview-property:is(.unused, .ruleview-overridden) :is(.ruleview-propertyname,.ruleview-propertyvalue) {
  color: var(--theme-text-color-inactive);
}

/* We use a dimmer color for those unmatching element, but we can't convey state change
    only with colors, so let's add a line-through line */
:is(
  .ruleview-selector.unmatched,
  .inspector-unmatched,
  .ruleview-invalid-at-computed-value-time,
  .ruleview-rule:is(.unmatched, [unmatched=true]) .ruleview-selector,
):not(
  /* don't show the strike through when the element is hovered so it's more legible */
  :hover
) {
  text-decoration: line-through solid currentColor;
}

.ruleview-selector.matched > .ruleview-selector-attribute {
  /* TODO: Bug 1178535 Awaiting UX feedback on highlight colors */
}

.ruleview-selector.matched > .ruleview-selector-pseudo-class {
  /* TODO: Bug 1178535 Awaiting UX feedback on highlight colors */
}

.ruleview-selector.matched > .ruleview-selector-pseudo-class-lock {
  color: var(--pseudo-class-text-color);
}

.ruleview-selector-warnings {
  display: inline-block;
  width: 16px;
  height: 12px;
  margin-inline-start: 2px;
  vertical-align: text-top;
  border-radius: 2px;

  border: 1px solid var(--theme-warning-border);
  background-color: var(--theme-warning-background);
  background-repeat: no-repeat;
  background-position: center;
  fill: var(--theme-warning-color);
  -moz-context-properties: fill;

  /* Default warning icon */
  background-image: url(resource://devtools-shared-images/alert-small.svg);
  background-size: 10px;

  &.slow {
    background-image: url(chrome://devtools/content/netmonitor/src/assets/icons/turtle.svg);
    background-size: 12px;
  }
}

/* When hovering the selector warnings icon, highlight the selector it applies to */
.ruleview-selector:has(.ruleview-selector-warnings:hover) {
  background-color: var(--theme-warning-background);
  /* We're not using --theme-warning-border to make it more visible */
  outline: 1px solid var(--theme-warning-color);

  & .ruleview-selector-warnings {
    /* In such case, hide the icon border to make it cleaner */
    border-color: transparent;
  }
}

.ruleview-selectorhighlighter {
  display: inline-block;
  border: none;
  vertical-align: top;
  width: 15px;
  height: 15px;
  margin-left: 5px;
  background: url("chrome://devtools/skin/images/highlight-selector.svg") no-repeat 0 0;
  background-size: contain;
  cursor: pointer;
  -moz-context-properties: fill;
  fill: var(--theme-icon-dimmed-color);
  /* Prevent line break when copy/pasting an entire rule */
  user-select: auto;
}

.ruleview-selectorhighlighter:hover {
  fill: var(--theme-icon-color);
}

.ruleview-selectorhighlighter:active,
.ruleview-selectorhighlighter.highlighted {
  fill: var(--theme-icon-checked-color);
  /* Since the icon is quite thin, it can be hard to differentiate it
     from its regular state. Adding some background helps make it pop a bit */
  background-color: var(--dimmed-highlighter-box-content-color);
}

#ruleview-add-rule-button::before {
  background-image: url("chrome://devtools/skin/images/add.svg");
  background-size: 14px;
}

#pseudo-class-panel-toggle::before {
  content: ":hov";
}

#class-panel-toggle::before {
  content: ".cls";
}

#pseudo-class-panel-toggle::before,
#class-panel-toggle::before {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  direction: ltr;
}

#print-simulation-toggle::before {
  background-image: url("chrome://devtools/skin/images/rules-view-print-simulation.svg");
}

#color-scheme-simulation-light-toggle::before {
  background-image: url("chrome://devtools/skin/images/rules-view-light-mode-simulation.svg");
  -moz-context-properties: fill, stroke;
  stroke: currentColor;
}

#color-scheme-simulation-dark-toggle::before {
  background-image: url("chrome://devtools/skin/images/rules-view-dark-mode-simulation.svg");
}

.flash-out {
  transition: background 1s;
}

.ruleview-propertyvalue-draggable {
  cursor: col-resize;
}

.ruleview-propertyvalue-dragging {
  user-select: none;
}

.ruleview-propertyvalue-break-spaces {
  white-space: break-spaces;
}
