/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root {
  --compatibility-base-unit: 4px;
  --compatibility-cause-color: var(--theme-text-color-alt);
  --compatibility-issue-icon-size: calc(var(--compatibility-base-unit) * 3);
  --compatibility-issue-item-height: calc(var(--compatibility-base-unit) * 4);
  --compatibility-issue-mdn-icon-fill-color: var(--grey-60);
  --compatibility-browser-icon-size: calc(var(--compatibility-base-unit) * 4);
  --compatibility-browser-icon-fill-color: var(--theme-icon-dimmed-color);
  --compatibility-browser-version-background-color: var(--theme-icon-error-color);
  --compatibility-browser-version-color: white;
}

:root.theme-dark {
  --compatibility-issue-mdn-icon-fill-color: var(--grey-40);
  --compatibility-browser-version-color: black;
}

/*
 * In dark mode, the tooltip background is slightly lighter than the compatibility panel
 * background, so we need to adjust colors to have good contrast.
 */
:root.theme-dark .devtools-tooltip-css-compatibility {
  --compatibility-browser-icon-fill-color: var(--theme-icon-color);
  --compatibility-browser-version-background-color: var(--red-40-a90);
}

.compatibility-app {
  height: 100%;
}

.compatibility-app__throbber {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--compatibility-base-unit) * 8);
}

.compatibility-app__container {
  display: grid;
  grid-template-rows: 1fr calc(var(--compatibility-base-unit) * 7);
  height: 100%;
}

.compatibility-app__container-hidden {
  display: none;
}

.compatibility-app__main {
  overflow: auto;
}

.compatibility-issue-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
 * Layout of the issue item
 *  +--------+-------------------+
 *  | [icon] | issue description |
 *  +--------+-------------------+
 *  |        | aliases           |
 *  +--------+-------------------+
 *  |        | occurrences       |
 *  +--------+-------------------+
 */
.compatibility-issue-item {
  display: grid;
  column-gap: var(--compatibility-base-unit);
  padding: calc(var(--compatibility-base-unit) * 2);
  grid-template-areas: "icon description"
                       ".    aliases"
                       ".    node-pane";
  grid-template-columns: auto 1fr;
}

.compatibility-issue-item:not(:last-child) {
  border-block-end: 1px solid var(--theme-splitter-color);
}

.compatibility-issue-item::before {
  grid-area: icon;
  content: "";
  display: block;
  width: var(--compatibility-issue-item-height);
  height: var(--compatibility-issue-item-height);
  background-size: var(--compatibility-issue-icon-size);
  background-position: center;
  background-repeat: no-repeat;
  -moz-context-properties: fill;
  /* In order to fit to the position */
  position: relative;
  top: -1px;
}

.compatibility-issue-item--experimental::before,
.compatibility-issue-item--unsupported::before {
  background-image: url(chrome://devtools/skin/images/info.svg);
  fill: currentColor;
}

.compatibility-issue-item--deprecated::before {
  background-image: url(resource://devtools-shared-images/alert-small.svg);
  fill: var(--theme-icon-warning-color);
}

/*
 * Layout of the issue description
 *  +--------------+----------------+---------------------------+
 *  | issue factor | (issue causes) | unsupported browser icons |
 *  +--------------+----------------+---------------------------+
 */
.compatibility-issue-item__description {
  grid-area: description;
  display: flex;
  column-gap: var(--compatibility-base-unit);
  row-gap: var(--compatibility-base-unit);
  flex-wrap: wrap;
  justify-content: space-between;
}

.compatibility-issue-item__causes {
  flex: auto;
  color: var(--compatibility-cause-color);
}

.compatibility-issue-item__property {
  color: var(--theme-link-color);
  unicode-bidi: plaintext;
  display: flex;
  gap: var(--compatibility-base-unit);
}

.compatibility-issue-item__mdn-link::after {
  content: "";
  width: 12px;
  height: 12px;
  display: inline-block;
  background-image: url("chrome://devtools/skin/images/mdn.svg");
  background-size: contain;
  background-repeat: no-repeat;
  border: 1px solid var(--compatibility-issue-mdn-icon-fill-color);
  -moz-context-properties: fill;
  fill: var(--compatibility-issue-mdn-icon-fill-color);
}

.compatibility-issue-item__aliases {
  grid-area: aliases;
  list-style: none;
  margin: 0;
  padding-inline-start: calc(var(--compatibility-base-unit) * 2);
  padding-block-end: var(--compatibility-base-unit);
}

.compatibility-issue-item__alias {
  unicode-bidi: plaintext;
}

.compatibility-unsupported-browser-list {
  list-style: none;
  padding: 0;
  flex: 1 0 auto;
  display: flex;
  column-gap: 6px;
  justify-content: end;
}

.compatibility-browser {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-inline: 1px;
}

.compatibility-browser-icon {
  display: inline-block;
  width: var(--compatibility-browser-icon-size);
  height: var(--compatibility-browser-icon-size);
  position: relative;
}

.compatibility-browser-icon--mobile::after {
  content: "";
  width: calc(var(--compatibility-base-unit) * 2);
  height: calc(var(--compatibility-base-unit) * 3);
  background-color: var(--theme-body-background);
  background-size: calc(var(--compatibility-base-unit) * 2 - 2px);
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(chrome://devtools/skin/images/browsers/mobile.svg);
  -moz-context-properties: fill;
  fill: var(--theme-icon-dimmed-color);
  position: absolute;
  right: calc(var(--compatibility-base-unit) * -1);
  bottom: calc(var(--compatibility-base-unit) * -1);
}

.compatibility-browser-icon__image {
  width: 100%;
  height: 100%;
  fill: var(--compatibility-browser-icon-fill-color);
  -moz-context-properties: fill;
}

.compatibility-browser .compatibility-browser-version {
  font-size: 9px;
  border-radius: 2px;
  padding: 1px 2px;
  line-height: 1;
  background-color: var(--compatibility-browser-version-background-color);
  color: var(--compatibility-browser-version-color);
}

.compatibility-node-pane {
  grid-area: node-pane;
}

.compatibility-node-pane__summary {
  color: var(--theme-comment);
  cursor: pointer;
  font-variant-numeric: tabular-nums;
  padding-block-end: var(--compatibility-base-unit);
}

.compatibility-node-list {
  list-style: none;
  margin: 0;
  padding-inline-start: calc(var(--compatibility-base-unit) * 2);
}

.compatibility-node-item:not(:last-child) {
  padding-block-end: var(--compatibility-base-unit);
}

.compatibility-footer {
  border-top: 1px solid var(--theme-splitter-color);
  display: flex;
  justify-content: space-evenly;
}

.compatibility-footer__button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  column-gap: var(--compatibility-base-unit);
}

.compatibility-footer__icon {
  -moz-context-properties: fill;
  fill: var(--theme-icon-color);
  width: calc(var(--compatibility-base-unit) * 4);
  height: calc(var(--compatibility-base-unit) * 4);
}

.compatibility-footer__label {
  color: var(--theme-toolbar-color);
  font-size: calc(var(--compatibility-base-unit) * 3);
}

.compatibility-settings {
  width: 100%;
  height: 100%;
  background-color: var(--theme-body-background);
  overflow: auto;
}

.compatibility-settings__header {
  display: grid;
  grid-template-columns: 1fr auto;
  padding-block: calc(var(--compatibility-base-unit) * 3);
  padding-inline: calc(var(--compatibility-base-unit) * 4);
}

.compatibility-settings__header-label {
  color: var(--theme-toolbar-color);
  font-size: calc(var(--compatibility-base-unit) * 3);
  font-weight: bold;
}

.compatibility-settings__header-button {
  background: none;
  border: none;
}

.compatibility-settings__header-icon {
  -moz-context-properties: fill;
  fill: var(--theme-icon-color);
  width: calc(var(--compatibility-base-unit) * 4);
  height: calc(var(--compatibility-base-unit) * 4);
}

.compatibility-settings__target-browsers-header {
  font-size: calc(var(--compatibility-base-unit) * 3);
  line-height: calc(var(--compatibility-base-unit) * 4);
  padding-block: var(--compatibility-base-unit);
  padding-inline: calc(var(--compatibility-base-unit) * 4);
  background-color: var(--theme-toolbar-background);
  border-block: 1px solid var(--theme-splitter-color);
}

.compatibility-settings__target-browsers-list {
  display: grid;
  list-style: none;
  margin: 0;
  padding-block: calc(var(--compatibility-base-unit) * 3);
  padding-inline: calc(var(--compatibility-base-unit) * 4);
  row-gap: calc(var(--compatibility-base-unit) * 2);
}

.compatibility-settings__target-browsers-item {
  display: grid;
  grid-template-columns: auto auto 1fr;
  align-items: center;
  padding-inline-start: calc(var(--compatibility-base-unit) * 2);
  column-gap: calc(var(--compatibility-base-unit) * 2);
}

.compatibility-settings__target-browsers-item-label {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 2/-1;
}
