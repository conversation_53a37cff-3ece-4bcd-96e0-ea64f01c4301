/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url(chrome://devtools/skin/variables.css);
@import url(chrome://devtools/skin/common.css);
@import url(chrome://devtools/skin/toolbars.css);
@import url(chrome://devtools/skin/tooltips.css);

body {
  margin: 0;
}

.theme-body {
  background: var(--theme-body-background);
  color: var(--theme-body-color);
}

.theme-sidebar {
  background: var(--theme-sidebar-background);
  color: var(--theme-body-color);
}

.theme-selected,
.CodeMirror-hint-active {
  background-color: var(--theme-selection-background);
  color: var(--theme-selection-color);
}

.variable-or-property:not([overridden])[changed] {
  background: var(--theme-contrast-background);
}

.theme-link,
.cm-s-mozilla .cm-link,
:is(.cm-editor, .cm-highlighted) .tok-link,
.CodeMirror-Tern-type {
  color: var(--theme-comment);
}

/*
 * FIXME: http://bugzil.la/575675 CSS links without :visited set cause assertion
 * failures in debug builds.
 */
:is(
  .theme-link,
  .cm-s-mozilla .cm-link,
  :is(.cm-editor, .cm-highlighted) .tok-link
):visited {
  color: var(--theme-link-color);
}

.theme-comment,
.cm-s-mozilla .cm-meta,
.cm-s-mozilla .cm-hr,
.cm-s-mozilla .cm-comment
:is(.cm-editor, .cm-highlighted) :is(
  .tok-meta,
  .tok-comment
),
.variable-or-property .token-undefined,
.variable-or-property .token-null,
.CodeMirror-Tern-completion-unknown:before {
  color: var(--theme-comment);
}

.cm-s-mozilla .cm-unused-line {
  text-decoration: line-through;
  text-decoration-color: var(--theme-highlight-bluegrey);
}

.cm-s-mozilla .cm-executed-line {
  background-color: #fcfffc;
}

.cm-s-mozilla .cm-number,
:is(.cm-editor, .cm-highlighted) .tok-number,
.variable-or-property .token-number,
.variable-or-property[return] > .title > .name,
.variable-or-property[scope] > .title > .name {
  color: var(--theme-highlight-purple);
}

.CodeMirror-Tern-completion-number:before {
  background-color: hsl(72,100%,27%);
}

.theme-fg-color1,
.cm-s-mozilla .cm-attribute,
.cm-s-mozilla .cm-builtin,
.cm-s-mozilla .cm-keyword,
:is(.cm-editor, .cm-highlighted) .tok-keyword,
.variables-view-variable > .title > .name {
  color: var(--theme-highlight-red);
}

.cm-s-mozilla .cm-def,
.cm-s-mozilla .cm-variable-2,
:is(.cm-editor, .cm-highlighted) :is(
  .tok-variableName.tok-definition,
  .tok-variableName
) {
  color: var(--blue-55);
}

.cm-s-mozilla .cm-variable,
:is(.cm-editor, .cm-highlighted) .tok-variableName2 {
  color: var(--purple-60);
}

.cm-s-mozilla .cm-property,
:is(.cm-editor, .cm-highlighted) .tok-propertyName {
  color: var(--theme-highlight-green);
}

.CodeMirror-Tern-completion-object:before {
  background-color: hsl(208,56%,40%);
}

.theme-fg-color3,
.cm-s-mozilla .cm-tag,
.cm-s-mozilla .cm-header,
.cm-s-mozilla .cm-bracket,
.cm-s-mozilla .cm-qualifier,
:is(.cm-editor, .cm-highlighted) .tok-heading,
.variables-view-property > .title > .name {
  color: var(--theme-highlight-blue);
}

.CodeMirror-Tern-completion-array:before {
  background-color: var(--theme-highlight-bluegrey);
}

.theme-fg-color2,
.cm-s-mozilla .cm-string,
.cm-s-mozilla .cm-string-2,
:is(.cm-editor, .cm-highlighted) .tok-string,
.variable-or-property .token-string,
.CodeMirror-Tern-farg {
  color: var(--theme-highlight-purple);
}

.CodeMirror-Tern-completion-string:before,
.CodeMirror-Tern-completion-fn:before {
  background-color: hsl(24,85%,39%);
}

.cm-s-mozilla .cm-atom,
.cm-s-mozilla .cm-quote,
.cm-s-mozilla .cm-error,
:is(.cm-editor, .cm-highlighted) :is(
  .tok-atom,
  .tok-bool,
  .tok-invalid
),
.variable-or-property .token-boolean,
.variable-or-property .token-domnode,
.variable-or-property[exception] > .title > .name {
  color: var(--theme-highlight-red);
}

.CodeMirror-Tern-completion-bool:before {
  background-color: #bf5656;
}

.variable-or-property .token-domnode {
  font-weight: bold;
}

.theme-toolbar,
.devtools-toolbar,
.devtools-sidebar-tabs tabs,
.devtools-sidebar-alltabs,
.cm-s-mozilla .CodeMirror-dialog { /* General toolbar styling */
  color: var(--theme-body-color);
  background-color: var(--theme-toolbar-background);
  border-color: var(--theme-splitter-color);
}

.theme-bg-contrast {
  background: var(--theme-contrast-background);
}

.theme-fg-contrast {
  color: var(--theme-contrast-color);
}

/* CodeMirror specific styles.
 * Best effort to match the existing theme, some of the colors
 * are duplicated here to prevent weirdness in the main theme. */

.CodeMirror.cm-s-mozilla { /* Inherit platform specific font sizing and styles */
  font-family: inherit;
  font-size: inherit;
  background: transparent;
}

.CodeMirror.cm-s-mozilla  pre,
.CodeMirror.cm-s-mozilla  pre.CodeMirror-line,
.CodeMirror.cm-s-mozilla  pre.CodeMirror-line-like,
.cm-s-mozilla .cm-variable-3,
.cm-s-mozilla .cm-operator,
.cm-s-mozilla .cm-special,
:is(.cm-editor, .cm-highlighted) :is(
  .tok-punctuation,
  .tok-operator
) {
  color: var(--theme-body-color);
}

.cm-s-mozilla .CodeMirror-lines .CodeMirror-cursor:not(.CodeMirror-secondarycursor),
.cm-editor .cm-cursor {
  border-left: solid 1px black;
}

.cm-s-mozilla .CodeMirror-activeline-background { /* selected color with alpha */
  background: rgba(185, 215, 253, .35);
}

/* Highlight for a line that contains an error. */
div.CodeMirror div.error-line {
  background: rgba(255,0,0,0.2);
}

/* Generic highlighted text */
div.CodeMirror span.marked-text {
  background: rgba(255,255,0,0.2);
  border: 1px dashed rgba(192,192,0,0.6);
  margin-inline-start: -1px;
  margin-inline-end: -1px;
}

.cm-s-mozilla .empty-line .CodeMirror-linenumber,
/* Codemirror 6 */
:is(.cm-editor, .cm-highlighted) .cm-gutterElement.empty-line {
  color: var(--grey-40);
}

/* Blackboxing lines */
.CodeMirror-lines .blackboxed-line :is(span, .cm-comment, .CodeMirror-gutter-elt),
/* Codemirror 6 */
.cm-gutterElement.blackboxed-line, .cm-line.blackboxed-line span[class^="tok-"] {
  color: #806414cc;
}

/* Highlight for evaluating current statement. */
div.CodeMirror span.eval-text {
  background-color: #ccd;
}

.cm-s-mozilla .CodeMirror-linenumber { /* line number text */
  color: var(--grey-50);
}

.cm-s-mozilla .CodeMirror-gutters,
.cm-editor .cm-gutters { /* vertical line next to line numbers */
  border-right-color: var(--theme-splitter-color);
  background-color: var(--theme-sidebar-background);
}

.CodeMirror-hints,
.CodeMirror-Tern-tooltip {
  box-shadow: 0 0 4px rgba(128, 128, 128, .5);
  background-color: var(--theme-sidebar-background);
}
