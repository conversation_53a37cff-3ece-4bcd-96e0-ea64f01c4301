/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

/**
 * HSplitBox Component
 * Styles for React component at `devtools/client/shared/components/HSplitBox.js`
 */

.h-split-box,
.h-split-box-pane {
  overflow: auto;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.h-split-box {
  display: flex;
  flex-direction: row;
  flex: 1;
}
