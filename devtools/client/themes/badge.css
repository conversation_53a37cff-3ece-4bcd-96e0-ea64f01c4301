/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

 :root {
  --badge-active-background-color: var(--blue-60);
  --badge-active-border-color: light-dark(rgba(255, 255, 255, 0.7), #FFF6);
  --badge-background-color: light-dark(white, var(--grey-80));
  --badge-border-color: light-dark(#CACAD1, var(--grey-50));
  --badge-color: light-dark(var(--grey-60), var(--grey-40));
  --badge-interactive-background-color: light-dark(var(--grey-20), var(--grey-70));
  --badge-interactive-color: light-dark(var(--grey-90), var(--grey-30));
  --badge-hover-interactive-background-color: light-dark(#DFDFE8, var(--grey-80));
  --badge-hover-interactive-color: var(--badge-color);
  --badge-interactive-border-color: light-dark(#CACAD1, var(--grey-50));
  --badge-scrollable-color: light-dark(#8000D7, #B98EFF);
  --badge-scrollable-background-color: light-dark(#FFFFFF, var(--badge-interactive-background-color));
  --badge-scrollable-active-background-color: #8000D7;
  --badge-scrollable-active-color: var(--theme-selection-color);

  &[forced-colors-active] {
    --badge-active-background-color: SelectedItem;
    --badge-active-border-color: SelectedItemText;
    --badge-color: CanvasText;
    --badge-border-color: GrayText;
    --badge-interactive-background-color: ButtonFace;
    --badge-interactive-color: ButtonText;
    --badge-interactive-border-color: ButtonText;
    --badge-hover-interactive-background-color: ButtonFace;
    --badge-hover-interactive-color: SelectedItem;
    --badge-scrollable-color: var(--theme-highlight-purple);
    --badge-scrollable-background-color: var(--theme-highlight-purple-text);
    --badge-scrollable-active-background-color: var(--theme-highlight-purple);
    --badge-scrollable-active-color: var(--theme-highlight-purple-text);
  }
}

/* Inspector badge */
.inspector-badge,
.editor.text .whitespace::before,
.unavailable-children::before {
  display: inline-block;
  /* 9px text is too blurry on low-resolution screens */
  font-size: 10px;
  font-family: var(--monospace-font-family);
  font-weight: normal;
  line-height: 10px;
  height: 12px;
  margin-top: 1px;
  vertical-align: top;
  /* borders are getting clipped in High Contrast Mode because of backplating,
     so we use an outline instead */
  border: none;
  outline: 1px solid var(--badge-border-color);
  border-radius: 5px;
  padding: 0 4px;
  margin-inline-start: 5px;
  user-select: none;
  background-color: var(--badge-background-color);
  color: var(--badge-color);
  box-sizing: border-box;
  /* center the badge text vertically */
  align-content: center;
}

.editor.text .whitespace::before {
  content: attr(data-label);
  margin-inline-start: 0;
}

.unavailable-children::before {
  content: attr(data-label);
  height: auto;
}

@media (min-resolution: 1.1dppx) {
  .inspector-badge,
  .editor.text .whitespace::before {
    font-size: 9px;
  }
}

/* Inspector badges that are interactive/clickable */
.inspector-badge.interactive {
  background-color: var(--badge-interactive-background-color);
  color: var(--badge-interactive-color);
  outline-color: var(--badge-interactive-border-color);
  cursor: pointer;

  &:focus {
    outline: var(--theme-focus-outline);
    outline-offset: var(--theme-outline-offset);
    box-shadow: var(--theme-outline-box-shadow);
  }
}

.inspector-badge:not(.active).interactive:is(:focus, :hover) {
  background-color: var(--badge-hover-interactive-background-color);
  color: var(--badge-hover-interactive-color);
}

.inspector-badge:is(.active,.interactive.active) {
  background-color: var(--badge-active-background-color);
  outline-color: var(--badge-active-border-color);
  color: var(--theme-selection-color);
}

.inspector-badge.interactive.scrollable-badge {
  background-color: var(--badge-scrollable-background-color);
  outline-color: var(--badge-scrollable-color);
  color: var(--badge-scrollable-color);
  forced-color-adjust: none;
}

.inspector-badge.interactive.scrollable-badge.active {
  background-color: var(--badge-scrollable-active-background-color);
  color: var(--badge-scrollable-active-color);
  outline-color: currentColor;
}

.inspector-badge.has-disabled-events {
  font-style: italic;
}

.inspector-badge.has-disabled-events::before {
  content: "*";
  padding-inline-end: 2px;
}
