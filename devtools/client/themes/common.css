/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://devtools/skin/splitters.css");
@namespace html url("http://www.w3.org/1999/xhtml");

:root {
  /* Bug 1458224: the initial `font-size` is different on Mac/Windows
   * (appears fixed to 11px) and Linux (bigger, depends on user settings).
   * Workaround: specify font-size on root, on form inputs, and whenever
   * we're using the `font` shorthand. */
  font: message-box;
  font-size: var(--theme-body-font-size);

  --tab-line-hover-color: light-dark(
    rgba(0,0,0,.2),
    rgba(255,255,255,.2)
  );

  --tab-line-selected-color: var(--blue-50);
  --toggle-thumb-color: light-dark(white, var(--grey-40));
  --toggle-thumb-color-pressed: white;
  --toggle-track-color: var(--grey-50);
  --toggle-track-color-pressed: var(--blue-55);
  --toggle-track-border-color: transparent;
  /* In regular themes, the disabled style is handled by the lowered opacity,
     the colors fall back to their "default" values */
  --toggle-disabled-track-border-color: var(--toggle-track-border-color);
  --toggle-disabled-track-color-pressed: var(--toggle-track-color-pressed);
  --toggle-disabled-thumb-color: var(--toggle-disabled-thumb-color);
  --toggle-track-disabled-opacity: 0.5;
  /* We don't have distinct :hover style in regular themes, the colors fall back to their
     "default" values */
  --toggle-hover-track-color: var(--toggle-track-color);
  --toggle-hover-track-border-color: var(--toggle-track-border-color);
  --toggle-hover-track-color-pressed: var(--toggle-track-color-pressed);
  --toggle-hover-thumb-color: var(--toggle-thumb-color);

  --searchbox-no-match-background-color: light-dark(#ffe5e5, #402325);
  --searchbox-no-match-stroke-color: light-dark(var(--red-60),var(--red-50));
  --searchbox-background-color: transparent;
  --searchbox-border-color: transparent;

  --devtools-searchinput-clear-cross-color: transparent;
  --devtools-searchinput-clear-hover-cross-color: transparent;
  --devtools-searchinput-clear-opacity: 0.8;

  /* Don't allow forced colors in DevTools if they're not supported */
  &:not([forced-colors-active]) {
    forced-color-adjust: none;
  }

  /* Forced Colors / High Contrast Mode specifics */
  &[forced-colors-active] {
    --toggle-track-color: ButtonFace;
    --toggle-track-color-pressed: ButtonText;
    --toggle-thumb-color: ButtonText;
    --toggle-thumb-color-pressed: ButtonFace;
    --toggle-track-border-color: ButtonText;
    --toggle-hover-track-color: SelectedItemText;
    --toggle-hover-track-border-color: SelectedItem;
    --toggle-hover-track-color-pressed: SelectedItem;
    --toggle-hover-thumb-color: SelectedItem;
    /* In High Contrast Mode, the opacity is not lowered, so the disabled style is
       handled by modifiying the colors */
    --toggle-disabled-track-border-color: GrayText;
    --toggle-disabled-track-color-pressed: GrayText;
    --toggle-disabled-thumb-color: GrayText;
    --toggle-track-disabled-opacity: 1;

    /* From High Contrast Mode guidelines:
       > Error inputs in our traditional design system use color to indicate they are destructive.
       > We cannot do this in HCM, so the default INPUT style should be used instead.
      Given this, we should use the default color.
    */
    --searchbox-no-match-stroke-color: var(--theme-icon-color);
    --searchbox-background-color: ButtonFace;
    --searchbox-border-color: ButtonText;

    --devtools-searchinput-clear-opacity: 1;
    --devtools-searchinput-clear-cross-color: ButtonFace;
    --devtools-searchinput-clear-hover-cross-color: SelectedItemText;
  }
}

:root.theme-light {
  color-scheme: light;
}

:root.theme-dark {
  color-scheme: dark;
}

:root[platform="mac"] {
  --monospace-font-family: Menlo, monospace;
}

:root[platform="win"] {
  --monospace-font-family: Consolas, monospace;
}

:root[platform="linux"] {
  --monospace-font-family: monospace;
}

/**
 * Customize the dark theme's scrollbar colors to avoid excessive contrast.
 */
:root.theme-dark {
  scrollbar-color: var(--grey-50) var(--theme-splitter-color);
}

/**
 * Customize scrollbar colors on Linux + light theme, to avoid visual conflicts
 * between the light theme and the selected GTK theme (see bug 1471163).
 * This removes GTK scrollbars and uses a fallback design (see bug 1464723).
 */
:root[platform="linux"].theme-light {
  scrollbar-color: var(--grey-40) var(--grey-20);
}

::selection {
  background-color: var(--theme-selection-background);
  color: var(--theme-selection-color);
}

.devtools-monospace {
  font-family: var(--monospace-font-family);
  font-size: var(--theme-code-font-size);
}

/**
 * For text that needs to be cut with an ellipsis …
 */
.devtools-ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**
 * Override global.css input styles
 */
html|input {
  margin: revert;
}

/**
 * Override wrong system font from forms.css
 * Bug 1458224: buttons use a wrong default font-size on Linux
 */
html|button, html|select, html|input {
  font: message-box;
  font-size: var(--theme-body-font-size);
}

/* Devtools rely on the old Gecko/HTML4 button hit testing */
html|button > * {
  pointer-events: none;
}

/* Prevent buttons from having a text selection highlight. Bug 1546366 */
button::selection {
  all: unset;
}

iframe {
  border: 0;
  min-width: 0;
  min-height: 0;
}

/* Autocomplete Popup */
.devtools-autocomplete-popup {
  overflow: hidden;

  /* Devtools autocompletes display technical english keywords and should be displayed
     using LTR direction. */
  direction: ltr !important;
}

/* Reset list styles. */
.devtools-autocomplete-popup ul {
  list-style: none;
}

.devtools-autocomplete-popup ul,
.devtools-autocomplete-popup li {
  margin: 0;
}

.devtools-autocomplete-listbox {
  --autocomplete-item-padding-inline: 8px;
  --autocomplete-item-color-swatch-size: 1em;
  --autocomplete-item-color-swatch-margin-inline: 5px;
  appearance: none !important;
  background-color: transparent;
  border-width: 0 !important;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  max-height: 20rem;
  box-sizing: border-box;
}

.devtools-autocomplete-listbox .autocomplete-item {
  width: 100%;
  box-sizing: border-box;
  background-color: transparent;
  color: var(--theme-popup-color);
  padding: 1px var(--autocomplete-item-padding-inline);
  cursor: default;
  text-overflow: ellipsis;
  white-space: pre;
  overflow: hidden;
}

.devtools-autocomplete-listbox .autocomplete-item > .initial-value,
.devtools-autocomplete-listbox .autocomplete-item > .autocomplete-value {
  margin: 0;
  padding: 0;
  float: inline-start;
}

.devtools-autocomplete-listbox .autocomplete-item > .autocomplete-postlabel {
  font-style: italic;
  float: inline-end;
  padding-inline-end: 3px;
}

.devtools-autocomplete-listbox .autocomplete-item > .autocomplete-count {
  text-align: end;
}

.devtools-autocomplete-listbox .autocomplete-swatch {
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 0 0 0 1px #c4c4c4;
  width: var(--autocomplete-item-color-swatch-size);
  height: var(--autocomplete-item-color-swatch-size);
  vertical-align: middle;
  /* align the swatch with its value */
  margin-top: -1px;
  margin-inline: var(--autocomplete-item-color-swatch-margin-inline);
  display: inline-block;
  position: relative;
}

/* Rest of the dark and light theme */
.devtools-autocomplete-popup,
.tooltip-panel.devtools-autocomplete-popup,
.CodeMirror-hints,
.CodeMirror-Tern-tooltip {
  border: 1px solid var(--theme-popup-border-color);
  background-color: var(--theme-popup-background);
  color: var(--theme-popup-color);
}

.devtools-autocomplete-listbox .autocomplete-item:hover {
  background-color: var(--theme-popup-hover-background);
  color: var(--theme-popup-hover-color);
}

.devtools-autocomplete-listbox .autocomplete-selected,
.devtools-autocomplete-listbox .autocomplete-selected:hover {
  background-color: var(--theme-selection-background);
  color: var(--theme-selection-color);
}

/* In High Contrast Mode, we want to distinguish betweeen hover and selected items.
   Since hover is already adding a "SelectedItem" background we can't rely on the color
   here, so add an outline as we do for focused element. This follows the HCM design
   for dropdown (e.g. the Reader Mode voice selector) */
:root[forced-colors-active] .devtools-autocomplete-listbox .autocomplete-selected {
  outline: var(--theme-focus-outline);
  outline-offset: -1px;
  &:not(:hover) {
    background-color: unset;
    color: unset;
  }
}

.devtools-autocomplete-listbox .autocomplete-selected > .initial-value {
  font-weight: bold;
  :root[forced-colors-active] & {
    background-color: Mark;
    color: MarkText;
  }
}

/* Autocomplete list clone used for accessibility. */

.devtools-autocomplete-list-aria-clone {
  /* Cannot use display:none or visibility:hidden : screen readers ignore the element. */
  position: fixed;
  overflow: hidden;
  margin: 0;
  width: 0;
  height: 0;
}

.devtools-autocomplete-list-aria-clone li {
  /* Prevent screen readers from prefacing every item with 'bullet'. */
  list-style-type: none;
}

/* Keyboard focus highlight styles */

:focus-visible {
  outline: var(--theme-focus-outline);
  outline-offset: var(--theme-outline-offset);
  box-shadow: var(--theme-outline-box-shadow);

  /* override properties set in global-shared.css for links  */
  a& {
    outline-color: var(--theme-focus-outline-color) !important;
    outline-width: var(--theme-focus-outline-size) !important;
  }

  /* offset the outline for checkbox and radio buttons so it doesn't conflict with existing border */
  :is(input[type="radio"], input[type="checkbox"], checkbox)& {
    outline-offset: 2px;
  }

  :is(input[type="text"], input[type="search"], input:not([type]))& {
    outline-color: var(--theme-focus-textinput-outline-color);
  }
}

/* Toolbar buttons */
.devtools-togglebutton,
.devtools-button {
  appearance: none;
  background: transparent;
  border: none;
  border-radius: 2px;
  color: var(--theme-body-color);
  align-items: center;
  text-shadow: none;
  padding: 2px;
  margin: 1px;

  /* Button text should not wrap on multiple lines */
  white-space: nowrap;
}

.devtools-togglebutton {
  padding: 1px 6px;
  border-block: 2px solid transparent;
  /* Separate the outline from the button as it might have a similar background color */
  outline-offset: 2px;

  :root[forced-colors-active] & {
    border: 1px solid currentColor;
  }
}

/* Remove system form border from devtools-button. */
.devtools-button::-moz-focus-inner {
  border: 0;
}

/* Button with text */
.devtools-button:not(:empty) {
  padding-inline: 5px;
  background: var(--toolbarbutton-background);
}

/* Button icon */
.devtools-button::before {
  content: none;
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin: 0 3px;
  color: var(--theme-icon-color);
  font-size: 11px;
  -moz-context-properties: fill;
  fill: currentColor;
  background-position: center;
  background-repeat: no-repeat;
}

/* Reveal the icon */
.devtools-button:empty::before {
  content: "";
}

.devtools-button:is(.checked, [aria-pressed="true"])::before {
  color: var(--theme-icon-checked-color);
}

/* Icon-only buttons */
.devtools-button:hover::before,
.devtools-button:is(.checked, [aria-pressed="true"])::before {
  opacity: 1;
}

/* Button states */
.devtools-button:disabled {
  pointer-events: none;

  &::before {
    fill: var(--theme-icon-disabled-color);
  }
}

.devtools-button:empty:not(
  [aria-expanded="true"],
  [aria-pressed="true"],
  .checked
):hover {
  background: var(--toolbarbutton-hover-background);
  color: var(--toolbarbutton-hover-color);

  &::before {
    color: var(--theme-icon-hover-color);
  }
}

/* Standalone buttons */
.devtools-button[data-standalone] {
  min-height: 28px;
  padding: 4px 6px;
  border: 1px solid transparent;
  background: var(--toolbarbutton-background);
}

.theme-light .devtools-button[data-standalone] {
  border-color: rgba(138,161,180,0.2);
}

/* Selectable button which is unchecked. */

.devtools-button:not(:empty, .checked, [aria-pressed="true"]):hover,
.devtools-button[aria-haspopup="menu"][aria-expanded="true"] {
  background-color: var(--toolbarbutton-hover-background);
  color: var(--toolbarbutton-hover-color);
}

.devtools-button:empty:is(.checked, [aria-pressed="true"]) {
  background-color: var(--toolbarbutton-checked-background);
}

.devtools-button:not(:empty, .checked, [aria-pressed="true"]):hover:active {
  background-color: var(--theme-selection-background-hover);
}

.devtools-togglebutton:not([aria-pressed="true"]) {
  background: var(--theme-toolbarbutton-background);
  color: var(--theme-toolbarbutton-color);
}

.devtools-togglebutton:hover:not([aria-pressed="true"], :active) {
  background-color: var(--theme-toolbarbutton-hover-background);
  color: var(--theme-toolbarbutton-hover-color);
}

/* Selectable button which is checked. */

.devtools-button:not(:empty):is(.checked, [aria-pressed="true"]) {
  background: var(--toolbarbutton-checked-background);
  color: var(--toolbarbutton-checked-color);
}

.devtools-togglebutton[aria-pressed="true"] {
  background-color: var(--theme-toolbarbutton-checked-background);
  color: var(--theme-toolbarbutton-checked-color);
  border-block-end-color: currentColor;
}

.devtools-togglebutton[aria-pressed="true"]:hover:not(:active) {
  background: var(--theme-toolbarbutton-checked-hover-background);
  color: var(--theme-toolbarbutton-checked-hover-color);
}

/* Only apply active style on non High Contrast Mode */
:root:not([forced-colors-active]) .devtools-togglebutton:active {
  background: var(--theme-toolbarbutton-active-background);
}

.devtools-togglebutton::-moz-focus-inner {
  border-color: transparent;
}

/* Icons */

.devtools-button.devtools-clear-icon::before {
  background-image: url("chrome://devtools/skin/images/clear.svg");
}

.devtools-option-toolbarbutton {
  list-style-image: url("chrome://devtools/skin/images/settings.svg");
}

.devtools-button.devtools-feature-callout::after {
  content: url("chrome://global/skin/icons/badge-blue.svg");
  width: 14px;
  height: 14px;
  display: block;
  position: absolute;
  top: -2px;
  right: 0;
}

/* Text input */

.devtools-textinput,
.devtools-searchinput,
.devtools-filterinput {
  appearance: none;
  margin: 0;
  padding: 0 4px;
  font: inherit;
  border: 1px solid transparent;
  background-color: var(--theme-body-background);
  color: var(--theme-body-color);
  flex-grow: 1;

  &::placeholder {
    /* override UA style */
    opacity: 1;
    /* Set a dimmed color that still gives us enough contrast  */
    color: var(--theme-text-color-alt);
  }
}

.devtools-searchinput,
.devtools-filterinput {
  padding-inline-start: 22px;
  background-position: 7px center;
  background-size: 12px;
  background-repeat: no-repeat;
  -moz-context-properties: fill;
  fill: var(--theme-icon-dimmed-color);
}

.devtools-searchinput {
  background-image: url(chrome://devtools/skin/images/search.svg);
}

.devtools-filterinput {
  background-image: url(chrome://devtools/skin/images/filter-small.svg);
}

.devtools-searchinput:-moz-locale-dir(rtl),
.devtools-searchinput:dir(rtl),
.devtools-filterinput:-moz-locale-dir(rtl),
.devtools-filterinput:dir(rtl) {
  background-position-x: right 7px;
}

.devtools-searchinput .textbox-input::placeholder,
.devtools-filterinput .textbox-input::placeholder {
  font-style: normal;
}

/* Searchbox with extra button(s) (MDN help or clear button) */
.devtools-searchbox {
  display: inline-flex;
  flex-grow: 1;
  gap: 4px;
  padding-inline-end: 4px;
  position: relative;
  border: 1px solid var(--searchbox-border-color);
  background-color: var(--searchbox-background-color);
  height: 100%;
  box-sizing: border-box;
}

.devtools-searchbox > .devtools-searchinput,
.devtools-searchbox > .devtools-filterinput {
  /* Those classes can be applied on <input>, which have a default styling.
     We want .devtools-searchbox to handle the background, border and outline, so we
     need to reset them here */
  background-color: transparent;
  border: none;
  outline: none;
  box-shadow: none;
  flex-grow: 1;
  min-width: 0;
  width: 100%;
}

/* Display an outline on the container when the child input is focused. If another element
   is focused (e.g. a button), we only want the outline on that element */
.devtools-searchbox:focus-within:has(input:focus-visible) {
  outline: var(--theme-focus-outline);
  outline-color: var(--theme-focus-textinput-outline-color);
  outline-offset: -2px;

  &.devtools-searchbox-no-match {
    outline-color: var(--searchbox-no-match-stroke-color);
  }
}

.devtools-searchbox-no-match {
  background-color: var(--searchbox-no-match-background-color);
  border-color: var(--searchbox-no-match-stroke-color);
}

/* Clear icon within the searchbox */
.devtools-searchinput-clear {
  flex: 0 0 auto;
  align-self: center;
  margin: 0;
  padding: 0;
  border: 0;
  width: 16px;
  height: 16px;
  background-color: transparent;
  background-image: url("chrome://devtools/skin/images/search-clear.svg");
  -moz-context-properties: fill, fill-opacity, stroke;
  fill: var(--theme-icon-color);
  fill-opacity: var(--devtools-searchinput-clear-opacity);
  stroke: var(--devtools-searchinput-clear-cross-color);

  &:hover {
    fill-opacity: 1;
    fill: var(--theme-icon-hover-color);
    stroke: var(--devtools-searchinput-clear-hover-cross-color);
  }
}

.devtools-searchbox-no-match > .devtools-searchinput-clear {
  fill: var(--searchbox-no-match-stroke-color);
}

/* MDN link within the searchbox */
.devtools-searchbox .learn-more-link::before {
  opacity: 0.6;
}

.devtools-searchbox:not(:hover) .learn-more-link {
  visibility: hidden;
}

.devtools-searchbox .devtools-searchinput-summary {
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
  color: var(--theme-comment);
  align-self: center;
}

/* Autocomplete popup within the searchbox */
.devtools-searchbox .devtools-autocomplete-popup {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  line-height: initial !important;
  /* See bug - 1414609. z-index is greater than 1000 so that searchbox's z-index
  is more than z-index of requests-list-headers-wrapper in netmonitor */
  z-index: 1001;
}

/* Twisty and checkbox controls */

.theme-twisty {
  width: 14px;
  height: 14px;
  cursor: pointer;
  background-image: url("chrome://devtools/skin/images/arrow.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 10px;
  -moz-context-properties: fill;
  /* Set color and use currentColor in fill so we adapt in High Contrast Mode */
  color: var(--theme-icon-dimmed-color);
  fill: currentColor;
  transform: rotate(-90deg);
}

/* Mirror the twisty for rtl direction */
.theme-twisty:dir(rtl),
.theme-twisty:-moz-locale-dir(rtl) {
  transform: rotate(90deg);
}

.theme-selected ~ .theme-twisty {
  color: var(--theme-selection-color);
}

.theme-twisty:is(
  .open,
  [open],
  [aria-expanded="true"]
) {
  transform: none;
}

.theme-twisty[invisible] {
  visibility: hidden;
}

/* Throbbers */
.devtools-throbber::before {
  content: "";
  display: inline-block;
  vertical-align: bottom;
  margin-inline-end: 0.5em;
  width: 1em;
  height: 1em;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: 1.1s linear throbber-spin infinite;
}

@keyframes throbber-spin {
  from {
    transform: none;
  }
  to {
    transform: rotate(360deg);
  }
}

/* Common tabs styles */

.all-tabs-menu {
  position: absolute;

  top: 0;
  inset-inline-end: 0;
  width: 15px;
  height: 100%;

  padding: 0;
  border: none;
  border-inline-start: 1px solid var(--theme-splitter-color);

  background: var(--theme-tab-toolbar-background);
  background-image: url("chrome://devtools/skin/images/dropmarker.svg");
  background-repeat: no-repeat;
  background-position: center;
  -moz-context-properties: fill;
  fill: var(--theme-icon-color);
  /* The button is often displayed next to the window edge, so adjust the layout to make the offset visible */
  outline-offset: -2px;
}

.all-tabs-menu:hover {
  background-color: var(--theme-toolbar-hover);
}

.all-tabs-menu:hover:active {
  background-color: var(--theme-toolbar-hover-active);
}

.devtools-tab-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: transparent;
  transition: transform 250ms var(--animation-curve), opacity 250ms var(--animation-curve);
  opacity: 0;
  transform: scaleX(0);

  :root[forced-colors-active] & {
    display: none;
  }
}

.devtools-tab:hover .devtools-tab-line,
.tabs-menu-item:hover .devtools-tab-line {
  background: var(--tab-line-hover-color);
  opacity: 1;
  transform: scaleX(1);
}

.devtools-tab.selected .devtools-tab-line,
/* don't show the line when focus is visible to prevent overlapping with the outline */
.tabs-menu-item.is-active .devtools-tab-line:not(:has(+ [role="tab"]:focus-visible)) {
  background: var(--tab-line-selected-color);
  opacity: 1;
  transform: scaleX(1);
}

@media (prefers-reduced-motion) {
  .devtools-tab-line {
    transition: none;
  }
}

.devtools-tab:not(.selected):focus .devtools-tab-line {
  background: var(--tab-line-hover-color);
  opacity: 1;
  transform: scaleX(1);
}

/* No result message styles */

.devtools-sidepanel-no-result {
  font-style: italic;
  padding: 0.5em 20px;
  user-select: none;
  font-size: 12px;
  line-height: calc(16 / 12);
}

/* Checkbox Toggle */

.devtools-checkbox-toggle {
  --x-pos: .15em;
  /* Reset native checkbox styling. */
  appearance: none;
  background-color: var(--toggle-track-color);
  cursor: pointer;
  /* Change font-size to scale. */
  font-size: 16px;
  width: 2em;
  height: 1em;
  border-radius: 1em;
  border: 1px solid var(--toggle-track-border-color);
  box-sizing: content-box;

  &:checked {
    --x-pos: 1.15em;
    background-color: var(--toggle-track-color-pressed);
  }

  &:hover:not(:active, [disabled]) {
    background-color: var(--toggle-hover-track-color);
    border-color: var(--toggle-hover-track-border-color);

    &:checked {
      background-color: var(--toggle-hover-track-color-pressed);
    }

    &:not(:checked)::before {
      background-color: var(--toggle-hover-thumb-color);
    }
  }

  &[disabled] {
    opacity: var(--toggle-track-disabled-opacity);
    --toggle-track-border-color: var(--toggle-disabled-track-border-color);
    --toggle-track-color-pressed: var(--toggle-disabled-track-color-pressed);
    --toggle-thumb-color: var(--toggle-disabled-thumb-color);
  }
}


/* For right-to-left layout, the toggle thumb goes in the opposite direction. */
html[dir="rtl"] .devtools-checkbox-toggle {
  --x-pos: -.15em;
}

html[dir="rtl"] .devtools-checkbox-toggle:checked {
  --x-pos: -1.15em;
}

.devtools-checkbox-toggle::before {
  position: relative;
  width: calc(1em - .3em);
  height: calc(1em - .3em);
  transform: translateY(.15em) translateX(var(--x-pos));
  border-radius: 100%;
  display: block;
  content: "";
  background-color: var(--toggle-thumb-color);
  transition: transform .1s ease-out;
}

.devtools-checkbox-toggle:checked::before {
  background-color: var(--toggle-thumb-color-pressed);
}

/* Remove transition on toggle when prefers reduced motion is enabled */
@media (prefers-reduced-motion) {
  .devtools-checkbox-toggle,
  .devtools-checkbox-toggle::before {
    transition: none;
  }
}
