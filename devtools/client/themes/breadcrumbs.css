/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

 :root {
  --breadcrumb-id-class-color: light-dark(#909090, var(--theme-text-color-strong));
  --breadcrumb-tag-color: light-dark(var(--theme-body-color), var(--theme-contrast-color));

  &[forced-colors-active] {
    --breadcrumb-id-class-color: ButtonText;
    --breadcrumb-tag-color: ButtonText;
  }
}

/* Inspector HTMLBreadcrumbs */

.breadcrumbs-widget-container {
  margin-right: 3px;
  max-height: 24px; /* Set max-height for proper sizing on linux */
  height: 24px; /* Set height to prevent starting small waiting for content */
}

.breadcrumbs-widget-container:-moz-locale-dir(rtl) {
  margin-right: 0;
  margin-left: 3px;
}

.scrollbutton-up,
.scrollbutton-down {
  appearance: none;
  background: transparent;
  box-shadow: none;
  border: none;
  list-style-image: none;
  margin: 0;
  padding: 0;
  visibility: collapse;
}

.scrollbutton-up > .toolbarbutton-icon,
.scrollbutton-down > .toolbarbutton-icon {
  appearance: none;
  width: 20px;
  height: 16px;
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("images/breadcrumbs-scrollbutton.svg");
  list-style-image: none;
  padding: 0;

  -moz-context-properties: fill;
  fill: var(--theme-toolbar-color);
}

.scrollbutton-up:not([disabled]):active:hover,
.scrollbutton-down:not([disabled]):active:hover {
  background-color: var(--theme-toolbar-hover);
}

.scrollbutton-up[disabled] > .toolbarbutton-icon,
.scrollbutton-down[disabled] > .toolbarbutton-icon {
  opacity: 0.5;
}

/* Draw shadows to indicate there is more content 'behind' scrollbuttons. */
.scrollbutton-up {
  border-right: solid 1px var(--theme-splitter-color);
  border-left: solid 1px transparent;
  box-shadow: 3px 0 3px -3px var(--theme-sidebar-background);
}

.scrollbutton-down {
  border-right: solid 1px transparent;
  border-left: solid 1px var(--theme-splitter-color);
  box-shadow: -3px 0 3px -3px var(--theme-sidebar-background);
}

.scrollbutton-up[disabled],
.scrollbutton-down[disabled] {
  box-shadow: none;
  border-color: transparent;
}

.scrollbutton-down > .toolbarbutton-icon {
  transform: scaleX(-1);
}

.breadcrumbs-widget-item {
  --separator-width: 8px;
  --inline-padding: 6px;
  background: none;
  border: none;
  padding: 0 var(--inline-padding);

  &[aria-pressed="true"] {
    color: var(--theme-highlight-blue);

    .breadcrumbs-widget-item-id {
      color: var(--theme-highlight-purple);
    }

    :is(
      .breadcrumbs-widget-item-tag,
      .breadcrumbs-widget-item-pseudo-classes,
      .breadcrumbs-widget-item-classes
    ) {
      color: currentColor;
    }

    :root[forced-colors-active] & {
      background-color: SelectedItem;
      color: SelectedItemText;
    }
  }
}

/* We display a separator before all items except the first one */
.breadcrumbs-widget-item:not(:first-child) {
  /* We don't want the separator to be part of the button visually, so
     we'll move it "between" the items */
  margin-inline-start: var(--separator-width);
  padding-inline-start: 0;

  &::before {
    content: url(chrome://devtools/skin/images/breadcrumbs-divider.svg);
    background: none;
    display: inline-block;
    text-align: center;
    width: var(--separator-width);
    /* move the separator "out" of the breadcrumb item */
    margin-inline-start: calc(-1 * var(--separator-width));
    /* and add margin after to simulate padding for the "inside" of the button */
    margin-inline-end: var(--inline-padding);
    -moz-context-properties: fill;
    fill: var(--theme-icon-disabled-color);
  }
}

.breadcrumbs-widget-item-tag {
  color: var(--breadcrumb-tag-color);
}

.breadcrumbs-widget-item-id,
.breadcrumbs-widget-item-classes {
  color: var(--breadcrumb-id-class-color);
}

.breadcrumbs-widget-item-pseudo-classes {
  color: var(--pseudo-class-text-color);
}
