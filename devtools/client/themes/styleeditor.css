/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#style-editor-chrome {
  flex: 1;
}

.splitview-nav > li,
.stylesheet-info,
.stylesheet-more,
.stylesheet-rule-count,
li.splitview-active > hgroup > .stylesheet-more > h3 > .stylesheet-saveButton,
li:hover > hgroup > .stylesheet-more > h3 > .stylesheet-saveButton {
  display: flex;
}

.devtools-toolbar > spacer {
  flex: 1;
}

.style-editor-newButton {
  list-style-image: url(images/add.svg);
}

.style-editor-importButton {
  list-style-image: url(images/import.svg);
}

/* Filter input */
#style-editor-chrome toolbar .devtools-searchbox {
  /* Adjust default focused styling as it looks a bit off */
  outline-offset: -2px;

  & > .devtools-filterinput {
    box-shadow: none;
  }
}

.stylesheet-details-container {
  flex: 1;
  min-width: 0;
  min-height: 0;
}

.stylesheet-editor-status {
  display: flex;
  align-items: center;
  border-block-start: 1px solid var(--theme-splitter-color) !important;
  border-block-end: none !important;

  /* Only show the editor toolbar if there's a stylesheet editor */
  .devtools-main-content:empty + & {
    display: none;
  }

  .style-editor-prettyPrintButton.devtools-button {
    margin: 0;
    /*
     * common.css sets a pointer-events:none on disabled button,
     * but here we want the title to be displayed since it gives extra information.
     */
    pointer-events: all;

    &::before {
      background-image: url(chrome://devtools/content/debugger/images/prettyPrint.svg);
    }
  }
}

.stylesheet-at-rules-container {
  overflow-y: auto;
  min-width: 0;
  min-height: 0;
}

.stylesheet-error-message {
  display: none;
}

li.error > .stylesheet-info > .stylesheet-more  > .stylesheet-error-message {
  display: block;
}

.stylesheet-title,
.stylesheet-name {
  text-decoration: none;
}

.stylesheet-name {
  font-size: 13px;
  white-space: nowrap;
}

.stylesheet-name > label {
  display: inline;
  cursor: pointer;
}

.stylesheet-info > h1 {
  flex: 1;
}

.stylesheet-info a.stylesheet-name:focus-visible {
  outline-offset: -2px;
}

.splitview-nav > li > hgroup.stylesheet-info {
  justify-content: center;
  /* This prevents stylesheets with long names from breaking the layout of
  the StyleEditor sources sidebar. */
  overflow: hidden;
}

.stylesheet-more > spacer {
  flex: 1;
}

.stylesheet-title,
.stylesheet-name,
.stylesheet-rule-count,
.stylesheet-linked-file,
.stylesheet-saveButton {
  color: var(--theme-body-color);
}

.stylesheet-saveButton {
  display: none;
  margin-top: 0;
  margin-bottom: 0;
  text-decoration: underline;
  cursor: pointer;
}

.splitview-active .stylesheet-title,
.splitview-active .stylesheet-name,
.theme-light .splitview-active .stylesheet-rule-count,
.theme-light .splitview-active .stylesheet-linked-file,
.theme-light .splitview-active .stylesheet-saveButton {
  color: var(--theme-selection-color);
}

.splitview-nav:focus {
  outline: 0; /* focus ring is on the stylesheet name */
}

.splitview-nav > li {
  flex-direction: row;
}

.splitview-nav > li > hgroup {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.splitview-nav > li.unsaved > hgroup .stylesheet-name {
  font-style: italic;
}

.splitview-nav:-moz-locale-dir(ltr) > li.unsaved > hgroup .stylesheet-name:before,
.splitview-nav:-moz-locale-dir(rtl) > li.unsaved > hgroup .stylesheet-name:after {
  font-style: italic;
}

.splitview-nav.empty > p {
  padding: 0 10px;
}

.stylesheet-sidebar {
  max-width: 400px;
  min-width: 100px;
  border-color: var(--theme-splitter-color);
}

.at-rule-label {
  display: grid;
  /*
   * +----------------------------------------------+
   * |        Rule information        | line number |
   * +----------------------------------------------+
   */
  grid-template-columns: 1fr max-content;
  padding: 4px;
  border-bottom: 1px solid var(--theme-splitter-color);
  cursor: pointer;
  word-break: break-word;
}

.media-responsive-mode-toggle {
  color: var(--theme-highlight-blue);
  text-decoration: underline;
}

.at-rule-line {
  padding-inline-start: 4px;
}

.media-condition-unmatched {
  opacity: 0.4;
}

.stylesheet-toggle {
  display: flex;
  cursor: pointer;
  padding: 8px 0;
  margin: 0 8px;
  background-image: url(images/eye-opened.svg);
  background-repeat: no-repeat;
  background-clip: content-box;
  background-position: center;
  background-size: 16px;
  width: 24px;
  height: 40px;
  flex-shrink: 0;

  /* The icon color should always match the text color. */
  -moz-context-properties: fill, stroke;
  fill: currentColor;
  stroke: currentColor;
}

.disabled > .stylesheet-toggle {
  background-image: url(images/eye-closed.svg);
}

.stylesheet-system > .stylesheet-toggle {
  /* Override the default "color: grayText" from [disabled] browser style */
  color: currentColor;
  cursor: auto;
  opacity: 0.3;
}

.stylesheet-linked-file:not(:empty){
  margin-inline-end: 0.4em;
}

.stylesheet-linked-file:not(:empty):before {
  margin-inline-start: 0.4em;
  content: " ↳ ";
}

li.unsaved > hgroup > h1 > .stylesheet-name:before {
  content: "*";
}

li.linked-file-error .stylesheet-linked-file {
  text-decoration: line-through;
}

li.linked-file-error .stylesheet-linked-file:after {
  font-size: 110%;
  content: " ✘";
}

li.linked-file-error .stylesheet-rule-count {
  visibility: hidden;
}

.stylesheet-more > h3 {
  font-size: 11px;
  margin-inline-end: 2px;
}

.placeholder a {
  text-decoration: underline;
}

h1,
h2,
h3 {
  font-size: inherit;
  font-weight: normal;
  margin: 0;
  padding: 0;
}

@media (max-width: 700px) {
  .stylesheet-sidebar {
    width: 150px;
  }
}

/* portrait mode */
@media (max-width: 550px) {
  li.splitview-active > hgroup > .stylesheet-more > .stylesheet-rule-count,
  li:hover > hgroup > .stylesheet-more > .stylesheet-rule-count {
    display: none;
  }

  .splitview-nav {
    box-shadow: none;
  }

  .splitview-nav > li.splitview-active {
    background-size: 0 0, 0 0, auto;
  }

  .stylesheet-toggle {
    padding: 0;
    height: 24px;
  }

  .splitview-nav > li > hgroup.stylesheet-info {
    align-items: baseline;
    flex-direction: row;
    flex: 1;
  }

  .stylesheet-sidebar {
    width: 180px;
  }

  .stylesheet-more {
    flex: 1;
    justify-content: flex-end;
  }

  .stylesheet-more > spacer {
    flex: none;
  }
}

.source-editor-frame {
  flex: 1;
}
