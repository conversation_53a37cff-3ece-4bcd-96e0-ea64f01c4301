/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* Chart */

.generic-chart-container {
  /* Hack: force hardware acceleration */
  transform: translateZ(1px);
  color: light-dark(var(--theme-text-color-alt), var(--theme-text-color-strong));
}

.chart-colored-blob {
  fill: var(--theme-body-color);
  background: var(--theme-body-color);
}

/* Chart: Pie */

.pie-chart-slice {
  stroke-width: 1px;
  cursor: pointer;
  stroke: light-dark(rgba(255,255,255,0.8), rgba(0,0,0,0.2)) ;

  &[largest] {
    stroke: light-dark(#000, #fff);
  }
}

.theme-dark .pie-chart-slice[largest] {
  stroke-width: 2px;
}

.pie-chart-label {
  text-anchor: middle;
  dominant-baseline: middle;
  pointer-events: none;
  fill: light-dark(#fff, #000);
}

.pie-chart-container[slices="1"] > .pie-chart-slice {
  stroke-width: 0;
}

.pie-chart-slice,
.pie-chart-label {
  transition: all 0.1s ease-out;
}

.pie-chart-slice:not(:hover, [focused]),
.pie-chart-slice:not(:hover, [focused]) + .pie-chart-label {
  transform: none !important;
}

/* Chart: Table */

.table-chart-title {
  padding-bottom: 10px;
  font-size: 120%;
  font-weight: 600;
}

.table-chart-row {
  margin-top: 1px;
  cursor: pointer;
}

/* Only adjust opacity when not in high Contrast Mode */
:root:not([forced-colors-active]) {
  .table-chart-grid:hover .table-chart-row {
    transition: opacity 0.1s ease-in-out;
  }

  .table-chart-grid:not(:hover) .table-chart-row {
    transition: opacity 0.2s ease-in-out;
  }

  .generic-chart-container:hover > .table-chart-grid:hover .table-chart-row:not(:hover),
  .generic-chart-container:hover ~ .table-chart-container > .table-chart-grid .table-chart-row:not([focused]) {
    opacity: 0.4;
  }
}

/* Add specific hover style for High Contrast Mode */
:root[forced-colors-active] tbody .table-chart-row:hover {
  outline: 1px solid SelectedItem;
  outline-offset: 2px;
}

.table-chart-row-box {
  width: 8px;
  height: 1.5em;
  margin-inline-end: 10px;
}

.table-chart-row-label {
  width: 8em;
  padding-inline-end: 6px;
  cursor: inherit;
}

.table-chart-totals {
  margin-top: 8px;
  padding-top: 6px;
}

.table-chart-totals {
  border-top: 1px solid var(--theme-splitter-color);
}

.table-chart-summary-label {
  font-weight: 600;
  padding: 1px 0;
  color: light-dark(var(--theme-body-color), var(--theme-text-color-strong));
}
