/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* Rules that may be used in different inspector panels and tooltips */

@import url("./inspector-swatches.css");

:root {
  --inspector-highlight-background-color: light-dark(
    var(--theme-highlight-yellow),
    #521C76
  );
  --inspector-highlight-color: inherit;

  &[forced-colors-active] {
    --inspector-highlight-background-color: Mark;
    --inspector-highlight-color: MarkText;
  }
}

.empty-css-variable {
  color: var(--theme-text-color-alt);
  font-style: italic;
}

.inspector-unmatched {
  color: var(--theme-text-color-alt);
}

.inspector-unmatched:not(
  /* don't show the strike through when the element is hovered so it's more legible */
  :hover
) {
  text-decoration: line-through solid currentColor;
}
