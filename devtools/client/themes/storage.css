/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/*
 * Global styles
 */
a {
  color: var(--theme-link-color);
  text-decoration: none;
  cursor: pointer;
}

/* Storage Host Tree */

#storage-tree {
  min-width: 220px;
  max-width: 500px;
  overflow: auto;
  min-height: 0;
  background: var(--theme-sidebar-background);
  /* Let the component gain focus when a click hits an empty area */
  -moz-user-focus: normal;
}

#storage-tree:focus {
  outline: 0;
}

/* Storage Table */

/* Let the component gain focus when a click hits an empty area */
#storage-table {
  display: flex;
  justify-content: center;
  align-items: center;
  -moz-user-focus: normal;
}

#storage-table:focus {
  outline: 0;
}

.table-widget-cell {
  /* The minimum column width needs to be determined by the header width */
  min-width: unset;
  outline-offset: -2px;
}

.table-widget-column {
  flex-grow: 1;
}

/* Set minimum column widths */

#name {
  min-width: 65px;
}

#host {
  min-width: 80px;
}

#path {
  min-width: 60px;
}

#value {
  min-width: 95px;
  /* Prevent all columns except for value from shrinking with the panel */
  flex-shrink: 1;
}

#hostOnly,
#isHttpOnly,
#sameSite,
#partitioned {
  min-width: 58px;
}

#size,
#isSecure {
  min-width: 48px;
}

#expires,
#lastAccessed,
#creationTime {
  min-width: 115px;
}

/* Text input in storage table */
#storage-table input {
  appearance: none;
  flex: 1;
  /* make sure the outline is not cut off */
  position: relative;
  box-sizing: border-box;
  padding: 0 2px;
  font: inherit;
  color: var(--theme-text-color-strong);
  background-color: var(--theme-body-background);
}

#storage-table input:focus {
  outline: 1px solid var(--blue-50);
}

/* Variables View Sidebar */

#storage-sidebar {
  max-width: 500px;
  min-width: 250px;
}

#storage-sidebar .devtools-toolbar {
  padding-inline: 0;
}

/* Toolbar */

/*
  .devtools-input-toolbar forces display: flex; which does not work
  properly with XUL. Force XUL flexbox instead.
*/
#storage-toolbar {
  display: flex;
}

#storage-searchbox {
  flex: 1;
  margin-inline-start: -3px;
  margin-inline-end: 1px;
  outline-offset: -2px;
}

#storage-toolbar .add-button::before {
  background-image: url("chrome://devtools/skin/images/add.svg");
  -moz-user-focus: normal;
}

#storage-toolbar .refresh-button::before {
  background-image: url("chrome://devtools/skin/images/reload.svg");
  -moz-user-focus: normal;
}

#storage-toolbar .devtools-button {
  min-width: 0;
}

#storage-toolbar .devtools-button hbox,
#storage-toolbar .sidebar-toggle[hidden] {
  display: none;
}

/* Responsive sidebar */
@media (width < 700px) {
  #storage-tree,
  #storage-sidebar {
    max-width: none;
    /* Override potential splitter-set width */
    width: auto !important;
  }
}

@media (width >= 700px) {
  #storage-tree,
  #storage-sidebar {
    /* Override potential splitter-set height */
    height: auto !important;
  }
}
