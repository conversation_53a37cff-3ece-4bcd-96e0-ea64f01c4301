/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

"use strict";

const {
  CONNECTION_TYPES,
  DEBUG_TARGET_TYPES,
} = require("resource://devtools/client/shared/remote-debugging/constants.js");

const actionTypes = {
  ADB_ADDON_INSTALL_START: "ADB_ADDON_INSTALL_START",
  ADB_ADDON_INSTALL_SUCCESS: "ADB_ADDON_INSTALL_SUCCESS",
  ADB_ADDON_INSTALL_FAILURE: "ADB_ADDON_INSTALL_FAILURE",
  ADB_ADDON_UNINSTALL_START: "ADB_ADDON_UNINSTALL_START",
  ADB_ADDON_UNINSTALL_SUCCESS: "ADB_ADDON_UNINSTALL_SUCCESS",
  ADB_ADDON_UNINSTALL_FAILURE: "ADB_ADDON_UNINSTALL_FAILURE",
  ADB_ADDON_STATUS_UPDATED: "ADB_ADDON_STATUS_UPDATED",
  ADB_READY_UPDATED: "ADB_READY_UPDATED",
  CONNECT_RUNTIME_CANCEL: "CONNECT_RUNTIME_CANCEL",
  CONNECT_RUNTIME_FAILURE: "CONNECT_RUNTIME_FAILURE",
  CONNECT_RUNTIME_NOT_RESPONDING: "CONNECT_RUNTIME_NOT_RESPONDING",
  CONNECT_RUNTIME_START: "CONNECT_RUNTIME_START",
  CONNECT_RUNTIME_SUCCESS: "CONNECT_RUNTIME_SUCCESS",
  DEBUG_TARGET_COLLAPSIBILITY_UPDATED: "DEBUG_TARGET_COLLAPSIBILITY_UPDATED",
  DISCONNECT_RUNTIME_FAILURE: "DISCONNECT_RUNTIME_FAILURE",
  DISCONNECT_RUNTIME_START: "DISCONNECT_RUNTIME_START",
  DISCONNECT_RUNTIME_SUCCESS: "DISCONNECT_RUNTIME_SUCCESS",
  EXTENSION_BGSCRIPT_STATUS_UPDATED: "EXTENSION_BGSCRIPT_STATUS_UPDATED",
  HIDE_PROFILER_DIALOG: "HIDE_PROFILER_DIALOG",
  SWITCH_PROFILER_CONTEXT: "SWITCH_PROFILER_CONTEXT",
  NETWORK_LOCATIONS_UPDATE_FAILURE: "NETWORK_LOCATIONS_UPDATE_FAILURE",
  NETWORK_LOCATIONS_UPDATE_START: "NETWORK_LOCATIONS_UPDATE_START",
  NETWORK_LOCATIONS_UPDATE_SUCCESS: "NETWORK_LOCATIONS_UPDATE_SUCCESS",
  REMOTE_RUNTIMES_UPDATED: "REMOTE_RUNTIMES_UPDATED",
  REQUEST_EXTENSIONS_FAILURE: "REQUEST_EXTENSIONS_FAILURE",
  REQUEST_EXTENSIONS_START: "REQUEST_EXTENSIONS_START",
  REQUEST_EXTENSIONS_SUCCESS: "REQUEST_EXTENSIONS_SUCCESS",
  REQUEST_PROCESSES_FAILURE: "REQUEST_PROCESSES_FAILURE",
  REQUEST_PROCESSES_START: "REQUEST_PROCESSES_START",
  REQUEST_PROCESSES_SUCCESS: "REQUEST_PROCESSES_SUCCESS",
  REQUEST_TABS_FAILURE: "REQUEST_TABS_FAILURE",
  REQUEST_TABS_START: "REQUEST_TABS_START",
  REQUEST_TABS_SUCCESS: "REQUEST_TABS_SUCCESS",
  REQUEST_WORKERS_FAILURE: "REQUEST_WORKERS_FAILURE",
  REQUEST_WORKERS_START: "REQUEST_WORKERS_START",
  REQUEST_WORKERS_SUCCESS: "REQUEST_WORKERS_SUCCESS",
  SELECT_PAGE_FAILURE: "SELECT_PAGE_FAILURE",
  SELECT_PAGE_START: "SELECT_PAGE_START",
  SELECT_PAGE_SUCCESS: "SELECT_PAGE_SUCCESS",
  SELECTED_RUNTIME_ID_UPDATED: "SELECTED_RUNTIME_ID_UPDATED",
  SHOW_PROFILER_DIALOG: "SHOW_PROFILER_DIALOG",
  TELEMETRY_RECORD: "TELEMETRY_RECORD",
  TEMPORARY_EXTENSION_INSTALL_FAILURE: "TEMPORARY_EXTENSION_INSTALL_FAILURE",
  TEMPORARY_EXTENSION_INSTALL_START: "TEMPORARY_EXTENSION_INSTALL_START",
  TEMPORARY_EXTENSION_INSTALL_SUCCESS: "TEMPORARY_EXTENSION_INSTALL_SUCCESS",
  TEMPORARY_EXTENSION_RELOAD_FAILURE: "TEMPORARY_EXTENSION_RELOAD_FAILURE",
  TEMPORARY_EXTENSION_RELOAD_START: "TEMPORARY_EXTENSION_RELOAD_START",
  TEMPORARY_EXTENSION_RELOAD_SUCCESS: "TEMPORARY_EXTENSION_RELOAD_SUCCESS",
  TERMINATE_EXTENSION_BGSCRIPT_FAILURE: "TERMINATE_EXTENSION_BGSCRIPT_FAILURE",
  TERMINATE_EXTENSION_BGSCRIPT_START: "TERMINATE_EXTENSION_BGSCRIPT_START",
  TERMINATE_EXTENSION_BGSCRIPT_SUCCESS: "TERMINATE_EXTENSION_BGSCRIPT_SUCCESS",
  THIS_FIREFOX_RUNTIME_CREATED: "THIS_FIREFOX_RUNTIME_CREATED",
  UNWATCH_RUNTIME_FAILURE: "UNWATCH_RUNTIME_FAILURE",
  UNWATCH_RUNTIME_START: "UNWATCH_RUNTIME_START",
  UNWATCH_RUNTIME_SUCCESS: "UNWATCH_RUNTIME_SUCCESS",
  UPDATE_CONNECTION_PROMPT_SETTING_FAILURE:
    "UPDATE_CONNECTION_PROMPT_SETTING_FAILURE",
  UPDATE_CONNECTION_PROMPT_SETTING_START:
    "UPDATE_CONNECTION_PROMPT_SETTING_START",
  UPDATE_CONNECTION_PROMPT_SETTING_SUCCESS:
    "UPDATE_CONNECTION_PROMPT_SETTING_SUCCESS",
  USB_RUNTIMES_SCAN_START: "USB_RUNTIMES_SCAN_START",
  USB_RUNTIMES_SCAN_SUCCESS: "USB_RUNTIMES_SCAN_SUCCESS",
  WATCH_RUNTIME_FAILURE: "WATCH_RUNTIME_FAILURE",
  WATCH_RUNTIME_START: "WATCH_RUNTIME_START",
  WATCH_RUNTIME_SUCCESS: "WATCH_RUNTIME_SUCCESS",
};

const DEBUG_TARGETS = DEBUG_TARGET_TYPES;

const DEBUG_TARGET_PANE = {
  INSTALLED_EXTENSION: "installedExtension",
  PROCESSES: "processes",
  OTHER_WORKER: "otherWorker",
  SERVICE_WORKER: "serviceWorker",
  SHARED_WORKER: "sharedWorker",
  TAB: "tab",
  TEMPORARY_EXTENSION: "temporaryExtension",
};

const ICON_LABEL_LEVEL = {
  INFO: "info",
  OK: "ok",
};

const MESSAGE_LEVEL = {
  ERROR: "error",
  INFO: "info",
  WARNING: "warning",
};

const PAGE_TYPES = {
  RUNTIME: "runtime",
  CONNECT: "connect",
};

const PREFERENCES = {
  // Preference that drives the display of the "Tabs" category on This Firefox.
  LOCAL_TAB_DEBUGGING_ENABLED: "devtools.aboutdebugging.local-tab-debugging",
  // Preference that drives the display of the "Processes" debug target category.
  PROCESS_DEBUGGING_ENABLED: "devtools.aboutdebugging.process-debugging",
  // Preference that drives the display of hidden & system addons in about:debugging.
  SHOW_HIDDEN_ADDONS: "devtools.aboutdebugging.showHiddenAddons",
  // Preference to store the last path used for loading a temporary extension.
  TEMPORARY_EXTENSION_PATH: "devtools.aboutdebugging.tmpExtDirPath",
  // Preference that disables installing extensions when set to false.
  XPINSTALL_ENABLED: "xpinstall.enabled",
};

const RUNTIME_PREFERENCE = {
  CONNECTION_PROMPT: "devtools.debugger.prompt-connection",
  PERMANENT_PRIVATE_BROWSING: "browser.privatebrowsing.autostart",
  SERVICE_WORKERS_ENABLED: "dom.serviceWorkers.enabled",
};

const RUNTIMES = {
  NETWORK: CONNECTION_TYPES.NETWORK,
  THIS_FIREFOX: CONNECTION_TYPES.THIS_FIREFOX,
  USB: CONNECTION_TYPES.USB,
};

const SERVICE_WORKER_FETCH_STATES = {
  LISTENING: "LISTENING",
  NOT_LISTENING: "NOT_LISTENING",
};

const SERVICE_WORKER_STATUSES = {
  RUNNING: "RUNNING",
  REGISTERING: "REGISTERING",
  STOPPED: "STOPPED",
};

const USB_STATES = {
  DISABLED_USB: "DISABLED_USB",
  ENABLED_USB: "ENABLED_USB",
  UPDATING_USB: "UPDATING_USB",
};

const EXTENSION_BGSCRIPT_STATUSES = {
  RUNNING: "RUNNING",
  STOPPED: "STOPPED",
};

/**
 * These constants reference the performance-new's concept of a PageContext.
 * These are defined in devtools/client/performance-new/@types/perf.d.ts
 * about:debugging only uses the remote variants of the PageContexts.
 */
const PROFILER_PAGE_CONTEXT = {
  DEVTOOLS_REMOTE: "devtools-remote",
  ABOUTPROFILING_REMOTE: "aboutprofiling-remote",
};

// flatten constants
module.exports = Object.assign(
  {},
  {
    DEBUG_TARGETS,
    DEBUG_TARGET_PANE,
    EXTENSION_BGSCRIPT_STATUSES,
    ICON_LABEL_LEVEL,
    MESSAGE_LEVEL,
    PAGE_TYPES,
    PREFERENCES,
    RUNTIME_PREFERENCE,
    RUNTIMES,
    SERVICE_WORKER_FETCH_STATES,
    SERVICE_WORKER_STATUSES,
    USB_STATES,
    PROFILER_PAGE_CONTEXT,
  },
  actionTypes
);
