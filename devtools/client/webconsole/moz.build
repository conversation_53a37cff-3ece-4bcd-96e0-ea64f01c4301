# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

DIRS += [
    "actions",
    "components",
    "enhancers",
    "middleware",
    "reducers",
    "selectors",
    "utils",
]
DevToolsModules(
    "browser-console-manager.js",
    "browser-console.js",
    "constants.js",
    "panel.js",
    "service-container.js",
    "store.js",
    "types.js",
    "utils.js",
    "webconsole-ui.js",
    "webconsole-wrapper.js",
    "webconsole.js",
)

BROWSER_CHROME_MANIFESTS += [
    "test/browser/_browser_console.toml",
    "test/browser/_jsterm.toml",
    "test/browser/_webconsole.toml",
    "test/node/fixtures/stubs/stubs.toml",
]

MOCHITEST_CHROME_MANIFESTS += [
    "test/chrome/chrome.toml",
]

XPCSHELL_TESTS_MANIFESTS += ["test/xpcshell/xpcshell.toml"]

with Files("**"):
    BUG_COMPONENT = ("DevTools", "Console")
