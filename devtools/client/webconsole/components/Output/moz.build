# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

DIRS += [
    "message-types",
]

DevToolsModules(
    "CollapseButton.js",
    "ConsoleOutput.js",
    "ConsoleTable.js",
    "GripMessageBody.js",
    "LazyMessageList.js",
    "Message.js",
    "MessageContainer.js",
    "MessageIcon.js",
    "MessageIndent.js",
    "MessageRepeat.js",
)
