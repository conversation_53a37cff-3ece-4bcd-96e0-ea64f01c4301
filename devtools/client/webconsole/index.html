<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<!doctype html>
<html
  dir=""
  id="devtools-webconsole"
  windowtype="devtools:webconsole"
  width="900"
  height="350"
  persist="screenX screenY width height sizemode"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <link rel="stylesheet" href="chrome://global/skin/global.css" />
    <link rel="stylesheet" href="chrome://devtools/skin/webconsole.css" />
    <link rel="stylesheet" href="chrome://devtools/skin/components-frame.css" />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/SmartTrace.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/NotificationBox.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/splitter/GridElementResizer.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/AppErrorBoundary.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/framework/components/ChromeDebugToolbar.css"
    />
    <!-- Console components -->
    <link
      rel="stylesheet"
      href="chrome://devtools/content/webconsole/components/App.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/webconsole/components/Input/EagerEvaluation.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/webconsole/components/Input/EvaluationNotification.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/webconsole/components/Input/EvaluationContextSelector.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/webconsole/components/Input/ReverseSearchInput.css"
    />
    <!-- CodeMirror CSS -->
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/sourceeditor/codemirror/lib/codemirror.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/sourceeditor/codemirror/addon/dialog/dialog.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/sourceeditor/codemirror/mozilla.css"
    />

    <!-- ObjectInspector/Reps styles -->
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/reps/reps.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/Tree.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/object-inspector/components/ObjectInspector.css"
    />
    <!-- Embedded Network Request detail styles -->
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/Accordion.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/MdnLink.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/tabs/Tabs.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/shared/components/tree/TreeView.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/variables.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/NetworkDetailsBar.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/StatusCode.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/RequestList.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/UrlPreview.css"
    />
    <link
      rel="stylesheet"
      href="chrome://devtools/content/netmonitor/src/assets/styles/HeadersPanel.css"
    />

    <script src="chrome://devtools/content/shared/theme-switching.js"></script>
  </head>
  <body class="theme-sidebar" role="application">
    <main
      id="app-wrapper"
      class="theme-body"
      role="document"
      aria-live="polite"
    ></main>
  </body>
</html>
