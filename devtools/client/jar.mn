# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

devtools.jar:
%   content devtools %content/
    content/shared/vendor/dagre-d3.js (shared/vendor/dagre-d3.js)
    content/shared/widgets/widgets.css (shared/widgets/widgets.css)
    content/shared/toolbarbutton.css (shared/toolbarbutton.css)
    content/shared/theme-switching.js (shared/theme-switching.js)
    content/shared/widgets/cubic-bezier.css (shared/widgets/cubic-bezier.css)
    content/shared/widgets/filter-widget.css (shared/widgets/filter-widget.css)
    content/shared/widgets/linear-widget.css (shared/widgets/linear-widget.css)
    content/shared/widgets/spectrum.css (shared/widgets/spectrum.css)
    content/shared/components/SmartTrace.css (shared/components/SmartTrace.css)
    content/shared/components/Tree.css (shared/components/Tree.css)
    content/shared/components/object-inspector/components/ObjectInspector.css (shared/components/object-inspector/components/ObjectInspector.css)
    content/shared/components/reps/reps.css (shared/components/reps/reps.css)
    content/shared/components/reps/images/input.svg (shared/components/reps/images/input.svg)
    content/shared/components/reps/images/jump-definition.svg (shared/components/reps/images/jump-definition.svg)
    content/shared/components/reps/images/open-a11y.svg (shared/components/reps/images/open-a11y.svg)
    content/shared/components/reps/images/open-inspector.svg (shared/components/reps/images/open-inspector.svg)
    content/shared/components/tabs/Tabs.css (shared/components/tabs/Tabs.css)
    content/shared/components/NotificationBox.css (shared/components/NotificationBox.css)
    content/shared/components/splitter/GridElementResizer.css (shared/components/splitter/GridElementResizer.css)
    content/shared/components/Accordion.css (shared/components/Accordion.css)
    content/shared/components/splitter/SplitBox.css (shared/components/splitter/SplitBox.css)
    content/shared/components/List.css (shared/components/List.css)
    content/shared/components/tree/TreeView.css (shared/components/tree/TreeView.css)
    content/shared/components/SearchModifiers.css (shared/components/SearchModifiers.css)
    content/shared/components/SidebarToggle.css (shared/components/SidebarToggle.css)
    content/shared/components/MdnLink.css (shared/components/MdnLink.css)
    content/shared/components/AppErrorBoundary.css (shared/components/AppErrorBoundary.css)
    skin/images/breadcrumbs-scrollbutton.svg (themes/images/breadcrumbs-scrollbutton.svg)
    skin/animation.css (themes/animation.css)
    skin/perf.css (themes/perf.css)
    skin/memory.css (themes/memory.css)
    skin/storage.css (themes/storage.css)
    skin/splitview.css (themes/splitview.css)
    skin/styleeditor.css (themes/styleeditor.css)
    skin/components-frame.css (themes/components-frame.css)
    skin/components-h-split-box.css (themes/components-h-split-box.css)
    skin/images/filter-small.svg (themes/images/filter-small.svg)
    skin/images/search.svg (themes/images/search.svg)
    skin/images/item-arrow-dark-rtl.svg (themes/images/item-arrow-dark-rtl.svg)
    skin/images/item-arrow-dark-ltr.svg (themes/images/item-arrow-dark-ltr.svg)
    skin/images/item-arrow-rtl.svg (themes/images/item-arrow-rtl.svg)
    skin/images/item-arrow-ltr.svg (themes/images/item-arrow-ltr.svg)
    skin/images/dropmarker.svg (themes/images/dropmarker.svg)
    skin/boxmodel.css (themes/boxmodel.css)
    skin/images/eye.svg (themes/images/eye.svg)
    skin/images/eye-closed.svg (themes/images/eye-closed.svg)
    skin/images/eye-opened.svg (themes/images/eye-opened.svg)
    skin/images/geometry-editor.svg (themes/images/geometry-editor.svg)
    skin/images/highlight-selector.svg (themes/images/highlight-selector.svg)
    skin/images/open-inspector.svg (themes/images/open-inspector.svg)
    skin/images/mdn.svg (themes/images/mdn.svg)
    skin/images/more.svg (themes/images/more.svg)
    skin/images/pause.svg (themes/images/pause.svg)
    skin/images/blocked.svg (themes/images/blocked.svg)
    skin/images/pencil-icon.svg (themes/images/pencil-icon.svg)
    skin/images/play.svg (themes/images/play.svg)
    skin/images/rewind.svg (themes/images/rewind.svg)
    skin/images/dock-bottom.svg (themes/images/dock-bottom.svg)
    skin/images/dock-side-left.svg (themes/images/dock-side-left.svg)
    skin/images/dock-side-right.svg (themes/images/dock-side-right.svg)
    skin/images/dock-undock.svg (themes/images/dock-undock.svg)
    skin/accessibility-color-contrast.css (themes/accessibility-color-contrast.css)
    skin/badge.css (themes/badge.css)
    skin/inspector.css (themes/inspector.css)
    skin/inspector-shared.css (themes/inspector-shared.css)
    skin/inspector-swatches.css (themes/inspector-swatches.css)
    skin/images/profiler-stopwatch.svg (themes/images/profiler-stopwatch.svg)
    skin/images/debugging-addons.svg (themes/images/debugging-addons.svg)
    skin/images/debugging-tabs.svg (themes/images/debugging-tabs.svg)
    skin/images/debugging-workers.svg (themes/images/debugging-workers.svg)
    skin/images/datastore.svg (themes/images/datastore.svg)
    skin/images/globe.svg (themes/images/globe.svg)
    skin/images/next.svg (themes/images/next.svg)
    skin/images/folder.svg (themes/images/folder.svg)
    skin/images/sad-face.svg (themes/images/sad-face.svg)
    skin/images/shape-swatch.svg (themes/images/shape-swatch.svg)
    skin/images/tool-webconsole.svg (themes/images/tool-webconsole.svg)
    skin/images/tool-debugger.svg (themes/images/tool-debugger.svg)
    skin/images/tool-inspector.svg (themes/images/tool-inspector.svg)
    skin/images/tool-styleeditor.svg (themes/images/tool-styleeditor.svg)
    skin/images/tool-storage.svg (themes/images/tool-storage.svg)
    skin/images/tool-profiler.svg (themes/images/tool-profiler.svg)
    skin/images/tool-network.svg (themes/images/tool-network.svg)
    skin/images/tool-memory.svg (themes/images/tool-memory.svg)
    skin/images/tool-dom.svg (themes/images/tool-dom.svg)
    skin/images/tool-accessibility.svg (themes/images/tool-accessibility.svg)
    skin/images/tool-application.svg (themes/images/tool-application.svg)
    skin/images/close.svg (themes/images/close.svg)
    skin/images/case-match.svg (themes/images/case-match.svg)
    skin/images/clear.svg (themes/images/clear.svg)
    skin/images/close-3-pane.svg (themes/images/close-3-pane.svg)
    skin/images/open-3-pane.svg (themes/images/open-3-pane.svg)
    skin/images/vview-delete.svg (themes/images/vview-delete.svg)
    skin/images/vview-edit.svg (themes/images/vview-edit.svg)
    skin/images/vview-lock.svg (themes/images/vview-lock.svg)
    skin/images/sort-ascending-arrow.svg (themes/images/sort-ascending-arrow.svg)
    skin/images/sort-descending-arrow.svg (themes/images/sort-descending-arrow.svg)
    skin/images/cubic-bezier-swatch.svg (themes/images/cubic-bezier-swatch.svg)
    skin/images/linear-easing-swatch.svg (themes/images/linear-easing-swatch.svg)
    skin/fonts.css (themes/fonts.css)
    skin/changes.css (themes/changes.css)
    skin/compatibility.css (themes/compatibility.css)
    skin/computed.css (themes/computed.css)
    skin/layout.css (themes/layout.css)
    skin/images/arrow-e.svg (themes/images/arrow-e.svg)
    skin/images/search-clear.svg (themes/images/search-clear.svg)
    skin/images/reload.svg (themes/images/reload.svg)
    skin/images/security-state-insecure.svg (themes/images/security-state-insecure.svg)
    skin/images/security-state-secure.svg (themes/images/security-state-secure.svg)
    skin/images/security-state-weak.svg (themes/images/security-state-weak.svg)
    skin/images/diff.svg (themes/images/diff.svg)
    skin/images/import.svg (themes/images/import.svg)
    skin/images/pane-collapse.svg (themes/images/pane-collapse.svg)
    skin/images/pane-expand.svg (themes/images/pane-expand.svg)
    skin/images/whatsnew.svg (themes/images/whatsnew.svg)
    skin/images/help.svg (themes/images/help.svg)
    skin/images/report.svg (themes/images/report.svg)
    skin/images/reveal.svg (themes/images/reveal.svg)
    skin/images/select-arrow.svg (themes/images/select-arrow.svg)
    skin/images/settings.svg (themes/images/settings.svg)
    skin/images/lock.svg (themes/images/lock.svg)
    skin/images/check.svg (themes/images/check.svg)
    skin/images/browsers/chrome.svg (themes/images/browsers/chrome.svg)
    skin/images/browsers/edge.svg (themes/images/browsers/edge.svg)
    skin/images/browsers/firefox.svg (themes/images/browsers/firefox.svg)
    skin/images/browsers/ie.svg (themes/images/browsers/ie.svg)
    skin/images/browsers/mobile.svg (themes/images/browsers/mobile.svg)
    skin/images/browsers/opera.svg (themes/images/browsers/opera.svg)
    skin/images/browsers/safari.svg (themes/images/browsers/safari.svg)
%   skin devtools classic/1.0 %skin/
    skin/common.css (themes/common.css)
    skin/variables.css (themes/variables.css)
    skin/splitters.css (themes/splitters.css)
    skin/devtools-browser.css (themes/devtools-browser.css)
    skin/dark-theme.css (themes/dark-theme.css)
    skin/light-theme.css (themes/light-theme.css)
    skin/toolbox.css (themes/toolbox.css)
    skin/tooltips.css (themes/tooltips.css)
    skin/toolbars.css (themes/toolbars.css)
    skin/images/accessibility.svg (themes/images/accessibility.svg)
    skin/images/add.svg (themes/images/add.svg)
    skin/images/alert.svg (themes/images/alert.svg)
    skin/images/alert-tiny.svg (themes/images/alert-tiny.svg)
    skin/images/arrow-dropdown-12.svg (themes/images/arrow-dropdown-12.svg)
    skin/images/error.svg (themes/images/error.svg)
    skin/images/error-tiny.svg (themes/images/error-tiny.svg)
    skin/images/info.svg (themes/images/info.svg)
    skin/images/info-tiny.svg (themes/images/info-tiny.svg)
    skin/images/arrow.svg (themes/images/arrow.svg)
    skin/images/arrow-big.svg (themes/images/arrow-big.svg)
    skin/images/arrowhead-left.svg (themes/images/arrowhead-left.svg)
    skin/images/arrowhead-right.svg (themes/images/arrowhead-right.svg)
    skin/images/arrowhead-down.svg (themes/images/arrowhead-down.svg)
    skin/images/arrowhead-up.svg (themes/images/arrowhead-up.svg)
    skin/images/breadcrumbs-divider.svg (themes/images/breadcrumbs-divider.svg)
    skin/images/checkbox.svg (themes/images/checkbox.svg)
    skin/images/filter-swatch.svg (themes/images/filter-swatch.svg)
    skin/images/application-debug.svg (themes/images/application-debug.svg)
    skin/images/application-manifest.svg (themes/images/application-manifest.svg)
    skin/images/fox-smiling.svg (themes/images/fox-smiling.svg)
    skin/images/grid.svg (themes/images/grid.svg)
    skin/images/angle-swatch.svg (themes/images/angle-swatch.svg)
    skin/images/flexbox-swatch.svg (themes/images/flexbox-swatch.svg)
    skin/images/pseudo-class.svg (themes/images/pseudo-class.svg)
    skin/images/copy.svg (themes/images/copy.svg)
    skin/images/animation-fast-track.svg (themes/images/animation-fast-track.svg)
    skin/breadcrumbs.css (themes/breadcrumbs.css)
    skin/chart.css (themes/chart.css)
    skin/widgets.css (themes/widgets.css)
    skin/rules.css (themes/rules.css)
    skin/images/command-always-on-top-window.svg (themes/images/command-always-on-top-window.svg)
    skin/images/command-screenshot.svg (themes/images/command-screenshot.svg)
    skin/images/command-responsivemode.svg (themes/images/command-responsivemode.svg)
    skin/images/command-pick-accessibility.svg (themes/images/command-pick-accessibility.svg)
    skin/images/command-frames.svg (themes/images/command-frames.svg)
    skin/images/command-console.svg (themes/images/command-console.svg)
    skin/images/command-eyedropper.svg (themes/images/command-eyedropper.svg)
    skin/images/command-rulers.svg (themes/images/command-rulers.svg)
    skin/images/command-measure.svg (themes/images/command-measure.svg)
    skin/images/command-noautohide.svg (themes/images/command-noautohide.svg)
    skin/images/command-chevron.svg (themes/images/command-chevron.svg)
    skin/images/command-accented.svg (themes/images/command-accented.svg)
    skin/images/command-bidi.svg (themes/images/command-bidi.svg)
    skin/images/rules-view-light-mode-simulation.svg (themes/images/rules-view-light-mode-simulation.svg)
    skin/images/rules-view-dark-mode-simulation.svg (themes/images/rules-view-dark-mode-simulation.svg)
    skin/images/rules-view-print-simulation.svg (themes/images/rules-view-print-simulation.svg)
    skin/markup.css (themes/markup.css)

    # Inspector
    content/inspector/markup/markup.xhtml (inspector/markup/markup.xhtml)
    content/inspector/components/InspectorTabPanel.css (inspector/components/InspectorTabPanel.css)
    content/styleeditor/index.xhtml (styleeditor/index.xhtml)
    content/inspector/index.xhtml (inspector/index.xhtml)
    content/responsive/toolbar.xhtml (responsive/toolbar.xhtml)
    content/responsive/index.css (responsive/index.css)
    content/responsive/responsive-browser.css (responsive/responsive-browser.css)
    content/responsive/images/grippers.svg (responsive/images/grippers.svg)
    content/responsive/images/rotate-viewport.svg (responsive/images/rotate-viewport.svg)
    content/responsive/images/touch-events.svg (responsive/images/touch-events.svg)

    # Storage
    content/storage/index.xhtml (storage/index.xhtml)

    # Source Editor
    content/shared/sourceeditor/codemirror/addon/dialog/dialog.css (shared/sourceeditor/codemirror/addon/dialog/dialog.css)
    content/shared/sourceeditor/codemirror/keymap/emacs.js (shared/sourceeditor/codemirror/keymap/emacs.js)
    content/shared/sourceeditor/codemirror/keymap/vim.js (shared/sourceeditor/codemirror/keymap/vim.js)
    content/shared/sourceeditor/codemirror/keymap/sublime.js (shared/sourceeditor/codemirror/keymap/sublime.js)
    content/shared/sourceeditor/codemirror/codemirror.bundle.js (shared/sourceeditor/codemirror/codemirror.bundle.js)
    content/shared/sourceeditor/codemirror/lib/codemirror.css (shared/sourceeditor/codemirror/lib/codemirror.css)
    content/shared/sourceeditor/codemirror/mozilla.css (shared/sourceeditor/codemirror/mozilla.css)
    content/shared/sourceeditor/codemirror/cmiframe.html (shared/sourceeditor/codemirror/cmiframe.html)

    # Debugger
    content/debugger/index.html (debugger/index.html)
    content/debugger/images/arrow-down.svg (debugger/images/arrow-down.svg)
    content/debugger/images/arrow-up.svg (debugger/images/arrow-up.svg)
    content/debugger/images/arrow.svg (debugger/images/arrow.svg)
    content/debugger/images/blackBox.svg (debugger/images/blackBox.svg)
    content/debugger/images/breadcrumbs-divider.svg (debugger/images/breadcrumbs-divider.svg)
    content/debugger/images/breakpoint.svg (debugger/images/breakpoint.svg)
    content/debugger/images/case-match.svg (debugger/images/case-match.svg)
    content/debugger/images/column-marker.svg (debugger/images/column-marker.svg)
    content/debugger/images/command-chevron.svg (debugger/images/command-chevron.svg)
    content/debugger/images/disable-pausing.svg (debugger/images/disable-pausing.svg)
    content/debugger/images/enable-pausing.svg (debugger/images/enable-pausing.svg)
    content/debugger/images/file-small.svg (debugger/images/file-small.svg)
    content/debugger/images/folder.svg (debugger/images/folder.svg)
    content/debugger/images/globe-small.svg (debugger/images/globe-small.svg)
    content/debugger/images/globe.svg (debugger/images/globe.svg)
    content/debugger/images/help.svg (debugger/images/help.svg)
    content/debugger/images/home.svg (debugger/images/home.svg)
    content/debugger/images/loader.svg (debugger/images/loader.svg)
    content/debugger/images/markup-breakpoint.svg (debugger/images/markup-breakpoint.svg)
    content/debugger/images/next-circle.svg (debugger/images/next-circle.svg)
    content/debugger/images/next.svg (debugger/images/next.svg)
    content/debugger/images/pane-collapse.svg (debugger/images/pane-collapse.svg)
    content/debugger/images/pane-expand.svg (debugger/images/pane-expand.svg)
    content/debugger/images/pause.svg (debugger/images/pause.svg)
    content/debugger/images/prettyPrint.svg (debugger/images/prettyPrint.svg)
    content/debugger/images/regex-match.svg (debugger/images/regex-match.svg)
    content/debugger/images/search.svg (debugger/images/search.svg)
    content/debugger/images/sourcemap.svg (debugger/images/sourcemap.svg)
    content/debugger/images/sourcemap-active.svg (debugger/images/sourcemap-active.svg)
    content/debugger/images/sourcemap-disabled.svg (debugger/images/sourcemap-disabled.svg)
    content/debugger/images/stepIn.svg (debugger/images/stepIn.svg)
    content/debugger/images/stepOut.svg (debugger/images/stepOut.svg)
    content/debugger/images/tab.svg (debugger/images/tab.svg)
    content/debugger/images/trace.svg (debugger/images/trace.svg)
    content/debugger/images/webconsole-logpoint.svg (debugger/images/webconsole-logpoint.svg)
    content/debugger/images/whole-word-match.svg (debugger/images/whole-word-match.svg)
    content/debugger/images/window.svg (debugger/images/window.svg)
    content/debugger/images/worker.svg (debugger/images/worker.svg)
    content/debugger/images/sources/aframe.svg (debugger/images/sources/aframe.svg)
    content/debugger/images/sources/angular.svg (debugger/images/sources/angular.svg)
    content/debugger/images/sources/babel.svg (debugger/images/sources/babel.svg)
    content/debugger/images/sources/backbone.svg (debugger/images/sources/backbone.svg)
    content/debugger/images/sources/choo.svg (debugger/images/sources/choo.svg)
    content/debugger/images/sources/coffeescript.svg (debugger/images/sources/coffeescript.svg)
    content/debugger/images/sources/dojo.svg (debugger/images/sources/dojo.svg)
    content/debugger/images/sources/ember.svg (debugger/images/sources/ember.svg)
    content/debugger/images/sources/express.svg (debugger/images/sources/express.svg)
    content/debugger/images/sources/extension.svg (debugger/images/sources/extension.svg)
    content/debugger/images/sources/immutable.svg (debugger/images/sources/immutable.svg)
    content/debugger/images/sources/javascript.svg (debugger/images/sources/javascript.svg)
    content/debugger/images/sources/jquery.svg (debugger/images/sources/jquery.svg)
    content/debugger/images/sources/lodash.svg (debugger/images/sources/lodash.svg)
    content/debugger/images/sources/marko.svg (debugger/images/sources/marko.svg)
    content/debugger/images/sources/mobx.svg (debugger/images/sources/mobx.svg)
    content/debugger/images/sources/nextjs.svg (debugger/images/sources/nextjs.svg)
    content/debugger/images/sources/node.svg (debugger/images/sources/node.svg)
    content/debugger/images/sources/nuxtjs.svg (debugger/images/sources/nuxtjs.svg)
    content/debugger/images/sources/preact.svg (debugger/images/sources/preact.svg)
    content/debugger/images/sources/pug.svg (debugger/images/sources/pug.svg)
    content/debugger/images/sources/react.svg (debugger/images/sources/react.svg)
    content/debugger/images/sources/redux.svg (debugger/images/sources/redux.svg)
    content/debugger/images/sources/rxjs.svg (debugger/images/sources/rxjs.svg)
    content/debugger/images/sources/sencha-extjs.svg (debugger/images/sources/sencha-extjs.svg)
    content/debugger/images/sources/typescript.svg (debugger/images/sources/typescript.svg)
    content/debugger/images/sources/underscore.svg (debugger/images/sources/underscore.svg)
    content/debugger/images/sources/vuejs.svg (debugger/images/sources/vuejs.svg)
    content/debugger/images/sources/webpack.svg (debugger/images/sources/webpack.svg)
    content/debugger/src/debugger.css (debugger/src/debugger.css)
    content/debugger/src/components/variables.css (debugger/src/components/variables.css)
    content/debugger/src/components/App.css (debugger/src/components/App.css)
    content/debugger/src/components/QuickOpenModal.css (debugger/src/components/QuickOpenModal.css)
    content/debugger/src/components/ShortcutsModal.css (debugger/src/components/ShortcutsModal.css)
    content/debugger/src/components/WelcomeBox.css (debugger/src/components/WelcomeBox.css)
    content/debugger/src/components/shared/AccessibleImage.css (debugger/src/components/shared/AccessibleImage.css)
    content/debugger/src/components/shared/Accordion.css (debugger/src/components/shared/Accordion.css)
    content/debugger/src/components/shared/Badge.css (debugger/src/components/shared/Badge.css)
    content/debugger/src/components/shared/BracketArrow.css (debugger/src/components/shared/BracketArrow.css)
    content/debugger/src/components/shared/Button/styles/CloseButton.css (debugger/src/components/shared/Button/styles/CloseButton.css)
    content/debugger/src/components/shared/Button/styles/CommandBarButton.css (debugger/src/components/shared/Button/styles/CommandBarButton.css)
    content/debugger/src/components/shared/Button/styles/PaneToggleButton.css (debugger/src/components/shared/Button/styles/PaneToggleButton.css)
    content/debugger/src/components/shared/Dropdown.css (debugger/src/components/shared/Dropdown.css)
    content/debugger/src/components/shared/menu.css (debugger/src/components/shared/menu.css)
    content/debugger/src/components/shared/Modal.css (debugger/src/components/shared/Modal.css)
    content/debugger/src/components/shared/Popover.css (debugger/src/components/shared/Popover.css)
    content/debugger/src/components/shared/PreviewFunction.css (debugger/src/components/shared/PreviewFunction.css)
    content/debugger/src/components/shared/ResultList.css (debugger/src/components/shared/ResultList.css)
    content/debugger/src/components/shared/SearchInput.css (debugger/src/components/shared/SearchInput.css)
    content/debugger/src/components/shared/SourceIcon.css (debugger/src/components/shared/SourceIcon.css)
    content/debugger/src/components/Editor/Breakpoints.css (debugger/src/components/Editor/Breakpoints.css)
    content/debugger/src/components/Editor/ConditionalPanel.css (debugger/src/components/Editor/ConditionalPanel.css)
    content/debugger/src/components/Editor/Editor.css (debugger/src/components/Editor/Editor.css)
    content/debugger/src/components/Editor/Footer.css (debugger/src/components/Editor/Footer.css)
    content/debugger/src/components/Editor/InlinePreview.css (debugger/src/components/Editor/InlinePreview.css)
    content/debugger/src/components/Editor/Preview/Popup.css (debugger/src/components/Editor/Preview/Popup.css)
    content/debugger/src/components/Editor/SearchInFileBar.css (debugger/src/components/Editor/SearchInFileBar.css)
    content/debugger/src/components/Editor/Tabs.css (debugger/src/components/Editor/Tabs.css)
    content/debugger/src/components/PrimaryPanes/Outline.css (debugger/src/components/PrimaryPanes/Outline.css)
    content/debugger/src/components/PrimaryPanes/OutlineFilter.css (debugger/src/components/PrimaryPanes/OutlineFilter.css)
    content/debugger/src/components/PrimaryPanes/ProjectSearch.css (debugger/src/components/PrimaryPanes/ProjectSearch.css)
    content/debugger/src/components/PrimaryPanes/Sources.css (debugger/src/components/PrimaryPanes/Sources.css)
    content/debugger/src/components/PrimaryPanes/Tracer.css (debugger/src/components/PrimaryPanes/Tracer.css)
    content/debugger/src/components/SecondaryPanes/Breakpoints/Breakpoints.css (debugger/src/components/SecondaryPanes/Breakpoints/Breakpoints.css)
    content/debugger/src/components/SecondaryPanes/CommandBar.css (debugger/src/components/SecondaryPanes/CommandBar.css)
    content/debugger/src/components/SecondaryPanes/EventListeners.css (debugger/src/components/SecondaryPanes/EventListeners.css)
    content/debugger/src/components/SecondaryPanes/DOMMutationBreakpoints.css (debugger/src/components/SecondaryPanes/DOMMutationBreakpoints.css)
    content/debugger/src/components/SecondaryPanes/Expressions.css (debugger/src/components/SecondaryPanes/Expressions.css)
    content/debugger/src/components/SecondaryPanes/Frames/Frames.css (debugger/src/components/SecondaryPanes/Frames/Frames.css)
    content/debugger/src/components/SecondaryPanes/Frames/Group.css (debugger/src/components/SecondaryPanes/Frames/Group.css)
    content/debugger/src/components/SecondaryPanes/Scopes.css (debugger/src/components/SecondaryPanes/Scopes.css)
    content/debugger/src/components/SecondaryPanes/SecondaryPanes.css (debugger/src/components/SecondaryPanes/SecondaryPanes.css)
    content/debugger/src/components/SecondaryPanes/WhyPaused.css (debugger/src/components/SecondaryPanes/WhyPaused.css)
    content/debugger/src/components/SecondaryPanes/Threads.css (debugger/src/components/SecondaryPanes/Threads.css)
    content/debugger/src/components/SecondaryPanes/XHRBreakpoints.css (debugger/src/components/SecondaryPanes/XHRBreakpoints.css)
    content/debugger/src/utils/editor/source-editor.css (debugger/src/utils/editor/source-editor.css)

    # Perfomance
    content/performance-new/panel/index.xhtml (performance-new/panel/index.xhtml)

    # Memory
    content/memory/index.xhtml (memory/index.xhtml)

    # Toolbox
    content/framework/toolbox-window.xhtml (framework/toolbox-window.xhtml)
    content/framework/toolbox-options.html (framework/toolbox-options.html)
    content/framework/toolbox.xhtml (framework/toolbox.xhtml)
    content/framework/toolbox-init.js (framework/toolbox-init.js)
    content/framework/options-panel.css (framework/options-panel.css)
    content/framework/browser-toolbox/window.html (framework/browser-toolbox/window.html)
    content/framework/browser-toolbox/window.css (framework/browser-toolbox/window.css)
    content/framework/browser-toolbox/window.js (framework/browser-toolbox/window.js)
    content/framework/components/DebugTargetErrorPage.css (framework/components/DebugTargetErrorPage.css)
    content/framework/components/ChromeDebugToolbar.css (framework/components/ChromeDebugToolbar.css)

    # DOM
    content/dom/index.html (dom/index.html)
    content/dom/main.js (dom/main.js)
    content/dom/content/dom-view.css (dom/content/dom-view.css)

    # Accessibility
    content/accessibility/index.html (accessibility/index.html)
    content/accessibility/main.js (accessibility/main.js)
    content/accessibility/accessibility.css (accessibility/accessibility.css)

    # about:debugging
    skin/images/aboutdebugging-connect-icon.svg (themes/images/aboutdebugging-connect-icon.svg)
    skin/images/aboutdebugging-error.svg (themes/images/aboutdebugging-error.svg)
    skin/images/aboutdebugging-fenix-nightly.svg (themes/images/aboutdebugging-fenix-nightly.svg)
    skin/images/aboutdebugging-fenix.svg (themes/images/aboutdebugging-fenix.svg)
    skin/images/aboutdebugging-firefox-aurora.svg (themes/images/aboutdebugging-firefox-aurora.svg)
    skin/images/aboutdebugging-firefox-beta.svg (themes/images/aboutdebugging-firefox-beta.svg)
    skin/images/aboutdebugging-firefox-logo.svg (themes/images/aboutdebugging-firefox-logo.svg)
    skin/images/aboutdebugging-firefox-nightly.svg (themes/images/aboutdebugging-firefox-nightly.svg)
    skin/images/aboutdebugging-firefox-release.svg (themes/images/aboutdebugging-firefox-release.svg)
    skin/images/aboutdebugging-globe-icon.svg (themes/images/aboutdebugging-globe-icon.svg)
    skin/images/aboutdebugging-information.svg (themes/images/aboutdebugging-information.svg)
    skin/images/aboutdebugging-process-icon.svg (themes/images/aboutdebugging-process-icon.svg)
    skin/images/aboutdebugging-usb-icon.svg (themes/images/aboutdebugging-usb-icon.svg)
    content/aboutdebugging/index.html (aboutdebugging/index.html)
    content/aboutdebugging/aboutdebugging.css (aboutdebugging/aboutdebugging.css)
    content/aboutdebugging/src/base.css (aboutdebugging/src/base.css)
    content/aboutdebugging/src/components/App.css (aboutdebugging/src/components/App.css)
    content/aboutdebugging/src/components/ProfilerDialog.css (aboutdebugging/src/components/ProfilerDialog.css)
    content/aboutdebugging/src/components/RuntimeActions.css (aboutdebugging/src/components/RuntimeActions.css)
    content/aboutdebugging/src/components/RuntimeInfo.css (aboutdebugging/src/components/RuntimeInfo.css)
    content/aboutdebugging/src/components/connect/ConnectPage.css (aboutdebugging/src/components/connect/ConnectPage.css)
    content/aboutdebugging/src/components/connect/ConnectSection.css (aboutdebugging/src/components/connect/ConnectSection.css)
    content/aboutdebugging/src/components/connect/ConnectSteps.css (aboutdebugging/src/components/connect/ConnectSteps.css)
    content/aboutdebugging/src/components/connect/NetworkLocationsForm.css (aboutdebugging/src/components/connect/NetworkLocationsForm.css)
    content/aboutdebugging/src/components/connect/NetworkLocationsList.css (aboutdebugging/src/components/connect/NetworkLocationsList.css)
    content/aboutdebugging/src/components/debugtarget/DebugTargetItem.css (aboutdebugging/src/components/debugtarget/DebugTargetItem.css)
    content/aboutdebugging/src/components/debugtarget/DebugTargetList.css (aboutdebugging/src/components/debugtarget/DebugTargetList.css)
    content/aboutdebugging/src/components/debugtarget/DebugTargetPane.css (aboutdebugging/src/components/debugtarget/DebugTargetPane.css)
    content/aboutdebugging/src/components/debugtarget/ExtensionDetail.css (aboutdebugging/src/components/debugtarget/ExtensionDetail.css)
    content/aboutdebugging/src/components/debugtarget/FieldPair.css (aboutdebugging/src/components/debugtarget/FieldPair.css)
    content/aboutdebugging/src/components/debugtarget/ServiceWorkerAction.css (aboutdebugging/src/components/debugtarget/ServiceWorkerAction.css)
    content/aboutdebugging/src/components/debugtarget/TemporaryExtensionInstallSection.css (aboutdebugging/src/components/debugtarget/TemporaryExtensionInstallSection.css)
    content/aboutdebugging/src/components/shared/IconLabel.css (aboutdebugging/src/components/shared/IconLabel.css)
    content/aboutdebugging/src/components/shared/Message.css (aboutdebugging/src/components/shared/Message.css)
    content/aboutdebugging/src/components/sidebar/Sidebar.css (aboutdebugging/src/components/sidebar/Sidebar.css)
    content/aboutdebugging/src/components/sidebar/SidebarFixedItem.css (aboutdebugging/src/components/sidebar/SidebarFixedItem.css)
    content/aboutdebugging/src/components/sidebar/SidebarItem.css (aboutdebugging/src/components/sidebar/SidebarItem.css)
    content/aboutdebugging/src/components/sidebar/SidebarRuntimeItem.css (aboutdebugging/src/components/sidebar/SidebarRuntimeItem.css)

    # Webconsole
    skin/webconsole.css (themes/webconsole.css)
    skin/images/webconsole/editor.svg (themes/images/webconsole/editor.svg)
    skin/images/webconsole/input.svg (themes/images/webconsole/input.svg)
    skin/images/webconsole/navigation.svg (themes/images/webconsole/navigation.svg)
    skin/images/webconsole/reverse-search.svg (themes/images/webconsole/reverse-search.svg)
    skin/images/webconsole/return.svg (themes/images/webconsole/return.svg)
    skin/images/webconsole/run.svg (themes/images/webconsole/run.svg)
    content/webconsole/index.html (webconsole/index.html)
    content/webconsole/components/Input/EagerEvaluation.css (webconsole/components/Input/EagerEvaluation.css)
    content/webconsole/components/Input/EvaluationNotification.css (webconsole/components/Input/EvaluationNotification.css)
    content/webconsole/components/Input/EvaluationContextSelector.css (webconsole/components/Input/EvaluationContextSelector.css)
    content/webconsole/components/Input/ReverseSearchInput.css (webconsole/components/Input/ReverseSearchInput.css)
    content/webconsole/components/App.css (webconsole/components/App.css)

    # Netmonitor
    content/netmonitor/src/assets/styles/netmonitor.css (netmonitor/src/assets/styles/netmonitor.css)
    content/netmonitor/src/assets/styles/NetworkActionBar.css (netmonitor/src/assets/styles/NetworkActionBar.css)
    content/netmonitor/src/assets/styles/RequestBlockingPanel.css (netmonitor/src/assets/styles/RequestBlockingPanel.css)
    content/netmonitor/src/assets/styles/NetworkDetailsBar.css (netmonitor/src/assets/styles/NetworkDetailsBar.css)
    content/netmonitor/src/assets/styles/CustomRequestPanel.css (netmonitor/src/assets/styles/CustomRequestPanel.css)
    content/netmonitor/src/assets/styles/HTTPCustomRequestPanel.css (netmonitor/src/assets/styles/HTTPCustomRequestPanel.css)
    content/netmonitor/src/assets/styles/RequestList.css (netmonitor/src/assets/styles/RequestList.css)
    content/netmonitor/src/assets/styles/StatisticsPanel.css (netmonitor/src/assets/styles/StatisticsPanel.css)
    content/netmonitor/src/assets/styles/StatusBar.css (netmonitor/src/assets/styles/StatusBar.css)
    content/netmonitor/src/assets/styles/Toolbar.css (netmonitor/src/assets/styles/Toolbar.css)
    content/netmonitor/src/assets/styles/variables.css (netmonitor/src/assets/styles/variables.css)
    content/netmonitor/src/assets/icons/play.svg (netmonitor/src/assets/icons/play.svg)
    content/netmonitor/src/assets/icons/arrow-up.svg (netmonitor/src/assets/icons/arrow-up.svg)
    content/netmonitor/src/assets/icons/shield.svg (netmonitor/src/assets/icons/shield.svg)
    content/netmonitor/src/assets/icons/turtle.svg (netmonitor/src/assets/icons/turtle.svg)
    content/netmonitor/index.html (netmonitor/index.html)
    content/netmonitor/src/assets/styles/StatusCode.css (netmonitor/src/assets/styles/StatusCode.css)
    content/netmonitor/src/assets/styles/messages.css (netmonitor/src/assets/styles/messages.css)
    content/netmonitor/src/assets/styles/search.css (netmonitor/src/assets/styles/search.css)
    content/netmonitor/src/assets/styles/UrlPreview.css (netmonitor/src/assets/styles/UrlPreview.css)
    content/netmonitor/src/assets/styles/HeadersPanel.css (netmonitor/src/assets/styles/HeadersPanel.css)

    # Application panel
    content/application/index.html (application/index.html)
    content/application/application.css (application/application.css)
    content/application/src/base.css (application/src/base.css)
    content/application/src/components/App.css (application/src/components/App.css)
    content/application/src/components/manifest/ManifestColorItem.css (application/src/components/manifest/ManifestColorItem.css)
    content/application/src/components/manifest/ManifestIconItem.css (application/src/components/manifest/ManifestIconItem.css)
    content/application/src/components/manifest/ManifestIssue.css (application/src/components/manifest/ManifestIssue.css)
    content/application/src/components/manifest/ManifestIssueList.css (application/src/components/manifest/ManifestIssueList.css)
    content/application/src/components/manifest/ManifestItem.css (application/src/components/manifest/ManifestItem.css)
    content/application/src/components/manifest/ManifestJsonLink.css (application/src/components/manifest/ManifestJsonLink.css)
    content/application/src/components/manifest/ManifestLoader.css (application/src/components/manifest/ManifestLoader.css)
    content/application/src/components/manifest/ManifestSection.css (application/src/components/manifest/ManifestSection.css)
    content/application/src/components/manifest/ManifestUrlItem.css (application/src/components/manifest/ManifestUrlItem.css)
    content/application/src/components/routing/PageSwitcher.css (application/src/components/routing/PageSwitcher.css)
    content/application/src/components/routing/Sidebar.css (application/src/components/routing/Sidebar.css)
    content/application/src/components/routing/SidebarItem.css (application/src/components/routing/SidebarItem.css)
    content/application/src/components/service-workers/Registration.css (application/src/components/service-workers/Registration.css)
    content/application/src/components/service-workers/RegistrationList.css (application/src/components/service-workers/RegistrationList.css)
    content/application/src/components/service-workers/Worker.css (application/src/components/service-workers/Worker.css)
    content/application/src/components/ui/UIButton.css (application/src/components/ui/UIButton.css)

    # about:profiling
    skin/aboutprofiling.css (themes/aboutprofiling.css)
    content/performance-new/aboutprofiling/initializer.js (performance-new/aboutprofiling/initializer.js)
    content/performance-new/aboutprofiling/index.xhtml (performance-new/aboutprofiling/index.xhtml)
