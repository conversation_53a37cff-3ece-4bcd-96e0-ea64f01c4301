/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

 /*
* Global styles
*/
@import "chrome://devtools/content/application/src/base.css";


/*
* Components
*/
@import "chrome://devtools/content/application/src/components/App.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestColorItem.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestIconItem.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestIssue.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestIssueList.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestItem.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestJsonLink.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestLoader.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestSection.css";
@import "chrome://devtools/content/application/src/components/manifest/ManifestUrlItem.css";
@import "chrome://devtools/content/application/src/components/routing/PageSwitcher.css";
@import "chrome://devtools/content/application/src/components/routing/Sidebar.css";
@import "chrome://devtools/content/application/src/components/routing/SidebarItem.css";
@import "chrome://devtools/content/application/src/components/service-workers/Registration.css";
@import "chrome://devtools/content/application/src/components/service-workers/RegistrationList.css";
@import "chrome://devtools/content/application/src/components/service-workers/Worker.css";
@import "chrome://devtools/content/application/src/components/ui/UIButton.css";

html,
body,
#mount {
  height: 100vh;
}
