/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

.manifest-item__color {
  /* NOTE: platform converts any color format that is in the manifest to
           hexadecimal, so we can uppercase */
  text-transform: uppercase;
  direction: ltr; /* force LTR so the # stays at the beginning of the hex number */
  display: inline-block;
}

.manifest-item__color::before {
  display: inline-block;
  content: '';
  background-color: #fff;
  background-image: linear-gradient(var(--color-value), var(--color-value)), /* injected via React */
    linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc),
    linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc);
  background-size: 6px 6px;
  background-position: 0 0, 3px 3px;
  border-radius: 50%;
  width: calc(var(--base-unit) * 3);
  height: calc(var(--base-unit) * 3);
  margin-block-start: calc(var(--base-unit) * -0.5);
  margin-inline-end: var(--base-unit);
  box-shadow: 0 0 0 1px var(--theme-splitter-color);
  vertical-align: middle;
}
