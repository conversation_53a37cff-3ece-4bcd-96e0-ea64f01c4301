/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */


/*
 * Sidebar list items
 */

.sidebar-item {
  display: grid;
  grid-template-columns: auto 1fr;
  grid-gap: var(--base-unit);
  padding: calc(var(--base-unit)) calc(var(--base-unit) * 6);
  user-select: none;
  cursor: pointer;
}

.sidebar-item--selected {
	background-color: var(--theme-selection-background);
	color: var(--theme-selection-color);
}

.sidebar-item:not(.sidebar-item--selected):hover {
	background-color: var(--highlight-color);
}

.sidebar-item__icon {
	height: calc(var(--base-unit) * 4);
	width: calc(var(--base-unit) * 4);
	-moz-context-properties: fill;
	fill: currentColor;
}
