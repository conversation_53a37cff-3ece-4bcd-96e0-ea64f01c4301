/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

 /*
 * The current layout of a service worker item is
 *
 *  +------------+------------------------------+
 *  |     Worker | script_name                  |
 *  |     Icon   |------------------------------|
 *  |            | status    start_button       |
 *  +------------+------------------------------+
 */

.worker {
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-areas: "icon  source"
                       "icon  misc";
  column-gap: calc(var(--base-unit) * 2);
  row-gap: var(--base-unit);

  line-height: calc(var(--base-unit) * 4);
  font-size: var(--body-10-font-size);
}

.worker__icon {
  grid-area: icon;
}

.worker__icon-image {
  width: calc(var(--base-unit) * 4);
  height: calc(var(--base-unit) * 4);
}

.worker__source {
  grid-area: source;
  user-select: text;
}

.worker__misc {
  grid-area: misc;
}

.worker__status {
  text-transform: capitalize;
  --status-bg-color: transparent;
  --status-border-color: transparent;
}

.worker__status::before {
  content: "";
  margin-inline-end: var(--base-unit);
  width: calc(var(--base-unit) * 2);
  height: calc(var(--base-unit) * 2);
  display: inline-block;
  background: var(--status-bg-color);
  border: 1px solid var(--status-border-color);
  border-radius: 100%;
}

.worker__status--active {
  --status-bg-color: var(--green-60);
  --status-border-color: var(--green-60);
}

.worker__status--waiting {
  --status-bg-color: var(--theme-text-color-alt);
  --status-border-color: var(--theme-text-color-alt);
}

.worker__status--installing, .worker__status--default {
  --status-bg-color: transparent;
  --status-border-color: var(--theme-text-color-alt);
}
