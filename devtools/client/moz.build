# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

include("../templates.mozbuild")

DIRS += [
    "aboutdebugging",
    "accessibility",
    "application",
    "debugger",
    "dom",
    "framework",
    "fronts",
    "inspector",
    "jsonview",
    "locales",
    "memory",
    "netmonitor",
    "performance-new",
    "preferences",
    "responsive",
    "shared",
    "storage",
    "styleeditor",
    "themes",
    "webconsole",
]

JAR_MANIFESTS += ["jar.mn"]

DevToolsModules(
    "constants.js",
    "definitions.js",
    "devtools-client.js",
    "menus.js",
)

if not CONFIG["MOZILLA_OFFICIAL"]:
    DevToolsModules(
        "devtools-experimental-prefs.js",
    )

with Files("**"):
    BUG_COMPONENT = ("DevTools", "General")
