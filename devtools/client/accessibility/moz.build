# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

MOCHITEST_CHROME_MANIFESTS += ["test/chrome/chrome.toml"]
BROWSER_CHROME_MANIFESTS += ["test/browser/browser.toml"]

DIRS += ["actions", "components", "reducers", "utils"]

DevToolsModules(
    "accessibility-proxy.js",
    "accessibility-view.js",
    "constants.js",
    "panel.js",
    "picker.js",
    "provider.js",
)

with Files("**"):
    BUG_COMPONENT = ("DevTools", "Accessibility Tools")
