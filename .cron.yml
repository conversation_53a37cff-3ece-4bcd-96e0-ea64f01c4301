# Definitions for jobs that run periodically.  For details on the format, see
# ``https://github.com/mozilla-releng/fxci-config/blob/main/build-decision/src/build_decision/cron/schema.yml``.
# For documentation, see `taskcluster/docs/cron.rst`.
---

jobs:
    - name: daily-releases
      job:
          type: decision-task
          treeherder-symbol: RPd
          target-tasks-method: daily_releases
      run-on-projects:
          - mozilla-beta
      when:
          by-project:
              # No default
              mozilla-beta:
                  - {weekday: 'Monday', hour: 13, minute: 0}
                  - {weekday: 'Wednesday', hour: 13, minute: 0}
                  - {weekday: 'Friday', hour: 13, minute: 0}

    - name: nightly-all
      job:
          type: decision-task
          treeherder-symbol: N
          target-tasks-method: nightly_all
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          by-project:
              # No default
              mozilla-central:
                  - {hour: 10, minute: 0}
                  - {hour: 22, minute: 0}

    - name: merge-beta-to-release-dry-run
      job:
          type: trigger-action
          action-name: merge-automation
          include-cron-input: false
          extra-input:
              force-dry-run: true
              behavior: beta-to-release
              push: true
      run-on-projects:
          - mozilla-beta
      when:
          - {weekday: "Friday", hour: 7, minute: 0}

    - name: merge-central-to-beta-dry-run
      job:
          type: trigger-action
          action-name: merge-automation
          include-cron-input: false
          extra-input:
              force-dry-run: true
              behavior: central-to-beta
              push: true
      run-on-projects:
          - mozilla-central
      when:
          - {weekday: "Friday", hour: 7, minute: 0}

    - name: nightly-desktop
      job:
          type: decision-task
          treeherder-symbol: Nd
          target-tasks-method: nightly_desktop
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-desktop-linux
      job:
          type: decision-task
          treeherder-symbol: Nd-Ln
          target-tasks-method: nightly_linux
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-desktop-osx
      job:
          type: decision-task
          treeherder-symbol: Nd-OSX
          target-tasks-method: nightly_macosx
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-desktop-win32
      job:
          type: decision-task
          treeherder-symbol: Nd-win32
          target-tasks-method: nightly_win32
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-desktop-win64
      job:
          type: decision-task
          treeherder-symbol: Nd-win64
          target-tasks-method: nightly_win64
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-desktop-win64-aarch64
      job:
          type: decision-task
          treeherder-symbol: Nd-win64-aarch64
          target-tasks-method: nightly_win64_aarch64
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when: []  # never (hook only)

    - name: nightly-android
      job:
          type: decision-task
          treeherder-symbol: Na
          target-tasks-method: nightly-android
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
          - oak
      when: []  # hook only

    - name: general-perf-testing
      job:
          type: decision-task
          treeherder-symbol: gpt
          target-tasks-method: general_perf_testing
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          - {weekday: 'Monday', hour: 10, minute: 30}
          - {weekday: 'Wednesday', hour: 10, minute: 30}
          - {weekday: 'Friday', hour: 10, minute: 30}

    - name: geckoview-perftest
      job:
          type: decision-task
          treeherder-symbol: gve-perftests
          target-tasks-method: geckoview-perftest
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          - {weekday: 'Monday', hour: 10, minute: 30}
          - {weekday: 'Thursday', hour: 10, minute: 30}

    - name: custom-car-perf-testing
      job:
          type: decision-task
          treeherder-symbol: ccar
          target-tasks-method: custom-car_perf_testing
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          by-project:
              mozilla-central: [{hour: 01, minute: 0}]

    # For more context on this job, see:
    # https://github.com/mozsearch/mozsearch-mozilla#how-searchfoxorg-stays-up-to-date
    # Note that searchfox now runs on-push for mozilla-central, but continues
    # to use cron jobs for all other branches
    - name: searchfox-index
      job:
          type: decision-task
          treeherder-symbol: Searchfox
          target-tasks-method: searchfox_index
      run-on-projects:
          - mozilla-beta
          - mozilla-release
          - mozilla-esr128
          - elm
          - cypress
      # For all non m-c jobs we just run once daily matching the 10 UTC
      # nightly which is designed to align with searchfox's AWS cron
      # jobs (for legacy reasons) rather than trying to align with
      # specific builds.  (Ex: mozilla-beta has a "daily-releases" job
      # that currently runs 3 times a week.)
      when:
          - {hour: 10, minute: 0}

    - name: linux64-clang-trunk-perf
      job:
          type: decision-task
          treeherder-symbol: linux64-clang-trunk-perf
          target-tasks-method: linux64_clang_trunk_perf
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 2, minute: 0}

    - name: updatebot-cron-job
      job:
          type: decision-task
          treeherder-symbol: updatebot
          target-tasks-method: updatebot_cron
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 0, minute: 0}
          - {hour: 6, minute: 0}
          - {hour: 12, minute: 0}
          - {hour: 18, minute: 0}

    - name: customv8-update
      job:
          type: decision-task
          treeherder-symbol: customv8
          target-tasks-method: customv8_update
          optimize-target-tasks: false
      run-on-projects:
          - mozilla-central
      when:
          by-project:
              mozilla-central: [{hour: 10, minute: 30}]

    - name: bouncer-check
      job:
          type: decision-task
          treeherder-symbol: Rel
          target-tasks-method: cron_bouncer_check
      run-on-projects:
          - mozilla-central
          - mozilla-beta
          - mozilla-release
          - mozilla-esr128
      when:
          by-project:
              # No default branch
              mozilla-central:
                  - {hour: 7, minute: 0}
                  - {hour: 19, minute: 0}
              mozilla-beta:
                  - {hour: 7, minute: 0}
                  - {hour: 19, minute: 0}
              mozilla-release:
                  - {hour: 7, minute: 0}
                  - {hour: 19, minute: 0}
              mozilla-esr128:
                  - {hour: 7, minute: 0}
                  - {hour: 19, minute: 0}

    - name: periodic-update
      job:
          type: decision-task
          treeherder-symbol: Nfile
          target-tasks-method: file_update
      run-on-projects:
          - mozilla-central
          - mozilla-beta
          - mozilla-release
          - mozilla-esr128
      when:
          - {weekday: 'Monday', hour: 8, minute: 0}
          - {weekday: 'Thursday', hour: 8, minute: 0}

    - name: daily-beta-perf
      job:
          type: decision-task
          treeherder-symbol: d-perf
          target-tasks-method: daily_beta_perf
          include-push-tasks: true
      run-on-projects:
          - mozilla-beta
      when:
          - {hour: 12, minute: 0}

    - name: weekly-release-perf
      job:
          type: decision-task
          treeherder-symbol: w-perf
          target-tasks-method: weekly_release_perf
          include-push-tasks: true
      run-on-projects:
          - mozilla-release
      when:
          - {weekday: 'Friday', hour: 12, minute: 0}

    - name: raptor-tp6m
      job:
          type: decision-task
          treeherder-symbol: tp6m
          target-tasks-method: raptor_tp6m
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          - {weekday: 'Monday', hour: 3, minute: 0}
          - {weekday: 'Tuesday', hour: 3, minute: 0}
          - {weekday: 'Wednesday', hour: 3, minute: 0}
          - {weekday: 'Thursday', hour: 3, minute: 0}
          - {weekday: 'Friday', hour: 3, minute: 0}
          - {weekday: 'Saturday', hour: 3, minute: 0}
          - {weekday: 'Sunday', hour: 3, minute: 0}

    - name: backfill-all-browsertime
      job:
          type: decision-task
          treeherder-symbol: baB
          target-tasks-method: backfill_all_browsertime
          include-push-tasks: true
      run-on-projects:
          - autoland
      when:
          - {hour: 5, minute: 0}

    - name: condprof
      job:
          type: decision-task
          treeherder-symbol: condprof
          target-tasks-method: condprof
          include-push-tasks: true
          optimize-target-tasks: false
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 16, minute: 00}

    - name: l10n-bumper
      job:
          type: decision-task
          treeherder-symbol: l10n-bump
          target-tasks-method: l10n_bump
      run-on-projects:
          - autoland
          - mozilla-beta
      when:
          by-project:
              autoland: [{hour: 6, minute: 45}, {hour: 18, minute: 45}]
              # 4h before launch of `daily-releases`
              mozilla-beta: [{hour: 9, minute: 00}]
              # No default

    - name: system-symbols
      job:
          type: decision-task
          treeherder-symbol: system-symbols
          target-tasks-method: system_symbols
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 0, minute: 0}

    - name: perftest
      job:
          type: decision-task
          treeherder-symbol: perftest
          target-tasks-method: perftest
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 4, minute: 00}

    - name: perftest-on-autoland
      job:
          type: decision-task
          treeherder-symbol: perftest-auto
          target-tasks-method: perftest-on-autoland
      when: []

    - name: scriptworker-canary
      job:
          type: trigger-action
          action-name: scriptworker-canary
          include-cron-input: true
      when: []  # never (hook only)

    - name: eslint-build
      job:
          type: decision-task
          treeherder-symbol: eslint-build
          target-tasks-method: eslint-build
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 0, minute: 00}

    - name: sp-perftests
      job:
          type: decision-task
          treeherder-symbol: sp-perftests
          target-tasks-method: sp-perftests
          include-push-tasks: true
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 0, minute: 00}

    - name: snap-upstream
      job:
          type: decision-task
          treeherder-symbol: snap-upstream
          target-tasks-method: snap_upstream_tests
      run-on-projects:
          - mozilla-central
      when:
          - {hour: 0, minute: 0}

    - name: android-l10n-import
      job:
          type: decision-task
          treeherder-symbol: android-l10n-import
          target-tasks-method: android-l10n-import
      run-on-projects: [autoland]
      when:
          - {hour: 0, minute: 0}

    - name: android-l10n-sync
      job:
          type: decision-task
          treeherder-symbol: android-l10n-sync
          target-tasks-method: android-l10n-sync
      run-on-projects: [mozilla-beta]
      when:
          - {hour: 2, minute: 0}

    - name: os-integration
      job:
          type: decision-task
          treeherder-symbol: osint
          target-tasks-method: os-integration
      run-on-projects:
          - mozilla-central
      when:
          - {weekday: 'Monday', hour: 6, minute: 0}
