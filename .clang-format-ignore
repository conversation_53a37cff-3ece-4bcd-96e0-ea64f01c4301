# Uses the LLVM coding style
build/clang-plugin/.*
# The two templates cannot be formatted
config/gcc-stl-wrapper.template.h
config/msvc-stl-wrapper.template.h
# Generated code
intl/components/src/LocaleGenerated.cpp
js/src/builtin/intl/TimeZoneDataGenerated.h

# Don't want to reformat irregexp (third-party code)
js/src/irregexp/imported/.*

# Don't want to reformat zydis (third-party library subject to occasional updates).
js/src/zydis/.*

# Generated by js/src/util/make_unicode.py
js/src/util/Unicode.cpp
js/src/util/UnicodeNonBMP.h

# Ignored because of bug 1506117 & 1342657
layout/style/nsCSSAnonBoxList.h
layout/style/nsCSSCounterDescList.h
layout/style/nsCSSFontDescList.h
layout/style/nsCSSKeywordList.h
layout/style/nsCSSPseudoElementList.h
layout/style/nsCSSVisitedDependentPropList.h
layout/style/nsDOMCSSValueList.h
layout/style/nsStyleStructList.h

# Generated by protobuf
.*/.*.pb.h
.*/.*.pb.cc

# Autogenerated file
gfx/gl/GLConsts.h
gfx/webrender_bindings/webrender_ffi_generated.h
intl/components/src/UnicodeScriptCodes.h
intl/unicharutil/util/nsSpecialCasingData.cpp
intl/unicharutil/util/nsUnicodePropertyData.cpp
media/mp4parse-rust/mp4parse.h
security/manager/ssl/StaticHPKPins.h
widget/gtk/wayland/gtk-primary-selection-client-protocol.h
widget/gtk/wayland/gtk-primary-selection-protocol.c
widget/gtk/wayland/primary-selection-unstable-v1-client-protocol.h
widget/gtk/wayland/primary-selection-unstable-v1-protocol.c

# Ignored because these files are used to generate a windows.h STL wrapper,
# and reformatting them can break generating that wrapper.
config/windows-h-.*.h

# Exclude tools/clang-tidy/test from automatic reformatting, since it can
# break some of the tests in that directory.
tools/clang-tidy/test/.*

# We are testing the incorrect formatting.
tools/lint/test/files/

# Contains an XML definition and formatting would break the layout
widget/gtk/MPRISInterfaceDescription.h

# The XPTCall stubs files have some inline assembly macros
# that get reformatted badly. See bug 1510781.
xpcom/reflect/xptcall/md/win32/.*
xpcom/reflect/xptcall/md/unix/.*

# Askama template code, which isn't valid C++ in its original form
toolkit/components/uniffi-bindgen-gecko-js/src/templates/.*
# Generated from that template code
toolkit/components/uniffi-js/UniFFIGeneratedScaffolding.cpp
toolkit/components/uniffi-js/UniFFIFixtureScaffolding.cpp

# Generated from ./tools/rewriting/ThirdPartyPaths.txt
# awk '{print ""$1".*"}' ./tools/rewriting/ThirdPartyPaths.txt
browser/extensions/mortar/ppapi/.*
devtools/client/shared/sourceeditor/codemirror/.*
devtools/client/shared/sourceeditor/codemirror6/.*
dom/canvas/test/webgl-conf/checkout/closure-library/.*
dom/media/gmp/rlz/.*
dom/media/gmp/widevine-adapter/content_decryption_module.h
dom/media/gmp/widevine-adapter/content_decryption_module_export.h
dom/media/gmp/widevine-adapter/content_decryption_module_ext.h
dom/media/gmp/widevine-adapter/content_decryption_module_proxy.h
dom/media/platforms/ffmpeg/ffmpeg57/.*
dom/media/platforms/ffmpeg/ffmpeg58/.*
dom/media/platforms/ffmpeg/ffmpeg59/.*
dom/media/platforms/ffmpeg/ffmpeg60/.*
dom/media/platforms/ffmpeg/ffmpeg61/.*
dom/media/platforms/ffmpeg/libav53/.*
dom/media/platforms/ffmpeg/libav54/.*
dom/media/platforms/ffmpeg/libav55/.*
dom/media/webrtc/transport/third_party/.*
dom/media/webspeech/recognition/endpointer.cc
dom/media/webspeech/recognition/endpointer.h
dom/media/webspeech/recognition/energy_endpointer.cc
dom/media/webspeech/recognition/energy_endpointer.h
dom/media/webspeech/recognition/energy_endpointer_params.cc
dom/media/webspeech/recognition/energy_endpointer_params.h
dom/webauthn/winwebauthn/webauthn.h
editor/libeditor/tests/browserscope/lib/richtext/.*
editor/libeditor/tests/browserscope/lib/richtext2/.*
extensions/spellcheck/hunspell/src/.*
gfx/angle/.*
gfx/cairo/.*
gfx/graphite2/.*
gfx/harfbuzz/.*
gfx/ots/src/.*
gfx/ots/include/.*
gfx/ots/tests/.*
gfx/qcms/.*
gfx/sfntly/.*
gfx/skia/.*
gfx/vr/service/openvr/.*
gfx/vr/service/openvr/headers/openvr.h
gfx/vr/service/openvr/src/README
gfx/vr/service/openvr/src/dirtools_public.cpp
gfx/vr/service/openvr/src/dirtools_public.h
gfx/vr/service/openvr/src/envvartools_public.cpp
gfx/vr/service/openvr/src/envvartools_public.h
gfx/vr/service/openvr/src/hmderrors_public.cpp
gfx/vr/service/openvr/src/hmderrors_public.h
gfx/vr/service/openvr/src/ivrclientcore.h
gfx/vr/service/openvr/src/openvr_api_public.cpp
gfx/vr/service/openvr/src/pathtools_public.cpp
gfx/vr/service/openvr/src/pathtools_public.h
gfx/vr/service/openvr/src/sharedlibtools_public.cpp
gfx/vr/service/openvr/src/sharedlibtools_public.h
gfx/vr/service/openvr/src/strtools_public.cpp
gfx/vr/service/openvr/src/strtools_public.h
gfx/vr/service/openvr/src/vrpathregistry_public.cpp
gfx/vr/service/openvr/src/vrpathregistry_public.h
gfx/ycbcr/.*
intl/hyphenation/hyphen/.*
intl/icu/.*
intl/icu_capi/.*
ipc/chromium/src/third_party/.*
js/src/ctypes/libffi/.*
js/src/dtoa.c.*
js/src/editline/.*
js/src/jit/arm64/vixl/.*
js/src/vtune/disable_warnings.h
js/src/vtune/ittnotify.h
js/src/vtune/ittnotify_config.h
js/src/vtune/ittnotify_static.c
js/src/vtune/ittnotify_static.h
js/src/vtune/ittnotify_types.h
js/src/vtune/jitprofiling.c
js/src/vtune/jitprofiling.h
js/src/vtune/legacy/.*
media/ffvpx/.*
media/kiss_fft/.*
media/libaom/.*
media/libcubeb/.*
media/libdav1d/.*
media/libjpeg/.*
media/libmkv/.*
media/libnestegg/.*
media/libogg/.*
media/libopus/.*
media/libpng/.*
media/libsoundtouch/.*
media/libspeex_resampler/.*
media/libvorbis/.*
media/libvpx/.*
media/libwebp/.*
media/libyuv/.*
media/mozva/va/.*
media/openmax_il/.*
media/webrtc/signaling/src/sdp/sipcc/.*
media/webrtc/trunk/.*
mfbt/double-conversion/double-conversion/.*
mobile/android/geckoview/src/thirdparty/.*
modules/brotli/.*
modules/fdlibm/.*
modules/freetype2/.*
modules/libbz2/.*
modules/pdfium/.*
modules/woff2/include/.*
modules/woff2/src/.*
modules/xz-embedded/.*
modules/zlib/.*
mozglue/misc/decimal/.*
mozglue/static/lz4/.*
mozglue/tests/glibc_printf_tests/.*
netwerk/dns/nsIDNKitInterface.h
netwerk/sctp/src/.*
netwerk/srtp/src/.*
nsprpub/.*
other-licenses/.*
parser/expat/.*
security/nss/.*
security/sandbox/chromium/.*
security/sandbox/chromium-shim/.*
testing/gtest/gmock/.*
testing/gtest/gtest/.*
testing/talos/talos/tests/dromaeo/.*
testing/talos/talos/tests/kraken/.*
testing/talos/talos/tests/v8_7/.*
testing/web-platform/tests/resources/webidl2/.*
testing/web-platform/tests/tools/third_party/.*
third_party/.*
toolkit/components/jsoncpp/.*
toolkit/components/protobuf/.*
toolkit/components/translation/cld2/.*
toolkit/components/url-classifier/chromium/.*
toolkit/components/url-classifier/protobuf/.*
toolkit/crashreporter/breakpad-client/.*
toolkit/crashreporter/google-breakpad/.*
tools/fuzzing/libfuzzer/.*
tools/profiler/core/vtune/.*
# tools/profiler/public/GeckoTraceEvent.h is a modified vendored copy
tools/profiler/public/GeckoTraceEvent.h
xpcom/build/mach_override.c
xpcom/build/mach_override.h
xpcom/io/crc32c.c
