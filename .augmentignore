# 排除编译产物和工具脚本
obj*/  
build/  
dist/  
*.o  
*.a  
*.so  
*.dylib  
*.dll  
*.exe  

# 排除第三方依赖和测试数据
third_party/  
tools/update-packaging/  
tools/clang-tidy/  
testing/  
taskcluster/  

# 排除大部分与 DOM 无关的模块
accessible/  
addon-sdk/  
browser/  
caps/  
devtools/shared/compat/  
docshell/  
editor/  
embedding/  
extensions/  
gfx/  
gradle/  
hal/  
intl/  
media/  
memory/  
mobile/  
modules/  
netwerk/  
nsprpub/  
parser/  
python/  
security/  
services/  
startupcache/  
storage/  
testing/  
toolkit/  
uriloader/  
view/  
widget/  
xpcom/  
xul/  

# 仅保留 DOM/DevTools 相关部分
# ⚠️ 显式包含关键目录（即使被上面通配符排了，也保留这些）
!dom/
!devtools/client/inspector/
!js/  
!layout/  
!devtools/shared/protocol/
!devtools/shared/dom-utils.js
!devtools/server/actors/inspector/
