# 排除编译产物和工具脚本
obj*/
build/
dist/
*.o
*.a
*.so
*.dylib
*.dll
*.exe

# 排除第三方依赖和测试数据
third_party/
tools/update-packaging/
tools/clang-tidy/
taskcluster/

# 排除与Firefox浏览器核心实现无关的模块
accessible/
addon-sdk/
# browser/ - 保留，包含浏览器UI和核心逻辑
caps/
devtools/shared/compat/
# docshell/ - 保留，文档加载和导航核心
editor/
embedding/
extensions/
# gfx/ - 保留，图形渲染核心
gradle/
hal/
intl/
media/
memory/
mobile/
modules/
# netwerk/ - 保留，网络层核心
nsprpub/
# parser/ - 保留，HTML/XML解析器
python/
security/
# services/ - 保留，包含重要的浏览器服务
startupcache/
storage/
testing/
# toolkit/ - 保留，包含核心工具和组件
uriloader/
# view/ - 保留，视图系统
# widget/ - 保留，UI组件
# xpcom/ - 保留，Firefox组件架构核心
# xul/ - 保留，XUL界面系统

# 显式包含Firefox浏览器核心实现相关目录
!dom/                              # DOM实现核心
!js/                               # JavaScript引擎
!layout/                           # 布局引擎
!browser/                          # 浏览器UI和核心逻辑
!docshell/                         # 文档加载和导航
!gfx/                              # 图形渲染
!netwerk/                          # 网络层
!parser/                           # HTML/XML解析器
!services/                         # 浏览器服务
!toolkit/                          # 核心工具和组件
!view/                             # 视图系统
!widget/                           # UI组件
!xpcom/                            # Firefox组件架构
!xul/                              # XUL界面系统
!remote/                           # 远程调试协议

# DevTools相关（用于调试浏览器实现）
!devtools/client/inspector/
!devtools/shared/protocol/
!devtools/shared/dom-utils.js
!devtools/server/actors/inspector/
!devtools/server/actors/webconsole/

# 排除不必要的测试和示例文件
**/test/
**/tests/
**/testing/
**/examples/
**/sample/
**/demo/
**/*test*.js
**/*test*.cpp
**/*test*.h

# 排除文档和配置文件（保留重要的构建配置）
docs/
*.md
*.rst
*.txt
!README.md
!moz.configure
!configure.py
!mozconfig*

# 排除本地化文件（除非研究国际化）
**/locale/
**/locales/
**/*.dtd
**/*.properties
**/*.po

# 保留关键的构建和配置文件
!mach
!configure*
!Makefile*
!*.mk
!*.in
!moz.build
!*.py
