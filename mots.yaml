%YAML 1.2
---
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at https://mozilla.org/MPL/2.0/.

# See https://mots.readthedocs.io/en/latest/#quick-start for quick start
# documentation and how to modify this file.
repo: mozilla-central
created_at: '2021-10-14T12:50:40.073465'
updated_at: '2024-12-13T13:27:25.205577+00:00'
export:
  path: ./docs/mots/index.rst
  format: rst
  searchfox_enabled: true
description: |
  Mozilla operates under a `module ownership governance system <https://www.mozilla.org/hacking/module-ownership.html>`_. A module is a discrete unit of code or activity. An owner is the person in charge of a module or sub-module. A peer is a person whom the owner has appointed to help them. A module may have multiple peers and, very occasionally, multiple owners.

  The system is overseen by the owner and peers of the Module Ownership module. For the modules that make up Firefox, oversight is provided by the Firefox Technical Leadership module. Owners may add and remove peers from their modules as they wish, without reference to anyone else.

aliases:
  - &TLMC
    nick: TLMC
people:
  - &adw
    bmo_id: 334927
    name: <PERSON>
    nick: adw
  - &afranchuk
    bmo_id: 719881
    name: <PERSON> <PERSON>anchuk
    nick: afranchuk
  - &agashlin
    bmo_id: 583776
    name: Adam <PERSON>hlin
    nick: agashlin
  - &agi
    bmo_id: 421286
    name: ''
    nick: agi
  - &aglavic
    bmo_id: 692423
    name: Andrej
    nick: aglavic
  - &ahal
    bmo_id: 382067
    name: Andrew Halberstadt
    nick: ahal
  - &ahochheiden
    bmo_id: 693303
    name: Alex Hochheiden
    nick: ahochheiden
  - &allstarschh
    bmo_id: 434670
    name: Yoshi Cheng-Hao Huang
    nick: allstars.chh
  - &alwu
    bmo_id: 517691
    name: Alastor Wu
    nick: alwu
  - bmo_id: 623634
    name: Arturo Mejia
    nick: amejia
  - &aminomancer
    bmo_id: 636965
    name: Shane Hughes
    nick: aminomancer
  - &amy
    bmo_id: 622155
    name: Amy Churchwell
    nick: amy
  - &anba
    bmo_id: 339940
    name: André Bargull
    nick: anba
  - &andi
    bmo_id: 555858
    name: Andi
    nick: andi
  - &annaweine
    bmo_id: 697976
    name: Anna Weine
    nick: anna.weine
  - &aoprea
    bmo_id: 509499
    name: Andrei Oprea
    nick: aoprea
  - &aosmond
    bmo_id: 505306
    name: Andrew Osmond
    nick: aosmond
  - &arai
    bmo_id: 310076
    name: Tooru Fujisawa
    nick: arai
  - &aryx
    bmo_id: 258347
    name: Sebastian Hengst
    nick: aryx
  - &asuth
    bmo_id: 151407
    name: Andrew Sutherland
    nick: asuth
  - &ato
    bmo_id: 446296
    name: Andreas Tolfsen
    nick: ato
  - &baku
    bmo_id: 446257
    name: Andrea Marchesini
    nick: baku
  - &basschouten
    bmo_id: 272464
    name: Bas Schouten
    nick: bas.schouten
  - &bc
    bmo_id: 23402
    name: Bob Clary
    nick: bc
  - &bdk
    bmo_id: 683100
    name: Ben Dean-Kawamura
    nick: bdk
  - &benbangert
    bmo_id: 424312
    name: Ben Bangert
    nick: benbangert
  - &beth
    bmo_id: 529428
    name: Beth Rennie
    nick: beth
  - &beurdouche
    bmo_id: 622637
    name: Benjamin Beurdouche
    nick: beurdouche
  - &bhearsum
    bmo_id: 259016
    name: bhearsum
    nick: bhearsum
  - &bholley
    bmo_id: 313730
    name: Bobby Holley
    nick: bholley
  - &birtles
    bmo_id: 165012
    name: Brian Birtles
    nick: birtles
  - &bobowen
    bmo_id: 458623
    name: Bob Owen
    nick: bobowen
  - &boek
    bmo_id: 599885
    name: Jeff Boek
    nick: boek
  - &bomsy
    bmo_id: 656417
    name: Hubert Boma Manilla
    nick: bomsy
  - &boris
    bmo_id: 500786
    name: Boris Chiou
    nick: boris
  - &botond
    bmo_id: 474533
    name: Botond Ballo
    nick: botond
  - bmo_id: 560379
    name: Bryce Seager van Dyk
    nick: bryce
  - bmo_id: 596517
    name: Brian Stack
    nick: bstack
  - &bthrall
    bmo_id: 700441
    name: Bryan Thrall
    nick: bthrall
  - &bvandersloot
    bmo_id: 692134
    name: Benjamin VanderSloot
    nick: bvandersloot
  - &bwc
    bmo_id: 478411
    name: Byron Campen
    nick: bwc
  - &bytesized
    bmo_id: 563604
    name: Robin Steuber
    nick: bytesized
  - &calixte
    bmo_id: 560431
    name: Calixte Denizet
    nick: calixte
  - bmo_id: 129968
    name: Justin Wood
    nick: Callek
  - &calu
    bmo_id: 693351
    name: Cathy Lu
    nick: calu
  - &canova
    bmo_id: 525693
    name: Nazım Can Altınova
    nick: canova
  - &charlie
    bmo_id: 710471
    name: Charlie Humphreys
    nick: charlie
  - &chunmin
    bmo_id: 521646
    name: C
    nick: chunmin
  - &chutten
    bmo_id: 553004
    name: Chris H-C
    nick: chutten
  - &ckerschb
    bmo_id: 363267
    name: Christoph Kerschbaumer
    nick: ckerschb
  - &cmartin
    bmo_id: 625428
    name: Chris Martin
    nick: cmartin
  - &cmkm
    bmo_id: 693338
    name: ''
    nick: cmkm
  - &cpeterson
    bmo_id: 430528
    name: Chris Peterson
    nick: cpeterson
  - &daisuke
    bmo_id: 439222
    name: Daisuke Akatsuka
    nick: daisuke
  - &daleharvey
    bmo_id: 389449
    name: Dale Harvey
    nick: daleharvey
  - &dao
    bmo_id: 219124
    name: Dão Gottwald
    nick: dao
  - &daoshengmu
    bmo_id: 531139
    name: Daosheng Mu
    nick: daoshengmu
  - &davehunt
    bmo_id: 396948
    name: Dave Hunt
    nick: davehunt
  - bmo_id: 3881
    name: David Baron
    nick: dbaron
  - &dcoates
    bmo_id: 468905
    name: Danny Coates
    nick: dcoates
  - &deanis74
    bmo_id: 7924
    name: ''
    nick: deanis74
  - &deian
    bmo_id: 637163
    name: Deian Stefan
    nick: deian
  - &denschub
    bmo_id: 421268
    name: Dennis Schubert
    nick: denschub
  - &Dexter
    bmo_id: 248036
    name: Alessio Placitelli
    nick: Dexter
  - &dholbert
    bmo_id: 278074
    name: Daniel Holbert
    nick: dholbert
  - &dimi
    bmo_id: 468232
    name: Dimi Lee
    nick: dimi
  - &djackson
    bmo_id: 644159
    name: Dennis Jackson
    nick: djackson
  - &dlrobertson
    bmo_id: 705145
    name: Dan Robertson
    nick: dlrobertson
  - &dminor
    bmo_id: 466787
    name: Dan Minor
    nick: dminor
  - &dmosedale
    bmo_id: 1537
    name: Dan Mosedale
    nick: dmosedale
  - &dshin
    bmo_id: 703422
    name: David Shin
    nick: dshin
  - &dveditz
    bmo_id: 1689
    name: Daniel Veditz
    nick: dveditz
  - &dwalker
    bmo_id: 713270
    name: DJ
    nick: dwalker
  - &edenchuang
    bmo_id: 516138
    name: Eden Chuang
    nick: edenchuang
  - &edgar
    bmo_id: 455480
    name: Edgar Chen
    nick: edgar
  - &edgul
    bmo_id: 701256
    name: edgul
    nick: edgul
  - &eeejay
    bmo_id: 291675
    name: Eitan Isaacson
    nick: eeejay
  - &eemeli
    bmo_id: 318883
    name: Eemeli Aro
    nick: eemeli
  - &ekr
    bmo_id: 35667
    name: Eric Rescorla
    nick: ekr
  - &emcminn
    bmo_id: 638890
    name: Emily McMinn
    nick: emcminn
  - &emilio
    bmo_id: 546716
    name: Emilio Cobos Álvarez
    nick: emilio
  - &emk
    bmo_id: 5842
    name: Masatoshi Kimura
    nick: emk
  - &emz
    bmo_id: 636491
    name: Emma Zühlcke
    nick: emz
  - &enndeakin
    bmo_id: 6102
    name: Neil Deakin
    nick: enndeakin
  - &epavlov
    bmo_id: 675384
    name: Evgeny Pavlov
    nick: epavlov
  - &ErichDonGubler
    bmo_id: 713343
    name: Erich Gubler
    nick: ErichDonGubler
  - &evilpie
    bmo_id: 393835
    name: Tom S
    nick: evilpie
  - &farre
    bmo_id: 566192
    name: Andreas Farre
    nick: farre
  - bmo_id: 417695
    name: Nick Fitzgerald
    nick: fitzgen
  - &flod
    bmo_id: 130462
    name: Francesco Lodolo
    nick: flod
  - &florian
    bmo_id: 149052
    name: Florian Quèze
    nick: florian
  - &fredwang
    bmo_id: 739410
    name: Frédéric Wang
    nick: fred.wang
  - &freddy
    bmo_id: 428608
    name: Frederik Braun
    nick: freddy
  - &froydnj
    bmo_id: 417288
    name: Nathan Froyd
    nick: froydnj
  - &gabriel
    bmo_id: 697145
    name: Gabriel Bustamante
    nick: gabriel
  - &gcp
    bmo_id: 151147
    name: Gian-Carlo Pascutto
    nick: gcp
  - &Gijs
    bmo_id: 159069
    name: ''
    nick: Gijs
  - &giorga
    bmo_id: 698576
    name: Iorga Gabriel
    nick: giorga
  - &gl
    bmo_id: 475223
    name: Gabriel Luong
    nick: gl
  - &glandium
    bmo_id: 47192
    name: Mike Hommey
    nick: glandium
  - bmo_id: 420446
    name: Gregory Szorc
    nick: gps
  - &gregtatum
    bmo_id: 561658
    name: Greg Tatum
    nick: gregtatum
  - &groovecoder
    bmo_id: 403306
    name: Luke Crouch
    nick: groovecoder
  - &gstoll
    bmo_id: 714603
    name: Greg Stoll
    nick: gstoll
  - &gsvelto
    bmo_id: 448747
    name: Gabriele Svelto
    nick: gsvelto
  - &gw
    bmo_id: 504871
    name: Glenn Watson
    nick: gw
  - &haik
    bmo_id: 558190
    name: Haik Aftandilian
    nick: haik
  - &handyman
    bmo_id: 506442
    name: David Parks
    nick: handyman
  - &hiro
    bmo_id: 131213
    name: Hiroyuki Ikezoe
    nick: hiro
  - &hjones
    bmo_id: 692246
    name: Hanna Jones
    nick: hjones
  - &hneiva
    bmo_id: 693369
    name: Heitor Neiva
    nick: hneiva
  - &Honza
    bmo_id: 303767
    name: Jan Honza Odvarko
    nick: Honza
  - &hsivonen
    bmo_id: 5490
    name: Henri Sivonen
    nick: hsivonen
  - &iain
    bmo_id: 623993
    name: Iain Ireland
    nick: iain
  - &issammani
    bmo_id: 708367
    name: Issam Mani
    nick: issammani
  - &itielyn8
    bmo_id: 353822
    name: Itiel
    nick: itiel_yn8
  - &Jamie
    bmo_id: 219126
    name: James Teh
    nick: Jamie
  - &jandem
    bmo_id: 375297
    name: Jan de Mooij
    nick: jandem
  - &janerik
    bmo_id: 533624
    name: Jan-Erik Rediger
    nick: janerik
  - &janika
    bmo_id: 708468
    name: Janika Neuberger
    nick: janika
  - &janv
    bmo_id: 8340
    name: Jan Varga
    nick: janv
  - &jchen
    bmo_id: 281508
    name: ''
    nick: jchen
  - &jcristau
    bmo_id: 580382
    name: Julien Cristau
    nick: jcristau
  - &jdescottes
    bmo_id: 559949
    name: Julian Descottes
    nick: jdescottes
  - &jesup
    bmo_id: 11539
    name: Randell Jesup
    nick: jesup
  - &jfkthame
    bmo_id: 329583
    name: Jonathan Kew
    nick: jfkthame
  - &jgilbert
    bmo_id: 419431
    name: Kelsey Gilbert
    nick: jgilbert
  - &jgraham
    bmo_id: 389650
    name: James Graham
    nick: jgraham
  - &jhirsch
    bmo_id: 446735
    name: Jared Hirsch
    nick: jhirsch
  - &jhlin
    bmo_id: 463180
    name: John Lin
    nick: jhlin
  - &jib
    bmo_id: 456498
    name: Jan-Ivar Bruaroey
    nick: jib
  - &jimb
    bmo_id: 298765
    name: Jim Blandy
    nick: jimb
  - &jimm
    bmo_id: 279663
    name: Jim Mathies
    nick: jimm
  - &jjalkanen
    bmo_id: 689836
    name: Jari Jalkanen
    nick: jjalkanen
  - &jld
    bmo_id: 462836
    name: Jed Davis
    nick: jld
  - &jlorenzo
    bmo_id: 504868
    name: Johan Lorenzo
    nick: jlorenzo
  - &jmaher
    bmo_id: 324370
    name: Joel Maher
    nick: jmaher
  - &jnicol
    bmo_id: 541747
    name: Jamie Nicol
    nick: jnicol
  - &jonalmeida
    bmo_id: 541581
    name: Jonathan Almeida
    nick: jonalmeida
  - &jonco
    bmo_id: 443194
    name: Jon Coppeard
    nick: jonco
  - &joschmidt
    bmo_id: 723184
    name: Johannes
    nick: joschmidt
  - &josh
    bmo_id: 621062
    name: Josh Groves
    nick: josh
  - &jrmuizel
    bmo_id: 309398
    name: Jeff Muizelaar
    nick: jrmuizel
  - &jschanck
    bmo_id: 689878
    name: John Schanck
    nick: jschanck
  - &jseward
    bmo_id: 346453
    name: Julian Seward
    nick: jseward
  - &jstutte
    bmo_id: 646284
    name: Jens Stutte
    nick: jstutte
  - &jsudiaman
    bmo_id: 713253
    name: Jonathan Sudiaman
    nick: jsudiaman
  - &jswinarton
    bmo_id: 699603
    name: Jeremy Swinarton
    nick: jswinarton
  - &jteow
    bmo_id: 701257
    name: James Teow
    nick: jteow
  - &jules
    bmo_id: 699161
    name: Jules Simplicio
    nick: jules
  - &jwatt
    bmo_id: 32767
    name: Jonathan Watt
    nick: jwatt
  - bmo_id: 512198
    name: Jean-Yves Avenard
    nick: jya
  - &k88hudson
    bmo_id: 556482
    name: Kate Hudson
    nick: k88hudson
  - &KaiE
    bmo_id: 36541
    name: Kai Engert
    nick: KaiE
  - &karlt
    bmo_id: 274246
    name: Karl Tomlinson
    nick: karlt
  - &kaya
    bmo_id: 712933
    name: Kayacan Kaya
    nick: kaya
  - &kcochrane
    bmo_id: 707663
    name: Kelly Cochrane
    nick: kcochrane
  - &keeler
    bmo_id: 349244
    name: Dana Keeler
    nick: keeler
  - &kershaw
    bmo_id: 505624
    name: Kershaw Chang
    nick: kershaw
  - &kinetik
    bmo_id: 274575
    name: Matthew Gregan
    nick: kinetik
  - &kip
    bmo_id: 499338
    name: ''
    nick: kip
  - &kmag
    bmo_id: 106098
    name: Kris Maglione
    nick: kmag
  - &kpatenio
    bmo_id: 690690
    name: kpatenio
    nick: kpatenio
  - &KrisWright
    bmo_id: 617144
    name: Kris Wright
    nick: KrisWright
  - &ksenia
    bmo_id: 638886
    name: Ksenia Berezina
    nick: ksenia
  - &kshampur
    bmo_id: 698141
    name: Kash Shampur
    nick: kshampur
  - &kvark
    bmo_id: 582393
    name: Dzmitry Malyshau
    nick: kvark
  - &lina
    bmo_id: 506322
    name: Lina Butler
    nick: lina
  - &longsonr
    bmo_id: 218550
    name: Robert Longson
    nick: longsonr
  - &lougenia
    bmo_id: 649193
    name: Lougenia Bailey
    nick: lougenia
  - &lsalzman
    bmo_id: 536714
    name: Lee Salzman
    nick: lsalzman
  - &mkato
    bmo_id: 8636
    name: Makoto Kato
    nick: m_kato
  - &mak
    bmo_id: 240353
    name: Marco Bonardo
    nick: mak
  - &marco
    bmo_id: 420453
    name: Marco Castelluccio
    nick: marco
  - &MarcoZ
    bmo_id: 285656
    name: Marco Zehe
    nick: MarcoZ
  - &Mardak
    bmo_id: 125983
    name: Ed Lee
    nick: Mardak
  - &markh
    bmo_id: 16943
    name: Mark Hammond
    nick: markh
  - &masayuki
    bmo_id: 34283
    name: Masayuki Nakano
    nick: masayuki
  - &mathjazz
    bmo_id: 275214
    name: Matjaz Horvat
    nick: mathjazz
  - &matttighe
    bmo_id: 701804
    name: Matt Tighe
    nick: matt-tighe
  - &MattN
    bmo_id: 305228
    name: Matthew N
    nick: MattN
  - &maxx
    bmo_id: 636237
    name: Maxx Crawford
    nick: maxx
  - &mcarare
    bmo_id: 648814
    name: Mihai Adrian Carare
    nick: mcarare
  - &mccr8
    bmo_id: 406194
    name: Andrew McCreight
    nick: mccr8
  - &mcheang
    bmo_id: 638884
    name: Mandy Cheang
    nick: mcheang
  - &mconley
    bmo_id: 403756
    name: Mike Conley
    nick: mconley
  - &mgaudet
    bmo_id: 607045
    name: Matthew Gaudet
    nick: mgaudet
  - &micah
    bmo_id: 585815
    name: Micah Tigley
    nick: micah
  - &mikokm
    bmo_id: 547538
    name: Miko Mynttinen
    nick: mikokm
  - &mkaply
    bmo_id: 7313
    name: Mike Kaply
    nick: mkaply
  - &molly
    bmo_id: 550257
    name: Molly Howell
    nick: molly
  - &morgan
    bmo_id: 618000
    name: Morgan Reschenberg
    nick: morgan
  - &mossop
    bmo_id: 91159
    name: Dave Townsend
    nick: mossop
  - &Ms2ger
    bmo_id: 302720
    name: ''
    nick: Ms2ger
  - &mstange
    bmo_id: 293943
    name: Markus Stange
    nick: mstange
  - &mstriemer
    bmo_id: 492360
    name: Mark Striemer
    nick: mstriemer
  - &mt
    bmo_id: 438654
    name: Martin Thomson
    nick: mt
  - &nalexander
    bmo_id: 432887
    name: Nick Alexander
    nick: nalexander
  - &nanj
    bmo_id: 538785
    name: Nan Jiang
    nick: nanj
  - &nbarrett
    bmo_id: 690000
    name: Nathan Barrett
    nick: nbarrett
  - &nbp
    bmo_id: 422187
    name: Nicolas B
    nick: nbp
  - &nchevobbe
    bmo_id: 557153
    name: Nicolas Chevobbe
    nick: nchevobbe
  - &nical
    bmo_id: 438998
    name: Nicolas Silva
    nick: nical
  - &nika
    bmo_id: 534482
    name: Nika Layzell
    nick: nika
  - &niklas
    bmo_id: 690738
    name: Niklas Baumgardner
    nick: niklas
  - &nlapre
    bmo_id: 711793
    name: Nathan LaPré
    nick: nlapre
  - &nohlmeier
    bmo_id: 499343
    name: ''
    nick: nohlmeier
  - &nordzilla
    bmo_id: 651777
    name: Erik Nordin
    nick: nordzilla
  - &nrishel
    bmo_id: 697005
    name: Nick Rishel
    nick: nrishel
  - &ochameau
    bmo_id: 283262
    name: Alexandre Poirot
    nick: ochameau
  - &olivia
    bmo_id: 694341
    name: Olivia Hall
    nick: olivia
  - &overholt
    bmo_id: 442218
    name: Andrew Overholt
    nick: overholt
  - &owlish
    bmo_id: 579431
    name: ''
    nick: owlish
  - &padenot
    bmo_id: 404637
    name: Paul Adenot
    nick: padenot
  - &pbone
    bmo_id: 595166
    name: Paul Bone
    nick: pbone
  - &pdahiya
    bmo_id: 471708
    name: Punam Dahiya
    nick: pdahiya
  - &pehrsons
    bmo_id: 489889
    name: Andreas Pehrson
    nick: pehrsons
  - &perrymcmanis
    bmo_id: 697076
    name: Perry McManis
    nick: perry.mcmanis
  - &peterv
    bmo_id: 24295
    name: Peter Van der Beken
    nick: peterv
  - &prathiksha
    bmo_id: 580428
    name: ''
    nick: prathiksha
  - &rbs
    bmo_id: 5630
    name: ''
    nick: rbs
  - &rhunt
    bmo_id: 573202
    name: Ryan Hunt
    nick: rhunt
  - &rkraesig
    bmo_id: 699639
    name: Ray Kraesig
    nick: rkraesig
  - &robwu
    bmo_id: 447061
    name: Rob Wu
    nick: robwu
  - &royang
    bmo_id: 644746
    name: Roger Yang
    nick: royang
  - &rpl
    bmo_id: 339062
    name: Luca Greco
    nick: rpl
  - &rrelyea
    bmo_id: 11099
    name: Robert Relyea
    nick: rrelyea
  - &rsainani
    bmo_id: 718207
    name: Rahul Sainani
    nick: rsainani
  - &ryansleevi
    bmo_id: 381992
    name: Ryan Sleevi
    nick: ryan.sleevi
  - &RyanVM
    bmo_id: 75935
    name: Ryan VanderMeulen
    nick: RyanVM
  - &saschanaz
    bmo_id: 473060
    name: Kagami Rosylight
    nick: saschanaz
  - &Sasha
    bmo_id: 704132
    name: Alexandra Borovova
    nick: Sasha
  - &scaraveo
    bmo_id: 443354
    name: ''
    nick: scaraveo
  - &sclements
    bmo_id: 603840
    name: Sarah Clements
    nick: sclements
  - &scunnane
    bmo_id: 703413
    name: Stephanie Cunnane
    nick: scunnane
  - &sefeng
    bmo_id: 625922
    name: Sean Feng
    nick: sefeng
  - &serg
    bmo_id: 691597
    name: Sergey Galich
    nick: serg
  - &sfink
    bmo_id: 359004
    name: Steve Fink
    nick: sfink
  - &sfoster
    bmo_id: 430165
    name: Sam Foster
    nick: sfoster
  - &shravanrn
    bmo_id: 639827
    name: Shravan Narayan
    nick: shravanrn
  - &skhamis
    bmo_id: 683894
    name: Sammy Khamis
    nick: skhamis
  - &smaug
    bmo_id: 39966
    name: Olli Pettay
    nick: smaug
  - &sotaro
    bmo_id: 434202
    name: Sotaro Ikeda
    nick: sotaro
  - &sparky
    bmo_id: 560562
    name: Greg Mierzwinski
    nick: sparky
  - &spohl
    bmo_id: 456485
    name: Stephen A Pohl
    nick: spohl
  - &standard8
    bmo_id: 112088
    name: Mark Banner
    nick: standard8
  - &sthompson
    bmo_id: 756026
    name: Stephen Thompson
    nick: sthompson
  - &stransky
    bmo_id: 263117
    name: Martin Stránský
    nick: stransky
  - bmo_id: 468136
    name: Dan Gohman
    nick: sunfish
  - &Sylvestre
    bmo_id: 495955
    name: Sylvestre Ledru
    nick: Sylvestre
  - &tantek
    bmo_id: 164722
    name: Tantek Çelik
    nick: tantek
  - &tarek
    bmo_id: 385707
    name: Tarek Ziadé
    nick: tarek
  - &tcampbell
    bmo_id: 586681
    name: Ted Campbell
    nick: tcampbell
  - &teoxoy
    bmo_id: 715425
    name: Teodor Tanasoaia
    nick: teoxoy
  - &teshaq
    bmo_id: 663555
    name: Tarik Eshaq
    nick: teshaq
  - &tgiles
    bmo_id: 666930
    name: Tim Giles
    nick: tgiles
  - &thecount
    bmo_id: 466874
    name: Scott
    nick: thecount
  - &timhuang
    bmo_id: 547199
    name: Tim Huang
    nick: timhuang
  - &tjr
    bmo_id: 578488
    name: Tom Ritter
    nick: tjr
  - &tlouw
    bmo_id: 707594
    name: Tiaan Louw
    nick: tlouw
  - &tnikkel
    bmo_id: 255010
    name: Timothy Nikkel
    nick: tnikkel
  - bmo_id: 461658
    name: Tom Prince
    nick: tomprince
  - &toshi
    bmo_id: 644141
    name: Toshihito Kikuchi
    nick: toshi
  - &travis
    bmo_id: 628857
    name: Travis Long
    nick: travis_
  - &tschuster
    bmo_id: 703078
    name: Tom Schuster
    nick: tschuster
  - &twisniewski
    bmo_id: 583576
    name: Thomas Wisniewski
    nick: twisniewski
  - &TYLin
    bmo_id: 503090
    name: Ting-Yu Lin
    nick: TYLin
  - &ueno
    bmo_id: 565522
    name: Daiki Ueno
    nick: ueno
  - &valentin
    bmo_id: 415378
    name: Valentin Gosu
    nick: valentin
  - &wchen
    bmo_id: 414438
    name: William Chen
    nick: wchen
  - &whimboo
    bmo_id: 76551
    name: Henrik Skupin
    nick: whimboo
  - &willdurand
    bmo_id: 603050
    name: William Durand
    nick: willdurand
  - &xidorn
    bmo_id: 373403
    name: Xidorn Quan
    nick: xidorn
  - &yulia
    bmo_id: 600802
    name: Yulia Startsev
    nick: yulia
  - &yury
    bmo_id: 382167
    name: Yury Delendik
    nick: yury
  - bmo_id: 458165
    name: Yura Zenevich
    nick: yzen
  - &zbraniecki
    bmo_id: 41270
    name: Zibi Braniecki
    nick: zbraniecki
  - &zeid
    bmo_id: 633708
    name: Zeid Zabaneh
    nick: zeid
  - &zombie
    bmo_id: 445095
    name: Tomislav Jovanovic
    nick: zombie
  - &zrhoffman
    bmo_id: 400406
    name: Zach Hoffman
    nick: zrhoffman
modules:
  - name: firefox-toplevel
    description: The top level directory for the Firefox/Gecko tree. Owned by the
      TLMC
    includes:
      - README.txt
    meta:
      owners_emeritus:
        - Brendan Eich
        - Kate Hudson
    owners:
      - *bholley
      - *overholt
      - *mossop
      - *benbangert
      - *nalexander
      - *nika
    peers: []
    machine_name: _firefoxtoplevel
    submodules:

      - name: Code Review Policy
        description: Submodule governing the code review process, ensuring adherence
          to coding standards and quality requirements.
        owners:
          - *TLMC
        peers: []
        machine_name: code_review_policy

      - name: Performance Regression Policy
        description: Submodule focused on preventing and addressing performance regressions,
          ensuring stable and optimized performance.
        owners:
          - *TLMC
        peers: []
        machine_name: performance_regression_policy

  - machine_name: android_components
    name: Android Components
    description: A collection of independent, reusable Android library components
      to make it easier to build browsers and browser-like applications.
    includes:
      - mobile/android/android-components/**/*
    excludes: []
    owners:
      - *jonalmeida
      - *matttighe
    peers:
      - *kaya
    meta:
  - machine_name: code_coverage
    name: Code Coverage
    description: Tools for code coverage instrumentation, and coverage data parsing
      and management.
    includes:
      - tools/code-coverage/**/*
      - python/mozbuild/mozbuild/codecoverage/**/*
      - testing/mozharness/mozharness/mozilla/testing/codecoverage.py
    excludes: []
    owners:
      - *marco
    peers:
      - *calixte
      - *jmaher
    meta:
      components:
        - 'Testing :: Code Coverage'

  - name: 'Core: Accessibility'
    description: Support for platform accessibility APIs. Accessibility APIs are used
      by 3rd party software like screen readers, screen magnifiers, and voice dictation
      software, which need information about document content and UI controls, as
      well as important events like changes of focus.
    includes:
      - accessible/**/*
    meta:
      owners_emeritus:
        - Aaron Leventhal
        - Alexander Surkov
      peers_emeritus:
        - David Bolter
        - Trevor Saunders
        - Ginn Chen
        - Yan Evan
        - Yura Zenevich
        - *MarcoZ
      group: dev-accessibility
      url: https://developer.mozilla.org/docs/Web/Accessibility
      components:
        - Core::Disability Access APIs
    owners:
      - *Jamie
    peers:
      - *eeejay
      - *morgan
      - *nlapre
    machine_name: core_accessibility

  - name: 'Core: Animation'
    description: 'Declarative animations: CSS animations, CSS transitions, Web Animations
      API, and off-main thread animations.'
    includes:
      - dom/animation/**/*
      # TODO "and animation-related and interpolation-related code
      # in layout/style, gfx/layers, servo/components/style and servo/ports/gecko
    meta:
      group: dev-platform
      components:
        - Core::DOM::Animation
        - Core::CSS Transitions and Animations
      peers_emeritus:
        - Matt Woodrow
    owners:
      - *birtles
    peers:
      - *boris
      - *hiro
    machine_name: core_animation

  - name: 'Core: Anti-Tracking'
    description: Tracking detection and content-blocking.
    includes:
      - toolkit/components/antitracking/**/*
      # several files under browser/ and netwerk/url-classifier/ **TODO**
    meta:
      peers_emeritus:
        - Ehsan Akhgari
        - Erica Wright
        - Gary Chen
        - *baku
        - Johann Hofmann
      group: dev-platform
      components:
        - 'Core::Privacy: Anti-Tracking'
    owners:
      - *timhuang
    peers:
      - *bvandersloot
      - *dimi
      - *emz
    machine_name: core_antitracking

  - name: 'Core: APZ (Graphics submodule)'
    description: Asynchronous panning and zooming
    includes:
      - gfx/layers/apz/**/*
    meta:
      owners_emeritus:
        - Kartikaya Gupta
      peers_emeritus:
        - Ryan Hunt
      group: dev-platform
      url: https://wiki.mozilla.org/Platform/GFX/APZ
      components:
        - Core::Panning and Zooming
    owners:
      - *botond
    peers:
      - *tnikkel
      - *dlrobertson
      - *hiro
      - *mstange
    machine_name: core_apz_graphics_submodule

  - name: 'Core: Build and Release Tools'
    description: Tools related to build and release automation and configuration of
      release builds.
    includes:
      - tools/update-packaging/**/*
      - tools/update-verify/**/*
    meta:
      owners_emeritus:
        - Aki Sasaki
      group: release-engineering
      url: https://wiki.mozilla.org/ReleaseEngineering
      components:
        - Release Engineering::*
    owners:
      - *jcristau
    peers:
      - *hneiva
      - *bhearsum
      - *gabriel
    machine_name: core_build_and_release_tools

  - name: 'Core: Build Config'
    description: The build system for Gecko and several mozilla.org hosted Gecko-based
      applications.
    includes:
      - build/**/*
      - config/**/*
      - python/mozbuild/**/*
      - browser/config/mozconfigs/**/*
    meta:
      owners_emeritus:
        - Chris Manchester
        - Gregory Szorc
        - Ted Mielczarek
        - Benjamin Smedberg
      peers_emeritus:
        - Ted Mielczarek
        - Ralph Giles
        - Gregory Szorc
        - Chris Manchester
        - Mike Shal
        - Nathan Froyd
        - Ricky Stewart
        - David Major
        - Mitchell Hentges
      group: dev-builds
      url: :ref:`Build System`
      components:
        - Core::Build Config
    owners:
      - *glandium
    peers:
      - *andi
      - *ahochheiden
    submodules:

      - name: 'Core: Build Config - Fennec'
        description: Submodule of the build config covering Fennec's build system
          in mobile/android.
        meta:
          group: dev-builds
          components:
            - Core::Build Config
        owners:
          - *nalexander
        peers: []
        machine_name: core_build_config_fennec

      - name: 'Core: Build Config - Taskgraph'
        description: Support for task-graph generation in decision, action, and cron
          tasks, including configuration of all tasks including those for CI, nightlies,
          and releases; as well as Docker and VM images used to execute those tasks.
        includes:
          - taskcluster
        meta:
          components:
            - Firefox Build System::Task Configuration
          owners_emeritus:
            - Tom Prince
          peers_emeritus:
            - Dustin Mitchell
            - Aki Sasaki
            - Brian Stack
            - Gregory Szorc
            - Justin Wood
        owners:
          - *ahal
        peers:
          - *glandium
          - *jcristau
          - *jlorenzo
          - *jmaher
        machine_name: core_build_config_taskgraph
    machine_name: core_build_config

  - name: 'Core: Code Analysis and Debugging Tools'
    description: Tools for debugging Mozilla code or for analyzing speed, memory use,
      and other characteristics of it.
    includes:
      - tools/performance/**/*
      - tools/rb/**/*
    meta:
      group: dev-performance
      owners_emeritus:
        - David Baron
    owners: []
    peers: []
    machine_name: core_code_analysis_and_debugging_tools

  - name: 'Core: Content Security'
    description: 'Native content-based security features enforced in the ContentSecurityManager,
      including: Content Security Policy (CSP), Mixed Content Blocker (MCB), Referrer
      Policy, Subresource Integrity (SRI), Cross-Origin Resource Sharing (CORS), X-Frame-Options,
      X-Content-Type-Options: nosniff, HTTPS-Only-Mode, Sanitizer API, Sec-Fetch Metadata,
      and top-level data: URI blocking.'
    includes:
      - dom/security/**/*
    meta:
      peers_emeritus:
        - Sid Stamm
        - Jonas Sicking
        - Jonathan Kingston
        - Thomas Nguyen
        - François Marier
        - Tanvi Vyas
        - Andrea Marchesini
      group: dev-security
      components:
        - 'Core::DOM: Security'
    owners:
      - *ckerschb
    peers:
      - *dveditz
      - *freddy
      - *tschuster
    machine_name: core_content_security

  - name: 'Core: Cookies'
    description: ''
    includes:
      - netwerk/cookie/**/*
    meta:
      owners_emeritus:
        - Monica Chew
        - Andrea Marchesini
      peers_emeritus:
        - Josh Matthews
        - Mike Connor
        - Dan Witte
        - Christian Biesinger
        - Shawn Wilsher
        - Ehsan Akhgari
        - Honza Bambas
      group: dev-platform
      components:
        - 'Core::Networking: Cookies'
    owners:
      - *dveditz
      - *timhuang
      - *valentin
    peers:
      - *edgul
      - *emz
      - *tschuster
    machine_name: core_cookies

  - name: 'Core: Crash reporting'
    description: Infrastructure and tools used to generate, submit and process crash
      reports. This includes the in-tree google-breakpad fork, the crash report generation
      machinery as well as the host tools used to dump symbols, analyse minidumps
      and generate stack traces.
    includes:
      - toolkit/crashreporter/**/*
      - toolkit/components/crashes/**/*
      - tools/crashreporter/**/*
      - ipc/glue/CrashReporter*
      - mobile/android/geckoview/src/main/java/org/mozilla/geckoview/CrashReporter.java
    meta:
      peers_emeritus:
        - Aria Beingessner
        - Kris Wright
      group: dev-platform
      url: :ref:`Crash Reporter`
      components:
        - Toolkit::Crash Reporting
    owners:
      - *gsvelto
    peers:
      - *calixte
      - *afranchuk
    machine_name: core_crash_reporting

  - machine_name: core_credentials
    name: 'Core: Credentials'
    description: API Surface for FedCM and WebAuthn
    includes:
      - toolkit/components/credentialmanagement/**/*
      - browser/components/credentialmanager/**/*
      - dom/credentialmanagement/**/*
      - dom/webauthn/**/*
    excludes: []
    owners:
      - *bvandersloot
      - *jschanck
    peers:
      - *timhuang
      - *emz
    meta:
      group: dev-platform
      components:
        - 'Core::DOM: Credential Management'
        - 'Core::DOM: Web Authentication'

  - name: 'Core: C++/Rust usage, tools, and style'
    description: Aspects of C++ use such as language feature usage, standard library
      versions/usage, compiler/toolchain versions, formatting and naming style, and
      aspects of Rust use as needs arise
    includes: []
      # NOTE: more of a "meta module" -- who do we ask when we want to do the above?
      # Includes/globs include where we define coding style, etc... (i.e. documentation)
      # - docs/code-quality/**/*
      # - docs/testing-rust-code/**/*
      # - docs/writing-rust-code/**/*
    meta:
      owners_emeritus:
        - Ehsan Akhgari
      peers_emeritus:
        - Jeff Walden
        - Simon Giesecke
      group: dev-platform
      components:
        - Various
    owners:
      - *bholley
    peers:
      - *botond
      - *glandium
    machine_name: core_crust_usage_tools_and_style

  - name: 'Core: Cycle Collector'
    description: Code to break and collect objects within reference cycles
    includes:
      - xpcom/base/nsCycleCollect*
      # TODO "and some support headers"
      # NOTE: not always clean lines between modules and file boundaries. This module is deeply intertwines with other modules (e.g. XPCOM)
    meta:
      peers_emeritus:
        - David Baron
      group: dev-platform
      components:
        - Core::Cycle Collector
    owners:
      - *mccr8
    peers:
      - *peterv
      - *smaug
    machine_name: core_cycle_collector

  - name: 'Core: DLL Services'
    description: Windows dynamic linker instrumentation and blocking
    includes:
      - toolkit/xre/dllservices/**/*
    meta:
      owners_emeritus:
        - Aaron Klotz
        - *toshi
      peers_emeritus:
        - *molly
      components:
        - Core::DLL Services
    owners:
      - *handyman
    peers:
      - *gstoll
    machine_name: core_dll_services

  - name: 'Core: docshell'
    description: ''
    includes:
      - docshell/**/*
      - uriloader/base/**/*
    meta:
      owners_emeritus:
        - Boris Zbarsky
      peers_emeritus:
        - Johnny Stenback
        - Christian Biesinger
        - Justin Lebar
        - Samael Wang
        - Kyle Machulis
      group: dev-platform
      components:
        - 'Core::DOM: Navigation'
    owners:
      - *smaug
      - *nika
    peers:
      - *peterv
      - *farre
    machine_name: core_docshell

  - name: 'Core: Document Object Model'
    description: ''
    includes:
      - dom/**/*
    exclude_module_paths: true
    meta:
      owners_emeritus:
        - Johnny Stenback
      peers_emeritus:
        - Justin Lebar
        - Jonas Sicking
        - Ben Turner
        - Mounir Lamouri
        - Kyle Huey
        - Bill McCloskey
        - Ben Kelly
        - Blake Kaplan
        - Kyle Machulis
        - Boris Zbarsky
        - Ehsan Akhgari
      group: dev-tech-dom
      url: http://developer.mozilla.org/en/docs/DOM
      components:
        - Core::DOM
        - 'Core::DOM: Core & HTML'
      review_group: dom-core
    owners:
      - *peterv
    peers:
      - *smaug
      - *hsivonen
      - *bholley
      - *baku
      - *mccr8
      - *nika
      - *farre
      - *emilio
      - *asuth
      - *edgar
      - *saschanaz
      - *sefeng
    machine_name: core_document_object_model

  - name: 'Core: DOM File'
    description: DOM Blob, File and FileSystem APIs
    includes:
      - dom/file/**/*
      - dom/filesystem/**/*
    meta:
      group: dev-platform
      url: http://developer.mozilla.org/en/docs/DOM
      components:
        - 'Core::DOM: File'
    owners:
      - *baku
    peers:
      - *smaug
    machine_name: core_dom_file

  - machine_name: core_dom_streams
    name: 'Core: DOM Streams'
    description: Streams Specification implementation
    includes:
      - dom/streams/**/*
    excludes: []
    owners:
      - *saschanaz
    peers:
      - *smaug
      - *evilpie
      - *mgaudet
    meta:
      url: http://developer.mozilla.org/en/docs/DOM
      group: dev-platform
      components:
        - 'Core::DOM: Streams'
      owners_emeritus:
        - *mgaudet
  - name: 'Core: Editor'
    description: ''
    includes:
      - editor/**/*
    meta:
      owners_emeritus:
        - Ehsan Akhgari
      group: dev-platform
      url: :ref:`Editor`
      components:
        - Core::Editor
    owners:
      - *masayuki
    peers:
      - *mkato
    machine_name: core_editor

  - name: 'Core: Event Handling'
    description: DOM Events and Event Handling
    includes:
      - dom/events/**/*
      # TODO "and event handling related code elsewhere"
    meta:
      peers_emeritus:
        - Stone Shih
      group: dev-platform
      url: http://developer.mozilla.org/en/docs/DOM
      components:
        - 'Core::DOM: Events'
        - 'Core::DOM: UI Events & Focus Handling'
    owners:
      - *smaug
      - *masayuki
    peers:
      - *edgar
    machine_name: core_event_handling

  - machine_name: core_firefox_source_documentation
    name: 'Core: Firefox Source Documentation'
    description: The infrastructure platform used to generate Firefox's source documentation,
      excluding editorial control over the content.
    includes:
      - docs/
      - tools/moztreedocs/
    owners:
      - *ahal
    peers:
      - *Sylvestre
      - *marco
    meta:
      components:
        - Developer Infrastructure::Source Documentation
      url: https://firefox-source-docs.mozilla.org/
  - name: 'Core: Gecko Profiler'
    description: Gecko's built-in profiler
    includes:
      - tools/profiler/**/*
      - mozglue/baseprofiler/**/*
    meta:
      owners_emeritus:
        - Benoit Girard
      peers_emeritus:
        - Shu-yu Guo (JS integration)
        - Thinker Lee (TaskTracer)
        - Cervantes Yu (TaskTracer)
        - Nicholas Nethercote
        - Gerald Squelart
        - Kannan Vijayan
        - Beth Rennie
        - Greg Tatum
      group: dev-platform
      url: https://firefox-source-docs.mozilla.org/tools/profiler/
      components:
        - Core::Gecko Profiler
    owners:
      - *mstange
    peers:
      - *canova
      - *florian
      - *jseward
    machine_name: core_gecko_profiler

  - name: 'Core: GeckoView'
    description: Framework for embedding Gecko into Android applications
    includes:
      - mobile/android/**/*
      - widget/android/**/*
      - hal/android/**/*
    excludes:
      - mobile/android/fenix/**/*
      - mobile/android/focus-android/**/*
      - mobile/android/android-components/**/*
    meta:
      owners_emeritus:
        - James Willcox
        - Agi Sferro
      peers_emeritus:
        - Dylan Roeh
        - Eugen Sawin
        - Aaron Klotz
        - Jim Chen
        - Randall E. Barker
        - Jon Almeida
      url: https://wiki.mozilla.org/Mobile/GeckoView
      components:
        - GeckoView::General
    owners:
      - *owlish
    peers:
      - *calu
      - *mkato
      - *olivia
    machine_name: core_geckoview

  - name: 'Core: Global Key Bindings'
    description: Global hot keys for Firefox. Does not include underlined menu accelerators
      and the like, as those are part of i18n.
    includes:
      - dom/events/**/*
      # TODO "and platform specific directories under it"
    meta:
      group: dev-accessibility
      url: https://support.mozilla.org/kb/keyboard-shortcuts-perform-firefox-tasks-quickly
      components:
        - 'Core::Keyboard: Navigation'
      peers_emeritus:
        - Neil Rashbrook
    owners:
      - *masayuki
    machine_name: core_global_key_bindings

  - name: 'Core: Graphics'
    description: Mozilla graphics API
    includes:
      - gfx/**/*
      - dom/canvas/**/*
    meta:
      owners_emeritus:
        - Robert O'Callahan
      peers_emeritus:
        - Benoit Girard(Compositor, Performance)
        - Ali Juma
        - George Wright(Canvas2D)
        - Mason Chang
        - David Anderson
        - Christopher Lord
        - John Daggett(text/fonts)
        - Benoit Jacob(gfx/gl)
        - Joe Drew
        - Vladimir Vukicevic
        - James Willcox(Android)
        - Nick Cameron
        - *rhunt
      group: dev-platform
      url: https://wiki.mozilla.org/Platform/GFX https://wiki.mozilla.org/Gecko:Layers
        https://wiki.mozilla.org/Gecko:2DGraphicsSketch
      components:
        - Core::Graphics
        - 'Core::Graphics: Layers'
        - 'Core::Graphics: Text'
        - 'Core::Graphics: WebRender'
        - 'Core::Graphics: Color Management'
        - 'Core::Graphics: Canvas2D'
        - 'Core::Graphics: CanvasWebGL'
    owners:
      - *jrmuizel
    peers:
      - *nical
      - *jgilbert
      - *mstange
      - *basschouten
      - *jfkthame
      - *sotaro
      - *jnicol
      - *gw
      - *lsalzman
      - *aosmond
    machine_name: core_graphics

  - name: 'Core: HAL'
    description: Hardware Abstraction Layer
    includes:
      - hal/**/*
    meta:
      group: dev-platform
      components:
        - Core::Hardware Abstraction Layer (HAL)
    owners:
      - *gsvelto
    peers: []
    machine_name: core_hal

  - name: 'Core: HTML Parser'
    description: The HTML Parser transforms HTML source code into a DOM. It conforms
      to the HTML specification, and is mostly translated automatically from Java
      to C++.
    includes:
      - parser/html/**/*
    meta:
      group: dev-platform
      url: http://about.validator.nu/
      components:
        - 'Core::HTML: Parser'
    owners:
      - *hsivonen
    peers:
      - *wchen
    machine_name: core_html_parser

  - name: 'Core: I18N Library'
    description: ''
    includes:
      - intl/**/*
    meta:
      owners_emeritus:
        - Jungshik Shin
        - Simon Montagu
      group: dev-i18n
      url: :ref:`Internationalization`
      components:
        - Core::Internationalization
    owners:
      - *hsivonen
      - *jfkthame
    peers:
      - *emk
      - *zbraniecki
      - *mkato
    machine_name: core_i18n_library

  - name: 'Core: ImageLib'
    description: ''
    includes:
      - media/libjpeg/**/*
      - media/libpng/**/*
      - image/**/*
      - modules/zlib/**/*
    meta:
      peers_emeritus:
        - Seth Fowler
        - Brian Bondy
        - Justin Lebar
      group: dev-platform
      components:
        - Core::ImageLib
    owners:
      - *tnikkel
    peers:
      - *aosmond
      - *jrmuizel
    machine_name: core_imagelib

  - name: 'Core: IndexedDB'
    description: ''
    includes:
      - dom/indexedDB/**/*
    meta:
      owners_emeritus:
        - Ben Turner
      peers_emeritus:
        - Jonas Sicking
        - Kyle Huey
        - Bevis Tseng
        - Andrea Marchesini
      group: dev-platform
      url: https://developer.mozilla.org/en/IndexedDB
      components:
        - 'Core::DOM: IndexedDB'
      review_group: dom-storage-reviewers
    owners:
      - *janv
    peers:
      - *asuth
      - *jjalkanen
    machine_name: core_indexeddb

  - name: 'Core: IPC'
    description: Native message-passing between threads and processes
    includes:
      - ipc/glue/**/*
      - ipc/ipdl/**/*
      - ipc/chromium/**/*
    meta:
      owners_emeritus:
        - Chris Jones
        - Bill McCloskey
        - Jed Davis
      peers_emeritus:
        - Benjamin Smedberg
        - Ben Turner
        - David Anderson
        - Kan-Ru Chen
        - Bevis Tseng
        - Ben Kelly
        - *jimm
      group: dev-platform
      components:
        - Core::IPC
      review_group: ipc-reviewers
    owners:
      - *nika
    peers:
      - *jld
      - *mccr8
      - *handyman
    machine_name: core_ipc

  - name: 'Core: JavaScript'
    description: JavaScript engine (SpiderMonkey)
    includes:
      - js/src/**/*
    meta:
      owners_emeritus:
        - Brendan Eich
        - Dave Mandelin
        - Luke Wagner
        - Jason Orendorff
      peers_emeritus:
        - Andreas Gal
        - Ashley Hauck
        - Bill McCloskey
        - Blake Kaplan
        - Brian Hackett
        - Caroline Cullen
        - Dan Gohman
        - David Anderson
        - Eddy Bruel
        - Eric Faust
        - Hannes Verschore
        - Igor Bukanov
        - Jeff Walden
        - Kannan Vijayan
        - Nicholas Nethercote
        - Nick Fitzgerald
        - Niko Matsakis
        - Shu-yu Guo
        - Till Schneidereit
      url: https://spidermonkey.dev/
      components:
        - Core::JavaScript Engine
        - 'Core::JavaScript: GC'
        - 'Core::JavaScript: Internationalization API'
        - 'Core::JavaScript: Standard Library'
        - 'Core::JavaScript: WebAssembly'
        - Core::js-ctypes
    owners:
      - *jandem
    peers:
      - *allstarschh
      - *anba
      - *arai
      - *bholley
      - *bthrall
      - *dminor
      - *evilpie
      - *iain
      - *jonco
      - *jseward
      - *mgaudet
      - *nbp
      - *rhunt
      - *sfink
      - *tcampbell
      - *yulia
      - *yury
    machine_name: core_javascript

  - name: 'Core: JavaScript JIT'
    description: JavaScript engine's JIT compilers (IonMonkey, Baseline)
    includes:
      - js/src/jit/**/*
    meta:
      peers_emeritus:
        - Benjamin Bouvier
        - Brian Hackett
        - Caroline Cullen
        - David Anderson
        - Hannes Verschore
        - Kannan Vijayan
        - Luke Wagner
        - Shu-yu Guo
        - Sean Stangl
      url: https://spidermonkey.dev/
      components:
        - 'Core::JavaScript Engine: JIT'
    owners:
      - *jandem
    peers:
      - *anba
      - *tcampbell
      - *mgaudet
      - *iain
      - *nbp
      - *evilpie
    machine_name: core_javascript_jit

  - name: 'Core: Layout Engine'
    description: rendering tree construction, layout (reflow), etc.
    includes:
      - layout/**/*
      - layout/base/**/*
      - layout/build/**/*
      - layout/forms/**/*
      - layout/generic/**/*
      - layout/printing/**/*
      - layout/tables/**/*
      - layout/tools/**/*
    meta:
      owners_emeritus:
        - David Baron
      peers_emeritus:
        - Matt Woodrow
        - Boris Zbarsky
        - Robert O'Callahan
        - Mats Palmgren
      group: dev-platform
      url: https://wiki.mozilla.org/Gecko:Overview#Layout
      components:
        - Core::Layout
        - 'Core::Layout: Block and Inline'
        - 'Core::Layout: Columns'
        - 'Core::Layout: Flexbox'
        - 'Core::Layout: Floats'
        - 'Core::Layout: Form Controls'
        - 'Core::Layout: Generated Content, Lists, and Counters'
        - 'Core::Layout: Grid'
        - 'Core::Layout: Images, Video, and HTML Frames'
        - 'Core::Layout: Positioned'
        - 'Core::Layout: Ruby'
        - 'Core::Layout: Scrolling and Overflow'
        - 'Core::Layout: Tables'
        - 'Core::Layout: Text and Fonts'
        - Core::Print Preview
        - 'Core::Printing: Output'
    owners:
      - *dholbert
    peers:
      - *jfkthame
      - *tnikkel
      - *xidorn
      - *emilio
      - *TYLin
      - *jwatt
    machine_name: core_layout_engine

  - name: 'Core: Legacy HTML Parser'
    description: ''
    includes:
      - parser/htmlparser/**/*
    meta:
      peers_emeritus:
        - Johnny Stenback
        - David Baron
      owners_emeritus:
        - Blake Kaplan
      url: http://www.mozilla.org/newlayout/doc/parser.html
      components:
        - 'Core::HTML: Parser'
    owners: []
    peers:
      - *peterv
      - *rbs
    machine_name: core_legacy_html_parser

  - name: 'Core: libjar'
    description: The JAR handling code (protocol handler, stream implementation, and
      zipreader/zipwriter).
    includes:
      - modules/libjar/**/*
    meta:
      owners_emeritus:
        - Taras Glek
        - Michael Wu
        - Aaron Klotz
      peers_emeritus:
        - Michal Novotny
      group: dev-platform
      components:
        - 'Core::Networking: JAR'
    owners:
      - *valentin
    peers:
      - *kershaw
      - *jesup
    machine_name: core_libjar

  - machine_name: core_machine_learning
    name: 'Core: Machine Learning'
    description: AI services and local inference
    includes:
      - browser/components/genai/
      - toolkit/components/aboutinference/
      - toolkit/components/ml/
    meta:
      components:
        - Core::Machine Learning
    owners:
      - *Mardak
      - *tarek
    peers:
      - *gregtatum

  - name: 'Core: MathML'
    description: MathML is a low-level specification for describing mathematics which
      provides a foundation for the inclusion of mathematical expressions in Web pages.
    includes:
      - layout/mathml/**/*
    meta:
      group: dev-tech-mathml
      url: https://developer.mozilla.org/docs/Web/MathML
      components:
        - Core::MathML
      peers_emeritus:
        - Robert O'Callahan
      owners_emeritus:
        - *karlt
    owners:
      - *emilio
    peers:
      - *fredwang
      - *jfkthame
    machine_name: core_mathml

  - name: 'Core: Media Playback'
    description: HTML Media APIs, including Media Source Extensions and non-MSE video/audio
      element playback, and Encrypted Media Extensions. (WebRTC and WebAudio not included).
    includes:
      - dom/media/**/*
      - media/gmp-clearkey/**/*
      - media/libcubeb/**/*
      - media/libnestegg/**/*
      - media/libogg/**/*
      - media/libopus/**/*
      - media/libtremor/**/*
      - media/libvorbis/**/*
      - media/libvpx/**/*
      - dom/media/platforms/omx/**/*
      - dom/media/gmp/rlz/**/*
    meta:
      owners_emeritus:
        - Robert O'Callahan
        - Chris Pearce
        - Jean-Yves Avenard
      group: dev-media
      components:
        - Core::Audio/Video
    peers:
      - *kinetik
      - *jhlin
      - *alwu
      - *padenot
      - *chunmin
    machine_name: core_media_playback

  - name: 'Core: Media Transport'
    description: Pluggable transport for real-time media
    includes:
      - dom/media/webrtc/transport/**/*
    meta:
      group: dev-media
      components:
        - Core::WebRTC::Networking
      owners_emeritus:
        - Eric Rescola
      peers_emeritus:
        - Adam Roach
    owners: []
    peers:
      - *bwc
      - *nohlmeier
    machine_name: core_media_transport

  - name: 'Core: Memory Allocator'
    description: Most things related to memory allocation in Gecko, including jemalloc,
      replace-malloc, DMD (dark matter detector), logalloc, etc.
    includes:
      - memory/**/*
    meta:
      peers_emeritus:
        - Eric Rahm
        - Nicholas Nethercote
      group: dev-platform
      components:
        - Core::DMD
        - Core::jemalloc
    owners:
      - *glandium
    peers:
      - *pbone
    machine_name: core_memory_allocator

  - name: 'Core: mfbt'
    description: mfbt is a collection of headers, macros, data structures, methods,
      and other functionality available for use and reuse throughout all Mozilla code
      (including SpiderMonkey and Gecko more broadly).
    includes:
      - mfbt/**/*
    meta:
      group: dev-platform
      components:
        - Core::MFBT
      owners_emeritus:
        - Jeff Walden
      peers_emeritus:
        - *Ms2ger
    owners:
      - *glandium
    peers: []
    machine_name: core_mfbt

  - name: 'Core: Moz2D (Graphics submodule)'
    description: Platform independent 2D graphics API
    includes:
      - gfx/2d/**/*
    meta:
      group: dev-platform
      url: https://wiki.mozilla.org/Platform/GFX/Moz2D
      components:
        - Core::Graphics
    owners:
      - *basschouten
    peers:
      - *jrmuizel
      - *jwatt
    machine_name: core_moz2d_graphics_submodule

  - name: 'Core: Mozglue'
    description: Glue library containing various low-level functionality, including
      a dynamic linker for Android, a DLL block list for Windows, etc.
    includes:
      - mozglue/**/*
    meta:
      group: dev-platform
      components:
        - Core::mozglue
      peers_emeritus:
        - Kartikaya Gupta (mozglue/android)
    owners:
      - *glandium
    peers:
      - *jchen
    machine_name: core_mozglue

  - name: 'Core: MSCOM'
    description: Integration with Microsoft Distributed COM
    includes:
      - ipc/mscom/**/*
    meta:
      owners_emeritus:
        - Aaron Klotz
        - *Jamie
      peers_emeritus:
        - *jimm
      group: dev-platform
      components:
        - 'Core::IPC: MSCOM'
    owners:
      - *handyman
    peers:
      - *Jamie
      - *nrishel
    machine_name: core_mscom

  - name: 'Core: Necko'
    description: The Mozilla Networking Library
    includes:
      - netwerk/**/*
      - netwerk/base/**/*
      - netwerk/build/**/*
      - netwerk/cache2/**/*
      - netwerk/dns/**/*
      - netwerk/locales/**/*
      - netwerk/mime/**/*
      - netwerk/protocol/**/*
      - netwerk/socket/**/*
      - netwerk/streamconv/**/*
      - netwerk/system/**/*
      - netwerk/test/**/*
      - dom/fetch/**/*
      - dom/xhr/**/*
      - dom/network/**/*
      - dom/websocket/**/*
      - uriloader/prefetch/**/*
      - uriloader/preload/**/*
    meta:
      owners_emeritus:
        - Dragana Damjanovic
        - Patrick McManus
        - Christian Biesinger
      peers_emeritus:
        - Michal Novotny
        - Honza Bambas
        - Shih-Chiang Chien
        - Boris Zbarsky
        - Steve Workman
        - Nick Hurley
        - Daniel Stenberg
        - Jason Duell
        - Junior Hsu
      group: dev-tech-network
      url: :ref:`Networking`
      components:
        - Core::Networking
        - 'Core::Networking: Cache'
        - 'Core::Networking: Cookies'
        - 'Core::Networking: File'
        - 'Core::Networking: HTTP'
        - 'Core::Networking: JAR'
        - 'Core::Networking: Proxy'
        - 'Core::Networking: Websockets'
        - 'Core::DOM: Networking'
    owners:
      - *valentin
    peers:
      - *kershaw
      - *jesup
    machine_name: core_necko

  - name: 'Core: NodeJS usage, tools, and style'
    description: Advises on the use of NodeJS and npm packages at build and runtime.
      Reviews additions/upgrades/removals of vendored npm packages. Works with appropriate
      teams to maintain automated license and security audits of npm packages. Works
      with the security team and relevant developers to respond to vulnerabilities
      in NodeJS and vendored npm packages.
    includes:
      - package.json
      - package-lock.json
      - node_modules/**/*
        # TODO: and others as appropriate
    meta:
      components:
        - Various
      url: 'https://wiki.mozilla.org/Firefox/firefox-dev, #nodejs on slack'
      peers_emeritus:
        - Kate Hudson
    owners:
      - *dmosedale
    peers:
      - *standard8
      - *dcoates
      - *Mardak
      - *mossop
    machine_name: core_nodejs_usage_tools_and_style

  - name: 'Core: Notifications'
    description: The infrastructure for Web Notifications API and the Firefox Desktop
      notifications.
    includes:
      - dom/notification/**/*
      - toolkit/components/alerts/**/*
      - toolkit/system/gnome/nsAlertsIconListener*
      - toolkit/system/gnome/nsSystemAlertsService*
      - widget/windows/ToastNotification*
      - widget/cocoa/OSXNotificationCenter*
    meta:
      group: dev-platform
      components:
        - 'Core::DOM: Notifications'
        - Toolkit::Alerts Service
    owners:
      - *saschanaz
    peers:
      - *nalexander
      - *nrishel
    machine_name: core_notifications

  - name: 'Core: NSPR'
    description: Netscape Portable Runtime
    includes:
      - nsprpub/**/*
    meta:
      owners_emeritus:
        - Wan-Teh Chang
      group: dev-tech-nspr
      url: :ref:`NSPR`
      components:
        - NSPR
    owners:
      - *KaiE
    peers:
      - *glandium
    machine_name: core_nspr

  - name: 'Core: PDF'
    description: Rendering code to display documents encoded in the ISO 32000-1 PDF
      format.
    includes:
      - toolkit/components/pdfjs/**/*
    meta:
      owners_emeritus:
        - Brendan Dahl
      peers_emeritus:
        - Artur Adib
        - Vivien Nicolas
      group: dev-platform
      url: https://github.com/mozilla/pdf.js
      components:
        - Core::PDF
    owners:
      - *calixte
    peers:
      - *marco
    machine_name: core_pdf

  - name: 'Core: Permissions'
    description: ''
    includes:
      - extensions/permissions/**/*
    meta:
      owners_emeritus:
        - Monica Chew
        - Ehsan Akhgari
      peers_emeritus:
        - Josh Matthews
        - Mike Connor
        - Dan Witte
        - Christian Biesinger
        - Shawn Wilsher
        - Honza Bambas
        - *baku
        - Johann Hofmann
      group: dev-platform
      components:
        - 'Core :: Permission Manager'
      review_group: permissions-reviewers
    owners:
      - *timhuang
    peers:
      - *emz
    machine_name: core_permissions

  - name: 'Core: Plugins'
    description: ' NPAPI Plugin support.'
    includes:
      - dom/plugins/**/*
    meta:
      owners_emeritus:
        - *jimm
      peers_emeritus:
        - Josh Aas
        - John Schoenick
        - Robert O'Callahan
        - Johnny Stenback
        - Benjamin Smedberg
      url: https://wiki.mozilla.org/Plugins
      components:
        - Core::Plug-ins
    owners:
      - *handyman
    machine_name: core_plugins

  - name: 'Core: Preferences'
    description: Preference library
    includes:
      - modules/libpref/**/*
    meta:
      owners_emeritus:
        - Nicholas Nethercote
        - *KrisWright
      peers_emeritus:
        - Felipe Gomes
        - Eric Rahm
      group: dev-platform
      components:
        - 'Core::Preferences: Backend'
    owners:
    peers:
      - *glandium
    machine_name: core_preferences

  - name: 'Core: Private Browsing'
    description: Implementation of the Private Browsing mode, and the integration
      of other modules with Private Browsing APIs.
    includes:
    # TODO
    # Implementation and consumers of Private Browsing APIs in nsILoadContext,
    # nsIPrivateBrowsingChannel, PrivateBrowsingUtils.sys.mjs and the related glue code.'
    meta:
      owners_emeritus:
        - Ehsan Akhgari
        - Johann Hofmann
      peers_emeritus:
        - Josh Matthews
      group: dev-platform
      url: https://wiki.mozilla.org/Private_Browsing
      components:
        - Firefox::Private Browsing
    owners:
      - *timhuang
    peers:
      - *timhuang
    machine_name: core_private_browsing

  - name: 'Core: Privilege Manager'
    description: Caps is the capabilities-based security system.
    includes:
      - caps/**/*
    meta:
      peers_emeritus:
        - Boris Zbarsky
        - Brendan Eich
        - Johnny Stenback
        - Dan Veditz
      group: dev-tech-dom
      url: http://www.mozilla.org/projects/security/components/index.html
      components:
        - 'Core::Security: CAPS'
    owners:
      - *bholley
    peers:
      - *ckerschb
    machine_name: core_privilege_manager

  - name: 'Core: Push Subscriptions'
    description: Push is a way for application developers to send messages to their
      web applications.
    includes:
      - dom/push/**/*
      - dom/interfaces/push/**/*
    meta:
      owners_emeritus:
        - Doug Turner
        - Lina Cambridge
      peers_emeritus:
        - Nikhil Marathe
        - Dragana Damjanovic
        - *mt
      components:
        - 'Core::DOM: Push Subscriptions'
    peers: []
    machine_name: core_push_subscriptions

  - name: 'Core: Sandboxing (Linux)'
    description: Sandboxing for the Linux platform
    includes:
      - security/sandbox/linux/**/*
    meta:
      group: dev-platform
      url: https://wiki.mozilla.org/Security/Sandbox
      components:
        - 'Core::Security: Process Sandboxing'
    owners:
      - *jld
    peers:
      - *gcp
    machine_name: core_sandboxing_linux

  - name: 'Core: Sandboxing (OSX)'
    description: Sandboxing for the OSX platform
    includes:
      - security/sandbox/mac/**/*
    meta:
      group: dev-platform
      url: https://wiki.mozilla.org/Security/Sandbox
      components:
        - 'Core::Security: Process Sandboxing'
    owners:
      - *haik
    peers: []
    machine_name: core_sandboxing_osx

  - name: 'Core: Sandboxing (Windows)'
    description: Sandboxing for the Windows platform
    includes:
      - security/sandbox/win/**/*
    meta:
      owners_emeritus:
        - Tim Abraldes
      peers_emeritus:
        - Brian Bondy
        - Aaron Klotz
        - *jimm
        - *toshi
      group: dev-platform
      url: https://wiki.mozilla.org/Security/Sandbox
      components:
        - 'Core::Security: Process Sandboxing'
    owners:
      - *bobowen
    peers:
      - *handyman
    machine_name: core_sandboxing_windows

  - name: 'Core: security'
    description: Crypto/PKI code, including NSS (Network Security Services) and JSS
      (NSS for Java)
    includes:
      - security/nss/**/*
    meta:
      owners_emeritus:
        - Wan-Teh Chang
        - Tim Taubert
        - J.C. Jones
      peers_emeritus:
        - Elio Maldonado
        - Franziskus Kiefer
        - Kevin Jacobs
      group: dev-tech-crypto
      url: :ref:`Network Security Services (NSS)`
      components:
        - NSS
        - JSS
        - Core::Security
        - 'Core::Security: S/MIME'
    owners:
      - *beurdouche
      - *rrelyea
      - *mt
    peers:
      - *KaiE
      - *ryansleevi
      - *ekr
      - *ueno
      - *annaweine
      - *djackson
      - *jschanck
    machine_name: core_security

  - name: 'Core: Security - Mozilla PSM Glue'
    description: Personal Security Manager
    includes:
      - security/manager/**/*
    meta:
      owners_emeritus:
        - Kai Engert (2001-2012)
      group: dev-tech-crypto
      components:
        - 'Core::Security: PSM'
      peers_emeritus:
        - Honza Bambas
        - Cykesiopka
        - Franziskus Kiefer
    owners:
      - *keeler
    peers:
      - *jschanck
    machine_name: core_security_mozilla_psm_glue

  - machine_name: core_security_rlbox
    name: Security - RLBox
    description: Sandboxing using WASM/RLBox libraries.
    includes:
      - security/rlbox
      - third_party/rlbox
      - third_party/rlbox_wasm2c_sandbox
    excludes: []
    owners:
      - *shravanrn
    peers:
      - *glandium
      - *tjr
      - *deian
    meta:
      components:
        - 'Core::Security: RLBox'
  - name: 'Core: Static analysis & rewriting for C++'
    description: Tools for checking C++ code looking for problems at compile time,
      plus tools for automated rewriting of C++ code.
    includes:
      - build/clang-plugin/**/*
      - tools/rewriting/**/*
      # TODO among other out of tree tools
    meta:
      peers_emeritus:
        - Birunthan Mohanathas
        - Ehsan Akhgari
      group: dev-platform
      components:
        - Core::Rewriting & Analysis
    owners:
      - *andi
    peers:
      - *nika
      - *sfink
      - *jrmuizel
    machine_name: core_static_analysis_rewriting_for_c

  - name: 'Core: SQLite and Embedded Database Bindings'
    description: Embedded database engines and their code bindings.
    includes:
      - storage/**/*
      - third_party/sqlite3/**/*
      - toolkit/components/kvstore/**/*
      - toolkit/modules/Sqlite.sys.mjs
    meta:
      group: dev-platform
      url: https://firefox-source-docs.mozilla.org/storage/index.html
      components:
        - Core::SQLite and Embedded Database Bindings
      owners_emeritus:
        - Shawn Wilsher
    owners:
      - *mak
    peers:
      - *asuth
      - *janv
      - *lina
    machine_name: core_storage

  - name: 'Core: String'
    description: ''
    includes:
      - xpcom/string/**/*
    meta:
      group: dev-tech-xpcom
      url: :ref:`String Guide`
      components:
        - Core::String
      owners_emeritus:
        - David Baron
      peers_emeritus:
        - Eric Rahm
    owners: []
    peers: []
    machine_name: core_string

  - name: 'Core: Style System'
    description: CSS style sheet handling; style data computation
    includes:
      - layout/style/**/*
      - servo/**/*
    meta:
      owners_emeritus:
        - David Baron
        - Cameron McCormack
      peers_emeritus:
        - Boris Zbarsky
      group: dev-platform
      url: https://wiki.mozilla.org/Gecko:Overview#Style_System
      components:
        - Core::CSS Parsing and Computation
        - 'Core::DOM: CSS Object Model'
    owners:
      - *emilio
    peers:
      - *bholley
      - *xidorn
      - *boris
      - *dshin
      - *tlouw
      - *zrhoffman
    machine_name: core_style_system

  - name: 'Core: Supply Chain'
    description: Policy management for third-party Rust dependencies
    includes:
      - supply-chain/**/*
    meta:
      peers_emeritus:
        - Aria Beingessner
      group: dev-platform
      components:
        - Firefox Build System::General
    owners:
      - *bholley
    peers:
      - *nika
      - *tjr
    machine_name: core_supply_chain

  - name: 'Core: SVG'
    description: Scalable Vector Graphics
    includes:
      - dom/svg/**/*
      - layout/svg/**/*
      - dom/smil/**/*
    meta:
      group: dev-tech-svg
      url: https://developer.mozilla.org/docs/Web/SVG
      components:
        - Core::SVG
      peers_emeritus:
        - Robert O'Callahan
    owners:
      - *jwatt
    peers:
      - *longsonr
      - *dholbert
      - *birtles
    machine_name: core_svg

  - name: 'Core: UA String'
    description: User Agent String
    includes:
      - netwerk/protocol/http/**/*
    meta:
      group: dev-platform
      url: https://developer.mozilla.org/docs/Web/HTTP/Headers/User-Agent/Firefox
      components:
        - 'Core::Networking: HTTP'
    owners:
      - *tantek
    peers:
      - *cpeterson
      - *hsivonen
    machine_name: core_ua_string

  - name: 'Core: View System'
    description: The View Manager is responsible for handling "heavyweight" rendering
      (some clipping, compositing) and event handling tasks.
    includes:
      - view/**/*
    meta:
      owners_emeritus:
        - Robert O'Callahan
      peers_emeritus:
        - David Baron
        - Boris Zbarsky
      group: dev-platform
      components:
        - 'Core::Layout: View Rendering'
    owners:
      - *tnikkel
    peers:
      - *mstange
    machine_name: core_view_system

  - name: 'Core: Web Audio'
    description: Support for the W3C Web Audio API specification.
    includes:
      - dom/media/webaudio/**/*
    meta:
      owners_emeritus:
        - Ehsan Akhgari
      peers_emeritus:
        - Robert O'Callahan
      group: dev-platform
      url: https://wiki.mozilla.org/Web_Audio_API
      components:
        - Core::Web Audio
    owners:
      - *padenot
    peers:
      - *karlt
    machine_name: core_web_audio

  - name: 'Core: Web Painting'
    description: painting, display lists, and layer construction
    includes:
      - layout/painting/**/*
      # TODO: "the display list and layer related methods on nsIFrame and its subclasses"
    meta:
      group: dev-platform
      url: :ref:`Style system (CSS) & Layout`
      components:
        - 'Core::Layout: Web Painting'
      owners_emeritus:
        - Matt Woodrow
      peers_emeritus:
        - David Baron
        - Robert O'Callahan
    owners:
    peers:
      - *tnikkel
      - *mstange
      - *mikokm
      - *jnicol
    machine_name: core_web_painting

  - name: 'Core: Web Workers'
    description: ''
    includes:
      - dom/serviceworkers/**/*
      - dom/workers/**/*
    meta:
      owners_emeritus:
        - Ben Turner
        - Andrea Marchesini
      peers_emeritus:
        - Blake Kaplan
        - Jonas Sicking
        - Kyle Huey
        - Ben Kelly
        - Yaron Tausky
      group: dev-platform
      url: https://developer.mozilla.org/docs/Web/API/Web_Workers_API/Using_web_workers
      components:
        - 'Core::DOM: Workers'
      review_group: dom-worker-reviewers
    owners:
      - *asuth
    peers:
      - *edenchuang
      - *jstutte
      - *smaug
    machine_name: core_web_workers

  - name: 'Core: WebGPU (Graphics submodule)'
    description: WebGPU implementation
    includes:
      - dom/webidl/WebGPU.webidl
      - dom/webgpu/**/*
      - gfx/wgpu_bindings/**/*
      - third_party/rust/naga/**/*
      - third_party/rust/wgpu-core/**/*
      - third_party/rust/wgpu-hal/**/*
      - third_party/rust/wgpu-types/**/*
      - testing/web-platform/tests/webgpu/**/*
      - testing/web-platform/meta/webgpu/**/*
      - testing/web-platform/mozilla/tests/webgpu/**/*
      - testing/web-platform/mozilla/meta/webgpu/**/*
    meta:
      group: dev-platform
      url: https://wiki.mozilla.org/Platform/GFX/WebGPU
      components:
        - Core::Graphics::WebGPU
      review_group: webgpu-reviewers
      owners_emeritus:
        - *kvark
      peers_emeritus:
        - *josh
    owners:
      - *jimb
    peers:
      - *ErichDonGubler
      - *jgilbert
      - *nical
      - *teoxoy
    machine_name: core_webgpu_graphics_submodule

  - name: 'Core: WebRTC'
    description: WebRTC is responsible for realtime audio and video communication,
      as well as related issues like low-level camera and microphone access
    includes:
      - netwerk/sctp/**/*
      # TODO: also see submodules "WebRTC Media" and "WebRTC Signaling"
    meta:
      peers_emeritus:
        - Ethan Hugg
        - Eric Rescola
        - Adam Roach
      group: dev-media
      url: https://wiki.mozilla.org/Media/webrtc
      components:
        - Core::WebRTC
        - Core::WebRTC Networking
    owners:
      - *jesup
    peers:
      - *bwc
    machine_name: core_webrtc

  - name: 'Core: WebVR'
    description: Gecko's implementation of WebVR (Virtual Reality) functionality,
      including API, devices, graphics and integration
    includes:
      - dom/vr/**/*
      - gfx/vr/**/*
    meta:
      peers_emeritus:
        - Vladimir Vukicevic
        - Imanol Fernández
      group: dev-platform
      url: https://mozvr.com/
      components:
        - Core::WebVR
    owners:
      - *kip
    peers:
      - *daoshengmu
    submodules:

      - name: 'Core: WebRTC Media'
        description: Submodule of WebRTC responsible for access to media input devices
          (microphones, cameras, screen capture), as well as realtime audiovisual
          codecs and packetization.
        includes:
          - media/webrtc/**/*
          - dom/media/webrtc/**/*
          - dom/media/systemservices/**/*
        meta:
          peers_emeritus:
            - Paul Kerr
            - Ethan Hugg
          group: dev-media
          url: https://wiki.mozilla.org/Media/webrtc
          components:
            - Core::WebRTC (Audio/Video)
        owners:
          - *jesup
        peers:
          - *jib
          - *dminor
          - *pehrsons
        machine_name: core_webrtc_media

      - name: 'Core: WebRTC Signaling'
        description: Submodule of WebRTC responsible for implementation of PeerConnection
          API, WebRTC identity, and SDP/JSEP handling
        includes:
          - media/webrtc/signaling/**/*
        meta:
          peers_emeritus:
            - Ethan Hugg
            - Eric Rescola
            - Adam Roach
            - *nohlmeier
          group: dev-media
          url: https://wiki.mozilla.org/Media/webrtc
          components:
            - Core::WebRTC (Signaling)
        owners:
          - *bwc
        peers:
          - *jesup
        machine_name: core_webrtc_signaling
    machine_name: core_webvr

  - name: 'Core: Widget'
    description: Top level Widget
    includes:
      - widget/**/*
    meta:
      owners_emeritus:
        - Vladimir Vukicevic
        - Robert O'Callahan
        - *jimm
      peers_emeritus:
        - Stuart Parmenter
      group: dev-platform
      components:
        - Core::Drag and Drop
        - Core::Widget
        - 'Core::Printing: Setup'
    owners:
      - *spohl
    machine_name: core_widget

  - name: 'Core: Widget - Android'
    description: This is part of the [https://wiki.mozilla.org/Modules/Core#GeckoView
      GeckoView] module.
    meta: {}
    owners:
      - *agi
    machine_name: core_widget_android

  - name: 'Core: Widget - GTK'
    description: GTK widget support
    includes:
      - widget/gtk/**/*
    meta:
      owners_emeritus:
        - Robert O'Callahan
      group: dev-platform
      url: http://www.mozilla.org/ports/gtk/
      components:
        - 'Core::Widget: Gtk'
    owners:
      - *karlt
    peers:
      - *stransky
      - *emilio
    machine_name: core_widget_gtk

  - name: 'Core: Widget - Headless'
    description: Headless widget support
    includes:
      - widget/headless/**/*
    meta:
      group: dev-platform
      components:
        - Firefox::Headless
      owners_emeritus:
        - Brendan Dahl
    owners: []
    peers: []
    machine_name: core_widget_headless

  - name: 'Core: Widget - macOS'
    description: ' macOS widget support'
    includes:
      - widget/cocoa/**/*
    meta:
      owners_emeritus:
        - Robert O'Callahan
        - Markus Stange
      peers_emeritus:
        - Josh Aas
        - Benoit Girard
        - Steven Michaud
      group: dev-platform
      components:
        - 'Core::Widget: Cocoa'
    owners:
      - *spohl
    peers:
      - *mstange
      - *haik
    machine_name: core_widget_macos

  - name: 'Core: Widget - Windows'
    description: Windows widget support
    includes:
      - widget/windows/**/*
    meta:
      owners_emeritus:
        - *jimm
      peers_emeritus:
        - Rob Strong
        - Vladimir Vukicevic
        - Brad Lassey
        - Brian Bondy
        - Christian Biesinger
        - Doug Turner
        - Josh 'timeless' Soref
        - Rob Arnold
        - Aaron Klotz
        - Neil Rashbrook
        - *toshi
      group: dev-platform
      components:
        - 'Core::Widget: Win32'
    owners:
      - *cmartin
    peers:
      - *handyman
      - *molly
    machine_name: core_widget_windows

  - name: 'Core: XML'
    description: XML in Mozilla, including XML, XHTML, Namespaces in XML, Associating
      Style Sheets with XML Documents, XML Linking and XML Extras. XML-related things
      that are not covered by more specific projects.
    includes:
      - dom/xml/**/*
      - parser/expat/**/*
    meta:
      peers_emeritus:
        - Jonas Sicking
        - Johnny Stenback
        - Boris Zbarsky
        - Eric Rahm
      group: dev-tech-xml
      components:
        - Core::XML
    owners:
      - *peterv
    peers: []
    machine_name: core_xml

  - name: 'Core: XPApps'
    description: Cross-Platform Applications, mostly Navigator front end and application
      shell.
    includes:
      - xpfe/**/*
    meta:
      group: dev-apps-seamonkey
      owners_emeritus:
        - Neil Rashbrook
      peers_emeritus:
        - Josh 'timeless' Soref
    peers:
      - *deanis74
    machine_name: core_xpapps

  - name: 'Core: XPCOM'
    description: The cross-platform object model and core data structures.
    includes:
      - startupcache/**/*
      - xpcom/**/*
      - xpcom/base/**/*
      - xpcom/build/**/*
      - xpcom/components/**/*
      - xpcom/docs/**/*
      - xpcom/ds/**/*
      - xpcom/glue/**/*
      - xpcom/reflect/**/*
      - xpcom/rust/**/*
      - xpcom/system/**/*
      - xpcom/tests/**/*
      - xpcom/threads/**/*
      - xpcom/windbgdlg/**/*
    meta:
      owners_emeritus:
        - Benjamin Smedberg
      peers_emeritus:
        - Doug Turner
        - Eric Rahm
        - Simon Giesecke
        - *KrisWright
      group: dev-platform
      url: :ref:`XPCOM`
      components:
        - Core::XPCOM
      review_group: xpcom-reviewers
    owners:
      - *nika
    peers:
      - *kmag
      - *beth
      - *jstutte
      - *mccr8
      - *emilio
    machine_name: core_xpcom

  - name: 'Core: XPConnect'
    description: Deep Magic
    includes:
      - js/xpconnect/**/*
    meta:
      peers_emeritus:
        - Boris Zbarsky
        - Blake Kaplan
        - Andreas Gal
        - Johnny Stenback
        - Gabor Krizsanits
      components:
        - Core::XPConnect
    owners:
      - *bholley
    peers:
      - *peterv
      - *mccr8
      - *kmag
      - *nika
    machine_name: core_xpconnect

  - name: 'Core: XPIDL'
    description: Cross-platform IDL compiler; produces .h C++ header files and .xpt
      runtime type description files from .idl interface description files.
    includes:
      - xpcom/idl-parser/**/*
      - xpcom/xpidl/**/*
    meta:
      owners_emeritus:
        - Kyle Huey
      peers_emeritus:
        - Mike Shaver
        - Josh 'timeless' Soref
      group: dev-tech-xpcom
      url: :ref:`XPIDL`
    owners:
      - *nika
    peers:
      - *mccr8
    machine_name: core_xpidl

  - name: 'Core: XSLT Processor'
    description: XSLT transformations processor
    includes:
      - dom/xslt/**/*
    meta:
      peers_emeritus:
        - Jonas Sicking
        - Axel Hecht
        - Eric Rahm
      group: dev-tech-xslt
      url: https://developer.mozilla.org/docs/Web/XSLT
      components:
        - Core::XSLT
    owners:
      - *peterv
    peers: []
    machine_name: core_xslt_processor

  - name: Desktop Firefox
    description: Standalone Web Browser.
    includes:
      - browser/**/*
      - toolkit/**/*
    meta:
      peers_emeritus:
        - Brian Bondy
        - Lina Cambridge
        - Luke Chang
        - Ricky Chien
        - Justin Dolske
        - Georg Fritzsche
        - Felipe Gomes
        - Tim Guan-tin Chien
        - Johann Hofmann
        - Molly Howell
        - KM Lee Rex
        - Fred Lin
        - Ray Lin
        - Fischer Liu
        - Bill McCloskey
        - Mark Mentovai
        - Ted Mielczarek
        - Brian Nicholson
        - Matthew Noorenberghe
        - Neil Rashbrook
        - Asaf Romano
        - Marina Samuel
        - J Ryan Stinnett
        - Gregory Szorc
        - Tim Taubert
        - Jared Wein
      group: firefox-dev
      components:
        - Firefox
        - Toolkit
    owners:
      - *mossop
      - *Gijs
    peers:
      - *dao
      - *mak
      - *mconley
      - *sclements
    submodules:

      - name: Add-ons Manager
        description: Extension management back-end.
        includes:
          - toolkit/mozapps/extensions/**/*
        meta:
          owners_emeritus:
            - Robert Strong
            - Andrew Swan
            - Kris Maglione
        owners:
          - *scaraveo
          - *rpl
        peers:
          - *rpl
          - *zombie
          - *robwu
          - *willdurand
        machine_name: addons_manager

      - name: Add-ons Manager UI
        description: about:addons.
        includes:
          - toolkit/mozapps/extensions/content/**/*
        meta:
          owners_emeritus:
            - Robert Strong
            - Andrew Swan
        owners:
          - *scaraveo
          - *mstriemer
        peers:
          - *rpl
          - *zombie
          - *robwu
          - *willdurand
        machine_name: addons_manager_ui

      - machine_name: address_bar
        name: Address Bar
        description: The address bar and address bar autocomplete.
        includes:
          - browser/components/urlbar/**/*
          - browser/themes/shared/urlbar*
        owners:
          - *adw
        peers:
          - *daisuke
          - *dao
          - *mak
          - *standard8
          - *jteow
        meta:
          components:
            - Firefox::Address Bar
          review_group: urlbar-reviewers

      - name: Application Update
        description: The application update services.
        includes:
          - toolkit/mozapps/update/**/*
        owners:
          - *bytesized
        peers:
          - *molly
        machine_name: application_update
        meta:
          peers_emeritus:
            - Adam Gashlin


      - name: Bookmarks & History
        description: The bookmarks and history services (Places).
        includes:
          - browser/components/places/**/*
          - toolkit/components/places/**/*
        meta:
          owners_emeritus:
            - Dietrich Ayala
          peers_emeritus:
            - Asaf Romano
            - David Dahl
            - Shawn Wilsher
          components:
            - Firefox::Bookmarks & History
            - Toolkit::Places
          review_group: places-reviewers
        owners:
          - *mak
        peers:
          - *adw
          - *daisuke
          - *lina
          - *standard8
        machine_name: bookmarks_history

      - name: Desktop Theme
        description: The style rules used in the desktop UI.
        includes:
          - browser/themes/**/*
          - toolkit/themes/**/*
        meta:
          peers_emeritus:
            - Tim Nguyen
            - *amy
          components:
            - Firefox::Theme
            - Toolkit::Themes
        owners:
          - *dao
        peers:
          - *cmkm
          - *emilio
          - *hjones
          - *itielyn8
          - *jules
          - *kcochrane
          - *sfoster
        machine_name: desktop_theme

      - name: Desktop UI
        description: The main browser UI except where covered by more specific submodules.
        includes:
          - browser/base/content/**/*
        meta:
          owners_emeritus:
            - Jared Wein
          peers_emeritus:
            - Florian Quèze
        owners:
          - *mconley
        peers:
          - *jhirsch
          - *sclements
          - *dwalker
          - *cmkm
        machine_name: desktop_ui

      - name: Download Manager
        description: The downloads UI and service.
        includes:
          - browser/components/downloads/**/*
          - toolkit/mozapps/downloads/**/*
          - uriloader/exthandler/**/*
        meta:
          owners_emeritus:
            - Paolo Amadini
            - Shawn Wilsher
          peers_emeritus:
            - *micah
        owners:
          - *mak
        peers:
          - *Gijs
        machine_name: download_manager

      - name: Enterprise Policies
        description: System policies for controlling Firefox.
        includes:
          - browser/components/enterprisepolicies/**/*
        meta: {}
        owners:
          - *mkaply
        peers: []
        machine_name: enterprise_policies

      - name: Experiments/Rollouts
        description: Desktop clients for our experiments and off-train deployments
          systems.
        includes:
          - toolkit/components/normandy/**/*
          - toolkit/components/nimbus/**/*
        meta:
          owners_emeritus:
            - Michael Cooper
          components:
            - Firefox::Normandy
            - Firefox::Nimbus Desktop Client
          url: https://experimenter.info/
        owners:
          - *beth
        peers:
          - *Gijs
          - *emcminn
          - *charlie
        machine_name: normandy

      - machine_name: firefox_view
        name: Firefox View
        description: The Firefox View page and its modules.
        includes:
          - browser/components/firefoxview/**/*
        excludes: []
        owners:
          - *sclements
        peers:
          - *sfoster
          - *kcochrane
          - *jsudiaman
        meta:
      - name: Form Autofill
        description: Form detection and autocomplete.
        includes:
          - browser/extensions/formautofill/**/*
          - toolkit/components/satchel/**/*
        meta:
          owners_emeritus:
            - Matthew Noorenberghe
            - *serg
          peers_emeritus:
            - *tgiles
        owners:
          - *dimi
        peers:
          - *enndeakin
          - *issammani
          - *janika
        machine_name: form_autofill

      - name: In-product Messaging
        description: The system for delivering in-product messaging and onboarding
          including Activity Stream Router, about:welcome, UI tour.
        includes:
          - browser/components/asrouter/**/*
          - browser/components/aboutwelcome/**/*
          - browser/components/uitour/**/*
          - toolkit/components/messaging-system/**/*
        meta:
          components:
            - Firefox::Messaging System
            - Firefox::Tours
          owners_emeritus:
            - *Mardak
          peers_emeritus:
            - *k88hudson
            - *MattN
        owners:
          - *pdahiya
        peers:
          - *nanj
          - *Mardak
          - *dmosedale
          - *aminomancer
        machine_name: inproduct_messaging

      - name: Launcher Process
        description: Windows process for bootstrapping the browser process.
        includes:
          - browser/app/winlauncher/**/*
        meta:
          owners_emeritus:
            - Aaron Klotz
            - *toshi
          components:
            - Firefox::Launcher Process
        owners:
          - *rkraesig
        peers:
          - *molly
          - *gstoll
          - *handyman
        machine_name: launcher_process

      - machine_name: localization
        name: Localization
        description: Tooling to enable translation and facilitate localization.
        includes: []
        excludes: []
        owners:
          - *flod
        peers:
          - *mathjazz
          - *eemeli
      - name: New Tab Page
        description: The new tab/home page.
        includes:
          - browser/components/newtab/**/*
        meta:
          components:
            - Firefox::New Tab Page
          owners_emeritus:
            - *Mardak
          peers_emeritus:
            - *k88hudson
            - *aoprea
        owners:
          - *thecount
        peers:
          - *amy
          - *nbarrett
          - *maxx
        machine_name: new_tab_page

      - name: Password Manager
        description: Managing, saving and filling logins.
        includes:
          - toolkit/components/passwordmgr/**/*
          - browser/components/aboutlogins/**/*
        meta:
          owners_emeritus:
            - Matthew Noorenberghe
            - *serg
          peers_emeritus:
            - Bianca Danforth
            - Severin Rudie
            - Jared Wein
            - *tgiles
            - *sfoster
          url: https://wiki.mozilla.org/Toolkit:Password_Manager
          components:
            - Toolkit::Password Manager
            - 'Toolkit::Password Manager: Site'
            - Compatibility
            - Firefox::about:logins
        owners:
          - *joschmidt
          - *micah
        peers:
          - *dimi
          - *enndeakin
          - *issammani
          - *janika
        machine_name: password_manager

      - machine_name: firefox_pip
        name: Picture-in-Picture
        description: A component that allows video elements to be pulled out into
          an always-on-top window.
        includes:
          - toolkit/components/pictureinpicture
          - browser/extensions/pictureinpicture
        meta:
          owners_emeritus:
            - *micah
            - *molly
        excludes: []
        owners:
          - *mconley
        peers:
          - *niklas
          - *kpatenio

      - name: Profile Migration
        description: Migrating data from other browsers.
        includes:
          - browser/components/migration/**/*
        meta: {}
        owners:
          - *Gijs
        peers:
          - *mconley
          - *mak
          - *MattN
        machine_name: profile_migration

      - name: Screenshots
        description: Code relating to Screenshots functionality
        includes:
          - browser/extensions/screenshots/**/*
          - browser/components/screenshots/**/*
        meta:
          owners_emeritus:
            - Emma Malysz
            - Ian Bicking
          peers_emeritus:
            - Barry Chen
          components:
            - Firefox::Screenshots
        owners:
          - *sfoster
          - *niklas
        peers:
          - *jhirsch
        machine_name: screenshots

      - name: Search
        description: The search service and search bar.
        includes:
          - browser/components/search/**/*
          - toolkit/components/search/**/*
        meta:
          components:
            - Firefox::Search
          peers_emeritus:
            - Michael de Boer
          review_group: search-reviewers
        owners:
          - *standard8
        peers:
          - *daleharvey
          - *jteow
          - *mcheang
          - *scunnane
        machine_name: search

      - name: Security and Privacy UI
        description: The front-end to our security and privacy features, including
          Protections UI, Site Identity, Site Permissions and Certificate Errors
        includes:
          - browser/components/protections/**/*
          - browser/components/controlcenter/**/*
        meta:
          peers_emeritus:
            - Erica Wright
            - Nihanth Subramanya
            - *prathiksha
          owners_emeritus:
            - Johann Hofmann
          components:
            - Firefox::Security
            - Firefox::Protections UI
            - Firefox::Site Identity
            - Firefox::Site Permissions
        owners:
          - *emz
        machine_name: security_and_privacy_ui

      - name: Session Restore
        description: Restoring a user's session after starting Firefox.
        includes:
          - browser/components/sessionstore/**/*
          - toolkit/components/sessionstore/**/*
        meta:
          owners_emeritus:
            - Michael de Boer
            - Kashav Madan
            - *daleharvey
            - *dao
          peers_emeritus:
            - Anny Gakhokidze
          components:
            - Firefox::Session Restore
        owners:
          - *sclements
          - *sfoster
          - *farre
        peers:
          - *dao
        machine_name: session_restore

      - name: Settings UI
        description: The front-end settings user interface.
        includes:
          - browser/components/preferences/**/*
          - browser/themes/*/preferences
          - toolkit/mozapps/preferences
        meta:
          owners_emeritus:
            - Jared Wein
          peers_emeritus:
            - Tim Nguyen
        owners:
          - *mossop
        peers:
          - *mstriemer
          - *Gijs
          - *mconley
        machine_name: settings_ui

      - name: Tabbed Browser
        description: The UI component controlling browser tabs.
        includes:
          - browser/components/tabbrowser/**/*
          - browser/themes/shared/tabbrowser/*
        meta:
          components:
            - Firefox::Tabbed Browser
          peers_emeritus:
            - Jared Wein
            - Matthew N
        owners:
          - *dao
        peers:
          - *mak
          - *mconley
          - *dwalker
          - *niklas
          - *jswinarton
          - *sthompson
        machine_name: tabbed_browser

      - name: Windows Installer
        description: The installer for Windows.
        includes:
          - browser/installer/**/*
          - toolkit/mozapps/installer/**/*
        meta:
          components:
            - Firefox::Installer
        owners:
          - *molly
        peers:
          - *agashlin
          - *nalexander
        machine_name: windows_installer
    machine_name: desktop_firefox

  - machine_name: devtools
    name: DevTools
    description: Mozilla Developer Tools
    includes:
      - devtools/**/*
    excludes: []
    owners:
      - *Honza
    peers:
      - *ochameau
      - *jdescottes
      - *nchevobbe
      - *bomsy
      - *whimboo
    meta:
      owners_emeritus:
        - Patrick Brosset
        - Joe Walker
        - Dave Camp
        - Rob Campbell
      peers_emeritus:
        - Mihai Șucan
        - Heather Arthur
        - Anton Kovalyov
        - Brandon Benvie
        - Eddy Bruel
        - James Long
        - Matteo Ferretti
        - Steve Fink (heapsnapshot code)
        - Jaroslav Šnajdr
        - Tom Tromey
        - Paul Rouget
        - Victor Porof
        - Lin Clark
        - Jan Keromnes
        - Jordan Santell
        - Soledad Penadés
        - Mike Ratcliffe
        - Panagiotis Astithas
        - Tim Nguyen
        - Brian Grinstead
        - J. Ryan Stinnett
        - Jason Laster
        - David Walsh
        - Greg Tatum
        - Gabriel Luong
        - Brad Werth
        - Daisuke Akatsuka
        - Yulia Startsev
        - Logan Smyth
        - Julien Wajsberg
        - Razvan Caliman
        - Micah Tigley
        - Nick Fitzgerald
        - Jim Blandy
        - Belén Albeza
      url: http://firefox-dev.tools/
      components:
        - DevTools
  - machine_name: fenix
    name: Fenix
    description: Android Project to build Firefox for Android
    includes:
      - mobile/android/fenix/**/*
    excludes: []
    owners:
      - *boek
      - *gl
    peers:
      - *royang
      - *rsainani
    meta:
  - machine_name: focus_android
    name: Focus for Android
    description: Android Project to build Focus for Android
    includes:
      - mobile/android/focus-android/**/*
    excludes: []
    owners:
      - *mcarare
      - *royang
    peers:
      - *giorga
    meta:
  - machine_name: javascript_usage
    name: JavaScript usage, tools, and style
    description: Aspects of JavaScript use such as language feature usage, tooling
      such as lint configurations, formatting and naming style.
    includes:
      - .eslintrc-test-paths.js
      - '**/.eslintrc*.js'
      - tools/lint/eslint/**/*
      - .prettier*
      - .stylelint*
    excludes: []
    owners:
      - *mossop
    peers:
      - *Gijs
      - *standard8
      - *jandem
    meta:
      review_group: frontend-codestyle-reviewers

  - name: mots config
    includes:
      - mots.yaml
    owners:
      - *zeid
    machine_name: mots

  - machine_name: mozharness
    name: mozharness
    description: Configuration-driven script harness.
    includes:
      - testing/mozharness/**/*
    excludes: []
    owners:
      - *ahal
    peers:
      - *jmaher
    meta:
      owners_emeritus:
        - Aki Sasaki
        - Geoff Brown
      peers_emeritus:
        - Justin Wood
        - Tom Prince
      components:
        - 'Release Engineering :: Applications: MozharnessCore'

  - machine_name: python_usage
    name: Python usage, tools, and style
    description: Aspects of Python use such as tooling, formatting and naming style
    includes:
      - tools/lint/python/**/*
    excludes: []
    owners:
      - *ahal
    peers:
      - *glandium
      - *marco
      - *Sylvestre
    meta:
      components:
        - 'Developer Infrastructure :: Lint and Formatting'

  - machine_name: remote_protocol
    name: Remote Protocol
    description: Low-level remote protocol exposing interfaces for inspecting state
      and controlling execution of web documents, instrumenting various subsystems
      in the browser, simulating user interaction for automation purposes, and for
      subscribing to updates from the aforementioned.
    includes:
      - remote/**/*
    meta:
      components:
        - Remote Protocol
      url: https://firefox-source-docs.mozilla.org/remote/
    owners:
      - *whimboo
    peers:
      - *jdescottes
      - *jgraham
      - *Sasha
    submodules:

      - machine_name: remote_protocol_agent
        name: Agent
        description: Underlying transport layer and server to allow remoting of Firefox
          for automation and debugging.
        includes:
          - remote/**/*
        excludes:
          - remote/cdp/*
          - remote/marionette/*
          - remote/webdriver-bidi/*
        meta:
          owners_emeritus:
            - Andreas Tolfsen
          peers_emeritus:
            - Maja Frydrychowicz
            - Alexandre Poirot
            - Yulia Startsev
          components:
            - 'Remote Protocol :: Agent'
        owners:
          - *whimboo
        peers:
          - *jdescottes
          - *jgraham
          - *Sasha

      - machine_name: remote_protocol_cdp
        name: CDP
        description: The core implementation for CDP support. Please file domain specific
          issues and requests under the appropriate CDP-prefixed Remote Protocol component.
        includes:
          - remote/cdp/*
        meta:
          owners_emeritus:
            - Andreas Tolfsen
          peers_emeritus:
            - Maja Frydrychowicz
            - Alexandre Poirot
            - Yulia Startsev
          components:
            - 'Remote Protocol :: CDP'
        owners:
          - *whimboo
        peers:
          - *jdescottes
          - *jgraham
          - *Sasha

      - machine_name: remote_protocol_marionette
        name: Marionette
        description: Marionette is a remote protocol that lets out-of-process programs
          communicate with, instrument, and control Gecko-based browsers. Combined
          with geckodriver, this forms our WebDriver classic implementation.
        includes:
          - remote/marionette/*
        meta:
          owners_emeritus:
            - Andreas Tolfsen
          peers_emeritus:
            - Maja Frydrychowicz
            - David Burns
          components:
            - 'Remote Protocol :: Marionette'
          group: dev-webdriver
        owners:
          - *whimboo
        peers:
          - *jdescottes
          - *jgraham
          - *Sasha

      - machine_name: remote_protocol_webdriver_bidi
        name: WebDriver BiDi
        description: W3C WebDriver BiDi implementation for Gecko-based browsers.
        includes:
          - remote/webdriver-bidi/*
        meta:
          components:
            - 'Remote Protocol :: WebDriver BiDi'
          group: dev-webdriver
        owners:
          - *whimboo
        peers:
          - *jdescottes
          - *jgraham
          - *Sasha

  - machine_name: sync
    name: Sync
    description: Firefox Sync client
    includes:
      - services/sync/**/*
    excludes: []
    owners:
      - *markh
    peers:
      - *lougenia
      - *teshaq
      - *bdk
      - *skhamis
      - *lina
    meta:
      owners_emeritus:
        - Ryan Kelly
      url: https://wiki.mozilla.org/Services/Process/Code_Review
      components:
        - Sync

  - machine_name: testing_firefox_ui
    name: firefox-ui
    description: Firefox UI test framework.
    includes:
      - testing/firefox-ui/**/*
    meta:
      peers_emeritus:
        - Maja Frydrychowicz
      components:
        - 'Testing :: Firefox UI'
    owners:
      - *whimboo
    peers:
      - *jdescottes
      - *jgraham
      - *Sasha

  - machine_name: testing_geckodriver
    name: geckodriver
    description: Proxy for using W3C WebDriver-compatible clients to interact with
      Gecko-based browsers.
    includes:
      - testing/geckodriver/**/*
    excludes: []
    owners:
      - *jgraham
    peers:
      - *whimboo
    meta:
      components:
        - 'Testing :: geckodriver'
      group: dev-webdriver

  - machine_name: testing_gtest
    name: gtest
    description: GTest test harness.
    includes:
      - testing/gtest/**/*
    excludes: []
    owners:
      - *jmaher
    peers: []
    meta:
      components:
        - 'Testing :: GTest'

  - machine_name: testing_marionette_client_harness
    name: Marionette Client & Harness
    description: Python client and harness for the Marionette remote protocol implementation.
    includes:
      - testing/marionette/**/*
    meta:
      owners_emeritus:
        - Andreas Tolfsen
      peers_emeritus:
        - Maja Frydrychowicz
        - David Burns
      components:
        - 'Testing :: Marionette Client & Harness'
    owners:
      - *whimboo
    peers:
      - *jdescottes
      - *jgraham
      - *Sasha

  - machine_name: testing_mochitest
    name: Mochitest
    description: Mochitest test framework
    includes:
      - testing/mochitest/**/*
    excludes: []
    owners:
      - *ahal
    peers:
      - *jmaher
    meta:
      components:
        - 'Testing :: Mochitest'
  - machine_name: testing_mozbase
    name: Mozbase
    description: Base modules used for implementing test components.
    includes:
      - testing/mozbase
    excludes: []
    owners:
      - *jmaher
    peers:
      - *ahal
      - *ato
      - *bc
      - *jgraham
      - *whimboo
    meta:
      components:
        - 'Testing :: Mozbase'
        - 'Testing :: Mozbase Rust'
  - machine_name: testing_performance_testing
    name: Performance Testing
    description: >
      This module encompasses all of our performance testing projects, e.g.  Raptor,  Talos,
      MozPerfTest, AWSY, JSShell, mach try perf, etc.. See our PerfDocs for more information  on
      the owners/peers of the various components (linked below).
    includes:
      - testing/raptor/**/*
      - testing/talos/**/*
      - python/mozperftest/**/*
      - testing/awsy/**/*
      - testing/jsshell/**/*
      - tools/lint/perfdocs/**/*
      - testing/perfdocs/**/*
      - testing/performance/**/*
      - testing/condprofile/**/*
      - tools/browsertime/**/*
      - tools/tryselect/selectors/perf.py
    excludes: []
    owners:
      - *sparky
    peers:
      - *aglavic
      - *davehunt
      - *kshampur
    meta:
      components:
        - 'Testing :: Raptor'
        - 'Testing :: Talos'
        - 'Testing :: AWSY'
        - 'Testing :: Performance'
        - 'Testing :: mozperftest'
        - 'Testing :: Condprofile'
      url: :ref:`Performance Testing`
  - machine_name: testing_reftest
    name: Reftest (+ jsreftest + crashtest)
    description: Reftest test framework
    includes:
      - layout/tools/reftest/**/*
    excludes: []
    owners:
      - *tnikkel
    peers:
      - *ahal
      - *jmaher
    meta:
      components:
        - 'Testing :: Reftest'
  - machine_name: testing_tryselect
    name: Tryselect
    description: Frontend for selecting jobs on the try server.
    includes:
      - tools/tryselect/**/*
    excludes: []
    owners:
      - *ahal
    peers:
      - *jgraham
      - *marco
    meta:
      components:
        - 'Developer Infrastructure :: Try'
  - machine_name: testing_web_platform_tests_infrastructure
    name: web-platform-tests infrastructure
    description: Infrastructure for running the cross-browser web-platform-tests
    includes:
      - testing/web-platform/**/*
      - testing/web-platform/tests/tools/**/*
    excludes:
      - testing/web-platform/tests/**/*
      - testing/web-platform/meta/**/*
      - testing/web-platform/mozilla/**/*
    owners:
      - *jgraham
    peers: []
    meta:
      components:
        - 'Testing :: web-platform-tests'
  - machine_name: testing_xpcshell
    name: XPCShell
    description: XPCShell test harness.
    includes:
      - testing/xpcshell/**/*
    excludes: []
    owners:
      - *jmaher
    meta:
      components:
        - Testing::XPCShell Harness
  - name: Toolkit
    description: Components shared between desktop and mobile browsers.
    includes:
      - toolkit/**/*
    meta:
      group: firefox-dev
      components:
        - Firefox
        - Toolkit
      peers_emeritus:
        - Matthew Noorenberghe
        - Jared Wein
    owners:
      - *mossop
      - *Gijs
    peers:
      - *dao
      - *mak
      - *mconley
      - *molly
    submodules:

      - name: Application Startup
        description: The profile system and startup process before the front-end launches.
        includes:
          - toolkit/profile/**/*
          - toolkit/components/remote/**/*
          - toolkit/xre/**/*
        owners:
          - *mossop
        meta:
          peers_emeritus:
            - *froydnj
        machine_name: application_startup

      - name: Telemetry
        description: >
          The core infrastructure in the Firefox client to send back telemetry
          data. Includes the common mechanisms to record, view and submit data: Legacy
          Telemetry and Glean (via Firefox on Glean (FOG)). This module
          does ''not'' include responsibility for every piece of submitted Telemetry
          data. Each team/module is responsible for their own measurements (histograms,
          scalars, other ping submissions, etc.).
        includes:
          - toolkit/components/glean/**/*
          - toolkit/components/telemetry/**/*
          - toolkit/content/aboutTelemetry.*
        meta:
          owners_emeritus:
            - Georg Fritzsche
          group: fx-data-dev
          url: :ref:`Telemetry`
          peers_emeritus:
            - *perrymcmanis
        owners:
          - *chutten
        peers:
          - *Dexter
          - *janerik
          - *travis
        machine_name: telemetry

      - name: UI Widgets
        description: The base widgets used throughout the UI.
        includes:
          - toolkit/content/widgets/**/*
        meta:
          owners_emeritus:
            - Neil Deakin
          peers_emeritus:
            - Andrew Swan
        owners:
          - *mstriemer
        peers:
          - *mak
          - *tgiles
          - *hjones
        machine_name: ui_widgets

      - name: WebCompat Addons
        description: Compatibility interventions (webcompat system addon) and bug
          reporting capabilities (Report Site Issue addon).
        includes:
          - browser/extensions/report-site-issue
          - browser/extensions/webcompat
          - mobile/android/android-components/components/feature/webcompat
          - mobile/android/android-components/components/feature/webcompat-reporter
          - toolkit/components/reportbrokensite
          - https://github.com/mozilla-extensions/webcompat-addon
        meta:
          url: https://wiki.mozilla.org/Compatibility/System_Addon
          group: compatibility
          peers_emeritus:
            - Mike Taylor
          components:
            - Web Compatibility::Interventions
            - Web Compatibility::Tooling & Investigations
        owners:
          - *denschub
          - *twisniewski
        peers:
          - *ksenia
        machine_name: webcompat_addons

      - name: Webextensions
        description: Webextension APIs and integration.
        includes:
          - browser/components/extensions/**/*
          - toolkit/components/extensions/**/*
        meta:
          peers_emeritus:
            - Andrew Swan
            - Kris Maglione
        owners:
          - *scaraveo
          - *zombie
        peers:
          - *rpl
          - *robwu
          - *willdurand
        machine_name: webextensions
    machine_name: toolkit
  - machine_name: translation
    name: Translation
    description: Support for translation in Firefox, and the infrastructure to train
      new translation language models.
    includes:
      - toolkit/components/translations/**/*
      - toolkit/components/translation/**/*
      - browser/components/translations/**/*
    excludes: []
    owners:
      - *gregtatum
    peers:
      - *nordzilla
      - *epavlov
      - *marco
    meta:
      group: dev-platform
      url: https://github.com/mozilla/firefox-translations-training https://github.com/mozilla/firefox-translations-models
        https://github.com/mozilla/firefox-translations-evaluations https://github.com/mozilla/firefox-translations
      components:
        - Firefox::Translation
      review_group: translations-reviewers
  - machine_name: tree_sheriffs
    name: Tree Sheriffs
    description: Tree Sheriffs aid developers to easily, quickly, and seamlessly land
      their code in the proper location(s) and ensure that code does not break our
      automated tests. In the service of this objective, the Sheriffs work closely
      with the larger engineering organization to create and enforce landing policies
      that increase productivity while maintaining an efficient and robust automated
      testing system. Beyond the policy role, they have also become shepherds of automation
      quality by monitoring intermittent failures, performing uplifts and merges,
      and identifying poorly performing automation machines.
    meta:
      group: sheriffs
      url: https://wiki.mozilla.org/Sheriffing
      owner_emeritus:
        - bmo_id: 75935
          name: Ryan VanderMeulen
          nick: ryanvm
        - Ed Morley
      peers_emeritus:
        - Carsten Book
        - Wes Kocher
    owners:
      - *aryx
    peers:
      - *RyanVM
  - machine_name: url_classifier
    name: URL Classifier
    description: Database and list-based classification of URL resources, such as
      Tracking Protection and SafeBrowsing.
    includes:
      - toolkit/components/url-classifier/**/*
      - netwerk/url-classifier/**/*
    excludes: []
    owners:
      - *dimi
      - *groovecoder
    peers:
      - *timhuang
      - *gcp
    meta:
      url: https://github.com/mozilla-services/shavar https://wiki.mozilla.org/Phishing_Protection
        https://wiki.mozilla.org/Security/Tracking_protection https://wiki.mozilla.org/Security/Application_Reputation
      owners_emeritus:
        - François Marier
      peers_emeritus:
        - Henry Chang
        - Ryan Tilder
      group: dev-platform
hashes:
  config: 47203670aa6060cfd7790c4bbf1ef02ff80d49b7
  export: faee9ad768d4c2fb9d5672b4089e0dd157049838
