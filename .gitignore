# .gitignore - List of filenames git should ignore

# See docs/code-quality/lint/linters/ignorefile.rst for lint-ignore-next-line
# syntax.

# Filenames that should be ignored wherever they appear
*~
*.pyc
*.pyo
TAGS
tags
.DS_Store
*.pdb
.eslintcache
*.gcda
*.gcno
*.gcov
compile_commands.json

# Ignore ID generated by idutils.
ID

# Un-ignore id directory (for Indonesian locale)
# lint-ignore-next-line: git-only
!id/

# Generated by hg or patch (e.g. revert, failed patch, ...)
*.orig
*.rej

# Filesystem temporaries
.fuse_hidden*

# Ignore Python .egg-info directories.
# This is only relevant for first-party modules, but adding that directory for
# third-party packages is dealt with by the script vendoring them.
*.egg-info/

# Vim swap files.
.*.sw[a-z]
.sw[a-z]

# Emacs directory variable files.
**/.dir-locals.el
# Emacs project sentinel files.
**/.projectile

# User files that may appear at the root
/.clang-tidy
/.clangd
/.mozconfig*
/mozconfig*
/.moz-fast-forward
/old-configure
/config.cache
/config.log
/.clang_complete
/machrc
/.machrc

# pyenv artifact
/.python-version

# Empty marker file that's generated when we check out NSS
security/manager/.nss.checkout

# Build directories
/obj*/

# gecko.log is generated by various test harnesses
/gecko.log

# Ignore all node_modules directories except for ones under third_party
node_modules/
devtools/**/node_modules/
tools/browsertime/node_modules/
tools/lint/eslint/eslint-plugin-mozilla/node_modules/
browser/components/asrouter/node_modules/
browser/components/newtab/node_modules/
browser/components/aboutwelcome/node_modules/
tools/ts/node_modules/

# Ignore newtab component build assets
browser/components/newtab/logs/

# Ignore about:welcome component build assets
browser/components/aboutwelcome/logs/

# Ignore ASRouter component build assets
browser/components/asrouter/logs/

# Ignore ASRouter generated test files
browser/components/asrouter/content-src/schemas/corpus/CFRMessageProvider.messages.json
browser/components/asrouter/content-src/schemas/corpus/OnboardingMessageProvider.messages.json
browser/components/asrouter/content-src/schemas/corpus/PanelTestProvider.messages.json
browser/components/asrouter/content-src/schemas/corpus/PanelTestProvider_toast_notification.messages.json

# Ignore Pocket component build and dev assets
browser/components/pocket/content/panels/css/main.compiled.css.map

# Ignore downloaded thirdparty build artifacts.
toolkit/components/translations/bergamot-translator/thirdparty

# Build directories for js shell
*_DBG.OBJ/
*_OPT.OBJ/
/js/src/*-obj/
/js/src/obj-*/

# SpiderMonkey configury
js/src/old-configure
js/src/autom4te.cache

# SpiderMonkey test result logs
js/src/tests/results-*.html
js/src/tests/results-*.txt
js/src/devtools/rootAnalysis/t/out

# SpiderMonkey wasm/generate-spectests artifacts
js/src/jit-test/etc/wasm/generate-spectests/specs/
js/src/jit-test/etc/wasm/generate-spectests/tests/
js/src/jit-test/etc/wasm/generate-spectests/target/

# Java HTML5 parser classes
parser/html/java/htmlparser/
parser/html/java/javaparser/
parser/html/java/javaparser.jar
parser/html/java/translator.jar

# SVN directories
.svn/

# Ignore the files and directory that Eclipse IDE creates
.project
.cproject
.settings/

# Ignore the files and directory that JetBrains IDEs create.
**/.idea/
*.iml

# Android Monitor in Android Studio creates a captures/ directory.
/captures/

# Gradle cache.
/.gradle/

# Local Gradle configuration properties.
/local.properties

# Ignore chrome.manifest files from the devtools loader
devtools/client/chrome.manifest
devtools/shared/chrome.manifest

# Ignore debugger build directories
devtools/client/debugger/assets/build
devtools/client/debugger/assets/module-manifest.json

# Ignore node_module directories and npm artifacts
remote/test/puppeteer/**.tsbuildinfo
remote/test/puppeteer/**/lib/
remote/test/puppeteer/**/node_modules/
remote/test/puppeteer/**/.wireit/
remote/test/puppeteer/.devcontainer/
remote/test/puppeteer/.github
remote/test/puppeteer/.husky
remote/test/puppeteer/.wireit/
remote/test/puppeteer/coverage/
remote/test/puppeteer/docker/
remote/test/puppeteer/docs/puppeteer-core.api.json
remote/test/puppeteer/docs/puppeteer.api.json
remote/test/puppeteer/experimental/
remote/test/puppeteer/lib/
remote/test/puppeteer/node_modules/
remote/test/puppeteer/package-lock.json
remote/test/puppeteer/packages/ng-schematics/test/build
remote/test/puppeteer/packages/puppeteer/**/README.md
remote/test/puppeteer/packages/puppeteer-core/src/generated
remote/test/puppeteer/packages/puppeteer-core/**/README.md
remote/test/puppeteer/src/generated
remote/test/puppeteer/test/build
remote/test/puppeteer/test/installation/puppeteer*.tgz
remote/test/puppeteer/test/output-firefox
remote/test/puppeteer/test/output-chromium
remote/test/puppeteer/testserver/lib/
remote/test/puppeteer/tools/internal/
remote/test/puppeteer/tools/mocha-runner/bin/
remote/test/puppeteer/website

third_party/js/PKI.js/node_modules/
third_party/js/PKI.js/package-lock.json

# git checkout of libstagefright
media/libstagefright/android

# Tag files generated by GNU Global
GTAGS
GRTAGS
GSYMS
GPATH

# Git clone directory for updating web-platform-tests
testing/web-platform/sync/

# Third party metadata for web-platform-tests
testing/web-platform/products/

# Android Gradle artifacts.
mobile/android/gradle/.gradle

# Android build cache
mobile/android/**/.build-cache
mobile/android/**/.gradle
mobile/android/**/build
mobile/android/**/bin
mobile/android/**/generated

# Android local.properties
mobile/android/**/local.properties

# Android - Web extensions: manifest.json files are generated
mobile/android/**/manifest.json

# XCode project cruft
/*.xcodeproj/

# Rust/Cargo output from running `cargo` directly
/target/
/servo/ports/geckolib/target/
/dom/base/rust/target/
/servo/components/style/target/
/dom/webgpu/tests/cts/vendor/target/

# Ignore mozharness execution files
testing/mozharness/.tox/
testing/mozharness/build/
testing/mozharness/logs/
testing/mozharness/.coverage
testing/mozharness/nosetests.xml

# Ignore tox generated dir
.tox/

# Ignore talos virtualenv and tp5n files.
# The tp5n set is supposed to be decompressed at
# testing/talos/talos/fis|tests/tp5n in order to run tests like tps
# locally. Similarly, running talos requires a Python package virtual
# environment. Both the virtual environment and tp5n files end up littering
# the status command, so we ignore them.
testing/talos/.Python
testing/talos/bin/
testing/talos/include/
testing/talos/lib/
testing/talos/talos/fis/tp5n.zip
testing/talos/talos/fis/tp5n.tar.gz
testing/talos/talos/fis/tp5n
testing/talos/talos/tests/tp5n.zip
testing/talos/talos/tests/tp5n.tar.gz
testing/talos/talos/tests/tp5n
testing/talos/talos/tests/pdfpaint/pdfs
testing/talos/talos/tests/devtools/damp.manifest.develop
testing/talos/talos/startup_test/startup_about_home_paint/startup_about_home_paint.manifest.develop
testing/talos/talos/webextensions/
talos-venv
py3venv
testing/talos/talos/mitmproxy/mitmdump
testing/talos/talos/mitmproxy/mitmproxy
testing/talos/talos/mitmproxy/mitmweb

# Ignore talos webkit benchmark files; source is copied from in-tree /third_party
# into testing/talos/talos/tests/webkit/PerformanceTests/ when run locally
# i.e. speedometer, motionmark, stylebench
testing/talos/talos/tests/webkit/PerformanceTests

# Ignore sync tps logs and reports
tps.log
tps_result.json

# Ignore toolchains.json created by tooltool.
toolchains.json

# Unit test
.pytest_cache/

# Ruff
.ruff_cache/

# Ignore files created when running a reftest.
lextab.py

# Ignore Visual Studio/Visual Studio Code workspace files.
.vs/
.vscode/
*.user

# Thunderbird source tree
/comm/

# Ignore various raptor performance framework files
testing/raptor/.raptor-venv
testing/raptor/raptor-venv
testing/raptor/raptor/tests/json/
testing/raptor/webext/raptor/auto_gen_test_config.js

# Ignore condprofile build directory
testing/condprofile/build

# Ignore browsertime output directory
browsertime-results

# Ignore the build directories of WebGPU and WebRender standalone builds.
gfx/wgpu/target
gfx/wgpu/**/build
gfx/wr/target/

# Ignore Rust/Cargo output from running `cargo` directly for image_builder docker image
taskcluster/docker/image_builder/build-image/target

# Ignore ICU4X experimentation data files.
# See intl/ICU4X.md for more details.
config/external/icu4x

# Ignore the index files generated by clangd.
.cache/clangd/index/

# Ignore mypy files
.mypy_cache/

# Ignore Storybook generated files
browser/components/storybook/node_modules/
browser/components/storybook/storybook-static/
browser/components/storybook/.storybook/chrome-map.js
browser/components/storybook/custom-elements.json

# Ignore design-system node_modules
toolkit/themes/shared/design-system/node_modules/

# Ignore TypeScript declarations reference file updated by tooling.
tools/@types/index.d.ts

# Ignore support files for en-US dictionary updates
extensions/spellcheck/locales/en-US/hunspell/dictionary-sources/scowl
extensions/spellcheck/locales/en-US/hunspell/dictionary-sources/support_files/
extensions/spellcheck/locales/en-US/hunspell/dictionary-sources/*en_US-mozilla*

# Ignore automatically generated mots documentation
docs/mots/index.rst

# Ignore generated directory with .class files for GeckoView annotation processor
mobile/android/annotations/bin/

# Ignore generated log files under media/libvpx
media/libvpx/config/**/config.log

# Ignore generated files resulting from building the minidump analyzer tests.
toolkit/crashreporter/minidump-analyzer/analyzer-test/target/
