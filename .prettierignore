# Exclude everything by default, then include all the formats we support.
*.*
!*.js
!*.jsm
!*.json
!*.jsx
!*.mjs
!*.sjs
!*.xhtml
!*.html

# Exclude xhtml/html test files.
**/test*/**/*.xhtml
**/test*/**/*.html

# This should be re-enabled in bug 1827456.
js/src/builtin

# Don't format README files.
README

# Ignore build files which are used for generating toolchains. Whitespace changes
# here would trigger new toolchains.
build/build-clang/**

# Ignore testing pref files which aren't parsed normally.
testing/profiles/**/user.js

# Ignore CORS fixtures which require specific resource hashes.
dom/security/test/sri/script*

# Files that are intentionally broken for tests.
dom/base/test/file_bug687859-charset.js
dom/base/test/file_bug687859-inherit.js
parser/htmlparser/tests/mochitest/broken_xml.xhtml
parser/htmlparser/tests/mochitest/test_bug563322.xhtml

# Files that are specific formats/layouts for tests.
devtools/client/debugger/src/**/fixtures/**

# Imported files where we don't want to change the format, but we do want ESLint
# to keep an eye on them (hence not in Generated.txt/ThirdPartyFiles.txt)
devtools/shared/plural-form.js
toolkit/components/formautofill/shared/CreditCardRuleset.sys.mjs

# Ignore .inc.(x)html files, since they may contain preprocessor directives/
# incomplete HTML.
**/*.inc.xhtml
**/*.inc.html

# Ignore preprocessed xhtml/html files
browser/base/content/aboutDialog.xhtml
browser/base/content/browser.xhtml
browser/base/content/default-bookmarks.html
browser/base/content/hiddenWindowMac.xhtml
browser/base/content/macWindow.inc.xhtml
browser/base/content/main-popupset.inc.xhtml
browser/base/content/pageinfo/pageInfo.xhtml
browser/base/content/webext-panels.xhtml
browser/components/backup/content/archive.template.html
browser/components/downloads/content/contentAreaDownloadsView.xhtml
browser/components/places/content/bookmarkProperties.xhtml
browser/components/places/content/bookmarksSidebar.xhtml
browser/components/places/content/historySidebar.xhtml
browser/components/places/content/places.xhtml
browser/components/preferences/dialogs/siteDataRemoveSelected.xhtml
browser/components/preferences/preferences.xhtml
browser/components/sessionstore/content/aboutSessionRestore.xhtml
browser/components/shell/content/setDesktopBackground.xhtml
security/manager/pki/resources/content/exceptionDialog.xhtml
toolkit/components/pictureinpicture/content/player.xhtml
toolkit/content/aboutSupport.xhtml
toolkit/content/buildconfig.html
toolkit/content/license.html
toolkit/mozapps/downloads/content/unknownContentType.xhtml
toolkit/mozapps/update/content/updateElevation.xhtml
toolkit/profile/content/profileDowngrade.xhtml

# Ignore non-UTF8/non-us-ascii files.
browser/components/places/tests/browser/keyword_form.html
browser/components/sessionstore/test/browser_485482_sample.html
docshell/test/browser/file_bug1648464-1-child.html
docshell/test/browser/file_bug1648464-1.html
docshell/test/browser/file_bug234628-*.html
docshell/test/browser/file_bug673087-1-child.html
docshell/test/browser/file_bug673087-1.html
docshell/test/browser/test-form_sjis.html
dom/base/test/bug466409-page.html
dom/encoding/test/file_in_lk_TLD.html
dom/encoding/test/file_jp_TLD.html
dom/encoding/test/file_utf16_*.xhtml
dom/html/test/test_bug615595.html
dom/serviceworkers/test/sw_clients/refresher_cached_compressed.html
dom/serviceworkers/test/sw_clients/refresher_compressed.html
dom/xhr/tests/file_html_in_xhr.html
dom/xhr/tests/file_html_in_xhr2.html
extensions/universalchardet/tests/bug1071816-2_text.html
extensions/universalchardet/tests/bug1071816-4_text.html
extensions/universalchardet/tests/bug426271_text-euc-jp.html
extensions/universalchardet/tests/bug431054_text.html
extensions/universalchardet/tests/bug620106_text.html
extensions/universalchardet/tests/bug631751be_text.html
extensions/universalchardet/tests/bug631751le_text.html
extensions/universalchardet/tests/bug638318_text.html
intl/uconv/tests/test_bug959058-1.html
intl/uconv/tests/test_bug959058-2.html
intl/uconv/tests/test_singlebyte_overconsumption.html
intl/uconv/tests/test_utf8_overconsumption.html
parser/htmlparser/tests/mochitest/file_bug594730-*.html
parser/htmlparser/tests/mochitest/file_bug672453_enc_error_inherited.html
parser/htmlparser/tests/mochitest/file_bug672453_enc_error.html
parser/htmlparser/tests/mochitest/file_bug672453_meta_speculation_fail.html
parser/htmlparser/tests/mochitest/file_bug672453_xml_speculation_fail.html
parser/htmlparser/tests/mochitest/file_bug716579-16.html
parser/htmlparser/tests/mochitest/file_bug716579-16.xhtml

# (x)html files that currently fail and need investigation/fixing.
accessible/tests/mochitest/actions/test_controls.html
accessible/tests/mochitest/actions/test_keys.html
accessible/tests/mochitest/attributes/test_obj_group.html
accessible/tests/mochitest/elm/test_HTMLSpec.html
accessible/tests/mochitest/events/test_focus_autocomplete.html
accessible/tests/mochitest/events/test_focus_removal.html
accessible/tests/mochitest/events/test_mutation.html
accessible/tests/mochitest/events/test_stylechange.html
accessible/tests/mochitest/hyperlink/test_general.html
accessible/tests/mochitest/hypertext/test_general.html
accessible/tests/mochitest/hypertext/test_update.html
accessible/tests/mochitest/name/test_general.html
accessible/tests/mochitest/name/test_svg.html
accessible/tests/mochitest/pivot/doc_virtualcursor_text.html
accessible/tests/mochitest/role/test_aria.html
accessible/tests/mochitest/role/test_general.html
accessible/tests/mochitest/states/test_aria.html
accessible/tests/mochitest/states/test_doc.html
accessible/tests/mochitest/states/test_inputs.html
accessible/tests/mochitest/table/test_css_tables.html
accessible/tests/mochitest/table/test_headers_table.html
accessible/tests/mochitest/table/test_layoutguess.html
accessible/tests/mochitest/table/test_struct_ariagrid.html
accessible/tests/mochitest/table/test_struct_ariatreegrid.html
accessible/tests/mochitest/test_aria_token_attrs.html
accessible/tests/mochitest/text/test_selection.html
accessible/tests/mochitest/tree/test_brokencontext.html
accessible/tests/mochitest/tree/test_iframe.html
accessible/tests/mochitest/tree/test_invalid_img.xhtml
accessible/tests/mochitest/tree/test_list.html
accessible/tests/mochitest/tree/test_txtctrl.html
accessible/tests/mochitest/treeupdate/test_delayed_removal.html
browser/base/content/appmenu-viewcache.inc.xhtml
browser/base/content/swipe-navigation.inc.xhtml
browser/base/content/test/about/dummy_page.html
browser/base/content/test/contextMenu/subtst_contextmenu.html
browser/base/content/test/favicons/datauri-favicon.html
browser/base/content/test/favicons/file_bug970276_popup1.html
browser/base/content/test/fullscreen/open_and_focus_helper.html
browser/base/content/test/general/browser_tab_dragdrop_embed.html
browser/base/content/test/general/dummy_page.html
browser/base/content/test/general/file_documentnavigation_frameset.html
browser/base/content/test/outOfProcess/file_base.html
browser/base/content/test/outOfProcess/file_frame2.html
browser/base/content/test/outOfProcess/file_innerframe.html
browser/base/content/test/pageinfo/svg_image.html
browser/base/content/test/permissions/temporary_permissions_frame.html
browser/base/content/test/permissions/temporary_permissions_subframe.html
browser/base/content/test/plugins/plugin_bug797677.html
browser/base/content/test/sanitize/dummy_page.html
browser/base/content/test/siteIdentity/dummy_iframe_page.html
browser/base/content/test/siteIdentity/dummy_page.html
browser/base/content/test/siteIdentity/test-mixedcontent-securityerrors.html
browser/base/content/test/siteIdentity/test_mcb_double_redirect_image.html
browser/base/content/test/siteIdentity/test_mcb_redirect_image.html
browser/base/content/test/siteIdentity/test_no_mcb_for_loopback.html
browser/base/content/test/siteIdentity/test_no_mcb_for_onions.html
browser/base/content/test/static/dummy_page.html
browser/base/content/test/webrtc/get_user_media_in_xorigin_frame_ancestor.html
browser/components/aboutlogins/content/aboutLogins.html
browser/components/extensions/test/browser/file_dummy.html
browser/components/extensions/test/browser/file_find_frames.html
browser/components/extensions/test/browser/file_title.html
browser/components/originattributes/test/browser/dummy.html
browser/components/places/tests/browser/bookmark_dummy_1.html
browser/components/places/tests/browser/bookmark_dummy_2.html
browser/components/places/tests/browser/bookmarklet_windowOpen_dummy.html
browser/components/places/tests/browser/framedPage.html
browser/components/places/tests/unit/bookmarks.glue.html
browser/components/resistfingerprinting/test/browser/file_animationapi_iframee.html
browser/components/resistfingerprinting/test/browser/file_dummy.html
browser/components/resistfingerprinting/test/browser/file_navigator.html
browser/components/search/test/browser/searchTelemetryAd_searchbox.html
browser/components/sessionstore/test/browser_423132_sample.html
browser/components/sessionstore/test/browser_485482_sample.html
browser/components/sessionstore/test/browser_frametree_sample_frameset.html
browser/components/sessionstore/test/browser_scrollPositions_sample_frameset.html
browser/components/tabbrowser/test/browser/tabs/dummy_page.html
browser/components/tabbrowser/test/browser/tabs/file_anchor_elements.html
browser/components/tabbrowser/test/browser/tabs/tab_that_closes.html
browser/components/textrecognition/textrecognition.html
browser/components/urlbar/content/quicksuggestOnboarding.html
browser/components/urlbar/tests/browser/dummy_page.html
browser/components/urlbar/tests/browser/file_urlbar_edit_dos.html
browser/tools/mozscreenshots/mozscreenshots/extension/lib/controlCenter/mixed.html
browser/tools/mozscreenshots/mozscreenshots/extension/lib/controlCenter/mixed_passive.html
devtools/client/aboutdebugging/test/browser/test-tab-favicons.html
devtools/client/inspector/rules/test/doc_author-sheet.html
devtools/client/inspector/rules/test/doc_blob_stylesheet.html
devtools/client/inspector/rules/test/doc_keyframeLineNumbers.html
devtools/client/inspector/test/doc_inspector_embed.html
devtools/client/netmonitor/test/html_csp-resend-test-page.html
devtools/client/styleeditor/test/bug_1405342_serviceworker_iframes.html
devtools/client/styleeditor/test/sourcemaps-inline.html
devtools/client/styleeditor/test/test_private.html
devtools/client/webconsole/test/browser/test-csp-violation.html
devtools/client/webconsole/test/browser/test-external-script-errors.html
devtools/client/webconsole/test/browser/test-mixedcontent-securityerrors.html
devtools/client/webconsole/test/browser/test-network.html
devtools/client/webconsole/test/browser/test_jsterm_screenshot_command.html
devtools/server/tests/browser/animation-data.html
devtools/server/tests/browser/doc_accessibility_infobar.html
devtools/server/tests/browser/doc_allocations.html
devtools/server/tests/browser/inspector-search-data.html
devtools/server/tests/chrome/inspector-eyedropper.html
devtools/server/tests/chrome/inspector-search-data.html
devtools/server/tests/chrome/inspector-styles-data.html
devtools/server/tests/chrome/inspector_css-properties.html
devtools/server/tests/chrome/inspector_display-type.html
devtools/server/tests/chrome/inspector_getImageData.html
devtools/server/tests/chrome/inspector_getOffsetParent.html
devtools/server/tests/chrome/test_overflowing-body.html
docshell/test/browser/file_bug420605.html
docshell/test/chrome/215405_nocache.html
docshell/test/chrome/215405_nostore.html
docshell/test/chrome/bug449778_window.xhtml
docshell/test/iframesandbox/test_child_navigation_by_location.html
docshell/test/mochitest/bug529119-window.html
docshell/test/mochitest/test_bug387979.html
docshell/test/mochitest/test_bug509055.html
docshell/test/mochitest/test_bug530396.html
docshell/test/mochitest/test_forceinheritprincipal_overrule_owner.html
docshell/test/navigation/file_triggeringprincipal_frame_1.html
docshell/test/navigation/iframe_slow_onload.html
docshell/test/navigation/test_triggeringprincipal_iframe_iframe_window_open.html
docshell/test/navigation/test_triggeringprincipal_parent_iframe_window_open.html
docshell/test/navigation/test_triggeringprincipal_window_open.html
dom/abort/tests/test_event_listener_leaks.html
dom/animation/test/chrome/file_animate_xrays.html
dom/animation/test/mozilla/test_distance_of_basic_shape.html
dom/animation/test/mozilla/test_distance_of_filter.html
dom/animation/test/mozilla/test_distance_of_path_function.html
dom/animation/test/mozilla/test_distance_of_transform.html
dom/animation/test/mozilla/test_event_listener_leaks.html
dom/animation/test/mozilla/test_moz_prefixed_properties.html
dom/animation/test/mozilla/test_restyles.html
dom/animation/test/style/test_interpolation-from-interpolatematrix-to-none.html
dom/animation/test/style/test_transform-non-normalizable-rotate3d.html
dom/base/test/345339_iframe.html
dom/base/test/dummy.html
dom/base/test/file_bug326337_inner.html
dom/base/test/file_bug541937.html
dom/base/test/file_bug541937.xhtml
dom/base/test/file_bug769117.html
dom/base/test/file_focus_display_none_xorigin_iframe_inner.html
dom/base/test/file_lock_orientation_with_pending_fullscreen.html
dom/base/test/file_youtube_flash_embed.html
dom/base/test/fullscreen/file_fullscreen-api.html
dom/base/test/fullscreen/file_fullscreen-esc-exit-inner.html
dom/base/test/fullscreen/file_fullscreen-esc-exit.html
dom/base/test/fullscreen/file_fullscreen-focus-inner.html
dom/base/test/fullscreen/file_fullscreen-focus.html
dom/base/test/fullscreen/file_fullscreen-multiple-inner.html
dom/base/test/fullscreen/file_fullscreen-multiple.html
dom/base/test/fullscreen/file_fullscreen-rollback.html
dom/base/test/fullscreen/file_fullscreen-selector.html
dom/base/test/fullscreen/file_fullscreen-single.html
dom/base/test/iframe_bug962251.html
dom/base/test/intersectionobserver_cross_domain_iframe.html
dom/base/test/intersectionobserver_iframe.html
dom/base/test/intersectionobserver_window.html
dom/base/test/test_NodeIterator_mutations_2.html
dom/base/test/test_NodeIterator_mutations_3.html
dom/base/test/test_base.xhtml
dom/base/test/test_bug1060938.html
dom/base/test/test_bug1075702.html
dom/base/test/test_bug1648887.html
dom/base/test/test_bug372086.html
dom/base/test/test_bug403841.html
dom/base/test/test_bug403852.html
dom/base/test/test_bug422403-2.xhtml
dom/base/test/test_bug424359-2.html
dom/base/test/test_bug428847.html
dom/base/test/test_bug455629.html
dom/base/test/test_bug564863.xhtml
dom/base/test/test_bug656283.html
dom/base/test/test_bug726364.html
dom/base/test/test_bug782342.html
dom/base/test/test_bug789315.html
dom/base/test/test_bug819051.html
dom/base/test/test_data_uri.html
dom/base/test/test_eventsource_event_listener_leaks.html
dom/base/test/test_focus_display_none_xorigin_iframe.html
dom/base/test/test_getLastOverWindowPointerLocationInCSSPixels.html
dom/base/test/test_history_document_open.html
dom/base/test/test_html_colors_quirks.html
dom/base/test/test_html_colors_standards.html
dom/base/test/test_iframe_event_listener_leaks.html
dom/base/test/test_meta_refresh_referrer.html
dom/base/test/test_mozMatchesSelector.html
dom/base/test/test_navigatorPrefOverride.html
dom/base/test/test_openDialogChromeOnly.html
dom/base/test/test_plugin_freezing.html
dom/base/test/test_postMessages_broadcastChannel.html
dom/base/test/test_postMessages_messagePort.html
dom/base/test/test_postMessages_window.html
dom/base/test/test_postMessages_workers.html
dom/base/test/test_toScreenRect.html
dom/base/test/test_urgent_start.html
dom/base/test/test_x-frame-options.html
dom/base/test/unit/nodelist_data_2.xhtml
dom/bindings/test/file_proxies_via_xray.html
dom/bindings/test/test_bug1123875.html
dom/bindings/test/test_bug773326.html
dom/bindings/test/test_enums.html
dom/bindings/test/test_lenientThis.html
dom/broadcastchannel/tests/test_event_listener_leaks.html
dom/canvas/test/crash/test_1251091-1.html
dom/canvas/test/crash/test_616401.html
dom/canvas/test/crash/test_798802-1.html
dom/canvas/test/crash/test_bug1233613.html
dom/canvas/test/crash/test_createImageBitmap-video.html
dom/canvas/test/crash/test_texImage2D.html
dom/canvas/test/test_canvas_focusring.html
dom/canvas/test/test_canvas_strokeStyle_getter.html
dom/canvas/test/test_invalid_mime_type_blob.html
dom/canvas/test/test_isPointInStroke.html
dom/canvas/test/test_offscreencanvas_font.html
dom/canvas/test/test_toDataURL_alpha.html
dom/console/tests/test_consoleEmptyStack.html
dom/credentialmanagement/tests/mochitest/test_credman_empty_option.html
dom/credentialmanagement/tests/mochitest/test_credman_iframes.html
dom/events/test/pointerevents/wpt/idlharness.html
dom/events/test/pointerevents/wpt/pointerevent_sequence_at_implicit_release_on_click-manual.html
dom/events/test/pointerevents/wpt/pointerevent_sequence_at_implicit_release_on_drag-manual.html
dom/events/test/test_accesskey.html
dom/events/test/test_bug1127588.html
dom/events/test/test_bug1264380.html
dom/events/test/test_bug1304044.html
dom/events/test/test_bug409604.html
dom/events/test/test_bug508479.html
dom/events/test/test_bug547996-2.xhtml
dom/events/test/test_bug650493.html
dom/events/test/test_deviceSensor.html
dom/events/test/test_draggableprop.html
dom/events/test/test_messageEvent_init.html
dom/events/test/test_moving_and_expanding_selection_per_page.html
dom/events/test/test_passive_listeners.html
dom/events/test/test_paste_image.html
dom/events/test/test_scroll_per_page.html
dom/file/ipc/tests/test_ipcBlob_createImageBitmap.html
dom/file/ipc/tests/test_ipcBlob_emptyMultiplex.html
dom/file/ipc/tests/test_ipcBlob_fileReaderSync.html
dom/file/ipc/tests/test_ipcBlob_mixedMultiplex.html
dom/file/ipc/tests/test_ipcBlob_workers.html
dom/file/tests/file_mozfiledataurl_doc.html
dom/file/tests/file_mozfiledataurl_inner.html
dom/file/tests/test_mozfiledataurl.html
dom/filesystem/compat/tests/test_basic.html
dom/filesystem/tests/test_bug1319088.html
dom/filesystem/tests/test_webkitdirectory.html
dom/flex/test/chrome/test_flex_parent.html
dom/html/test/bug199692-popup.html
dom/html/test/dialog/test_bug1648877_dialog_fullscreen_denied.html
dom/html/test/file_iframe_sandbox_a_if10.html
dom/html/test/file_iframe_sandbox_a_if11.html
dom/html/test/file_iframe_sandbox_d_if1.html
dom/html/test/file_iframe_sandbox_d_if16.html
dom/html/test/file_iframe_sandbox_d_if2.html
dom/html/test/file_iframe_sandbox_d_if4.html
dom/html/test/file_iframe_sandbox_d_if5.html
dom/html/test/file_iframe_sandbox_d_if6.html
dom/html/test/file_iframe_sandbox_e_if6.html
dom/html/test/file_iframe_sandbox_k_if3.html
dom/html/test/file_iframe_sandbox_k_if6.html
dom/html/test/file_refresh_after_document_write.html
dom/html/test/file_window_open_close_inner.html
dom/html/test/file_window_open_close_outer.html
dom/html/test/forms/test_change_event.html
dom/html/test/forms/test_input_color_picker_popup.html
dom/html/test/forms/test_input_date_bad_input.html
dom/html/test/forms/test_input_email.html
dom/html/test/forms/test_input_file_picker.html
dom/html/test/forms/test_input_radio_indeterminate.html
dom/html/test/forms/test_input_radio_radiogroup.html
dom/html/test/forms/test_interactive_content_in_label.html
dom/html/test/forms/test_interactive_content_in_summary.html
dom/html/test/forms/test_label_input_controls.html
dom/html/test/forms/test_radio_in_label.html
dom/html/test/forms/test_radio_radionodelist.html
dom/html/test/forms/test_select_validation.html
dom/html/test/forms/test_set_range_text.html
dom/html/test/forms/without_selectionchange/test_select.html
dom/html/test/test_bug1013316.html
dom/html/test/test_bug1264157.html
dom/html/test/test_bug1297.html
dom/html/test/test_bug274626.html
dom/html/test/test_bug324378.html
dom/html/test/test_bug353415-1.html
dom/html/test/test_bug371375.html
dom/html/test/test_bug372098.html
dom/html/test/test_bug388794.html
dom/html/test/test_bug406596.html
dom/html/test/test_bug579079.html
dom/html/test/test_bug598833-1.html
dom/html/test/test_bug622558.html
dom/html/test/test_bug780993.html
dom/html/test/test_bug797113.html
dom/html/test/test_bug821307.html
dom/html/test/test_fakepath.html
dom/html/test/test_form-parsing.html
dom/html/test/test_getElementsByName_after_mutation.html
dom/html/test/test_input_file_cancel_event.html
dom/html/test/test_inputmode.html
dom/indexedDB/test/test_event_listener_leaks.html
dom/indexedDB/test/test_leaving_page.html
dom/indexedDB/test/test_sandbox.html
dom/ipc/tests/file_cross_frame.html
dom/l10n/tests/mochitest/document_l10n/test_connectRoot_webcomponent.html
dom/l10n/tests/mochitest/document_l10n/test_docl10n.xhtml
dom/l10n/tests/mochitest/l10n_overlays/test_same_id.html
dom/l10n/tests/mochitest/l10n_overlays/test_same_id_args.html
dom/media/autoplay/test/mochitest/test_autoplay.html
dom/media/autoplay/test/mochitest/test_autoplay_policy.html
dom/media/mediacontrol/tests/browser/file_non_autoplay.html
dom/media/mediasource/test/test_MediaSource_capture_gc.html
dom/media/test/test_background_video_cancel_suspend_visible.html
dom/media/test/test_bug1431810_opus_downmix_to_mono.html
dom/media/test/test_bug874897.html
dom/media/test/test_hls_player_independency.html
dom/media/test/test_imagecapture.html
dom/media/test/test_looping_eventsOrder.html
dom/media/test/test_mediarecorder_fires_start_event_once_when_erroring.html
dom/media/test/test_mediarecorder_onerror_pause.html
dom/media/test/test_mediarecorder_record_4ch_audiocontext.html
dom/media/test/test_mediarecorder_record_audiocontext.html
dom/media/test/test_mediarecorder_record_audionode.html
dom/media/test/test_mediarecorder_record_getdata_afterstart.html
dom/media/test/test_mediarecorder_webm_support.html
dom/media/test/test_play_promise_1.html
dom/media/test/test_play_promise_10.html
dom/media/test/test_play_promise_11.html
dom/media/test/test_play_promise_12.html
dom/media/test/test_play_promise_13.html
dom/media/test/test_play_promise_14.html
dom/media/test/test_play_promise_15.html
dom/media/test/test_play_promise_16.html
dom/media/test/test_play_promise_17.html
dom/media/test/test_play_promise_18.html
dom/media/test/test_play_promise_2.html
dom/media/test/test_play_promise_3.html
dom/media/test/test_play_promise_4.html
dom/media/test/test_play_promise_5.html
dom/media/test/test_play_promise_6.html
dom/media/test/test_play_promise_7.html
dom/media/test/test_play_promise_8.html
dom/media/test/test_play_promise_9.html
dom/media/test/test_preserve_playbackrate_after_ui_play.html
dom/media/test/test_seamless_looping.html
dom/media/test/test_seek_promise_bug1344357.html
dom/media/test/test_video_to_canvas.html
dom/media/webaudio/test/test_ScriptProcessorCollected1.html
dom/media/webaudio/test/test_audioContextGC.html
dom/media/webaudio/test/test_decodeOpusTail.html
dom/media/webaudio/test/test_event_listener_leaks.html
dom/media/webaudio/test/test_mediaElementAudioSourceNodeFidelity.html
dom/media/webaudio/test/test_mediaStreamAudioDestinationNode.html
dom/media/webaudio/test/test_retrospective-exponentialRampToValueAtTime.html
dom/media/webaudio/test/test_retrospective-linearRampToValueAtTime.html
dom/media/webaudio/test/test_retrospective-setTargetAtTime.html
dom/media/webaudio/test/test_retrospective-setValueCurveAtTime.html
dom/media/webrtc/tests/mochitests/test_forceSampleRate.html
dom/media/webrtc/tests/mochitests/test_peerConnection_replaceNullTrackThenRenegotiateAudio.html
dom/media/webrtc/tests/mochitests/test_peerConnection_stereoFmtpPref.html
dom/media/webvtt/test/mochitest/test_texttrack.html
dom/media/webvtt/test/mochitest/test_texttrack_moz.html
dom/media/webvtt/test/mochitest/test_texttrackcue_moz.html
dom/media/webvtt/test/mochitest/test_trackelementevent.html
dom/media/webvtt/test/mochitest/test_webvtt_overlapping_time.html
dom/messagechannel/tests/test_event_listener_leaks.html
dom/midi/tests/file_midi_permission_gated.html
dom/midi/tests/port_ids_page_1.html
dom/midi/tests/port_ids_page_2.html
dom/midi/tests/refresh_port_list.html
dom/payments/test/test_abortPayment.html
dom/performance/tests/test_performance_paint_timing_helper.html
dom/performance/tests/test_timeOrigin.html
dom/plugins/test/mochitest/block_all_plugins.html
dom/plugins/test/mochitest/test_mixed_case_mime.html
dom/plugins/test/mochitest/test_plugin_fallback_focus.html
dom/security/test/csp/file_bug1505412_frame.html
dom/security/test/csp/file_bug1738418_parent.html
dom/security/test/csp/file_bug836922_npolicies.html
dom/security/test/csp/file_bug886164.html
dom/security/test/csp/file_bug886164_2.html
dom/security/test/csp/file_bug886164_3.html
dom/security/test/csp/file_bug886164_4.html
dom/security/test/csp/file_bug886164_5.html
dom/security/test/csp/file_bug886164_6.html
dom/security/test/csp/file_bug941404.html
dom/security/test/csp/file_doccomment_meta.html
dom/security/test/csp/file_docwrite_meta.html
dom/security/test/csp/file_image_nonce.html
dom/security/test/csp/file_link_rel_preload.html
dom/security/test/csp/file_main.html
dom/security/test/csp/file_meta_element.html
dom/security/test/csp/file_multi_policy_injection_bypass.html
dom/security/test/csp/file_multi_policy_injection_bypass_2.html
dom/security/test/csp/file_sandbox_1.html
dom/security/test/csp/file_sandbox_10.html
dom/security/test/csp/file_sandbox_11.html
dom/security/test/csp/file_sandbox_12.html
dom/security/test/csp/file_sandbox_13.html
dom/security/test/csp/file_sandbox_2.html
dom/security/test/csp/file_sandbox_3.html
dom/security/test/csp/file_sandbox_4.html
dom/security/test/csp/file_sandbox_5.html
dom/security/test/csp/file_sandbox_6.html
dom/security/test/csp/file_sandbox_7.html
dom/security/test/csp/file_sandbox_8.html
dom/security/test/csp/file_sandbox_9.html
dom/security/test/csp/file_upgrade_insecure.html
dom/security/test/csp/file_upgrade_insecure_meta.html
dom/security/test/csp/file_upgrade_insecure_reporting.html
dom/security/test/csp/test_CSP.html
dom/security/test/csp/test_bug1229639.html
dom/security/test/csp/test_bug1505412.html
dom/security/test/csp/test_bug1579094.html
dom/security/test/csp/test_bug836922_npolicies.html
dom/security/test/csp/test_bug885433.html
dom/security/test/csp/test_bug886164.html
dom/security/test/csp/test_bug888172.html
dom/security/test/csp/test_bug941404.html
dom/security/test/csp/test_evalscript.html
dom/security/test/csp/test_frameancestors.html
dom/security/test/csp/test_frameancestors_userpass.html
dom/security/test/csp/test_hash_source.html
dom/security/test/csp/test_inlinestyle.html
dom/security/test/csp/test_multi_policy_injection_bypass.html
dom/security/test/csp/test_nonce_source.html
dom/security/test/csp/test_redirects.html
dom/security/test/csp/test_report.html
dom/security/test/csp/test_report_for_import.html
dom/security/test/csp/test_xslt_inherits_csp.html
dom/security/test/general/file_framing_xfo_embed.html
dom/security/test/general/file_loads_nonscript.html
dom/security/test/general/file_toplevel_data_meta_redirect.html
dom/security/test/general/file_view_image_data_navigation.html
dom/security/test/general/test_assert_about_page_no_csp.html
dom/security/test/https-first/file_mixed_content_auto_upgrade.html
dom/security/test/https-first/file_navigation.html
dom/security/test/https-first/file_upgrade_insecure.html
dom/security/test/https-first/test_resource_upgrade.html
dom/security/test/https-only/file_upgrade_insecure.html
dom/security/test/https-only/file_websocket_exceptions_iframe.html
dom/security/test/mixedcontentblocker/file_csp_block_all_mixedcontent_and_mixed_content_display_upgrade.html
dom/serviceworkers/test/claim_clients/client.html
dom/serviceworkers/test/controller/index.html
dom/serviceworkers/test/fetch/index.html
dom/serviceworkers/test/file_notification_openWindow.html
dom/serviceworkers/test/skip_waiting_scope/index.html
dom/serviceworkers/test/sw_clients/file_blob_upload_frame.html
dom/serviceworkers/test/sw_clients/navigator.html
dom/serviceworkers/test/sw_clients/refresher.html
dom/serviceworkers/test/sw_clients/refresher_cached.html
dom/serviceworkers/test/sw_clients/simple.html
dom/serviceworkers/test/test_async_waituntil.html
dom/serviceworkers/test/test_bad_script_cache.html
dom/serviceworkers/test/test_bug1151916.html
dom/serviceworkers/test/test_bug1240436.html
dom/serviceworkers/test/test_bug1408734.html
dom/serviceworkers/test/test_claim.html
dom/serviceworkers/test/test_claim_oninstall.html
dom/serviceworkers/test/test_controller.html
dom/serviceworkers/test/test_cookie_fetch.html
dom/serviceworkers/test/test_cross_origin_url_after_redirect.html
dom/serviceworkers/test/test_csp_upgrade-insecure_intercept.html
dom/serviceworkers/test/test_empty_serviceworker.html
dom/serviceworkers/test/test_eval_allowed.html
dom/serviceworkers/test/test_event_listener_leaks.html
dom/serviceworkers/test/test_eventsource_intercept.html
dom/serviceworkers/test/test_fetch_event.html
dom/serviceworkers/test/test_fetch_event_no_orb.html
dom/serviceworkers/test/test_fetch_event_with_thirdpartypref.html
dom/serviceworkers/test/test_fetch_event_with_thirdpartypref_no_orb.html
dom/serviceworkers/test/test_file_blob_response.html
dom/serviceworkers/test/test_file_blob_upload.html
dom/serviceworkers/test/test_force_refresh.html
dom/serviceworkers/test/test_gzip_redirect.html
dom/serviceworkers/test/test_hsts_upgrade_intercept.html
dom/serviceworkers/test/test_https_fetch.html
dom/serviceworkers/test/test_https_fetch_cloned_response.html
dom/serviceworkers/test/test_https_origin_after_redirect.html
dom/serviceworkers/test/test_https_origin_after_redirect_cached.html
dom/serviceworkers/test/test_https_synth_fetch_from_cached_sw.html
dom/serviceworkers/test/test_imagecache.html
dom/serviceworkers/test/test_imagecache_max_age.html
dom/serviceworkers/test/test_importscript.html
dom/serviceworkers/test/test_importscript_mixedcontent.html
dom/serviceworkers/test/test_install_event.html
dom/serviceworkers/test/test_install_event_gc.html
dom/serviceworkers/test/test_installation_simple.html
dom/serviceworkers/test/test_match_all.html
dom/serviceworkers/test/test_match_all_advanced.html
dom/serviceworkers/test/test_match_all_client_id.html
dom/serviceworkers/test/test_match_all_client_properties.html
dom/serviceworkers/test/test_navigationPreload_disable_crash.html
dom/serviceworkers/test/test_navigator.html
dom/serviceworkers/test/test_nofetch_handler.html
dom/serviceworkers/test/test_not_intercept_plugin.html
dom/serviceworkers/test/test_notification_constructor_error.html
dom/serviceworkers/test/test_notification_openWindow.html
dom/serviceworkers/test/test_opaque_intercept.html
dom/serviceworkers/test/test_origin_after_redirect.html
dom/serviceworkers/test/test_origin_after_redirect_cached.html
dom/serviceworkers/test/test_origin_after_redirect_to_https.html
dom/serviceworkers/test/test_origin_after_redirect_to_https_cached.html
dom/serviceworkers/test/test_post_message.html
dom/serviceworkers/test/test_post_message_advanced.html
dom/serviceworkers/test/test_post_message_source.html
dom/serviceworkers/test/test_sandbox_intercept.html
dom/serviceworkers/test/test_sanitize.html
dom/serviceworkers/test/test_sanitize_domain.html
dom/serviceworkers/test/test_scopes.html
dom/serviceworkers/test/test_self_update_worker.html
dom/serviceworkers/test/test_service_worker_allowed.html
dom/serviceworkers/test/test_serviceworker.html
dom/serviceworkers/test/test_serviceworker_not_sharedworker.html
dom/serviceworkers/test/test_skip_waiting.html
dom/serviceworkers/test/test_strict_mode_warning.html
dom/serviceworkers/test/test_third_party_iframes.html
dom/serviceworkers/test/test_unregister.html
dom/serviceworkers/test/test_unresolved_fetch_interception.html
dom/serviceworkers/test/test_workerUnregister.html
dom/serviceworkers/test/test_workerUpdate.html
dom/serviceworkers/test/test_worker_reference_gc_timeout.html
dom/serviceworkers/test/test_workerupdatefoundevent.html
dom/serviceworkers/test/test_xslt.html
dom/serviceworkers/test/unregister/index.html
dom/serviceworkers/test/unregister/unregister.html
dom/serviceworkers/test/workerUpdate/update.html
dom/smil/test/file_smilWithTransition.html
dom/smil/test/test_smilWithTransition.html
dom/svg/test/test_getBBox-method.html
dom/svg/test/test_non-scaling-stroke.html
dom/svg/test/test_style_sheet.html
dom/svg/test/test_text_update.html
dom/tests/browser/dummy.html
dom/tests/browser/image.html
dom/tests/browser/test_new_window_from_content_child.html
dom/tests/mochitest/beacon/test_beaconOriginHeader.html
dom/tests/mochitest/beacon/test_beaconRedirect.html
dom/tests/mochitest/bugs/file_bug809290_b1.html
dom/tests/mochitest/bugs/file_bug809290_b2.html
dom/tests/mochitest/bugs/iframe_bug304459-1.html
dom/tests/mochitest/bugs/iframe_bug304459-2.html
dom/tests/mochitest/bugs/test_bug265203.html
dom/tests/mochitest/bugs/test_bug414291.html
dom/tests/mochitest/bugs/test_bug430276.html
dom/tests/mochitest/bugs/test_bug743615.html
dom/tests/mochitest/bugs/test_domparser_after_blank.html
dom/tests/mochitest/chrome/child_focus_frame.html
dom/tests/mochitest/chrome/file_popup_blocker_chrome.html
dom/tests/mochitest/chrome/focus_frameset.html
dom/tests/mochitest/chrome/queryCaretRectUnix.html
dom/tests/mochitest/chrome/queryCaretRectWin.html
dom/tests/mochitest/chrome/test_intlUtils_getDisplayNames.html
dom/tests/mochitest/chrome/test_intlUtils_isAppLocaleRTL.html
dom/tests/mochitest/chrome/test_nodesFromRect.html
dom/tests/mochitest/chrome/test_queryCaretRect.html
dom/tests/mochitest/chrome/test_selectAtPoint.html
dom/tests/mochitest/chrome/test_window_getRegionalPrefsLocales.html
dom/tests/mochitest/chrome/window_focus.xhtml
dom/tests/mochitest/general/file_clonewrapper.html
dom/tests/mochitest/general/file_focusrings.html
dom/tests/mochitest/general/navigation_timing.html
dom/tests/mochitest/general/resource_timing_cross_origin.html
dom/tests/mochitest/general/test_CCW_optimization.html
dom/tests/mochitest/general/test_DOMMatrix.html
dom/tests/mochitest/general/test_bug628069_1.html
dom/tests/mochitest/general/test_bug628069_2.html
dom/tests/mochitest/general/test_bug653364.html
dom/tests/mochitest/general/test_offsets.html
dom/tests/mochitest/general/test_paste_selection.html
dom/tests/mochitest/general/test_resource_timing_frameset.html
dom/tests/mochitest/geolocation/geolocation.html
dom/tests/mochitest/geolocation/popup.html
dom/tests/mochitest/localstorage/test_localStorageKeyOrder.html
dom/tests/mochitest/webcomponents/test_event_composed.html
dom/tests/mochitest/whatwg/test_postMessage_special.xhtml
dom/url/tests/test_urlSearchParams_utf8.html
dom/url/tests/test_worker_protocol.html
dom/url/tests/test_worker_url.html
dom/url/tests/test_worker_urlApi.html
dom/url/tests/test_worker_urlSearchParams.html
dom/url/tests/test_worker_url_exceptions.html
dom/webauthn/tests/browser/tab_webauthn_result.html
dom/webauthn/tests/test_webauthn_abort_signal.html
dom/webauthn/tests/test_webauthn_attestation_conveyance.html
dom/webauthn/tests/test_webauthn_authenticator_selection.html
dom/webauthn/tests/test_webauthn_authenticator_transports.html
dom/webauthn/tests/test_webauthn_get_assertion.html
dom/webauthn/tests/test_webauthn_get_assertion_dead_object.html
dom/webauthn/tests/test_webauthn_isplatformauthenticatoravailable.html
dom/webauthn/tests/test_webauthn_loopback.html
dom/webauthn/tests/test_webauthn_make_credential.html
dom/webauthn/tests/test_webauthn_no_token.html
dom/webauthn/tests/test_webauthn_override_request.html
dom/webauthn/tests/test_webauthn_sameorigin.html
dom/webauthn/tests/test_webauthn_sameoriginwithancestors.html
dom/webauthn/tests/test_webauthn_store_credential.html
dom/websocket/tests/test_bug1384658.html
dom/websocket/tests/test_event_listener_leaks.html
dom/websocket/tests/test_websocket1.html
dom/websocket/tests/test_websocket2.html
dom/websocket/tests/test_websocket3.html
dom/websocket/tests/test_websocket4.html
dom/websocket/tests/test_websocket5.html
dom/websocket/tests/test_websocket_bigBlob.html
dom/websocket/tests/test_websocket_longString.html
dom/websocket/tests/test_websocket_mixed_content.html
dom/websocket/tests/test_websocket_mixed_content_opener.html
dom/websocket/tests/test_websocket_no_duplicate_packet.html
dom/websocket/tests/test_websocket_sharedWorker.html
dom/websocket/tests/test_worker_websocket1.html
dom/websocket/tests/test_worker_websocket2.html
dom/websocket/tests/test_worker_websocket3.html
dom/websocket/tests/test_worker_websocket4.html
dom/websocket/tests/test_worker_websocket5.html
dom/websocket/tests/test_worker_websocket_basic.html
dom/websocket/tests/test_worker_websocket_loadgroup.html
dom/websocket/tests/websocket_hybi/test_receive-arraybuffer.html
dom/websocket/tests/websocket_hybi/test_receive-blob.html
dom/websocket/tests/websocket_hybi/test_send-arraybuffer.html
dom/websocket/tests/websocket_hybi/test_send-blob.html
dom/workers/test/test_bug1002702.html
dom/workers/test/test_bug1060621.html
dom/workers/test/test_bug1062920.html
dom/workers/test/test_bug1104064.html
dom/workers/test/test_bug1132395.html
dom/workers/test/test_bug1132924.html
dom/workers/test/test_bug1301094.html
dom/workers/test/test_bug1317725.html
dom/workers/test/test_bug949946.html
dom/workers/test/test_bug998474.html
dom/workers/test/test_file.xhtml
dom/workers/test/test_fileBlobPosting.xhtml
dom/workers/test/test_fileBlobSubWorker.xhtml
dom/workers/test/test_filePosting.xhtml
dom/workers/test/test_fileReadSlice.xhtml
dom/workers/test/test_fileReaderSync.xhtml
dom/workers/test/test_fileReaderSyncErrors.xhtml
dom/workers/test/test_fileSlice.xhtml
dom/workers/test/test_fileSubWorker.xhtml
dom/workers/test/test_onLine.html
dom/workers/test/test_promise.html
dom/workers/test/test_referrer.html
dom/workers/test/test_rvals.html
dom/workers/test/test_sharedworker_event_listener_leaks.html
dom/xhr/tests/test_event_listener_leaks.html
dom/xhr/tests/test_worker_xhr_3rdparty.html
dom/xhr/tests/test_worker_xhr_doubleSend.html
editor/composer/test/test_bug389350.html
editor/libeditor/tests/bug527935.html
editor/libeditor/tests/test_bug1186799.html
editor/libeditor/tests/test_bug1248128.html
editor/libeditor/tests/test_bug1248185.html
editor/libeditor/tests/test_bug1270235.html
editor/libeditor/tests/test_bug1620778.html
editor/libeditor/tests/test_bug358033.html
editor/libeditor/tests/test_bug537046.html
editor/libeditor/tests/test_password_input_with_unmasked_range.html
editor/libeditor/tests/test_pasting_table_rows.html
editor/libeditor/tests/test_textarea_value_not_include_cr.html
editor/spellchecker/tests/test_bug1200533.html
editor/spellchecker/tests/test_bug1204147.html
editor/spellchecker/tests/test_bug1205983.html
editor/spellchecker/tests/test_bug1209414.html
editor/spellchecker/tests/test_bug1365383.html
editor/spellchecker/tests/test_bug1402822.html
editor/spellchecker/tests/test_bug1761273.html
editor/spellchecker/tests/test_bug1773802.html
editor/spellchecker/tests/test_bug678842.html
editor/spellchecker/tests/test_bug697981.html
editor/spellchecker/tests/test_bug717433.html
gfx/layers/apz/test/mochitest/helper_bug1637135_narrow_viewport.html
gfx/layers/apz/test/mochitest/helper_bug1638441_fixed_pos_hit_test.html
gfx/layers/apz/test/mochitest/helper_bug1638458_contextmenu.html
gfx/layers/apz/test/mochitest/helper_content_response_timeout.html
gfx/layers/apz/test/mochitest/helper_doubletap_zoom_noscroll.html
gfx/layers/apz/test/mochitest/helper_hittest_fixed_bg.html
gfx/layers/apz/test/mochitest/helper_interrupted_reflow.html
gfx/layers/apz/test/mochitest/helper_overscroll_behavior_bug1425573.html
gfx/layers/apz/test/mochitest/helper_touch_action_regions.html
gfx/layers/apz/test/mochitest/helper_visualscroll_clamp_restore.html
gfx/layers/apz/test/mochitest/helper_zoomToFocusedInput_iframe.html
gfx/layers/apz/test/mochitest/test_bug1277814.html
gfx/layers/apz/test/mochitest/test_frame_reconstruction.html
image/test/browser/image.html
image/test/mochitest/bug490949-iframe.html
image/test/mochitest/bug496292-iframe-1.html
image/test/mochitest/bug496292-iframe-2.html
image/test/mochitest/bug496292-iframe-ref.html
image/test/mochitest/bug497665-iframe.html
image/test/mochitest/bug671906-iframe.html
image/test/mochitest/bug89419-iframe.html
image/test/mochitest/test_bug552605-1.html
image/test/mochitest/test_bug552605-2.html
image/test/mochitest/test_error_events.html
image/test/mochitest/test_mq_dynamic_svg.html
image/test/mochitest/test_svg_animatedGIF.html
intl/uconv/tests/test_big5_encoder.html
intl/uconv/tests/test_bug335816.html
js/src/README.html
js/xpconnect/tests/chrome/file_bug618176.xhtml
layout/base/tests/accessiblecaret_magnifier.html
layout/base/tests/bug1263288-ref.html
layout/base/tests/bug1263288.html
layout/base/tests/bug1448730.html
layout/base/tests/bug1484094-1-ref.html
layout/base/tests/bug1484094-1.html
layout/base/tests/bug1484094-2-ref.html
layout/base/tests/bug1484094-2.html
layout/base/tests/bug389321-1-ref.html
layout/base/tests/bug389321-1.html
layout/base/tests/chrome/test_bug551434.html
layout/base/tests/input-stoppropagation-ref.html
layout/base/tests/input-stoppropagation.html
layout/base/tests/test_accessiblecaret_magnifier.html
layout/base/tests/test_bug1448730.html
layout/base/tests/test_bug386575.xhtml
layout/base/tests/test_bug435293-interaction.html
layout/base/tests/test_bug449781.html
layout/base/tests/test_bug629838.html
layout/base/tests/test_bug677878.html
layout/base/tests/test_bug718809.html
layout/base/tests/test_bug725426.html
layout/base/tests/test_dynamic_toolbar_max_height.html
layout/base/tests/test_event_target_radius.html
layout/base/tests/test_mozPaintCount.html
layout/base/tests/test_partialbg.html
layout/forms/test/test_bug644542.html
layout/generic/test/file_bug448987.html
layout/generic/test/file_bug448987_notref.html
layout/generic/test/file_bug448987_ref.html
layout/generic/test/file_bug579767_1.html
layout/generic/test/file_bug579767_2.html
layout/generic/test/test_bug1408607.html
layout/generic/test/test_bug405178.html
layout/generic/test/test_bug416168.html
layout/generic/test/test_bug719503.html
layout/generic/test/test_bug719515.html
layout/generic/test/test_bug719518.html
layout/generic/test/test_bug719523.html
layout/generic/test/test_bug791616.html
layout/generic/test/test_bug841361.html
layout/generic/test/test_bug938772.html
layout/generic/test/test_page_scroll_with_fixed_pos.html
layout/generic/test/test_reframe_for_lazy_load_image.html
layout/generic/test/test_scroll_behavior.html
layout/inspector/tests/test_bug557726.html
layout/style/test/chrome/test_bug1371453.html
layout/style/test/chrome/test_display_mode.html
layout/style/test/file_bug1381233.html
layout/style/test/test_addSheet.html
layout/style/test/test_animations_event_handler_attribute.html
layout/style/test/test_animations_event_order.html
layout/style/test/test_animations_omta.html
layout/style/test/test_animations_omta_scroll.html
layout/style/test/test_animations_omta_scroll_rtl.html
layout/style/test/test_area_url_cursor.html
layout/style/test/test_bug1443344-1.html
layout/style/test/test_bug1443344-2.html
layout/style/test/test_bug302186.html
layout/style/test/test_bug363146.html
layout/style/test/test_bug379440.html
layout/style/test/test_bug391221.html
layout/style/test/test_bug399349.html
layout/style/test/test_bug412901.html
layout/style/test/test_ch_ex_no_infloops.html
layout/style/test/test_computed_style_min_size_auto.html
layout/style/test/test_flexbox_focus_order.html
layout/style/test/test_font_loading_api.html
layout/style/test/test_grid_computed_values.html
layout/style/test/test_mql_event_listener_leaks.html
layout/style/test/test_pointer-events.html
layout/style/test/test_reframe_image_loading.html
layout/tables/test/test_bug337124.html
mobile/android/android-components/docs/_includes/footer.html
mobile/android/android-components/docs/_includes/head.html
mobile/android/android-components/docs/_layouts/home.html
mobile/android/fenix/app/src/androidTest/assets/pages/htmlControls.html
mobile/android/fenix/app/src/androidTest/assets/pages/trackingPage.html
mobile/android/geckoview/src/androidTest/assets/www/accessibility/test-headings.html
mobile/android/geckoview/src/androidTest/assets/www/autoplay.html
mobile/android/geckoview/src/androidTest/assets/www/badVideoPath.html
mobile/android/geckoview/src/androidTest/assets/www/context_menu_video.html
mobile/android/geckoview/src/androidTest/assets/www/mp4.html
mobile/android/geckoview/src/androidTest/assets/www/webm.html
mobile/shared/components/extensions/test/mochitest/file_dummy.html
netwerk/test/browser/103_preload_iframe.html
netwerk/test/browser/post.html
netwerk/test/mochitests/file_domain_hierarchy_inner.html
netwerk/test/mochitests/file_domain_hierarchy_inner_inner.html
netwerk/test/mochitests/file_domain_inner.html
netwerk/test/mochitests/file_localhost_inner.html
netwerk/test/mochitests/file_loopback_inner.html
netwerk/test/mochitests/file_subdomain_inner.html
netwerk/test/mochitests/test_idn_redirect.html
netwerk/test/mochitests/test_redirect_ref.html
parser/html/java/named-character-references.html
testing/marionette/harness/marionette_harness/www/click_out_of_bounds_overflow.html
testing/mochitest/tests/Harness_sanity/test_SpecialPowersExtension.html
testing/mochitest/tests/Harness_sanity/test_SpecialPowersLoadPrivilegedScript.html
testing/mochitest/tests/Harness_sanity/test_sanityEventUtils.html
testing/mochitest/tests/browser/dummy.html
testing/talos/talos/tests/cpstartup/target.html
testing/talos/talos/tests/tabpaint/target.html
testing/talos/talos/tests/tart/tart.html
toolkit/components/certviewer/tests/browser/dummy_page.html
toolkit/components/extensions/test/mochitest/file_with_subframes_and_embed.html
toolkit/components/extensions/test/mochitest/test_ext_webrequest_upload.html
toolkit/components/passwordmgr/test/browser/form_disabled_readonly_passwordField.html
toolkit/components/passwordmgr/test/browser/multiple_forms.html
toolkit/components/passwordmgr/test/mochitest/test_LoginManagerContent_passwordEditedOrGenerated.html
toolkit/components/passwordmgr/test/mochitest/test_autocomplete_highlight.html
toolkit/components/passwordmgr/test/mochitest/test_autocomplete_highlight_non_login.html
toolkit/components/passwordmgr/test/mochitest/test_autocomplete_highlight_username_only_form.html
toolkit/components/passwordmgr/test/mochitest/test_autofill_highlight.html
toolkit/components/passwordmgr/test/mochitest/test_autofill_highlight_empty_username.html
toolkit/components/passwordmgr/test/mochitest/test_autofill_highlight_username_only_form.html
toolkit/components/passwordmgr/test/mochitest/test_autofill_username-only.html
toolkit/components/passwordmgr/test/mochitest/test_autofill_username-only_threshold.html
toolkit/components/passwordmgr/test/mochitest/test_basic_form_honor_autocomplete_off.html
toolkit/components/places/tests/unit/bookmarks.corrupt.html
toolkit/components/places/tests/unit/bookmarks.preplaces.html
toolkit/components/places/tests/unit/bookmarks_html_localized.html
toolkit/components/places/tests/unit/bookmarks_html_singleframe.html
toolkit/components/printing/tests/simplifyArticleSample.html
toolkit/components/processtools/tests/browser/dummy.html
toolkit/components/reader/tests/browser/readerModeArticleMedium.html
toolkit/components/reader/tests/browser/readerModeArticleShort.html
toolkit/components/translations/content/translations.html
toolkit/components/translations/tests/browser/translations-tester-no-tag.html
toolkit/components/url-classifier/tests/mochitest/classifiedAnnotatedFrame.html
toolkit/components/url-classifier/tests/mochitest/classifiedAnnotatedPBFrame.html
toolkit/components/url-classifier/tests/mochitest/classifierFrame.html
toolkit/components/url-classifier/tests/mochitest/gethashFrame.html
toolkit/components/url-classifier/tests/mochitest/sandboxed.html
toolkit/components/url-classifier/tests/mochitest/test_classifier.html
toolkit/components/url-classifier/tests/mochitest/test_classifier_worker.html
toolkit/components/url-classifier/tests/mochitest/trackerFrame.html
toolkit/components/windowcreator/test/file_form_state.html
toolkit/components/windowcreator/test/test_window_open_position_constraint.html
toolkit/components/windowcreator/test/test_window_open_units.html
toolkit/components/windowwatcher/test/file_storage_copied.html
toolkit/components/windowwatcher/test/file_test_dialog.html
toolkit/content/aboutGlean.html
toolkit/content/aboutNetError.xhtml
toolkit/content/aboutUrlClassifier.xhtml
toolkit/content/tests/browser/file_outside_viewport_videos.html
toolkit/content/tests/browser/file_redirect.html
toolkit/content/tests/browser/file_redirect_to.html
toolkit/content/tests/browser/image_page.html
toolkit/content/tests/mochitest/file_mousecapture4.html
toolkit/mozapps/extensions/test/browser/webapi_checkframed.html
toolkit/xre/test/test_fpuhandler.html
tools/jprof/README.html
uriloader/exthandler/tests/mochitest/save_filenames.html

##############################################################################
# The list below is copied from .eslintrc-ignores.js. Prettier doesn't currently
# support multiple ignore files or dynamic ignore configurations.
# When this is implemented, we'll update the configuration below (bug 1825508)
##############################################################################

# Always ignore crashtests - specially crafted files that originally caused a
# crash.
**/crashtests/
# Also ignore reftest - specially crafted to produce expected output.
**/reftest/
**/reftests/
# Don't ignore the reftest harness files.
!/layout/tools/reftest/

# Exclude expected objdirs.
obj*/

# build/ third-party code
build/pgo/js-input/

# browser/ exclusions
browser/app/
browser/branding/**/firefox-branding.js
# Gzipped test file.
browser/base/content/test/general/gZipOfflineChild.html
browser/base/content/test/urlbar/file_blank_but_not_blank.html
# Test files that are really json not js, and don't need to be linted.
browser/components/sessionstore/test/unit/data/sessionstore_valid.js
browser/components/sessionstore/test/unit/data/sessionstore_invalid.js
# This file is split into two in order to keep it as a valid json file
# for documentation purposes (policies.json) but to be accessed by the
# code as a .sys.mjs (schema.sys.mjs)
browser/components/enterprisepolicies/schemas/schema.sys.mjs
# generated or library files in pocket
browser/components/pocket/content/panels/js/tmpl.js
browser/components/pocket/content/panels/js/vendor.bundle.js
browser/components/pocket/content/panels/js/main.bundle.js
# Include the Storybook config files.
!browser/components/storybook/.storybook/
!browser/components/storybook/.storybook/*.js

# Ignore newtab files
browser/components/newtab/data/
browser/components/newtab/logs/

# Ignore about:welcome files
browser/components/aboutwelcome/logs/

# The only file in browser/locales/ is pre-processed.
browser/locales/
# Generated data files
browser/extensions/formautofill/phonenumberutils/PhoneNumberMetaData.jsm

# Ignore devtools debugger files which aren't intended for linting.
devtools/client/debugger/bin/
devtools/client/debugger/configs/
devtools/client/debugger/dist/
devtools/client/debugger/images/
devtools/client/debugger/packages/
devtools/client/debugger/test/mochitest/examples/
devtools/client/debugger/index.html
devtools/client/debugger/webpack.config.js

# Ignore devtools preferences files
devtools/client/preferences/

# Ignore devtools generated code
devtools/client/webconsole/test/node/fixtures/stubs/*.js
!devtools/client/webconsole/test/node/fixtures/stubs/index.js
devtools/client/shared/components/test/node/stubs/reps/*.js
devtools/client/shared/source-map-loader/test/browser/fixtures/*.js

# Ignore devtools files testing sourcemaps / code style
devtools/client/framework/test/code_*
devtools/client/inspector/markup/test/events_bundle.js
devtools/client/netmonitor/test/xhr_bundle.js
devtools/client/webconsole/test/browser/code_bundle_nosource.js
devtools/client/webconsole/test/browser/code_bundle_invalidmap.js
devtools/client/webconsole/test/browser/test-autocomplete-mapped.js
devtools/client/webconsole/test/browser/test-autocomplete-mapped.src.js
devtools/client/inspector/markup/test/shadowdom_open_debugger.min.js
devtools/client/webconsole/test/browser/test-click-function-to-source*.js
devtools/client/webconsole/test/browser/test-external-script-errors.js
devtools/client/webconsole/test/browser/test-mangled-function.*
devtools/client/webconsole/test/browser/test-message-categories-canvas-css.js
devtools/client/webconsole/test/browser/test-message-categories-empty-getelementbyid.js
devtools/client/webconsole/test/browser/test-sourcemap*.js
devtools/server/tests/xpcshell/setBreakpoint*
devtools/server/tests/xpcshell/sourcemapped.js

# Ignore generated code from wasm-bindgen
devtools/client/performance-new/shared/profiler_get_symbols.js

# Testing syntax error
devtools/client/aboutdebugging/test/browser/resources/bad-extensions/invalid-json/manifest.json
devtools/client/jsonview/test/invalid_json.json
devtools/client/webconsole/test/browser/test-syntaxerror-worklet.js

# devtools specific format test file
devtools/server/tests/xpcshell/xpcshell_debugging_script.js
devtools/shared/webconsole/test/browser/data.json

# Generated
dom/canvas/test/webgl-conf/generated/

# Intentionally invalid/not parsable
dom/html/test/test_bug677658.html
dom/svg/test/test_nonAnimStrings.xhtml
dom/svg/test/test_SVG_namespace_ids.html

# Strange encodings
dom/base/test/file_bug687859-16.js
dom/base/test/file_bug707142_utf-16.json
dom/encoding/test/test_utf16_files.html
dom/encoding/test/file_utf16_be_bom.js
dom/encoding/test/file_utf16_le_bom.js

# Service workers fixtures which require specific resource caching.
dom/base/test/file_js_cache.js
dom/serviceworkers/test/file_js_cache.js

# Intentional broken files
dom/base/test/file_js_cache_syntax_error.js
dom/base/test/jsmodules/test_scriptNotParsedAsModule.html
dom/base/test/jsmodules/test_syntaxError.html
dom/base/test/jsmodules/test_syntaxErrorAsync.html
dom/base/test/jsmodules/module_badSyntax.mjs
dom/base/test/jsmodules/test_syntaxErrorInline.html
dom/base/test/jsmodules/test_syntaxErrorInlineAsync.html
dom/base/test/test_bug687859.html
dom/media/webrtc/tests/mochitests/identity/idp-bad.js
dom/security/test/general/file_nonscript.json
dom/serviceworkers/test/file_js_cache_syntax_error.js
dom/serviceworkers/test/parse_error_worker.js
dom/tests/mochitest/bugs/test_bug531176.html
dom/webauthn/tests/cbor.js
dom/workers/test/importScripts_worker_imported3.js
dom/workers/test/invalid.js
dom/workers/test/threadErrors_worker1.js

# Bug 1527075: This directory is linted in github repository
intl/l10n/

# Exclude everything but self-hosted JS
js/examples/
js/public/
js/src/devtools/
js/src/jit-test/
js/src/tests/
js/src/Y.js

# Changes to XPConnect tests must be carefully audited.
js/xpconnect/tests/mochitest/
js/xpconnect/tests/unit/

# Fuzzing code for testing only, targeting the JS shell
js/src/fuzz-tests/

# Template file
mobile/android/docs/geckoview/assets/js/search-data.json

# Uses `#filter substitution`
mobile/android/app/geckoview-prefs.js

# Not much JS to lint and non-standard at that
mobile/android/installer/
mobile/android/locales/

# Android - Web extensions: manifest.json files may be generated by the build system.
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/manifest.json
mobile/android/android-components/samples/browser/src/main/assets/extensions/test/manifest.json

# Pre-processed/pref files
modules/libpref/greprefs.js
modules/libpref/init/all.js
modules/libpref/test/unit/*data/
toolkit/components/backgroundtasks/defaults/backgroundtasks.js
toolkit/components/backgroundtasks/defaults/backgroundtasks_browser.js

# Only contains non-standard test files.
python/

# These are (mainly) imported code that we don't want to lint to make imports easier.
remote/cdp/Protocol.sys.mjs
remote/cdp/test/browser/chrome-remote-interface.js
remote/marionette/atom.sys.mjs

# This file explicitly has a syntax error and cannot be parsed by eslint.
remote/shared/messagehandler/test/browser/resources/modules/root/invalid.sys.mjs

# services/ exclusions

# Webpack-bundled library
services/fxaccounts/FxAccountsPairingChannel.sys.mjs

# Servo is imported.
servo/

# Rust/Cargo output from running `cargo` directly
target/
servo/ports/geckolib/target/
dom/base/rust/target/
servo/components/style/target/
dom/webgpu/tests/cts/vendor/target/

# Test files that we don't want to lint (preprocessed, minified etc)
testing/condprofile/condprof/tests/profile
testing/mozbase/mozprofile/tests/files/prefs_with_comments.js
testing/mozharness/configs/test/test_malformed.json
testing/talos/talos/startup_test/sessionrestore/profile/sessionstore.js
testing/talos/talos/startup_test/sessionrestore/profile-manywindows/sessionstore.js
testing/talos/talos/tests/offscreencanvas/benchmarks/video/demuxer_mp4.js
testing/talos/talos/tests/offscreencanvas/benchmarks/video/mp4box.all.min.js
# Python json.
testing/talos/talos/unittests/test_talosconfig_browser_config.json
testing/talos/talos/unittests/test_talosconfig_test_config.json
# Runing Talos may extract data here, see bug 1435677.
testing/talos/talos/tests/tp5n/
testing/talos/talos/fis/tp5n/

# Mainly third-party related code, that shouldn't be linted.
testing/web-platform/

# toolkit/ exclusions

# Intentionally invalid files
toolkit/components/workerloader/tests/moduleF-syntax-error.js
toolkit/components/enterprisepolicies/tests/browser/config_broken_json.json
toolkit/mozapps/extensions/test/xpcshell/data/test_AddonRepository_fail.json

# Built files
toolkit/components/pdfjs/content/build
toolkit/components/pdfjs/content/web

# Uses preprocessing
toolkit/components/reader/Readerable.sys.mjs

# Generated & special files in cld2
toolkit/components/translation/cld2/

# Uses preprocessing
toolkit/mozapps/update/tests/data/xpcshellConstantsPP.js
toolkit/modules/AppConstants.sys.mjs

# Tests of ESLint command.
tools/lint/test/files

# Uses special template formatting.
tools/tryselect/selectors/chooser/templates/chooser.html

# Ignore preprocessed *(P)refs.js files in update-packaging.
tools/update-packaging/**/*refs.js

# Ignore pre-generated webpack and typescript transpiled files for translations
browser/extensions/translations/extension/

# "scaffolding" used by uniffi which isn't valid JS in its original form.
toolkit/components/uniffi-bindgen-gecko-js/src/templates/js/
toolkit/components/uniffi-bindgen-gecko-js/components/generated/*

##############################################################################
# The list below is copied from ThirdPartyPaths.txt. Prettier doesn't currently
# support multiple ignore files or dynamic ignore configurations.
# When this is implemented, we'll update the configuration below (bug 1825508)
##############################################################################

browser/components/newtab/vendor/
browser/components/pocket/content/panels/css/normalize.scss
browser/components/pocket/content/panels/js/vendor/
browser/components/storybook/node_modules/
browser/extensions/formautofill/content/third-party/
browser/extensions/formautofill/test/fixtures/third_party/
devtools/client/inspector/markup/test/lib_*
devtools/client/jsonview/lib/require.js
devtools/client/shared/build/babel.js
devtools/client/shared/source-map/
devtools/client/shared/sourceeditor/codemirror/
devtools/client/shared/sourceeditor/codemirror6/
devtools/client/shared/sourceeditor/test/cm_mode_ruby.js
devtools/client/shared/sourceeditor/test/codemirror/
devtools/client/shared/vendor/
devtools/client/inspector/markup/test/helper_diff.js
devtools/client/debugger/src/workers/parser/utils/parse-script-tags/
devtools/shared/acorn/
devtools/shared/compatibility/dataset/css-properties.json
devtools/shared/heapsnapshot/CoreDump.pb.cc
devtools/shared/heapsnapshot/CoreDump.pb.h
devtools/shared/jsbeautify/
devtools/shared/node-properties/
devtools/shared/qrcode/decoder/
devtools/shared/qrcode/encoder/
devtools/shared/sprintfjs/
devtools/shared/storage/vendor/
dom/canvas/test/webgl-conf/checkout/
dom/imptests/
dom/media/gmp/rlz/
dom/media/gmp/widevine-adapter/content_decryption_module_export.h
dom/media/gmp/widevine-adapter/content_decryption_module_ext.h
dom/media/gmp/widevine-adapter/content_decryption_module.h
dom/media/gmp/widevine-adapter/content_decryption_module_proxy.h
dom/media/platforms/ffmpeg/ffmpeg57/
dom/media/platforms/ffmpeg/ffmpeg58/
dom/media/platforms/ffmpeg/ffmpeg59/
dom/media/platforms/ffmpeg/ffmpeg60/
dom/media/platforms/ffmpeg/ffmpeg61/
dom/media/platforms/ffmpeg/libav53/
dom/media/platforms/ffmpeg/libav54/
dom/media/platforms/ffmpeg/libav55/
dom/media/webaudio/test/blink/
dom/media/webrtc/tests/mochitests/helpers_from_wpt/sdp.js
dom/media/webrtc/transport/third_party/
dom/media/webspeech/recognition/endpointer.cc
dom/media/webspeech/recognition/endpointer.h
dom/media/webspeech/recognition/energy_endpointer.cc
dom/media/webspeech/recognition/energy_endpointer.h
dom/media/webspeech/recognition/energy_endpointer_params.cc
dom/media/webspeech/recognition/energy_endpointer_params.h
dom/media/webvtt/vtt.sys.mjs
dom/tests/mochitest/ajax/
dom/tests/mochitest/dom-level1-core/
dom/tests/mochitest/dom-level2-core/
dom/tests/mochitest/dom-level2-html/
dom/u2f/tests/pkijs/
dom/webauthn/tests/pkijs/
dom/webgpu/tests/cts/checkout/
editor/libeditor/tests/browserscope/lib/richtext/
editor/libeditor/tests/browserscope/lib/richtext2/
extensions/spellcheck/hunspell/src/
function2/
gfx/angle/checkout/
gfx/cairo/
gfx/graphite2/
gfx/harfbuzz/
gfx/ots/
gfx/qcms/
gfx/sfntly/
gfx/skia/
gfx/vr/service/openvr/
gfx/vr/service/openvr/headers/openvr.h
gfx/vr/service/openvr/src/README
gfx/vr/service/openvr/src/dirtools_public.cpp
gfx/vr/service/openvr/src/dirtools_public.h
gfx/vr/service/openvr/src/envvartools_public.cpp
gfx/vr/service/openvr/src/envvartools_public.h
gfx/vr/service/openvr/src/hmderrors_public.cpp
gfx/vr/service/openvr/src/hmderrors_public.h
gfx/vr/service/openvr/src/ivrclientcore.h
gfx/vr/service/openvr/src/openvr_api_public.cpp
gfx/vr/service/openvr/src/pathtools_public.cpp
gfx/vr/service/openvr/src/pathtools_public.h
gfx/vr/service/openvr/src/sharedlibtools_public.cpp
gfx/vr/service/openvr/src/sharedlibtools_public.h
gfx/vr/service/openvr/src/strtools_public.cpp
gfx/vr/service/openvr/src/strtools_public.h
gfx/vr/service/openvr/src/vrpathregistry_public.cpp
gfx/vr/service/openvr/src/vrpathregistry_public.h
gfx/wr/
gfx/ycbcr/
intl/icu/
intl/icu_capi/
ipc/chromium/src/third_party/
js/src/ctypes/libffi/
js/src/dtoa.c
js/src/editline/
js/src/jit/arm64/vixl/
js/src/octane/
js/src/tests/test262/
js/src/vtune/disable_warnings.h
js/src/vtune/ittnotify_config.h
js/src/vtune/ittnotify.h
js/src/vtune/ittnotify_static.c
js/src/vtune/ittnotify_static.h
js/src/vtune/ittnotify_types.h
js/src/vtune/jitprofiling.c
js/src/vtune/jitprofiling.h
js/src/vtune/legacy/
js/src/zydis/
layout/docs/css-gap-decorations/
media/ffvpx/
media/kiss_fft/
media/libaom/
media/libcubeb/
media/libdav1d/
media/libjpeg/
media/libmkv/
media/libnestegg/
media/libogg/
media/libopus/
media/libpng/
media/libsoundtouch/
media/libspeex_resampler/
media/libvorbis/
media/libvpx/
media/libwebp/
media/libyuv/
media/mozva/va
media/mp4parse-rust/
media/openmax_il/
media/webrtc/signaling/gtest/MockCall.h
mfbt/double-conversion/double-conversion/
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/JSDOMParser-0.4.2.js
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/readability-0.4.2.js
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/readability-readerable-0.4.2.js
mobile/android/exoplayer2/
modules/brotli/
modules/fdlibm/
modules/freetype2/
modules/woff2/
modules/xz-embedded/
modules/zlib/
mozglue/misc/decimal/
mozglue/static/lz4/.*
mozglue/tests/glibc_printf_tests/
netwerk/dns/nsIDNKitInterface.h
netwerk/sctp/src/
netwerk/srtp/src/
nsprpub/
other-licenses/
parser/expat/
remote/cdp/test/browser/chrome-remote-interface.js
remote/test/puppeteer/
security/manager/tools/log_list.json
security/nss/
security/sandbox/chromium/
security/sandbox/chromium-shim/
security/sandbox/linux/launch/glibc_hack/sandbox/linux/services/namespace_sandbox.cc
services/common/kinto-http-client.sys.mjs
services/common/kinto-offline-client.sys.mjs
testing/gtest/gmock/
testing/gtest/gtest/
testing/mochitest/pywebsocket3/
testing/modules/sinon-7.2.7.js
testing/mozbase/mozproxy/mozproxy/backends/mitm/scripts/catapult/
testing/talos/talos/tests/devtools/addon/content/pages/
testing/talos/talos/tests/dromaeo/
testing/talos/talos/tests/kraken/
testing/talos/talos/tests/offscreencanvas/benchmarks/video/demuxer_mp4.js
testing/talos/talos/tests/offscreencanvas/benchmarks/video/mp4box.all.min.js
testing/talos/talos/tests/v8_7/
testing/web-platform/tests/resources/webidl2/
testing/web-platform/tests/tools/third_party/
testing/web-platform/mozilla/tests/webgpu/
testing/xpcshell/dns-packet/
testing/xpcshell/node_ip/
testing/xpcshell/node-http2/
testing/xpcshell/node-ws/
third_party/
toolkit/components/certviewer/content/vendor/
toolkit/components/jsoncpp/
toolkit/components/ml/vendor/
toolkit/components/normandy/vendor/
toolkit/components/passwordmgr/shared/PasswordRulesParser.sys.mjs
toolkit/components/protobuf/
toolkit/components/reader/readability/
toolkit/components/resistfingerprinting/content/gl-matrix.js
toolkit/components/translation/cld2/
toolkit/components/translations/bergamot-translator/thirdparty
toolkit/components/translations/bergamot-translator/bergamot-translator.js
toolkit/components/url-classifier/chromium/
toolkit/components/utils/mozjexl.sys.mjs
toolkit/components/viaduct/fetch_msg_types.pb.cc
toolkit/components/viaduct/fetch_msg_types.pb.h
toolkit/content/widgets/vendor/lit.all.mjs
toolkit/crashreporter/breakpad-client/
toolkit/crashreporter/google-breakpad/
tools/fuzzing/libfuzzer/
tools/profiler/core/vtune/
xpcom/build/mach_override.c
xpcom/build/mach_override.h
xpcom/io/crc32c.c


##############################################################################
# The list below is copied from Generated.txt. Prettier doesn't currently
# support multiple ignore files or dynamic ignore configurations.
# When this is implemented, we'll update the configuration below (bug 1825508)
##############################################################################

.gradle/
build/vs/vs2022.yaml
browser/components/aboutwelcome/content/aboutwelcome.bundle.js
browser/components/aboutwelcome/logs/
browser/components/aboutwelcome/node_modules/
browser/components/asrouter/node_modules/
browser/components/asrouter/content/asrouter-admin.bundle.js
browser/components/asrouter/logs/
browser/components/asrouter/content-src/schemas/BackgroundTaskMessagingExperiment.schema.json
browser/components/asrouter/content-src/schemas/MessagingExperiment.schema.json
browser/components/asrouter/tests/InflightAssetsMessageProvider.sys.mjs
browser/components/asrouter/tests/NimbusRolloutMessageProvider.sys.mjs
browser/components/newtab/logs/
browser/components/newtab/node_modules/
browser/components/storybook/storybook-static/
browser/locales/l10n-changesets.json
browser/locales/l10n-onchange-changesets.json
devtools/client/aboutdebugging/test/jest/node_modules/
devtools/client/application/test/components/node_modules/
devtools/client/debugger/node_modules/
dom/tests/ajax/jquery/
dom/tests/ajax/mochikit/
gradle/wrapper/
intl/components/src/UnicodeScriptCodes.h
intl/unicharutil/util/nsSpecialCasingData.cpp
intl/unicharutil/util/nsUnicodePropertyData.cpp
mobile/android/**/.build-cache
mobile/android/**/.gradle
mobile/android/**/build
mobile/android/**/bin
mobile/android/**/generated
mobile/android/**\/local.properties
mobile/android/**\/manifest.json
mobile/android/android-components/components/feature/search/src/main/assets/search/search_telemetry_v2.json
mobile/android/android-components/config/detekt-baseline.xml
mobile/android/android-components/gradle/wrapper/
mobile/android/android-components/samples/glean/src/main/res/raw/initial_experiments.json
mobile/android/fenix/app/lint-baseline.xml
mobile/android/fenix/app/src/debug/res/raw/initial_experiments.json
mobile/android/fenix/app/src/main/res/raw/initial_experiments.json
mobile/android/fenix/config/detekt-baseline.xml
mobile/android/fenix/gradle/wrapper/
mobile/android/focus-android/app/lint-baseline.xml
mobile/android/focus-android/gradle/wrapper/
mobile/android/focus-android/quality/detekt-baseline.xml
mobile/locales/l10n-changesets.json
mobile/locales/l10n-onchange-changesets.json
node_modules/
python/mozperftest/mozperftest/tests/data/
security/manager/tools/KnownRootHashes.json
security/manager/tools/PreloadedHPKPins.json
services/settings/dumps/
toolkit/components/nimbus/schemas/ExperimentFeature.schema.json
toolkit/components/nimbus/schemas/ExperimentFeatureManifest.schema.json
toolkit/components/nimbus/schemas/NimbusExperiment.schema.json
toolkit/components/pdfjs/PdfJsDefaultPrefs.js
toolkit/components/pdfjs/PdfJsOverridePrefs.js
toolkit/components/uniffi-js/UniFFIGeneratedScaffolding.cpp
toolkit/components/uniffi-js/UniFFIFixtureScaffolding.cpp
toolkit/components/uniffi-bindgen-gecko-js/fixtures/generated
tools/browsertime/package.json
tools/browsertime/package-lock.json
tools/ts/error_list.json
try_task_config.json
xpcom/idl-parser/xpidl/fixtures/xpctest.d.json
