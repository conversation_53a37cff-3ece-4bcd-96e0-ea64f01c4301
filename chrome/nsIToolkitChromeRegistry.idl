/* -*- Mode: C++; tab-width; 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIChromeRegistry.idl"

interface nsIURI;
interface nsIUTF8StringEnumerator;

[scriptable, uuid(8727651c-9530-45a0-b81e-0e0690c30c50)]
interface nsIToolkitChromeRegistry : nsIXULChromeRegistry
{
  /**
   * Get a list of locales available for the specified package.
   */
  nsIUTF8StringEnumerator getLocalesForPackage(in AUTF8String aPackage);
};
