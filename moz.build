# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("AUTHORS"):
    BUG_COMPONENT = ("mozilla.org", "Licensing")

with Files("LICENSE"):
    BUG_COMPONENT = ("mozilla.org", "Licensing")

with Files("mots.yaml"):
    BUG_COMPONENT = ("Conduit", "mots")

with Files("aclocal.m4"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("Cargo.*"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("client.*"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("CLOBBER"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("*configure*"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("docs/**"):
    BUG_COMPONENT = ("Developer Infrastructure", "Source Documentation")
    SCHEDULES.exclusive = ["docs"]

with Files("mach*"):
    BUG_COMPONENT = ("Firefox Build System", "Mach Core")

with Files("pyproject.toml"):
    BUG_COMPONENT = ("Developer Infrastructure", "Lint and Formatting")

with Files("*moz*"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("GNUmakefile"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("*gradle*"):
    BUG_COMPONENT = ("GeckoView", "General")
    SCHEDULES.exclusive = ["android"]

with Files("*.json"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("**/l10n.toml"):
    BUG_COMPONENT = ("Core", "Localization")
    FINAL = True

with Files("README.txt"):
    BUG_COMPONENT = ("Core", "General")

with Files("nsprpub/**"):
    BUG_COMPONENT = ("NSPR", "NSPR")

with Files("**/Makefile.in"):
    BUG_COMPONENT = ("Firefox Build System", "General")
    FINAL = True

with Files("**/*.rst"):
    SCHEDULES.inclusive += ["docs"]

with Files("**/*.md"):
    SCHEDULES.inclusive += ["docs"]

with Files("**/*.rs"):
    SCHEDULES.inclusive += ["rusttests"]

with Files("**/reftest.list"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["reftest"]

with Files("**/reftest-qr.list"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["reftest"]

with Files("**/crashtest*.list"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["crashtest"]

with Files("**/a11y.toml"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["mochitest-a11y"]

with Files("**/mochitest.toml"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["mochitest-plain"]

with Files("**/browser.toml"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["mochitest-browser-chrome"]

with Files("**/test*/**browser_*"):
    SCHEDULES.exclusive = ["mochitest-browser-chrome"]

with Files("**/chrome.toml"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["mochitest-chrome"]

with Files("**/xpcshell.toml"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["xpcshell"]

with Files("**/reftest*/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["reftest"]

with Files("**/crashtest*/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["crashtest"]

with Files("**/mochitest*/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["mochitest"]

with Files("**/xpcshell/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]
    SCHEDULES.exclusive = ["xpcshell"]

with Files("**/tests/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]

with Files("**/test/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]

with Files("**/unit/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]

with Files("**/browser/tools/mozscreenshots/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]

with Files("**/devtools/shared/test-helpers/**"):
    SCHEDULES.inclusive += ["test-verify", "test-verify-gpu"]

CONFIGURE_SUBST_FILES += [
    "config/autoconf.mk",
    "config/emptyvars.mk",
]

if CONFIG["ENABLE_CLANG_PLUGIN"]:
    DIRS += ["build/clang-plugin"]

DIRS += [
    "build",
    "config",
    "python",
    "testing",
    "third_party/python",
]

if not CONFIG["JS_STANDALONE"]:
    DIRS += ["taskcluster"]

if not CONFIG["JS_STANDALONE"] or not CONFIG["MOZ_BUILD_APP"]:
    CONFIGURE_DEFINE_FILES += [
        "mozilla-config.h",
    ]
    EXPORTS += [
        "!buildid.h",
        "!mozilla-config.h",
        "!source-repo.h",
    ]

    GENERATED_FILES += [
        "buildid.h",
        "source-repo.h",
    ]

    GENERATED_FILES["buildid.h"].script = "build/variables.py:buildid_header"
    GENERATED_FILES["source-repo.h"].script = "build/variables.py:source_repo_header"

if CONFIG["MOZ_BUILD_APP"]:
    # Bring in the configuration for the configured application.
    include("/" + CONFIG["MOZ_BUILD_APP"] + "/app.mozbuild")
else:
    include("/toolkit/toolkit.mozbuild")

OBJDIR_PP_FILES[".cargo"] += [
    CONFIG["MOZ_OVERRIDE_CARGO_CONFIG"] or ".cargo/config.toml.in"
]

DEFINES["top_srcdir"] = TOPSRCDIR

SPHINX_TREES["contributing"] = "docs/contributing"

SPHINX_TREES["code-quality"] = "docs/code-quality"

SPHINX_TREES["testing-rust-code"] = "docs/testing-rust-code"

SPHINX_TREES["writing-rust-code"] = "docs/writing-rust-code"

SPHINX_TREES["rust-components"] = "docs/rust-components"

SPHINX_TREES["bug-mgmt"] = "docs/bug-mgmt"

SPHINX_TREES["glossary"] = "docs/glossary"

SPHINX_TREES["overview"] = "docs/overview"

SPHINX_TREES["setup"] = "docs/setup"

SPHINX_TREES["crash-reporting"] = "docs/crash-reporting"

SPHINX_TREES["performance"] = "docs/performance"

SPHINX_TREES["metrics"] = "docs/metrics"

SPHINX_TREES["gtest"] = "docs/gtest"

SPHINX_TREES["nspr"] = "docs/nspr"

SPHINX_TREES["mots"] = "docs/mots"

SPHINX_TREES["update-infrastructure"] = "docs/update-infrastructure"

SPHINX_TREES["content-security"] = "docs/content-security"

SPHINX_TREES["jsloader"] = "docs/jsloader"

include("build/templates.mozbuild")

DIRS += ["lw"]
