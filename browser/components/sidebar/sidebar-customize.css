/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/. */

.sidebar-panel {
  font: menu;

  > * + * {
    margin-top: var(--space-xlarge);
  }

  > sidebar-panel-header + * {
    margin-top: var(--space-medium);
  }
}

.customize-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  -moz-context-properties: fill;
  fill: currentColor;
  color: currentColor;

  .customize-close-button::part(button) {
    background-image: url("chrome://global/skin/icons/close-12.svg");
  }
}

.icon {
  -moz-context-properties: fill;
  fill: currentColor;
  width: var(--icon-size-default);
  height: var(--icon-size-default);
}

.extensions {
  display: flex;
  flex-direction: column;
  margin-top: var(--space-small);
  padding-inline: var(--space-medium);
  background-color: var(--sidebar-box-background);
  color: var(--sidebar-box-color);
  border: var(--sidebar-box-border);
  border-radius: var(--border-radius-medium);
}

.extension-item {
  display: flex;
  gap: var(--space-small);
  padding-block: var(--space-medium);
  box-sizing: border-box;

  &:not(:last-of-type){
    border-bottom: 0.5px solid var(--panel-separator-color);
  }
}

.customize-extensions-heading {
  margin: 0;
  font-weight: var(--font-weight-bold);
}

moz-radio-group,
moz-fieldset {
  display: block;

  &::part(inputs) {
    background-color: var(--sidebar-box-background);
    color: var(--sidebar-box-color);
    border: var(--sidebar-box-border);
    border-radius: var(--border-radius-medium);
    gap: 0;
    padding-inline: var(--space-medium);
  }
}

moz-radio,
moz-checkbox {
  padding-block: var(--space-medium);

  &:not(:last-of-type) {
    border-bottom: 0.5px solid var(--panel-separator-color);
  }

}

#manage-settings {
  display: flex;
  align-items: center;
  gap: var(--space-small);
  padding-inline-start: var(--space-medium);
}

.customize-group {
  margin-block-end: var(--space-xlarge);
}
