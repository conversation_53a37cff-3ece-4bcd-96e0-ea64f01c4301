/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/. */

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "sidebar.verticalTabs") {
  :host {
    --button-size-icon: var(--tab-min-height);
    --button-min-height: var(--button-size-icon);
    --button-border-radius: var(--border-radius-medium);
  }
}

:host {
  width: 100%;
}

.wrapper {
  display: grid;
  grid-template-rows: auto min-content min-content;
  box-sizing: border-box;
  height: 100%;
  max-width: 100%;
  padding-inline: 0;

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "sidebar.verticalTabs") {
    :host([expanded]) & {
      /* TODO: Should this be some other pixel value between 5/6? Need to be careful of Linux where font size is larger */
      --tab-pinned-horizontal-count: 5;
      /* TODO: These --space-* tokens should use semantic names shared with tabs.css */
      width: calc(var(--tab-pinned-horizontal-count) * var(--tab-pinned-min-width-expanded) + 2 * var(--tab-pinned-container-margin-inline-expanded) + 1px);
    }

    :host([customWidth]) & {
      width: 100%;
    }
  }
}

.actions-list {
  display: flex;
  flex-direction: column;
  align-items: start;
  max-width: 100%;
  overflow-x: hidden;

  > moz-button {
    --button-icon-fill: var(--toolbarbutton-icon-fill);
  }

  > moz-button:not(.tools-overflow) {
    --button-outer-padding-inline: var(--space-medium);
    --button-outer-padding-block: var(--space-xxsmall);

    &:first-of-type {
      --button-outer-padding-block-start: var(--space-small);
    }

    &:last-of-type {
      --button-outer-padding-block-end: var(--space-small);
    }
  }

  moz-button:not([type~="ghost"]):not(:hover) {
    &::part(button){
      background-color: var(--toolbarbutton-active-background);
    }
  }
}

.tools-and-extensions[orientation="horizontal"] {
  :host([expanded]) & {
    display: flex;
    flex-wrap: wrap-reverse;
    flex-direction: row;
    justify-content: flex-start;
    gap: var(--space-xxsmall);
    padding-inline: var(--space-medium);
    padding-block-end: var(--space-small);

    .tools-overflow {
      width: unset;
    }
  }
}

.bottom-actions > moz-button:last-of-type {
  --button-outer-padding-block-end: var(--space-medium);
}

@media (prefers-reduced-motion: no-preference) {
  moz-button::part(moz-button-label) {
    opacity: 0;
    transition: opacity 300ms ease-in-out;
  }
}

.expanded-button {
  width: 100%;

  &::part(button) {
    font-size: var(--font-size-large);
    font-weight: var(--font-weight);
    justify-content: flex-start;
    padding-inline: calc((var(--button-size-icon) - var(--icon-size-default)) / 2 - var(--border-width));
  }

  &::part(moz-button-label) {
    opacity: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.tools-and-extensions {
  align-self: start;
}

.bottom-actions {
  align-self: end;
}
