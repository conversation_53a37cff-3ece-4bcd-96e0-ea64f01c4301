<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<html:template id="allTabsMenu-container">
  <panelview id="allTabsMenu-allTabsView" class="PanelUI-subView">
    <vbox class="panel-subview-body">
      <toolbarbutton id="allTabsMenu-searchTabs"
                     class="subviewbutton"
                     data-l10n-id="all-tabs-menu-search-tabs"/>
      <toolbarbutton id="allTabsMenu-syncedTabs"
                     class="subviewbutton"
                     data-l10n-id="all-tabs-menu-synced-tabs"/>
      <toolbarbutton id="allTabsMenu-closeDuplicateTabs"
                     class="subviewbutton"
                     data-l10n-id="all-tabs-menu-close-all-duplicate-tabs"/>
      <toolbarbutton id="allTabsMenu-containerTabsButton"
                     class="subviewbutton subviewbutton-nav"
                     closemenu="none"
                     data-l10n-id="all-tabs-menu-new-user-context"/>
      <toolbarseparator id="allTabsMenu-hiddenTabsSeparator"/>
      <toolbarbutton id="allTabsMenu-hiddenTabsButton"
                     class="subviewbutton subviewbutton-nav"
                     closemenu="none"
                     data-l10n-id="all-tabs-menu-hidden-tabs"/>
      <toolbarseparator id="allTabsMenu-groupsSeparator"/>
      <vbox id="allTabsMenu-groupsView" class="panel-subview-body">
      </vbox>
      <toolbarseparator id="allTabsMenu-tabsSeparator"/>
      <vbox id="allTabsMenu-dropIndicatorHolder">
        <vbox id="allTabsMenu-dropIndicator" collapsed="true"/>
      </vbox>
      <vbox id="allTabsMenu-allTabsView-tabs" class="panel-subview-body"/>
    </vbox>
  </panelview>

  <panelview id="allTabsMenu-hiddenTabsView" class="PanelUI-subView">
    <vbox id="allTabsMenu-hiddenTabsView-tabs" class="panel-subview-body"/>
  </panelview>

  <panelview id="allTabsMenu-containerTabsView" class="PanelUI-subView">
    <vbox class="panel-subview-body">
      <toolbarseparator class="container-tabs-submenu-separator"/>
      <toolbarbutton class="subviewbutton"
                     data-l10n-id="user-context-manage-containers"
                     command="Browser:OpenAboutContainers"/>
    </vbox>
  </panelview>
</html:template>
