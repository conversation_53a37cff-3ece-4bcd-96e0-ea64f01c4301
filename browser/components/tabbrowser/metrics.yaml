# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Adding a new metric? We have docs for that!
# https://firefox-source-docs.mozilla.org/toolkit/components/glean/user/new_definitions_file.html

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0
$tags:
  - 'Firefox :: Tabbed Browser'

browser.engagement:
  tab_unload_count:
    type: counter
    description: >
      The count of tab unload events by TabUnloader due to a low-memory
      situation.
      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.engagement.tab_unload_count.
    bugs:
      - https://bugzil.la/1715858
    data_reviews:
      - https://bugzil.la/1715858
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_ENGAGEMENT_TAB_UNLOAD_COUNT

  tab_reload_count:
    type: counter
    description: >
      The count of tab reload events by the user after unloaded.
      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.engagement.tab_reload_count.
    bugs:
      - https://bugzil.la/1715858
    data_reviews:
      - https://bugzil.la/1715858
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_ENGAGEMENT_TAB_RELOAD_COUNT

  tab_explicit_unload:
    type: event
    description: >
      Recorded when the user explicitly unloads tab(s) from memory via
      the tab context menu.
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    bugs:
      - https://bugzil.la/1926390
    data_reviews:
      - https://bugzil.la/1926390
    data_sensitivity:
      - interaction
    extra_keys:
      unload_selected_tab:
        description: whether the currently-selected tab is unloaded
        type: boolean
      all_tabs_unloaded:
        description: whether all tabs in the window were unloaded
        type: boolean
      tabs_unloaded:
        description: The number of tabs unloaded in this operation
        type: quantity
      memory_before:
        description: Memory usage before unloading tabs (in bytes)
        type: quantity
      memory_after:
        description: Memory usage after unloading tabs (in bytes)
        type: quantity
      time_to_unload_in_ms:
        description: Time to unload tabs (in ms)
        type: quantity
    expires: never

browser.ui.interaction:
  all_tabs_panel_dragstart_tab_event_count:
    type: counter
    description: >
      Records a count of how many times a drag event started for a tab
      within the All Tabs panel.
      This metric was generated to correspond to the Legacy Telemetry
      scalar
      browser.ui.interaction.all_tabs_panel_dragstart_tab_event_count.
    bugs:
      - https://bugzil.la/1804722
    data_reviews:
      - https://bugzil.la/1804722
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_UI_INTERACTION_ALL_TABS_PANEL_DRAGSTART_TAB_EVENT_COUNT
    no_lint:
      - COMMON_PREFIX

  all_tabs_panel_entrypoint:
    type: labeled_counter
    description: >
      Records a count of how many times the All Tabs Panel was opened,
      keyed on a string describing the entrypoint.
      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.ui.interaction.all_tabs_panel_entrypoint.
    bugs:
      - https://bugzil.la/1804722
    data_reviews:
      - https://bugzil.la/1804722
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_UI_INTERACTION_ALL_TABS_PANEL_ENTRYPOINT
    no_lint:
      - COMMON_PREFIX
