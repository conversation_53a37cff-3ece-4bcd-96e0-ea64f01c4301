# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

JAR_MANIFESTS += ["jar.mn"]

BROWSER_CHROME_MANIFESTS += ["test/browser/browser.toml"]

XPCSHELL_TESTS_MANIFESTS += ["test/xpcshell/xpcshell.toml"]

EXTRA_JS_MODULES.syncedtabs += [
    "EventEmitter.sys.mjs",
    "SyncedTabsDeckComponent.sys.mjs",
    "SyncedTabsDeckStore.sys.mjs",
    "SyncedTabsDeckView.sys.mjs",
    "SyncedTabsListStore.sys.mjs",
    "TabListComponent.sys.mjs",
    "TabListView.sys.mjs",
    "util.sys.mjs",
]

with Files("**"):
    BUG_COMPONENT = ("Firefox", "Sync")
