<?xml version="1.0" encoding="UTF-8"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!DOCTYPE html>
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:xul="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
>
  <head>
    <script src="chrome://browser/content/syncedtabs/sidebar.js" />
    <script src="chrome://browser/content/utilityOverlay.js" />
    <script src="chrome://browser/content/contentTheme.js" />

    <link
      rel="stylesheet"
      href="chrome://global/skin/design-system/tokens-brand.css"
    />
    <link
      rel="stylesheet"
      href="chrome://browser/skin/syncedtabs/sidebar.css"
    />
    <link rel="localization" href="browser/syncedTabs.ftl" />
    <title data-l10n-id="synced-tabs-sidebar-title" />
  </head>

  <body role="application">
    <template id="client-template">
      <div class="item client" role="option" tabindex="-1">
        <div class="item-title-container">
          <div class="item-twisty-container"></div>
          <div class="item-icon-container"></div>
          <p class="item-title"></p>
        </div>
        <div class="item-tabs-list"></div>
      </div>
    </template>
    <template id="empty-client-template">
      <div class="item empty client" role="option" tabindex="-1">
        <div class="item-title-container">
          <div class="item-twisty-container"></div>
          <div class="item-icon-container"></div>
          <p class="item-title"></p>
        </div>
        <div class="item-tabs-list">
          <div class="item empty" role="option" tabindex="-1">
            <div class="item-title-container">
              <div class="item-icon-container"></div>
              <p
                class="item-title"
                data-l10n-id="synced-tabs-sidebar-notabs"
              ></p>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template id="tab-template">
      <div class="item tab" role="option" tabindex="-1">
        <div class="item-title-container">
          <div class="item-icon-container"></div>
          <p class="item-title"></p>
        </div>
      </div>
    </template>

    <template id="tabs-container-template">
      <div class="tabs-container">
        <div class="list" role="listbox"></div>
      </div>
    </template>

    <template id="deck-template">
      <div class="deck">
        <div class="tabs-fetching sync-state">
          <!-- Show intentionally blank panel, see bug 1239845 -->
        </div>
        <div class="notAuthedInfo sync-state">
          <div class="syncIllustration"></div>
          <p class="instructions" data-l10n-id="synced-tabs-sidebar-intro"></p>
          <button
            class="button sync-prefs"
            data-l10n-id="synced-tabs-fxa-sign-in"
          ></button>
        </div>
        <div class="syncDisabled sync-state">
          <div class="syncIllustration"></div>
          <p class="instructions" data-l10n-id="synced-tabs-sidebar-intro"></p>
          <button
            class="button sync-prefs"
            data-l10n-id="synced-tabs-turn-on-sync"
          ></button>
        </div>
        <div class="reauth sync-state">
          <div class="syncIllustrationIssue"></div>
          <p class="instructions" data-l10n-id="synced-tabs-sidebar-intro"></p>
          <button
            class="button sync-prefs"
            data-l10n-id="synced-tabs-fxa-sign-in"
          ></button>
        </div>
        <div class="unverified sync-state">
          <div class="syncIllustrationIssue"></div>
          <p
            class="instructions"
            data-l10n-id="synced-tabs-sidebar-unverified"
          ></p>
          <button
            class="button sync-prefs"
            data-l10n-id="synced-tabs-sidebar-open-settings"
          ></button>
        </div>
        <div class="singleDeviceInfo sync-state">
          <div class="syncIllustrationIssue"></div>
          <p
            class="instructions"
            data-l10n-id="synced-tabs-sidebar-noclients-subtitle"
          ></p>
          <button
            class="button connect-device"
            data-l10n-id="synced-tabs-sidebar-connect-another-device"
          ></button>
        </div>
        <div class="tabs-disabled sync-state">
          <div class="syncIllustrationIssue"></div>
          <p
            class="instructions"
            data-l10n-id="synced-tabs-sidebar-tabsnotsyncing"
          ></p>
          <button
            class="button sync-prefs"
            data-l10n-id="synced-tabs-sidebar-open-settings"
          ></button>
        </div>
      </div>
    </template>

    <div class="content-container">
      <!-- the non-scrollable header -->
      <div class="content-header">
        <div class="sidebar-search-container tabs-container sync-state">
          <xul:search-textbox
            class="tabsFilter"
            tabindex="1"
            data-l10n-id="synced-tabs-sidebar-search"
            data-l10n-attrs="placeholder"
          />
        </div>
      </div>
      <!-- the scrollable content area where our templates are inserted -->
      <div
        id="template-container"
        class="content-scrollable"
        tabindex="-1"
      ></div>
    </div>
  </body>
</html>
