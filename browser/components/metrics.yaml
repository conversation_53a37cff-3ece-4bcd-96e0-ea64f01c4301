# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Adding a new metric? We have docs for that!
# https://firefox-source-docs.mozilla.org/toolkit/components/glean/user/new_definitions_file.html

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0
$tags:
  - 'Firefox :: General'

browser.launched_to_handle:
  system_notification:
    type: event
    description: >
      Recorded when Firefox launches to complete a native notification popped by
      a system (chrome privileged) alert.  Windows-only at the time of writing.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1788960
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1788960#c10
    data_sensitivity:
      - interaction
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    extra_keys:
      name:
        description: >
          The `name` of the system (chrome privileged) alert that Firefox was
          launched to complete.
        type: string
      action:
        description: >
          The `action` of the system (chrome privileged) alert that Firefox was
          launched to complete.
        type: string
    telemetry_mirror: BrowserLaunched_to_handle_SystemNotification_Toast

background_update:
  reasons_to_not_update:
    type: string_list
    description: >
      Records which error was causing the background updater to fail.
      This list supercedes the `background-update.reason` in
      `mozapps/update/metrics.yaml`
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1795471
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1795471
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - background-update
      - metrics
    lifetime: application

  time_last_update_scheduled:
    type: datetime
    time_unit: day
    description: >
      Last time the background update was triggered.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1795471
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1795471
    data_sensitivity:
      - interaction
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - background-update
      - metrics
    lifetime: application

start_menu:
  manually_unpinned_since_last_launch:
    type: event
    description: >
      Records whether Firefox has been unpinned from the Windows start menu
      since last launch. This will only be recorded on MSIX due to the
      underlying API only being available for packaged applications.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1900035
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1900035
    data_sensitivity:
      - interaction
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - events
    lifetime: ping

sslkeylogging:
  enabled:
    type: boolean
    description: >
      Records whether TLS key logging has been enabled via the environment
      variable SSLKEYLOGFILE.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1918455
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1918455
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never

launch_on_login:
  last_profile_disable_startup:
    type: event
    description: >
      Recorded when Launch on login is disabled because the start with
      last profile setting has been disabled.
      This event was generated to correspond to the Legacy Telemetry event
      launch_on_login.last_profile_disable#startup.
    bugs:
      - https://bugzil.la/1858223
    data_reviews:
      - https://bugzil.la/1858223
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: Launch_on_login_LastProfileDisable_Startup

upgrade_dialog:
  trigger_reason:
    type: event
    description: >
      Triggering behaviors of the upgrade dialog. Value indicates which
      condition failed or all satisfied.
      This event was generated to correspond to the Legacy Telemetry event
      upgrade_dialog.trigger#reason.
    bugs:
      - https://bugzil.la/1697222
    data_reviews:
      - https://bugzil.la/1697222
    notification_emails:
      - <EMAIL>
    expires: never
    extra_keys:
      value:
        description: >
          The `value` of the event. Mirrors to the Legacy Telemetry
          event's `value` parameter.
        type: string
    telemetry_mirror: Upgrade_dialog_Trigger_Reason

browser.startup:
  abouthome_cache_result:
    type: quantity
    description: >
      How the about:home startup cache functioned on startup.

      0: Result value was never set (error case)
      1: Cache did not exist
      2: Cache page stream was corrupt / inaccessible
      3: Cache script stream was corrupt / inaccessible
      4: Cache was invalidated by a version bump
      5: Cache was valid, but read too late to be useful.
      6: Cache was valid and used.
      7: Cache is disabled.
      8: User did not load about:home on its own by default.
      9: Cache is disabled because about:newtab preloading is disabled.

      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.startup.abouthome_cache_result.
    bugs:
      - https://bugzil.la/1622263
      - https://bugzil.la/1614351
      - https://bugzil.la/1683101
      - https://bugzil.la/1714258
      - https://bugzil.la/1730042
      - https://bugzil.la/1754641
      - https://bugzil.la/1781978
      - https://bugzil.la/1811151
      - https://bugzil.la/1841926
    data_reviews:
      - https://bugzil.la/1622263
      - https://bugzil.la/1614351
      - https://bugzil.la/1683101
      - https://bugzil.la/1714258
      - https://bugzil.la/1730042
      - https://bugzil.la/1754641
      - https://bugzil.la/1781978
      - https://bugzil.la/1811151
      - https://bugzil.la/1841926
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    unit: CACHE_RESULT_SCALARS
    telemetry_mirror: BROWSER_STARTUP_ABOUTHOME_CACHE_RESULT
    no_lint:
      - COMMON_PREFIX

  abouthome_cache_shutdownwrite:
    type: boolean
    description: >
      True if the about:home startup cache was written via the
      AsyncShutdown blocker.
      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.startup.abouthome_cache_shutdownwrite.
    bugs:
      - https://bugzil.la/1622263
      - https://bugzil.la/1614351
      - https://bugzil.la/1683101
      - https://bugzil.la/1714258
      - https://bugzil.la/1730042
      - https://bugzil.la/1754641
      - https://bugzil.la/1781978
      - https://bugzil.la/1811151
      - https://bugzil.la/1841926
    data_reviews:
      - https://bugzil.la/1622263
      - https://bugzil.la/1614351
      - https://bugzil.la/1683101
      - https://bugzil.la/1714258
      - https://bugzil.la/1730042
      - https://bugzil.la/1754641
      - https://bugzil.la/1781978
      - https://bugzil.la/1811151
      - https://bugzil.la/1841926
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_STARTUP_ABOUTHOME_CACHE_SHUTDOWNWRITE
    no_lint:
      - COMMON_PREFIX

datasanitization:
  privacy_sanitize_sanitize_on_shutdown:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.sanitize.sanitizeOnShutdown pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_sanitize_sanitizeOnShutdown.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_SANITIZE_SANITIZEONSHUTDOWN

  privacy_clear_on_shutdown_cookies:
    type: boolean
    description: >
      A boolean reporting the value of the privacy.clearOnShutdown.cookies
      pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_cookies.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_COOKIES

  privacy_clear_on_shutdown_history:
    type: boolean
    description: >
      A boolean reporting the value of the privacy.clearOnShutdown.history
      pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_history.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_HISTORY

  privacy_clear_on_shutdown_formdata:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.formdata pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_formdata.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_FORMDATA

  privacy_clear_on_shutdown_downloads:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.downloads pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_downloads.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_DOWNLOADS

  privacy_clear_on_shutdown_cache:
    type: boolean
    description: >
      A boolean reporting the value of the privacy.clearOnShutdown.cache
      pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_cache.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_CACHE

  privacy_clear_on_shutdown_sessions:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.sessions pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_sessions.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_SESSIONS

  privacy_clear_on_shutdown_offline_apps:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.offlineApps pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_offlineApps.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_OFFLINEAPPS

  privacy_clear_on_shutdown_site_settings:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.siteSettings pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_siteSettings.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_SITESETTINGS

  privacy_clear_on_shutdown_open_windows:
    type: boolean
    description: >
      A boolean reporting the value of the
      privacy.clearOnShutdown.openWindows pref.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.privacy_clearOnShutdown_openWindows.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: DATASANITIZATION_PRIVACY_CLEARONSHUTDOWN_OPENWINDOWS

  session_permission_exceptions:
    type: quantity
    description: >
      A count of how many "session" cookie exceptions a user has set.
      This metric was generated to correspond to the Legacy Telemetry
      scalar datasanitization.session_permission_exceptions.
    bugs:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
      - https://bugzil.la/1744559
    data_reviews:
      - https://bugzil.la/1589753
      - https://bugzil.la/1617241
      - https://bugzil.la/1645089
      - https://bugzil.la/1678210
      - https://bugzil.la/1744559
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    unit: unknown
    telemetry_mirror: DATASANITIZATION_SESSION_PERMISSION_EXCEPTIONS

startup:
  is_cold:
    type: boolean
    description: >
      Whether or not this startup is the first startup since OS reboot
      (according to our best guess.)
      This metric was generated to correspond to the Legacy Telemetry
      scalar startup.is_cold.
    bugs:
      - https://bugzil.la/1542833
    data_reviews:
      - https://bugzil.la/1542833
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: STARTUP_IS_COLD

  seconds_since_last_os_restart:
    type: quantity
    description: >
      The time in seconds between the first browser window loading, and
      the time the OS started. This can give us an indication of whether
      starting the browser may have been the first thing the user did
      after starting their computer.
      This metric was generated to correspond to the Legacy Telemetry
      scalar startup.seconds_since_last_os_restart.
    bugs:
      - https://bugzil.la/1654063
    data_reviews:
      - https://bugzil.la/1654063
    notification_emails:
      - <EMAIL>
    expires: never
    unit: seconds
    telemetry_mirror: STARTUP_SECONDS_SINCE_LAST_OS_RESTART

os.environment:
  launch_method:
    type: string
    description: >
      Records how Firefox was started on Windows. Currently will be one of
      "Desktop", "DesktopPrivate", "StartMenu" (including pins),
      "StartMenuPrivate", "Taskbar", "TaskbarPrivate", "OtherShortcut", or
      "Other"
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.launch_method.
    bugs:
      - https://bugzil.la/1685213
      - https://bugzil.la/1725298
      - https://bugzil.la/1754656
    data_reviews:
      - https://bugzil.la/1685213
      - https://bugzil.la/1725298
      - https://bugzil.la/1754656
    notification_emails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_LAUNCH_METHOD

  launched_to_handle:
    type: labeled_counter
    description: >
      Records counts for when Firefox was launched afresh (i.e., was not
      already running) to handle a file type or protocol with `-osint -url
      ...`.  The result is split into keys which represent the file
      extension: currently, the set of file types Firefox registers to
      handle, namely ".avif", ".htm", ".html", ".pdf", ".shtml", ".xht",
      ".xhtml", ".svg", ".webp", and the set of protocol schemes that
      Firefox registers to handle, namely "about", "http", "https",
      "mailto".  If Firefox was launched to handle a file type or protocol
      it does not register to handle by default, the count is recorded as
      ".<other extension>" or "<other protocol>", respectively (neither of
      which are valid extension or protocol identifiers).
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.launched_to_handle.
    bugs:
      - https://bugzil.la/1243603
      - https://bugzil.la/1781984
    data_reviews:
      - https://bugzil.la/1243603
      - https://bugzil.la/1781984
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_LAUNCHED_TO_HANDLE

  invoked_to_handle:
    type: labeled_counter
    description: >
      Records counts for when Firefox was invoked (i.e., was already
      running and was not launched) to handle a file type or protocol with
      `-osint -url ...`.  The result is split into keys which represent
      the file extension: currently, the set of file types Firefox
      registers to handle, namely ".avif", ".htm", ".html", ".pdf",
      ".shtml", ".xht", ".xhtml", ".svg", ".webp", and the set of protocol
      schemes that Firefox registers to handle, namely "about", "http",
      "https", "mailto".  If Firefox was invoked to handle a file type or
      protocol it does not register to handle by default, the count is
      recorded as ".<other extension>" or "<other protocol>", respectively
      (neither of which are valid extension or protocol identifiers).
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.invoked_to_handle.
    bugs:
      - https://bugzil.la/1243603
      - https://bugzil.la/1781984
    data_reviews:
      - https://bugzil.la/1243603
      - https://bugzil.la/1781984
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_INVOKED_TO_HANDLE

  is_default_handler:
    type: labeled_boolean
    description: >
      Records whether Firefox was the default handler for particular file
      types or protocols.  The result is split into keys which represent
      the file extension or scheme: currently, a subset of the file types
      Firefox registers to handle, namely ".pdf" and "mailto" as protocol.
      In the future, more file types may be recorded.
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.is_default_handler.
    bugs:
      - https://bugzil.la/1743914
      - https://bugzil.la/1781984
      - https://bugzil.la/1842290
    data_reviews:
      - https://bugzil.la/1743914
      - https://bugzil.la/1781984
      - https://bugzil.la/1842290
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_IS_DEFAULT_HANDLER

  is_kept_in_dock:
    type: boolean
    description: >
      Whether this app was kept in macOS Dock on startup
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.is_kept_in_dock.
    bugs:
      - https://bugzil.la/1715348
    data_reviews:
      - https://bugzil.la/1715348
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_IS_KEPT_IN_DOCK

  is_taskbar_pinned:
    type: boolean
    description: >
      Whether the non-Private Browsing version of this app was pinned to
      taskbar on startup
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.is_taskbar_pinned.
    bugs:
      - https://bugzil.la/1685213
      - https://bugzil.la/1725298
    data_reviews:
      - https://bugzil.la/1685213
      - https://bugzil.la/1725298
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_IS_TASKBAR_PINNED

  is_taskbar_pinned_private:
    type: boolean
    description: >
      Whether the Private Browsing version of this app was pinned to
      taskbar on startup
      This metric was generated to correspond to the Legacy Telemetry
      scalar os.environment.is_taskbar_pinned_private.
    bugs:
      - https://bugzil.la/1751038
    data_reviews:
      - https://bugzil.la/1751038
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: OS_ENVIRONMENT_IS_TASKBAR_PINNED_PRIVATE

security:
  https_only_mode_enabled:
    type: quantity
    description: >
      Measures user retention of the HTTPS-Only Mode. 0 = never enabled, 1
      = enabled, 2 = disabled (but was enabled)
      This metric was generated to correspond to the Legacy Telemetry
      scalar security.https_only_mode_enabled.
    bugs:
      - https://bugzil.la/1620244
    data_reviews:
      - https://bugzil.la/1620244
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    unit: unknown
    telemetry_mirror: SECURITY_HTTPS_ONLY_MODE_ENABLED

  https_only_mode_enabled_pbm:
    type: quantity
    description: >
      Measures user retention of the HTTPS-Only Mode in Private Browsing.
      0 = https-only never enabled in PBM, 1 = https-only enabled in PBM,
      2 = https-only disabled in PBM (but was enabled)
      This metric was generated to correspond to the Legacy Telemetry
      scalar security.https_only_mode_enabled_pbm.
    bugs:
      - https://bugzil.la/1647719
    data_reviews:
      - https://bugzil.la/1647719
    notification_emails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    expires: never
    unit: unknown
    telemetry_mirror: SECURITY_HTTPS_ONLY_MODE_ENABLED_PBM

  global_privacy_control_enabled:
    type: quantity
    description: >
      Measures user retention of the Global Privacy Control. 0 = never
      enabled, 1 = enabled, 2 = disabled (but was enabled)
      This metric was generated to correspond to the Legacy Telemetry
      scalar security.global_privacy_control_enabled.
    bugs:
      - https://bugzil.la/1734185
    data_reviews:
      - https://bugzil.la/1734185
    notification_emails:
      - <EMAIL>
    expires: never
    unit: unknown
    telemetry_mirror: SECURITY_GLOBAL_PRIVACY_CONTROL_ENABLED

primary.password:
  enabled:
    type: boolean
    description: >
      If a primary-password is enabled for this profile.
      Set soon after browser startup.
      Does not update if a primary password is added or removed after startup.
    bugs:
      - https://bugzil.la/1936036
    data_reviews:
      - https://bugzil.la/1936036
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: PRIMARY_PASSWORD_ENABLED
