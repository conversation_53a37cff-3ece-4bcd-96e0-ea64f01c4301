<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this file,
   - You can obtain one at http://mozilla.org/MPL/2.0/. -->

<link
  rel="stylesheet"
  href="chrome://global/skin/design-system/text-and-typography.css"
/>
<link
  rel="stylesheet"
  href="chrome://global/skin/design-system/tokens-brand.css"
/>

<style>
  :root {
    font-size: var(--font-size-root);
  }

  a {
    color: var(--link-color) !important;
    text-decoration: underline !important;
  }

  div:has(p, .toc-wrapper) {
    width: unset !important;
    min-width: 10rem !important;
  }

  .toc-wrapper {
    margin-inline-end: 8px;
  }

  /* Override the default Storybook padding in favour of styles
   provided by our WithCommonStyles wrapper */
  .sb-show-main.sb-main-padded {
    padding: 0;
  }

  /* Ensure WithCommonStyles can grow to fit the page */
  html,
  body,
  #root,
  #root-inner,
  #storybook-root {
    height: 100%;
  }

  /* Docs stories are being given unnecessary height, possibly because we
   turned off certain controls */
  .docs-story div div {
    height: unset;
  }

  /* Typography preview and design tokens table */
  table.sb-preview-design-tokens {
    -moz-osx-font-smoothing: auto;
    border-collapse: separate;
    table-layout: fixed;
    text-align: left;
    width: 100%;

    & h1.sb-preview-font-size-xxlarge {
      font-size: var(--font-size-xxlarge);
    }

    & th {
      background: #ebf5fc;
    }

    & tr td,
    & tr th {
      padding: 8px;
    }

    & td {
      background: #ffffff;
    }
  }

  td.sb-preview-chrome-typescale {
    & .docs-story {
      * {
        font: message-box;
      }

      & h1 {
        font-weight: var(--font-weight-bold) !important;
      }
    }

    &.sb-preview-chrome-menu .docs-story * {
      font: menu;
    }
  }

  h1.sb-preview-chrome-typescale {
    font: message-box;
    font-weight: var(--font-weight-bold) !important;

    &.sb-preview-chrome-menu {
      font: menu;
    }
  }

  table .sb-preview-font-size-small {
    font-size: var(--font-size-small);
  }

  .box {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    &.width-max-content {
      width: max-content;
    }

    &.relative {
      position: relative;
    }
    &.absolute {
      position: absolute;
    }

    &.justify-center {
      justify-content: center;
    }

    &.align-center {
      align-items: center;
    }

    &.align-end {
      align-items: flex-end;
    }

    &.vertical {
      flex-direction: column;
    }

    &.left {
      left: 0;
    }

    &.right {
      right: 0;
    }

    &.top {
      top: 0;
    }

    & + h1,
    & + h2,
    & + h3 {
      margin-top: 16px !important;
    }
  }

  .post-it {
    align-items: center;
    background:
      linear-gradient(to left bottom, transparent 50%, rgba(0, 0, 0, 0.4) 0)
        no-repeat 100% 0 / 1em 1em,
      linear-gradient(-135deg, transparent 0.7em, var(--color-red-05) 0);
    display: flex;
    font-size: 14px !important;
    height: 85px;
    justify-content: center;
    margin: 12px 0;
    position: relative;
    text-align: center;
    width: 85px;

    &.green {
      background:
        linear-gradient(to left bottom, transparent 50%, rgba(0, 0, 0, 0.4) 0)
          no-repeat 100% 0 / 1em 1em,
        linear-gradient(-135deg, transparent 0.7em, var(--color-green-05) 0);
    }

    &.blue {
      background:
        linear-gradient(to left bottom, transparent 50%, rgba(0, 0, 0, 0.4) 0)
          no-repeat 100% 0 / 1em 1em,
        linear-gradient(-135deg, transparent 0.7em, var(--color-blue-05) 0);
    }

    &.orange {
      background:
        linear-gradient(to left bottom, transparent 50%, rgba(0, 0, 0, 0.4) 0)
          no-repeat 100% 0 / 1em 1em,
        linear-gradient(-135deg, transparent 0.7em, var(--color-yellow-05) 0);
    }

    &.big {
      height: 160px;
      width: 160px;
    }

    &.disabled {
      opacity: 0.4;
    }
  }

  .container-width-large {
    min-width: 968px;
  }
</style>
