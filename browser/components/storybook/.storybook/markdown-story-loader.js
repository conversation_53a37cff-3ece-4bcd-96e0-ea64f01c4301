/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
/* eslint-env node */

/**
 * This file contains a Webpack loader that takes markdown as its source and
 * outputs a docs only MDX Storybook story. This enables us to write docs only
 * pages in plain markdown by specifying a `.stories.md` extension.
 *
 * For more context on docs only stories, see:
 * https://storybook.js.org/docs/web-components/writing-docs/mdx#documentation-only-mdx
 *
 * The MDX generated by the loader will then get run through the same loaders
 * Storybook usually uses to transform MDX files.
 */

const { getStoryTitle, getMDXSource } = require("./markdown-story-utils.js");

/**
 * The WebpackLoader export. Takes markdown as its source and returns a docs
 * only MDX story. Falls back to filing stories under "Docs" for everything
 * outside of `toolkit/content/widgets`.
 *
 * @param {string} source - The markdown source to rewrite to MDX.
 */
module.exports = function markdownStoryLoader(source) {
  // `this.resourcePath` is the path of the file being processed.
  let title = getStoryTitle(this.resourcePath);
  let mdxSource = getMDXSource(source, title, this.resourcePath);
  return mdxSource;
};
