/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

.addon-panel-body {
  box-sizing: border-box;
}

.addon-panel-message {
  background: #FFF5CF;
  color: #333333;
  padding: 10px 15px;
  line-height: 20px;
  box-shadow: rgba(0,0,0,.1) 0 -1px 0 0 inset;
  font-size: 13px;
}

table.addon-panel-table {
  border-spacing: 0;
  text-align: left;
  width: 100%;
  margin: 0;
}

table.addon-panel-table thead.addon-panel-table-head th {
  border: none;
  color: rgba(46, 52, 56, 0.75);
}

@media (prefers-color-scheme: dark) {
  table.addon-panel-table thead.addon-panel-table-head th {
    color: rgba(201, 205, 207, 0.55)
  }
}

table.addon-panel-table thead.addon-panel-table-head tr {
  border-top: none;
}

.addon-panel-table-head th:first-of-type,
.addon-panel-table-body td:first-of-type {
  width: 25%;
  padding-left: 20px;
  border-inline: none;
}

.addon-panel-table-head th:last-of-type,
.addon-panel-table-body td:last-of-type {
  padding-right: 20px;
  border-inline-start: none;
}

.addon-panel-body {
  overflow: hidden;
}

.addon-panel-table-body td {
  font-weight: bold;
  vertical-align: top;
}

table.addon-panel-table .addon-panel-table-body tr:nth-of-type(2n) {
  background-color: unset;
}

.addon-panel-table-body tr:last-of-type td {
  border-bottom: none;
}

.addon-panel-table-body textarea {
  height: fit-content;
  width: 100%;
}
