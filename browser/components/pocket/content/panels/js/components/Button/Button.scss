.stp_button {
  cursor: pointer;
  display: inline-block;
  margin: 12px 0;

  &:hover {
    text-decoration: none;
  }

  &.stp_button_text {
    color: #0060DF;
    font-size: 0.95rem;
    line-height: 1.2rem;
    font-style: normal;
    font-weight: 600;

    &:focus {
      text-decoration: underline;
    }

    &:hover {
      color: #0250BB;
      text-decoration: none;
    }

    &:active {
      color: #054096;
    }

    @include theme_dark {
      color: #00DDFF;
    }
  }

  &.stp_button_primary {
    align-items: center;
    background: #0060DF;
    border-radius: 4px;
    color: #FBFBFE;
    font-size: 0.85rem;
    line-height: 1rem;
    font-style: normal;
    font-weight: 600;
    justify-content: center;
    padding: 6px 12px;

    &:focus {
      text-decoration: none;
      background: #0060DF;
      outline: 2px solid #0060df;
      outline-offset: 2px;
    }

    &:hover {
      background: #0250BB;
    }

    &:active {
      background: #054096;
    }

    @include theme_dark {
      background: #00DDFF;
      color: #15141A;

      &:hover {
        background: #80ebfe;
      }

      &:focus {
        outline: 2px solid #00DDFF;
      }
    }
  }

  &.stp_button_secondary {
    align-items: center;
    background: #F0F0F4;
    border-radius: 4px;
    color: #15141A;
    font-size: 0.85rem;
    line-height: 1rem;
    font-style: normal;
    font-weight: 600;
    padding: 6px 12px;

    &:focus {
      text-decoration: none;
      background: #F0F0F4;
      outline: 2px solid #0060df;
      outline-offset: 2px;
    }

    &:hover {
      background: #E0E0E6;
    }

    &:active {
      background: #CFCFD8;
    }

    @include theme_dark {
      background: #2B2A33;
      color: #FBFBFE;

      &:focus {
        outline: 2px solid #00DDFF;
      }

      &:hover {
        background: #53535d;
      }
    }
  }
}

.stp_button_wide {
  .stp_button {
    display: block;
    margin: 12px 0;
    text-align: center;
    padding: 8px 12px;

    &.stp_button_primary {
      font-size: 1.1rem;
      line-height: 1.35rem;
    }

    &.stp_button_secondary {
      font-size: 0.85rem;
      line-height: 1rem;
    }
  }
}

.stp_button_wide {
  .stp_button {
    display: block;
    margin: 12px 0;
    text-align: center;
  }
}
