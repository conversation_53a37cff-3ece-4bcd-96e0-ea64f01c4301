.stp_tag_picker {
  .stp_tag_picker_tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 8px;
    border: 1px solid #8F8F9D;
    border-radius: 4px;
    font-style: normal;
    font-weight: normal;
    font-size: 1rem;
    line-height: 1.2rem;
    color: #15141A;
    margin-bottom: 10px;
  }

  .stp_tag_picker_tag {
    background: #F0F0F4;
    border-radius: 4px;
    color: #15141A;
    display: inline-block;
    font-size: 0.85rem;
    line-height: 1rem;
    font-style: normal;
    font-weight: 600;
    padding: 0 8px;
    transition: background-color 200ms ease-in-out;

    @include theme_dark {
      background: #2B2A33;
      color: #FBFBFB;
    }
  }

  .recent_tags .stp_tag_picker_tag {
    margin-inline-end: 5px;
  }

  .stp_tag_picker_tag_remove {
    padding-top: 5px;
    padding-bottom: 5px;
    padding-inline-end: 5px;
    color: #5B5B66;
    font-weight: 400;

    &:hover {
      color: #3E3E44;
    }
    
    &:focus {
      color: #3E3E44;
      outline: 2px solid #0060df;
      outline-offset: -4px;
    }

    @include theme_dark {
      color: #8F8F9D;

      &:hover {
        color: #fff;
      }

      &:focus {
        outline: 2px solid #00DDFF;
      }
    }
  }

  .stp_tag_picker_tag_duplicate {
    background-color: #bbb;

    @include theme_dark {
      background-color: #666;
    }
  }

  .stp_tag_picker_input_wrapper {
    display: flex;
    flex-grow: 1;
  }

  .stp_tag_picker_input {
    flex-grow: 1;
    border: 1px solid #8F8F9D;
    padding: 0 6px;
    border-start-start-radius: 4px;
    border-end-start-radius: 4px;

    &:focus {
      border: 1px solid #0060DF;
      outline: 1px solid #0060DF;
    }

    @include theme_dark {
      background: none;
      color: #FBFBFB;

      &:focus {
        border: 1px solid #00DDFF;
        outline: 1px solid #00DDFF;
      }
    }
  }

  .stp_tag_picker_button {
    font-size: 0.95rem;
    line-height: 1.1rem;
    padding: 4px 6px;
    background-color: #F0F0F4;
    border: 1px solid #8F8F9D;
    border-inline-start: none;
    border-start-end-radius: 4px;
    border-end-end-radius: 4px;
    &:disabled {
      color: #8F8F9D;
    }
    &:hover:enabled {
      background-color: #DADADF;
    }
    &:focus:enabled {
      border: 1px solid #0060DF;
      outline: 1px solid #0060DF;
    }

    @include theme_dark {
      background-color: #2B2A33;
      color: #FBFBFB;
      &:disabled {
        color: #666;
      }
      &:hover:enabled {
        background-color: #53535d;
      }
      &:focus:enabled {
        border: 1px solid #00DDFF;
        outline: 1px solid #00DDFF;
      }
    }
  }
}
