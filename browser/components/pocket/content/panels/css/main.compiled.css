/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-family: sans-serif; /* 1 */
}

/**
 * Remove default margin.
 */
body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none;
}

/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0;
}

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic;
}

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9/10.
 */
img {
  border: 0;
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1em 40px;
}

/**
 * Address differences between Firefox and other browsers.
 */
hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}

/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto;
}

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit; /* 1 */
  font: inherit; /* 2 */
  margin: 0; /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type=button],
input[type=reset],
input[type=submit] {
  cursor: pointer; /* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal;
}

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */
input[type=checkbox],
input[type=radio] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */
input[type=search] {
  box-sizing: content-box;
}

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto;
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/* Normalization for FF panel defauts
   ========================================================================== */
html {
  outline: none;
  padding: 0;
}

a {
  color: #0095dd;
  margin: 0;
  outline: none;
  padding: 0;
  text-decoration: none;
}

a:hover,
a:active,
a:focus {
  color: #008acb;
  text-decoration: underline;
}

a:active {
  color: #006b9d;
}

html {
  font: menu;
}

body.theme_dark {
  background: #42414c;
  color: #FBFBFE;
}

hr {
  margin: 12px -8px;
  background-color: #F0F0F4;
  height: 1px;
  border: none;
}
body.theme_dark hr {
  background-color: #52525E;
}

.header_large {
  margin: 12px 0 8px;
  font-size: 1.25rem;
  line-height: 1.65rem;
}
.header_large .stp_button {
  margin: 0;
}

.header_medium {
  margin: 12px 0 8px;
  font-size: 1.1rem;
  line-height: 1.35rem;
}

.header_small {
  margin: 12px 0 8px;
  font-size: 0.95rem;
  line-height: 1.16rem;
}

.header_flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header_center {
  text-align: center;
}

p {
  margin: 8px 0;
  font-size: 0.95rem;
  line-height: 1.16rem;
}

.stp_panel_container {
  overflow: hidden;
}

.stp_panel {
  padding: 0 16px;
  margin: 16px 0 12px;
}

/*  saved.css
 *
 *  Description:
 *  With base elements out of the way, this sets all custom styling for the page saved dialog.
 *
 *  Contents:
 *  Global
 *  Loading spinner
 *  Core detail
 *  Tag entry
 *  Recent/suggested tags
 *  Premium upsell
 *  Token input/autocomplete
 *  Overflow mode
 *  Language overrides
 */
/*=Global
--------------------------------------------------------------------------------------- */
.pkt_ext_containersaved {
  background-color: #fbfbfb;
  border-radius: 4px;
  display: block;
  padding: 0;
  position: relative;
  text-align: center;
  overflow: hidden;
}

.pkt_ext_cf:after {
  content: " ";
  display: table;
  clear: both;
}

.pkt_ext_containersaved .pkt_ext_tag_detail,
.pkt_ext_containersaved .pkt_ext_recenttag_detail,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail {
  margin: 0 auto;
  padding: 0.25em 1em;
  position: relative;
  width: auto;
}

/*=Loading spinner
--------------------------------------------------------------------------------------- */
@keyframes pkt_ext_spin {
  to {
    transform: rotate(1turn);
  }
}
.pkt_ext_containersaved .pkt_ext_loadingspinner {
  display: inline-block;
  height: 2.5em;
  inset-inline-start: 50%;
  margin-block: 2em 0;
  margin-inline: -1.25em 0;
  font-size: 10px;
  text-indent: 999em;
  position: absolute;
  top: 4em;
  overflow: hidden;
  width: 2.5em;
  animation: pkt_ext_spin 0.7s infinite steps(8);
}

.pkt_ext_containersaved .pkt_ext_loadingspinner:before,
.pkt_ext_containersaved .pkt_ext_loadingspinner:after,
.pkt_ext_containersaved .pkt_ext_loadingspinner > div:before,
.pkt_ext_containersaved .pkt_ext_loadingspinner > div:after {
  content: "";
  position: absolute;
  top: 0;
  inset-inline-start: 1.125em;
  width: 0.25em;
  height: 0.75em;
  border-radius: 0.2em;
  background: #eee;
  box-shadow: 0 1.75em #eee;
  transform-origin: 50% 1.25em;
}

.pkt_ext_containersaved .pkt_ext_loadingspinner:before {
  background: #555;
}

.pkt_ext_containersaved .pkt_ext_loadingspinner:after {
  transform: rotate(-45deg);
  background: #777;
}

.pkt_ext_containersaved .pkt_ext_loadingspinner > div:before {
  transform: rotate(-90deg);
  background: #999;
}

.pkt_ext_containersaved .pkt_ext_loadingspinner > div:after {
  transform: rotate(-135deg);
  background: #bbb;
}

/*=Core detail
--------------------------------------------------------------------------------------- */
.pkt_ext_containersaved .pkt_ext_initload {
  inset-inline-start: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.pkt_ext_containersaved .pkt_ext_detail {
  opacity: 0;
  position: relative;
  padding-bottom: 1.25em;
}

.pkt_ext_container_detailactive .pkt_ext_initload {
  opacity: 0;
}

.pkt_ext_container_detailactive .pkt_ext_initload .pkt_ext_loadingspinner,
.pkt_ext_container_finalstate .pkt_ext_initload .pkt_ext_loadingspinner {
  animation: none;
}

.pkt_ext_container_detailactive .pkt_ext_detail {
  max-height: 20em;
  opacity: 1;
}

.pkt_ext_container_finalstate .pkt_ext_edit_msg,
.pkt_ext_container_finalstate .pkt_ext_tag_detail,
.pkt_ext_container_finalstate .pkt_ext_suggestedtag_detail,
.pkt_ext_container_finalstate .pkt_ext_item_actions {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.pkt_ext_container_finalerrorstate .pkt_ext_edit_msg,
.pkt_ext_container_finalerrorstate .pkt_ext_tag_detail,
.pkt_ext_container_finalerrorstate .pkt_ext_suggestedtag_detail,
.pkt_ext_container_finalerrorstate .pkt_ext_item_actions {
  display: none;
  transition: none;
}

.pkt_ext_containersaved h2 {
  background: transparent;
  border: none;
  color: #333;
  display: block;
  float: none;
  font-size: 1.2em;
  font-weight: normal;
  letter-spacing: normal;
  line-height: 1;
  margin: 19px 0 4px;
  padding: 0;
  position: relative;
  text-align: start;
  text-transform: none;
}

@keyframes fade_in_out {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.pkt_ext_container_finalstate h2 {
  animation: fade_in_out 0.4s ease-out;
}

.pkt_ext_container_finalerrorstate h2 {
  animation: none;
  color: #d74345;
}

.pkt_ext_containersaved .pkt_ext_errordetail {
  display: none;
  font-size: 0.9em;
  font-weight: normal;
  inset-inline-start: 6.4em;
  max-width: 21em;
  opacity: 0;
  position: absolute;
  top: 2.7em;
  text-align: start;
  visibility: hidden;
}

.pkt_ext_container_finalerrorstate {
  max-height: 133px;
}

.pkt_ext_container_finalerrorstate .pkt_ext_errordetail {
  display: block;
  opacity: 1;
  visibility: visible;
}

.pkt_ext_containersaved .pkt_ext_logo {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x), center center no-repeat;
  display: block;
  float: inline-start;
  height: 40px;
  padding: 1.25em 1em;
  position: relative;
  width: 44px;
}

.pkt_ext_container_finalerrorstate .pkt_ext_logo {
  background-image: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x);
  height: 44px;
  width: 44px;
}

.pkt_ext_containersaved .pkt_ext_topdetail {
  float: inline-start;
}

.pkt_ext_containersaved .pkt_ext_edit_msg_container {
  position: relative;
}
.pkt_ext_containersaved .pkt_ext_edit_msg_container .pkt_ext_edit_msg {
  box-sizing: border-box;
  display: none;
  font-size: 0.75em;
  inset-inline-start: auto;
  padding: 0 1.4em;
  position: absolute;
  text-align: start;
  top: 0;
  width: 100%;
  margin: 0;
}
.pkt_ext_containersaved .pkt_ext_edit_msg_container .pkt_ext_edit_msg.pkt_ext_edit_msg_error {
  color: #d74345;
}
.pkt_ext_containersaved .pkt_ext_edit_msg_container .pkt_ext_edit_msg.pkt_ext_edit_msg_active {
  display: block;
}

.pkt_ext_containersaved .pkt_ext_item_actions {
  background: transparent;
  float: none;
  height: auto;
  margin-bottom: 1em;
  margin-top: 0;
  width: auto;
}

.pkt_ext_containersaved .pkt_ext_item_actions_disabled {
  opacity: 0.5;
}

.pkt_ext_container_finalstate .pkt_ext_item_actions_disabled {
  opacity: 0;
}

.pkt_ext_containersaved .pkt_ext_item_actions ul {
  background: none;
  display: block;
  float: none;
  height: auto;
  margin: 0;
  padding: 0;
  width: 100%;
}

.pkt_ext_containersaved .pkt_ext_item_actions li {
  box-sizing: border-box;
  background: none;
  border: 0;
  float: inline-start;
  list-style: none;
  line-height: 0.8;
  height: auto;
  padding-inline-end: 0.4em;
  width: auto;
}

.pkt_ext_containersaved .pkt_ext_item_actions li:before {
  content: none;
}

.pkt_ext_containersaved .pkt_ext_item_actions .pkt_ext_actions_separator {
  border-inline-start: 2px solid #777;
  height: 1em;
  margin-top: 0.3em;
  padding: 0;
  width: 10px;
}

.pkt_ext_containersaved .pkt_ext_item_actions a {
  background: transparent;
  color: #0095dd;
  display: block;
  font-feature-settings: normal;
  font-size: 0.9em;
  font-weight: normal;
  letter-spacing: normal;
  line-height: inherit;
  height: auto;
  margin: 0;
  padding: 0.5em;
  float: inline-start;
  text-align: start;
  text-decoration: none;
  text-transform: none;
}

.pkt_ext_containersaved .pkt_ext_item_actions a:hover,
.pkt_ext_containersaved .pkt_ext_item_actions a:focus {
  color: #008acb;
  text-decoration: underline;
}

.pkt_ext_containersaved .pkt_ext_item_actions a:before,
.pkt_ext_containersaved .pkt_ext_item_actions a:after {
  background: transparent;
  display: none;
}

.pkt_ext_containersaved .pkt_ext_item_actions_disabled a {
  cursor: default;
}

.pkt_ext_containersaved .pkt_ext_item_actions .pkt_ext_openpocket {
  float: inline-end;
  padding-inline-end: 0.7em;
  text-align: end;
}

.pkt_ext_containersaved .pkt_ext_item_actions .pkt_ext_removeitem {
  padding-inline-start: 0;
}

.pkt_ext_containersaved .pkt_ext_close {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
  color: #333;
  display: block;
  font-size: 0.8em;
  height: 10px;
  inset-inline-end: 0.5em;
  overflow: hidden;
  position: absolute;
  text-align: center;
  text-indent: -9999px;
  top: -1em;
  width: 10px;
}

.pkt_ext_containersaved .pkt_ext_close:hover {
  color: #000;
  text-decoration: none;
}

/*=Tag entry
--------------------------------------------------------------------------------------- */
.pkt_ext_containersaved .pkt_ext_tag_detail {
  border: 1px solid #c1c1c1;
  border-radius: 2px;
  clear: both;
  margin: 0 1em;
  padding: 0;
  display: flex;
}

.pkt_ext_containersaved .pkt_ext_tag_error {
  border: none;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper {
  box-sizing: border-box;
  flex: 1;
  background-color: #fff;
  border-inline-end: 1px solid #c3c3c3;
  color: #333;
  display: block;
  float: none;
  font-size: 0.875em;
  list-style: none;
  margin: 0;
  overflow: hidden;
  padding: 0.25em 0.5em;
  width: 14em;
  padding-inline: 0.5em;
}

.pkt_ext_containersaved .pkt_ext_tag_error .pkt_ext_tag_input_wrapper {
  border: 1px solid #d74345;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper .token-input-list {
  display: block;
  height: 1.7em;
  overflow: hidden;
  position: relative;
  width: 60em;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper .token-input-list,
.pkt_ext_containersaved .pkt_ext_tag_input_wrapper li {
  font-size: 1em;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper li {
  height: auto;
  width: auto;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper li:before {
  content: none;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper input {
  border: 0;
  box-shadow: none;
  background-color: #fff;
  color: #333;
  font-size: 1em;
  float: inline-start;
  line-height: normal;
  height: auto;
  min-height: 0;
  min-width: 5em;
  padding: 3px 2px 1px;
  text-transform: none;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper input::placeholder {
  color: #a9a9a9;
  letter-spacing: normal;
  text-transform: none;
}

.pkt_ext_containersaved .input_disabled {
  cursor: default;
  opacity: 0.5;
}

.pkt_ext_containersaved .pkt_ext_btn {
  box-sizing: border-box;
  color: #333;
  float: none;
  font-size: 1em;
  letter-spacing: normal;
  height: 2.2em;
  min-width: 4em;
  padding: 0.5em 0;
  text-decoration: none;
  text-transform: none;
  width: auto;
}

.pkt_ext_containersaved .pkt_ext_btn:hover {
  background-color: #ebebeb;
}

.pkt_ext_containersaved .pkt_ext_btn:active {
  background-color: #dadada;
}

.pkt_ext_containersaved .pkt_ext_btn_disabled,
.pkt_ext_containersaved .pkt_ext_btn_disabled:hover,
.pkt_ext_containersaved .pkt_ext_btn_disabled:active {
  background-color: transparent;
  cursor: default;
  opacity: 0.4;
}

.pkt_ext_containersaved .pkt_ext_tag_error .pkt_ext_btn {
  border: 1px solid #c3c3c3;
  border-block-width: 1px;
  border-inline-width: 0 1px;
  height: 2.35em;
}

.pkt_ext_containersaved .autocomplete-suggestions {
  margin-top: 2.2em;
}

/*=Recent/suggested tags
--------------------------------------------------------------------------------------- */
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail {
  box-sizing: border-box;
  clear: both;
  inset-inline-start: 0;
  opacity: 0;
  min-height: 110px;
  visibility: hidden;
  width: 100%;
}

.pkt_ext_container_detailactive .pkt_ext_suggestedtag_detail {
  opacity: 1;
  visibility: visible;
}

.pkt_ext_container_finalstate .pkt_ext_suggestedtag_detail {
  opacity: 0;
  visibility: hidden;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail h4,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail h4 {
  color: #333;
  font-size: 1em;
  font-weight: normal;
  font-style: normal;
  letter-spacing: normal;
  margin: 0.5em 0;
  text-align: start;
  text-transform: none;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail .pkt_ext_loadingspinner,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail .pkt_ext_loadingspinner {
  display: none;
  position: absolute;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail_loading .pkt_ext_loadingspinner,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail_loading .pkt_ext_loadingspinner {
  display: block;
  font-size: 6px;
  inset-inline-start: 48%;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail ul,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail ul {
  display: block;
  margin: 0;
  height: 2em;
  overflow: hidden;
  padding: 2px 0 0;
}

.pkt_ext_containersaved .pkt_ext_suggestedtag_detail ul {
  height: auto;
  margin: 0;
  max-height: 4em;
  padding-top: 6px;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail li,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail li {
  background: none;
  float: inline-start;
  height: inherit;
  line-height: 1.5;
  list-style: none;
  margin-bottom: 0.5em;
  width: inherit;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail li:before,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail li:before {
  content: none;
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail .recenttag_msg,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail .suggestedtag_msg {
  color: #333;
  font-size: 0.8125em;
  line-height: 1.2;
  inset-inline-start: auto;
  position: absolute;
  text-align: start;
  top: 2em;
}

.pkt_ext_containersaved .pkt_ext_suggestedtag_detail .suggestedtag_msg {
  margin-inline-end: 1.3em;
}

.pkt_ext_containersaved .token_tag {
  border-radius: 4px;
  background: #f7f7f7;
  border: 1px solid #c3c3c3;
  color: #333;
  font-size: 1em;
  font-weight: normal;
  letter-spacing: normal;
  margin-inline-end: 0.5em;
  padding: 0.125em 0.625em;
  text-decoration: none;
  text-transform: none;
}

.pkt_ext_containersaved .token_tag:hover {
  background-color: #008acb;
  border-color: #008acb;
  color: #fff;
  text-decoration: none;
}

.pkt_ext_containersaved .token_tag:before,
.pkt_ext_containersaved .token_tag:after {
  content: none;
}

.pkt_ext_containersaved .token_tag:hover span {
  background-image: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x);
}

.pkt_ext_containersaved .pkt_ext_recenttag_detail_disabled .token_tag,
.pkt_ext_containersaved .pkt_ext_recenttag_detail_disabled .token_tag:hover,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail_disabled .token_tag,
.pkt_ext_containersaved .pkt_ext_suggestedtag_detail_disabled .token_tag:hover {
  background-color: #f7f7f7;
  cursor: default;
  opacity: 0.5;
}

.pkt_ext_containersaved .token_tag_inactive {
  display: none;
}

/*=Premium upsell
--------------------------------------------------------------------------------------- */
.pkt_ext_detail .pkt_ext_premupsell {
  background-color: #50bbb6;
  display: block;
  padding: 1.5em 0;
  text-align: center;
}

.pkt_ext_premupsell h4 {
  color: #fff;
  font-size: 1em;
  margin-bottom: 1em;
}

.pkt_ext_premupsell a {
  color: #28605d;
  border-bottom: 1px solid #47a7a3;
  font-weight: normal;
}

.pkt_ext_premupsell a:hover {
  color: #14302f;
}

/*=Token input/autocomplete
--------------------------------------------------------------------------------------- */
.token-input-dropdown-tag {
  border-radius: 4px;
  box-sizing: border-box;
  background: #fff;
  border: 1px solid #cdcdcd;
  margin-top: 0.5em;
  inset-inline-start: 0 !important;
  overflow-y: auto;
  top: 1.9em !important;
  z-index: 9000;
}

.token-input-dropdown-tag ul {
  height: inherit;
  max-height: 115px;
  margin: 0;
  overflow: auto;
  padding: 0.5em 0;
}

.token-input-dropdown-tag ul li {
  background: none;
  color: #333;
  font-weight: normal;
  font-size: 1em;
  float: none;
  height: inherit;
  letter-spacing: normal;
  list-style: none;
  padding: 0.75em;
  text-align: start;
  text-transform: none;
  width: inherit;
}

.token-input-dropdown-tag ul li:before {
  content: none;
}

.token-input-dropdown ul li.token-input-selected-dropdown-item {
  background-color: #008acb;
  color: #fff;
}

.token-input-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.token-input-list li {
  text-align: start;
  list-style: none;
}

.token-input-list li input {
  border: 0;
  background-color: white;
}

.pkt_ext_containersaved .token-input-token {
  background: none;
  border-radius: 4px;
  border: 1px solid #c3c3c3;
  overflow: hidden;
  margin: 0 0.2em;
  padding: 0 8px;
  background-color: #f7f7f7;
  color: #000;
  font-weight: normal;
  cursor: default;
  line-height: 1.5;
  display: block;
  width: auto;
  float: inline-start;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled {
  position: relative;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled input {
  opacity: 0.5;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled .token-input-list {
  opacity: 0.5;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled .pkt_ext_tag_input_blocker {
  height: 100%;
  inset-inline-start: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 5;
}

.pkt_ext_containersaved .token-input-token p {
  display: inline-block;
  font-size: 1em;
  font-weight: normal;
  line-height: inherit;
  letter-spacing: normal;
  padding: 0;
  margin: 0;
  text-transform: none;
  vertical-align: top;
  width: auto;
  unicode-bidi: plaintext;
}

.pkt_ext_containersaved .token-input-token p:before {
  content: none;
  width: 0;
}

.pkt_ext_containersaved .token-input-token span {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
  cursor: pointer;
  display: inline-block;
  height: 8px;
  margin-block: 0;
  margin-inline: 8px 0;
  overflow: hidden;
  width: 8px;
  text-indent: -99px;
}

.pkt_ext_containersaved .token-input-selected-token {
  background-color: #008acb;
  border-color: #008acb;
  color: #fff;
}

.pkt_ext_containersaved .token-input-selected-token span {
  background-image: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x);
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled .token-input-selected-token {
  background-color: #f7f7f7;
}

.pkt_ext_containersaved .pkt_ext_tag_input_wrapper_disabled .token-input-selected-token span {
  color: #bbb;
}

/*=Language overrides
--------------------------------------------------------------------------------------- */
.pkt_ext_saved_es .pkt_ext_btn {
  min-width: 5em;
}

.pkt_ext_saved_de .pkt_ext_btn,
.pkt_ext_saved_ru .pkt_ext_btn {
  min-width: 6em;
}

/*=Coral Button
--------------------------------------------------------------------------------------- */
button {
  padding: 0;
  margin: 0;
  background: none;
  border: 0;
  outline: none;
  color: inherit;
  font: inherit;
  overflow: visible;
}

.pkt_ext_button {
  padding: 3px;
  background-color: #EF4056;
  color: #FFF;
  text-align: center;
  cursor: pointer;
  height: 32px;
  box-sizing: border-box;
  width: 320px;
  margin: 0 auto;
  border-radius: 2px;
  font-size: 1em;
}

.pkt_ext_button:hover,
.pkt_ext_button:active {
  background-color: #d5374b;
}

/* alt button */
.pkt_ext_blue_button {
  background-color: #0060df;
  color: #FFF;
}

.pkt_ext_blue_button:hover {
  background-color: #003eaa;
}

.pkt_ext_blue_button:active {
  background-color: #002275;
}

.pkt_ext_ffx_icon:after {
  position: absolute;
  height: 22px;
  width: 22px;
  top: -3px;
  inset-inline-start: -28px;
  content: "";
  background-image: url(../img/<EMAIL>);
  background-size: 22px 22px;
  background-repeat: no-repeat;
}

.pkt_ext_subshell {
  display: none;
  border-top: 1px solid #c1c1c1;
  background: #ebebeb;
  width: 100%;
}

.pkt_ext_subshell hr {
  display: none;
}

.recs_enabled .pkt_ext_subshell hr {
  display: block;
  border: 0;
  border-top: 1px solid #D7D7DB;
  margin: 0;
}

.pkt_ext_item_recs {
  text-align: start;
  margin: 0 auto;
  padding: 0.25em 1em;
}

.pkt_ext_item_recs header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.pkt_ext_item_recs header h4 {
  color: #333;
  font-size: 1em;
  font-weight: normal;
  font-style: normal;
  letter-spacing: normal;
  margin: 0.5em 0;
  text-align: start;
  text-transform: none;
}

.pkt_ext_item_recs header a {
  font-style: normal;
  font-weight: 500;
  font-size: 1em;
  line-height: 20px;
  color: #0095DD;
}

.pkt_ext_containersaved .pkt_ext_item_recs ol {
  padding: 0;
  margin: 0 0 10px;
  list-style: none;
}

.pkt_ext_containersaved .pkt_ext_item_recs li {
  float: none;
  display: flex;
  font-style: normal;
  font-weight: normal;
  line-height: 18px;
  margin: 0 -1em;
  min-height: 60px;
}

.pkt_ext_containersaved .pkt_ext_item_recs li a {
  padding-block: 8px;
  padding-inline: 1em 40px;
  background: url(../img/open.svg) top 8px right 14px no-repeat;
  flex-grow: 1;
}

.pkt_ext_containersaved .pkt_ext_item_recs li a:dir(rtl) {
  background-position-x: left 14px;
}

.pkt_ext_containersaved .pkt_ext_item_recs li:hover,
.pkt_ext_containersaved .pkt_ext_item_recs li a:focus {
  background-color: rgba(12, 12, 13, 0.1);
}

.pkt_ext_containersaved .pkt_ext_item_recs li:active {
  background-color: rgba(12, 12, 13, 0.2);
}

.pkt_ext_containersaved .pkt_ext_item_recs .pkt_ext_item_recs_link:hover {
  text-decoration: none;
}

.pkt_ext_containersaved .pkt_ext_item_recs .rec-thumb {
  width: 40px;
  height: 40px;
  float: inline-start;
  margin: 0;
  margin-inline-end: 12px;
  border-radius: 2px;
}

.pkt_ext_containersaved .pkt_ext_item_recs .rec-thumb:-moz-broken {
  display: none;
}

.pkt_ext_containersaved .pkt_ext_item_recs p {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  word-break: break-word;
  font-style: normal;
  font-weight: normal;
  margin: 0;
}

.pkt_ext_containersaved .pkt_ext_item_recs .rec-title {
  -webkit-line-clamp: 2;
  font-size: 1em;
  line-height: 18px;
  color: #0C0C0D;
}

.pkt_ext_containersaved .pkt_ext_item_recs .rec-source {
  -webkit-line-clamp: 1;
  font-size: 0.9em;
  line-height: 16px;
  color: #737373;
}

/*  signup.css
 *
 *  Description:
 *  With base elements out of the way, this sets all custom styling for the extension.
 *
 *  Contents:
 *  Global
 *  Core detail
 *  Core detail - storyboard
 *  Buttons
 *  Overflow mode
 *  Language overrides
 */
/*=Global
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup {
  background-color: #ebebeb;
  color: #333;
  display: block;
  margin: 0;
  padding: 0;
  position: relative;
  text-align: center;
  overflow: hidden;
}

.pkt_ext_containersignup_inactive {
  animation: pkt_ext_hide 0.3s ease-out;
  opacity: 0;
  visibility: hidden;
}

.pkt_ext_cf:after {
  content: " ";
  display: table;
  clear: both;
}

@keyframes pkt_ext_hide {
  0% {
    opacity: 1;
    visibility: visible;
  }
  99% {
    opacity: 0;
    visibility: visible;
  }
  100% {
    opacity: 0;
    visibility: hidden;
  }
}
/*=Core detail
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup p {
  font-size: 1em;
  color: #333;
  line-height: 1.3;
  margin: 0 auto 1.5em;
  max-width: 260px;
}

.pkt_ext_containersignup a {
  color: #4c8fd0;
}

.pkt_ext_containersignup a:hover {
  color: #3076b9;
}

.pkt_ext_containersignup .pkt_ext_introdetail {
  background-color: #fbfbfb;
  border: 1px solid #c1c1c1;
  border-width: 0 0 1px;
}

.pkt_ext_containersignup .pkt_ext_logo {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center bottom no-repeat;
  display: block;
  height: 32px;
  margin: 0 auto 15px;
  padding-top: 25px;
  position: relative;
  text-indent: -9999px;
  width: 123px;
}

.pkt_ext_containersignup .pkt_ext_introimg {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
  display: block;
  height: 125px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999px;
  width: 255px;
}

.pkt_ext_containersignup .pkt_ext_tagline {
  margin-bottom: 0.5em;
}

.pkt_ext_containersignup .pkt_ext_learnmore {
  font-size: 0.9em;
}

.pkt_ext_signupdetail {
  overflow: hidden;
}

.pkt_ext_signupdetail h4 {
  font-size: 0.9em;
  font-weight: normal;
}

.pkt_ext_signupdetail .btn-container {
  position: relative;
  margin-bottom: 0.8em;
}

.pkt_ext_containersignup .ff_signuphelp {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
  display: block;
  height: 18px;
  margin-top: -9px;
  inset-inline-end: -15px;
  position: absolute;
  text-indent: -9999px;
  width: 18px;
  top: 50%;
}

.pkt_ext_containersignup .alreadyhave {
  font-size: 0.9em;
  max-width: 320px;
  margin-top: 15px;
}

/*=Core detail - storyboard
--------------------------------------------------------------------------------------- */
.pkt_ext_introstory {
  align-items: center;
  display: flex;
  padding: 20px;
}

.pkt_ext_introstory:after {
  clear: both;
  content: "";
  display: table;
}

.pkt_ext_introstory p {
  margin-bottom: 0;
  text-align: start;
}

.pkt_ext_introstoryone {
  padding-block: 20px 15px;
  padding-inline: 20px 18px;
}

.pkt_ext_introstorytwo {
  padding-block: 3px 0;
  padding-inline: 20px 0;
}

.pkt_ext_introstorytwo .pkt_ext_tagline {
  margin-bottom: 1.5em;
}

.pkt_ext_introstory_text {
  flex: 1;
}

.pkt_ext_introstoryone_img,
.pkt_ext_introstorytwo_img {
  display: block;
  overflow: hidden;
  position: relative;
  text-indent: -999px;
}

.pkt_ext_introstoryone_img {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center right no-repeat;
  height: 82px;
  padding-block: 0;
  padding-inline: 0.7em 0;
  width: 82px;
}

.pkt_ext_introstoryone_img:dir(rtl) {
  background-position-x: left;
}

.pkt_ext_introstorytwo_img {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) bottom right no-repeat;
  height: 110px;
  padding-block: 1em 0;
  padding-inline: 0.7em 0;
  width: 124px;
}

.pkt_ext_introstorytwo_img:dir(rtl) {
  background-position-x: left;
}

.pkt_ext_introstorydivider {
  border-top: 1px solid #c1c1c1;
  height: 1px;
  margin: 0 auto;
  width: 125px;
}

/*=Buttons
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup .btn {
  background-color: #0096dd;
  border: 1px solid #0095dd;
  border-radius: 2px;
  color: #fff;
  display: inline-block;
  font-size: 1.1em;
  font-weight: normal;
  line-height: 1;
  margin: 0;
  padding: 11px 45px;
  text-align: center;
  text-decoration: none;
  text-shadow: 0 -1px 0 rgba(142, 4, 17, 0.5);
  transition: background-color 0.1s linear;
  width: auto;
}

.pkt_ext_containersignup .btn-secondary {
  background-color: #fbfbfb;
  border-color: #c1c1c1;
  color: #444;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.pkt_ext_containersignup .btn-small {
  padding: 6px 20px;
}

.pkt_ext_containersignup .btn:hover {
  background-color: #008acb;
  color: #fff;
  text-decoration: none;
}

.pkt_ext_containersignup .btn-secondary:hover,
.pkt_ext_containersignup .btn-important:hover {
  background-color: #f6f6f6;
  color: #222;
}

.pkt_ext_containersignup .btn-disabled {
  background-image: none;
  color: #ccc;
  color: rgba(255, 255, 255, 0.6);
  cursor: default;
  opacity: 0.9;
}

.pkt_ext_containersignup .signup-btn-firefox,
.pkt_ext_containersignup .signup-btn-email,
.pkt_ext_containersignup .signupinterim-btn-login,
.pkt_ext_containersignup .signupinterim-btn-signup,
.pkt_ext_containersignup .forgot-btn-submit,
.pkt_ext_containersignup .forgotreset-btn-change {
  min-width: 12.125em;
  padding: 0.8em 1.1875em;
  box-sizing: content-box;
}

.pkt_ext_containersignup .signup-btn-email {
  position: relative;
  z-index: 10;
}

.pkt_ext_containersignup .signup-btn-firefox {
  min-width: 14.5em;
  position: relative;
  padding: 0;
}

.pkt_ext_containersignup .signup-btn-firefox .logo {
  background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
  height: 2.6em;
  inset-inline-start: 10px;
  margin: 0;
  padding: 0;
  width: 22px;
  position: absolute;
}

.pkt_ext_containersignup .forgotreset-btn-change {
  margin-bottom: 2em;
}

.pkt_ext_containersignup .signup-btn-firefox .text {
  display: inline-block;
  padding: 0.8em 1.625em;
  position: relative;
  text-shadow: none;
  white-space: nowrap;
}

.pkt_ext_containersignup .signup-btn-firefox .text {
  color: #fff;
}

.pkt_ext_containersignup .btn-disabled .text {
  color: #ccc;
  color: rgba(255, 255, 255, 0.6);
}

/*=Language overrides
--------------------------------------------------------------------------------------- */
.pkt_ext_signup_de .pkt_ext_introstoryone_img {
  margin-inline-end: -5px;
  padding-inline-start: 0;
}

.pkt_ext_signup_de .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_es .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_ja .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_ru .pkt_ext_introstorytwo .pkt_ext_tagline {
  margin-bottom: 0.5em;
}

.pkt_ext_signup_ja .signup-btn-firefox .text,
.pkt_ext_signup_ru .signup-btn-firefox .text {
  inset-inline-start: 15px;
}

.pkt_ext_signup_de .signup-btn-firefox .logo,
.pkt_ext_signup_ja .signup-btn-firefox .logo,
.pkt_ext_signup_ru .signup-btn-firefox .logo {
  height: 2.4em;
}

@media (min-resolution: 1.1dppx) {
  .pkt_ext_signup_de .signup-btn-firefox .logo,
.pkt_ext_signup_ja .signup-btn-firefox .logo,
.pkt_ext_signup_ru .signup-btn-firefox .logo {
    height: 2.5em;
  }
}
.pkt_ext_signup_de .signup-btn-email,
.pkt_ext_signup_ja .signup-btn-email,
.pkt_ext_signup_ru .signup-btn-email {
  min-width: 13em;
  padding: 0.8533em 1.2667em;
}

.pkt_ext_signup_de .pkt_ext_logo,
.pkt_ext_signup_es .pkt_ext_logo,
.pkt_ext_signup_ru .pkt_ext_logo {
  padding-top: 15px;
}

.pkt_ext_signup_overflow.pkt_ext_signup_de .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_es .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_ja .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_ru .signup-btn-firefox .logo {
  display: none;
}

/* stylelint-disable max-nesting-depth */
.pkt_ext_containerhome,
.pkt_ext_wrapperhome {
  overflow: hidden;
}

.pkt_ext_home {
  line-height: 20px;
  color: #363636;
}
.pkt_ext_home a {
  color: #008078;
  text-decoration: none;
}
.pkt_ext_home a, .pkt_ext_home p {
  font-size: 0.9em;
}
.pkt_ext_home .pkt_ext_hr {
  height: 1px;
  background: linear-gradient(90deg, #83EDB8 0%, #83EDB8 0%, #83EDB8 0.01%, #1CB0A8 33.15%, #EF4056 67.4%, #FCB643 100%);
}
.pkt_ext_home .pkt_ext_detail {
  margin: 18px 20px;
}
.pkt_ext_home .pkt_ext_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pkt_ext_home .pkt_ext_header .pkt_ext_mylist_icon {
  background: url(../img/list-view.svg) no-repeat;
  background-size: contain;
  height: 1.2em;
  width: 1.2em;
  margin-inline-end: 8px;
}
.pkt_ext_home .pkt_ext_header a {
  height: 36px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 8px;
  margin: 12px;
  border-radius: 4px;
}
.pkt_ext_home .pkt_ext_header a:hover {
  background: #F5F5F5;
}
.pkt_ext_home .pkt_ext_header .pkt_ext_logo {
  background: url(../img/pocketlogo.svg) bottom right no-repeat;
  background-size: contain;
  height: 32px;
  width: 123px;
  margin: 0 20px;
}
.pkt_ext_home .pkt_ext_detail a {
  display: block;
}
.pkt_ext_home .pkt_ext_detail h2 {
  font-weight: 600;
  font-size: 1em;
}
.pkt_ext_home .pkt_ext_detail h2,
.pkt_ext_home .pkt_ext_detail p {
  margin: 8px 0;
}
.pkt_ext_home .pkt_ext_detail h3 {
  font-weight: 600;
  font-size: 1em;
  margin: 12px 0;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_more {
  margin: 19px 0;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_more .pkt_ext_chevron_right {
  background: url(../img/chevron-right.svg) no-repeat;
  background-size: contain;
  height: 1.2em;
  width: 1.2em;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_more ul {
  list-style-type: none;
  padding: 0;
  line-height: 14px;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_more ul li a {
  height: 44px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border-bottom: 1px solid #EAEAEA;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_discover {
  line-height: 12px;
  margin: 20px 0;
  height: 40px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}
.pkt_ext_home .pkt_ext_detail .pkt_ext_discover:hover {
  background: #F5F5F5;
}

#stp_style_guide {
  border: 1px solid #ddd;
  margin: 20px auto;
  padding: 20px;
  width: 260px;
}
#stp_style_guide #dark_mode_toggle {
  text-align: end;
}
body.theme_dark #stp_style_guide {
  background: #42414c;
}

#stp_style_guide .stp_superheader {
  margin: 0;
}
#stp_style_guide .stp_styleguide_h4 {
  border-bottom: 1px solid #ccc;
  margin: 20px 0;
}
#stp_style_guide .stp_styleguide_h5 {
  font-size: 10px;
  margin: 10px 0;
}

.stp_tag_picker .stp_tag_picker_tags {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 8px;
  border: 1px solid #8F8F9D;
  border-radius: 4px;
  font-style: normal;
  font-weight: normal;
  font-size: 1rem;
  line-height: 1.2rem;
  color: #15141A;
  margin-bottom: 10px;
}
.stp_tag_picker .stp_tag_picker_tag {
  background: #F0F0F4;
  border-radius: 4px;
  color: #15141A;
  display: inline-block;
  font-size: 0.85rem;
  line-height: 1rem;
  font-style: normal;
  font-weight: 600;
  padding: 0 8px;
  transition: background-color 200ms ease-in-out;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_tag {
  background: #2B2A33;
  color: #FBFBFB;
}

.stp_tag_picker .recent_tags .stp_tag_picker_tag {
  margin-inline-end: 5px;
}
.stp_tag_picker .stp_tag_picker_tag_remove {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-inline-end: 5px;
  color: #5B5B66;
  font-weight: 400;
}
.stp_tag_picker .stp_tag_picker_tag_remove:hover {
  color: #3E3E44;
}
.stp_tag_picker .stp_tag_picker_tag_remove:focus {
  color: #3E3E44;
  outline: 2px solid #0060df;
  outline-offset: -4px;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_tag_remove {
  color: #8F8F9D;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_tag_remove:hover {
  color: #fff;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_tag_remove:focus {
  outline: 2px solid #00DDFF;
}

.stp_tag_picker .stp_tag_picker_tag_duplicate {
  background-color: #bbb;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_tag_duplicate {
  background-color: #666;
}

.stp_tag_picker .stp_tag_picker_input_wrapper {
  display: flex;
  flex-grow: 1;
}
.stp_tag_picker .stp_tag_picker_input {
  flex-grow: 1;
  border: 1px solid #8F8F9D;
  padding: 0 6px;
  border-start-start-radius: 4px;
  border-end-start-radius: 4px;
}
.stp_tag_picker .stp_tag_picker_input:focus {
  border: 1px solid #0060DF;
  outline: 1px solid #0060DF;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_input {
  background: none;
  color: #FBFBFB;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_input:focus {
  border: 1px solid #00DDFF;
  outline: 1px solid #00DDFF;
}

.stp_tag_picker .stp_tag_picker_button {
  font-size: 0.95rem;
  line-height: 1.1rem;
  padding: 4px 6px;
  background-color: #F0F0F4;
  border: 1px solid #8F8F9D;
  border-inline-start: none;
  border-start-end-radius: 4px;
  border-end-end-radius: 4px;
}
.stp_tag_picker .stp_tag_picker_button:disabled {
  color: #8F8F9D;
}
.stp_tag_picker .stp_tag_picker_button:hover:enabled {
  background-color: #DADADF;
}
.stp_tag_picker .stp_tag_picker_button:focus:enabled {
  border: 1px solid #0060DF;
  outline: 1px solid #0060DF;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_button {
  background-color: #2B2A33;
  color: #FBFBFB;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_button:disabled {
  color: #666;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_button:hover:enabled {
  background-color: #53535d;
}
body.theme_dark .stp_tag_picker .stp_tag_picker_button:focus:enabled {
  border: 1px solid #00DDFF;
  outline: 1px solid #00DDFF;
}

/* stylelint-disable max-nesting-depth */
.stp_popular_topics {
  padding: 0;
}
.stp_popular_topics .stp_popular_topic {
  display: inline-block;
}
.stp_popular_topics .stp_popular_topic .stp_popular_topic_link {
  display: inline-block;
  background: #F0F0F4;
  border-radius: 4px;
  font-size: 0.85rem;
  line-height: 1rem;
  font-style: normal;
  font-weight: 600;
  margin-inline-end: 8px;
  margin-bottom: 8px;
  padding: 4px 8px;
  color: #000;
}
.stp_popular_topics .stp_popular_topic .stp_popular_topic_link:focus {
  text-decoration: none;
  background: #F0F0F4;
  outline: 2px solid #0060df;
  outline-offset: 2px;
}
.stp_popular_topics .stp_popular_topic .stp_popular_topic_link:hover {
  background: #E0E0E6;
  text-decoration: none;
}
.stp_popular_topics .stp_popular_topic .stp_popular_topic_link:active {
  background: #CFCFD8;
}
.stp_popular_topics .stp_popular_topic .stp_popular_topic_link::after {
  content: " >";
}
body.theme_dark .stp_popular_topics .stp_popular_topic .stp_popular_topic_link {
  background: #2B2A33;
  color: #FBFBFE;
}
body.theme_dark .stp_popular_topics .stp_popular_topic .stp_popular_topic_link:focus {
  outline: 2px solid #00DDFF;
}
body.theme_dark .stp_popular_topics .stp_popular_topic .stp_popular_topic_link:hover {
  background: #53535d;
}

.stp_article_list {
  padding: 0;
  list-style: none;
}
.stp_article_list .stp_article_list_saved_article,
.stp_article_list .stp_article_list_link {
  display: flex;
  border-radius: 4px;
  padding: 8px;
  margin: 0 -8px;
}
.stp_article_list .stp_article_list_link:hover, .stp_article_list .stp_article_list_link:focus {
  text-decoration: none;
  background-color: #ECECEE;
}
body.theme_dark .stp_article_list .stp_article_list_link:hover, body.theme_dark .stp_article_list .stp_article_list_link:focus {
  background-color: #2B2A33;
}

.stp_article_list .stp_article_list_thumb,
.stp_article_list .stp_article_list_thumb_placeholder {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  margin-inline-end: 8px;
  background-color: #ECECEE;
  flex-shrink: 0;
}
.stp_article_list .stp_article_list_header {
  font-style: normal;
  font-weight: 600;
  font-size: 0.95rem;
  line-height: 1.18rem;
  color: #15141A;
  margin: 0 0 4px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  word-break: break-word;
}
body.theme_dark .stp_article_list .stp_article_list_header {
  color: #FBFBFE;
}

.stp_article_list .stp_article_list_publisher {
  font-style: normal;
  font-weight: normal;
  font-size: 0.95rem;
  line-height: 1.18rem;
  color: #52525E;
  margin: 4px 0 0;
}
body.theme_dark .stp_article_list .stp_article_list_publisher {
  color: #CFCFD8;
}

.stp_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 12px;
  font-weight: 600;
}
.stp_header .stp_header_logo {
  background: url(../img/pocketlogo.svg) bottom center no-repeat;
  background-size: contain;
  height: 32px;
  width: 121px;
}
body.theme_dark .stp_header .stp_header_logo {
  background-image: url(../img/pocketlogo-dark.svg);
}

.stp_header .stp_button {
  margin: 0;
}

.stp_button {
  cursor: pointer;
  display: inline-block;
  margin: 12px 0;
}
.stp_button:hover {
  text-decoration: none;
}
.stp_button.stp_button_text {
  color: #0060DF;
  font-size: 0.95rem;
  line-height: 1.2rem;
  font-style: normal;
  font-weight: 600;
}
.stp_button.stp_button_text:focus {
  text-decoration: underline;
}
.stp_button.stp_button_text:hover {
  color: #0250BB;
  text-decoration: none;
}
.stp_button.stp_button_text:active {
  color: #054096;
}
body.theme_dark .stp_button.stp_button_text {
  color: #00DDFF;
}

.stp_button.stp_button_primary {
  align-items: center;
  background: #0060DF;
  border-radius: 4px;
  color: #FBFBFE;
  font-size: 0.85rem;
  line-height: 1rem;
  font-style: normal;
  font-weight: 600;
  justify-content: center;
  padding: 6px 12px;
}
.stp_button.stp_button_primary:focus {
  text-decoration: none;
  background: #0060DF;
  outline: 2px solid #0060df;
  outline-offset: 2px;
}
.stp_button.stp_button_primary:hover {
  background: #0250BB;
}
.stp_button.stp_button_primary:active {
  background: #054096;
}
body.theme_dark .stp_button.stp_button_primary {
  background: #00DDFF;
  color: #15141A;
}
body.theme_dark .stp_button.stp_button_primary:hover {
  background: #80ebfe;
}
body.theme_dark .stp_button.stp_button_primary:focus {
  outline: 2px solid #00DDFF;
}

.stp_button.stp_button_secondary {
  align-items: center;
  background: #F0F0F4;
  border-radius: 4px;
  color: #15141A;
  font-size: 0.85rem;
  line-height: 1rem;
  font-style: normal;
  font-weight: 600;
  padding: 6px 12px;
}
.stp_button.stp_button_secondary:focus {
  text-decoration: none;
  background: #F0F0F4;
  outline: 2px solid #0060df;
  outline-offset: 2px;
}
.stp_button.stp_button_secondary:hover {
  background: #E0E0E6;
}
.stp_button.stp_button_secondary:active {
  background: #CFCFD8;
}
body.theme_dark .stp_button.stp_button_secondary {
  background: #2B2A33;
  color: #FBFBFE;
}
body.theme_dark .stp_button.stp_button_secondary:focus {
  outline: 2px solid #00DDFF;
}
body.theme_dark .stp_button.stp_button_secondary:hover {
  background: #53535d;
}

.stp_button_wide .stp_button {
  display: block;
  margin: 12px 0;
  text-align: center;
  padding: 8px 12px;
}
.stp_button_wide .stp_button.stp_button_primary {
  font-size: 1.1rem;
  line-height: 1.35rem;
}
.stp_button_wide .stp_button.stp_button_secondary {
  font-size: 0.85rem;
  line-height: 1rem;
}

.stp_button_wide .stp_button {
  display: block;
  margin: 12px 0;
  text-align: center;
}

body.stp_signup_body {
  overflow: hidden;
}

.stp_panel_signup .stp_signup_content_wrapper {
  margin: 12px 0 20px;
}
.stp_panel_signup .stp_signup_img_rainbow_reader {
  background: url(../img/rainbow-reader.svg) bottom center no-repeat;
  background-size: contain;
  height: 72px;
  width: 82px;
  float: inline-end;
  margin-inline-start: 16px;
}

body.stp_saved_body {
  overflow: hidden;
}

.stp_panel_error {
  margin: 23px 0 32px;
}
.stp_panel_error .stp_panel_error_icon {
  float: inline-start;
  margin-block: 6px 16px;
  margin-inline: 7px 17px;
  background-image: url(../img/<EMAIL>);
  height: 44px;
  width: 44px;
}

/*# sourceMappingURL=main.compiled.css.map */
