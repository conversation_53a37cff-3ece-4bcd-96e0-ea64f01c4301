// Mixins

@mixin theme_dark {
  @at-root body.theme_dark & { 
    @content;
  }
}

html {
  font: menu;
}

body {
  &.theme_dark {
    background: #42414c;
    color: #FBFBFE;
  }
}

hr {
  margin: 12px -8px;
  background-color: #F0F0F4;
  height: 1px;
  border: none;

  @include theme_dark {
    background-color: #52525E;
  }
}

.header_large {
  margin: 12px 0 8px;
  font-size: 1.25rem;
  line-height: 1.65rem;

  .stp_button {
    margin: 0;
  }
}

.header_medium {
  margin: 12px 0 8px;
  font-size: 1.1rem;
  line-height: 1.35rem;
}

.header_small {
  margin: 12px 0 8px;
  font-size: 0.95rem;
  line-height: 1.16rem;
}

.header_flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header_center {
  text-align: center;
}

p {
  margin: 8px 0;
  font-size: 0.95rem;
  line-height: 1.16rem;
}
