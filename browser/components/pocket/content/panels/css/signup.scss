/*  signup.css
 *
 *  Description:
 *  With base elements out of the way, this sets all custom styling for the extension.
 *
 *  Contents:
 *  Global
 *  Core detail
 *  Core detail - storyboard
 *  Buttons
 *  Overflow mode
 *  Language overrides
 */

/*=Global
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup {
    background-color: #ebebeb;
    color: #333;
    display: block;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center;
    overflow: hidden;
}
.pkt_ext_containersignup_inactive {
    animation: pkt_ext_hide 0.3s ease-out;
    opacity: 0;
    visibility: hidden;
}
.pkt_ext_cf:after {
    content: " ";
    display: table;
    clear: both;
}
@keyframes pkt_ext_hide {
    0% {
        opacity: 1;
        visibility: visible;
    }
    99% {
        opacity: 0;
        visibility: visible;
    }
    100% {
        opacity: 0;
        visibility: hidden;
    }
}

/*=Core detail
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup p {
    font-size: 1em;
    color: #333;
    line-height: 1.3;
    margin: 0 auto 1.5em;
    max-width: 260px;
}
.pkt_ext_containersignup a {
    color: #4c8fd0;
}
.pkt_ext_containersignup a:hover {
    color: #3076b9;
}
.pkt_ext_containersignup .pkt_ext_introdetail {
    background-color: #fbfbfb;
    border: 1px solid #c1c1c1;
    border-width: 0 0 1px;
}
.pkt_ext_containersignup .pkt_ext_logo {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center bottom no-repeat;
    display: block;
    height: 32px;
    margin: 0 auto 15px;
    padding-top: 25px;
    position: relative;
    text-indent: -9999px;
    width: 123px;
}
.pkt_ext_containersignup .pkt_ext_introimg {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
    display: block;
    height: 125px;
    margin: 0 auto;
    position: relative;
    text-indent: -9999px;
    width: 255px;
}
.pkt_ext_containersignup .pkt_ext_tagline {
    margin-bottom: 0.5em;
}
.pkt_ext_containersignup .pkt_ext_learnmore {
    font-size: 0.9em;
}
.pkt_ext_signupdetail {
    overflow: hidden;
}
.pkt_ext_signupdetail h4 {
    font-size: 0.9em;
    font-weight: normal;
}
.pkt_ext_signupdetail .btn-container {
    position: relative;
    margin-bottom: 0.8em;
}
.pkt_ext_containersignup .ff_signuphelp {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
    display: block;
    height: 18px;
    margin-top: -9px;
    inset-inline-end: -15px;
    position: absolute;
    text-indent: -9999px;
    width: 18px;
    top: 50%;
}
.pkt_ext_containersignup .alreadyhave {
    font-size: 0.9em;
    max-width: 320px;
    margin-top: 15px;
}

/*=Core detail - storyboard
--------------------------------------------------------------------------------------- */
.pkt_ext_introstory {
    align-items: center;
    display: flex;
    padding: 20px;
}
.pkt_ext_introstory:after {
    clear: both;
    content: "";
    display: table;
}
.pkt_ext_introstory p {
    margin-bottom: 0;
    text-align: start;
}
.pkt_ext_introstoryone {
    padding-block: 20px 15px;
    padding-inline: 20px 18px;
}
.pkt_ext_introstorytwo {
    padding-block: 3px 0;
    padding-inline: 20px 0;
}
.pkt_ext_introstorytwo .pkt_ext_tagline {
    margin-bottom: 1.5em;
}
.pkt_ext_introstory_text {
    flex: 1;
}
.pkt_ext_introstoryone_img,
.pkt_ext_introstorytwo_img {
    display: block;
    overflow: hidden;
    position: relative;
    text-indent: -999px;
}
.pkt_ext_introstoryone_img {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center right no-repeat;
    height: 82px;
    padding-block: 0;
    padding-inline: 0.7em 0;
    width: 82px;
}
.pkt_ext_introstoryone_img:dir(rtl) {
    background-position-x: left;
}
.pkt_ext_introstorytwo_img {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) bottom right no-repeat;
    height: 110px;
    padding-block: 1em 0;
    padding-inline: 0.7em 0;
    width: 124px;
}
.pkt_ext_introstorytwo_img:dir(rtl) {
    background-position-x: left;
}
.pkt_ext_introstorydivider {
    border-top: 1px solid #c1c1c1;
    height: 1px;
    margin: 0 auto;
    width: 125px;
}

/*=Buttons
--------------------------------------------------------------------------------------- */
.pkt_ext_containersignup .btn {
    background-color: #0096dd;
    border: 1px solid #0095dd;
    border-radius: 2px;
    color: #fff;
    display: inline-block;
    font-size: 1.1em;
    font-weight: normal;
    line-height: 1;
    margin: 0;
    padding: 11px 45px;
    text-align: center;
    text-decoration: none;
    text-shadow: 0 -1px 0 rgba(142,4,17,0.5);
    transition: background-color 0.1s linear;
    width: auto;
}
.pkt_ext_containersignup .btn-secondary {
    background-color: #fbfbfb;
    border-color: #c1c1c1;
    color: #444;
    text-shadow: 0 1px 0 rgba(255,255,255,0.5);
}
.pkt_ext_containersignup .btn-small {
    padding: 6px 20px;
}
.pkt_ext_containersignup .btn:hover {
    background-color: #008acb;
    color: #fff;
    text-decoration: none;
}
.pkt_ext_containersignup .btn-secondary:hover,
.pkt_ext_containersignup .btn-important:hover {
    background-color: #f6f6f6;
    color: #222;
}
.pkt_ext_containersignup .btn-disabled {
    background-image: none;
    color: #ccc;
    color: rgba(255,255,255,0.6);
    cursor: default;
    opacity: 0.9;
}
.pkt_ext_containersignup .signup-btn-firefox,
.pkt_ext_containersignup .signup-btn-email,
.pkt_ext_containersignup .signupinterim-btn-login,
.pkt_ext_containersignup .signupinterim-btn-signup,
.pkt_ext_containersignup .forgot-btn-submit,
.pkt_ext_containersignup .forgotreset-btn-change {
    min-width: 12.125em;
    padding: 0.8em 1.1875em;
    box-sizing: content-box;
}
.pkt_ext_containersignup .signup-btn-email {
    position: relative;
    z-index: 10;
}
.pkt_ext_containersignup .signup-btn-firefox {
    min-width: 14.5em;
    position: relative;
    padding: 0;
}
.pkt_ext_containersignup .signup-btn-firefox .logo {
    background: image-set(url(../img/<EMAIL>), url(../img/<EMAIL>) 2x) center center no-repeat;
    height: 2.6em;
    inset-inline-start: 10px;
    margin: 0;
    padding: 0;
    width: 22px;
    position: absolute;
}
.pkt_ext_containersignup .forgotreset-btn-change {
    margin-bottom: 2em;
}
.pkt_ext_containersignup .signup-btn-firefox .text {
    display: inline-block;
    padding: 0.8em 1.625em;
    position: relative;
    text-shadow: none;
    white-space: nowrap;
}
.pkt_ext_containersignup .signup-btn-firefox .text {
    color: #fff;
}
.pkt_ext_containersignup .btn-disabled .text {
    color: #ccc;
    color: rgba(255,255,255,0.6);
}

/*=Language overrides
--------------------------------------------------------------------------------------- */
.pkt_ext_signup_de .pkt_ext_introstoryone_img {
    margin-inline-end: -5px;
    padding-inline-start: 0;
}
.pkt_ext_signup_de .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_es .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_ja .pkt_ext_introstorytwo .pkt_ext_tagline,
.pkt_ext_signup_ru .pkt_ext_introstorytwo .pkt_ext_tagline {
    margin-bottom: 0.5em;
}
.pkt_ext_signup_ja .signup-btn-firefox .text,
.pkt_ext_signup_ru .signup-btn-firefox .text {
    inset-inline-start: 15px;
}
.pkt_ext_signup_de .signup-btn-firefox .logo,
.pkt_ext_signup_ja .signup-btn-firefox .logo,
.pkt_ext_signup_ru .signup-btn-firefox .logo {
    height: 2.4em;
}
@media (min-resolution: 1.1dppx) {
    .pkt_ext_signup_de .signup-btn-firefox .logo,
    .pkt_ext_signup_ja .signup-btn-firefox .logo,
    .pkt_ext_signup_ru .signup-btn-firefox .logo {
        height: 2.5em;
    }
}
.pkt_ext_signup_de .signup-btn-email,
.pkt_ext_signup_ja .signup-btn-email,
.pkt_ext_signup_ru .signup-btn-email {
    min-width: 13em;
    padding: 0.8533em 1.2667em;
}
.pkt_ext_signup_de .pkt_ext_logo,
.pkt_ext_signup_es .pkt_ext_logo,
.pkt_ext_signup_ru .pkt_ext_logo {
    padding-top: 15px;
}
.pkt_ext_signup_overflow.pkt_ext_signup_de .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_es .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_ja .signup-btn-firefox .logo,
.pkt_ext_signup_overflow.pkt_ext_signup_ru .signup-btn-firefox .logo {
    display: none;
}
