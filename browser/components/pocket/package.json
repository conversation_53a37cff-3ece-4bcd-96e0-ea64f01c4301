{"description": "Task running for Save to Pocket Extension", "scripts": {"start": "npm run build && npm run watch", "build": "npm-run-all build:*", "build:webpack": "webpack --config webpack.config.js", "build:sass": "sass content/panels/css/main.scss content/panels/css/main.compiled.css", "watch": "npm-run-all -p watch:*", "watch:webpack": "npm run build:webpack -- --env development -w", "watch:sass": "chokidar \"content/panels/**/*.scss\" -c \"npm run build:sass\"", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Mozilla (https://mozilla.org/)", "license": "MPL-2.0", "devDependencies": {"@babel/core": "7.18.6", "@babel/preset-react": "7.18.6", "babel-loader": "8.2.5", "chokidar-cli": "3.0.0", "npm-run-all": "4.1.5", "sass": "1.53.0", "webpack": "5.90.1", "webpack-cli": "4.10.0"}, "repository": "https://hg.mozilla.org/", "dependencies": {"react": "18.2.0", "react-dom": "18.2.0"}}