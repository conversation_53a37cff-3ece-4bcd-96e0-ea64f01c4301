# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

browser.jar:
% content pocket %content/pocket/ contentaccessible=yes
  content/pocket/pktApi.sys.mjs (content/pktApi.sys.mjs)
  content/pocket/pktTelemetry.sys.mjs (content/pktTelemetry.sys.mjs)
  content/pocket/pktUI.js (content/pktUI.js)
  content/pocket/Pocket.sys.mjs (content/Pocket.sys.mjs)
  content/pocket/SaveToPocket.sys.mjs (content/SaveToPocket.sys.mjs)
  content/pocket/panels/css (content/panels/css/*)
  content/pocket/panels/fonts (content/panels/fonts/*)
  content/pocket/panels/img (content/panels/img/*)
  content/pocket/panels/home.html (content/panels/home.html)
  content/pocket/panels/saved.html (content/panels/saved.html)
  content/pocket/panels/signup.html (content/panels/signup.html)
  content/pocket/panels/style-guide.html (content/panels/style-guide.html)
  content/pocket/panels/js/vendor.bundle.js (content/panels/js/vendor.bundle.js)
  content/pocket/panels/js/main.bundle.js (content/panels/js/main.bundle.js)
  content/pocket/panels/js/home/<USER>/panels/js/home/<USER>
  content/pocket/panels/js/saved/entry.js (content/panels/js/saved/entry.js)
  content/pocket/panels/js/signup/entry.js (content/panels/js/signup/entry.js)
  content/pocket/panels/js/style-guide/entry.js (content/panels/js/style-guide/entry.js)
