<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<html:template id="template-select-translations-panel">
  <panel id="select-translations-panel"
         class="panel-no-padding translations-panel translations-panel-view"
         type="arrow"
         role="alertdialog"
         noautofocus="true"
         tabspecific="true"
         locationspecific="true"
         orient="vertical">
    <html:div role="document"
              aria-labelledby="select-translations-panel-header select-translations-panel-beta-icon-wrapper"
              aria-describedby="select-translations-panel-settings-button">
      <hbox id="select-translations-panel-header-row"
            class="panel-header select-translations-panel-content">
        <html:h1 class="translations-panel-header-wrapper">
          <html:span id="select-translations-panel-header" data-l10n-id="select-translations-panel-header">
          </html:span>
        </html:h1>
        <hbox id="select-translations-panel-beta-icon-wrapper"
              class="translations-panel-beta"
              role="image"
              aria-label="Beta">
          <image id="select-translations-panel-beta-icon"
                 class="translations-panel-beta-icon">
          </image>
        </hbox>
        <toolbarbutton id="select-translations-panel-settings-button"
                       class="panel-info-button translations-panel-settings-gear-icon"
                       data-l10n-id="translations-panel-settings-button"
                       tabindex="0"
                       closemenu="none"/>
      </hbox>
      <html:div id="select-translations-panel-init-failure-content"
                hidden="true">
        <hbox class="select-translations-panel-content">
          <html:moz-message-bar id="select-translations-panel-init-failure-message-bar"
                                type="error"
                                class="select-translations-panel-message-bar"
                                data-l10n-id="select-translations-panel-init-failure-message">
          </html:moz-message-bar>
        </hbox>
      </html:div>
      <html:div id="select-translations-panel-unsupported-language-content" hidden="true">
        <vbox class="select-translations-panel-content">
          <html:moz-message-bar id="select-translations-panel-unsupported-language-message-bar"
                                type="info"
                                class="select-translations-panel-message-bar"
                                data-l10n-id="select-translations-panel-unsupported-language-message-unknown">
          </html:moz-message-bar>
        </vbox>
        <vbox class="select-translations-panel-content">
          <label id="select-translations-panel-try-another-language-label"
                 class="select-translations-panel-label"
                 data-l10n-id="select-translations-panel-try-another-language-label">
          </label>
          <menulist id="select-translations-panel-try-another-language"
                    flex="1"
                    value=""
                    size="large"
                    data-l10n-id="translations-panel-choose-language"
                    aria-labelledby="
                      select-translations-panel-unsupported-language-message-bar
                      select-translations-panel-try-another-language-label
                    "
                    noinitialselection="true">
            <menupopup id="select-translations-panel-try-another-language-menupopup"
                       class="translations-panel-language-menupopup-from">
              <!-- The list of <menuitem> will be dynamically inserted. -->
            </menupopup>
          </menulist>
        </vbox>
      </html:div>
      <html:div id="select-translations-panel-main-content">
        <hbox id="select-translations-panel-lang-selection" class="select-translations-panel-content">
          <vbox flex="1">
            <label id="select-translations-panel-from-label"
                    class="select-translations-panel-label"
                    data-l10n-id="select-translations-panel-from-label">
            </label>
            <menulist id="select-translations-panel-from"
                      flex="1"
                      value=""
                      size="large"
                      data-l10n-id="translations-panel-choose-language"
                      aria-labelledby="select-translations-panel-from-label"
                      noinitialselection="true">
              <menupopup id="select-translations-panel-from-menupopup"
                          class="translations-panel-language-menupopup-from">
                <!-- The list of <menuitem> will be dynamically inserted. -->
              </menupopup>
            </menulist>
          </vbox>
          <vbox flex="1">
            <label id="select-translations-panel-to-label"
                    class="select-translations-panel-label"
                    data-l10n-id="select-translations-panel-to-label">
            </label>
            <menulist id="select-translations-panel-to"
                      flex="1"
                      value=""
                      size="large"
                      data-l10n-id="translations-panel-choose-language"
                      aria-labelledby="select-translations-panel-to-label"
                      noinitialselection="true">
              <menupopup id="select-translations-panel-to-menupopup"
                          class="translations-panel-language-menupopup-to">
                <!-- The list of <menuitem> will be dynamically inserted. -->
              </menupopup>
            </menulist>
          </vbox>
        </hbox>
        <vbox class="select-translations-panel-content">
          <html:textarea id="select-translations-panel-text-area"
                         readonly="true"
                         tabindex="0"
                         aria-labelledby="select-translations-panel-main-content">
          </html:textarea>
          <html:moz-message-bar id="select-translations-panel-translation-failure-message-bar"
                                type="error"
                                class="select-translations-panel-message-bar"
                                hidden="true"
                                data-l10n-id="select-translations-panel-translation-failure-message">
          </html:moz-message-bar>
        </vbox>
      </html:div>
      <html:div id="select-translations-panel-footer"
                class="panel-footer">
        <button id="select-translations-panel-copy-button"
                class="footer-button select-translations-panel-copy-button"
                data-l10n-id="select-translations-panel-copy-button">
        </button>
        <html:moz-button-group id="select-translations-panel-footer-button-group"
                               class="translations-panel-button-group">
          <button id="select-translations-panel-cancel-button"
                  class="footer-button"
                  hidden="true"
                  data-l10n-id="select-translations-panel-cancel-button">
          </button>
          <button id="select-translations-panel-done-button-secondary"
                  hidden="true"
                  class="footer-button"
                  data-l10n-id="select-translations-panel-done-button">
          </button>
          <button id="select-translations-panel-translate-full-page-button"
                  class="footer-button"
                  data-l10n-id="select-translations-panel-translate-full-page-button">
          </button>
          <button id="select-translations-panel-done-button-primary"
                  class="footer-button"
                  data-l10n-id="select-translations-panel-done-button"
                  default="true">
          </button>
          <button id="select-translations-panel-translate-button"
                  class="footer-button"
                  data-l10n-id="select-translations-panel-translate-button"
                  hidden="true"
                  default="true"
                  disabled="true">
          </button>
          <button id="select-translations-panel-try-again-button"
                  class="footer-button"
                  data-l10n-id="select-translations-panel-try-again-button"
                  aria-describedby="select-translations-panel-translation-failure-message-bar"
                  hidden="true"
                  default="true">
          </button>
        </html:moz-button-group>
      </html:div>
    </html:div>
  </panel>
</html:template>
