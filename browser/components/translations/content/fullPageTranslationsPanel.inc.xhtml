<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<html:template id="template-translations-panel">
<panel id="full-page-translations-panel"
       class="panel-no-padding translations-panel"
       type="arrow"
       role="alertdialog"
       noautofocus="true"
       orient="vertical">
  <panelmultiview id="full-page-translations-panel-multiview"
                  mainViewId="full-page-translations-panel-view-default">
    <panelview id="full-page-translations-panel-view-default"
               class="PanelUI-subView translations-panel-view"
               role="document"
               aria-labelledby="full-page-translations-panel-header"
               mainview-with-header="true"
               has-custom-header="true">
      <hbox class="panel-header translations-panel-header">
        <html:h1 class="translations-panel-header-wrapper">
          <html:span id="full-page-translations-panel-header"></html:span>
        </html:h1>
        <hbox class="translations-panel-beta" role="image" aria-label="Beta">
          <image class="translations-panel-beta-icon"></image>
        </hbox>
        <toolbarbutton id="translations-panel-settings"
                       class="panel-info-button translations-panel-settings-gear-icon"
                       data-l10n-id="translations-panel-settings-button"
                       closemenu="none"/>
      </hbox>

      <vbox class="translations-panel-content">
        <html:div id="full-page-translations-panel-intro">
          <html:span data-l10n-id="translations-panel-intro-description"></html:span>
          <html:a id="full-page-translations-panel-intro-learn-more-link"
                  is="moz-support-link"
                  data-l10n-id="translations-panel-learn-more-link"
                  support-page="website-translation" />
        </html:div>
        <vbox id="full-page-translations-panel-lang-selection">
          <label data-l10n-id="translations-panel-from-label" id="full-page-translations-panel-from-label"></label>
          <menulist id="full-page-translations-panel-from"
                    flex="1"
                    value="detect"
                    size="large"
                    aria-labelledby="full-page-translations-panel-from-label">
            <menupopup id="full-page-translations-panel-from-menupopup"
                      class="translations-panel-language-menupopup-from">
              <menuitem data-l10n-id="translations-panel-choose-language" value=""></menuitem>
              <!-- The list of <menuitem> will be dynamically inserted. -->
            </menupopup>
          </menulist>

          <label data-l10n-id="translations-panel-to-label" id="full-page-translations-panel-to-label"></label>
          <menulist id="full-page-translations-panel-to"
                    flex="1"
                    value="detect"
                    size="large"
                    aria-labelledby="full-page-translations-panel-to-label">
            <menupopup id="full-page-translations-panel-to-menupopup"
                      class="translations-panel-language-menupopup-to">
              <menuitem data-l10n-id="translations-panel-choose-language" value=""></menuitem>
              <!-- The list of <menuitem> will be dynamically inserted. -->
            </menupopup>
          </menulist>
        </vbox>

        <vbox id="full-page-translations-panel-error" hidden="true">
          <hbox class="translations-panel-error-header">
            <image class="translations-panel-error-icon translations-panel-error-header-icon" />
            <description id="full-page-translations-panel-error-message"></description>
          </hbox>
          <hbox id="full-page-translations-panel-error-message-hint"></hbox>
          <hbox pack="end">
            <button id="full-page-translations-panel-translate-hint-action" />
          </hbox>
        </vbox>
      </vbox>

      <html:moz-button-group class="panel-footer translations-panel-footer translations-panel-button-group">
        <button id="full-page-translations-panel-restore-button"
                class="footer-button"
                data-l10n-id="translations-panel-restore-button">
        </button>
        <button id="full-page-translations-panel-cancel"
                class="footer-button"
                data-l10n-id="translations-panel-translate-cancel">
        </button>
        <button id="full-page-translations-panel-translate"
                class="footer-button"
                data-l10n-id="translations-panel-translate-button"
                default="true">
        </button>
      </html:moz-button-group>
    </panelview>

    <panelview id="full-page-translations-panel-view-unsupported-language"
               class="PanelUI-subView translations-panel-view"
               role="document"
               aria-labelledby="full-page-translations-panel-unsupported-language-header"
               has-custom-header="true">
      <hbox class="panel-header translations-panel-header">
        <image class="translations-panel-error-icon" />
        <html:h1 id="full-page-translations-panel-unsupported-language-header">
          <html:span data-l10n-id="translations-panel-error-unsupported"></html:span>
        </html:h1>
      </hbox>

      <vbox class="translations-panel-content">
        <html:div>
          <html:span id="full-page-translations-panel-error-unsupported-hint"></html:span>
          <html:a id="full-page-translations-panel-unsupported-learn-more-link"
                  is="moz-support-link"
                  data-l10n-id="translations-panel-learn-more-link"
                  support-page="website-translation" />
        </html:div>
      </vbox>

      <html:moz-button-group class="panel-footer translations-panel-footer">
        <button id="full-page-translations-panel-change-source-language"
                class="footer-button"
                data-l10n-id="translations-panel-error-change-button">
        </button>
        <button id="full-page-translations-panel-dismiss-error"
                class="footer-button"
                data-l10n-id="translations-panel-error-dismiss-button"
                default="true">
        </button>
      </html:moz-button-group>
    </panelview>
  </panelmultiview>
</panel>
</html:template>
