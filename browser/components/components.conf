# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{5d0ce354-df01-421a-83fb-7ead0990c24e}',
        'contract_ids': [
            '@mozilla.org/browser/clh;1',
            '@mozilla.org/uriloader/content-handler;1?type=application/http-index-format',
            '@mozilla.org/uriloader/content-handler;1?type=application/xhtml+xml',
            '@mozilla.org/uriloader/content-handler;1?type=image/bmp',
            '@mozilla.org/uriloader/content-handler;1?type=image/gif',
            '@mozilla.org/uriloader/content-handler;1?type=image/jpeg',
            '@mozilla.org/uriloader/content-handler;1?type=image/jpg',
            '@mozilla.org/uriloader/content-handler;1?type=image/png',
            '@mozilla.org/uriloader/content-handler;1?type=image/svg+xml',
            '@mozilla.org/uriloader/content-handler;1?type=image/vnd.microsoft.icon',
            '@mozilla.org/uriloader/content-handler;1?type=image/x-icon',
            '@mozilla.org/uriloader/content-handler;1?type=text/css',
            '@mozilla.org/uriloader/content-handler;1?type=text/html',
            '@mozilla.org/uriloader/content-handler;1?type=text/plain',
            '@mozilla.org/uriloader/content-handler;1?type=text/rdf',
            '@mozilla.org/uriloader/content-handler;1?type=text/xml',
        ],
        'esModule': 'resource:///modules/BrowserContentHandler.sys.mjs',
        'constructor': 'nsBrowserContentHandler',
        'processes': ProcessSelector.MAIN_PROCESS_ONLY,
        'categories': {
            'command-line-handler': 'm-browser',
            'command-line-validator': 'b-browser',
        },
    },
    {
        'cid': '{47cd0651-b1be-4a0f-b5c4-10e5a573ef71}',
        'contract_ids': ['@mozilla.org/browser/final-clh;1'],
        'esModule': 'resource:///modules/BrowserContentHandler.sys.mjs',
        'processes': ProcessSelector.MAIN_PROCESS_ONLY,
        'constructor': 'nsDefaultCommandLineHandler',
        'categories': {'command-line-handler': 'x-default'},
    },
    {
        'cid': '{eab9012e-5f74-4cbc-b2b5-a590235513cc}',
        'contract_ids': ['@mozilla.org/browser/browserglue;1'],
        'esModule': 'resource:///modules/BrowserGlue.sys.mjs',
        'constructor': 'BrowserGlue',
    },
    {
        'cid': '{d8903bf6-68d5-4e97-bcd1-e4d3012f721a}',
        'contract_ids': ['@mozilla.org/content-permission/prompt;1'],
        'esModule': 'resource:///modules/BrowserGlue.sys.mjs',
        'constructor': 'ContentPermissionPrompt',
    },
]

if (buildconfig.substs.get('MOZ_DEBUG') or
        buildconfig.substs.get('MOZ_DEV_EDITION') or
        buildconfig.substs.get('NIGHTLY_BUILD')):
    Classes += [
        {
            'cid': '{11c095b2-e42e-4bdf-9dd0-aed87595f6a4}',
            'contract_ids': ['@mozilla.org/test/startuprecorder;1'],
            'esModule': 'resource:///modules/StartupRecorder.sys.mjs',
            'constructor': 'StartupRecorder',
            'categories': {'app-startup': 'startupRecorder'},
            'processes': ProcessSelector.MAIN_PROCESS_ONLY,
        },
    ]
