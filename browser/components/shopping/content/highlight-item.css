/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* Separate logo, label and details into rows and columns */
.highlight-item-wrapper {
  align-items: center;
  display: grid;
  column-gap: 1em;
  grid-template-columns: 1rem auto;
  grid-template-rows: 1rem auto;
}

.highlight-icon {
  content: url("chrome://global/skin/icons/defaultFavicon.svg");
  color: var(--icon-color);
  fill: currentColor;
  -moz-context-properties: fill;
  grid-row-start: 1;
  grid-column-start: 1;

  &.price {
    content: url("chrome://browser/content/shopping/assets/price.svg");
  }

  &.quality {
    content: url("chrome://browser/content/shopping/assets/quality.svg");
  }

  &.shipping {
    content: url("chrome://browser/content/shopping/assets/shipping.svg");
  }

  &.competitiveness {
    content: url("chrome://browser/content/shopping/assets/competitiveness.svg");
  }

  &.packaging\/appearance {
    content: url("chrome://browser/content/shopping/assets/packaging.svg");
  }
}

.highlight-label {
  font-weight: 590;
  grid-column-start: 2;
  grid-row-start: 1;
}

.highlight-details {
  grid-column-start: 2;
  grid-row-start: 2;
  margin: 0;
  padding: 0;
}

.highlight-details-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.highlight-details-list > li {
  /* Render LTR since English review snippets are only supported at this time. */
  direction: ltr;
  margin: 1em 0;
}
