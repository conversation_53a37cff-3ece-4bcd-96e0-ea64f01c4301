/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/common.css");

:host {
  display: block;
  --shopping-card-border-color: light-dark(#f0f0f4, #52525e);
  --shopping-card-border-radius: 8px;
  --shopping-card-border-width: 1px;
  --shopping-card-summary-border-radius: calc(var(--shopping-card-border-radius) - var(--shopping-card-border-width));
  --shopping-card-padding: 12px;
  border-radius: var(--shopping-card-border-radius);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
}

.shopping-card {
  display: flex;
  border: var(--shopping-card-border-width) solid var(--shopping-card-border-color);
  border-radius: var(--shopping-card-border-radius);
  background-color: var(--in-content-page-background);
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  padding: var(--shopping-card-padding);
  position: relative;
}

/* For accordions, adjust padding and border so that focus outlines wrap around the summary. */
:host([type="accordion"]) > .shopping-card {
  padding: 0;

  summary {
    padding: var(--shopping-card-padding);
    border-radius: var(--shopping-card-summary-border-radius);
  }

  #content {
    padding: 0 var(--shopping-card-padding) var(--shopping-card-padding);
  }

  > details[open] > summary {
    border-radius: var(--shopping-card-summary-border-radius) var(--shopping-card-summary-border-radius) 0 0;
  }
}

button {
  margin: 0;
}

#shopping-details {
  width: 100%;
}

#label-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

:host([type="accordion"]) #label-wrapper {
  cursor: pointer;
  position: relative;

  & > #header {
    /* 24px for the ghost button, 12px gap between
     * text and button. */
    margin-inline-end: 36px;
  }
}

#header {
  color: var(--in-content-text-color);
  font-size: 1em;
  margin: 0;
}

#content {
  align-self: stretch;
}

details > summary {
  list-style: none;
}

details > summary:focus-visible {
  outline: var(--in-content-focus-outline);
}

.chevron-icon {
  background-image: url("chrome://global/skin/icons/arrow-down.svg");
  background-position: center;
  background-repeat: no-repeat;
  color: var(--icon-color);
  -moz-context-properties: fill;
  fill: currentColor;
  width: 24px;
  height: 24px;
  /* override some default button sizing styles */
  min-width: 24px;
  min-height: 24px;
  padding: 0;
  /* Abspos rather than flexbox so we don't influence the padding
   * around the title.
   */
  position: absolute;
  top: calc(50% - var(--shopping-card-padding));
  /* This approximates the top/bottom 'padding' gap created by the abspos
   * above. It won't always be perfectly accurate, and that's OK. */
  inset-inline-end: -2px;
}

details[open] .chevron-icon {
  background-image: url("chrome://global/skin/icons/arrow-up.svg");
}

.show-more footer {
  width: 100%;
  background-color: var(--in-content-page-background);
  box-shadow: 2px -10px 11px var(--in-content-page-background);
  border-top: var(--shopping-card-border-width) solid var(--shopping-card-border-color);
  border-radius: 0 0 var(--shopping-card-border-radius) var(--shopping-card-border-radius);
  position: absolute;
  bottom: 0;
  text-align: center;
  padding-block: 8px;
  left: 0;
  right: 0;
}

.show-more[expanded="false"]  {
  overflow: clip;
  height: 200px;
}

:host(:not([showMoreButtonDisabled])) .show-more ::slotted(div) {
  margin-block-end: 4rem;
}

:host([showMoreButtonDisabled]) footer {
  display: none;
}

@media (prefers-color-scheme: dark) {
  :host > .shopping-card {
    background-color: #42414d;
  }

  .show-more footer {
    background-color: #42414d;
    box-shadow: 2px -10px 11px #42414d;
  }
}

@media (prefers-contrast) {
  .shopping-card {
    border: var(--shopping-card-border-width) solid ButtonText;

    .show-more footer {
      border-top: var(--shopping-card-border-width) solid ButtonText;
    }
  }

  /* Style accordion card like a dropdown button. */
  :host([type="accordion"]) > .shopping-card {
    summary {
      background-color: var(--button-background-color);
      color: ButtonText;

      #label-wrapper {
        color: inherit;

        > :is(#header, .chevron-icon) {
          color: inherit;
        }
      }

      .chevron-icon {
        background-color: transparent;
        border: none;
      }

      &:hover {
        background-color: SelectedItemText;
        border-color: SelectedItem;
        color: SelectedItem;
      }

      &:hover:active {
        border-color: ButtonText;
      }
    }

    details[open] {
      border-color: var(--shopping-card-border-color);

      summary {
        border-block-end: var(--shopping-card-border-width) solid ButtonText;
      }
    }
  }
}
