/* This Source Code Form is subject to the terms of the Mozilla Public
* License, v. 2.0. If a copy of the MPL was not distributed with this
* file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/common.css");

:host {
  --background-term-a: #B3FFE3;
  --background-term-b: #80EBFF;
  --background-term-c: #FFEA80;
  --background-term-d: #FFB587;
  --background-term-f: #FF848B;
  --in-content-box-border-color: rgba(0, 0, 0, 0.15);
  --inner-border: 1px solid var(--in-content-box-border-color);
  --letter-grade-width: 1.5rem;
  --letter-grade-term-inline-padding: 0.25rem;
}

#letter-grade-wrapper {
  border-radius: 4px;
  color: #000;
  display: flex;
  font-weight: 600;
  line-height: 150%;
  margin: 0;
  overflow-wrap: anywhere;
}

#letter-grade-term {
  align-items: center;
  align-self: stretch;
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0;
  font-size: 1em;
  justify-content: center;
  margin: 0;
  padding: 0.0625rem var(--letter-grade-term-inline-padding);
  text-align: center;
  width: var(--letter-grade-width);
}

:host([showdescription]) #letter-grade-term  {
  /* For border "shadow" inside the container */
  border-block: var(--inner-border);
  border-inline-start: var(--inner-border);
  border-start-start-radius: 4px;
  border-end-start-radius: 4px;
  /* Add 1px padding so that the letter does not shift when changing
   * between the show description and no description variants. */
  padding-inline-end: calc(var(--letter-grade-term-inline-padding) + 1px);
}

:host(:not([showdescription])) #letter-grade-term {
  border: var(--inner-border);
  border-radius: 4px;
}

#letter-grade-description {
  /* For border "shadow" inside the container */
  border-block: var(--inner-border);
  border-inline-end: var(--inner-border);
  border-start-end-radius: 4px;
  border-end-end-radius: 4px;

  align-items: center;
  align-self: stretch;
  box-sizing: border-box;
  display: flex;
  font-size: 0.87rem;
  font-weight: var(--font-weight);
  margin: 0;
  padding: 0.125rem 0.5rem;
}

/* Letter grade colors */

:host([letter="A"]) #letter-grade-term {
  background-color: var(--background-term-a);
}

:host([letter="A"]) #letter-grade-description {
  background-color: rgba(231, 255, 246, 1);
}

:host([letter="B"]) #letter-grade-term {
  background-color: var(--background-term-b);
}

:host([letter="B"]) #letter-grade-description {
  background-color: rgba(222, 250, 255, 1);
}

:host([letter="C"]) #letter-grade-term {
  background-color: var(--background-term-c);
}

:host([letter="C"]) #letter-grade-description {
  background-color: rgba(255, 249, 218, 1);
}

:host([letter="D"]) #letter-grade-term {
  background-color: var(--background-term-d);
}

:host([letter="D"]) #letter-grade-description {
  background-color: rgba(252, 230, 213, 1);
}

:host([letter="F"]) #letter-grade-term {
  background-color: var(--background-term-f);
}

:host([letter="F"]) #letter-grade-description {
  background-color: rgba(255, 228, 230, 1);
}

@media (prefers-contrast) {
  :host {
    --in-content-box-border-color: unset;
  }

  #letter-grade-term {
    background-color: var(--in-content-page-color) !important;
    color: var(--in-content-page-background) !important;
  }

  #letter-grade-description {
    background-color: var(--in-content-page-background) !important;
    color: var(--in-content-page-color) !important;
  }
}
