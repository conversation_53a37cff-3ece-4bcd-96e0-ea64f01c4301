/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/common.css");

#unanalyzed-product-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

#unanalyzed-product-icon {
  max-width: 264px;
  max-height: 290px;
  width: 100%;
  content: url("chrome://browser/content/shopping/assets/unanalyzedLight.avif");

  @media (prefers-color-scheme: dark) {
    content: url("chrome://browser/content/shopping/assets/unanalyzedDark.avif");
  }
}

#unanalyzed-product-message-content {
  display: flex;
  flex-direction: column;
  line-height: 1.5;

  > h2 {
    font-size: inherit;
  }

  > p {
    margin-block: 0.25rem;
  }
}

#unanalyzed-product-analysis-button {
  width: 100%;
}
