/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/common.css");

:host {
  display: block;
  border-radius: 4px;
}

#message-bar-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0;
  margin: 0;
}

:host([type="stale"]) #message-bar-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  column-gap: 8px;

  > span {
    display: flex;
    flex-direction: column;
    align-self: center;
  }
}

button {
  margin-inline-start: 0;
}

.shopping-message-bar {
  display: flex;
  align-items: center;
  padding-block: 0.5rem;
  gap: 0.75rem;

  &.analysis-in-progress {
    align-items: start;
  }

  .icon {
    --message-bar-icon-url: url("chrome://global/skin/icons/info-filled.svg");
    width: var(--icon-size-default);
    height: var(--icon-size-default);
    flex-shrink: 0;
    appearance: none;
    -moz-context-properties: fill, stroke;
    fill: currentColor;
    stroke: currentColor;
    color: var(--icon-color);
    background-image: var(--message-bar-icon-url);
  }
}

:host([type=analysis-in-progress]) .icon,
:host([type=reanalysis-in-progress]) .icon {
  --message-bar-icon-url: conic-gradient(var(--icon-color-information) var(--analysis-progress-pcent, 0%), transparent var(--analysis-progress-pcent, 0%));
  border: 1px solid var(--icon-color);
  border-radius: 50%;
  margin-block: 1px 0;
  margin-inline: 1px 0;
  width: calc(var(--icon-size-default) - 2px);
  height: calc(var(--icon-size-default) - 2px);
}
