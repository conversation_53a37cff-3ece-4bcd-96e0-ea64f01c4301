/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#recommended-ad-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-decoration: none;
  color: var(--in-content-text-color);
}

#recommended-ad-wrapper:hover {
  cursor: pointer;
}

#recommended-ad-wrapper:hover #ad-title {
  text-decoration: underline;
  color: var(--link-color-hover);
}

#recommended-ad-wrapper:focus-visible {
  outline-offset: 4px;
  border-radius: 1px;
}

#ad-content {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  height: 80px;
}

#ad-preview-image {
  max-width: 80px;
  max-height: 80px;
}

#ad-title {
  overflow: hidden;
  text-overflow: ellipsis;
  height: fit-content;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  /* This text won't be localized and when in RTL, the ellipsis is positioned
     awkwardly so we are forcing LTR */
  direction: ltr;
}

#price {
  font-size: 1em;
  font-weight: 600;
}

#footer {
  display: flex;
  justify-content: space-between;
}

#sponsored-label {
  font-size: var(--font-size-small);
  margin-block-start: 8px;
  color: var(--text-color-deemphasized);
}
