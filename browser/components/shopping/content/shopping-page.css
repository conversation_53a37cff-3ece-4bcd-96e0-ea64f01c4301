/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root {
  --shopping-header-background: light-dark(#f9f9fb, #2b2a33);
  background-color: var(--shopping-header-background);
  font: menu;
}

@media (prefers-contrast) {
  button.shopping-button:enabled,
  button.ghost-button:not(.semi-transparent):enabled {
    background-color: ButtonFace;
    border-color: ButtonText;
    color: ButtonText;

    &:hover {
      background-color: SelectedItemText;
      border-color: SelectedItem;
      color: SelectedItem;
    }

    &:hover:active {
      border-color: ButtonText;
    }
  }
}
