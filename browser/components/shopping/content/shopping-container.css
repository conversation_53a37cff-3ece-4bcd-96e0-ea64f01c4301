/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:host {
  --shopping-close-button-size: var(--button-min-height);
  --shopping-header-font-size: 1.3rem;
}

#shopping-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

#header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  background-color: var(--shopping-header-background);
  box-sizing: border-box;
  padding-block: 16px 8px;
  padding-inline: 16px 8px;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 2;
}

#shopping-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

#shopping-header-title {
  font-size: var(--shopping-header-font-size);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.shopping-header-overflow {
  box-shadow: 0 1px 2px 0 rgba(58, 57, 68, 0.20);
}

#beta-marker {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight);
  padding: 2px 4px;
  margin: 0;
  line-height: 150%;
  text-transform: uppercase;
  color: var(--icon-color);
  border: 1px solid var(--icon-color);
  border-radius: var(--border-radius-small);
}

#close-button {
  min-width: var(--shopping-close-button-size);
  min-height: var(--shopping-close-button-size);
  -moz-context-properties: fill;
  fill: currentColor;
  background-image: url("chrome://global/skin/icons/close.svg");
  background-repeat: no-repeat;
  background-position: center;
  margin-inline-end: 0;

  @media not (prefers-contrast) {
    color: var(--icon-color);
  }
}

#content {
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 16px;
  padding: 0 16px 16px;

  &.is-empty-state {
    /* We don't want to scale margins as sidebar width expands.
     * Use another unit to ensure we can approximately center content and ensure it
     * stays in place regardless of sidebar width. */
    margin-block-start: 9rem;

    .first-footer-card {
      margin-block-start: 9ch;
    }
  }

  &:focus-visible {
    outline-offset: -2px;
  }
}

#shopping-empty-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#shopping-empty-state-header {
  margin-block-end: 0;
}

#shopping-empty-state-header,
#shopping-empty-state-text {
  text-align: center;
}

#shopping-empty-state-img {
  max-width: 19em;
}

#shopping-empty-state-text {
  color: var(--text-color-deemphasized);
  max-width: 23em;
}

#shopping-empty-list-of-supported-domains {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin: 0;
  padding-inline-start: 1rem;
}

.shopping-supported-domain-link {
  text-decoration: none;
}

.loading-box {
  box-shadow: none;
  border: none;
  background: var(--in-content-button-background);
  margin-block: 1rem;
}

.loading-box.small {
  height: 2.67rem;
}

.loading-box.medium {
  height: 5.34rem;
}

.loading-box.large {
  height: 12.8rem;
}

.loading-box:nth-child(odd) {
  background-color: var(--in-content-button-background);
}

.loading-box:nth-child(even) {
  background-color: var(--in-content-button-background-hover);
}

@media not (prefers-reduced-motion) {
  .animate > .loading-box {
    animation-name: fade-in;
    animation-direction: alternate;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
  }

  /* First box + every 4th box, fifth box + every 4th box */
  .loading-box:nth-child(4n-3) {
    animation-delay: -1s;
  }

  /* Second box + every 4th box, sixth box + every 4th box */
  .loading-box:nth-child(4n-2) {
    animation-delay: 0s;
  }

  /* Third box + every 4th box */
  .loading-box:nth-child(4n-1) {
    animation-delay: -1.5s;
  }

  /* Fourth box + every 4th box */
  .loading-box:nth-child(4n) {
    animation-delay: -0.5s;
  }

  @keyframes fade-in {
   from {
     opacity: .25;
   }
   to {
     opacity: 1;
   }
 }
}
