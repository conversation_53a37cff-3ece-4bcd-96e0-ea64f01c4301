/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/common.css");

#shopping-settings-wrapper {
  --shopping-settings-between-label-and-control-option-gap: 4px;
  display: grid;
  grid-template-rows: auto;
  row-gap: 8px;
  margin-top: 12px;

  .shopping-settings-toggle-option-wrapper {
    display: grid;
    row-gap: var(--shopping-settings-between-label-and-control-option-gap);
  }

  #shopping-settings-opt-out-section {
    display: grid;
    justify-content: center;

    #shopping-settings-opt-out-button {
      display: flex;
      align-items: center;
    }
  }

  #shopping-settings-toggles-section {
    display: grid;
    row-gap: 8px;
  }

  /* When `browser.shopping.experience2023.autoOpen` is true. */
  &.shopping-settings-auto-open-ui-enabled {
    --shopping-settings-between-options-gap: 12px;
    row-gap: var(--shopping-settings-between-options-gap);

    #shopping-settings-toggles-section {
      display: grid;
      row-gap: var(--shopping-settings-between-options-gap);
    }

    .divider {
      border: var(--shopping-card-border-width) solid var(--shopping-card-border-color);

      @media (prefers-contrast) {
        border-color: ButtonText;
        border-width: 1px 0 0 0;
      }
    }

    #shopping-settings-opt-out-section {
      row-gap: var(--shopping-settings-between-label-and-control-option-gap);

      #shopping-settings-opt-out-button {
        width: fit-content;
        margin-inline-start: 0;
      }
    }
  }
}

#powered-by-fakespot {
  font-size: var(--font-size-small);
  color: var(--text-color-deemphasized);
}
