# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Adding a new metric? We have docs for that!
# https://firefox-source-docs.mozilla.org/toolkit/components/glean/user/new_definitions_file.html

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0
$tags:
  - "Firefox :: Shopping"

shopping.settings:
  nimbus_disabled_shopping:
    type: boolean
    lifetime: application
    description: |
      Indicates if <PERSON>mb<PERSON> has disabled the use the shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_NIMBUS_DISABLED
    no_lint:
      - GIFFT_NON_PING_LIFETIME

  component_opted_out:
    type: boolean
    lifetime: application
    description: |
      Indicates if the user has opted out of using the shopping component.
      Set during shopping component init and updated when changed in browser.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_COMPONENT_OPTED_OUT
    no_lint:
      - GIFFT_NON_PING_LIFETIME

  has_onboarded:
    type: boolean
    lifetime: application
    description: |
      Indicates if the user has completed the Shopping product Onboarding
      experience. Set during shopping component init and updated when changed
      in browser.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1845822
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_HAS_ONBOARDED
    no_lint:
      - GIFFT_NON_PING_LIFETIME

  disabled_ads:
    type: boolean
    lifetime: application
    description: |
      Indicates if the user has manually disabled ads. Set during shopping
      component init and updated when changed in browser.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1858540
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1858540
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_DISABLED_ADS
    no_lint:
      - GIFFT_NON_PING_LIFETIME

  auto_open_user_disabled:
    type: boolean
    lifetime: application
    description: |
      Indicates if the user has manually disabled the auto open sidebar feature.
      Set during shopping component init and updated when changed in browser.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879119
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879119
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_AUTO_OPEN_USER_DISABLED
    no_lint:
      - GIFFT_NON_PING_LIFETIME

shopping:
  surface_displayed:
    type: event
    description: |
      The Shopping product Sidebar was displayed.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849236
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848870#c2
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      side_bar_state:
        type: string
        description: |
          Which of the possible configurations of the sidebar was displayed.

  surface_reanalyze_clicked:
    type: event
    description: |
      The user clicked to REanalyze reviews in the shopping side bar. This
      metric does not contain any information about the product the user is
      viewing or any displayed trusted deals.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848870
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848870
    data_sensitivity:
      - interaction
    expires: 147
    send_in_pings:
      - events
    notification_emails:
      - <EMAIL>
      - <EMAIL>

  surface_show_quality_explainer_clicked:
    type: event
    description: |
      The user clicked to see the explanation of Review Quality in the
      shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849382
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849382
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the button was used to expand or collapse the quality
          explainer card.
          Possible values are `expanded` and `collapsed`.
        type: string

  surface_settings_expand_clicked:
    type: event
    description: |
      The user opened the settings menu of the shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849382
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849382
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the button was used to expand or collapse the settings card.
          Possible values are `expanded` and `collapsed`.
        type: string

  surface_closed:
    type: event
    description: |
      The user opened the settings menu of the shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849240
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      source:
        description: >
          The source of the close event. For example, whether the shopping
          sidebar was closed with the close button or the icon in the
          address bar.
        type: string

  address_bar_icon_clicked:
    type: event
    description: |
      The Shopping product Address Bar Icon was clicked by the user.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849239
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the icon was used to open or close the Shopping sidebar.
        type: string

  surface_show_more_reviews_button_clicked:
    type: event
    description: |
      The user clicked to expand the recent reviews to see more.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849241
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849241
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the button was used to expand or collapse the more reviews
          card.
        type: string

  surface_show_terms_clicked:
    type: event
    description: |
      The user clicked to view the Terms of Service.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849899
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_show_privacy_policy_clicked:
    type: event
    description: |
      The user clicked to view the Privacy Policy.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849899
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_not_now_clicked:
    type: event
    description: |
      The user clicked 'Not Now' to dismiss the dialog.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849899
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_opt_in_clicked:
    type: event
    description: |
      The user clicked the "Yes, try it" element to use the Shopping product's
      functionality.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849899
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_onboarding_displayed:
    type: event
    description: |
      The Shopping Side bar displayed the onboarding experience.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849899
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      configuration:
        description: >
          Which version of the onboarding experience the user was shown.
        type: string

  surface_no_review_reliability_available:
    type: event
    description: |
      Review reliability was not available for display in the shopping side
      bar. This metric does not contain any information about the product
      the user is viewing.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849243
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892#c6
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_analyze_reviews_none_available_clicked:
    type: event
    description: |
      The user clicked to analyze reviews in the case the reliability rating
      was not available for display in the shopping side bar. This metric
      does not contain any information about the product the user is viewing.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849244
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_learn_more_clicked:
    type: event
    description: |
      The user clicked the 'Learn More' link in the Shopping onboarding
      experience.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1851820
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1851820#c2
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_show_quality_explainer_url_clicked:
    type: event
    description: |
      The user clicked to see the explanation of Review Quality in the
      shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1849382
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848870
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  address_bar_icon_displayed:
    type: event
    description: |
      The Shopping product Address Bar Icon was displayed to the user.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1851036
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1841892
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_reactivated_button_clicked:
    type: event
    description: |
      The user clicked the reactivated product button.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1851675
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1851675#c4
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_stale_analysis_shown:
    type: event
    description: |
      The user was shown the dialogue box indicating that analysis of a product
      was stale. No information about the product is included.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1854223
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1854223
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  product_page_visits:
    type: counter
    description: |
      Counts number of visits to a supported retailer product page
      while enrolled in either the control or treatment branches
      of the shopping experiment.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848160
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1848160
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - metrics
    telemetry_mirror: SHOPPING_PRODUCT_PAGE_VISITS

  surface_powered_by_fakespot_link_clicked:
    type: event
    description: |
      The user clicked the "Fakespot by Mozilla" link in the shopping side
      bar.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1853785
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1853785
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  address_bar_feature_callout_displayed:
    type: event
    description: |
      The user was shown the feature callout for the Shopping component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1854376
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1854376
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      configuration:
        description: >
          Message id for the version of the feature callout shown.
        type: string

  surface_ads_clicked:
    type: event
    description: |
      An ad shown in the sidebar was clicked.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855812
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855812
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      sponsored:
        description: >
          Whether the ad was sponsored or not.
        type: boolean

  surface_ads_impression:
    type: event
    description: |
      An ad was shown and visible in the sidebar for 1.5 seconds.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855810
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855810
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      sponsored:
        description: >
          Whether the ad was sponsored or not.
        type: boolean

  surface_ads_placement:
    type: event
    description: |
      An ad unit was fetched successfully.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1872872
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1872872
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      sponsored:
        description: >
          Whether the ad was sponsored or not.
        type: boolean

  surface_no_ads_available:
    type: event
    description: |
      On a supported product page, the review checker showed analysis, and
      review checker ads were enabled, but when we tried to fetch an ad from
      the ad server, no ad was available.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855811
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1855811
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_ads_setting_toggled:
    type: event
    description: |
      The user clicked the settings toggle to enable or disable ads in the
      sidebar settings component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1858540
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1858540
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the toggle was used to enable or disable ads. Possible values
          are `enabled` and `disabled`.
        type: string

  surface_opt_out_button_clicked:
    type: event
    description: |
      The user clicked the button in the settings panel to turn off the shopping experience.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1869413
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1869413
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_auto_open_setting_toggled:
    type: event
    description: |
      The user clicked the settings toggle to enable or disable auto-open in the
      sidebar settings component.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879125
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879125
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
    extra_keys:
      action:
        description: >
          Whether the toggle was used to enable or disable auto-open. Possible values
          are `enabled` and `disabled`.
        type: string

  surface_no_thanks_button_clicked:
    type: event
    description: |
      The user clicks the 'No thanks' button when asked if they want to
      disable auto-open behavior.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879127
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879127
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events

  surface_yes_keep_closed_button_clicked:
    type: event
    description: |
      The user clicks the 'Yes, keep closed' button when asked if they want to
      disable auto-open behavior.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879127
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1879127
    data_sensitivity:
      - interaction
    expires: 147
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    send_in_pings:
      - events
