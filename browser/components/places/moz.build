# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

XPCSHELL_TESTS_MANIFESTS += [
    "tests/unit/xpcshell.toml",
]
MOCHITEST_CHROME_MANIFESTS += ["tests/chrome/chrome.toml"]
BROWSER_CHROME_MANIFESTS += [
    "tests/browser/browser.toml",
    "tests/browser/browserSidebarRevamp.toml",
    "tests/browser/interactions/browser.toml",
]
MARIONETTE_MANIFESTS += ["tests/marionette/manifest.toml"]

JAR_MANIFESTS += ["jar.mn"]

SPHINX_TREES["/browser/places"] = "docs"

EXTRA_JS_MODULES += [
    "Interactions.sys.mjs",
    "InteractionsBlocklist.sys.mjs",
    "PlacesUIUtils.sys.mjs",
]

FINAL_TARGET_FILES.actors += [
    "InteractionsChild.sys.mjs",
    "InteractionsParent.sys.mjs",
]

with Files("**"):
    BUG_COMPONENT = ("Firefox", "Bookmarks & History")
