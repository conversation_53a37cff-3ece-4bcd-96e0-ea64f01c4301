# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

browser.jar:
# Provide another URI for the bookmarkProperties dialog so we can persist the
# attributes separately
*   content/browser/places/places.xhtml                  (content/places.xhtml)
    content/browser/places/places.js                     (content/places.js)
    content/browser/places/places.css                    (content/places.css)
*   content/browser/places/bookmarkProperties.xhtml      (content/bookmarkProperties.xhtml)
    content/browser/places/bookmarkProperties.js         (content/bookmarkProperties.js)
    content/browser/places/places-commands.js            (content/places-commands.js)
    content/browser/places/places-menupopup.js           (content/places-menupopup.js)
    content/browser/places/places-tree.js                (content/places-tree.js)
    content/browser/places/controller.js                 (content/controller.js)
    content/browser/places/treeView.js                   (content/treeView.js)
    content/browser/places/browserPlacesViews.js         (content/browserPlacesViews.js)
*   content/browser/places/historySidebar.xhtml          (content/historySidebar.xhtml)
    content/browser/places/historySidebar.js             (content/historySidebar.js)
*   content/browser/places/bookmarksSidebar.xhtml        (content/bookmarksSidebar.xhtml)
    content/browser/places/bookmarksSidebar.js           (content/bookmarksSidebar.js)
    content/browser/places/editBookmark.js               (content/editBookmark.js)
    content/browser/places/placesContextMenu.js          (content/placesContextMenu.js)
#ifdef NIGHTLY_BUILD
    content/browser/places/interactionsViewer.css        (metadataViewer/interactionsViewer.css)
    content/browser/places/interactionsViewer.html       (metadataViewer/interactionsViewer.html)
    content/browser/places/interactionsViewer.js         (metadataViewer/interactionsViewer.js)
#endif
