<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!DOCTYPE window>

<window xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        xmlns:html="http://www.w3.org/1999/xhtml"
        id="bookmarkproperties"
        headerparent="bookmarkpropertiesdialog"
        neediconheader="true"
        onunload="BookmarkPropertiesPanel.onDialogUnload();"
        style="min-width: 40em;">
<dialog id="bookmarkpropertiesdialog"
        buttons="accept, cancel">

  <linkset>
    <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
    <html:link
      rel="stylesheet"
      href="chrome://global/content/commonDialog.css"
    />
    <html:link rel="stylesheet" href="chrome://global/skin/commonDialog.css" />
    <html:link
      rel="stylesheet"
      href="chrome://browser/skin/places/editBookmark.css"
    />
    <html:link
      rel="stylesheet"
      href="chrome://browser/skin/places/tree-icons.css"
    />
    <html:link
      rel="stylesheet"
      href="chrome://browser/content/places/places.css"
    />

    <html:link rel="localization" href="browser/editBookmarkOverlay.ftl"/>
  </linkset>

  <stringbundleset id="stringbundleset">
    <stringbundle id="stringBundle"
                  src="chrome://browser/locale/places/bookmarkProperties.properties"/>
  </stringbundleset>

  <script src="chrome://browser/content/places/editBookmark.js"/>
  <script src="chrome://browser/content/places/bookmarkProperties.js"/>
  <script src="chrome://global/content/globalOverlay.js"/>
  <script src="chrome://global/content/editMenuOverlay.js"/>
  <script src="chrome://browser/content/utilityOverlay.js"/>
  <script src="chrome://browser/content/places/places-tree.js"/>
  <script src="chrome://global/content/adjustableTitle.js"/>

#include editBookmarkPanel.inc.xhtml

</dialog>
</window>
