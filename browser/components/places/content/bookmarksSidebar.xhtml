<?xml version="1.0"?> <!-- -*- Mode: xml; indent-tabs-mode: nil; -*- -->
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!DOCTYPE window>

<window id="bookmarksPanel"
        class="sidebar-panel"
        xmlns:html="http://www.w3.org/1999/xhtml"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        onload="init();"
        onunload="unloadBookmarksSidebar();"
        data-l10n-id="bookmarks-sidebar-content">

  <script src="chrome://browser/content/places/bookmarksSidebar.js"/>
  <script src="chrome://global/content/globalOverlay.js"/>
  <script src="chrome://browser/content/utilityOverlay.js"/>
  <script src="chrome://browser/content/contentTheme.js"/>
  <script src="chrome://browser/content/places/places-tree.js"/>
  <script src="chrome://global/content/editMenuOverlay.js"/>

  <linkset>
    <html:link
      rel="stylesheet"
      href="chrome://browser/content/places/places.css"
    />
    <html:link
      rel="stylesheet"
      href="chrome://browser/content/usercontext/usercontext.css"
    />
    <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
    <html:link
      rel="stylesheet"
      href="chrome://browser/skin/places/tree-icons.css"
    />
    <html:link
      rel="stylesheet"
      href="chrome://browser/skin/places/sidebar.css"
    />

    <html:link rel="localization" href="toolkit/global/textActions.ftl"/>
    <html:link rel="localization" href="browser/browser.ftl"/>
    <html:link rel="localization" href="browser/places.ftl"/>
  </linkset>

#include placesCommands.inc.xhtml
#include placesContextMenu.inc.xhtml
#include bookmarksHistoryTooltip.inc.xhtml

  <hbox id="sidebar-search-container" align="center">
    <search-textbox id="search-box" flex="1"
                    data-l10n-id="places-bookmarks-search"
                    data-l10n-attrs="placeholder"
                    aria-controls="bookmarks-view"
                    oncommand="searchBookmarks(this.value);"/>
  </hbox>

  <tree id="bookmarks-view"
        class="sidebar-placesTree"
        is="places-tree"
        flex="1"
        hidecolumnpicker="true"
        context="placesContext"
        singleclickopens="true"
        onkeypress="PlacesUIUtils.onSidebarTreeKeyPress(event);"
        onclick="PlacesUIUtils.onSidebarTreeClick(event);"
        onmousemove="PlacesUIUtils.onSidebarTreeMouseMove(event);"
        onmouseout="PlacesUIUtils.setMouseoverURL('', window);">
    <treecols>
      <treecol id="title" flex="1" primary="true" hideheader="true"/>
    </treecols>
    <treechildren view="bookmarks-view"
                  class="sidebar-placesTreechildren" flex="1" tooltip="bhTooltip"/>
  </tree>
</window>
