# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Widget: Cocoa")

BROWSER_CHROME_MANIFESTS += ["tests/browser/browser.toml"]

EXTRA_JS_MODULES += [
    "MacTouchBar.sys.mjs",
]

XPCOM_MANIFESTS += [
    "components.conf",
]

SPHINX_TREES["/browser/touchbar"] = "docs"
