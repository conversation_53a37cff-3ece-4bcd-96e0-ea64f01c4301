# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Firefox", "General")

with Files("distribution.sys.mjs"):
    BUG_COMPONENT = ("Firefox", "Distributions")

with Files("tests/**"):
    BUG_COMPONENT = ("Firefox", "General")

with Files("tests/browser/browser_contentpermissionprompt.js"):
    BUG_COMPONENT = ("Firefox", "Site Permissions")

with Files("tests/unit/test_distribution.js"):
    BUG_COMPONENT = ("Firefox", "Distributions")

with Files("safebrowsing/**"):
    BUG_COMPONENT = ("<PERSON>lk<PERSON>", "Safe Browsing")

with Files("controlcenter/**"):
    BUG_COMPONENT = ("Firefox", "General")


DIRS += [
    "about",
    "aboutlogins",
    "aboutwelcome",
    "asrouter",
    "attribution",
    "backup",
    "contentanalysis",
    "contextualidentity",
    "customizableui",
    "doh",
    "downloads",
    "enterprisepolicies",
    "extensions",
    "firefoxview",
    "genai",
    "messagepreview",
    "migration",
    "newtab",
    "originattributes",
    "pagedata",
    "places",
    "preferences",
    "privatebrowsing",
    "prompts",
    "protections",
    "protocolhandler",
    "reportbrokensite",
    "resistfingerprinting",
    "screenshots",
    "search",
    "sessionstore",
    "shell",
    "shopping",
    "sidebar",
    "syncedtabs",
    "tabbrowser",
    "tabunloader",
    "textrecognition",
    "topsites",
    "translations",
    "uitour",
    "urlbar",
]

DIRS += ["build"]

if CONFIG["MOZ_SELECTABLE_PROFILES"]:
    DIRS += ["profiles"]

if CONFIG["MOZ_WIDGET_TOOLKIT"] == "cocoa":
    DIRS += ["touchbar"]
elif CONFIG["MOZ_WIDGET_TOOLKIT"] == "windows":
    DIRS += ["installerprefs"]

XPIDL_SOURCES += [
    "nsIBrowserHandler.idl",
]

XPIDL_MODULE = "browsercompsbase"

XPCOM_MANIFESTS += [
    "components.conf",
]

EXTRA_COMPONENTS += [
    "BrowserComponents.manifest",
]

EXTRA_JS_MODULES += [
    "BrowserContentHandler.sys.mjs",
    "BrowserGlue.sys.mjs",
    "distribution.sys.mjs",
]

if CONFIG["MOZ_DEBUG"] or CONFIG["MOZ_DEV_EDITION"] or CONFIG["NIGHTLY_BUILD"]:
    EXTRA_JS_MODULES += [
        "StartupRecorder.sys.mjs",
    ]

BROWSER_CHROME_MANIFESTS += [
    "safebrowsing/content/test/browser.toml",
    "tests/browser/browser.toml",
]

if CONFIG["MOZ_UPDATER"]:
    BROWSER_CHROME_MANIFESTS += [
        "tests/browser/whats_new_page/browser.toml",
    ]

MARIONETTE_MANIFESTS += ["tests/marionette/manifest.toml"]

XPCSHELL_TESTS_MANIFESTS += ["tests/unit/xpcshell.toml"]
