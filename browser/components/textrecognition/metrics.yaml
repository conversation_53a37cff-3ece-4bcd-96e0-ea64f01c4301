# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Adding a new metric? We have docs for that!
# https://firefox-source-docs.mozilla.org/toolkit/components/glean/user/new_definitions_file.html

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0
$tags:
  - 'Firefox :: General'

browser.ui.interaction:
  textrecognition_error:
    type: counter
    description: >
      Recorded when text recognition in images fails for some unknown
      reason.
      This metric was generated to correspond to the Legacy Telemetry
      scalar browser.ui.interaction.textrecognition_error.
    bugs:
      - https://bugzil.la/1783261
    data_reviews:
      - https://bugzil.la/1783261
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: BROWSER_UI_INTERACTION_TEXTRECOGNITION_ERROR
