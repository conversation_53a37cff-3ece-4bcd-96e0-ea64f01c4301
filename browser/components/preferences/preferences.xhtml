<?xml version="1.0"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this file,
   - You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml"
        xmlns:html="http://www.w3.org/1999/xhtml"
        role="document"
        id="preferences-root">

<head>
  <!-- @CSP: We should remove 'unsafe-inline' from style-src, see Bug 1579160 -->
  <meta http-equiv="Content-Security-Policy" content="default-src chrome:; img-src chrome: moz-icon: https: blob: data:; style-src chrome: data: 'unsafe-inline'; object-src 'none'" />

  <title data-l10n-id="settings-page-title"></title>

  <meta name="color-scheme" content="light dark" />
  <link rel="stylesheet" href="chrome://global/skin/global.css" />

  <link rel="stylesheet" href="chrome://global/skin/in-content/common.css" />
  <link
    rel="stylesheet"
    href="chrome://browser/skin/preferences/preferences.css"
  />
  <link
    rel="stylesheet"
    href="chrome://browser/skin/preferences/translations.css"
  />
  <link
    rel="stylesheet"
    href="chrome://browser/content/preferences/dialogs/handlers.css"
  />
  <link
    rel="stylesheet"
    href="chrome://browser/skin/preferences/applications.css"
  />
  <link rel="stylesheet" href="chrome://browser/skin/preferences/search.css" />
  <link
    rel="stylesheet"
    href="chrome://browser/skin/preferences/containers.css"
  />
  <link rel="stylesheet" href="chrome://browser/skin/preferences/privacy.css" />

  <link rel="localization" href="branding/brand.ftl"/>
  <link rel="localization" href="browser/browser.ftl"/>
    <!-- Used by fontbuilder.js -->
  <link rel="localization" href="browser/preferences/fonts.ftl"/>
  <link rel="localization" href="browser/preferences/moreFromMozilla.ftl"/>
  <link rel="localization" href="browser/preferences/preferences.ftl"/>
  <link rel="localization" href="toolkit/branding/brandings.ftl"/>
  <link rel="localization" href="toolkit/featuregates/features.ftl"/>

  <!-- Links below are only used for search-l10n-ids into subdialogs -->
  <link rel="localization" href="browser/aboutDialog.ftl"/>
  <link rel="localization" href="browser/genai.ftl"/>
  <link rel="localization" href="browser/preferences/addEngine.ftl"/>
  <link rel="localization" href="browser/preferences/blocklists.ftl"/>
  <link rel="localization" href="browser/preferences/clearSiteData.ftl"/>
  <link rel="localization" href="browser/preferences/colors.ftl"/>
  <link rel="localization" href="browser/preferences/connection.ftl"/>
  <link rel="localization" href="browser/preferences/formAutofill.ftl"/>
  <link rel="localization" href="browser/preferences/languages.ftl"/>
  <link rel="localization" href="browser/preferences/permissions.ftl"/>
  <link rel="localization" href="browser/preferences/selectBookmark.ftl"/>
  <link rel="localization" href="browser/preferences/siteDataSettings.ftl"/>
  <link rel="localization" href="browser/sanitize.ftl"/>
  <link rel="localization" href="browser/translations.ftl"/>
  <link rel="localization" href="preview/translations.ftl"/>
  <link rel="localization" href="preview/enUS-searchFeatures.ftl"/>
  <link rel="localization" href="preview/backupSettings.ftl"/>
  <link rel="localization" href="security/certificates/certManager.ftl"/>
  <link rel="localization" href="security/certificates/deviceManager.ftl"/>
  <link rel="localization" href="toolkit/updates/history.ftl"/>
  <link rel="localization" href="toolkit/global/profileSelection.ftl"/>

  <link rel="shortcut icon" href="chrome://global/skin/icons/settings.svg"/>

  <script src="chrome://browser/content/utilityOverlay.js"/>
  <script src="chrome://global/content/preferencesBindings.js"/>
  <script src="chrome://browser/content/preferences/preferences.js"/>
  <script src="chrome://browser/content/preferences/extensionControlled.js"/>
  <script src="chrome://browser/content/preferences/findInPage.js"/>
  <script src="chrome://browser/content/migration/migration-wizard.mjs" type="module"></script>
  <script type="module" src="chrome://browser/content/backup/backup-settings.mjs"></script>
</head>

<html:body xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
           id="preferences-body">

  <stringbundle id="pkiBundle"
                src="chrome://pippki/locale/pippki.properties"/>
  <stringbundle id="browserBundle"
                src="chrome://browser/locale/browser.properties"/>

  <stack id="preferences-stack" flex="1">
  <hbox flex="1">

    <vbox class="navigation">
      <!-- category list -->
      <richlistbox id="categories" data-l10n-id="category-list" data-l10n-attrs="aria-label">
        <richlistitem id="category-general"
                      class="category"
                      value="paneGeneral"
                      helpTopic="prefs-main"
                      data-l10n-id="category-general"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="pane-general-title"></label>
        </richlistitem>

        <richlistitem id="category-home"
                      class="category"
                      value="paneHome"
                      helpTopic="prefs-home"
                      data-l10n-id="category-home"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="pane-home-title"></label>
        </richlistitem>

        <richlistitem id="category-search"
                      class="category"
                      value="paneSearch"
                      helpTopic="prefs-search"
                      data-l10n-id="category-search"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="pane-search-title"></label>
        </richlistitem>

        <richlistitem id="category-privacy"
                      class="category"
                      value="panePrivacy"
                      helpTopic="prefs-privacy"
                      data-l10n-id="category-privacy"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="pane-privacy-title"></label>
        </richlistitem>

        <richlistitem id="category-sync"
                      class="category"
                      hidden="true"
                      value="paneSync"
                      helpTopic="prefs-weave"
                      data-l10n-id="category-sync3"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="pane-sync-title3"></label>
        </richlistitem>

        <richlistitem id="category-experimental"
                      class="category"
                      hidden="true"
                      value="paneExperimental"
                      helpTopic="prefs-experimental"
                      data-l10n-id="settings-category-labs"
                      data-l10n-attrs="tooltiptext"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="settings-pane-labs-title"></label>
        </richlistitem>
        <richlistitem id="category-more-from-mozilla"
                      class="category"
                      hidden="true"
                      data-l10n-id="more-from-moz-category"
                      data-l10n-attrs="tooltiptext"
                      value="paneMoreFromMozilla"
                      align="center">
          <image class="category-icon"/>
          <label class="category-name" flex="1" data-l10n-id="more-from-moz-title"></label>
        </richlistitem>

        <!-- The following <richlistitem> elements are hidden from the list of items via the hidden-category class.
            The intent is that they act as sub-pages, accessible from the UI of one of the main pages, but are
            not listed as primary pages themselves via the selector.
            These elements need to be hidden with CSS, rather than the "hidden" property, otherwise their
            functionality will be disabled.
            In the future, we may want to determine a way to support sub-pages in a first-class fashion, rather
            than creating them as hidden primary pages like this. For now, any further pages of this kind should
            be added in this fashion. -->
        <richlistitem
                      class="category hidden-category"
                      value="paneContainers"
                      helpTopic="prefs-containers"/>

        <richlistitem
                      class="category hidden-category"
                      value="paneTranslations"
                      helpTopic="prefs-translations"/>

      </richlistbox>

      <spacer flex="1"/>

      <vbox class="sidebar-footer-list">
        <html:a id="addonsButton" class="sidebar-footer-link" href="about:addons">
          <image class="sidebar-footer-icon addons-icon"/>
          <label class="sidebar-footer-label" flex="1" data-l10n-id="addons-button-label"></label>
        </html:a>
        <html:a id="helpButton" class="sidebar-footer-link" target="_blank"
                is="moz-support-link" support-page="preferences">
          <image class="sidebar-footer-icon help-icon"/>
          <label class="sidebar-footer-label" flex="1" data-l10n-id="help-button-label"></label>
        </html:a>
      </vbox>
    </vbox>

    <keyset>
      <key data-l10n-id="focus-search" key="" modifiers="accel" id="focusSearch1"/>
    </keyset>

    <vbox class="main-content" flex="1" align="start">
      <vbox class="pane-container">
        <hbox class="sticky-container">
          <hbox class="sticky-inner-container" pack="end" align="start">
            <hbox id="policies-container" class="info-box-container smaller-font-size" flex="1" hidden="true">
              <hbox class="info-icon-container">
                <html:img class="info-icon" data-l10n-attrs="alt" data-l10n-id="managed-notice-info-icon"></html:img>
              </hbox>
              <hbox align="center" flex="1">
                <html:a href="about:policies" target="_blank" data-l10n-id="managed-notice"/>
              </hbox>
            </hbox>
            <search-textbox
              id="searchInput"
              data-l10n-id="search-input-box2"
              data-l10n-attrs="placeholder, style"
              />
          </hbox>
        </hbox>
        <vbox id="mainPrefPane">
#include searchResults.inc.xhtml
#include main.inc.xhtml
#include home.inc.xhtml
#include search.inc.xhtml
#include privacy.inc.xhtml
#include containers.inc.xhtml
#include translations.inc.xhtml
#include sync.inc.xhtml
#include experimental.inc.xhtml
#include moreFromMozilla.inc.xhtml
        </vbox>
      </vbox>
    </vbox>
  </hbox>

  <stack id="dialogStack" hidden="true"/>
  <vbox id="dialogTemplate" class="dialogOverlay" align="center" pack="center" topmost="true" hidden="true">
    <vbox class="dialogBox"
          pack="end"
          role="dialog"
          aria-labelledby="dialogTitle">
      <hbox class="dialogTitleBar" align="center">
        <label class="dialogTitle" flex="1"/>
        <button class="dialogClose close-icon"
                data-l10n-id="close-button"/>
      </hbox>
      <browser class="dialogFrame"
               autoscroll="false"
               disablehistory="true"/>
    </vbox>
  </vbox>
  </stack>

  <html:dialog id="migrationWizardDialog"></html:dialog>
</html:body>
</html>
