# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<!-- General panel -->

<script src="chrome://browser/content/preferences/main.js"/>

#ifdef MOZ_UPDATER
  <script src="chrome://browser/content/aboutDialog-appUpdater.js"/>
#endif

<script src="chrome://mozapps/content/preferences/fontbuilder.js"/>

<html:template id="template-paneGeneral">
<hbox id="generalCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="pane-general-title"/>
</hbox>

<!-- Startup -->
<groupbox id="startupGroup"
          data-category="paneGeneral"
          hidden="true">
  <label><html:h2 data-l10n-id="startup-header"/></label>

  <vbox id="startupPageBox">
    <checkbox id="browserRestoreSession"
              data-l10n-id="startup-restore-windows-and-tabs"/>
    <html:a is="moz-support-link" support-page="session-restore" data-l10n-id="session-restore-learn-more" />

#ifdef XP_WIN
    <hbox id="windowsLaunchOnLoginBox" align="center" hidden="true">
      <checkbox id="windowsLaunchOnLogin"
                data-l10n-id="windows-launch-on-login"/>
    </hbox>
    <hbox id="windowsLaunchOnLoginDisabledBox" align="center" class="indent" hidden="true">
      <hbox class="info-icon-container">
        <html:img class="info-icon"/>
      </hbox>
      <html:div data-l10n-id="windows-launch-on-login-disabled">
        <html:a id="windowsAutostartLink" class="text-link" data-l10n-name="startup-link" href="ms-settings:startupapps" target="_self" />
      </html:div>
    </hbox>
    <hbox id="windowsLaunchOnLoginDisabledProfileBox" align="center" class="indent" hidden="true">
      <hbox class="info-icon-container">
        <html:img class="info-icon"/>
      </hbox>
      <html:div data-l10n-id="windows-launch-on-login-profile-disabled"/>
    </hbox>
#endif
  </vbox>

#ifdef HAVE_SHELL_SERVICE
  <vbox id="defaultBrowserBox" hidden="true">
    <checkbox id="alwaysCheckDefault" preference="browser.shell.checkDefaultBrowser"
              disabled="true"
              data-l10n-id="always-check-default"/>
    <stack id="setDefaultPane">
      <hbox id="isNotDefaultPane" align="center" class="indent">
        <label class="face-sad" id="isNotDefaultLabel" flex="1" data-l10n-id="is-not-default"/>
        <button id="setDefaultButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="set-as-my-default-browser"
                preference="pref.general.disable_button.default_browser"/>
      </hbox>
      <hbox id="isDefaultPane" align="center" class="indent">
        <label class="face-smile" id="isDefaultLabel" flex="1" data-l10n-id="is-default"/>
      </hbox>
    </stack>
  </vbox>
#endif

</groupbox>

<!-- Data migration -->
<groupbox id="dataMigrationGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="preferences-data-migration-header"/></label>

  <hbox id="dataMigration" flex="1">
    <description flex="1" control="data-migration" data-l10n-id="preferences-data-migration-description"/>
    <button id="data-migration"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="preferences-data-migration-button"/>
  </hbox>
</groupbox>

<groupbox id="dataBackupGroup" data-category="paneGeneral" hidden="true"
          data-hidden-from-search="true">
  <label><html:h2 data-l10n-id="settings-data-backup-header"/></label>
  <hbox flex="1">
    <html:backup-settings />
  </hbox>
</groupbox>

<!-- Tab preferences -->
<groupbox data-category="paneGeneral"
          hidden="true">
    <label><html:h2 data-l10n-id="tabs-group-header"/></label>

    <checkbox id="ctrlTabRecentlyUsedOrder" data-l10n-id="ctrl-tab-recently-used-order"
              preference="browser.ctrlTab.sortByRecentlyUsed"/>

    <checkbox id="linkTargeting" data-l10n-id="open-new-link-as-tabs"
              preference="browser.link.open_newwindow"/>

    <checkbox id="warnOpenMany" data-l10n-id="warn-on-open-many-tabs"
              preference="browser.tabs.warnOnOpen"/>

    <checkbox id="switchToNewTabs" data-l10n-id="switch-to-new-tabs"
              preference="browser.tabs.loadInBackground"/>

    <checkbox id="warnCloseMultiple" data-l10n-id="ask-on-close-multiple-tabs"
              preference="browser.tabs.warnOnClose"/>

#ifndef XP_WIN
    <checkbox id="warnOnQuitKey" preference="browser.warnOnQuitShortcut"/>
#endif

#ifdef XP_WIN
    <checkbox id="showTabsInTaskbar" data-l10n-id="show-tabs-in-taskbar"
              preference="browser.taskbar.previews.enable"/>
#endif

    <checkbox id="tabPreviewShowThumbnails" data-l10n-id="settings-tabs-show-image-in-preview"
              preference="browser.tabs.hoverPreview.showThumbnails" hidden="true"/>

    <vbox id="browserContainersbox" hidden="true">
      <hbox id="browserContainersExtensionContent"
            align="center" class="extension-controlled info-box-container">
        <hbox flex="1">
          <description control="disableContainersExtension" class="description-with-side-element" flex="1" />
        </hbox>
        <button id="disableContainersExtension"
                is="highlightable-button"
                class="extension-controlled-button accessory-button"
                data-l10n-id="disable-extension" />
      </hbox>
      <hbox align="center">
        <checkbox id="browserContainersCheckbox"
                  class="tail-with-learn-more"
                  data-l10n-id="browser-containers-enabled"
                  preference="privacy.userContext.enabled"/>
        <html:a
          is="moz-support-link"
          support-page="containers"
          data-l10n-id="browser-containers-learn-more"
        />
        <spacer flex="1"/>
        <button id="browserContainersSettings"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="browser-containers-settings"
                search-l10n-ids="containers-add-button.label,
                  containers-settings-button.label,
                  containers-remove-button.label"
                />
      </hbox>
    </vbox>
</groupbox>

<hbox id="languageAndAppearanceCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="language-and-appearance-header"/>
</hbox>

<!-- Website appearance -->
<groupbox id="webAppearanceGroup" data-category="paneGeneral" hidden="true">
  <html:h2 data-l10n-id="preferences-web-appearance-header"/>
  <html:div id="webAppearanceSettings">
    <description class="description-deemphasized" data-l10n-id="preferences-web-appearance-description"/>
    <html:moz-message-bar id="web-appearance-override-warning" data-l10n-id="preferences-web-appearance-override-warning2">
        <button slot="actions" class="accessory-button" data-l10n-id="preferences-colors-manage-button" id="web-appearance-manage-colors-button"/>
    </html:moz-message-bar>
    <form xmlns="http://www.w3.org/1999/xhtml" id="web-appearance-chooser" autocomplete="off">
      <label class="web-appearance-choice" data-l10n-id="preferences-web-appearance-choice-tooltip-auto">
        <div class="web-appearance-choice-image-container"><img role="presentation" alt="" width="54" height="42" /></div>
        <div class="web-appearance-choice-footer">
          <input type="radio" name="web-appearance" value="auto" data-l10n-id="preferences-web-appearance-choice-input-auto"
          /><span data-l10n-id="preferences-web-appearance-choice-auto" />
        </div>
      </label>
      <label class="web-appearance-choice" data-l10n-id="preferences-web-appearance-choice-tooltip-light">
        <div class="web-appearance-choice-image-container"><img role="presentation" alt="" width="54" height="42" /></div>
        <div class="web-appearance-choice-footer">
          <input type="radio" name="web-appearance" value="light" data-l10n-id="preferences-web-appearance-choice-input-light"
          /><span data-l10n-id="preferences-web-appearance-choice-light" />
        </div>
      </label>
      <label class="web-appearance-choice" data-l10n-id="preferences-web-appearance-choice-tooltip-dark">
        <div class="web-appearance-choice-image-container"><img role="presentation" alt="" width="54" height="42" /></div>
        <div class="web-appearance-choice-footer">
          <input type="radio" name="web-appearance" value="dark" data-l10n-id="preferences-web-appearance-choice-input-dark"
          /><span data-l10n-id="preferences-web-appearance-choice-dark" />
        </div>
      </label>
    </form>
    <html:div data-l10n-id="preferences-web-appearance-footer">
      <html:a id="web-appearance-manage-themes-link" class="text-link" data-l10n-name="themes-link" href="about:addons" target="_blank" />
    </html:div>
  </html:div>
</groupbox>

<!-- Colors -->
<groupbox id="colorsGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="preferences-colors-header"/></label>

  <hbox id="colorsSettings" align="center" flex="1">
    <description flex="1" control="colors" data-l10n-id="preferences-colors-description"/>
    <button id="colors"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="preferences-colors-manage-button"
            search-l10n-ids="
              colors-page-override,
              colors-page-override-option-always.label,
              colors-page-override-option-auto.label,
              colors-page-override-option-never.label,
              colors-text-and-background,
              colors-text-header,
              colors-background,
              colors-use-system,
              colors-links-header,
              colors-unvisited-links,
              colors-visited-links
            "/>
  </hbox>
</groupbox>

<!-- Fonts -->
<groupbox id="fontsGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="preferences-fonts-header"/></label>

  <hbox id="fontSettings">
    <hbox align="center" flex="1">
      <label control="defaultFont" data-l10n-id="default-font"/>
      <menulist id="defaultFont" delayprefsave="true"/>
      <label id="defaultFontSizeLabel" control="defaultFontSize" data-l10n-id="default-font-size"></label>
      <menulist id="defaultFontSize" delayprefsave="true">
        <menupopup>
          <menuitem value="9" label="9"/>
          <menuitem value="10" label="10"/>
          <menuitem value="11" label="11"/>
          <menuitem value="12" label="12"/>
          <menuitem value="13" label="13"/>
          <menuitem value="14" label="14"/>
          <menuitem value="15" label="15"/>
          <menuitem value="16" label="16"/>
          <menuitem value="17" label="17"/>
          <menuitem value="18" label="18"/>
          <menuitem value="20" label="20"/>
          <menuitem value="22" label="22"/>
          <menuitem value="24" label="24"/>
          <menuitem value="26" label="26"/>
          <menuitem value="28" label="28"/>
          <menuitem value="30" label="30"/>
          <menuitem value="32" label="32"/>
          <menuitem value="34" label="34"/>
          <menuitem value="36" label="36"/>
          <menuitem value="40" label="40"/>
          <menuitem value="44" label="44"/>
          <menuitem value="48" label="48"/>
          <menuitem value="56" label="56"/>
          <menuitem value="64" label="64"/>
          <menuitem value="72" label="72"/>
        </menupopup>
      </menulist>
    </hbox>

    <button id="advancedFonts"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="advanced-fonts"
            search-l10n-ids="
              fonts-window.title,
              fonts-langgroup-header,
              fonts-proportional-size,
              fonts-proportional-header,
              fonts-serif,
              fonts-sans-serif,
              fonts-monospace,
              fonts-langgroup-arabic.label,
              fonts-langgroup-armenian.label,
              fonts-langgroup-bengali.label,
              fonts-langgroup-simpl-chinese.label,
              fonts-langgroup-trad-chinese-hk.label,
              fonts-langgroup-trad-chinese.label,
              fonts-langgroup-cyrillic.label,
              fonts-langgroup-devanagari.label,
              fonts-langgroup-ethiopic.label,
              fonts-langgroup-georgian.label,
              fonts-langgroup-el.label,
              fonts-langgroup-gujarati.label,
              fonts-langgroup-gurmukhi.label,
              fonts-langgroup-japanese.label,
              fonts-langgroup-hebrew.label,
              fonts-langgroup-kannada.label,
              fonts-langgroup-khmer.label,
              fonts-langgroup-korean.label,
              fonts-langgroup-latin.label,
              fonts-langgroup-malayalam.label,
              fonts-langgroup-math.label,
              fonts-langgroup-odia.label,
              fonts-langgroup-sinhala.label,
              fonts-langgroup-tamil.label,
              fonts-langgroup-telugu.label,
              fonts-langgroup-thai.label,
              fonts-langgroup-tibetan.label,
              fonts-langgroup-canadian.label,
              fonts-langgroup-other.label,
              fonts-minsize,
              fonts-minsize-none.label,
              fonts-default-serif.label,
              fonts-default-sans-serif.label,
              fonts-allow-own.label,
            " />
  </hbox>
</groupbox>

<!-- Zoom -->
<groupbox id="zoomGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="preferences-zoom-header"/></label>
  <hbox id="zoomBox" align="center" hidden="true">
    <label control="defaultZoom" data-l10n-id="preferences-default-zoom"/>
    <menulist id="defaultZoom">
      <menupopup/>
    </menulist>
  </hbox>
  <html:moz-message-bar id="text-zoom-override-warning" data-l10n-id="preferences-text-zoom-override-warning"/>
  <checkbox id="zoomText"
    data-l10n-id="preferences-zoom-text-only"/>

</groupbox>

<!-- Languages -->
<groupbox id="languagesGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="language-header"/></label>

  <vbox id="browserLanguagesBox" align="start" hidden="true">
    <description flex="1" controls="chooseBrowserLanguage" data-l10n-id="choose-browser-language-description"/>
    <hbox>
      <menulist id="primaryBrowserLocale">
        <menupopup/>
      </menulist>
      <button id="manageBrowserLanguagesButton"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="manage-browser-languages-button"/>
    </hbox>
  </vbox>
  <hbox id="confirmBrowserLanguage" class="message-bar" align="center" hidden="true">
    <html:img class="message-bar-icon"/>
    <vbox class="message-bar-content-container" align="stretch" flex="1"/>
  </hbox>

  <hbox id="languagesBox" align="center">
    <description flex="1" control="chooseLanguage" data-l10n-id="choose-language-description"/>
    <button id="chooseLanguage"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="choose-button"
            search-l10n-ids="
              webpage-languages-window2.title,
              languages-description,
              languages-customize-moveup.label,
              languages-customize-movedown.label,
              languages-customize-remove.label,
              languages-customize-select-language.placeholder,
              languages-customize-add.label,
            " />
  </hbox>

  <checkbox id="useSystemLocale" hidden="true"
            data-l10n-id="use-system-locale"
            data-l10n-args='{"localeName": "und"}'
            preference="intl.regional_prefs.use_os_locales"/>

  <!-- This Firefox Translations UI manages the prefs for the addon only. -->
  <hbox id="fxtranslationsBox" hidden="true" data-subcategory="fxtranslations">
    <description flex="1" control="fxtranslateButton" data-l10n-id="fx-translate-web-pages"/>
    <button id="fxtranslateButton"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="translate-exceptions"/>
  </hbox>

  <checkbox id="checkSpelling"
          data-l10n-id="check-user-spelling"
          preference="layout.spellcheckDefault"/>

  <!-- Translations -->
  <vbox id="translationsGroup" hidden="true" data-subcategory="translations">
    <label><html:h2 data-l10n-id="translations-manage-header"/></label>
    <hbox id="translations-manage-description" align="center">
      <description flex="1" data-l10n-id="translations-manage-intro-2"/>
      <button id="translations-manage-settings-button"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="translations-manage-settings-button"/>
    </hbox>
    <vbox>
      <html:div id="translations-manage-install-list" hidden="true" tabindex="-1">
        <hbox class="translations-manage-language">
          <label data-l10n-id="translations-manage-download-description"></label>
          <button id="translations-manage-install-all"
                  data-l10n-id="translations-manage-language-download-all-button"></button>
          <button id="translations-manage-delete-all"
                  data-l10n-id="translations-manage-language-remove-all-button"></button>
        </hbox>
        <!-- The downloadable languages will be listed here. -->
      </html:div>
      <description id="translations-manage-error" hidden="true"></description>
    </vbox>
  </vbox>
</groupbox>

<!-- Files and Applications -->
<hbox id="filesAndApplicationsCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="files-and-applications-title"/>
</hbox>

<!--Downloads-->
<groupbox id="downloadsGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="download-header"/></label>

  <hbox id="saveWhere">
    <label id="saveTo"
           control="downloadFolder"
           data-l10n-id="download-save-where"/>
    <html:input id="downloadFolder"
                type="text"
                readonly="readonly"
                aria-labelledby="saveTo"/>
    <button id="chooseFolder"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="download-choose-folder"/>
  </hbox>
  <checkbox id="alwaysAsk"
            data-l10n-id="download-always-ask-where"
            preference="browser.download.useDownloadDir"/>
</groupbox>

<groupbox id="applicationsGroup" data-category="paneGeneral" hidden="true">
  <label><html:h2 data-l10n-id="applications-header"/></label>
  <description class="description-deemphasized" data-l10n-id="applications-description"/>
  <search-textbox id="filter" flex="1"
                  data-l10n-id="applications-filter"
                  data-l10n-attrs="placeholder"
                  aria-controls="handlersView"/>

  <listheader id="handlersViewHeader">
    <treecol id="typeColumn" data-l10n-id="applications-type-column" value="type"
             persist="sortDirection"
             style="flex: 1 50%" sortDirection="ascending"/>
    <treecol id="actionColumn" data-l10n-id="applications-action-column" value="action"
             persist="sortDirection"
             style="flex: 1 50%"/>
  </listheader>
  <richlistbox id="handlersView"
               preference="pref.downloads.disable_button.edit_actions"/>
  <label id="handleNewFileTypesDesc"
         data-l10n-id="applications-handle-new-file-types-description"/>
  <radiogroup id="handleNewFileTypes"
              preference="browser.download.always_ask_before_handling_new_types">
    <radio id="saveForNewTypes"
           value="false"
           data-l10n-id="applications-save-for-new-types"/>
    <radio id="askBeforeHandling"
           value="true"
           data-l10n-id="applications-ask-before-handling"/>
  </radiogroup>
</groupbox>


<!-- DRM Content -->
<groupbox id="drmGroup" data-category="paneGeneral" data-subcategory="drm" hidden="true">
  <label><html:h2 data-l10n-id="drm-content-header"/></label>
  <hbox align="center">
    <checkbox id="playDRMContent" preference="media.eme.enabled"
              class="tail-with-learn-more" data-l10n-id="play-drm-content" />
    <html:a is="moz-support-link"
      data-l10n-id="play-drm-content-learn-more"
      support-page="drm-content"
    />
  </hbox>
</groupbox>

<hbox id="updatesCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="update-application-title"/>
</hbox>

<!-- Update -->
<groupbox id="updateApp" data-category="paneGeneral" hidden="true">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="update-application-title"/></label>

  <description class="description-deemphasized" data-l10n-id="update-application-description"/>
  <hbox align="center">
    <vbox flex="1">
      <html:span id="updateAppInfo">
        <html:a id="releasenotes" target="_blank" data-l10n-name="learn-more" class="learnMore text-link" hidden="true"/>
      </html:span>
      <description id="distribution" class="text-blurb" hidden="true"/>
      <description id="distributionId" class="text-blurb" hidden="true"/>
    </vbox>
#ifdef MOZ_UPDATER
    <button id="showUpdateHistory"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="update-history"
            preference="app.update.disable_button.showUpdateHistory"
            search-l10n-ids="
              history-title,
              history-intro
            "/>
#endif
  </hbox>
#ifdef MOZ_UPDATER
  <vbox id="updateBox">
    <deck id="updateDeck" orient="vertical">
      <hbox id="checkForUpdates" align="start">
        <spacer flex="1"/>
        <button id="checkForUpdatesButton"
                is="highlightable-button"
                data-l10n-id="update-checkForUpdatesButton"/>
      </hbox>
      <hbox id="downloadAndInstall" align="start">
        <spacer flex="1"/>
        <button id="downloadAndInstallButton"
                is="highlightable-button"/>
                <!-- label and accesskey will be filled by JS -->
      </hbox>
      <hbox id="apply" align="start">
        <spacer flex="1"/>
        <button id="updateButton"
                is="highlightable-button"
                data-l10n-id="update-updateButton"/>
      </hbox>
      <hbox id="checkingForUpdates" align="start">
        <html:img class="update-throbber"/>
        <label data-l10n-id="update-checkingForUpdates"/>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <description id="downloading" data-l10n-id="settings-update-downloading" data-l10n-args='{"transfer":""}'>
        <html:img class="update-throbber" data-l10n-name="icon"/>
        <label data-l10n-name="download-status"/>
      </description>
      <hbox id="applying" align="start">
        <html:img class="update-throbber"/>
        <label data-l10n-id="update-applying"/>
      </hbox>
      <hbox id="downloadFailed" align="start">
        <label data-l10n-id="update-failed-main">
          <html:a id="failedLink" target="_blank" class="learnMore text-link" data-l10n-name="failed-link-main"></html:a>
        </label>
        <button id="checkForUpdatesButton2"
                data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"/>
      </hbox>
      <hbox id="policyDisabled" align="start">
        <label data-l10n-id="update-policy-disabled"/>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <hbox id="noUpdatesFound" align="start">
        <label class="face-smile" data-l10n-id="update-noUpdatesFound"/>
        <button id="checkForUpdatesButton3"
                data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"/>
      </hbox>
      <hbox id="checkingFailed" align="start">
        <label class="face-sad" data-l10n-id="aboutdialog-update-checking-failed"/>
        <button id="checkForUpdatesButton4"
                data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"/>
      </hbox>
      <hbox id="otherInstanceHandlingUpdates" align="start">
        <label data-l10n-id="update-otherInstanceHandlingUpdates"/>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <hbox id="manualUpdate" align="start">
        <description class="face-sad" data-l10n-id="settings-update-manual-with-link" data-l10n-args='{"displayUrl":""}'>
          <html:a class="manualLink" data-l10n-name="manual-link" target="_blank"/>
        </description>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <hbox id="unsupportedSystem" align="start">
        <description flex="1" data-l10n-id="update-unsupported">
          <label id="unsupportedLink" class="learnMore" data-l10n-name="unsupported-link" is="text-link"></label>
        </description>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <hbox id="restarting" align="start">
        <html:img class="update-throbber"/>
        <label data-l10n-id="update-restarting"/>
        <button data-l10n-id="update-updateButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
      <hbox id="internalError" align="start">
        <description class="face-sad" flex="1" data-l10n-id="update-internal-error2" data-l10n-args='{"displayUrl":""}'>
          <label class="manualLink" data-l10n-name="manual-link" is="text-link"/>
        </description>
        <button data-l10n-id="update-checkForUpdatesButton"
                is="highlightable-button"
                disabled="true"/>
      </hbox>
    </deck>
  </vbox>
#endif

#ifdef MOZ_UPDATER
  <label id="updateAllowDescription" data-l10n-id="update-application-allow-description"></label>
  <vbox id="updateSettingsContainer" class="info-box-container">
    <radiogroup id="updateRadioGroup">
      <radio id="autoDesktop"
             value="true"
             data-l10n-id="update-application-auto"/>
#ifdef MOZ_UPDATE_AGENT
      <checkbox id="backgroundUpdate"
                class="indent"
                hidden="true"
                data-l10n-id="update-application-background-enabled"/>
#endif
      <radio id="manualDesktop"
             value="false"
             data-l10n-id="update-application-check-choose"/>
    </radiogroup>
    <hbox id="updateSettingCrossUserWarningDesc" hidden="true">
      <hbox class="info-icon-container">
        <html:img class="info-icon"/>
      </hbox>
      <description id="updateSettingCrossUserWarning"
                   flex="1"
                   data-l10n-id="update-application-warning-cross-user-setting">
      </description>
    </hbox>
  </vbox>
#ifdef NIGHTLY_BUILD
  <checkbox id="showUpdatePrompts"
            data-l10n-id="update-application-suppress-prompts"
            preference="app.update.suppressPrompts"/>
#endif
#endif
</groupbox>

<hbox id="performanceCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="performance-title"/>
</hbox>

<!-- Performance -->
<groupbox id="performanceGroup" data-category="paneGeneral" hidden="true">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="performance-title"/></label>

  <hbox align="center">
    <checkbox id="useRecommendedPerformanceSettings"
              class="tail-with-learn-more"
              data-l10n-id="performance-use-recommended-settings-checkbox"
              preference="browser.preferences.defaultPerformanceSettings.enabled"/>
    <html:a is="moz-support-link"
      data-l10n-id="performance-settings-learn-more"
      support-page="performance"
      />
  </hbox>
  <description class="indent tip-caption" data-l10n-id="performance-use-recommended-settings-desc"/>

  <vbox id="performanceSettings" class="indent" hidden="true">
    <checkbox id="allowHWAccel"
              data-l10n-id="performance-allow-hw-accel"
              preference="layers.acceleration.disabled"/>
    <hbox align="center">
      <label id="limitContentProcess" data-l10n-id="performance-limit-content-process-option" control="contentProcessCount"/>
      <menulist id="contentProcessCount" preference="dom.ipc.processCount">
        <menupopup>
          <menuitem label="1" value="1"/>
          <menuitem label="2" value="2"/>
          <menuitem label="3" value="3"/>
          <menuitem label="4" value="4"/>
          <menuitem label="5" value="5"/>
          <menuitem label="6" value="6"/>
          <menuitem label="7" value="7"/>
          <menuitem label="8" value="8"/>
        </menupopup>
      </menulist>
    </hbox>
    <description id="contentProcessCountEnabledDescription" class="tip-caption" data-l10n-id="performance-limit-content-process-enabled-desc"/>
    <description id="contentProcessCountDisabledDescription" class="tip-caption" data-l10n-id="performance-limit-content-process-blocked-desc">
      <html:a class="text-link" data-l10n-name="learn-more" href="https://wiki.mozilla.org/Electrolysis"/>
    </description>
  </vbox>
</groupbox>

<hbox id="browsingCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="browsing-title"/>
</hbox>

<!-- Browsing -->
<groupbox id="browsingGroup" data-category="paneGeneral" hidden="true">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="browsing-title"/></label>

  <checkbox id="useAutoScroll"
            data-l10n-id="browsing-use-autoscroll"
            preference="general.autoScroll"/>
  <checkbox id="useSmoothScrolling"
            data-l10n-id="browsing-use-smooth-scrolling"
            preference="general.smoothScroll"/>
#ifdef MOZ_WIDGET_GTK
  <checkbox id="useOverlayScrollbars"
            data-l10n-id="browsing-gtk-use-non-overlay-scrollbars"
            preference="widget.gtk.overlay-scrollbars.enabled"/>
#endif
#ifdef XP_WIN
  <checkbox id="useOnScreenKeyboard"
            data-l10n-id="browsing-use-onscreen-keyboard"
            preference="ui.osk.enabled"/>
#endif
  <checkbox id="useCursorNavigation"
            data-l10n-id="browsing-use-cursor-navigation"
            preference="accessibility.browsewithcaret"/>
#ifdef XP_MACOSX
  <checkbox id="useFullKeyboardNavigation"
            data-l10n-id="browsing-use-full-keyboard-navigation"
            preference="accessibility.tabfocus" />
#endif
  <checkbox id="alwaysUnderlineLinks"
            data-l10n-id="browsing-always-underline-links"
            preference="layout.css.always_underline_links"/>
  <checkbox id="searchStartTyping"
            data-l10n-id="browsing-search-on-start-typing"
            preference="accessibility.typeaheadfind"/>
  <hbox id="pictureInPictureBox" align="center" hidden="true">
    <checkbox id="pictureInPictureToggleEnabled"
            class="tail-with-learn-more"
            data-l10n-id="browsing-picture-in-picture-toggle-enabled"
            preference="media.videocontrols.picture-in-picture.video-toggle.enabled"/>
    <html:a is="moz-support-link"
          data-l10n-id="browsing-picture-in-picture-learn-more"
          support-page="picture-in-picture"
    />
  </hbox>
  <hbox id="mediaControlBox" align="center" hidden="true">
    <checkbox id="mediaControlToggleEnabled"
            class="tail-with-learn-more"
            data-l10n-id="browsing-media-control"
            preference="media.hardwaremediakeys.enabled"/>
    <html:a is="moz-support-link"
          data-l10n-id="browsing-media-control-learn-more"
          support-page="media-keyboard-control"
    />
  </hbox>
  <hbox align="center" data-subcategory="cfraddons" hidden="true">
    <checkbox id="cfrRecommendations"
            class="tail-with-learn-more"
            data-l10n-id="browsing-cfr-recommendations"
            preference="browser.newtabpage.activity-stream.asrouter.userprefs.cfr.addons"/>
    <html:a is="moz-support-link"
          data-l10n-id="browsing-cfr-recommendations-learn-more"
          support-page="extensionrecommendations"
    />
  </hbox>
  <hbox align="center" data-subcategory="cfrfeatures" hidden="true">
    <checkbox id="cfrRecommendations-features"
            class="tail-with-learn-more"
            data-l10n-id="browsing-cfr-features"
            preference="browser.newtabpage.activity-stream.asrouter.userprefs.cfr.features"/>
    <html:a is="moz-support-link"
          data-l10n-id="browsing-cfr-recommendations-learn-more"
          support-page="extensionrecommendations"
    />
  </hbox>
</groupbox>

<hbox id="networkProxyCategory"
      class="subcategory"
      hidden="true"
      data-category="paneGeneral">
  <html:h1 data-l10n-id="network-settings-title"/>
</hbox>

<!-- Network Settings-->
<groupbox id="connectionGroup" data-category="paneGeneral" hidden="true">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="network-settings-title"/></label>

  <hbox align="center"
      data-subcategory="netsettings">
    <description flex="1" control="connectionSettings">
      <html:span id="connectionSettingsDescription"/>
      <html:a is="moz-support-link"
            data-l10n-id="network-proxy-connection-learn-more"
            support-page="prefs-connection-settings"
      />
    </description>
    <separator orient="vertical"/>
    <button id="connectionSettings"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="network-proxy-connection-settings"
            search-l10n-ids="
              connection-window2.title,
              connection-proxy-option-no.label,
              connection-proxy-option-auto.label,
              connection-proxy-option-system.label,
              connection-proxy-option-wpad.label,
              connection-proxy-option-manual.label,
              connection-proxy-http,
              connection-proxy-https,
              connection-proxy-http-port,
              connection-proxy-socks,
              connection-proxy-socks4,
              connection-proxy-socks5,
              connection-proxy-noproxy,
              connection-proxy-noproxy-desc,
              connection-proxy-https-sharing.label,
              connection-proxy-autotype.label,
              connection-proxy-reload.label,
              connection-proxy-autologin-checkbox.label,
              connection-proxy-socks-remote-dns.label,
          " />
  </hbox>
</groupbox>
</html:template>
