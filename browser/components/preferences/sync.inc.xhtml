# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<!-- Sync panel -->

<script src="chrome://browser/content/preferences/sync.js"/>
<html:template id="template-paneSync">
<hbox id="firefoxAccountCategory"
      class="subcategory"
      hidden="true"
      data-category="paneSync">
  <html:h1 data-l10n-id="pane-sync-title3"/>
</hbox>

<deck id="weavePrefsDeck" data-category="paneSync" hidden="true"
      data-hidden-from-search="true">
  <groupbox id="noFxaAccount">
    <hbox>
      <vbox flex="1">
        <label id="noFxaCaption"><html:h2 data-l10n-id="sync-signedout-caption"/></label>
        <description id="noFxaDescription" class="description-deemphasized" flex="1" data-l10n-id="sync-signedout-description2"/>
      </vbox>
      <vbox>
        <image class="fxaSyncIllustration" alt=""/>
      </vbox>
    </hbox>
    <hbox id="fxaNoLoginStatus" align="center" flex="1">
      <vbox flex="1">
        <hbox align="center" flex="1">
          <button id="noFxaSignIn"
                  is="highlightable-button"
                  class="accessory-button"
                  data-l10n-id="sync-signedout-account-signin3"/>
        </hbox>
      </vbox>
    </hbox>
    <label class="fxaMobilePromo" data-l10n-id="sync-mobile-promo">
      <html:img
        role="none"
        src="chrome://browser/skin/logo-android.svg"
        data-l10n-name="android-icon"
        class="androidIcon"/>
      <html:a
        data-l10n-name="android-link"
        class="fxaMobilePromo-android text-link" target="_blank"/>
      <html:img
        role="none"
        src="chrome://browser/skin/logo-ios.svg"
        data-l10n-name="ios-icon"
        class="iOSIcon"/>
      <html:a
        data-l10n-name="ios-link"
        class="fxaMobilePromo-ios text-link" target="_blank"/>
    </label>
  </groupbox>

  <vbox id="hasFxaAccount">
    <hbox>
      <vbox id="fxaContentWrapper" flex="1">
        <groupbox id="fxaGroup">
          <label class="search-header" hidden="true"><html:h2 data-l10n-id="pane-sync-title3"/></label>

          <deck id="fxaLoginStatus" flex="1">

            <!-- logged in and verified and all is good -->
            <hbox id="fxaLoginVerified" align="center" flex="1">
              <image id="openChangeProfileImage"
                     class="fxaProfileImage actionable"
                     role="button"
                     data-l10n-attrs="alt"
                     data-l10n-id="sync-profile-picture-with-alt"/>
              <vbox flex="1" pack="center">
                <hbox flex="1" align="baseline">
                  <label id="fxaDisplayName" hidden="true">
                    <html:h2 id="fxaDisplayNameHeading"/>
                  </label>
                  <label id="fxaEmailAddress" flex="1" crop="end"/>
                  <button id="fxaUnlinkButton"
                          is="highlightable-button"
                          class="accessory-button"
                          data-l10n-id="sync-sign-out"/>
                </hbox>
                <hbox>
                  <html:a id="verifiedManage" class="openLink" target="_blank"
                          data-l10n-id="sync-manage-account"/>
                </hbox>
              </vbox>
            </hbox>

            <!-- logged in to an unverified account -->
            <hbox id="fxaLoginUnverified">
              <vbox>
                <image class="fxaProfileImage"
                       data-l10n-attrs="alt"
                       data-l10n-id="sync-profile-picture-account-problem"/>
              </vbox>
              <vbox flex="1" pack="center">
                <hbox align="center">
                  <image class="fxaLoginRejectedWarning"
                         data-l10n-attrs="alt"
                         data-l10n-id="fxa-login-rejected-warning"/>
                  <description flex="1"
                    class="l10nArgsEmailAddress"
                    data-l10n-id="sync-signedin-unverified"
                    data-l10n-args='{"email": ""}'/>
                </hbox>
                <hbox class="fxaAccountBoxButtons">
                  <button id="verifyFxaAccount"
                          is="highlightable-button"
                          data-l10n-id="sync-verify-account"/>
                  <button id="unverifiedUnlinkFxaAccount"
                          is="highlightable-button"
                          data-l10n-id="sync-remove-account"/>
                </hbox>
              </vbox>
            </hbox>

            <!-- logged in locally but server rejected credentials -->
            <hbox id="fxaLoginRejected">
              <vbox>
                <image class="fxaProfileImage"
                       data-l10n-attrs="alt"
                       data-l10n-id="sync-profile-picture-account-problem"/>
              </vbox>
              <vbox flex="1" pack="center">
                <hbox align="center">
                  <image class="fxaLoginRejectedWarning"
                         data-l10n-attrs="alt"
                         data-l10n-id="fxa-login-rejected-warning"/>
                  <description flex="1"
                    class="l10nArgsEmailAddress"
                    data-l10n-id="sync-signedin-login-failure"
                    data-l10n-args='{"email": ""}'/>
                </hbox>
                <hbox class="fxaAccountBoxButtons">
                  <button id="rejectReSignIn"
                          is="highlightable-button"
                          data-l10n-id="sync-sign-in"/>
                  <button id="rejectUnlinkFxaAccount"
                          is="highlightable-button"
                          data-l10n-id="sync-remove-account"/>
                </hbox>
              </vbox>
            </hbox>
          </deck>
        </groupbox>

        <groupbox>
          <label control="fxaSyncComputerName">
            <html:h2 data-l10n-id="sync-device-name-header"/>
          </label>
          <hbox id="fxaDeviceName">
            <html:input id="fxaSyncComputerName" type="text" disabled="true"/>
            <button id="fxaChangeDeviceName"
                    is="highlightable-button"
                    class="needs-account-ready"
                    data-l10n-id="sync-device-name-change"/>
            <button id="fxaCancelChangeDeviceName"
                    is="highlightable-button"
                    data-l10n-id="sync-device-name-cancel"
                    hidden="true"/>
            <button id="fxaSaveChangeDeviceName"
                    is="highlightable-button"
                    data-l10n-id="sync-device-name-save"
                    hidden="true"/>
          </hbox>
        </groupbox>

        <groupbox>
          <vbox id="syncStatusContainer" flex="1">
            <hbox>
              <html:h2 id="syncStatusTitle" data-l10n-id="prefs-syncing-off"/>
              <spacer flex="1"/>
              <!-- 'Sync Now' button, visible only when sync is on -->
              <button id="syncNow" class="accessory-button needs-account-ready"></button>
            </hbox>
            <vbox id="syncNotConfigured">
              <hbox class="sync-group info-box-container">
                <vbox flex="1">
                  <label data-l10n-id="prefs-sync-offer-setup-label2"/>
                </vbox>
              <button id="syncSetup"
                      is="highlightable-button"
                      class="accessory-button needs-account-ready"
                      data-l10n-id="prefs-sync-turn-on-syncing"/>
              </hbox>
            </vbox>

            <vbox id="syncConfigured">
              <vbox class="sync-group sync-info-box sync-configured">
                <label data-l10n-id="sync-syncing-across-devices-heading"/>
                <html:div class="sync-engines-list">
                  <html:div engine_preference="services.sync.engine.bookmarks">
                    <image class="sync-engine-image sync-engine-bookmarks" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-bookmarks"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.history">
                    <image class="sync-engine-image sync-engine-history" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-history"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.tabs">
                    <image class="sync-engine-image sync-engine-tabs" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-tabs"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.passwords">
                    <image class="sync-engine-image sync-engine-passwords" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-passwords"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.addresses">
                    <image class="sync-engine-image sync-engine-addresses" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-addresses"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.creditcards">
                    <image class="sync-engine-image sync-engine-creditcards" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-payment-methods"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.addons">
                    <image class="sync-engine-image sync-engine-addons" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-addons"/>
                  </html:div>
                  <html:div engine_preference="services.sync.engine.prefs">
                    <image class="sync-engine-image sync-engine-prefs" alt=""/>
                    <label data-l10n-id="sync-currently-syncing-settings"/>
                  </html:div>
                </html:div>
                <hbox>
                  <button id="syncChangeOptions"
                          is="highlightable-button"
                          data-l10n-id="sync-manage-options"/>
                  <spacer flex="1"/>
                </hbox>
              </vbox>
            </vbox>
          </vbox>
        </groupbox>
      </vbox>
    </hbox>
    <vbox align="start">
      <html:a id="connect-another-device"
             is="text-link"
             class="fxaMobilePromo"
             target="_blank"
             data-l10n-id="sync-connect-another-device"/>
    </vbox>
  </vbox>
</deck>
</html:template>
