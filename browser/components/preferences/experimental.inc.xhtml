# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<!-- Experimental panel -->

<script src="chrome://browser/content/preferences/experimental.js"/>
<html:template id="template-paneExperimental">
<vbox id="firefoxExperimentalCategory"
      class="subcategory"
      hidden="true"
      data-category="paneExperimental">
  <html:h1 data-l10n-id="settings-pane-labs-title" class="section-heading"/>
</vbox>

<html:div data-category="paneExperimental"
          id="pane-experimental-featureGates"
          hidden="true">
  <label class="search-header" hidden="true">
    <html:h2 data-l10n-id="settings-pane-labs-title" class="section-heading"/>
  </label>
  <html:p data-l10n-id="pane-experimental-description3"
          class="description-deemphasized section-description"/>
  <hbox pack="start" class="section-header-last">
    <html:moz-button id="experimentalCategory-reset"
                     data-l10n-id="pane-experimental-reset"/>
  </hbox>
</html:div>
</html:template>

<html:template id="template-testFeatureGateExtra">
  <html:div id="testFeatureGateExtraContent">Test extra content</html:div>
</html:template>

<html:template id="template-genai-chat">
    <html:div>
        <label id="genai-chat-label"
               class="indent"
               control="genai-chat-provider"
               data-l10n-id="genai-settings-chat-choose"/>
        <description class="featureGateDescription indent tip-caption"
            data-l10n-id="genai-settings-chat-links"
            id="genai-chat-links"/>
        <menulist aria-describedby="genai-chat-links"
                  class="indent"
                  id="genai-chat-provider"
                  preference="browser.ml.chat.provider">
            <menupopup>
                <menuitem data-l10n-id="genai-settings-chat-choose-one-menuitem"
                          value=""/>
            </menupopup>
        </menulist>
    </html:div>
    <html:moz-checkbox class="indent"
                       data-l10n-id="genai-settings-chat-shortcuts"
                       id="genai-chat-shortcuts"
                       preference="browser.ml.chat.shortcuts"/>
</html:template>
