# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<!-- Privacy panel -->

<script src="chrome://browser/content/preferences/privacy.js"/>
<stringbundle id="signonBundle" src="chrome://passwordmgr/locale/passwordmgr.properties"/>
<html:template id="template-panePrivacy">
<hbox id="browserPrivacyCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="privacy-header"/>
</hbox>

<!-- Tracking / Content Blocking -->
<groupbox id="trackingGroup" data-category="panePrivacy" hidden="true" aria-describedby="contentBlockingDescription" class="highlighting-group">
  <label id="contentBlockingHeader"><html:h2 data-l10n-id="content-blocking-enhanced-tracking-protection"/></label>
  <vbox data-subcategory="trackingprotection">
    <hbox align="start">
      <image id="trackingProtectionShield"/>
      <description class="description-with-side-element" flex="1">
        <html:span id="contentBlockingDescription" data-l10n-id="content-blocking-section-top-level-description"></html:span>
        <html:a is="moz-support-link"
                id="contentBlockingLearnMore"
                class="learnMore"
                data-l10n-id="content-blocking-learn-more"
                support-page="enhanced-tracking-protection"
        />
      </description>
      <button id="trackingProtectionExceptions"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="tracking-manage-exceptions"
              preference="pref.privacy.disable_button.tracking_protection_exceptions"
              search-l10n-ids="
                permissions-address,
                permissions-disable-etp,
                permissions-remove.label,
                permissions-remove-all.label,
                permissions-exceptions-etp-window2.title,
                permissions-exceptions-manage-etp-desc,
              "/>
    </hbox>
    <hbox id="rfpIncompatibilityWarning" class="info-box-container" hidden="true">
      <vbox class="info-icon-container">
        <html:img class="info-icon"></html:img>
      </vbox>
      <vbox flex="1">
        <description>
          <html:span data-l10n-id="content-blocking-rfp-incompatibility-warning"/>
          <html:a is="moz-support-link"
                  class="learnMore"
                  support-page="resist-fingerprinting"
          />
        </description>
      </vbox>
    </hbox>
    <hbox id="fpiIncompatibilityWarning" class="info-box-container" hidden="true">
      <vbox class="info-icon-container">
        <html:img class="info-icon"></html:img>
      </vbox>
      <vbox flex="1">
        <description>
          <html:span data-l10n-id="content-blocking-fpi-incompatibility-warning"/>
        </description>
      </vbox>
    </hbox>
    <vbox id="contentBlockingCategories">
      <radiogroup id="contentBlockingCategoryRadio"
                  preference="browser.contentblocking.category"
                  aria-labelledby="trackingProtectionMenuDesc">
        <vbox id="contentBlockingOptionStandard" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="standardRadio"
                   value="standard"
                   data-l10n-id="enhanced-tracking-protection-setting-standard"
                   flex="1"/>
            <button id="standardArrow"
                    is="highlightable-button"
                    class="arrowhead"
                    data-l10n-id="content-blocking-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <description data-l10n-id="content-blocking-etp-standard-desc"></description>
            <vbox class="privacy-extra-information">
              <label class="content-blocking-extra-blocking-desc" data-l10n-id="content-blocking-etp-blocking-desc"/>
              <vbox class="indent">
                <hbox class="extra-information-label social-media-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-social-media-trackers"/>
                </hbox>
                <hbox class="extra-information-label cross-site-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-cookies-in-all-windows2"/>
                </hbox>
                <hbox class="extra-information-label third-party-tracking-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-tracking-cookies"/>
                </hbox>
                <hbox class="extra-information-label all-third-party-cookies-private-windows-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cross-site-cookies-private-windows"/>
                </hbox>
                <hbox class="extra-information-label third-party-tracking-cookies-plus-isolate-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-tracking-cookies-plus-isolate"/>
                </hbox>
                <hbox class="extra-information-label pb-trackers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-private-windows"/>
                </hbox>
                <hbox class="extra-information-label trackers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-windows-tracking-content"/>
                </hbox>
                <hbox class="extra-information-label all-third-party-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cross-site-cookies"/>
                </hbox>
                <hbox class="extra-information-label all-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cookies"/>
                </hbox>
                <hbox class="extra-information-label unvisited-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-unvisited-cookies"/>
                </hbox>
                <hbox class="extra-information-label cryptominers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cryptominers"/>
                </hbox>
                <hbox class="extra-information-label fingerprinters-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-fingerprinters"/>
                </hbox>
              </vbox>
              <vbox id="etpStandardTCPBox" class="content-blocking-warning info-box-container">
                <label class="content-blocking-warning-title" data-l10n-id="content-blocking-etp-standard-tcp-title"/>
                <description>
                  <html:span data-l10n-id="content-blocking-etp-standard-tcp-rollout-description"></html:span>
                  <html:a is="moz-support-link"
                          id="tcp-learn-more-link"
                          class="learnMore"
                          data-l10n-id="content-blocking-etp-standard-tcp-rollout-learn-more"
                          support-page="total-cookie-protection"
                  />
                </description>
              </vbox>
              <html:div class="content-blocking-warning info-box-container reload-tabs" hidden="true">
                <html:div class="content-blocking-reload-desc-container">
                  <html:div class="info-icon-container">
                    <html:img class="info-icon"/>
                  </html:div>
                  <html:span data-l10n-id="content-blocking-reload-description"
                             class="content-blocking-reload-description" />
                </html:div>
                <button class="accessory-button reload-tabs-button primary"
                        is="highlightable-button"
                        data-l10n-id="content-blocking-reload-tabs-button"/>
              </html:div>
            </vbox>
          </vbox>
        </vbox>
        <vbox id="contentBlockingOptionStrict" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="strictRadio"
                   value="strict"
                   data-l10n-id="enhanced-tracking-protection-setting-strict"
                   flex="1"/>
            <button id="strictArrow"
                    is="highlightable-button"
                    class="arrowhead"
                    data-l10n-id="content-blocking-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <label data-l10n-id="content-blocking-etp-strict-desc"></label>
            <vbox class="privacy-extra-information">
              <label class="content-blocking-extra-blocking-desc" data-l10n-id="content-blocking-etp-blocking-desc"/>
              <vbox class="indent">
                <hbox class="extra-information-label social-media-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-social-media-trackers"/>
                </hbox>
                <hbox class="extra-information-label third-party-tracking-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-tracking-cookies"/>
                </hbox>
                <hbox class="extra-information-label all-third-party-cookies-private-windows-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cross-site-cookies-private-windows"/>
                </hbox>
                <hbox class="extra-information-label cross-site-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-cookies-in-all-windows2"/>
                </hbox>
                <hbox class="extra-information-label pb-trackers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-private-windows"/>
                </hbox>
                <hbox class="extra-information-label trackers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-windows-tracking-content"/>
                </hbox>
                <hbox class="extra-information-label all-third-party-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cross-site-cookies"/>
                </hbox>
                <hbox class="extra-information-label all-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-all-cookies"/>
                </hbox>
                <hbox class="extra-information-label unvisited-cookies-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-unvisited-cookies"/>
                </hbox>
                <hbox class="extra-information-label third-party-tracking-cookies-plus-isolate-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cross-site-tracking-cookies-plus-isolate"/>
                </hbox>
                <hbox class="extra-information-label cryptominers-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-cryptominers"/>
                </hbox>
                <hbox class="extra-information-label fingerprinters-option" hidden="true">
                  <label class="content-blocking-label" data-l10n-id="content-blocking-known-and-suspected-fingerprinters"/>
                </hbox>
              </vbox>
              <html:div class="content-blocking-warning info-box-container reload-tabs" hidden="true">
                <html:div class="content-blocking-reload-desc-container">
                  <html:div class="info-icon-container">
                    <html:img class="info-icon"/>
                  </html:div>
                  <html:span data-l10n-id="content-blocking-reload-description"
                             class="content-blocking-reload-description" />
                </html:div>
                <button class="accessory-button reload-tabs-button primary"
                        is="highlightable-button"
                        data-l10n-id="content-blocking-reload-tabs-button"/>
              </html:div>
              <vbox class="content-blocking-warning info-box-container">
                <hbox>
                  <image class="content-blocking-warning-image"/>
                  <label class="content-blocking-warning-title" data-l10n-id="content-blocking-warning-title"/>
                </hbox>
                <description class="indent">
                  <html:span class="content-blocking-warning-description" data-l10n-id="content-blocking-and-isolating-etp-warning-description-2"></html:span>
                  <html:a is="moz-support-link"
                          class="learnMore"
                          data-l10n-id="content-blocking-warning-learn-how"
                          support-page="turn-off-etp-desktop"
                  />
                </description>
              </vbox>
            </vbox>
          </vbox>
        </vbox>
        <vbox id="contentBlockingOptionCustom" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="customRadio"
                   value="custom"
                   data-l10n-id="enhanced-tracking-protection-setting-custom"
                   flex="1"/>
            <button id="customArrow"
                    is="highlightable-button"
                    class="arrowhead"
                    data-l10n-id="content-blocking-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <description id="contentBlockingCustomDesc" data-l10n-id="content-blocking-etp-custom-desc"></description>
            <vbox class="privacy-extra-information">
                <hbox class="reject-trackers-ui custom-option">
                  <checkbox id="contentBlockingBlockCookiesCheckbox"
                            class="content-blocking-checkbox" flex="1"
                            data-l10n-id="content-blocking-cookies-label"
                            aria-describedby="contentBlockingCustomDesc"
                            preference="network.cookie.cookieBehavior"/>
                  <vbox>
                    <menulist id="blockCookiesMenu"
                              sizetopopup="none"
                              preference="network.cookie.cookieBehavior">
                      <menupopup>
                        <menuitem id="blockCookiesSocialMedia" data-l10n-id="sitedata-option-block-cross-site-trackers" value="trackers"/>
                        <menuitem id="isolateCookiesSocialMedia" data-l10n-id="sitedata-option-block-cross-site-cookies" value="trackers-plus-isolate"/>
                        <menuitem data-l10n-id="sitedata-option-block-unvisited" value="unvisited"/>
                        <menuitem data-l10n-id="sitedata-option-block-all-cross-site-cookies" value="all-third-parties"/>
                        <menuitem data-l10n-id="sitedata-option-block-all" value="always"/>
                      </menupopup>
                    </menulist>
                  </vbox>
                </hbox>
                <hbox id="contentBlockingTrackingProtectionExtensionContentLabel"
                      align="center" hidden="true" class="extension-controlled">
                  <description control="contentBlockingDisableTrackingProtectionExtension" flex="1"/>
                  <button id="contentBlockingDisableTrackingProtectionExtension"
                          is="highlightable-button"
                          class="extension-controlled-button accessory-button"
                          data-l10n-id="disable-extension" hidden="true"/>
                </hbox>
                  <hbox class="custom-option">
                    <checkbox id="contentBlockingTrackingProtectionCheckbox"
                              class="content-blocking-checkbox" flex="1"
                              data-l10n-id="content-blocking-tracking-content-label"
                              aria-describedby="contentBlockingCustomDesc"/>
                    <vbox>
                      <menulist id="trackingProtectionMenu">
                        <menupopup>
                          <menuitem data-l10n-id="content-blocking-option-private" value="private"/>
                          <menuitem data-l10n-id="content-blocking-tracking-protection-option-all-windows" value="always"/>
                        </menupopup>
                      </menulist>
                    </vbox>
                  </hbox>
                  <label id="changeBlockListLink"
                         data-l10n-id="content-blocking-tracking-protection-change-block-list"
                         is="text-link"
                         search-l10n-ids="blocklist-window2.title, blocklist-description, blocklist-dialog.buttonlabelaccept"/>

                <hbox class="custom-option" id="contentBlockingCryptominersOption">
                  <checkbox id="contentBlockingCryptominersCheckbox"
                            class="content-blocking-checkbox" flex="1"
                            preference="privacy.trackingprotection.cryptomining.enabled"
                            data-l10n-id="content-blocking-cryptominers-label"
                            aria-describedby="contentBlockingCustomDesc"/>
                </hbox>
                <hbox class="custom-option" id="contentBlockingFingerprintersOption">
                  <checkbox id="contentBlockingFingerprintersCheckbox"
                            class="content-blocking-checkbox" flex="1"
                            preference="privacy.trackingprotection.fingerprinting.enabled"
                            data-l10n-id="content-blocking-known-fingerprinters-label"
                            aria-describedby="contentBlockingCustomDesc"/>
                </hbox>
                <hbox class="custom-option">
                    <checkbox id="contentBlockingFingerprintingProtectionCheckbox"
                              class="content-blocking-checkbox" flex="1"
                              data-l10n-id="content-blocking-suspected-fingerprinters-label"
                              aria-describedby="contentBlockingCustomDesc"/>
                    <vbox>
                      <menulist id="fingerprintingProtectionMenu">
                        <menupopup>
                          <menuitem data-l10n-id="content-blocking-option-private" value="private"/>
                          <menuitem data-l10n-id="content-blocking-tracking-protection-option-all-windows" value="always"/>
                        </menupopup>
                      </menulist>
                    </vbox>
                  </hbox>
              <html:div class="content-blocking-warning info-box-container reload-tabs" hidden="true">
                <html:div class="content-blocking-reload-desc-container">
                  <html:div class="info-icon-container">
                    <html:img class="info-icon"/>
                  </html:div>
                  <html:span data-l10n-id="content-blocking-reload-description"
                             class="content-blocking-reload-description" />
                </html:div>
                <button class="accessory-button reload-tabs-button primary"
                        is="highlightable-button"
                        data-l10n-id="content-blocking-reload-tabs-button"/>
              </html:div>
              <vbox class="content-blocking-warning info-box-container">
                <hbox>
                  <image class="content-blocking-warning-image"/>
                  <label class="content-blocking-warning-title" data-l10n-id="content-blocking-warning-title"/>
                </hbox>
                <description class="indent">
                  <html:span class="content-blocking-warning-description" data-l10n-id="content-blocking-and-isolating-etp-warning-description-2"></html:span>
                  <html:a is="moz-support-link"
                          class="learnMore"
                          data-l10n-id="content-blocking-warning-learn-how"
                          support-page="turn-off-etp-desktop"
                  />
                </description>
              </vbox>
            </vbox>
          </vbox>
        </vbox>
      </radiogroup>
    </vbox>
  </vbox>
</groupbox>
<groupbox id="nonTechnicalPrivacyGroup" data-category="panePrivacy" hidden="true">
  <label id="nonTechnicalPrivacyHeader"><html:h2 data-l10n-id="non-technical-privacy-header"/></label>
  <vbox id="nonTechnicalPrivacyBox">
    <hbox id="globalPrivacyControlBox" flex="1" align="center">
      <checkbox id="globalPrivacyControlCheckbox"
              class="tail-with-learn-more"
              preference="privacy.globalprivacycontrol.enabled"
              data-l10n-id="global-privacy-control-description"
              search-l10n-ids="global-privacy-control-search" />
      <html:a is="moz-support-link"
          id="globalPrivacyControlLearnMoreLink"
          support-page="global-privacy-control" />
    </hbox>
    <hbox id="doNotTrackBox" flex="1" align="center" hidden="true">
      <html:a is="moz-support-link"
          id="doNotTrackRemoval"
          support-page="how-do-i-turn-do-not-track-feature"
          data-l10n-id="do-not-track-removal" />
    </hbox>
  </vbox>
</groupbox>

<!-- Site Data -->
<groupbox id="siteDataGroup" data-category="panePrivacy" hidden="true" aria-describedby="totalSiteDataSize">
  <label><html:h2 data-l10n-id="sitedata-header"/></label>

  <hbox data-subcategory="sitedata" align="baseline">
    <vbox flex="1">
      <description class="description-with-side-element description-deemphasized" flex="1">
        <html:span id="totalSiteDataSize"></html:span>
        <html:a is="moz-support-link"
                id="siteDataLearnMoreLink"
                data-l10n-id="sitedata-learn-more"
                support-page="storage-permissions"
        />
      </description>
      <hbox flex="1" id="deleteOnCloseNote" class="info-box-container smaller-font-size">
        <hbox class="info-icon-container">
          <html:img class="info-icon"></html:img>
        </hbox>
        <description flex="1" data-l10n-id="sitedata-delete-on-close-private-browsing2" />
      </hbox>
      <hbox id="keepRow"
            align="center">
        <checkbox id="deleteOnClose"
                  data-l10n-id="sitedata-delete-on-close"
                  flex="1" />
      </hbox>
    </vbox>
    <vbox align="end">
      <button id="clearSiteDataButton"
          is="highlightable-button"
          class="accessory-button"
          search-l10n-ids="clear-site-data-cookies-empty.label, clear-site-data-cache-empty.label"
          data-l10n-id="sitedata-clear"/>
      <button id="siteDataSettings"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="sitedata-settings"
              search-l10n-ids="
                site-data-settings-window.title,
                site-data-column-host.label,
                site-data-column-cookies.label,
                site-data-column-storage.label,
                site-data-settings-description,
                site-data-remove-all.label,
              "/>
      <button id="cookieExceptions"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="sitedata-cookies-exceptions"
              preference="pref.privacy.disable_button.cookie_exceptions"
              search-l10n-ids="
                permissions-address,
                permissions-block.label,
                permissions-allow.label,
                permissions-remove.label,
                permissions-remove-all.label,
                permissions-exceptions-cookie-desc,
              " />
    </vbox>
  </hbox>
</groupbox>

<!-- Cookie Banner Handling -->
<groupbox id="cookieBannerHandlingGroup" data-category="panePrivacy" data-subcategory="cookiebanner" hidden="true">
  <label><html:h2 data-l10n-id="cookie-banner-blocker-header" /></label>
  <vbox flex="1">
    <hbox>
      <description class="description-deemphasized">
        <html:span id="cookieBannerReductionExplanation" data-l10n-id="cookie-banner-blocker-description" ></html:span>
        <html:a is="moz-support-link" id="cookieBannerHandlingLearnMore" class="learnMore"  data-l10n-id="cookie-banner-learn-more" support-page="cookie-banner-reduction"/>
      </description>
    </hbox>
    <hbox>
      <checkbox id="handleCookieBanners"
                preference="cookiebanners.service.mode.privateBrowsing"
                data-l10n-id="cookie-banner-blocker-checkbox-label"
                flex="1" />
    </hbox>
  </vbox>
</groupbox>

<!-- Passwords -->
<groupbox id="passwordsGroup" orient="vertical" data-category="panePrivacy" data-subcategory="logins" hidden="true">
  <label><html:h2 data-l10n-id="pane-privacy-passwords-header" data-l10n-attrs="searchkeywords"/></label>

  <vbox id="passwordSettings">
    <hbox id="passwordManagerExtensionContent"
          class="extension-controlled"
          align="center"
          hidden="true">
          <description control="disablePasswordManagerExtension"
                       flex="1"/>
          <button id="disablePasswordManagerExtension"
                  class="extension-controlled-button accessory-button"
                  data-l10n-id="disable-extension"
                  hidden="true" />
    </hbox>
    <hbox>
      <vbox flex="1">
        <hbox>
          <checkbox id="savePasswords"
                    data-l10n-id="forms-ask-to-save-passwords"
                    preference="signon.rememberSignons"
                    flex="1" />
        </hbox>
        <hbox class="indent" flex="1">
          <checkbox id="passwordAutofillCheckbox"
                    data-l10n-id="forms-fill-usernames-and-passwords"
                    preference="signon.autofillForms"
                    flex="1" />
        </hbox>
        <hbox class="indent" id="generatePasswordsBox" flex="1">
          <checkbox id="generatePasswords"
                    data-l10n-id="forms-suggest-passwords"
                    preference="signon.generation.enabled"
                    flex="1" />
        </hbox>
      </vbox>
      <vbox align="end">
        <button id="passwordExceptions"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="forms-exceptions"
                preference="pref.privacy.disable_button.view_passwords_exceptions"
                search-l10n-ids="
                  permissions-address,
                  permissions-exceptions-saved-passwords-window.title,
                  permissions-exceptions-saved-passwords-desc,
                "/>
        <button id="showPasswords"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="forms-saved-passwords"
                preference="pref.privacy.disable_button.view_passwords"/>
      </vbox>
    </hbox>
    <hbox class="indent" id="relayIntegrationBox" flex="1" align="center">
      <checkbox id="relayIntegration"
                class="tail-with-learn-more"
                data-l10n-id="preferences-relay-integration-checkbox2"/>
      <html:a is="moz-support-link"
              id="relayIntegrationLearnMoreLink"
              class="learnMore"
              data-l10n-id="relay-integration-learn-more-link"/>
    </hbox>
    <hbox class="indent" id="breachAlertsBox" flex="1" align="center">
      <checkbox id="breachAlerts"
                class="tail-with-learn-more"
                data-l10n-id="forms-breach-alerts"
                preference="signon.management.page.breach-alerts.enabled"/>
      <html:a is="moz-support-link"
              id="breachAlertsLearnMoreLink"
              data-l10n-id="forms-breach-alerts-learn-more-link"
              support-page="lockwise-alerts"
      />
    </hbox>
  </vbox>
  <vbox>
    <hbox id="osReauthRow" align="center">
      <checkbox id="osReauthCheckbox"
                data-l10n-id="forms-os-reauth"/>
    </hbox>
  </vbox>
  <vbox>
    <hbox id="masterPasswordRow" align="center">
      <checkbox id="useMasterPassword"
                data-l10n-id="forms-primary-pw-use"
                class="tail-with-learn-more"/>
      <html:a is="moz-support-link"
              id="primaryPasswordLearnMoreLink"
              data-l10n-id="forms-primary-pw-learn-more-link"
              support-page="primary-password-stored-logins"
      />
      <spacer flex="1"/>
      <button id="changeMasterPassword"
              is="highlightable-button"
              class="accessory-button"
              search-l10n-ids="forms-master-pw-change.label"
              data-l10n-id="forms-primary-pw-change"/>
    </hbox>
    <description class="indent tip-caption"
                 data-l10n-id="forms-primary-pw-former-name"
                 data-l10n-attrs="hidden"
                 flex="1"/>
#ifdef XP_WIN
    <hbox id="windows-sso" align="center">
      <checkbox data-l10n-id="forms-windows-sso"
                preference="network.http.windows-sso.enabled"
                class="tail-with-learn-more"/>
      <html:a is="moz-support-link"
              id="windowsSSOLearnMoreLink"
              data-l10n-id="forms-windows-sso-learn-more-link"
              support-page="windows-sso"
      />
    </hbox>
    <description id="windows-sso-caption" class="indent tip-caption"
           data-l10n-id="forms-windows-sso-desc"/>
#endif
  </vbox>

  <label id="openWindowsPasskeySettings">
    <html:a class="text-link" data-l10n-id="windows-passkey-settings-label" href="ms-settings:savedpasskeys" />
  </label>
  <!--
    Those two strings are meant to be invisible and will be used exclusively to provide
    localization for an alert window.
  -->
  <label id="fips-title" hidden="true" data-l10n-id="forms-primary-pw-fips-title"></label>
  <label id="fips-desc" hidden="true" data-l10n-id="forms-master-pw-fips-desc"></label>
</groupbox>

<!-- The form autofill section is inserted in to this box
     after the form autofill extension has initialized. -->
<groupbox id="formAutofillGroupBox"
          data-category="panePrivacy"
          data-subcategory="form-autofill" hidden="true"></groupbox>

<!-- History -->
<groupbox id="historyGroup" data-category="panePrivacy" hidden="true">
  <label><html:h2 data-l10n-id="history-header"/></label>
  <hbox align="center">
    <label id="historyModeLabel"
           control="historyMode"
           data-l10n-id="history-remember-label"/>
    <menulist id="historyMode">
      <menupopup>
        <menuitem data-l10n-id="history-remember-option-all"
                  value="remember"
                  search-l10n-ids="history-remember-description"/>
        <menuitem data-l10n-id="history-remember-option-never"
                  value="dontremember"
                  search-l10n-ids="history-dontremember-description"/>
        <menuitem data-l10n-id="history-remember-option-custom"
                  value="custom"
                  search-l10n-ids="
                    history-private-browsing-permanent.label,
                    history-remember-browser-option.label,
                    history-remember-search-option.label,
                    history-clear-on-close-option.label,
                    history-clear-on-close-settings.label"/>
      </menupopup>
    </menulist>
  </hbox>
  <hbox>
    <deck id="historyPane" flex="1">
      <vbox id="historyRememberPane">
        <hbox align="center" flex="1">
          <vbox flex="1">
            <description
              class="description-with-side-element"
              data-l10n-id="history-remember-description"/>
          </vbox>
        </hbox>
      </vbox>
      <vbox id="historyDontRememberPane">
        <hbox align="center" flex="1">
          <vbox flex="1">
            <description
              class="description-with-side-element"
              data-l10n-id="history-dontremember-description"/>
          </vbox>
        </hbox>
      </vbox>
      <vbox id="historyCustomPane">
        <vbox>
          <checkbox id="privateBrowsingAutoStart"
                    data-l10n-id="history-private-browsing-permanent"
                    preference="browser.privatebrowsing.autostart"/>
          <vbox class="indent">
            <checkbox id="rememberHistory"
                      data-l10n-id="history-remember-browser-option"
                      preference="places.history.enabled"/>
            <checkbox id="rememberForms"
                      data-l10n-id="history-remember-search-option"
                      preference="browser.formfill.enable"/>
            <hbox id="clearDataBox"
                  align="center">
              <checkbox id="alwaysClear"
                        preference="privacy.sanitize.sanitizeOnShutdown"
                        data-l10n-id="history-clear-on-close-option"
                        flex="1" />
            </hbox>
          </vbox>
        </vbox>
      </vbox>
    </deck>
    <vbox id="historyButtons" align="end">
      <button id="clearHistoryButton"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="history-clear-button"/>
      <button id="clearDataSettings"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="history-clear-on-close-settings"
              search-l10n-ids="
                clear-data-settings-label,
                history-section-label,
                item-history-and-downloads.label,
                item-cookies.label,
                item-active-logins.label,
                item-cache.label,
                item-form-search-history.label,
                data-section-label,
                item-site-settings.label,
                item-offline-apps.label
              "/>
    </vbox>
  </hbox>
</groupbox>

<hbox id="permissionsCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="permissions-header"/>
</hbox>

<!-- Permissions -->
<groupbox id="permissionsGroup" data-category="panePrivacy" hidden="true" data-subcategory="permissions">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="permissions-header"/></label>

  <!-- The hbox around the buttons is to compute the search tooltip position properly -->
  <vbox>
    <hbox id="locationSettingsRow" align="center" role="group" aria-labelledby="locationPermissionsLabel">
      <hbox flex="1">
        <image class="geo-icon permission-icon" />
        <label id="locationPermissionsLabel" data-l10n-id="permissions-location"/>
      </hbox>
      <hbox pack="end">
        <button id="locationSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-location-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-location-window2.title,
                  permissions-site-location-desc,
                  permissions-site-location-disable-label,
                  permissions-site-location-disable-desc,
                " />
      </hbox>
    </hbox>

    <hbox id="cameraSettingsRow" align="center" role="group" aria-labelledby="cameraPermissionsLabel">
      <hbox flex="1">
        <image class="camera-icon permission-icon" />
        <label id="cameraPermissionsLabel" data-l10n-id="permissions-camera"/>
      </hbox>
      <hbox pack="end">
        <button id="cameraSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-camera-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-camera-window2.title,
                  permissions-site-camera-desc,
                  permissions-site-camera-disable-label,
                  permissions-site-camera-disable-desc,
                " />
      </hbox>
    </hbox>

    <hbox id="microphoneSettingsRow" align="center" role="group" aria-labelledby="microphonePermissionsLabel">
      <hbox flex="1">
        <image class="microphone-icon permission-icon" />
        <label id="microphonePermissionsLabel" data-l10n-id="permissions-microphone"/>
      </hbox>
      <hbox pack="end">
        <button id="microphoneSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-microphone-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-microphone-window2.title,
                  permissions-site-microphone-desc,
                  permissions-site-microphone-disable-label,
                  permissions-site-microphone-disable-desc,
                " />
      </hbox>
    </hbox>

    <hbox id="speakerSettingsRow" align="center" role="group" aria-labelledby="speakerPermissionsLabel">
      <hbox flex="1">
        <image class="speaker-icon permission-icon" />
        <label id="speakerPermissionsLabel" data-l10n-id="permissions-speaker"/>
      </hbox>
      <hbox pack="end">
        <button id="speakerSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-speaker-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-speaker-window.title,
                  permissions-site-speaker-desc,
                " />
      </hbox>
    </hbox>

    <hbox id="notificationSettingsRow" align="center" role="group" aria-labelledby="notificationPermissionsLabel">
      <hbox flex="1">
        <image class="desktop-notification-icon permission-icon" />
        <label id="notificationPermissionsLabel"
               class="tail-with-learn-more"
               data-l10n-id="permissions-notification"/>
        <html:a is="moz-support-link"
                id="notificationPermissionsLearnMore"
                class="learnMore"
                data-l10n-id="permissions-notification-link"
                support-page="push"
        />
      </hbox>
      <hbox pack="end">
        <button id="notificationSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-notification-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-notification-window2.title,
                  permissions-site-notification-desc,
                  permissions-site-notification-disable-label,
                  permissions-site-notification-disable-desc,
                " />
      </hbox>
    </hbox>

    <vbox id="notificationsDoNotDisturbBox" hidden="true">
      <checkbox id="notificationsDoNotDisturb" class="indent"/>
    </vbox>

    <hbox id="autoplaySettingsRow" align="center" role="group" aria-labelledby="autoplayPermissionsLabel">
      <hbox flex="1">
        <image class="autoplay-icon permission-icon" />
        <label id="autoplayPermissionsLabel"
               data-l10n-id="permissions-autoplay"/>
      </hbox>
      <hbox pack="end">
        <button id="autoplaySettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-autoplay-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-autoplay-window2.title,
                  permissions-site-autoplay-desc,
                " />
      </hbox>
    </hbox>

    <hbox id="xrSettingsRow" align="center" role="group" aria-labelledby="xrPermissionsLabel">
      <hbox flex="1">
        <image class="xr-icon permission-icon" />
        <label id="xrPermissionsLabel" data-l10n-id="permissions-xr"/>
      </hbox>
      <hbox pack="end">
        <button id="xrSettingsButton"
                is="highlightable-button"
                class="accessory-button"
                data-l10n-id="permissions-xr-settings"
                search-l10n-ids="
                  permissions-remove.label,
                  permissions-remove-all.label,
                  permissions-site-xr-window2.title,
                  permissions-site-xr-desc,
                  permissions-site-xr-disable-label,
                  permissions-site-xr-disable-desc,
                " />
      </hbox>
    </hbox>
  </vbox>

  <separator />

  <hbox data-subcategory="permissions-block-popups">
    <checkbox id="popupPolicy" preference="dom.disable_open_during_load"
              data-l10n-id="permissions-block-popups"
              flex="1" />
    <button id="popupPolicyButton"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="permissions-block-popups-exceptions-button"
            data-l10n-attrs="searchkeywords"
            search-l10n-ids="
              permissions-address,
              permissions-exceptions-popup-window2.title,
              permissions-exceptions-popup-desc,
            " />
  </hbox>

  <hbox id="addonInstallBox">
    <checkbox id="warnAddonInstall"
              data-l10n-id="permissions-addon-install-warning"
              preference="xpinstall.whitelist.required"
              flex="1" />
    <button id="addonExceptions"
            is="highlightable-button"
            class="accessory-button"
            data-l10n-id="permissions-addon-exceptions"
            search-l10n-ids="
              permissions-address,
              permissions-allow.label,
              permissions-remove.label,
              permissions-remove-all.label,
              permissions-exceptions-addons-window2.title,
              permissions-exceptions-addons-desc,
            " />
  </hbox>

</groupbox>

<!-- Firefox Data Collection and Use -->
<hbox id="dataCollectionCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="collection-header2" data-l10n-attrs="searchkeywords"/>
</hbox>

<groupbox id="dataCollectionGroup" data-category="panePrivacy" hidden="true">
  <label class="search-header" hidden="true"><html:h2 data-l10n-id="collection-header2" data-l10n-attrs="searchkeywords"/></label>

  <description class="description-deemphasized">
    <html:span data-l10n-id="collection-description"/>
    <html:a id="dataCollectionPrivacyNotice"
            class="learnMore"
            target="_blank"
            data-l10n-id="collection-privacy-notice"/>
  </description>
#ifdef MOZ_DATA_REPORTING
  <hbox id="telemetry-container" class="info-box-container smaller-font-size" hidden="true">
    <hbox class="info-icon-container">
      <html:img class="info-icon"></html:img>
    </hbox>
    <description>
      <html:span id="telemetryDisabledDescription"
                 data-l10n-id="collection-health-report-telemetry-disabled"/>
      <html:a is="moz-support-link"
              support-page="telemetry-clientid"
              class="learnMore"
              data-l10n-id="collection-health-report-telemetry-disabled-link"/>
    </description>
  </hbox>
  <vbox data-subcategory="reports">
    <hbox>
      <checkbox id="submitHealthReportBox"
                data-l10n-id="collection-health-report"
                class="tail-with-learn-more"/>
      <html:a id="FHRLearnMore"
              class="learnMore"
              target="_blank"
              data-l10n-id="collection-health-report-link"/>
    </hbox>
    <hbox class="indent">
      <checkbox id="addonRecommendationEnabled"
                class="tail-with-learn-more"
                data-l10n-id="addon-recommendations"/>
      <html:a is="moz-support-link"
              id="addonRecommendationLearnMore"
              class="learnMore"
              data-l10n-id="addon-recommendations-link"
              support-page="personalized-addons"/>
    </hbox>
#ifndef MOZ_TELEMETRY_REPORTING
  <description id="TelemetryDisabledDesc"
    class="indent tip-caption" control="telemetryGroup"
    data-l10n-id="collection-health-report-disabled"/>
#endif

#ifdef MOZ_NORMANDY
    <hbox align="center" class="indent">
      <checkbox id="optOutStudiesEnabled"
                class="tail-with-learn-more"
                data-l10n-id="collection-studies"/>
      <html:a id="viewShieldStudies"
              href="about:studies"
              class="learnMore"
              target="_blank"
              data-l10n-id="collection-studies-link"/>
    </hbox>
#endif

#ifdef MOZ_CRASHREPORTER
    <hbox align="center" class="checkbox-row">
      <html:input type="checkbox"
                id="automaticallySubmitCrashesBox"
                preference="browser.crashReports.unsubmittedCheck.autoSubmit2"/>
      <html:label is="moz-label"
                for="automaticallySubmitCrashesBox"
                id="crashReporterLabel"
                data-l10n-id="collection-backlogged-crash-reports"
                data-l10n-attrs="accesskey"/>
      <html:a id="crashReporterLearnMore" is="moz-support-link" class="learnMore"/>
    </hbox>
#endif
  </vbox>
#endif
  <vbox class="firefoxSuggestOptionBox" id="firefoxSuggestPrivacyContainer" hidden="true">
    <html:moz-toggle id="firefoxSuggestDataCollectionPrivacyToggle"
                     preference="browser.urlbar.quicksuggest.dataCollection.enabled"
                     data-l10n-id="addressbar-firefox-suggest-data-collection"
                     data-l10n-attrs="label, description"
                     support-page="firefox-suggest">
    </html:moz-toggle>
  </vbox>
  <vbox id="privacySegmentationSection" data-subcategory="privacy-segmentation" hidden="true">
    <label>
      <html:h2 data-l10n-id="privacy-segmentation-section-header"/>
    </label>
    <label data-l10n-id="privacy-segmentation-section-description"/>
    <radiogroup id="privacyDataFeatureRecommendationRadioGroup" preference="browser.dataFeatureRecommendations.enabled">
        <radio id="privacyDataFeatureRecommendationEnabled"
               data-l10n-id="privacy-segmentation-radio-off"
               value="true"/>
        <radio id="privacyDataFeatureRecommendationDisabled"
               data-l10n-id="privacy-segmentation-radio-on"
               value="false"/>
    </radiogroup>
  </vbox>
</groupbox>

#ifdef MOZ_DATA_REPORTING
<!-- Website Advertising Preferences -->
<hbox id="websiteAdvertisingCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="website-advertising-header"/>
</hbox>

<groupbox id="websiteAdvertisingGroup" data-category="panePrivacy" hidden="true">
  <label class="search-header" searchkeywords="ppa" hidden="true"><html:h2 data-l10n-id="website-advertising-header"/></label>
  <checkbox id="privateAttribution"
            preference="dom.private-attribution.submission.enabled"
            data-l10n-id="website-advertising-private-attribution"/>
  <description class="indent tip-caption">
    <html:span data-l10n-id="website-advertising-private-attribution-description"/>
    <html:a is="moz-support-link"
            class="learnMore"
            support-page="privacy-preserving-attribution"/>
  </description>
</groupbox>
#endif

<hbox id="securityCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="security-header"/>
</hbox>

<!-- addons, forgery (phishing) UI Security -->
<groupbox id="browsingProtectionGroup" data-category="panePrivacy" hidden="true">
  <label><html:h2 data-l10n-id="security-browsing-protection"/></label>
  <hbox align = "center">
    <checkbox id="enableSafeBrowsing"
              data-l10n-id="security-enable-safe-browsing"
              class="tail-with-learn-more"/>
    <html:a is="moz-support-link"
            id="enableSafeBrowsingLearnMore"
            class="learnMore"
            data-l10n-id="security-enable-safe-browsing-link"
            support-page="phishing-malware"
    />
  </hbox>
  <vbox class="indent">
    <checkbox id="blockDownloads"
              data-l10n-id="security-block-downloads"/>
    <checkbox id="blockUncommonUnwanted"
              data-l10n-id="security-block-uncommon-software"/>
  </vbox>
</groupbox>

<!-- Certificates -->
<groupbox id="certSelection" data-category="panePrivacy" hidden="true">
  <label><html:h2 data-l10n-id="certs-header"/></label>
  <hbox align="start">
    <checkbox id="enableOCSP"
              data-l10n-id="certs-enable-ocsp"
              preference="security.OCSP.enabled"
              flex="1" />
    <vbox align="end">
      <button id="viewCertificatesButton"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="certs-view"
              preference="security.disable_button.openCertManager"
              search-l10n-ids="
                certmgr-tab-mine.label,
                certmgr-tab-people.label,
                certmgr-tab-servers.label,
                certmgr-tab-ca.label,
                certmgr-mine,
                certmgr-people,
                certmgr-server,
                certmgr-ca,
                certmgr-cert-name.label,
                certmgr-token-name.label,
                certmgr-view.label,
                certmgr-export.label,
                certmgr-delete.label
              "/>
      <button id="viewSecurityDevicesButton"
              is="highlightable-button"
              class="accessory-button"
              data-l10n-id="certs-devices"
              preference="security.disable_button.openDeviceManager"
              search-l10n-ids="
                devmgr-window.title,
                devmgr-devlist.label,
                devmgr-header-details.label,
                devmgr-header-value.label,
                devmgr-button-login.label,
                devmgr-button-logout.label,
                devmgr-button-changepw.label,
                devmgr-button-load.label,
                devmgr-button-unload.label
              "/>
    </vbox>
  </hbox>

  <hbox id="certEnableThirdPartyToggleBox" align="center">
    <checkbox id="certEnableThirdPartyToggle"
              data-l10n-id="certs-thirdparty-toggle"
              preference="security.enterprise_roots.enabled"
              class="tail-with-learn-more"
    />
    <html:a is="moz-support-link"
            class="learnMore"
            support-page="automatically-trust-third-party-certificates"
    />
  </hbox>
</groupbox>

<!-- HTTPS-ONLY Mode -->
<groupbox id="httpsOnlyBox" data-category="panePrivacy" hidden="true">
  <label><html:h2 data-l10n-id="httpsonly-header"/></label>
  <vbox data-subcategory="httpsonly" flex="1">
    <description id="httpsOnlyDescription" class="description-deemphasized" data-l10n-id="httpsonly-description2"/>
    <html:a is="moz-support-link"
            id="httpsOnlyLearnMore"
            class="learnMore"
            data-l10n-id="httpsonly-learn-more"
            support-page="https-only-prefs"
    />
  </vbox>
  <hbox>
    <radiogroup flex="1" id="httpsOnlyRadioGroup">
      <radio id="httpsOnlyRadioEnabled"
              data-l10n-id="httpsonly-radio-enabled2"
              value="enabled"/>
      <radio id="httpsOnlyRadioEnabledPBM"
              data-l10n-id="httpsonly-radio-enabled-pbm2"
              value="privateOnly"/>
      <radio id="httpsOnlyRadioDisabled"
              data-l10n-id="httpsonly-radio-disabled2"
              value="disabled"/>
    </radiogroup>
    <vbox>
      <button id="httpsOnlyExceptionButton" is="highlightable-button" class="accessory-button" disabled="true"
        data-l10n-id="sitedata-cookies-exceptions" search-l10n-ids="
                      permissions-address,
                      permissions-allow.label,
                      permissions-remove.label,
                      permissions-remove-all.label,
                      permissions-exceptions-https-only-desc2,
                    " />
    </vbox>
  </hbox>
</groupbox>

<!-- DoH -->
<hbox id="DoHCategory"
      class="subcategory"
      hidden="true"
      data-category="panePrivacy">
  <html:h1 data-l10n-id="preferences-doh-header"/>
</hbox>

<groupbox id="dohBox" data-category="panePrivacy" data-subcategory="doh" hidden="true" class="highlighting-group">
  <label class="search-header" searchkeywords="doh trr" hidden="true"><html:h2 data-l10n-id="preferences-doh-header"/></label>
  <vbox flex="1">
    <description id="dohDescription" class="tail-with-learn-more description-deemphasized" data-l10n-id="preferences-doh-description2"></description>
    <html:a is="moz-support-link"
            id="dohLearnMore"
            class="learnMore"
            support-page="dns-over-https"
    />
  </vbox>
  <vbox id="dohStatusSection" class="privacy-detailedoption info-box-container">
    <hbox>
      <label id="dohStatus" class="doh-status-label tail-with-learn-more"/>
      <html:a is="moz-support-link"
            id="dohStatusLearnMore"
            class="learnMore"
            support-page="doh-status"
            hidden="true"/>
    </hbox>
    <label class="doh-status-label" id="dohResolver"/>
    <label class="doh-status-label" id="dohSteeringStatus" data-l10n-id="preferences-doh-steering-status" hidden="true"/>
  </vbox>
  <button id="dohExceptionsButton"
          is="highlightable-button"
          class="accessory-button"
          data-l10n-id="preferences-doh-manage-exceptions"
          search-l10n-ids="
            permissions-doh-entry-field,
            permissions-doh-add-exception.label,
            permissions-doh-remove.label,
            permissions-doh-remove-all.label,
            permissions-exceptions-doh-window.title,
            permissions-exceptions-manage-doh-desc,
          "/>
  <vbox>
    <label><html:h2 id="dohGroupMessage" data-l10n-id="preferences-doh-group-message2"/></label>
    <vbox id="dohCategories">
      <radiogroup id="dohCategoryRadioGroup"
                  preference="network.trr.mode" aria-labelledby="dohGroupMessage">
        <vbox id="dohOptionDefault" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="dohDefaultRadio"
                   value="0"
                   data-l10n-id="preferences-doh-setting-default"
                   flex="1"/>
            <button id="dohDefaultArrow"
                    is="highlightable-button"
                    class="arrowhead doh-expand-section"
                    data-l10n-id="preferences-doh-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <label data-l10n-id="preferences-doh-default-desc"></label>
            <vbox class="privacy-extra-information">
              <vbox class="indent">
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-default-detailed-desc-1"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-default-detailed-desc-2"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label tail-with-learn-more" data-l10n-id="preferences-doh-default-detailed-desc-3"/>
                  <html:a is="moz-support-link"
                          id="default-desc-3-learn-more"
                          class="learnMore"
                          support-page="doh-local-provider"
                  />
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-default-detailed-desc-4"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label tail-with-learn-more" data-l10n-id="preferences-doh-default-detailed-desc-5"/>
                  <html:a is="moz-support-link"
                          id="default-desc-5-learn-more"
                          class="learnMore"
                          support-page="firefox-turn-off-secure-dns"
                  />
                </hbox>
              </vbox>
              <hbox id="dohWarningBox1" class="extra-information-label" hidden="true">
                <checkbox id="dohWarnCheckbox1"
                          flex="1"
                          data-l10n-id="preferences-doh-checkbox-warn"
                          preference="network.trr.display_fallback_warning"/>
              </hbox>
            </vbox>
          </vbox>
        </vbox>
        <vbox id="dohOptionEnabled" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="dohEnabledRadio"
                   value="2"
                   data-l10n-id="preferences-doh-setting-enabled"
                   flex="1"/>
            <button id="dohEnabledArrow"
                    is="highlightable-button"
                    class="arrowhead doh-expand-section"
                    data-l10n-id="preferences-doh-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <label data-l10n-id="preferences-doh-enabled-desc"></label>
            <vbox class="privacy-extra-information">
              <vbox class="indent">
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-enabled-detailed-desc-1"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-enabled-detailed-desc-2"/>
                </hbox>
              </vbox>
              <hbox id="dohWarningBox2" class="extra-information-label" hidden="true">
                <checkbox id="dohWarnCheckbox2"
                          flex="1"
                          data-l10n-id="preferences-doh-checkbox-warn"
                          preference="network.trr.display_fallback_warning"/>
              </hbox>
              <vbox class="extra-information-label">
                <label data-l10n-id="preferences-doh-select-resolver"/>
                <menulist id="dohEnabledResolverChoices"
                          sizetopopup="none">
                </menulist>
                <html:input id="dohEnabledInputField" type="text" style="flex: 1;"
                    preference="network.trr.custom_uri" hidden="true"/>
              </vbox>
            </vbox>
          </vbox>
        </vbox>
        <vbox id="dohOptionStrict" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="dohStrictRadio"
                   value="3"
                   data-l10n-id="preferences-doh-setting-strict"
                   flex="1"/>
            <button id="dohStrictArrow"
                    is="highlightable-button"
                    class="arrowhead doh-expand-section"
                    data-l10n-id="preferences-doh-expand-section"
                    aria-expanded="false"/>
          </hbox>
          <vbox class="indent">
            <label data-l10n-id="preferences-doh-strict-desc"></label>
            <vbox class="privacy-extra-information">
              <vbox class="indent">
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-strict-detailed-desc-1"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-strict-detailed-desc-2"/>
                </hbox>
                <hbox class="extra-information-label">
                  <label class="doh-label" data-l10n-id="preferences-doh-strict-detailed-desc-3"/>
                </hbox>
              </vbox>
              <vbox class="extra-information-label">
                <label data-l10n-id="preferences-doh-select-resolver"/>
                <menulist id="dohStrictResolverChoices"
                          sizetopopup="none">
                </menulist>
                <html:input id="dohStrictInputField" type="text" style="flex: 1;"
                    preference="network.trr.custom_uri" hidden="true"/>
              </vbox>
            </vbox>
          </vbox>
        </vbox>
        <vbox id="dohOptionOff" class="privacy-detailedoption info-box-container">
          <hbox>
            <radio id="dohOffRadio"
                   value="5"
                   data-l10n-id="preferences-doh-setting-off"
                   flex="1"/>
          </hbox>
          <vbox class="indent">
            <label data-l10n-id="preferences-doh-off-desc"></label>
          </vbox>
        </vbox>
      </radiogroup>
    </vbox>
  </vbox>
</groupbox>

</html:template>
