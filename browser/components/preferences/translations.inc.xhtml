# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

<script src="chrome://browser/content/preferences/translations.js"/>

<div id="translations-settings-page"
     xmlns="http://www.w3.org/1999/xhtml"
     xmlns:xul="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
     data-category="paneTranslations"
     data-hidden-from-search="true"
     data-subpanel="true"
     hidden="true">

  <button id="translations-settings-back-button" class="back-button"
          data-l10n-id="translations-settings-back-button"/>

  <h1 id="translations-settings-header"
      data-l10n-id="translations-settings-header"/>

  <p id="translations-settings-description" data-l10n-id="translations-settings-description"/>

  <moz-card class="translations-settings-manage-section"
       id="translations-settings-always-translate-section">
    <div class="translations-settings-manage-language">
      <h2 id="translations-settings-always-translate" data-l10n-id="translations-settings-always-translate"/>
      <xul:menulist id="translations-settings-always-translate-list"
                    data-l10n-id="translations-settings-add-language-button"
                    aria-labelledby="translations-settings-always-translate">
        <!-- The list of <menuitem> will be dynamically inserted. -->
        <xul:menupopup id="translations-settings-always-translate-popup"/>
      </xul:menulist>
    </div>
    <div id="translations-settings-always-translate-languages-card" class="translations-settings-languages-card" hidden="true">
      <h3 class="translations-settings-language-header" data-l10n-id="translations-settings-language-header"></h3>
      <div id="translations-settings-always-translate-language-list" class="translations-settings-language-list"
        tabindex="0" role="listbox">
      </div>
    </div>
  </moz-card>

  <moz-card id="translations-settings-never-translate-section"
       class="translations-settings-manage-section">
    <div class="translations-settings-manage-language">
      <h2 id="translations-settings-never-translate" data-l10n-id="translations-settings-never-translate"/>
      <xul:menulist id="translations-settings-never-translate-list"
                    data-l10n-id="translations-settings-add-language-button"
                    aria-labelledby="translations-settings-never-translate">
        <!-- The list of <menuitem> will be dynamically inserted. -->
        <xul:menupopup id="translations-settings-never-translate-popup"/>
      </xul:menulist>
    </div>
    <div id="translations-settings-never-translate-languages-card" class="translations-settings-languages-card" hidden="true">
      <h3 class="translations-settings-language-header" data-l10n-id="translations-settings-language-header"></h3>
      <div id="translations-settings-never-translate-language-list" class="translations-settings-language-list"
                tabindex="0" role="listbox">
      </div>
    </div>
  </moz-card>

  <moz-card id="translations-settings-never-sites-section"
            class="translations-settings-manage-section">
    <div class="translations-settings-manage-section-info" >
      <h2 id="translations-settings-never-sites-header"
          data-l10n-id="translations-settings-never-sites-header"/>
      <p id="translations-settings-never-sites"
         data-l10n-id="translations-settings-never-sites-description"/>
    </div>
    <div id="translations-settings-never-translate-site-card" class="translations-settings-languages-card" hidden="true">
      <h3 class="translations-settings-language-header" data-l10n-id="translations-settings-language-header"></h3>
      <div id="translations-settings-never-translate-site-list" class="translations-settings-language-list"
              tabindex="0" role="listbox">
      </div>
    </div>
  </moz-card>

  <moz-card id="translations-settings-download-section"
       class="translations-settings-manage-section">
    <div class="translations-settings-manage-section-info">
      <h2 data-l10n-id="translations-settings-download-languages"/>
      <a is="moz-support-link" class="learnMore"
          id="download-languages-learn-more"
          data-l10n-id="translations-settings-download-languages-link"
          support-page="website-translation"/>
    </div>
    <div class="translations-settings-languages-card">
      <h3 class="translations-settings-language-header" data-l10n-id="translations-settings-language-header"></h3>
      <div id="translations-settings-download-language-list" class="translations-settings-language-list" tabindex="0"
        role="listbox">
        <div id="translations-settings-download-all-languages-id" class="translations-settings-language" role="option">
          <moz-button class="translations-settings-download-icon translations-settings-manage-downloaded-language-button" type="ghost icon"
                      data-l10n-id="translations-settings-download-all-button" tabindex="-1"></moz-button>
          <!--  The option to "All languages" is added here.
                In translations.js the option to download individual languages is
                added dynamically based on the supported language list -->
          <label id="translations-settings-download-all-languages" data-l10n-id="translations-settings-download-all-languages"></label>
        </div>
      </div>
    </div>
  </moz-card>
</div>
