<?xml version="1.0"?>

<!-- -*- Mode: Java; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- -->
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  type="child"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  onload="gSyncChooseWhatToSync.init();"
  data-l10n-id="sync-choose-what-to-sync-dialog4"
  data-l10n-attrs="title, style"
>
  <dialog
    id="syncChooseOptions"
    buttons="accept,cancel,extra2"
    data-l10n-id="sync-choose-what-to-sync-dialog4"
    data-l10n-attrs="buttonlabelaccept, buttonlabelextra2"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />

      <html:link
        rel="localization"
        href="browser/preferences/preferences.ftl"
      />
    </linkset>
    <script src="chrome://global/content/preferencesBindings.js" />
    <script src="chrome://browser/content/preferences/dialogs/syncChooseWhatToSync.js" />
    <html:div class="sync-engines-list">
      <html:div class="sync-engine-bookmarks">
        <checkbox
          data-l10n-id="sync-engine-bookmarks"
          preference="services.sync.engine.bookmarks"
        />
      </html:div>
      <html:div class="sync-engine-history">
        <checkbox
          data-l10n-id="sync-engine-history"
          preference="services.sync.engine.history"
        />
      </html:div>
      <html:div class="sync-engine-tabs">
        <checkbox
          data-l10n-id="sync-engine-tabs"
          preference="services.sync.engine.tabs"
        />
      </html:div>
      <html:div class="sync-engine-passwords">
        <checkbox
          data-l10n-id="sync-engine-passwords"
          preference="services.sync.engine.passwords"
        />
      </html:div>
      <html:div class="sync-engine-addresses">
        <checkbox
          data-l10n-id="sync-engine-addresses"
          preference="services.sync.engine.addresses"
        />
      </html:div>
      <html:div class="sync-engine-creditcards">
        <checkbox
          data-l10n-id="sync-engine-payment-methods2"
          preference="services.sync.engine.creditcards"
        />
      </html:div>
      <html:div class="sync-engine-addons">
        <checkbox
          data-l10n-id="sync-engine-addons"
          preference="services.sync.engine.addons"
        />
      </html:div>
      <html:div class="sync-engine-prefs">
        <checkbox
          data-l10n-id="sync-engine-settings"
          preference="services.sync.engine.prefs"
        />
      </html:div>
    </html:div>
  </dialog>
</window>
