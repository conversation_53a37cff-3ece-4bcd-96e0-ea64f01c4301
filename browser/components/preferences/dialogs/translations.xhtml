<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="TranslationsDialog"
  data-l10n-id="translations-settings-title"
  data-l10n-attrs="title, style"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  onload="gTranslationsSettings.onLoad();"
  onunload="gTranslationsSettings.removeObservers();"
  persist="width height"
>
  <dialog
    buttons="accept"
    data-l10n-id="translations-settings-close-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="browser/translations.ftl" />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/translations.js" />

    <keyset>
      <key
        data-l10n-id="translations-settings-close-key"
        modifiers="accel"
        oncommand="window.close();"
      />
    </keyset>

    <vbox class="contentPane">
      <vbox flex="1">
        <label
          id="alwaysTranslateLanguagesLabel"
          data-l10n-id="translations-settings-always-translate-langs-description"
          control="permissionsTree"
        />
        <separator class="thin" />
        <tree
          id="alwaysTranslateLanguagesTree"
          flex="1"
          style="height: 12em"
          hidecolumnpicker="true"
          onkeypress="gTranslationsSettings.onAlwaysTranslateLanguageKeyPress(event)"
          onselect="gTranslationsSettings.onSelectAlwaysTranslateLanguage();"
        >
          <treecols>
            <treecol
              id="languageCol"
              data-l10n-id="translations-settings-languages-column"
              flex="1"
            />
          </treecols>
          <treechildren />
        </tree>
      </vbox>
      <hbox class="actionButtons" pack="start">
        <button
          id="removeAlwaysTranslateLanguage"
          disabled="true"
          data-l10n-id="translations-settings-remove-language-button"
          oncommand="gTranslationsSettings.onRemoveAlwaysTranslateLanguage();"
        />
        <button
          id="removeAllAlwaysTranslateLanguages"
          data-l10n-id="translations-settings-remove-all-languages-button"
          oncommand="gTranslationsSettings.onRemoveAllAlwaysTranslateLanguages();"
        />
      </hbox>
      <separator />
      <vbox flex="1">
        <label
          id="neverTranslateLanguagesLabel"
          data-l10n-id="translations-settings-never-translate-langs-description"
          control="permissionsTree"
        />
        <separator class="thin" />
        <tree
          id="neverTranslateLanguagesTree"
          flex="1"
          style="height: 12em"
          hidecolumnpicker="true"
          onkeypress="gTranslationsSettings.onNeverTranslateLanguageKeyPress(event)"
          onselect="gTranslationsSettings.onSelectNeverTranslateLanguage();"
        >
          <treecols>
            <treecol
              id="languageCol"
              data-l10n-id="translations-settings-languages-column"
              flex="1"
            />
          </treecols>
          <treechildren />
        </tree>
      </vbox>
      <hbox class="actionButtons" pack="start">
        <button
          id="removeNeverTranslateLanguage"
          disabled="true"
          data-l10n-id="translations-settings-remove-language-button"
          oncommand="gTranslationsSettings.onRemoveNeverTranslateLanguage();"
        />
        <button
          id="removeAllNeverTranslateLanguages"
          data-l10n-id="translations-settings-remove-all-languages-button"
          oncommand="gTranslationsSettings.onRemoveAllNeverTranslateLanguages();"
        />
      </hbox>
      <separator />
      <vbox flex="1">
        <label
          id="neverTranslateSitesLabel"
          data-l10n-id="translations-settings-never-translate-sites-description"
          control="permissionsTree"
        />
        <separator class="thin" />
        <tree
          id="neverTranslateSitesTree"
          flex="1"
          style="height: 12em"
          hidecolumnpicker="true"
          onkeypress="gTranslationsSettings.onNeverTranslateSiteKeyPress(event)"
          onselect="gTranslationsSettings.onSelectNeverTranslateSite();"
        >
          <treecols>
            <treecol
              id="siteCol"
              data-l10n-id="translations-settings-sites-column"
              flex="1"
            />
          </treecols>
          <treechildren />
        </tree>
      </vbox>
      <hbox class="actionButtons" pack="start">
        <button
          id="removeNeverTranslateSite"
          disabled="true"
          data-l10n-id="translations-settings-remove-site-button"
          oncommand="gTranslationsSettings.onRemoveNeverTranslateSite();"
        />
        <button
          id="removeAllNeverTranslateSites"
          data-l10n-id="translations-settings-remove-all-sites-button"
          oncommand="gTranslationsSettings.onRemoveAllNeverTranslateSites();"
        />
      </hbox>
    </vbox>
  </dialog>
</window>
