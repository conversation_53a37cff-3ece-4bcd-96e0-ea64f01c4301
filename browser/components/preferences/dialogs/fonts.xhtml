<?xml version="1.0"?>

<!-- -*- Mode: Java; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- -->
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  type="child"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="fonts-window"
  data-l10n-attrs="title"
  persist="lastSelected"
>
  <dialog
    id="FontsDialog"
    buttons="accept,cancel,help"
    helpTopic="prefs-fonts-and-colors"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="browser/preferences/fonts.ftl" />
    </linkset>

    <script src="chrome://browser/content/utilityOverlay.js" />
    <script src="chrome://global/content/preferencesBindings.js" />

    <keyset>
      <key
        data-l10n-id="fonts-window-close"
        modifiers="accel"
        oncommand="Preferences.close(event)"
      />
    </keyset>

    <!-- Fonts for: [ Language ] -->
    <groupbox>
      <hbox align="center">
        <label control="selectLangs"
          ><html:h2
            class="heading-medium"
            data-l10n-id="fonts-langgroup-header"
        /></label>
      </hbox>
      <menulist id="selectLangs" preference="font.language.group">
        <menupopup>
          <menuitem value="ar" data-l10n-id="fonts-langgroup-arabic" />
          <menuitem value="x-armn" data-l10n-id="fonts-langgroup-armenian" />
          <menuitem value="x-beng" data-l10n-id="fonts-langgroup-bengali" />
          <menuitem
            value="zh-CN"
            data-l10n-id="fonts-langgroup-simpl-chinese"
          />
          <menuitem
            value="zh-HK"
            data-l10n-id="fonts-langgroup-trad-chinese-hk"
          />
          <menuitem value="zh-TW" data-l10n-id="fonts-langgroup-trad-chinese" />
          <menuitem
            value="x-cyrillic"
            data-l10n-id="fonts-langgroup-cyrillic"
          />
          <menuitem
            value="x-devanagari"
            data-l10n-id="fonts-langgroup-devanagari"
          />
          <menuitem value="x-ethi" data-l10n-id="fonts-langgroup-ethiopic" />
          <menuitem value="x-geor" data-l10n-id="fonts-langgroup-georgian" />
          <menuitem value="el" data-l10n-id="fonts-langgroup-el" />
          <menuitem value="x-gujr" data-l10n-id="fonts-langgroup-gujarati" />
          <menuitem value="x-guru" data-l10n-id="fonts-langgroup-gurmukhi" />
          <menuitem value="he" data-l10n-id="fonts-langgroup-hebrew" />
          <menuitem value="ja" data-l10n-id="fonts-langgroup-japanese" />
          <menuitem value="x-knda" data-l10n-id="fonts-langgroup-kannada" />
          <menuitem value="x-khmr" data-l10n-id="fonts-langgroup-khmer" />
          <menuitem value="ko" data-l10n-id="fonts-langgroup-korean" />
          <menuitem value="x-western" data-l10n-id="fonts-langgroup-latin" />
          <menuitem value="x-mlym" data-l10n-id="fonts-langgroup-malayalam" />
          <menuitem value="x-math" data-l10n-id="fonts-langgroup-math" />
          <menuitem value="x-orya" data-l10n-id="fonts-langgroup-odia" />
          <menuitem value="x-sinh" data-l10n-id="fonts-langgroup-sinhala" />
          <menuitem value="x-tamil" data-l10n-id="fonts-langgroup-tamil" />
          <menuitem value="x-telu" data-l10n-id="fonts-langgroup-telugu" />
          <menuitem value="th" data-l10n-id="fonts-langgroup-thai" />
          <menuitem value="x-tibt" data-l10n-id="fonts-langgroup-tibetan" />
          <menuitem value="x-cans" data-l10n-id="fonts-langgroup-canadian" />
          <menuitem value="x-unicode" data-l10n-id="fonts-langgroup-other" />
        </menupopup>
      </menulist>

      <separator class="thin" />

      <box id="font-chooser-group">
        <!-- proportional row -->
        <hbox align="center" pack="end">
          <label
            data-l10n-id="fonts-proportional-header"
            control="defaultFontType"
          />
        </hbox>
        <menulist id="defaultFontType">
          <menupopup>
            <menuitem value="serif" data-l10n-id="fonts-default-serif" />
            <menuitem
              value="sans-serif"
              data-l10n-id="fonts-default-sans-serif"
            />
          </menupopup>
        </menulist>
        <hbox align="center" pack="end">
          <label data-l10n-id="fonts-proportional-size" control="sizeVar" />
        </hbox>
        <menulist id="sizeVar" delayprefsave="true">
          <menupopup>
            <menuitem value="9" label="9" />
            <menuitem value="10" label="10" />
            <menuitem value="11" label="11" />
            <menuitem value="12" label="12" />
            <menuitem value="13" label="13" />
            <menuitem value="14" label="14" />
            <menuitem value="15" label="15" />
            <menuitem value="16" label="16" />
            <menuitem value="17" label="17" />
            <menuitem value="18" label="18" />
            <menuitem value="20" label="20" />
            <menuitem value="22" label="22" />
            <menuitem value="24" label="24" />
            <menuitem value="26" label="26" />
            <menuitem value="28" label="28" />
            <menuitem value="30" label="30" />
            <menuitem value="32" label="32" />
            <menuitem value="34" label="34" />
            <menuitem value="36" label="36" />
            <menuitem value="40" label="40" />
            <menuitem value="44" label="44" />
            <menuitem value="48" label="48" />
            <menuitem value="56" label="56" />
            <menuitem value="64" label="64" />
            <menuitem value="72" label="72" />
          </menupopup>
        </menulist>

        <!-- serif row -->
        <hbox align="center" pack="end">
          <label data-l10n-id="fonts-serif" control="serif" />
        </hbox>
        <menulist id="serif" delayprefsave="true" />
        <spacer />
        <spacer />

        <!-- sans-serif row -->
        <hbox align="center" pack="end">
          <label data-l10n-id="fonts-sans-serif" control="sans-serif" />
        </hbox>
        <menulist id="sans-serif" delayprefsave="true" />
        <spacer />
        <spacer />

        <!-- monospace row -->
        <hbox align="center" pack="end">
          <label data-l10n-id="fonts-monospace" control="monospace" />
        </hbox>
        <!--
          FIXME(emilio): Why is this the only menulist here with crop="end"?
          This goes back to the beginning of time...
      -->
        <menulist id="monospace" crop="end" delayprefsave="true" />
        <hbox align="center" pack="end">
          <label data-l10n-id="fonts-monospace-size" control="sizeMono" />
        </hbox>
        <menulist id="sizeMono" delayprefsave="true">
          <menupopup>
            <menuitem value="9" label="9" />
            <menuitem value="10" label="10" />
            <menuitem value="11" label="11" />
            <menuitem value="12" label="12" />
            <menuitem value="13" label="13" />
            <menuitem value="14" label="14" />
            <menuitem value="15" label="15" />
            <menuitem value="16" label="16" />
            <menuitem value="17" label="17" />
            <menuitem value="18" label="18" />
            <menuitem value="20" label="20" />
            <menuitem value="22" label="22" />
            <menuitem value="24" label="24" />
            <menuitem value="26" label="26" />
            <menuitem value="28" label="28" />
            <menuitem value="30" label="30" />
            <menuitem value="32" label="32" />
            <menuitem value="34" label="34" />
            <menuitem value="36" label="36" />
            <menuitem value="40" label="40" />
            <menuitem value="44" label="44" />
            <menuitem value="48" label="48" />
            <menuitem value="56" label="56" />
            <menuitem value="64" label="64" />
            <menuitem value="72" label="72" />
          </menupopup>
        </menulist>
      </box>
      <separator class="thin" />
      <hbox align="center" pack="end">
        <label data-l10n-id="fonts-minsize" control="minSize" />
        <menulist id="minSize">
          <menupopup>
            <menuitem value="0" data-l10n-id="fonts-minsize-none" />
            <menuitem value="9" label="9" />
            <menuitem value="10" label="10" />
            <menuitem value="11" label="11" />
            <menuitem value="12" label="12" />
            <menuitem value="13" label="13" />
            <menuitem value="14" label="14" />
            <menuitem value="15" label="15" />
            <menuitem value="16" label="16" />
            <menuitem value="17" label="17" />
            <menuitem value="18" label="18" />
            <menuitem value="20" label="20" />
            <menuitem value="22" label="22" />
            <menuitem value="24" label="24" />
            <menuitem value="26" label="26" />
            <menuitem value="28" label="28" />
            <menuitem value="30" label="30" />
            <menuitem value="32" label="32" />
            <menuitem value="34" label="34" />
            <menuitem value="36" label="36" />
            <menuitem value="40" label="40" />
            <menuitem value="44" label="44" />
            <menuitem value="48" label="48" />
            <menuitem value="56" label="56" />
            <menuitem value="64" label="64" />
            <menuitem value="72" label="72" />
          </menupopup>
        </menulist>
      </hbox>
      <separator />
      <separator class="groove" />
      <hbox>
        <checkbox
          id="useDocumentFonts"
          data-l10n-id="fonts-allow-own"
          preference="browser.display.use_document_fonts"
        />
      </hbox>
    </groupbox>

    <!-- Load the script after the elements for layout issues (bug 1501755). -->
    <script src="chrome://mozapps/content/preferences/fontbuilder.js" />
    <script src="chrome://browser/content/preferences/dialogs/fonts.js" />
  </dialog>
</window>
