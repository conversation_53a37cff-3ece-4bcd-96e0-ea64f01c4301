<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  type="child"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="connection-window2"
  data-l10n-attrs="title, style"
  persist="lastSelected"
  onload="gConnectionsDialog.checkForSystemProxy();"
>
  <dialog
    id="ConnectionsDialog"
    buttons="accept,cancel,help"
    helpTopic="prefs-connection-settings"
  >
    <!-- Used for extension-controlled lockdown message -->
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="browser/preferences/connection.ftl" />
      <html:link
        rel="localization"
        href="browser/preferences/preferences.ftl"
      />
      <html:link rel="localization" href="branding/brand.ftl" />
    </linkset>

    <script src="chrome://browser/content/utilityOverlay.js" />
    <script src="chrome://global/content/preferencesBindings.js" />
    <script src="chrome://browser/content/preferences/extensionControlled.js" />

    <keyset>
      <key
        data-l10n-id="connection-close-key"
        modifiers="accel"
        oncommand="Preferences.close(event)"
      />
    </keyset>

    <script src="chrome://browser/content/preferences/dialogs/connection.js" />

    <hbox
      id="proxyExtensionContent"
      align="start"
      hidden="true"
      class="extension-controlled"
    >
      <description control="disableProxyExtension" flex="1" />
      <button
        id="disableProxyExtension"
        class="extension-controlled-button accessory-button"
        data-l10n-id="connection-disable-extension"
      />
    </hbox>

    <groupbox>
      <label
        ><html:h2
          class="heading-medium"
          data-l10n-id="connection-proxy-configure"
      /></label>

      <radiogroup id="networkProxyType" preference="network.proxy.type">
        <radio value="0" data-l10n-id="connection-proxy-option-no" />
        <radio value="4" data-l10n-id="connection-proxy-option-auto" />
        <radio
          value="5"
          data-l10n-id="connection-proxy-option-system"
          id="systemPref"
          hidden="true"
        />
        <checkbox
          value="true"
          data-l10n-id="connection-proxy-option-wpad"
          id="systemWpad"
          hidden="true"
          preference="network.proxy.system_wpad"
          class="indent"
        />
        <radio value="1" data-l10n-id="connection-proxy-option-manual" />
        <box id="proxy-grid" class="indent" flex="1">
          <html:div class="proxy-grid-row">
            <hbox pack="end">
              <label
                data-l10n-id="connection-proxy-http"
                control="networkProxyHTTP"
              />
            </hbox>
            <hbox align="center">
              <html:input
                id="networkProxyHTTP"
                type="text"
                style="flex: 1"
                preference="network.proxy.http"
              />
              <label
                data-l10n-id="connection-proxy-http-port"
                control="networkProxyHTTP_Port"
              />
              <html:input
                id="networkProxyHTTP_Port"
                class="proxy-port-input"
                hidespinbuttons="true"
                type="number"
                min="0"
                max="65535"
                preference="network.proxy.http_port"
              />
            </hbox>
          </html:div>
          <html:div class="proxy-grid-row">
            <hbox />
            <hbox>
              <checkbox
                id="shareAllProxies"
                data-l10n-id="connection-proxy-https-sharing"
                preference="network.proxy.share_proxy_settings"
              />
            </hbox>
          </html:div>
          <html:div class="proxy-grid-row">
            <hbox pack="end">
              <label
                data-l10n-id="connection-proxy-https"
                control="networkProxySSL"
              />
            </hbox>
            <hbox align="center">
              <html:input
                id="networkProxySSL"
                type="text"
                style="flex: 1"
                preference="network.proxy.ssl"
              />
              <label
                data-l10n-id="connection-proxy-ssl-port"
                control="networkProxySSL_Port"
              />
              <html:input
                id="networkProxySSL_Port"
                class="proxy-port-input"
                hidespinbuttons="true"
                type="number"
                min="0"
                max="65535"
                size="5"
                preference="network.proxy.ssl_port"
              />
            </hbox>
          </html:div>
          <separator class="thin" />
          <html:div class="proxy-grid-row">
            <hbox pack="end">
              <label
                data-l10n-id="connection-proxy-socks"
                control="networkProxySOCKS"
              />
            </hbox>
            <hbox align="center">
              <html:input
                id="networkProxySOCKS"
                type="text"
                style="flex: 1"
                preference="network.proxy.socks"
              />
              <label
                data-l10n-id="connection-proxy-socks-port"
                control="networkProxySOCKS_Port"
              />
              <html:input
                id="networkProxySOCKS_Port"
                class="proxy-port-input"
                hidespinbuttons="true"
                type="number"
                min="0"
                max="65535"
                size="5"
                preference="network.proxy.socks_port"
              />
            </hbox>
          </html:div>
          <html:div class="proxy-grid-row">
            <spacer />
            <box pack="start">
              <radiogroup
                id="networkProxySOCKSVersion"
                orient="horizontal"
                preference="network.proxy.socks_version"
              >
                <radio
                  id="networkProxySOCKSVersion4"
                  value="4"
                  data-l10n-id="connection-proxy-socks4"
                />
                <radio
                  id="networkProxySOCKSVersion5"
                  value="5"
                  data-l10n-id="connection-proxy-socks5"
                />
              </radiogroup>
            </box>
          </html:div>
        </box>
        <radio value="2" data-l10n-id="connection-proxy-autotype" />
        <hbox class="indent" flex="1" align="center">
          <html:input
            id="networkProxyAutoconfigURL"
            type="text"
            style="flex: 1"
            preference="network.proxy.autoconfig_url"
            oninput="gConnectionsDialog.updateReloadButton();"
          />
          <button
            id="autoReload"
            data-l10n-id="connection-proxy-reload"
            oncommand="gConnectionsDialog.reloadPAC();"
            preference="pref.advanced.proxies.disable_button.reload"
          />
        </hbox>
      </radiogroup>
    </groupbox>
    <separator class="thin" />
    <label data-l10n-id="connection-proxy-noproxy" control="networkProxyNone" />
    <html:textarea
      id="networkProxyNone"
      preference="network.proxy.no_proxies_on"
      rows="2"
    />
    <label
      control="networkProxyNone"
      data-l10n-id="connection-proxy-noproxy-desc"
    />
    <label
      id="networkProxyNoneLocalhost"
      control="networkProxyNone"
      data-l10n-id="connection-proxy-noproxy-localhost-desc-2"
    />
    <separator class="thin" />
    <checkbox
      id="autologinProxy"
      data-l10n-id="connection-proxy-autologin-checkbox"
      preference="signon.autologin.proxy"
    />
    <checkbox
      id="networkProxySOCKS4RemoteDNS"
      preference="network.proxy.socks_remote_dns"
      data-l10n-id="connection-proxy-socks4-remote-dns"
    />
    <checkbox
      id="networkProxySOCKSRemoteDNS"
      preference="network.proxy.socks5_remote_dns"
      data-l10n-id="connection-proxy-socks-remote-dns"
    />
  </dialog>
</window>
