/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

.website-name,
label.website-status {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-inline-start: 7px;
}

#permissionsBox {
  flex: 1 auto;
  height: 18em;
  min-height: 70px; /* 2 * 35px, which is the min row height specified below */
}

#siteCol,
#statusCol,
#permissionsBox > richlistitem {
  min-height: 35px;
}

#permissionsBox > richlistitem > hbox {
  flex: 1;
  align-items: center;
}

#siteCol,
.website-name {
  flex: 1;
  width: 0; /* Don't make our intrinsic size affect our final size */
}

#statusCol,
.website-status {
  width: 35%;
}

menulist.website-status {
  margin-block: 1px;
  margin-inline: 0 5px;
  width: calc(35% - 5px);
}

#browserNotificationsPermissionExtensionContent,
#permissionsDisableDescription {
  margin-inline-start: 32px;
}

#permissionsDisableDescription {
  color: var(--text-color-deemphasized);
}

#permissionsDisableCheckbox {
  margin-inline-start: 4px;
  padding-top: 10px;
}
