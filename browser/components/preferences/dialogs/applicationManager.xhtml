<?xml version="1.0"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  onload="gAppManagerDialog.onLoad();"
  data-l10n-id="app-manager-window2"
  data-l10n-attrs="title, style"
>
  <dialog id="appManager" buttons="accept,cancel">
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/applications.css"
      />

      <html:link
        rel="localization"
        href="browser/preferences/applicationManager.ftl"
      />
    </linkset>

    <script src="chrome://browser/content/utilityOverlay.js" />
    <script src="chrome://global/content/preferencesBindings.js" />
    <script src="chrome://browser/content/preferences/dialogs/applicationManager.js" />

    <commandset id="appManagerCommandSet">
      <command
        id="cmd_remove"
        oncommand="gAppManagerDialog.remove();"
        disabled="true"
      />
    </commandset>

    <keyset id="appManagerKeyset">
      <key id="delete" keycode="VK_DELETE" command="cmd_remove" />
    </keyset>

    <description id="appDescription" />
    <separator class="thin" />
    <hbox flex="1">
      <richlistbox
        id="appList"
        onselect="gAppManagerDialog.onSelect();"
        flex="1"
      />
      <vbox>
        <button
          id="remove"
          data-l10n-id="app-manager-remove"
          command="cmd_remove"
        />
        <spacer flex="1" />
      </vbox>
    </hbox>
    <vbox id="appDetails">
      <separator class="thin" />
      <label id="appType" />
      <html:input
        type="text"
        id="appLocation"
        readonly="readonly"
        style="margin-inline: 0"
      />
    </vbox>
  </dialog>
</window>
