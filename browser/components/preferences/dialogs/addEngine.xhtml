<?xml version="1.0"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="add-engine-window2"
  data-l10n-attrs="title, style"
  persist="width height"
>
  <dialog
    buttons="accept,cancel"
    buttondisabledaccept="true"
    data-l10n-id="add-engine-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/content/preferences/dialogs/addEngine.css"
      />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="browser/preferences/addEngine.ftl" />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/addEngine.js" />
    <script src="chrome://global/content/globalOverlay.js" />
    <script src="chrome://browser/content/utilityOverlay.js" />

    <separator class="thin" />

    <html:form id="addEngineForm">
      <html:span
        id="engineNameExists"
        hidden="hidden"
        data-l10n-id="engine-name-exists"
      />
      <html:label
        id="engineNameLabel"
        for="engineName"
        data-l10n-id="add-engine-name"
      />
      <hbox>
        <html:input id="engineName" type="text" required="required" />
      </hbox>

      <html:label
        id="engineUrlLabel"
        for="engineUrl"
        data-l10n-id="add-engine-url"
      />
      <hbox>
        <html:input id="engineUrl" type="url" required="required" />
      </hbox>

      <html:span
        id="engineAliasExists"
        hidden="hidden"
        data-l10n-id="engine-alias-exists"
      />
      <html:label
        id="engineAliasLabel"
        for="engineAlias"
        data-l10n-id="add-engine-alias"
      />
      <hbox>
        <html:input id="engineAlias" type="text" />
      </hbox>
    </html:form>
  </dialog>
</window>
