<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  type="child"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="webpage-languages-window2"
  data-l10n-attrs="title, style"
  persist="lastSelected"
  onload="gLanguagesDialog.onLoad();"
>
  <dialog
    id="LanguagesDialog"
    buttons="accept,cancel,help"
    helpTopic="prefs-languages"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="browser/preferences/languages.ftl" />
    </linkset>

    <script src="chrome://browser/content/utilityOverlay.js" />
    <script src="chrome://global/content/preferencesBindings.js" />
    <script src="chrome://browser/content/preferences/dialogs/languages.js" />

    <keyset>
      <key
        data-l10n-id="languages-close-key"
        modifiers="accel"
        oncommand="Preferences.close(event)"
      />
    </keyset>

    <stringbundleset id="languageSet">
      <stringbundle
        id="bundleAccepted"
        src="resource://gre/res/language.properties"
      />
    </stringbundleset>

    <description data-l10n-id="languages-description" />
    <checkbox
      id="spoofEnglish"
      data-l10n-id="languages-customize-spoof-english"
      preference="privacy.spoof_english"
    />
    <box class="languages-grid">
      <richlistbox
        id="activeLanguages"
        seltype="multiple"
        onselect="gLanguagesDialog.onLanguageSelect();"
      />
      <vbox>
        <button
          id="up"
          class="up"
          oncommand="gLanguagesDialog.moveUp();"
          disabled="true"
          data-l10n-id="languages-customize-moveup"
          preference="pref.browser.language.disable_button.up"
        />
        <button
          id="down"
          class="down"
          oncommand="gLanguagesDialog.moveDown();"
          disabled="true"
          data-l10n-id="languages-customize-movedown"
          preference="pref.browser.language.disable_button.down"
        />
        <button
          id="remove"
          oncommand="gLanguagesDialog.removeLanguage();"
          disabled="true"
          data-l10n-id="languages-customize-remove"
          preference="pref.browser.language.disable_button.remove"
        />
      </vbox>
      <!-- This <vbox> is needed to position search tooltips correctly. -->
      <vbox>
        <menulist
          id="availableLanguages"
          oncommand="gLanguagesDialog.onAvailableLanguageSelect();"
          data-l10n-id="languages-customize-select-language"
          data-l10n-attrs="placeholder"
          aria-labelledby="availableLanguagesPopup"
        >
          <menupopup id="availableLanguagesPopup" />
        </menulist>
      </vbox>
      <button
        id="addButton"
        class="add-web-language"
        oncommand="gLanguagesDialog.addLanguage();"
        disabled="true"
        data-l10n-id="languages-customize-add"
      />
    </box>
  </dialog>
</window>
