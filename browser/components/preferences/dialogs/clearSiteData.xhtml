<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="ClearSiteDataDialog"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="clear-site-data-window2"
  data-l10n-attrs="title, style"
  persist="width height"
>
  <dialog
    buttons="accept,cancel"
    data-l10n-id="clear-site-data-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />
      <html:link
        rel="stylesheet"
        href="chrome://browser/content/preferences/dialogs/clearSiteData.css"
      />

      <html:link rel="localization" href="branding/brand.ftl" />
      <html:link
        rel="localization"
        href="browser/preferences/clearSiteData.ftl"
      />
    </linkset>
    <script src="chrome://browser/content/preferences/dialogs/clearSiteData.js" />

    <keyset>
      <key
        data-l10n-id="clear-site-data-close-key"
        modifiers="accel"
        oncommand="window.close();"
      />
    </keyset>

    <vbox class="contentPane">
      <description control="url" data-l10n-id="clear-site-data-description" />
      <separator class="thin" />
      <vbox class="options-container">
        <vbox class="option">
          <checkbox
            data-l10n-id="clear-site-data-cookies-empty"
            id="clearSiteData"
            checked="true"
          />
          <description
            class="option-description indent"
            data-l10n-id="clear-site-data-cookies-info"
          />
        </vbox>
        <vbox class="option">
          <checkbox
            data-l10n-id="clear-site-data-cache-empty"
            id="clearCache"
            checked="true"
          />
          <description
            class="option-description indent"
            data-l10n-id="clear-site-data-cache-info"
          />
        </vbox>
      </vbox>
    </vbox>
  </dialog>
</window>
