<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="DoHExceptionsDialog"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="permissions-exceptions-doh-window"
  data-l10n-attrs="title, style"
  persist="width height"
>
  <dialog
    id="exceptionDialog"
    buttons="accept,cancel"
    data-l10n-id="permission-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/content/preferences/dialogs/sitePermissions.css"
      />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="branding/brand.ftl" />
      <html:link
        rel="localization"
        href="browser/preferences/permissions.ftl"
      />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/dohExceptions.js" />

    <keyset>
      <key
        data-l10n-id="permissions-close-key"
        modifiers="accel"
        oncommand="window.close();"
      />
    </keyset>

    <vbox class="contentPane">
      <description
        id="dohExceptionText"
        control="url"
        data-l10n-id="permissions-exceptions-manage-doh-desc"
      />
      <separator class="thin" />
      <label
        id="urlLabel"
        control="url"
        data-l10n-id="permissions-doh-entry-field"
      />
      <hbox align="start">
        <html:input
          id="url"
          type="text"
          style="flex: 1"
          oninput="gDoHExceptionsManager.onExceptionInput();"
          onkeypress="gDoHExceptionsManager.onExceptionKeyPress(event);"
        />
      </hbox>
      <hbox pack="end">
        <button
          id="btnAddException"
          disabled="true"
          data-l10n-id="permissions-doh-add-exception"
          oncommand="gDoHExceptionsManager.addException();"
        />
      </hbox>
      <separator class="thin" />
      <listheader>
        <treecol
          id="siteCol"
          data-l10n-id="permissions-doh-col"
          style="flex: 3 3 auto; width: 0"
          data-isCurrentSortCol="true"
          onclick="gDoHExceptionsManager.buildExceptionList(event.target)"
        />
      </listheader>
      <richlistbox
        id="permissionsBox"
        selected="false"
        onkeypress="gDoHExceptionsManager.onListBoxKeyPress(event);"
        onselect="gDoHExceptionsManager.onListBoxSelect();"
      />
    </vbox>

    <hbox class="actionButtons">
      <button
        id="removeException"
        disabled="true"
        data-l10n-id="permissions-doh-remove"
        oncommand="gDoHExceptionsManager.onExceptionDelete();"
      />
      <button
        id="removeAllExceptions"
        data-l10n-id="permissions-doh-remove-all"
        oncommand="gDoHExceptionsManager.onAllExceptionsDelete();"
      />
    </hbox>
  </dialog>
</window>
