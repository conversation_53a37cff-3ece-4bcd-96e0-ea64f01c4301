<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="PermissionsDialog"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="permissions-window2"
  data-l10n-attrs="title, style"
  persist="width height"
  csp="default-src chrome: resource:; img-src chrome: resource: data:; object-src 'none'; script-src-attr 'none'; style-src 'unsafe-inline';"
>
  <dialog
    buttons="accept,cancel"
    data-l10n-id="permission-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/content/preferences/dialogs/sitePermissions.css"
      />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="branding/brand.ftl" />
      <html:link
        rel="localization"
        href="browser/preferences/permissions.ftl"
      />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/permissions.js" />

    <keyset>
      <key
        id="permissionsDialogCloseKey"
        data-l10n-id="permissions-close-key"
        modifiers="accel"
      />
    </keyset>

    <vbox class="contentPane">
      <description id="permissionsText" control="url" />
      <separator class="thin" />
      <label id="urlLabel" control="url" data-l10n-id="permissions-address" />
      <hbox align="start">
        <html:input id="url" type="text" style="flex: 1" />
      </hbox>
      <hbox pack="end">
        <button
          id="btnDisableETP"
          disabled="true"
          data-l10n-id="permissions-disable-etp"
        />
        <button
          id="btnBlock"
          disabled="true"
          data-l10n-id="permissions-block"
        />
        <button
          id="btnCookieSession"
          disabled="true"
          data-l10n-id="permissions-session"
        />
        <button
          id="btnAllow"
          disabled="true"
          data-l10n-id="permissions-allow"
        />
        <button
          id="btnHttpsOnlyOff"
          disabled="true"
          data-l10n-id="permissions-button-off"
        />
        <button
          id="btnHttpsOnlyOffTmp"
          disabled="true"
          data-l10n-id="permissions-button-off-temporarily"
        />
      </hbox>
      <separator class="thin" />
      <listheader>
        <treecol
          id="siteCol"
          data-l10n-id="permissions-site-name"
          style="flex: 3 3 auto; width: 0"
        />
        <treecol
          id="statusCol"
          data-l10n-id="permissions-status"
          style="flex: 1 1 auto; width: 0"
          data-isCurrentSortCol="true"
        />
      </listheader>
      <richlistbox id="permissionsBox" selected="false" />
    </vbox>

    <hbox class="actionButtons">
      <button
        id="removePermission"
        disabled="true"
        data-l10n-id="permissions-remove"
      />
      <button id="removeAllPermissions" data-l10n-id="permissions-remove-all" />
    </hbox>
  </dialog>
</window>
