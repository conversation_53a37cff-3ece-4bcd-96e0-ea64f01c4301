<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="BlocklistsDialog"
  data-l10n-id="blocklist-window2"
  data-l10n-attrs="title, style"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  onload="gBlocklistManager.onLoad();"
  onunload="gBlocklistManager.uninit();"
  persist="width height"
>
  <dialog
    buttons="accept,cancel"
    data-l10n-id="blocklist-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link rel="localization" href="branding/brand.ftl" />
      <html:link rel="localization" href="browser/preferences/blocklists.ftl" />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/blocklists.js" />

    <keyset>
      <key
        data-l10n-id="blocklist-close-key"
        modifiers="accel"
        oncommand="window.close();"
      />
    </keyset>

    <vbox class="contentPane">
      <description
        id="blocklistsText"
        data-l10n-id="blocklist-description"
        control="url"
      >
        <html:a
          target="_blank"
          class="text-link"
          data-l10n-name="disconnect-link"
          href="https://disconnect.me/"
        />
      </description>
      <separator class="thin" />
      <tree
        id="blocklistsTree"
        flex="1"
        style="height: 18em"
        hidecolumnpicker="true"
        onselect="gBlocklistManager.onListSelected();"
      >
        <treecols>
          <treecol
            id="selectionCol"
            label=""
            style="flex: 1 auto"
            sortable="false"
            type="checkbox"
          />
          <treecol
            id="listCol"
            data-l10n-id="blocklist-treehead-list"
            style="flex: 80 80 auto"
            sortable="false"
          />
        </treecols>
        <treechildren />
      </tree>
    </vbox>
  </dialog>
</window>
