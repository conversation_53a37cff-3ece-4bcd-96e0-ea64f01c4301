<?xml version="1.0"?>

<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<window
  id="SitePermissionsDialog"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
  xmlns:html="http://www.w3.org/1999/xhtml"
  data-l10n-id="permissions-window2"
  data-l10n-attrs="title, style"
  onload="gSitePermissionsManager.onLoad();"
  onunload="gSitePermissionsManager.uninit();"
  persist="width height"
>
  <dialog
    buttons="accept,cancel"
    data-l10n-id="permission-dialog"
    data-l10n-attrs="buttonlabelaccept, buttonaccesskeyaccept"
  >
    <linkset>
      <html:link rel="stylesheet" href="chrome://global/skin/global.css" />
      <html:link
        rel="stylesheet"
        href="chrome://browser/content/preferences/dialogs/sitePermissions.css"
      />
      <html:link
        rel="stylesheet"
        href="chrome://browser/skin/preferences/preferences.css"
      />

      <html:link
        rel="localization"
        href="browser/preferences/preferences.ftl"
      />
      <html:link
        rel="localization"
        href="browser/preferences/permissions.ftl"
      />
    </linkset>

    <script src="chrome://browser/content/preferences/dialogs/sitePermissions.js" />
    <script src="chrome://browser/content/preferences/extensionControlled.js" />

    <keyset>
      <key
        data-l10n-id="permissions-close-key"
        modifiers="accel"
        oncommand="window.close();"
      />
    </keyset>

    <vbox class="contentPane">
      <hbox align="center" id="setAutoplayPref" hidden="true">
        <label data-l10n-id="permissions-autoplay-menu" />
      </hbox>
      <description id="permissionsText" control="url" />
      <separator class="thin" />
      <hbox align="start">
        <search-textbox
          id="searchBox"
          flex="1"
          data-l10n-id="permissions-searchbox"
          data-l10n-attrs="placeholder"
          oncommand="gSitePermissionsManager.buildPermissionsList();"
        />
      </hbox>
      <separator class="thin" />
      <listheader>
        <treecol
          id="siteCol"
          data-l10n-id="permissions-site-name"
          onclick="gSitePermissionsManager.buildPermissionsList(event.target)"
        />
        <treecol
          id="statusCol"
          data-l10n-id="permissions-status"
          data-isCurrentSortCol="true"
          onclick="gSitePermissionsManager.buildPermissionsList(event.target);"
        />
      </listheader>
      <richlistbox
        id="permissionsBox"
        selected="false"
        onkeypress="gSitePermissionsManager.onPermissionKeyPress(event);"
        onselect="gSitePermissionsManager.onPermissionSelect();"
      />
    </vbox>

    <hbox class="actionButtons">
      <button
        id="removePermission"
        disabled="true"
        data-l10n-id="permissions-remove"
        oncommand="gSitePermissionsManager.onPermissionDelete();"
      />
      <button
        id="removeAllPermissions"
        data-l10n-id="permissions-remove-all"
        oncommand="gSitePermissionsManager.onAllPermissionsDelete();"
      />
    </hbox>

    <checkbox id="permissionsDisableCheckbox" />
    <description id="permissionsDisableDescription" />
    <hbox
      id="browserNotificationsPermissionExtensionContent"
      class="extension-controlled"
      align="center"
      hidden="true"
    >
      <description control="disableNotificationsPermissionExtension" flex="1" />
      <button
        id="disableNotificationsPermissionExtension"
        class="extension-controlled-button accessory-button"
        data-l10n-id="disable-extension"
      />
    </hbox>
  </dialog>
</window>
