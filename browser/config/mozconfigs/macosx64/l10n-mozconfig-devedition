. "$topsrcdir/browser/config/mozconfigs/common"
. "$topsrcdir/build/mozconfig.no-compile"

if test `uname -s` = "Linux"; then
  # We need to indicate the target for cross builds
  ac_add_options --target=x86_64-apple-darwin
  export MKFSHFS=$MOZ_FETCHES_DIR/hfsplus/newfs_hfs
  export DMG_TOOL=$MOZ_FETCHES_DIR/dmg/dmg
  export HFS_TOOL=$MOZ_FETCHES_DIR/dmg/hfsplus
fi

ac_add_options --enable-update-channel=${MOZ_UPDATE_CHANNEL}
ac_add_options --with-branding=browser/branding/aurora

ac_add_options --disable-nodejs
unset NODEJS

if test "${MOZ_UPDATE_CHANNEL}" = "nightly"; then
ac_add_options --with-macbundlename-prefix=Firefox
fi

export MOZILLA_OFFICIAL=1

# Don't autoclobber l10n, as this can lead to missing binaries and broken builds
# Bug 1283438
mk_add_options AUTOCLOBBER=

. "$topsrcdir/build/mozconfig.common.override"
