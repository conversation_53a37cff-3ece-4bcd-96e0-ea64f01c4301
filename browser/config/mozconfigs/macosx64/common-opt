# This file is sourced by the nightly, beta, and release mozconfigs.

. $topsrcdir/build/macosx/mozconfig.common

ac_add_options --enable-update-channel=${MOZ_UPDATE_CHANNEL}
ac_add_options --with-google-location-service-api-keyfile=/builds/gls-gapi.data
ac_add_options --with-google-safebrowsing-api-keyfile=/builds/sb-gapi.data
ac_add_options --with-mozilla-api-keyfile=/builds/mozilla-desktop-geoloc-api.key

# Needed to enable breakpad in application.ini
export MOZILLA_OFFICIAL=1

# Package js shell.
export MOZ_PACKAGE_JSSHELL=1
