. "$topsrcdir/browser/config/mozconfigs/macosx64/common-opt"

# Add-on signing is not required for DevEdition
MOZ_REQUIRE_SIGNING=

ac_add_options --enable-instruments

# Cross-compiled builds fail when dtrace is enabled
if test `uname -s` != Linux; then
  ac_add_options --enable-dtrace
fi

if test "${MOZ_UPDATE_CHANNEL}" = "nightly"; then
ac_add_options --with-macbundlename-prefix=Firefox
fi

ac_add_options --with-branding=browser/branding/aurora

. "$topsrcdir/build/mozconfig.common.override"
