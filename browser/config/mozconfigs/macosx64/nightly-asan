ac_add_options --enable-optimize=-O2
ac_add_options --enable-debug-symbols=-gline-tables-only

. $topsrcdir/build/unix/mozconfig.asan

# Package js shell.
export MOZ_PACKAGE_JSSHELL=1

# Need this to prevent name conflicts with the normal nightly build packages
# Before mozconfig.common so we can test for asan builds there
export MOZ_PKG_SPECIAL=asan

. "$topsrcdir/build/macosx/mozconfig.common"

# This is disabled by mozconfig.asan and reenabled by mozconfig.common.
# Ensure it is disabled since it conflicts with ASan.
ac_add_options --disable-crashreporter

. "$topsrcdir/build/mozconfig.common.override"
