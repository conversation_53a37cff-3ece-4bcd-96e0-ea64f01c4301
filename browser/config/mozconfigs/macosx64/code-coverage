. "$topsrcdir/browser/config/mozconfigs/macosx64/nightly"

ac_add_options --enable-debug-symbols=-g1
ac_add_options --disable-sandbox
ac_add_options --disable-warnings-as-errors
ac_add_options --without-wasm-sandboxed-libraries
ac_add_options --enable-coverage

CLANG_LIB_DIR="$(cd $MOZ_FETCHES_DIR/clang/lib/clang/* && cd lib/darwin && pwd)"
export LDFLAGS="-coverage -L$CLANG_LIB_DIR"
export LIBS="-lclang_rt.profile_osx"
export RUSTFLAGS="-Ccodegen-units=1 -Zprofile -Cpanic=abort -Zpanic_abort_tests -Coverflow-checks=off"
export RUSTDOCFLAGS="-Cpanic=abort"
