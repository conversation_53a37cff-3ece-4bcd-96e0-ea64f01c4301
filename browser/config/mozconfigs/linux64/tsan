ac_add_options --enable-debug-symbols=-gline-tables-only

. $topsrcdir/build/unix/mozconfig.linux
. $topsrcdir/build/unix/mozconfig.tsan

# Package js shell.
export MOZ_PACKAGE_JSSHELL=1

# Need this to prevent name conflicts with the normal nightly build packages
export MOZ_PKG_SPECIAL=tsan

# Disable telemetry
ac_add_options MOZ_TELEMETRY_REPORTING=

# rustfmt is currently missing in Rust nightly
unset RUSTFMT

# Current Rust Nightly has warnings
ac_add_options --disable-warnings-as-errors

. "$topsrcdir/build/mozconfig.common.override"
