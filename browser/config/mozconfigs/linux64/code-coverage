. "$topsrcdir/browser/config/mozconfigs/linux64/nightly"

ac_add_options --disable-install-strip
ac_add_options --disable-sandbox
ac_add_options --disable-dmd
ac_add_options --disable-profiling
ac_add_options --disable-warnings-as-errors
ac_add_options --without-wasm-sandboxed-libraries
ac_add_options --enable-coverage

CLANG_LIB_DIR="$(cd $MOZ_FETCHES_DIR/clang/lib/clang/* && cd lib/linux && pwd)"
export LDFLAGS="--coverage -L$CLANG_LIB_DIR"
export LIBS="-lclang_rt.profile-x86_64"
export RUSTFLAGS="-Ccodegen-units=1 -Zprofile -Cpanic=abort -Zpanic_abort_tests -Coverflow-checks=off"
export RUSTDOCFLAGS="-Cpanic=abort"
