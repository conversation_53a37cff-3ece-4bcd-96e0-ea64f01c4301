. "$topsrcdir/browser/config/mozconfigs/common"
. "$topsrcdir/browser/config/mozconfigs/win32/common-win32"
. "$topsrcdir/build/mozconfig.no-compile"

ac_add_options --enable-update-channel=${MOZ_UPDATE_CHANNEL}
ac_add_options --enable-official-branding

ac_add_options --disable-nodejs
unset NODEJS

export MOZILLA_OFFICIAL=1

# Don't autoclobber l10n, as this can lead to missing binaries and broken builds
# Bug 1283438
mk_add_options AUTOCLOBBER=

. "$topsrcdir/build/mozconfig.common.override"
