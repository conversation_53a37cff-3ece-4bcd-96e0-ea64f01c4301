# This file is sourced by the nightly, beta, and release mozconfigs.

. $topsrcdir/build/macosx/mozconfig.common

ac_add_options --enable-update-channel=${MOZ_UPDATE_CHANNEL}

if test `uname -s` != Linux; then
APIKEYDIR="${APIKEYDIR:-${WORKSPACE}}"
else
APIKEYDIR="${APIKEYDIR:-/builds}"
fi

ac_add_options --with-google-location-service-api-keyfile=${APIKEYDIR}/gls-gapi.data
ac_add_options --with-google-safebrowsing-api-keyfile=${APIKEYDIR}/sb-gapi.data
ac_add_options --with-mozilla-api-keyfile=${APIKEYDIR}/mozilla-desktop-geoloc-api.key

# Needed to enable breakpad in application.ini
export MOZILLA_OFFICIAL=1

# Package js shell.
export MOZ_PACKAGE_JSSHELL=1

ac_add_options --target=aarch64-apple-darwin

# As of Clang 13, the default is -mcpu=apple-m1 when using a aarch64-apple-macos target,
# but we're using apple64-apple-darwin, which defaults to -mcpu=apple-a7, which disables
# a bunch of # performance-enabling CPU features.
# TODO: We'll want to switch to aarch64-apple-macos eventually.
export CFLAGS="$CFLAGS -mcpu=apple-m1"
export CXXFLAGS="$CXXFLAGS -mcpu=apple-m1"
