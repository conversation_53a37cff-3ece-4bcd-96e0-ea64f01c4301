# This file is used by all AArch64 Win64 builds

ac_add_options --target=aarch64-pc-windows-msvc

. $topsrcdir/build/mozconfig.clang-cl

if test `uname -s` = Linux; then

# Configure expects executables for check_prog, so set the relevant files
# as executable on the first evaluation of the mozconfig where they exist.
export UPX="${MOZ_FETCHES_DIR}/upx-3.95-win64/upx.exe"
if [ -f "${UPX}" ]; then
    chmod +x "${UPX}"
fi

fi
