<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this file,
   - You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Permission Prompts</title>
  </head>
  <body>
    <button
      id="geo"
      onclick="navigator.geolocation.getCurrentPosition(() => {})"
    >
      Geolocation
    </button>
    <button id="xr" onclick="content.navigator.getVRDisplays();">WebXR</button>
    <button id="persistent-storage" onclick="navigator.storage.persist()">
      Persistent Storage
    </button>
    <button
      id="webRTC-shareDevices"
      onclick="shareDevice({video: true, fake: true});"
    >
      Video
    </button>
    <button
      id="webRTC-shareMicrophone"
      onclick="shareDevice({audio: true, fake: true});"
    >
      Audio
    </button>
    <button
      id="webRTC-shareDevices2"
      onclick="shareDevice({audio: true, video: true, fake: true});"
    >
      Audio and Video
    </button>
    <button
      id="webRTC-shareScreen"
      onclick="shareDevice({video: {mediaSource: 'screen'}});"
    >
      Screen
    </button>
    <button id="web-notifications" onclick="Notification.requestPermission()">
      web-notifications
    </button>
    <a id="addons" href="borderify.xpi">Install Add-On</a>
    <form>
      <input type="email" id="email" value="<EMAIL>" />
      <input type="password" id="password" value="" />
      <button type="submit" id="login-capture">Login</button>
    </form>

    <script>
      // Share device used in onclick calls above.
      /* exported shareDevice */
      function shareDevice(config) {
        navigator.mediaDevices.getUserMedia(config);
      }
    </script>
  </body>
</html>
