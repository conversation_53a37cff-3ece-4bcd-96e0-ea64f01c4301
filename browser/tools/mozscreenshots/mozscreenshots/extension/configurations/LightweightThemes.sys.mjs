/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

// eslint-disable-next-line no-shadow
import { AddonManager } from "resource://gre/modules/AddonManager.sys.mjs";

export var LightweightThemes = {
  init() {},

  configurations: {
    noLWT: {
      selectors: [],
      async applyConfig() {
        let addon = await AddonManager.getAddonByID(
          "<EMAIL>"
        );
        await addon.enable();
      },
    },

    compactLight: {
      selectors: [],
      async applyConfig() {
        let addon = await AddonManager.getAddonByID(
          "<EMAIL>"
        );
        await addon.enable();
      },
    },

    compactDark: {
      selectors: [],
      async applyConfig() {
        let addon = await AddonManager.getAddonByID(
          "<EMAIL>"
        );
        await addon.enable();
      },
    },

    alpenGlow: {
      selectors: [],
      async applyConfig() {
        let addon = await AddonManager.getAddonByID(
          "<EMAIL>"
        );
        await addon.enable();
      },
    },
  },
};
