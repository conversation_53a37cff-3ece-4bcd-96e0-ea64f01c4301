# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

STANDALONE_MAKEFILE := 1
DIST_SUBDIR := browser

include $(topsrcdir)/config/rules.mk

MOZ_PKG_REMOVALS = $(srcdir)/removed-files.in

MOZ_PKG_MANIFEST = $(srcdir)/package-manifest.in
MOZ_PKG_DUPEFLAGS = -f $(srcdir)/allowed-dupes.mn

DEFINES += -DPKG_LOCALE_MANIFEST=$(topobjdir)/browser/installer/locale-manifest.in
MOZ_CHROME_LOCALE_ENTRIES=@RESPATH@/browser/chrome/ @RESPATH@/chrome/

# Some files have been already bundled with xulrunner
MOZ_PKG_FATAL_WARNINGS = 1

# When packaging an artifact build not all xpt files expected by the
# packager will be present.
ifdef MOZ_ARTIFACT_BUILDS
MOZ_PKG_FATAL_WARNINGS =
endif

DEFINES += -DMOZ_APP_NAME=$(MOZ_APP_NAME) -DPREF_DIR=$(PREF_DIR) -DMOZ_APP_DISPLAYNAME="$(MOZ_APP_DISPLAYNAME)"

ifdef MOZ_DEBUG
DEFINES += -DMOZ_DEBUG=1
endif
ifdef MOZ_LAYOUT_DEBUGGER
DEFINES += -DMOZ_LAYOUT_DEBUGGER=1
endif

ifeq ($(MOZ_WIDGET_TOOLKIT),gtk)
DEFINES += -DMOZ_GTK=1
endif

ifdef MOZ_SYSTEM_NSPR
DEFINES += -DMOZ_SYSTEM_NSPR=1
endif

ifdef MOZ_SYSTEM_NSS
DEFINES += -DMOZ_SYSTEM_NSS=1
endif

ifdef MOZ_ARTIFACT_BUILDS
DEFINES += -DMOZ_ARTIFACT_BUILDS=1
endif

DEFINES += -DJAREXT=

ifdef MOZ_ANGLE_RENDERER
DEFINES += -DMOZ_ANGLE_RENDERER=$(MOZ_ANGLE_RENDERER)
endif

ifdef MOZ_ENABLE_SKIA_PDF
DEFINES += -DMOZ_ENABLE_SKIA_PDF=$(MOZ_ENABLE_SKIA_PDF)
endif

DEFINES += -DMOZ_CHILD_PROCESS_NAME=$(MOZ_CHILD_PROCESS_NAME)
DEFINES += -DMOZ_EME_PROCESS_NAME="$(MOZ_EME_PROCESS_NAME)"

# Set MSVC dlls version to package, if any.
ifdef MOZ_NO_DEBUG_RTL
ifdef WIN32_REDIST_DIR
ifndef MOZ_ARTIFACT_BUILDS
DEFINES += -DMOZ_PACKAGE_MSVC_DLLS=1
DEFINES += -DMSVC_C_RUNTIME_DLL=$(MSVC_C_RUNTIME_DLL)
ifdef MSVC_C_RUNTIME_1_DLL
DEFINES += -DMSVC_C_RUNTIME_1_DLL=$(MSVC_C_RUNTIME_1_DLL)
endif
DEFINES += -DMSVC_CXX_RUNTIME_DLL=$(MSVC_CXX_RUNTIME_DLL)
endif
endif
endif

ifneq (,$(filter WINNT Darwin Android,$(OS_TARGET)))
DEFINES += -DMOZ_SHARED_MOZGLUE=1
endif

ifdef NECKO_WIFI
DEFINES += -DNECKO_WIFI
endif

ifdef MAKENSISU
DEFINES += -DHAVE_MAKENSISU=1
endif

ifdef MOZ_DEFAULT_BROWSER_AGENT
DEFINES += -DMOZ_DEFAULT_BROWSER_AGENT=1
endif

ifdef MOZ_NOTIFICATION_SERVER
DEFINES += -DMOZ_NOTIFICATION_SERVER=1
endif

ifeq (cocoa,$(MOZ_WIDGET_TOOLKIT))
MOZ_PKG_MAC_DSSTORE=$(topsrcdir)/$(MOZ_BRANDING_DIRECTORY)/dsstore
MOZ_PKG_MAC_BACKGROUND=$(topsrcdir)/$(MOZ_BRANDING_DIRECTORY)/background.png
MOZ_PKG_MAC_ICON=$(topsrcdir)/$(MOZ_BRANDING_DIRECTORY)/disk.icns
MOZ_PKG_MAC_EXTRA=--symlink '/Applications:/ '
endif

include $(topsrcdir)/toolkit/mozapps/installer/packager.mk

ifeq (Darwin,$(OS_TARGET))
BINPATH = $(_BINPATH)
DEFINES += -DAPPNAME='$(_APPNAME)'
else
# Every other platform just winds up in dist/bin
BINPATH = bin
endif
DEFINES += -DBINPATH='$(BINPATH)'

ifeq (cocoa,$(MOZ_WIDGET_TOOLKIT))
RESPATH = $(_RESPATH)
else
RESPATH = $(BINPATH)
endif
DEFINES += -DRESPATH='$(RESPATH)'

LPROJ_ROOT = $(firstword $(subst -, ,$(AB_CD)))
ifeq (cocoa,$(MOZ_WIDGET_TOOLKIT))
ifeq (zh-TW,$(AB_CD))
LPROJ_ROOT := $(subst -,_,$(AB_CD))
endif
endif
DEFINES += -DLPROJ_ROOT=$(LPROJ_ROOT)

ifdef LLVM_SYMBOLIZER
DEFINES += -DLLVM_SYMBOLIZER=$(notdir $(LLVM_SYMBOLIZER))
endif
ifdef MOZ_CLANG_RT_ASAN_LIB_PATH
DEFINES += -DMOZ_CLANG_RT_ASAN_LIB=$(notdir $(MOZ_CLANG_RT_ASAN_LIB_PATH))
endif

# Builds using the hybrid FasterMake/RecursiveMake backend will
# fail to produce a langpack. See bug 1255096.
libs::
ifeq (,$(filter FasterMake+RecursiveMake,$(BUILD_BACKENDS)))
	$(MAKE) -C $(DEPTH)/browser/locales langpack
endif

ifeq (WINNT,$(OS_ARCH))
PKGCOMP_FIND_OPTS =
else
PKGCOMP_FIND_OPTS = -L
endif
ifeq (Darwin, $(OS_ARCH))
FINDPATH = $(_APPNAME)/Contents/MacOS
else
FINDPATH=bin
endif

package-compare::
	cd $(DIST); find $(PKGCOMP_FIND_OPTS) '$(FINDPATH)' -type f | sort > bin-list.txt
	$(call py_action,preprocessor,$(DEFINES) $(ACDEFINES) $(MOZ_PKG_MANIFEST)) | grep '^$(BINPATH)' | sed -e 's/^\///' | sort > $(DIST)/pack-list.txt
	-diff -u $(DIST)/pack-list.txt $(DIST)/bin-list.txt
	rm -f $(DIST)/pack-list.txt $(DIST)/bin-list.txt

ifdef ENABLE_WEBDRIVER
DEFINES += -DENABLE_WEBDRIVER=1
endif
