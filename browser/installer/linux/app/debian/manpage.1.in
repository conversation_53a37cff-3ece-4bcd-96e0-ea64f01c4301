.TH ${DEB_DISPLAY_NAME} 1 "${DEB_MANPAGE_DATE}" ${DEB_PKG_NAME} "Linux User's Manual"
.SH NAME
${DEB_PKG_NAME} - ${DEB_DISPLAY_NAME} offers safe and easy web browsing

.SH SYNOPSIS
.B ${DEB_PKG_NAME}
[\fIOPTIONS\fR] [\fIURL\fR]

.B /usr/lib/${DEB_PKG_NAME}/${DEB_PKG_NAME}
[\fIOPTIONS\fR] [\fIURL\fR]

.SH DESCRIPTION
\fB${DEB_DISPLAY_NAME}\fR is an open-source web browser, designed for
standards compliance, performance and portability.

.SH USAGE
If there is an ${DEB_DISPLAY_NAME} browser already running, \fB${DEB_PKG_NAME}\fR will
arrange for it to create a new browser window; otherwise it will start
a new instance.

.SH OPTIONS
A summary of the options supported by \fB${DEB_PKG_NAME}\fR is included below.

.SS "X11 options"
.TP
.BI \-\-display= DISPLAY
X display to use
.TP
.B \-\-sync
Make X calls synchronous
.TP
.B \-\-no\-xshm
Don't use X shared memory extension
.TP
.BI \-\-xim\-preedit= STYLE
.TP
.BI \-\-xim\-status= STYLE
.TP
.B \-\-g\-fatal\-warnings
Make all warnings fatal

.SS "Mozilla options"
.TP
.B \-h, \-help
Show summary of options.
.TP
.B \-v, \-version
Print ${DEB_DISPLAY_NAME} version.
.TP
\fB\-P\fR [\fIprofile\fR]
Start with \fIprofile\fR. When no profile is given, displays the Profile Manager. May require \fB\-no\-remote\fR, see below.
.TP
.B \-migration
Start with migration wizard. May require \fB\-no\-remote\fR, see below.
.TP
.B \-ProfileManager
Start with profile manager. May require \fB\-no\-remote\fR, see below.
.TP
.B \-no\-remote
Don't connect to a running ${DEB_DISPLAY_NAME} instance. This option can be necessary
in conjunction to several of the options above, that won't have any effect
when an ${DEB_DISPLAY_NAME} instance is running unless \fB\-no\-remote\fR is used at
the same time.
.TP
\fB\-UILocale\fR \fIlocale\fR
Start with \fIlocale\fR resources as User Interface locale. By default, it is
guessed from environment and available locales for ${DEB_DISPLAY_NAME}.
.TP
.B \-safe\-mode
Starts ${DEB_DISPLAY_NAME} in safe mode, i.e. disabling all extensions and
showing a bit more debugging messages.
.TP
.B \-jsconsole
Start with Javascript Console
.TP
\fB\-new-window\fR \fIURL\fR
Open \fIURL\fR in a new window in an already running ${DEB_DISPLAY_NAME} process.
.TP
\fB\-new-tab\fR \fIURL\fR
Open \fIURL\fR in a new tab in an already running ${DEB_DISPLAY_NAME} process.
.TP
\fB\-chrome\fR \fIurl\fR
Load the specified chrome.

.SH ENVIRONMENT
\fIMOZILLA_DISABLE_PLUGINS\fR - when set, totally disables loading browser plugins
(the ones that appear at the about:plugins url)

.SH FILES
\fI/usr/bin/${DEB_PKG_NAME}\fR - symbolic link to \fB/usr/lib/${DEB_PKG_NAME}/${DEB_PKG_NAME}\fR.
.br

\fI/usr/lib/${DEB_PKG_NAME}/${DEB_PKG_NAME}\fR - ${DEB_DISPLAY_NAME} executable.

\fI/usr/lib/${DEB_PKG_NAME}/firefox\-bin\fR - Legacy executable.
.br

.SH BUGS
To report a bug, please visit \fIhttp://bugzilla.mozilla.org/\fR

.SH AUTHORS
.TP
.B The Mozilla Organization
.I https://www.mozilla.org/en-US/about/
