
; IMPORTANT: This file should always start with a newline in case a locale
; provided updater.ini does not end with a newline.
; Application to launch after an update has been successfully applied. This
; must be in the same directory or a sub-directory of the directory of the
; application executable that initiated the software update.
[PostUpdateWin]
; ExeRelPath is the path to the PostUpdateWin executable relative to the
; application executable.
ExeRelPath=uninstall\helper.exe
; ExeArg is the argument to pass to the PostUpdateWin exe
ExeArg=/PostUpdate
