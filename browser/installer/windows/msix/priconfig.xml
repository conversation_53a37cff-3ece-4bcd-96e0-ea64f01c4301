<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<resources targetOsVersion="10.0.0" majorVersion="1">
	<!-- Commenting this out yields a single `.pri` declaring all scales. See
	     https://stackoverflow.com/a/42975692. -->
	<!-- <packaging> -->
	<!-- 	<autoResourcePackage qualifier="Language"/> -->
	<!-- 	<autoResourcePackage qualifier="Scale"/> -->
	<!-- 	<autoResourcePackage qualifier="DXFeatureLevel"/> -->
	<!-- </packaging> -->
	<index root="\" startIndexAt="\">
		<default>
			<qualifier name="Language" value="en-US"/>
			<qualifier name="Contrast" value="standard"/>
			<qualifier name="Scale" value="100"/>
			<qualifier name="HomeRegion" value="001"/>
			<qualifier name="TargetSize" value="256"/>
			<qualifier name="LayoutDirection" value="LTR"/>
			<qualifier name="Theme" value="dark"/>
			<qualifier name="AlternateForm" value=""/>
			<qualifier name="DXFeatureLevel" value="DX9"/>
			<qualifier name="Configuration" value=""/>
			<qualifier name="DeviceFamily" value="Universal"/>
			<qualifier name="Custom" value=""/>
		</default>
		<indexer-config type="folder" foldernameAsQualifier="true" filenameAsQualifier="true" qualifierDelimiter="."/>
		<indexer-config type="resw" convertDotsToSlashes="true" initialPath=""/>
		<indexer-config type="resjson" initialPath=""/>
		<indexer-config type="PRI"/>
	</index>
	<!--<index startIndexAt="Start Index Here" root="Root Here">-->
	<!--        <indexer-config type="resfiles" qualifierDelimiter="."/>-->
	<!--        <indexer-config type="priinfo" emitStrings="true" emitPaths="true" emitEmbeddedData="true"/>-->
	<!--</index>-->
</resources>
