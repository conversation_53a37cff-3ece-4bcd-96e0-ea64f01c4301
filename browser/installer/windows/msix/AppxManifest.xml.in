<?xml version="1.0" encoding="utf-8"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
- License, v. 2.0. If a copy of the MPL was not distributed with this file,
- You can obtain one at http://mozilla.org/MPL/2.0/. -->
<!-- #filter substitution -->
<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:com="http://schemas.microsoft.com/appx/manifest/com/windows10"
  xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:uap2="http://schemas.microsoft.com/appx/manifest/uap/windows10/2"
  xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
  xmlns:uap5="http://schemas.microsoft.com/appx/manifest/uap/windows10/5"
  xmlns:uap10="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  IgnorableNamespaces="uap uap2 uap3 uap5 uap10 rescap">

  <Identity Name="@APPX_IDENTITY@" Publisher="@APPX_PUBLISHER@" Version="@APPX_VERSION@" ProcessorArchitecture="@APPX_ARCH@" />
  <Properties>
    <DisplayName>@APPX_DISPLAYNAME@</DisplayName>
    <PublisherDisplayName>@APPX_PUBLISHER_DISPLAY_NAME@</PublisherDisplayName>
    <Description>@APPX_DESCRIPTION@</Description>
    <Logo>Assets\StoreLogo.png</Logo>
    <uap10:PackageIntegrity>
      <uap10:Content Enforcement="on" />
    </uap10:PackageIntegrity>
  </Properties>
  <Resources>
@APPX_RESOURCE_LANGUAGE_LIST@
  </Resources>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22621.1555" />
  </Dependencies>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
  <Applications>
    <Application Id="App" Executable="VFS\ProgramFiles\@APPX_INSTDIR@\@MOZ_APP_NAME@.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements BackgroundColor="#20123A" DisplayName="@MOZ_APP_DISPLAYNAME@" Square150x150Logo="Assets\Square150x150Logo.png" Square44x44Logo="Assets\Square44x44Logo.png" Description="@MOZ_APP_DISPLAYNAME@">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" Square310x310Logo="Assets\LargeTile.png" Square71x71Logo="Assets\SmallTile.png">
          <uap:ShowNameOnTiles>
            <uap:ShowOn Tile="square150x150Logo"/>
            <uap:ShowOn Tile="wide310x150Logo"/>
            <uap:ShowOn Tile="square310x310Logo"/>
          </uap:ShowNameOnTiles>
        </uap:DefaultTile>
      </uap:VisualElements>
      <Extensions>
        <uap3:Extension Category="windows.appExecutionAlias" EntryPoint="Windows.FullTrustApplication" Executable="VFS\ProgramFiles\@APPX_INSTDIR@\@MOZ_APP_NAME@.exe">
          <uap3:AppExecutionAlias>
            <desktop:ExecutionAlias Alias="@MOZ_APP_NAME@.exe" />
          </uap3:AppExecutionAlias>
        </uap3:Extension>
        <uap3:Extension Category="windows.fileTypeAssociation">
          <uap3:FileTypeAssociation Name="htm">
            <uap:SupportedFileTypes>
              <!-- Keep synchronized with
                   https://searchfox.org/mozilla-central/source/browser/installer/windows/nsis/shared.nsh
                   and `os.environment.launched_to_handle` and `os.environment.invoked_to_handle` telemetry in
                   https://searchfox.org/mozilla-central/source/browser/components/BrowserContentHandler.sys.mjs. -->
              <uap:FileType>.avif</uap:FileType>
              <uap:FileType>.htm</uap:FileType>
              <uap:FileType>.html</uap:FileType>
              <uap:FileType>.pdf</uap:FileType>
              <uap:FileType>.shtml</uap:FileType>
              <uap:FileType>.xht</uap:FileType>
              <uap:FileType>.xhtml</uap:FileType>
              <uap:FileType>.svg</uap:FileType>
              <uap:FileType>.webp</uap:FileType>
            </uap:SupportedFileTypes>
            <uap:Logo>Assets\Document44x44.png</uap:Logo>
            <uap2:SupportedVerbs>
              <uap3:Verb Id="open" Parameters="-osint -url &quot;%1&quot;">open</uap3:Verb>
            </uap2:SupportedVerbs>
          </uap3:FileTypeAssociation>
        </uap3:Extension>
        <uap3:Extension Category="windows.protocol">
          <uap3:Protocol Name="http" Parameters="-osint -url &quot;%1&quot;">
            <uap:DisplayName>http</uap:DisplayName>
            <uap:Logo>Assets\Document44x44.png</uap:Logo>
          </uap3:Protocol>
        </uap3:Extension>
        <uap3:Extension Category="windows.protocol">
          <uap3:Protocol Name="https" Parameters="-osint -url &quot;%1&quot;">
            <uap:DisplayName>https</uap:DisplayName>
            <uap:Logo>Assets\Document44x44.png</uap:Logo>
          </uap3:Protocol>
        </uap3:Extension>
        <uap3:Extension Category="windows.protocol">
          <uap3:Protocol Name="mailto" Parameters="-osint -url &quot;%1&quot;">
            <uap:DisplayName>mailto</uap:DisplayName>
            <uap:Logo>Assets\Document44x44.png</uap:Logo>
          </uap3:Protocol>
        </uap3:Extension>
        <!-- COM registrations for the notification server. -->
        <com:Extension Category="windows.comServer">
          <com:ComServer>
            <com:SurrogateServer DisplayName="NotificationServer"
                                 AppId="@MOZ_INOTIFICATIONACTIVATION_CLSID@">
              <com:Class Id="@MOZ_INOTIFICATIONACTIVATION_CLSID@"
                         Path="VFS\ProgramFiles\@APPX_INSTDIR@\notificationserver.dll"
                         ThreadingModel="Both" />
            </com:SurrogateServer>
          </com:ComServer>
        </com:Extension>
        <uap5:Extension
          Category="windows.startupTask"
          Executable="VFS\ProgramFiles\@APPX_INSTDIR@\@MOZ_APP_NAME@.exe"
          EntryPoint="Windows.FullTrustApplication"
          uap10:Parameters="-os-autostart">
          <uap5:StartupTask
            TaskId="LaunchOnLogin"
            Enabled="false"
            DisplayName="@MOZ_APP_DISPLAYNAME@" />
        </uap5:Extension>
        <desktop:Extension Category="windows.toastNotificationActivation">
          <desktop:ToastNotificationActivation ToastActivatorCLSID="@MOZ_INOTIFICATIONACTIVATION_CLSID@" />
        </desktop:Extension>
      </Extensions>
    </Application>
  </Applications>
  <Extensions>
    <!-- These COM registrations allow Windows/MSAA to access Firefox accessibility features. -->
    <com:Extension Category="windows.comInterface">
      <com:ComInterface>
        <com:ProxyStub DisplayName="AccessibleMarshal"
                       Id="1814ceeb-49e2-407f-af99-fa755a7d2607"
                       Path="VFS\ProgramFiles\@APPX_INSTDIR@\AccessibleMarshal.dll" />
        <com:Interface Id="4e747be5-2052-4265-8af0-8ecad7aad1c0"
                       ProxyStubClsid="1814ceeb-49e2-407f-af99-fa755a7d2607" />
        <com:Interface Id="1814ceeb-49e2-407f-af99-fa755a7d2607"
                       ProxyStubClsid="1814ceeb-49e2-407f-af99-fa755a7d2607" />
        <com:Interface Id="0d68d6d0-d93d-4d08-a30d-f00dd1f45b24"
                       ProxyStubClsid="1814ceeb-49e2-407f-af99-fa755a7d2607" />
      </com:ComInterface>
    </com:Extension>
  </Extensions>
</Package>
