# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

DEFINES["APP_VERSION"] = CONFIG["MOZ_APP_VERSION"]

DEFINES["MOZ_APP_NAME"] = CONFIG["MOZ_APP_NAME"]
DEFINES["MOZ_APP_DISPLAYNAME"] = CONFIG["MOZ_APP_DISPLAYNAME"]
DEFINES["MOZILLA_VERSION"] = CONFIG["MOZILLA_VERSION"]

# Turn `firefox` into `Firefox`.
DEFINES["MOZ_TOAST_APP_NAME"] = "%s" % CONFIG["MOZ_APP_NAME"].title()

if CONFIG["MOZ_DEFAULT_BROWSER_AGENT"]:
    DEFINES["MOZ_DEFAULT_BROWSER_AGENT"] = CONFIG["MOZ_DEFAULT_BROWSER_AGENT"]

SPHINX_TREES["installer"] = "docs"
