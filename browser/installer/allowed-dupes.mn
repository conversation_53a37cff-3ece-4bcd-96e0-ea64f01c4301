# Known duplicate files
# This file is ideally removed, but some existing files will be allowed exceptions
# See bug 1303184
#
# PLEASE DO NOT ADD MORE EXCEPTIONS TO THIS LIST
#

# updater on osx is bug 1311194
LaunchServices/org.mozilla.updater
updater.app/Contents/MacOS/org.mozilla.updater

plugin-container.app/Contents/PkgInfo
updater.app/Contents/PkgInfo
media-plugin-helper.app/Contents/PkgInfo

media-plugin-helper.app/Contents/MacOS/@MOZ_APP_DISPLAYNAME@ Media Plugin Helper
plugin-container.app/Contents/MacOS/plugin-container

# Duplicated on Linux by browser/branding/branding-common.mozbuild
#ifdef XP_LINUX
browser/chrome/browser/content/branding/icon16.png
browser/chrome/icons/default/default16.png
browser/chrome/browser/content/branding/icon32.png
browser/chrome/icons/default/default32.png
browser/chrome/browser/content/branding/icon48.png
browser/chrome/icons/default/default48.png
browser/chrome/browser/content/branding/icon64.png
browser/chrome/icons/default/default64.png
browser/chrome/browser/content/branding/icon128.png
browser/chrome/icons/default/default128.png
#endif

chrome.manifest
browser/chrome.manifest

crashreporter.app/Contents/Resources/English.lproj/MainMenu.nib/classes.nib
crashreporter.app/Contents/Resources/English.lproj/MainMenuRTL.nib/classes.nib

#ifndef XP_MACOSX
# firefox/firefox-bin is bug 658850
@MOZ_APP_NAME@
@MOZ_APP_NAME@-bin
#endif

# Row and column icons are duplicated
res/table-remove-column-active.gif
res/table-remove-row-active.gif
res/table-remove-column-hover.gif
res/table-remove-row-hover.gif
res/table-remove-column.gif
res/table-remove-row.gif

res/multilocale.txt
update.locale

# Bug 1451016 - Nightly-only PaymentRequest & Form Autofill code sharing.
browser/features/<EMAIL>/chrome/content/editAddress.xhtml
browser/chrome/browser/res/payments/formautofill/editAddress.xhtml
browser/features/<EMAIL>/chrome/content/editCreditCard.xhtml
browser/chrome/browser/res/payments/formautofill/editCreditCard.xhtml
browser/features/<EMAIL>/chrome/content/autofillEditForms.js
browser/chrome/browser/res/payments/formautofill/autofillEditForms.js

# Bug 1713693 - empty-shim.txt, an intentionally blank file used by SmartBlock (0 bytes)
#ifdef MOZ_GTK
browser/features/<EMAIL>/shims/empty-shim.txt
removed-files
#endif

# Juggler/marionette files
chrome/juggler/content/content/floating-scrollbars.css
browser/chrome/devtools/skin/floating-scrollbars-responsive-design.css
chrome/juggler/content/server/stream-utils.js
chrome/marionette/content/stream-utils.js

# Bug 1606928 - There's no reliable way to connect Top Sites favicons with the favicons in the Search Service
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/allegro-pl.ico
browser/defaults/settings/main/search-config-icons/96327a73-c433-5eb4-a16d-b090cadfb80b
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/amazon.ico
browser/defaults/settings/main/search-config-icons/2e835b0e-9709-d1bb-9725-87f59f3445ca
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/ebay.ico
browser/defaults/settings/main/search-config-icons/70fdd651-6c50-b7bb-09ec-7e85da259173
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/bing-com.ico
browser/defaults/settings/main/search-config-icons/cbf9e891-d079-2b28-5617-283450d463dd
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/duckduckgo-com.ico
browser/defaults/settings/main/search-config-icons/a06dc3fd-4bdb-41f3-2ebc-4cbed06a9bd3
browser/chrome/browser/content/activity-stream/data/content/tippytop/favicons/google-com.ico
browser/defaults/settings/main/search-config-icons/fa0fc42c-d91d-fca7-34eb-806ff46062dc

# Bug 1720584 - De-duplicate theme and customize icons
chrome/toolkit/skin/classic/mozapps/extensions/themeGeneric.svg
browser/chrome/browser/skin/classic/browser/customize.svg

# Bug 1718144 - De-duplicate lightbulb icon
browser/features/<EMAIL>/icons/lightbulb.svg
chrome/toolkit/skin/classic/global/icons/lightbulb.svg

# Bug 1861662 - These will be removed with new search config changes
browser/chrome/browser/search-extensions/amazondotcn/_locales/default/messages.json
browser/chrome/browser/search-extensions/amazondotcn/_locales/mozillaonline/messages.json
browser/chrome/browser/search-extensions/amazondotcom/_locales/en/messages.json
browser/chrome/browser/search-extensions/amazondotcom/_locales/us/messages.json

# Bug 1895873 - Search engines with the same icon
browser/defaults/settings/main/search-config-icons/6a83583a-f0ba-fd39-2fdb-fd2b6990ea3b
browser/defaults/settings/main/search-config-icons/f312610a-ebfb-a106-ea92-fd643c5d3636
browser/defaults/settings/main/search-config-icons/47da97b5-600f-c450-fd15-a52bb2169c11
browser/defaults/settings/main/search-config-icons/fed4f021-ff3e-942a-010e-afa43fda2136
