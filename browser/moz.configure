# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

imply_option("MOZ_PLACES", True)
imply_option("MOZ_SERVICES_HEALTHREPORT", False)
imply_option("MOZ_SERVICES_SYNC", True)
imply_option("MOZ_DEDICATED_PROFILES", True)
imply_option("MOZ_BLOCK_PROFILE_DOWNGRADE", True)
imply_option("MOZ_NORMANDY", False)
imply_option("MOZ_PROFILE_MIGRATOR", True)


imply_option("MOZ_APP_VENDOR", "Mozilla")
imply_option("MOZ_APP_ID", "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}")
# Include the DevTools client, not just the server (which is the default)
imply_option("MOZ_DEVTOOLS", "all")
imply_option("BROWSER_CHROME_URL", "chrome://browser/content/browser.xhtml")

with only_when(target_has_linux_kernel & compile_environment):
    option(env="MOZ_NO_PIE_COMPAT", help="Enable non-PIE wrapper")

    set_config("MOZ_NO_PIE_COMPAT", depends_if("MOZ_NO_PIE_COMPAT")(lambda _: True))


@depends(target, update_channel, have_64_bit, moz_debug, "MOZ_AUTOMATION")
@imports(_from="os", _import="environ")
def requires_stub_installer(
    target, update_channel, have_64_bit, moz_debug, moz_automation
):
    if target.kernel != "WINNT":
        return False
    if have_64_bit:
        return False
    if update_channel not in ("nightly", "nightly-try", "aurora", "beta", "release"):
        return False

    if moz_debug:
        return False

    # Expect USE_STUB_INSTALLER from taskcluster for downstream task consistency
    if moz_automation and not environ.get("USE_STUB_INSTALLER"):
        die(
            "STUB installer expected to be enabled but "
            "USE_STUB_INSTALLER is not specified in the environment"
        )

    return True


imply_option("MOZ_STUB_INSTALLER", True, when=requires_stub_installer)

include("../toolkit/moz.configure")
