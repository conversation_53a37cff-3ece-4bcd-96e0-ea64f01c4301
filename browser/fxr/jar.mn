# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

#if defined(NIGHTLY_BUILD)
browser.jar:
%  content fxr %content/browser/fxr/
   content/browser/fxr/common.css               (content/common.css)
   content/browser/fxr/common.js                (content/common.js)
   content/browser/fxr/fxrui.html               (content/fxrui.html)
   content/browser/fxr/fxrui.css                (content/fxrui.css)
   content/browser/fxr/fxrui_blue.css           (content/fxrui_blue.css)
   content/browser/fxr/fxrui.js                 (content/fxrui.js)
   content/browser/fxr/fxr-fullScreen.js        (content/fxr-fullScreen.js)
   content/browser/fxr/permissions.js           (content/permissions.js)
   content/browser/fxr/prefs.html               (content/prefs.html)
   content/browser/fxr/prefs.css                (content/prefs.css)
   content/browser/fxr/prefs.js                 (content/prefs.js)

   content/browser/fxr/assets/icon-backward.svg         (content/assets/icon-backward.svg)
   content/browser/fxr/assets/icon-forward.svg          (content/assets/icon-forward.svg)
   content/browser/fxr/assets/icon-home.svg             (content/assets/icon-home.svg)
   content/browser/fxr/assets/icon-logo-settings-preview.png  (content/assets/icon-logo-settings-preview.png)
   content/browser/fxr/assets/icon-refresh.svg         (content/assets/icon-refresh.svg)
   content/browser/fxr/assets/icon-reportissue.svg      (content/assets/icon-reportissue.svg)
   content/browser/fxr/assets/icon-secure.svg           (content/assets/icon-secure.svg)
   content/browser/fxr/assets/icon-settings.svg         (content/assets/icon-settings.svg)
   content/browser/fxr/assets/icon-stop-reload.svg      (content/assets/icon-stop-reload.svg)
   content/browser/fxr/assets/icon-toggle-off.png       (content/assets/icon-toggle-off.png)
   content/browser/fxr/assets/icon-toggle-on.png        (content/assets/icon-toggle-on.png)
#endif
