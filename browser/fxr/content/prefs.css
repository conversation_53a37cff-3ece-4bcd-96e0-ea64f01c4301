/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

body {
  margin: 10px 50px;
  background-color: var(--num06);
  color: var(--num13);
}

.plain_button {
  border: none;
  background-color: transparent;
}

.settings_header{
  display: flex;
  flex-direction: row;
}

.icon_backward {
  height: 30px;
  width: 30px;

  margin: 20px 70px 70px 20px;

  background-image: url("assets/icon-backward.svg");
  -moz-context-properties: fill;
  fill: var(--num14);
}
.icon_backward:hover {
  height: 40px;
  width: 40px;
  margin: 15px 65px 65px 15px;

  fill: var(--num07);
}
.icon_backward:active {
  height: 40px;
  width: 40px;
  margin: 15px 65px 65px 15px;

  fill: var(--num09);
}

.about_container {
  flex-grow: 1;
  text-align: center;
}

.fxr_logo {
  width: 72px;
  height: 72px;
}

.version_text {
  font-size: 10px;
  color: var(--num14);
}

.button_report_issue {
  height: 50px;
  width: 110px;
  margin: 20px 0 70px 10px;

  font-size: 10px;
  text-align: start;

  -moz-context-properties: fill;
  fill: var(--num14);
}
.button_report_issue:hover {
  fill: var(--num07);
}
.button_report_issue:active {
  fill: var(--num09);
}

.button_report_issue_icon {
  float:left;
  height: 30px;
  margin: 5px;
}

.button_report_text {
  color: var(--num13);
}

.button_report_url {
  color: var(--num14);
}

.settings_title {
  font-size: 22px;
  font-weight: bold;
  color: var(--num13);
  text-align: center;

  flex-grow: 1;
}

.divider {
  border-style: solid;
  border-width: 1px;
  border-color:  var(--num04);
  margin: 10px;
}

.setting_container {
  display: flex;
  flex-direction: row;

  margin: 10px 5px;
}

.setting_name {
  font-size: 14px;
  color: var(--num13);

  flex-grow: 1;
}

.setting_description {
  font-size: 10px;
  color: var(--num14);
  margin: 0 10px;
}

.setting_control {
  min-width: 120px;
  margin: 5px 0;
}

button.setting_control {
  height: 30px;

  font-size: 14px;
  font-weight: bold;

  border: 2px solid var(--num08);
  border-radius: 5px;

  background-color: var(--num06);
}
button.setting_control:hover {
  background-color: var(--num07);
  border-color: var(--num07);
}
button.setting_control:active {
  background-color: var(--num09);
  border-color: var(--num09);
  color: var(--num19);
}
button.setting_control:disabled {
  background-color: var(--num06);
  border-color: var(--num08);
  color: var(--num03);
}

.clear_confirmation {
  padding: 20px;
  background-color: var(--num19);
  border-radius: 20px;
}

.setting_control_chk {
  opacity: 0;
}
.setting_control_chk + label {
  background-position: right;
  background-size: contain;
  background-repeat: no-repeat;

  background-image: url("assets/icon-toggle-off.png");
}
.setting_control_chk:checked + label {
  background-image: url("assets/icon-toggle-on.png");
}
.setting_control_chk:disabled + label {
  filter: grayscale(1);
}

.modal_mask:not([hidden]) {
  background-color: var(--num14_alpha);
}
