<!doctype html>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!--
  This file contains the HTML UI for the 2D window of Firefox Reality on Desktop
-->

<!-- 1280x720 chosen for default 16:9 ratio -->
<html width="1280" height="720">
  <head>
    <title>Firefox Reality</title>
    <link rel="stylesheet" href="common.css" />
    <link rel="stylesheet" href="fxrui.css" />
    <link rel="stylesheet" href="fxrui_blue.css" />
    <script src="common.js"></script>
    <script src="permissions.js"></script>
    <script src="fxrui.js"></script>
  </head>

  <body>
    <div id="eBrowserContainer" class="browser_container"></div>

    <div class="navbar_container">
      <button id="eBack" class="icon_container icon_backward"></button>
      <button id="eForward" class="icon_container icon_forward"></button>
      <button
        id="eRefresh"
        class="icon_container icon_refresh icon_disabled_hide"
      ></button>
      <button
        id="eStop"
        class="icon_container icon_stop icon_disabled_hide"
        disabled
      ></button>
      <button id="eHome" class="icon_container icon_home"></button>

      <div
        class="urlbar_container urlbar_container_normal"
        id="eUrlBarContainer"
      >
        <img
          class="urlbar_secure_icon"
          id="eUrlSecure"
          src="assets/icon-secure.svg"
          alt="Secure"
        />
        <input class="urlbar_input" id="eUrlInput" type="text" value="" />
      </div>
      <button id="ePrefs" class="icon_container icon_prefs"></button>
    </div>
  </body>
</html>
