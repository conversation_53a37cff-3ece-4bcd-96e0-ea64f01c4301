/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace xul url("http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul");

html, body {
  height: 100%;
}


body {
  display: flex;
  flex-direction: column;
}

.navbar_container {
  width: 100%;

  margin-block: 15px;

  display: flex;
  flex-direction: row;
}

/* Sizing and positioning properties for all icons
   Color is determined by enabled/disabled classes below
 */
.icon_container {
  width: 44px;
  height: 44px;
  margin: 5px;

  border-radius: 22px;
  border-width: 2px;
  border-style: solid;

  background-size: 22px;
  background-repeat: no-repeat;
  background-position: center;
  background-color: inherit;

  -moz-context-properties: fill;

  transition-property: transform;
  transition-duration: 200ms;
}

.icon_container:hover {
  transform: scale(1.25);
}

.icon_container:disabled {
  transform: unset;
}

.icon_disabled_hide:disabled {
  display:none;
}
.icon_backward {
  background-image: url("assets/icon-backward.svg");
  margin-inline-start: 10px;
}
.icon_forward {
  background-image: url("assets/icon-forward.svg");
}
.icon_refresh {
  background-image: url("assets/icon-refresh.svg");
}
.icon_stop {
  background-image: url("assets/icon-stop-reload.svg");
}
.icon_home {
  background-image: url("assets/icon-home.svg");
}
.icon_prefs {
  background-image: url("assets/icon-settings.svg");
  margin-inline-end: 10px;
}

.urlbar_container {
  height: 40px;
  flex-grow: 1;

  padding: 0;
  margin: 5px;

  border-radius: 22px;
  border: 2px solid transparent;

  vertical-align: top;

  display: flex;
  flex-direction: row;
}

.urlbar_secure_icon {
  -moz-context-properties: fill;

  height: 32px;
  padding: 2px;

  visibility: hidden;

  display: inline-block;
}

.urlbar_input {
  background-color: transparent;

  flex-grow: 1;

  border: none;

  font-size: 18px;
  font-family: 'Open Sans', sans-serif;

  mask-image: linear-gradient(to left, transparent, black 8ch);
}

.browser_container {
  width: 100%;
  flex-grow: 1;
}

.browser_instance {
  width: 100%;
  height: 100%;
}

/* Hide the navbar when in fullscreen so that <browser> can
 * fill the entire viewport
 */
:root[inFullScreen] .navbar_container {
  display:none;
}

.browser_settings {
  width: 600px;
  height: 400px;
  border-radius: 20px;
}

.modal_mask:not([hidden]) {
  background-color: var(--num13_alpha);
}
