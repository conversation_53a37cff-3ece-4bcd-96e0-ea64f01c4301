<!doctype html>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!--
  This file contains the Settings UI for Firefox Reality on Desktop
-->

<html width="800" height="400">
  <head>
    <title>Firefox Reality Settings</title>
    <link rel="stylesheet" href="common.css" />
    <link rel="stylesheet" href="fxrui_blue.css" />
    <link rel="stylesheet" href="prefs.css" />
    <script src="prefs.js"></script>
    <script src="common.js"></script>
  </head>

  <body>
    <div class="settings_header">
      <button id="eCloseSettings" class="plain_button icon_backward"></button>
      <div class="about_container">
        <img class="fxr_logo" src="assets/icon-logo-settings-preview.png" />
        <div class="version_text" id="eFxrVersion"></div>
        <div class="version_text" id="eFxrDate"></div>
        <div class="version_text" id="eFxVersion"></div>
      </div>
      <button id="eReportIssue" class="plain_button button_report_issue">
        <img
          class="button_report_issue_icon"
          src="assets/icon-reportissue.svg"
        />
        <span class="button_report_text">Report an issue</span>
        <br />
        <span class="button_report_url">mzl.la/fxr</span>
      </button>
    </div>

    <div class="settings_title">Settings</div>
    <hr class="divider" />

    <div class="setting_container">
      <div class="setting_name">Mozilla's Privacy Policy Page</div>
      <button id="ePrivacyPolicy" class="setting_control">Open</button>
    </div>

    <div class="setting_container">
      <div class="setting_name">Firefox Reality Licensing Information</div>
      <button id="eLicenseInfo" class="setting_control">Open</button>
    </div>

    <div class="setting_container">
      <div class="setting_name">
        Cookies, Site Data, and Cached Web Content
        <div class="setting_description">
          Clearing may sign you out of websites and will require websites to
          reload images and data.
        </div>
      </div>
      <button class="setting_control" id="eClearTry">Clear Data</button>
    </div>
    <div class="clear_confirmation modal_hide" id="eClearPrompt">
      Are you sure you want to clear all data?
      <div>
        <button id="eClearCancel" class="setting_control">Cancel</button>
        <button id="eClearConfirm" class="setting_control">Clear</button>
      </div>
    </div>

    <div class="setting_container">
      <div class="setting_name">
        Allow Firefox to Anonymously Collect and Use Technical and Interaction
        Data
      </div>
      <input id="eCrashConfig" type="checkbox" class="setting_control_chk" />
      <label class="setting_control" for="eCrashConfig"></label>
    </div>
  </body>
</html>
