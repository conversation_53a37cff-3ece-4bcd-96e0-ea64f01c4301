/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

.popup-notification-body[popupid="addon-progress"],
.popup-notification-body[popupid="addon-install-confirmation"] {
  width: 28em;
  max-width: 28em;
}

.addon-install-confirmation-name {
  font-weight: bold;
}

html|*.addon-webext-perm-list {
  margin-block-end: 0;
  padding-inline-start: 0;

  > html|li {
    list-style: none;

    &.webext-perm-granted {
      /* NOTE: Insert line breaks on long permission strings (or domain name included
       * in the localized string that ends up be overflowing */
      overflow-wrap: break-word;
    }

    /* style the permissions list items that are not editable to use the check.svg image */
    &.webext-perm-granted::before {
      content: "";
      background-image: url("chrome://global/skin/icons/check.svg");
      background-repeat: no-repeat;
      -moz-context-properties: fill;
      fill: currentColor;
      /* style the permissions list items to match sizes and margins used by the
       * checkbox xul element to make sure they are properly aligned with the
       * private browsing checkbox at the end of the list. */
      display: inline-flex;
      width: var(--checkbox-size);
      height: var(--checkbox-size);
      margin-inline-end: var(--checkbox-margin-inline);
      vertical-align: text-bottom;
    }
  }
}

html|ul.webext-perm-domains-list {
  --domains-list-border-color: var(--border-color-deemphasized);
  --domains-list-text-color: var(--text-color-deemphasized);

  border: var(--border-width) solid var(--domains-list-border-color);
  border-radius: var(--border-radius-small);
  margin-block: var(--space-small);
  margin-inline: calc(var(--size-item-small) + var(--space-small)) 0;
  padding-block: var(--space-xsmall);
  padding-inline-start: var(--size-item-medium);

  max-width: 80vw;
  overflow: auto;

  /* max-height is set when the domains list is longer than 5 domains
   * to force the domains list to become scrollable. */
  &.scrollable-domains-list {
    max-height: 5.5lh;
  }

  > html|li {
    list-style: disc;
    color: var(--domains-list-text-color);
    /* NOTE: Insert line breaks anywhere in long domain names that would be overflowing */
    overflow-wrap: anywhere;
  }
}

.addon-webext-perm-single-entry {
  margin-top: 11px;
  /* NOTE: Insert line breaks on long permission strings (or domain name included
   * in the localized string that ends up be overflowing */
  overflow-wrap: break-word;
}

.addon-webext-perm-text,
.addon-webext-perm-single-entry {
  margin-inline-start: 0;
}

.addon-webext-perm-text-multiline {
  margin-top: 1em;
  white-space: pre-line;
}

.popup-notification-description[popupid="addon-webext-permissions"],
.popup-notification-description[popupid="addon-webext-permissions-notification"] {
  margin-inline-start: -1px;
}

.popup-notification-description[popupid="addon-install-failed-blocklist"] {
  font-weight: normal;
}

.addon-webext-perm-notification-content,
.addon-installed-notification-content {
  margin-top: 0;
}

.addon-installed-notification-content > checkbox {
  margin: 8px 4px 2px;
}

.addon-webext-name {
  display: inline;
  font-weight: bold;
  margin: 0;
}

.addon-warning-icon {
  -moz-context-properties: fill;
  fill: #FFBF00;
}
