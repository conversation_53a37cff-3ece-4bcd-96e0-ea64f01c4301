<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<svg xmlns="http://www.w3.org/2000/svg" width="260" height="20" fill="none" fill-opacity="context-fill-opacity">
  <defs>
    <clipPath id="g">
      <path d="M11.11 9.78c0 0 4.72-4.72 4.72-4.72a.63.63 0 0 0-.45-********** 0 0 0-.44.17c0 0-4.7 4.7-4.7 4.7 0 0-.48 0-.48 0 0 0-4.7-4.7-4.7-4.7a.63.63 0 0 0-.*********** 0 0 0-.02.87c0 0 4.7 4.69 4.7 4.69 0 0 0 .5 0 .5 0 0-4.7 4.69-4.7 4.69a.63.63 0 0 0 .89.89c0 0 4.69-4.7 4.69-4.7 0 0 .5 0 .5 0 0 0 4.69 4.7 4.69 4.7a.63.63 0 0 0 1.06-.45.63.63 0 0 0-.17-.44c0 0-4.72-4.72-4.72-4.72 0 0 0-.44 0-.44z"/>
    </clipPath>
  </defs>
  <svg viewBox="0 0 20 20" width="20" height="20">
    <defs>
      <clipPath id="a">
        <path d="M12.71 8c0 0 3.99 0 3.99 0 0 0 .3-.3.3-.3 0 0 0-3.99 0-3.99a.5.5 0 0 0-.85-.35c0 0-1.46 1.46-1.46 1.46a6.95 6.95 0 0 0-4.69-1.81c-3.86 0-7 3.14-7 7 0 3.86 3.14 7 7 7a6.97 6.97 0 0 0 6.97-********** 0 0 0-.56-.68.64.64 0 0 0-.68.56 5.73 5.73 0 0 1-5.72 5.19 5.76 5.76 0 0 1-5.75-5.75 5.76 5.76 0 0 1 5.75-5.75c1.44 0 2.78.53 3.81 1.45 0 0-1.45 1.45-1.45 1.45a.5.5 0 0 0 .35.85z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#a)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M7.272 5.586c-3.647 1.048-3.685 3.897-3.17 5.68.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.23-1.314-1.092-3.759-1.092-3.759 0 0-1.187-2.566-4.187-1.878-1.594-.04-2.858.112-3.853.398"/>
      <path d="M11.989-1.5C5.203-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-24.608 0-24.608 0 0-4.775 0-10.119 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="20">
    <defs>
      <clipPath id="b">
        <path d="M13.35 10.31c0 0 2.98 2.65 2.98 2.65 0 0 .42-.02.42-.02 0 0 2.65-2.98 2.65-2.98a.5.5 0 0 0-.4-.83c0 0-2.06.12-2.06.12a6.95 6.95 0 0 0-2.3-4.47c-2.88-2.57-7.32-2.31-9.88.57-2.57 2.88-2.31 7.32.57 9.88a6.97 6.97 0 0 0 9.41-.09.63.63 0 0 0 .03-.88.64.64 0 0 0-.88-.03 5.73 5.73 0 0 1-7.73.07 5.76 5.76 0 0 1-.47-8.12 5.76 5.76 0 0 1 8.12-.47c1.07.96 1.72 2.24 1.88 3.61 0 0-2.05.12-2.05.12a.5.5 0 0 0-.3.87z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#b)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M7.272 5.586c-3.647 1.048-3.685 3.897-3.17 5.68.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.23-1.314-1.092-3.759-1.092-3.759 0 0-1.187-2.566-4.187-1.878-1.594-.04-2.858.112-3.853.398"/>
      <path d="M11.989-1.5C5.203-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-24.608 0-24.608 0 0-4.775 0-10.119 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="40">
    <defs>
      <clipPath id="c">
        <path d="M10.25 13.36c0 0-2.12 3.38-2.12 3.38 0 0 .*********** 0 0 3.38 2.12 3.38 2.12a.5.5 0 0 0 .75-.53c0 0-.46-2.01-.46-2.01a6.95 6.95 0 0 0 4.03-3c2.05-3.27 1.06-7.6-2.2-9.65-3.27-2.05-7.6-1.06-9.65 2.2a6.97 6.97 0 0 0 1.65 ********** 0 0 0 .88-.11.64.64 0 0 0-.11-.88 5.73 5.73 0 0 1-1.35-7.61 5.76 5.76 0 0 1 7.93-1.81 5.76 5.76 0 0 1 1.81 7.93c-.77 1.22-1.93 2.07-3.25 2.45 0 0-.46-2-.46-2a.5.5 0 0 0-.91-.15z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#c)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M7.272 5.586c-3.647 1.048-3.685 3.897-3.17 5.68.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.23-1.314-1.092-3.759-1.092-3.759 0 0-1.187-2.566-4.187-1.878-1.594-.04-2.858.112-3.853.398"/>
      <path d="M11.989-1.5C5.203-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-24.608 0-24.608 0 0-4.775 0-10.119 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="60">
    <defs>
      <clipPath id="d">
        <path d="M7.28 11.98c0 0-3.99-.02-3.99-.02 0 0-.3.3-.3.3 0 0-.02 3.99-.02 3.99a.5.5 0 0 0 .85.36c0 0 1.47-1.45 1.47-1.45a6.95 6.95 0 0 0 4.68 1.84c3.86.02 7.02-3.1 7.04-6.96.02-3.86-3.1-7.02-6.96-7.04a6.97 6.97 0 0 0-7 ********** 0 0 0 .*********** 0 0 0 .69-.56 5.73 5.73 0 0 1 5.75-5.16 5.76 5.76 0 0 1 5.72 5.78 5.76 5.76 0 0 1-5.78 5.72c-1.44-.01-2.77-.55-3.8-1.47 0 0 1.46-1.44 1.46-1.44a.5.5 0 0 0-.35-.86z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#d)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M7.272 5.586c-3.647 1.048-3.685 3.897-3.17 5.68.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.23-1.314-1.092-3.759-1.092-3.759 0 0-1.187-2.566-4.187-1.878-1.594-.04-2.858.112-3.853.398"/>
      <path d="M11.989-1.5C5.203-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-24.608 0-24.608 0 0-4.775 0-10.119 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="80">
    <defs>
      <clipPath id="e">
        <path d="M6.65 9.68c0 0-2.97-2.67-2.97-2.67 0 0-.42.02-.42.02 0 0-2.67 2.97-2.67 2.97a.5.5 0 0 0 .4.83c0 0 2.06-.11 2.06-.11a6.95 6.95 0 0 0 2.27 4.48c2.87 2.58 7.31 2.34 9.89-.53 2.58-2.87 2.34-7.31-.53-9.89a6.97 6.97 0 0 0-**********.63 0 0 0-.*********** 0 0 0 .88.04 5.73 5.73 0 0 1 7.73-.03 5.76 5.76 0 0 1 .43 8.12 5.76 5.76 0 0 1-8.12.43c-1.07-.96-1.71-2.25-1.86-3.62 0 0 2.05-.11 2.05-.11a.5.5 0 0 0 .31-.87z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#e)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M7.272 5.586c-3.647 1.048-3.685 3.897-3.17 5.68.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.23-1.314-1.092-3.759-1.092-3.759 0 0-1.187-2.566-4.187-1.878-1.594-.04-2.858.112-3.853.398"/>
      <path d="M11.989-1.5C5.203-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-24.608 0-24.608 0 0-4.775 0-10.119 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="100">
    <defs>
      <clipPath id="f">
        <path d="M7.31 7.98c0 0-1.14-3.83-1.14-3.83 0 0-.37-.2-.37-.2 0 0-3.83 1.14-3.83 1.14a.5.5 0 0 0-.1.92c0 0 1.82.98 1.82.98a6.95 6.95 0 0 0-.4 5.01c1.1 3.7 5.01 5.81 8.71 4.71 3.7-1.1 5.81-5.01 4.71-8.71a6.97 6.97 0 0 0-8.05-********** 0 0 0-.*********** 0 0 0 .73.49 5.73 5.73 0 0 1 6.61 4 5.76 5.76 0 0 1-3.87 7.15 5.76 5.76 0 0 1-7.15-3.87c-.41-1.38-.28-2.81.3-4.06 0 0 1.81.98 1.81.98a.5.5 0 0 0 .72-.58z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#f)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M8.297 5.357c-4.661.778-4.755 3.972-4.195 5.909.821 2.842 2.696 5.371 6.835 4.918.389-.043 1.68-.478 2.125-.559 2.508-1.254 3.069-3.243 3.342-4.8.012-.069.02-.141.024-.216"/>
      <path d="M14.726-1.5C7.477-1.5-2.5-1.5-2.5-1.5c0 0 0 24.608 0 24.608 0 0 24.608 0 24.608 0 0 0 0-.726 0-1.929"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="120">
    <defs>
      <clipPath id="h">
        <path d="M8.39 7.04c0 0 .55-3.95.55-3.95 0 0-.26-.34-.26-.34 0 0-3.95-.55-3.95-.55a.5.5 0 0 0-.47.8c0 0 1.24 1.65 1.24 1.65a6.95 6.95 0 0 0-2.44 4.39c-.53 3.82 2.14 7.37 5.97 7.9 3.82.53 7.37-2.14 7.9-5.97a6.97 6.97 0 0 0-5.3-********** 0 0 0-.*********** 0 0 0 .46.75 5.73 5.73 0 0 1 4.35 6.39 5.76 5.76 0 0 1-6.49 4.9 5.76 5.76 0 0 1-4.9-6.49c.2-1.43.91-2.68 1.96-3.57 0 0 1.24 1.64 1.24 1.64a.5.5 0 0 0 .89-.23z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 .***************" fill="none"/>
    </g>
    <g clip-path="url(#h)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M9.341 5.23c-5.689.453-5.838 3.964-5.239 6.036.467 1.617 1.275 3.133 2.648 4.055"/>
      <path d="M17.462-1.5c-7.131 0-19.962 0-19.962 0 0 0 0 10.326 0 17.6"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="140">
    <defs>
      <clipPath id="i">
        <path d="M9.47 6.68c0 0 1.84-3.54 1.84-3.54 0 0-.13-.4-.13-.4 0 0-3.54-1.84-3.54-1.84a.5.5 0 0 0-.71.59c0 0 .62 1.97.62 1.97a6.95 6.95 0 0 0-3.77 3.32c-1.78 3.42-.44 7.66 2.99 9.44 3.42 1.78 7.66.44 9.44-2.99a6.97 6.97 0 0 0-2.4-********* 0 0 0-.*********** 0 0 0 .18.87 5.73 5.73 0 0 1 1.97 7.47 5.76 5.76 0 0 1-7.75 2.45 5.76 5.76 0 0 1-2.45-7.75c.66-1.28 1.75-2.22 3.04-2.71 0 0 .62 1.96.62 1.96a.5.5 0 0 0 .92.08z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 2.618 2.669 5.486 5.594" fill="none"/>
    </g>
    <g clip-path="url(#i)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M11.125 5.188c-2.026-.051-3.518.208-4.603.658"/>
      <path d="M22.108-1.5c0 0-6.074 0-12.186 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="160">
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 6.727 6.858 10.432 10.636" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="180">
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="200">
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.375 16.375c0 0 2.502-2.454 5.315-5.214" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="220">
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.375 16.375c0 0 6.455-6.331 10.29-10.093" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="240">
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
    <g clip-path="url(#g)">
      <path stroke="context-fill" stroke-width="4" d="M3.375 16.375c0 0 13.063-12.813 13.063-12.813" fill="none"/>
    </g>
  </svg>
</svg>
