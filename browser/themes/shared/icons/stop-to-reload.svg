<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="20" stroke-width="4" fill="none" fill-opacity="context-fill-opacity">
  <defs>
    <clipPath id="a">
      <path d="M11.11 9.78c0 0 4.72-4.72 4.72-4.72a.63.63 0 0 0-.45-********** 0 0 0-.44.17c0 0-4.7 4.7-4.7 4.7 0 0-.48 0-.48 0 0 0-4.7-4.7-4.7-4.7a.63.63 0 0 0-.*********** 0 0 0-.02.87c0 0 4.7 4.69 4.7 4.69 0 0 0 .5 0 .5 0 0-4.7 4.69-4.7 4.69a.63.63 0 0 0 .89.89c0 0 4.69-4.7 4.69-4.7 0 0 .5 0 .5 0 0 0 4.69 4.7 4.69 4.7a.63.63 0 0 0 1.06-.45.63.63 0 0 0-.17-.44c0 0-4.72-4.72-4.72-4.72 0 0 0-.44 0-.44z"/>
    </clipPath>
    <clipPath id="m">
      <path d="M12.71 8c0 0 3.99 0 3.99 0 0 0 .3-.3.3-.3 0 0 0-3.99 0-3.99a.5.5 0 0 0-.85-.35c0 0-1.46 1.46-1.46 1.46a6.95 6.95 0 0 0-4.69-1.81c-3.86 0-7 3.14-7 7 0 3.86 3.14 7 7 7a6.97 6.97 0 0 0 6.97-********** 0 0 0-.56-.68.64.64 0 0 0-.68.56 5.73 5.73 0 0 1-5.72 5.19 5.76 5.76 0 0 1-5.75-5.75 5.76 5.76 0 0 1 5.75-5.75c1.44 0 2.78.53 3.81 1.45 0 0-1.45 1.45-1.45 1.45a.5.5 0 0 0 .35.85z"/>
    </clipPath>
  </defs>
  <svg viewBox="0 0 20 20" width="20" height="20">
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.375 16.375c0 0 13.063-12.813 13.063-12.813" fill="none"/>
    </g>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="20">
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M9.003 10.855c3.537-3.469 7.435-7.293 7.435-7.293" fill="none"/>
    </g>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="40">
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M14.003 5.95c1.44-1.412 2.435-2.388 2.435-2.388" fill="none"/>
    </g>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="60">
    <defs>
      <clipPath id="b">
        <path d="M13.36 9.95c0 0 3.25 2.32 3.25 2.32 0 0 .42-.07.42-.07 0 0 2.32-3.25 2.32-3.25a.5.5 0 0 0-.49-.78c0 0-2.04.34-2.04.34a6.95 6.95 0 0 0-2.76-4.2c-3.14-2.24-7.52-1.51-9.76 1.63-2.24 3.14-1.51 7.52 1.63 9.76a6.97 6.97 0 0 0 9.34-********** 0 0 0-.06-.88.64.64 0 0 0-.88.06 5.73 5.73 0 0 1-7.68.9 5.76 5.76 0 0 1-1.34-8.02 5.76 5.76 0 0 1 8.02-1.34c1.17.84 1.95 2.05 2.26 3.39 0 0-2.03.34-2.03.34a.5.5 0 0 0-.21.9z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 12.813 13.063 12.813 13.063" fill="none"/>
    </g>
    <g clip-path="url(#b)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .09.461.077 1.166"/>
      <path d="M-1.875 9.432c0 4.538 0 9.232 0 11.398"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="80">
    <defs>
      <clipPath id="c">
        <path d="M12.62 12.11c0 0 1.02 3.86 1.02 3.86 0 0 .*********** 0 0 3.86-1.02 3.86-1.02a.5.5 0 0 0 .12-.92c0 0-1.78-1.04-1.78-1.04a6.95 6.95 0 0 0 .56-4.99c-.98-3.73-4.82-5.97-8.55-4.98-3.73.98-5.97 4.82-4.98 8.55a6.97 6.97 0 0 0 7.89 ********** 0 0 0 .52-.72.64.64 0 0 0-.72-.52 5.73 5.73 0 0 1-6.48-4.21 5.76 5.76 0 0 1 4.09-7.03 5.76 5.76 0 0 1 7.03 4.09c.37 1.39.19 2.82-.43 4.05 0 0-1.77-1.03-1.77-1.03a.5.5 0 0 0-.74.56z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 3.824 3.898 7.293 7.435" fill="none"/>
    </g>
    <g clip-path="url(#c)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .54 2.767-1.219 5.113"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 5.634 0 11.407 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="100">
    <defs>
      <clipPath id="d">
        <path d="M10.64 13.3c0 0-1.72 3.6-1.72 3.6 0 0 .********* 0 0 3.6 1.72 3.6 1.72a.5.5 0 0 0 .69-.62c0 0-.69-1.94-.69-1.94a6.95 6.95 0 0 0 3.65-3.45c1.66-3.48.18-7.67-3.3-9.33-3.48-1.66-7.67-.18-9.33 3.3a6.97 6.97 0 0 0 2.71 ********** 0 0 0 .86-.21.64.64 0 0 0-.21-.86 5.73 5.73 0 0 1-2.22-7.4 5.76 5.76 0 0 1 7.67-2.71 5.76 5.76 0 0 1 2.71 7.67c-.62 1.3-1.67 2.28-2.94 2.81 0 0-.68-1.94-.68-1.94a.5.5 0 0 0-.92-.05z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#a)">
      <path stroke="context-fill" stroke-width="4" d="M3.5 3.438c0 0 .976.995 2.388 2.435" fill="none"/>
    </g>
    <g clip-path="url(#d)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-.354.032-.69.033-1.01.008"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-.126 0-.36"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="120">
    <defs>
      <clipPath id="e">
        <path d="M8.39 12.95c0 0-3.62 1.68-3.62 1.68 0 0-.15.4-.15.4 0 0 1.68 3.62 1.68 3.62a.5.5 0 0 0 .92-.04c0 0 .71-1.94.71-1.94a6.95 6.95 0 0 0 5.01-.33c3.5-1.63 5.02-5.8 3.4-9.3-1.63-3.5-5.8-5.02-9.3-3.4a6.97 6.97 0 0 0-3.65 ********** 0 0 0 .********** 0 0 0 .38-.8 5.73 5.73 0 0 1 3-7.12 5.76 5.76 0 0 1 7.64 2.79 5.76 5.76 0 0 1-2.79 7.64c-1.3.61-2.74.69-4.06.29 0 0 .71-1.93.71-1.93a.5.5 0 0 0-.68-.63z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#e)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-2.086.187-3.57-.687-4.585-1.748"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-6.412 0-12.563"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="140">
    <defs>
      <clipPath id="f">
        <path d="M6.91 11.33c0 0-3.89-.91-3.89-.91 0 0-.36.22-.36.22 0 0-.91 3.89-.91 3.89a.5.5 0 0 0 .75.54c0 0 1.75-1.09 1.75-1.09a6.95 6.95 0 0 0 4.15 2.83c3.76.88 7.53-1.46 8.41-5.22.88-3.76-1.46-7.53-5.22-8.41a6.97 6.97 0 0 0-8.23 ********** 0 0 0 .*********** 0 0 0 .79-.39 5.73 5.73 0 0 1 6.76-3.75 5.76 5.76 0 0 1 4.28 6.91 5.76 5.76 0 0 1-6.91 4.28c-1.4-.33-2.58-1.15-3.38-2.28 0 0 1.75-1.08 1.75-1.08a.5.5 0 0 0-.15-.91z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#f)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-.012-.039-.029-.11"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-.119 0-.34 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="160">
    <defs>
      <clipPath id="g">
        <path d="M6.72 9.25c0 0-2.6-3.03-2.6-3.03 0 0-.42-.03-.42-.03 0 0-3.03 2.6-3.03 2.6a.5.5 0 0 0 .29.88c0 0 2.06.15 2.06.15a6.95 6.95 0 0 0 1.68 4.73c2.52 2.92 6.95 3.26 9.87.74 2.92-2.52 3.26-6.95.74-9.87a6.97 6.97 0 0 0-9.34-********** 0 0 0-.*********** 0 0 0 .87.15 5.73 5.73 0 0 1 7.67.95 5.76 5.76 0 0 1-.61 8.11 5.76 5.76 0 0 1-8.11-.61c-.94-1.09-1.41-2.45-1.39-3.83 0 0 2.05.15 2.05.15a.5.5 0 0 0 .42-.83z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#g)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-.594-1.947.692-3.396"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-5.246 0-10.801 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="180">
    <defs>
      <clipPath id="h">
        <path d="M7.67 7.57c0 0-.51-3.96-.51-3.96 0 0-.34-.26-.34-.26 0 0-3.96.51-3.96.51a.5.5 0 0 0-.24.89c0 0 1.63 1.26 1.63 1.26a6.95 6.95 0 0 0-1.2 4.88c.49 3.83 4 6.55 7.83 6.06 3.83-.49 6.55-4 6.06-7.83a6.97 6.97 0 0 0-7.15-********** 0 0 0-.*********** 0 0 0 .64.61 5.73 5.73 0 0 1 5.87 5.02 5.76 5.76 0 0 1-4.98 6.43 5.76 5.76 0 0 1-6.43-4.98c-.18-1.43.18-2.82.95-3.96 0 0 1.62 1.26 1.62 1.26a.5.5 0 0 0 .8-.46z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#h)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.178-3.862 3.376-4.758"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-13.306 0-19.972 0"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="200">
    <defs>
      <clipPath id="i">
        <path d="M9.21 6.73c0 0 1.55-3.68 1.55-3.68 0 0-.16-.39-.16-.39 0 0-3.68-1.55-3.68-1.55a.5.5 0 0 0-.66.65c0 0 .78 1.91.78 1.91a6.95 6.95 0 0 0-3.49 3.61c-1.5 3.55.17 7.67 3.72 9.17 3.55 1.5 7.67-.17 9.17-3.72a6.97 6.97 0 0 0-3.11-********** 0 0 0-.*********** 0 0 0 .25.85 5.73 5.73 0 0 1 2.55 7.29 5.76 5.76 0 0 1-7.53 3.06 5.76 5.76 0 0 1-3.06-7.53c.56-1.33 1.57-2.35 2.81-2.94 0 0 .77 1.9.77 1.9a.5.5 0 0 0 .92.01z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#i)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.562-5.121 5.865-4.937"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-23.686 0-23.686 0 0 0 0 1.511 0 3.77"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="220">
    <defs>
      <clipPath id="j">
        <path d="M10.73 6.72c0 0 3.01-2.62 3.01-2.62 0 0 .03-.42.03-.42 0 0-2.62-3.01-2.62-3.01a.5.5 0 0 0-.88.29c0 0-.14 2.06-.14 2.06a6.95 6.95 0 0 0-4.73 1.71c-2.91 2.53-3.23 6.96-.7 9.88 2.53 2.91 6.96 3.23 9.88.7a6.97 6.97 0 0 0 1.12-********** 0 0 0-.87-.15.64.64 0 0 0-.15.87 5.73 5.73 0 0 1-.92 7.67 5.76 5.76 0 0 1-8.11-.57 5.76 5.76 0 0 1 .57-8.11c1.09-.94 2.44-1.42 3.82-1.4 0 0-.15 2.05-.15 2.05a.5.5 0 0 0 .83.41z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#j)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.563-5.125 5.875-4.937.616 0 1.172.18 1.659.449"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-23.686 0-23.686 0 0 0 0 4.164 0 9.001"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="240">
    <defs>
      <clipPath id="k">
        <path d="M11.86 7.2c0 0 3.75-1.36 3.75-1.36 0 0 .18-.38.18-.38 0 0-1.36-3.75-1.36-3.75a.5.5 0 0 0-.92-.04c0 0-.87 1.87-.87 1.87a6.95 6.95 0 0 0-5.02-.1c-3.63 1.32-5.51 5.34-4.19 8.97 1.32 3.63 5.34 5.51 8.97 4.19a6.97 6.97 0 0 0 4.39-********** 0 0 0-.76-.45.64.64 0 0 0-.45.76 5.73 5.73 0 0 1-3.61 6.83 5.76 5.76 0 0 1-7.37-3.44 5.76 5.76 0 0 1 3.44-7.37c1.35-.49 2.79-.45 4.07.06 0 0-.87 1.86-.87 1.86a.5.5 0 0 0 .62.68z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#k)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.563-5.125 5.875-4.937.873 0 1.626.361 2.233.825"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-23.686 0-23.686 0 0 0 0 5.409 0 11.057"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="260">
    <defs>
      <clipPath id="l">
        <path d="M12.5 7.75c0 0 3.97-.39 3.97-.39 0 0 .27-.33.27-.33 0 0-.39-3.97-.39-3.97a.5.5 0 0 0-.88-.27c0 0-1.31 1.59-1.31 1.59a6.95 6.95 0 0 0-4.84-1.35c-3.84.37-6.66 3.8-6.29 7.64.37 3.84 3.8 6.66 7.64 6.29a6.97 6.97 0 0 0 6.33-********** 0 0 0-.62-.62.64.64 0 0 0-.63.63 5.73 5.73 0 0 1-5.2 5.72 5.76 5.76 0 0 1-6.28-5.17 5.76 5.76 0 0 1 5.17-6.28c1.43-.14 2.81.26 3.93 1.07 0 0-1.31 1.58-1.31 1.58a.5.5 0 0 0 .43.82z"/>
      </clipPath>
    </defs>
    <g clip-path="url(#l)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.563-5.125 5.875-4.937.873 0 1.626.361 2.233.825"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-23.686 0-23.686 0 0 0 0 5.409 0 11.057"/>
    </g>
  </svg>
  <svg viewBox="0 0 20 20" width="20" height="20" x="280">
    <g clip-path="url(#m)" stroke="context-fill" stroke-width="8" fill="none">
      <path d="M13.358 6.013c1.087.831 1.704 1.991 1.704 1.991 0 0 .938 4.809-3.687 7.121-4.875.438-6.461-4.922-6.461-4.922m.336-.078c0 0-1.563-5.125 5.875-4.937.873 0 1.626.361 2.233.825"/>
      <path d="M-1.875 9.432c0 6.171 0 12.629 0 12.629 0 0 23.686 0 23.686 0 0 0 0-23.686 0-23.686 0 0-23.686 0-23.686 0 0 0 0 5.409 0 11.057"/>
    </g>
  </svg>
</svg>
