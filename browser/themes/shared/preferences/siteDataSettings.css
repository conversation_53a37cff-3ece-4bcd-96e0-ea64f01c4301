/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/**
 * Site Data - Settings dialog
 */
#sitesList {
  flex: 1 auto;
  height: 20em;
}

/* Show selected items in high contrast mode. */
#sitesList > richlistitem[selected] {
  outline: 1px solid transparent;
}

#sitesList > richlistitem > hbox,
.item-box > label {
  flex: 1;
}

.item-box {
  padding: 5px 8px;
  align-items: center;
  width: 50px;
}

/**
 * Confirmation dialog of removing sites selected
 */
#SiteDataRemoveSelectedDialog {
  padding: 16px;
  min-width: 50px;
}

#SiteDataRemoveSelectedDialog.single-entry {
  min-height: 8em;
}

#SiteDataRemoveSelectedDialog.single-entry .multi-site {
  display: none;
}

#SiteDataRemoveSelectedDialog.multi-entry #removalList {
  max-height: 20em;
}

#SiteDataRemoveSelectedDialog > dialog {
  padding: 0; /* override dialog.css */
}

#contentContainer {
  font-size: 1.2em;
  margin-bottom: 10px;
}

.question-icon {
  margin: 6px;
}

#removing-label {
  font-weight: bold;
}
