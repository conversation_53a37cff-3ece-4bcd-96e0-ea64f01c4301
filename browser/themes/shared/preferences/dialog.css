/* - This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this file,
   - You can obtain one at http://mozilla.org/MPL/2.0/. */

window,
dialog {
  appearance: none;
  background-color: var(--in-content-page-background);
  color: var(--in-content-page-color);
  margin: 0;
  padding: 0;
}

/* Add some padding around the contents of the dialog */
dialog {
  padding: 6px;
}

.contentPane,
dialog::part(content-box) {
  flex: 1;
  /* This allows the focus ring to display fully when scrolling is enabled. */
  padding: 4px;
}

.contentPane.doScroll,
dialog.doScroll::part(content-box) {
  overflow-y: auto;
  contain: size;
}

tree:not(#rejectsTree) {
  min-height: 15em;
}

.actionButtons {
  margin: 3px 0 0;
}

menulist label {
  font-weight: unset;
}
