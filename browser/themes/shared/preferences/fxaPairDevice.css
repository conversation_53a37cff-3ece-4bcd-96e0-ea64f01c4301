/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#fxaPairDeviceDialog {
  padding-bottom: 2em;
}

#pairTitle {
  font-size: 2.1rem;
  font-weight: bold;
  padding-top: 12px;
  text-align: center;
}

.pairHeading {
  font-size: 1.08em;
}

.menu-icon {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  -moz-context-properties: fill;
  fill: currentColor;
}

#qrCodeDisplay {
  width: 450px;
  margin: auto;
}

#qrWrapper {
  position: relative;
  width: 200px;
  height: 200px;
  margin: auto;
  margin-top: 28px;
}

#qrContainer {
  height: 200px;
  width: 200px;
  background-size: contain;
  image-rendering: -moz-crisp-edges;
  transition: filter 250ms cubic-bezier(.07,.95,0,1);
}

#qrWrapper:not([pairing-status="ready"]) #qrContainer {
  opacity: 0.05;
  filter: blur(3px);
}

#qrWrapper:not([pairing-status="loading"]) #qrSpinner {
  opacity: 0;
}

#qrSpinner {
  display: block;
  position: absolute;
  height: 250px;
  width: 250px;
}

#qrSpinner::before {
  background-image: url("chrome://browser/skin/fxa/fxa-spinner.svg");
  animation: 0.9s spin infinite linear;
  background-size: 36px;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  position: absolute;
  height: 200px;
  width: 200px;
  content: "";
  transition: opacity 250ms cubic-bezier(.07,.95,0,1);
  will-change: transform;
  margin-inline: auto;
}

#qrWrapper:not([pairing-status="error"]) #qrError {
  display: none;
}

#qrError {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 200px; /* Same as #qrContainer */
  position: absolute;
  inset: 0;
  margin-inline: auto;
  transition: opacity 250ms cubic-bezier(.07,.95,0,1);
  cursor: pointer;
}

.qr-error-text {
  text-align: center;
  user-select: none;
  display: block;
  color: #2484C6;
  cursor: pointer;
}

#refresh-qr {
  width: 36px;
  height: 36px;
  background-image: url("chrome://global/skin/icons/reload.svg");
  background-size: contain;
  -moz-context-properties: fill;
  fill: #2484C6;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
