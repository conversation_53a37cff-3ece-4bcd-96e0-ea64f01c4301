/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

/* Permissions */

.highlighting-group {
  --section-highlight-background-color: color-mix(in srgb, var(--color-accent-primary) 20%, transparent);
}

.permission-icon {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-inline-end: var(--space-xsmall);

  + label {
    vertical-align: middle;
  }
}

.permission-icon,
.privacy-detailedoption .checkbox-label-box,
.extra-information-label > image,
.arrowhead,
.reload-tabs-button,
.content-blocking-warning-image {
  -moz-context-properties: fill;
  fill: currentColor;
}

.geo-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/geo.svg);
}

.xr-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/xr.svg);
}

.camera-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/camera.svg);
}

.microphone-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/microphone.svg);
}

.speaker-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/speaker.svg);
}

.desktop-notification-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/desktop-notification.svg);
}

.autoplay-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/autoplay-media.svg);
}

.midi-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/midi.svg);
}

/* Content Blocking */

/* Override styling that sets descriptions as grey */
#trackingGroup description.indent,
#trackingGroup .indent > description {
  color: inherit;
}

[data-subcategory="trackingprotection"] {
  margin-top: 10px;
}

#trackingProtectionShield {
  list-style-image: url("chrome://browser/skin/controlcenter/tracking-protection.svg");
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: 0.5;
  width: 64px;
  height: 64px;
  margin-inline-end: 10px;
}

.privacy-detailedoption,
#fpiIncompatibilityWarning,
#rfpIncompatibilityWarning {
  margin: 3px 0;
}

#fpiIncompatibilityWarning,
#rfpIncompatibilityWarning {
  background-color: var(--section-highlight-background-color);
}

#fpiIncompatibilityWarning,
#rfpIncompatibilityWarning,
#contentBlockingCategories {
  margin-top: 16px;
}

.privacy-detailedoption {
  border-color: var(--in-content-box-border-color);
}

.privacy-detailedoption.disabled {
  opacity: 0.5;
}

.privacy-detailedoption.disabled .radio-check {
  opacity: 1;
}

.privacy-detailedoption > .indent {
  margin-inline-end: 22px;
}

.arrowhead {
  border-radius: 2px;
  min-height: 20px;
  min-width: 20px;
  max-height: 20px;
  max-width: 20px;
  list-style-image: url("chrome://global/skin/icons/arrow-down-12.svg");
  background-color: transparent;
  padding: 3px;
}

.arrowhead:not([disabled]):hover {
  cursor: pointer;
}

.arrowhead.up {
  list-style-image: url("chrome://global/skin/icons/arrow-up-12.svg");
}

.privacy-detailedoption.expanded:not(.selected) .reload-tabs {
  display: none;
}

.content-blocking-warning.reload-tabs:not([hidden]) {
  display: flex;
  flex-wrap: wrap;
}

.content-blocking-reload-desc-container {
  /* 345px is enough for (almost) twice the size of the default English
   * string at default font sizes. If this and the button do not fit
   * on one line, the button will get wrapped to the next line and this
   * item will stretch to fill all available space. */
  flex: 1 1 345px;
  display: flex;
  align-self: center;
}

.content-blocking-reload-description {
  margin-inline-end: 5px;
}

.reload-tabs-button {
  max-height: 30px;
  min-height: 30px;
  padding: 0 20px;
  list-style-image: url("chrome://global/skin/icons/reload.svg");
  align-self: center;
}

.reload-tabs-button .button-icon {
  margin: 0 6px;
  color: inherit;
}

.reload-tabs-button .button-text {
  color: inherit;
}

.privacy-detailedoption.selected .content-blocking-warning {
  background-color: var(--section-highlight-background-color);
}

.privacy-detailedoption.selected .arrowhead {
  display: none;
}

.privacy-detailedoption.selected {
  border-color: var(--color-accent-primary);
}

@media (forced-colors) {
  .privacy-detailedoption.selected {
    outline: 2px solid var(--color-accent-primary);
  }
}

@media not (forced-colors) {
  .privacy-detailedoption {
    background-color: rgba(215, 215, 219, 0.1);
  }

  .privacy-detailedoption.selected {
    background-color: var(--section-highlight-background-color);
  }
}

.content-blocking-warning-title,
.privacy-detailedoption .radio-label-box {
  font-weight: bold;
}

.privacy-detailedoption:not(.expanded, .selected) .privacy-extra-information {
  visibility: collapse;
}

.extra-information-label {
  margin-top: 18px;
}

/* Apply display: block to the containers of all the category information, as
 * without this the flex-wrapped blocks inside don't stretch vertically to
 * enclose their content. */
.privacy-detailedoption > .indent {
  display: block;
}

.privacy-detailedoption.expanded .privacy-extra-information,
.privacy-detailedoption.selected .privacy-extra-information {
  display: flex;
  flex-direction: column;
  align-content: stretch;
}

.privacy-extra-information > .indent {
  margin-bottom: 18px;
}

.privacy-extra-information > .custom-option {
  margin: 10px 0;
}

.content-blocking-warning {
  /* Match .indent's horizontal padding, minus the border added via .info-box-container: */
  padding-inline: calc(22px - 1px);
  margin: 10px 0;
}

.content-blocking-warning:not([hidden]) + .content-blocking-warning {
  margin-top: 0;
}

.content-blocking-extra-blocking-desc {
  margin-inline-start: 0.5em;
  position: relative;
  top: 0.65em;
}

.content-blocking-label,
.doh-label {
  display: list-item;
}

#dohExceptionsButton {
  align-self: end;
}

.content-blocking-warning-image {
  list-style-image: url("chrome://global/skin/icons/warning.svg");
  width: 16px;
  margin-inline-end: 6px;
}

#blockCookiesMenu {
  width: 450px;
}

#blockCookiesMenu,
#trackingProtectionMenu,
#fingerprintingProtectionMenu {
  margin: 0;
}

#changeBlockListLink {
  margin-inline-start: 56px;
}

#telemetry-container {
  margin-block: 4px;
  width: 100%;
}

.privacy-detailedoption-description {
  font-size: 90%;
  opacity: 0.6;
}

.warning-description {
  background: url(chrome://global/skin/icons/warning.svg) no-repeat 0 5px;
  opacity: 0.6;
  -moz-context-properties: fill, stroke;
  fill: #d7b600;
  stroke: white;
  padding-inline-start: 20px;
}

.reject-trackers-warning-icon:-moz-locale-dir(rtl) {
  background-position-x: right 0;
}

#contentBlockingChangeCookieSettings {
  padding: 0.25em 0.75em;
  margin: 4px 8px;
}

#deleteOnCloseNote {
  margin-top: 8px;
  margin-inline-end: 32px;
}

#submitHealthReportBox {
  display: inline-flex;
}

/* Address bar */

.firefoxSuggestOptionBox {
  /* With this value we're trying to keep the apparent vertical space between
     option boxes the same as the apparent vertical space between the labels of
     the checkboxes above the Firefox Suggest subsection. */
  margin-block-start: 18px;
}

#firefoxSuggestInfoBox {
  margin-block-start: 16px;
}

#openSearchEnginePreferences {
  margin-block-start: .5em;
}

#dismissedSuggestions,
#openSearchEnginePreferences.extraMargin {
  margin-block-start: 2em;
}

/* Logins and Passwords */

#openWindowsPasskeySettings {
  margin-block-start: 2em;
}
