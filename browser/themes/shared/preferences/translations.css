/* Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#translations-settings-header {
  margin-top: var(--space-xlarge);
  margin-bottom: calc( 2 * var(--space-small));
}

.translations-settings-manage-section {
  margin-top: var(--space-xlarge);
}

.translations-settings-manage-language {
  margin: 0 calc( 2 * var(--space-small));
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.translations-settings-manage-section-info {
  display: flex;
  flex-direction: column;
  h2, p, a {
    display: block;
    margin: var(--space-small) calc( 2 * var(--space-small));
  }
  a {
    display: block;
  }
}

.translations-settings-languages-card {
  flex-direction: column;
  max-height: calc( 14 * var(--space-xlarge));
  padding-inline: calc( 2 * var(--space-small));

  &[hidden] {
    display: none;
  }
  &:not([hidden]) {
    display: flex;
  }
}

.translations-settings-language-header {
  margin: calc( 2 * var(--space-small)) 0;
  font-size: var(--font-size-root);
  font-weight: var(--font-weight-bold);
}

.translations-settings-language-list {
  overflow: auto;
}

.translations-settings-language {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: var(--space-small) 0;
  border-top: 1px solid var(--in-content-border-color);
  label {
    margin: 0 calc( 2 * var(--space-small));
  }
}
.translations-settings-language-error {
  display: inline-block;
  flex: 0 1 100%;
}
.translations-settings-download-icon[type~="icon"]::part(button) {
  background-image: url(chrome://browser/skin/downloads/downloads.svg);
}

.translations-settings-remove-icon[type~="icon"]::part(button) {
  background-image: url(chrome://global/skin/icons/delete.svg);
}

.translations-settings-loading-icon[type~="icon"]::part(button) {
  background-image: url(chrome://global/skin/icons/loading.svg);
}

.translations-settings-download-size {
  color: var(--text-color-deemphasized);
}
