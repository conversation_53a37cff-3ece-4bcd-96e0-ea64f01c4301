/* - This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this file,
   - You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html "http://www.w3.org/1999/xhtml";

/* Global rules */
:root {
  user-select: text;
}

#preferences-root {
  text-rendering: optimizeLegibility;
}

@media not print {
  #preferences-root,
  #preferences-body {
    height: 100%;
    overflow: hidden;
  }
}

#preferences-root,
#preferences-body {
  margin: 0;
  padding: 0;
  width: 100%;
}

#preferences-body {
  vertical-align: top; /* See bug 1606130 */
  display: flex;
  flex-direction: column;

}

/*
  The stack has some very tall content but it shouldn't be taller than the
  viewport (and we know the tall content will still be accessible via scrollbars
  regardless if the stack has a shorter height). So we use min-height: 0 to allow
  the stack to be smaller than its content height so it can fit the viewport size.
*/
#preferences-stack {
  min-height: 0;
}

.main-content {
  padding-top: 0;
  /* the 32px min-height, plus 20+30px margin below, plus some leeway: */
  scroll-padding-top: 90px;
  contain: inline-size;
}

.pane-container {
  /* A workaround to keep the container always float on the `top: 0` (Bug 1377009) */
  display: block;
  width: min(100%, var(--page-main-content-width));
  min-width: min-content;
}

#mainPrefPane {
  width: 100%;
  padding: 0;
}

html|div[data-category],
groupbox[data-category] {
  margin: 0 0 32px;
}

html|h1 {
  font-size: var(--font-size-xlarge);
  margin: 0 0 var(--space-small);
}

html|h2 {
  font-size: var(--font-size-large);
  margin: var(--space-small) 0 var(--space-xxsmall) 0;
}

description.indent,
.indent:not(#browserPrivateEngineSelection) > description {
  color: var(--text-color-deemphasized);

  /* Ensures margin on checkboxes, radios, and description don't create a big gap between the input and indented description */
  margin-top: calc(var(--space-xsmall) * -1);
}

button,
treecol,
menulist,
html|select {
  /* override the root rule */
  user-select: none;
}

/* Buttons get a default min-width in XUL. When they also have flex, they will
 * be large enough anyway, and we don't want them to become smaller than their inner
 * text which would then overflow the button. Set min-width back to auto for that:
 */
button[flex] {
  min-width: auto;
}

description,
label {
  margin: var(--space-xxsmall) 0;
}

label {
  margin: var(--space-xxsmall) 0;

  &.tail-with-learn-more + a {
    margin-block: var(--space-xxsmall);
    margin-inline: var(--space-small) 0;
  }
}

.description-deemphasized {
  color: var(--text-color-deemphasized);
}

.tip-caption {
  font-size: .9em;
  color: var(--text-color-deemphasized);
}

menulist::part(label),
menuitem > label,
button > hbox > label {
  line-height: unset;
}

.indent {
  margin-inline-start: calc(var(--checkbox-size) + var(--checkbox-margin-inline)) !important;
}

checkbox,
radio {
  margin: var(--space-small) 0;

  &.tail-with-learn-more + a {
    margin-block: var(--space-small);
    margin-inline: var(--space-small) 0;
  }
}

/* Add a bit of space to the end of descriptions to
 * leave margin with e.g. additional buttons on the side. */
.description-with-side-element {
  margin-inline-end: 10px !important;
}

.learnMore {
  margin-inline-start: 0;
  font-weight: normal;
  white-space: nowrap;
}

.accessory-button {
  min-width: 150px;
  margin: 4px 0;
}

.spotlight {
  background-color: color-mix(in srgb, var(--color-accent-primary) 15%, transparent);
  outline: var(--color-accent-primary) 1px dotted;
  animation: fade-out 0.5s ease-out 2s forwards;
  /* Show the border to spotlight the components in high-contrast mode. */
  border: 1px solid transparent;
  border-radius: 2px;
}

@keyframes fade-out {
  100% {
    background-color: transparent;
    outline-color: transparent;
  }
}

[data-subcategory] {
  margin-inline: -4px;
  padding-inline: 4px;
}

.ac-site-icon {
  display: flex;
}

/* Subcategory title */

/**
 * The first subcategory title for each category should not have margin-top.
 */

.subcategory:not([hidden]) ~ .subcategory {
  padding-top: var(--space-xxlarge);
  border-top: 1px solid var(--in-content-border-color);
}

/* Category List */

#categories {
  overflow: visible;
  min-height: auto;
  padding: 1px; /* Adding padding around richlistitem in order to make entire keyboard focusing outline visible */
}

#category-general > .category-icon {
  list-style-image: url("chrome://browser/skin/preferences/category-general.svg");
}

#category-home > .category-icon {
  list-style-image: url("chrome://browser/skin/home.svg");
}

#category-search > .category-icon {
  list-style-image: url("chrome://browser/skin/preferences/category-search.svg");
}

#category-privacy > .category-icon {
  list-style-image: url("chrome://browser/skin/preferences/category-privacy-security.svg");
}

#category-sync > .category-icon {
  list-style-image: url("chrome://browser/skin/preferences/category-sync.svg");
}

#category-experimental > .category-icon {
  list-style-image: url("chrome://global/skin/icons/experiments.svg");
}

#category-more-from-mozilla > .category-icon {
  list-style-image: url("chrome://browser/skin/preferences/mozilla-logo.svg");
}

/* All panes */

.info-icon,
.androidIcon,
.iOSIcon {
  -moz-context-properties: fill;
  fill: currentColor;
}

/* General Pane */

.is-default > #isNotDefaultPane,
:not(.is-default) > #isDefaultPane {
  visibility: hidden;
}

#isDefaultLabel,
#isNotDefaultLabel {
  font-weight: var(--font-weight-bold);
}

/* stylelint-disable-next-line media-query-no-invalid */
@media not (-moz-bool-pref: "browser.migrate.preferences-entrypoint.enabled") {
  #dataMigrationGroup {
    display: none;
  }
}

#dataMigration {
  /* This should really be using baseline, but that doesn't work properly with
   * the <description> and XUL <button> markup. */
  align-items: center;
}

#browserContainersExtensionContent {
  padding-block: 5px;
}

.extension-controlled {
  margin-block: 18px;
}

.extension-controlled-icon {
  height: 20px;
  margin: 2px 0 6px;
  vertical-align: middle;
  width: 20px;
  -moz-context-properties: fill;
  fill: currentColor;
}

.extension-controlled-disabled {
  -moz-context-properties: fill;
  color: GrayText;
  fill: currentColor;
}

.extension-controlled-disabled > .extension-controlled-button {
  display: none;
}

.extension-controlled-icon.close-icon {
  height: 30px;
  width: 30px;
  margin-inline-start: 5px;
}

#getStarted {
  font-size: 90%;
}

#colors {
  margin-inline-start: 5px;
}

#FontsDialog menulist:empty {
  /* When there's no menupopup, while the font builder is running,
   * the height of the menulist would otherwise shrink. This throws
   * off the height determination SubDialog.sys.mjs does, which then
   * leads to content being cut off. Fix by enforcing a minimum height
   * when there's no content: */
  min-height: max(32px, calc(1.4em + 16px));
}

#useDocumentColors {
  margin: 4px 0;
}

#activeLanguages > richlistitem {
  padding: 0.3em;
}

#saveWhere {
  align-items: center;
}

#saveTo {
  margin-inline-end: 1em;
}

#downloadFolder {
  flex: 1;
  margin-block: 4px;
  margin-inline-start: 0;
  padding-inline-start: 30px;
  background-repeat: no-repeat;
  background-size: 16px;
  background-position: center left 8px;
}

#downloadFolder:-moz-locale-dir(rtl) {
  background-position-x: right 8px;
}

#updateBox {
  margin-block: 4px 32px;
}

/* Work around bug 560067 - animated images in visibility: hidden
 * still eat CPU. */
#updateDeck > *:not(.deck-selected) > .update-throbber {
  display: none;
}

#updateDeck > hbox > button {
  margin-block: 0;
  margin-inline-end: 0;
}

#updateDeck > hbox > label {
  flex: 1;
  margin-inline-end: 5px !important;
}

.manualLink {
  margin-inline-start: 6px !important;
}

#updateRadioGroup > radio {
  min-height: 30px;
  margin: 2px 0;
}

#filter {
  margin: 4px 0 8px;
}

#handlersView {
  height: 25em;
  margin-inline-end: 0;
}

#handlersView > richlistitem {
  min-height: 30px;
}

#handlersView .actionsMenu {
  margin: 0;
  min-height: 0;
  padding: 0;
  border: none;
  padding-inline-end: 8px;
}

#handlersView .actionsMenu::part(label) {
  margin: 0;
}

.typeIcon,
.actionIcon,
.actionsMenu::part(icon) {
  height: 16px;
  width: 16px;
}

.typeIcon {
  margin-inline: 10px 9px !important;
}

#handlersView .actionsMenu::part(icon),
.actionIcon {
  margin-inline: 11px 8px !important;
}

.actionsMenu > menupopup > menuitem {
  padding-inline-start: 10px !important;
}

.actionsMenu > menupopup > menuitem > .menu-iconic-left {
  margin-inline-end: 8px !important;
}

/* Increase the specifity to override common.inc.css */
richlistitem[selected] .actionsMenu:focus-visible {
  outline-offset: -3px;
}

/* Home Pane */

#homepageGroup menulist,
#homepageGroup html|input {
  margin: var(--space-xsmall) 0;
}

#homepageGroup html|input {
  text-align: match-parent;
}

#homepageGroup:-moz-locale-dir(rtl) html|input {
  unicode-bidi: plaintext;
}

#homepageAndNewWindowsOption {
  margin-top: 20px;
}

#homepageGroup:-moz-locale-dir(rtl) panel[type="autocomplete-richlistbox"] {
  direction: rtl;
}

.homepage-buttons > .homepage-button {
  flex: 1 auto;
}

.homepage-button:first-of-type {
  margin-inline-start: 0;
}

.homepage-button:last-of-type {
  margin-inline-end: 0;
}

#homeContentsGroup .checkbox-icon[src] {
  -moz-context-properties: fill;
  fill: currentColor;
  margin-inline-end: var(--checkbox-margin-inline);
  width: 16px;
}

#homeContentsGroup > [data-subcategory] {
  margin-top: 14px;
}

#homeContentsGroup > [data-subcategory] .section-checkbox {
  font-weight: var(--font-weight-bold);
}

#homeContentsGroup > [data-subcategory] > vbox menulist {
  margin-block: 0;
}

/* Align fix for prefs that have a text link in the headline */
#homeContentsGroup > [data-subcategory] > hbox {
  align-items: last baseline;
}

#homeContentsGroup .checkbox-label {
  margin-inline-end: var(--space-small);
}

.addon-with-favicon .menu-iconic-icon {
  margin-inline-start: 0;
}

a[is="moz-support-link"]:not(.sidebar-footer-link) {
  display: inline-block;
  white-space: initial;
}

/* Search Pane */

#engineList {
  margin: 2px 0 5px;
}

#engineList > treechildren::-moz-tree-image(engineShown, checked),
#blocklistsTree > treechildren::-moz-tree-image(selectionCol, checked) {
  list-style-image: url("chrome://global/skin/icons/check.svg");
  -moz-context-properties: fill, stroke;
  fill: var(--color-accent-primary);
}

#engineList > treechildren::-moz-tree-image(engineShown, checked, selected),
#blocklistsTree > treechildren::-moz-tree-image(selectionCol, checked, selected) {
  fill: var(--in-content-item-selected-text);
  stroke: var(--in-content-item-selected);
}

@media (forced-colors) {
  #engineList > treechildren::-moz-tree-image(hover),
  #blocklistsTree > treechildren::-moz-tree-image(hover) {
    fill: var(--in-content-item-hover-text);
    stroke: var(--in-content-item-hover);
  }
}

#engineList > treechildren::-moz-tree-row,
#blocklistsTree > treechildren::-moz-tree-row {
  min-height: 36px;
}

#selectionCol {
  min-width: 26px;
}

#addEnginesBox {
  margin: 4px 0 0;
}

/* Privacy Pane */

#allowSmartSize {
  margin-block: 0 4px;
}

#historyPane {
  margin-top: 4px;
}

#historyButtons {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

#blockCookies,
#keepRow {
  margin-top: 1.5em;
}

/* Collapse the non-active vboxes in decks to use only the height the
   active vbox needs */
#historyPane:not([selectedIndex="1"]) > #historyDontRememberPane,
#historyPane:not([selectedIndex="2"]) > #historyCustomPane,
#weavePrefsDeck:not([selectedIndex="1"]) > #hasFxaAccount,
#fxaLoginStatus:not([selectedIndex="1"]) > #fxaLoginUnverified,
#fxaLoginStatus:not([selectedIndex="2"]) > #fxaLoginRejected {
  visibility: collapse;
}

#tabsElement {
  margin-inline-end: 4px; /* add the 4px end-margin of other elements */
}

.text-link {
  margin-bottom: 0;
}

.text-link[disabled="true"] {
  pointer-events: none;
}

html|label[disabled] {
  /* This matches XUL checkbox.css - but for HTML labels for HTML inputs we
   * need to do this ourselves. */
  opacity: 0.4;
}

#showUpdateHistory {
  margin-inline-start: 0;
}

/**
 * Dialog
 */

.dialogOverlay {
  visibility: hidden;
}

html|dialog {
  padding: var(--space-xlarge);
}

html|dialog::backdrop,
.dialogOverlay[topmost="true"] {
  background-color: rgba(0,0,0,0.5);
}

html|dialog,
.dialogBox {
  border: 1px solid transparent;
  border-radius: 8px;
  box-shadow: 0 2px 6px 0 rgba(0,0,0,0.3);
}

.dialogBox {
  background-color: var(--in-content-page-background);
  background-clip: content-box;
  color: var(--in-content-page-color);
  /* `transparent` will use the dialogText color in high-contrast themes and
     when page colors are disabled */
  display: flex;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}

@media (prefers-color-scheme: dark) {
  @media not (forced-colors) {
    html|dialog,
    .dialogBox {
      --in-content-page-background: #42414d;
    }
  }
}

.dialogBox[resizable="true"] {
  resize: both;
  overflow: hidden;
  min-height: 20em;
  min-width: 66ch;
}

.dialogTitleBar {
  padding: .4em .2em;
  border-bottom: 1px solid var(--in-content-border-color);
}

.dialogTitle {
  font-weight: var(--font-weight-bold);
  text-align: center;
  user-select: none;
}

.fxaPairDeviceIcon {
  background-image: url("chrome://browser/content/logos/fxa-logo.svg");
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  height: 80px;
  padding-top: 42px;
  margin-top: -40px;
}

.fxaPairDeviceIcon .close-icon {
  margin-top: 10px;
}

.close-icon {
  border: none;
  background-color: transparent; /* override common.css, but keep hover/active states */
  min-width: 0;
  min-height: auto;
  width: 20px;
  height: 20px;
  padding: 0;
  margin-inline: 0 8px;
}

.dialogFrame {
  margin: 10px;
  flex: 1;
  /* Default dialog dimensions */
  min-width: 66ch;
}

/**
 * End Dialog
 */

/**
 * Font group and font dialog menulist fixes
 */

#font-chooser-group {
  display: grid;
  grid-template-columns: max-content 1fr max-content max-content;
}

#defaultFont {
  flex: 1 25ch;
}

#defaultFontType,
#serif,
#sans-serif,
#monospace {
  width: 0; /* Might be able to be removed */
  min-width: 30ch;
}

/**
 * Sync
 */

.fxaProfileImage {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 1px solid transparent;
  list-style-image: url(chrome://browser/skin/fxa/avatar-color.svg);
  margin-inline-end: var(--space-xlarge);
  -moz-user-focus: normal;
}

.fxaProfileImage.actionable {
  cursor: pointer;
}

.fxaProfileImage.actionable:hover {
  border-color: #0095DD;
}

.fxaProfileImage.actionable:hover:active {
  border-color: #ff9500;
}

#useFirefoxSync {
  font-size: 90%;
  margin-inline-end: 8px !important;
}

#fxaNoLoginStatus {
  margin-block: 46px 64px;
}

#fxaSyncComputerName {
  flex: 1;
  margin-block: 4px;
  margin-inline-start: 0;
}

#fxaChangeDeviceName {
  margin-block: 4px;
  margin-inline-end: 0;
}

#noFxaAccount > label:first-child {
  margin-bottom: 0.6em;
}

#noFxaCaption {
  margin-block: 0 4px;
}

#noFxaSignIn {
  margin-inline-start: 8px;
}

.fxaSyncIllustration {
  list-style-image: url(chrome://browser/skin/fxa/sync-devices.svg);
  width: 312px;
  height: 136px;
}

#noFxaDescription {
  padding-inline-end: 52px;
}

.separator {
  border-bottom: 1px solid var(--in-content-box-border-color);
}

#fxaGroup {
  margin-bottom: 32px;
}

.openLink {
  cursor: pointer;
}

#fxaDisplayName {
  margin-inline-end: 10px !important;
}

.fxaLoginRejectedWarning {
  list-style-image: url(chrome://global/skin/icons/warning.svg);
  -moz-context-properties: fill;
  fill: #FFBF00;
  margin-inline: 4px 8px;
}

#syncStatusContainer {
  margin-top: 32px;
}

#syncChangeOptions {
  margin-inline-start: 0;
}

.sync-group {
  margin-block: var(--space-large);
  border: 1px solid var(--in-content-border-color);
}

.sync-configured {
  display: block;
}

.sync-info-box {
  border-radius: var(--border-radius-medium);
  padding: var(--space-large);
}

.sync-engines-list > div,
.sync-engines-list + hbox {
  margin-top: 0;
  padding-top: var(--space-large);
}

dialog > .sync-engines-list > div,
dialog > .sync-engines-list + hbox {
  padding-top: 0;
}

.sync-engines-list checkbox .checkbox-icon,
.sync-engine-image {
  display: flex;
  margin-inline: 2px 8px;
  width: 16px;
  height: 16px;
  -moz-context-properties: fill;
  fill: currentColor;
  align-self: center;
}

.sync-engines-list checkbox {
  min-width: max-content;
  max-width: calc(50vw - 10px);
}

.sync-engines-list .checkbox-label-box {
  align-items: center;
}

.sync-engines-list {
  column-count: 3;
  column-fill: balance;
  margin-block-end: 10px;
}

.sync-engines-list > div:not([hidden]) {
  display: flex;
  break-inside: avoid;
}

.sync-engine-bookmarks .checkbox-icon,
.sync-engine-bookmarks.sync-engine-image {
  list-style-image: url("chrome://browser/skin/bookmark.svg");
}

.sync-engine-history .checkbox-icon,
.sync-engine-history.sync-engine-image {
  list-style-image: url("chrome://browser/skin/history.svg");
}

.sync-engine-tabs .checkbox-icon,
.sync-engine-tabs.sync-engine-image {
  list-style-image: url("chrome://browser/skin/synced-tabs.svg");
}

.sync-engine-passwords .checkbox-icon,
.sync-engine-passwords.sync-engine-image {
  list-style-image: url("chrome://browser/skin/login.svg");
}

.sync-engine-addons .checkbox-icon,
.sync-engine-addons.sync-engine-image {
  list-style-image: url("chrome://mozapps/skin/extensions/extension.svg");
}

.sync-engine-prefs .checkbox-icon,
.sync-engine-prefs.sync-engine-image {
  list-style-image: url("chrome://global/skin/icons/settings.svg");
}

.sync-engine-addresses .checkbox-icon,
.sync-engine-addresses.sync-engine-image {
  list-style-image: url("chrome://formautofill/content/formfill-anchor.svg");
}

.sync-engine-creditcards .checkbox-icon,
.sync-engine-creditcards.sync-engine-image {
  list-style-image: url("chrome://formautofill/content/icon-credit-card-generic.svg");
}

.fxaMobilePromo {
  margin-top: 2px !important;
}

.androidIcon,
.iOSIcon {
  margin-inline: 2px 4px;
  width: 20px;
  height: 20px;
  vertical-align: text-bottom;
}

.update-throbber {
  width: 1.3em;
  min-height: 1.3em;
  margin-inline-end: 8px;
  content: url("chrome://global/skin/icons/loading.svg");
  -moz-context-properties: fill;
  fill: var(--color-accent-primary);
}

.navigation {
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}

@media (prefers-reduced-motion) {
  /* Setting border-inline-end on .navigation makes it a focusable element */
  .navigation::after {
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--in-content-border-color);
    top: 0;
    inset-inline-end: 0;
    position: absolute;
  }
}

.face-sad::before {
  content: "\01F641";
  font-size: 1.3em;
  margin-inline-end: 8px;
}

.face-smile::before {
  content: "\01F600";
  font-size: 1.3em;
  margin-inline-end: 8px;
}

#policies-container,
#searchInput {
  min-height: 32px;
  margin: 20px 0 30px;
}

#policies-container {
  margin-inline-end: var(--space-large);
  /* Make sure the container is at least the same height as the searchbox.
     This is needed in addition to the min-height above to make the info icon
     the same height as the first line in the policies label.
     Searchbox's height - line-height - border top and bottom */
  padding-block: max(0px, calc((32px - 1.8em - 2px) / 2));
}

.info-box-container {
  background-color: var(--in-content-box-info-background);
  padding: 9px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.info-box-container.smaller-font-size {
  font-size: 85%;
}

.info-icon-container {
  display: flex;
}

.info-icon {
  content: url("chrome://global/skin/icons/info.svg");
  width: 16px;
  height: 16px;
  margin-inline-end: 6px;
  align-self: start;
  margin-top: 0.4em;
  flex-shrink: 0;
}

.info-box-container.smaller-font-size .info-icon {
  margin-top: 0.3em;
}

.sticky-container {
  position: sticky;
  background-color: var(--in-content-page-background);
  top: 0;
  z-index: 1;
  /* The sticky-container should have the capability to cover all spotlight area. */
  width: calc(100% + 8px);
  margin-inline: -4px;
  padding-inline: 4px;
}

.sticky-inner-container {
  flex: 1;
  max-width: var(--page-main-content-width);
}

.search-tooltip {
  max-width: 10em;
  font-size: 1rem;
  position: absolute;
  padding: 0 .7em;
  background-color: #ffe900;
  color: #000;
  border: 1px solid #d7b600;
  margin-top: -18px;
  opacity: .85;
}

.search-tooltip:hover {
  opacity: .1;
}

.search-tooltip::before {
  position: absolute;
  content: "";
  border: 7px solid transparent;
  border-top-color: #d7b600;
  top: 100%;
  inset-inline-start: calc(50% - 7px);
  forced-color-adjust: none;
}

.search-tooltip::after {
  position: absolute;
  content: "";
  border: 6px solid transparent;
  border-top-color: #ffe900;
  top: 100%;
  inset-inline-start: calc(50% - 6px);
  forced-color-adjust: none;
}

@media (forced-colors: active) {
  .search-tooltip::before {
    border-top-color: CanvasText;
  }

  .search-tooltip::after {
    border-top-color: Canvas;
  }
}

.search-tooltip-parent {
  position: relative;
}

.search-tooltip > span {
  display: block;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.visually-hidden {
  display: none !important;
}

menulist[indicator=true] > menupopup menuitem:not([image]) > .menu-iconic-left {
  display: flex;
  min-width: auto; /* Override the min-width defined in menu.css */
  margin-inline-end: 6px;
}

menulist[indicator=true] > menupopup menuitem:not([image]) > .menu-iconic-left > .menu-iconic-icon {
  width: 8px;
  height: 10px;
  margin: 0;
}

menulist[indicator=true] > menupopup menuitem[indicator=true]:not([image]) > .menu-iconic-left > .menu-iconic-icon {
  list-style-image: url(chrome://browser/skin/preferences/search-arrow-indicator.svg);
}

menulist[indicator=true] > menupopup menuitem[indicator=true]:not([image]) > .menu-iconic-left > .menu-iconic-icon:-moz-locale-dir(rtl) {
  transform: scaleX(-1);
}

.menu-iconic-highlightable-text {
  margin: 0; /* Align with the margin of xul:label.menu-iconic-text */
}

#no-results-message > label {
  margin: 2px 0;
}

/* Proxy port input */

.proxy-port-input {
  width: calc(5ch + 18px); /* 5 chars + (8px padding + 1px border) on both sides */
  margin-inline-start: 4px;
}

#primaryBrowserLocale {
  margin-inline-start: 0;
  min-width: 20em;
}

#availableLanguages,
#availableLocales {
  margin-inline-start: 0;
}

#availableLanguages,
#availableLocales,
#activeLanguages,
#selectedLocales {
  margin-inline-end: 4px;
}

#activeLanguages,
#selectedLocales {
  min-height: 15em;
  contain: size;
}

#selectedLocales > richlistitem {
  padding-inline-start: 13px;
}

#warning-message > .message-bar-description {
  width: 32em;
}

#warning-message {
  margin-top: 8px;
}

.action-button {
  margin-inline-end: 0;
}

.languages-grid {
  flex: 1;
  display: grid;
  grid-template-rows: 1fr auto;
  grid-template-columns: 1fr auto;
}

/* Menulist styles */
.label-item {
  font-size: 0.8em;
}

.updateSettingCrossUserWarningContainer {
  margin-block-end: 17px;
}

#updateSettingCrossUserWarningDesc {
  padding-inline-start: calc(16px + 6px); /* radio-check's width + margin-inline-end */
  margin-block-start: 20px;
}

richlistitem .text-link {
  color: inherit;
  text-decoration: underline;
}

richlistitem .text-link:hover {
  color: inherit;
}

#proxy-grid,
#dnsOverHttps-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
}

.proxy-grid-row:not([hidden]),
.dnsOverHttps-grid-row:not([hidden]) {
  display: contents;
}

#proxy-grid > .thin {
  grid-column: span 2;
  height: 20px;
}

#pane-experimental-featureGates > .featureGate:not(.visually-hidden) ~ .featureGate:not(.visually-hidden) {
  margin-top: var(--space-xlarge);
}

#pane-experimental-featureGates moz-fieldset::part(label) {
  font-size: var(--font-size-large);
}

.featureGateDescription {
  white-space: pre-wrap;
}

#genai-chat-label,
#genai-chat-provider,
#genai-chat-links {
  margin-block: 0 var(--input-space-block);
}

#genai-chat-provider {
  width: min-content;
  min-width: 20em;
}

.checkbox-row {
  display: flex;
  align-items: baseline;
}

.checkbox-row input[type=checkbox] {
  translate: 0 2px
}

.checkbox-row a {
  margin-inline-start: 6px;
}

#moreFromMozillaCategory-header .subtitle {
  margin-block-end: var(--space-xlarge);
}

#moreFromMozillaCategory:not([hidden]) {
  display: flex;
  flex-direction: column;
}

#moreFromMozillaCategory .mozilla-product-item {
  margin-top: 30px;
}

#moreFromMozillaCategory .product-title {
  margin: 4px 0 3px;
  background-repeat: no-repeat;
  background-size: contain;
  padding-inline-start: 30px;
  -moz-context-properties: fill;
  fill: currentColor;
}

#moreFromMozillaCategory .product-title:-moz-locale-dir(rtl) {
  background-position-x: right;
}

#moreFromMozillaCategory .product-description-box {
  padding-inline-start: 30px;
  margin: 2px 0;
}

#moreFromMozillaCategory .description {
  display: inline;
}

#moreFromMozillaCategory .small-button {
  margin-block: 14px 2px;
  margin-inline: 30px 0;
}

#firefox-mobile {
  background-image: url("chrome://browser/skin/device-phone.svg");
}

#mozilla-vpn {
  background-image: url("chrome://browser/skin/preferences/vpn-logo.svg");
}

#mozilla-monitor {
  background-image: url("chrome://browser/skin/preferences/monitor-logo.svg");
}

#firefox-relay {
  background-image: url("chrome://browser/skin/preferences/relay-logo.svg");
}

/*
 * The :not clause is required because hiding an element with `display: flex` only
 * partially hides it.
 */
.qr-code-box:not([hidden="true"]) {
  max-width: 320px; /* a unit better than px for this & next? */
  min-height: 204px;
  margin-block: 10px 0;
  margin-inline-start: 30px;
  background-color: var(--in-content-box-info-background);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  border-radius: 4px;
}

@media (forced-colors) {
  .qr-code-box:not([hidden="true"]) {
    border: 1px solid currentColor;
  }
}

.qr-code-box-image-wrapper {
  position: relative;
  display: grid;
  align-items: center;
  justify-items: center;
}

.qr-code-box-image {
  border-radius: 4px;
  padding: 2px;
  width: 80px;
  flex: 0 0 auto;
  -moz-context-properties: fill;
  fill: currentColor;
}

.qr-code-box-logo {
  height: 18px;
  position: absolute;
}

.qr-code-title-label {
  flex: 1 1 auto;
}

.qr-code-box-title {
  font-size: .87em;
  font-weight: normal;

  text-align: center;

  margin-block: 20px 0;
  margin-inline: 40px;
}

.simple .qr-code-box-title {
  margin-block: 0;
  width: 296px;
}

.simple .qr-code-box {
  padding-inline: var(--space-xlarge);
  padding-block: 20px
}

.simple .qr-code-box-image {
  padding: 10px;
}

#moreFromMozillaCategory .qr-code-link {
  font-size: .87em;
}

@media (prefers-color-scheme: dark) {
  .qr-code-box:not([hidden="true"]) {
    background-color: rgba(91,91,102,0.45);
  }

  .qr-code-box-title {
    color: #fbfbfe;
  }
}

#websiteAppearanceGroup {
  display: block;
}

#web-appearance-chooser {
  display: flex;
  justify-content: space-between;
  gap: var(--space-large);
}

#web-appearance-chooser,
#web-appearance-override-warning,
#text-zoom-override-warning {
  margin: var(--space-medium) 0;
}

#web-appearance-override-warning:not([hidden]) {
  display: flex;
}

.web-appearance-choice {
  width: 100%;
  border: 1px solid var(--in-content-box-border-color);
  background-color: var(--in-content-box-background-color);
  color: var(--in-content-text-color);
  border-radius: 4px;
  overflow: clip;
}

.web-appearance-choice-image-container {
  background-color: var(--in-content-button-background);
  padding: var(--space-medium) 0;
}

.web-appearance-choice:hover > .web-appearance-choice-image-container {
  background-color: var(--in-content-button-background-hover);
}

.web-appearance-choice:hover:active > .web-appearance-choice-image-container {
  background-color: var(--in-content-button-background-active);
}

.web-appearance-choice img {
  display: block;
  margin: 0 auto;

  border: 1px solid var(--in-content-box-border-color);
  border-radius: 4px;
  box-shadow: 0 0 2px 0 rgba(0,0,0,0.2);

  transform: scaleX(1);
  content: url("chrome://browser/content/preferences/web-appearance-light.svg")
}

.web-appearance-choice.dark img {
  content: url("chrome://browser/content/preferences/web-appearance-dark.svg")
}

.web-appearance-choice img:-moz-locale-dir(rtl) {
  transform: scaleX(-1);
}

.web-appearance-choice-footer {
  border-top: 1px solid var(--in-content-box-border-color);
  padding: 4px 8px;
  overflow-wrap: anywhere;
  display: flex;
}

#translations-manage-install-list {
  height: 220px;
  overflow: auto;
  background-color: var(--background-color-box);
  border: 1px solid var(--in-content-box-border-color);
  border-radius: 4px;
  resize: vertical;
  margin: 4px 0;
}

.translations-manage-language:first-child {
  border-bottom: 1px solid var(--in-content-box-border-color);
  padding: 8px;
  margin-bottom: 8px;
}

.translations-manage-language {
  align-items: center;
  padding: 0 8px;
}

.translations-manage-language > label {
  flex: 1;
  margin: 0 15px;
}

#translations-manage-error {
  color: var(--text-color-error);
  margin: var(--space-large) 0;
}

.hidden-category {
  display: none;
}
/* Platform-specific tweaks & overrides */

@media (-moz-platform: macos) {
  tabpanels {
    padding: 20px 7px 7px;
  }

  #browserUseCurrent,
  #browserUseBookmark,
  #browserUseBlank {
    margin-top: 10px;
  }

  #privacyPrefs {
    padding: 0 4px;
  }

  #privacyPrefs > tabpanels {
    padding: 18px 10px 10px;
  }

  #OCSPDialogPane {
    font: message-box !important;
  }

  /**
   * Update Preferences
   */
  #autoInstallOptions {
    margin-inline-start: 20px;
  }

  .updateControls {
    margin-inline-start: 10px;
  }
}

@media (-moz-platform: linux) {
  .treecol-sortdirection {
    /* override the Linux only toolkit rule */
    appearance: none;
  }
}

.section-heading,
.section-description {
  margin: 0 0 var(--space-small);
}

.section-header-last {
  margin: 0 0 var(--space-large);
}

.search-header:has(.section-heading) {
  margin: 0;
}
