/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#defaultEngine,
#defaultPrivateEngine {
  margin-inline-start: 0;
}

#defaultEngine::part(icon),
#defaultPrivateEngine::part(icon) {
  height: 16px;
  width: 16px;
}

/* work around a display: none in Linux's menu.css, see bug 1112310 */
.searchengine-menuitem > .menu-iconic-left {
  display: flex;
}

#engineList {
  margin: .5em 0;
}

#engineList treechildren::-moz-tree-image(engineName) {
  margin-inline: 1px 10px;
  width: 16px;
  height: 16px;
}

#engineList treechildren::-moz-tree-drop-feedback {
  background-color: SelectedItem;
  width: 10000px; /* 100% doesn't work; 10k is hopefully larger than any window
                     we may have, overflow isn't visible. */
  height: 2px;
  margin-inline-start: 0;
}

#engineList treechildren::-moz-tree-image(bookmarks),
#engineList treechildren::-moz-tree-image(tabs),
#engineList treechildren::-moz-tree-image(history) {
  -moz-context-properties: fill;
}

/* Local search shortcut icon colors.  These should match the values in
   urlbarView.inc.css. */
#engineList treechildren::-moz-tree-image(bookmarks) {
  fill: #0060df; /* Blue-60 */
}
#engineList treechildren::-moz-tree-image(tabs) {
  fill: #008eaf; /* Teal-70 */
}
#engineList treechildren::-moz-tree-image(history),
#engineList treechildren::-moz-tree-image(bookmarks, selected),
#engineList treechildren::-moz-tree-image(tabs, selected) {
  fill: currentColor;
}

/* Ensure keywords and search engine names have the directionality of their
   content, ignoring ancestors - otherwise directionality is wrong in RTL
   locales for LTR keywords, with the "@" appearing in the wrong place.  */
#engineList > treechildren::-moz-tree-cell(engineKeyword) {
  unicode-bidi: plaintext;
}

#engineShown {
  min-width: 26px;
}

#addEnginesBox {
  margin-bottom: 1em;
}

#removeEngineButton,
#restoreDefaultSearchEngines {
  margin-inline: 0;
}
