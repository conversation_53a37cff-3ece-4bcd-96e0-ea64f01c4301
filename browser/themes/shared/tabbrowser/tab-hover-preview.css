/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#tab-preview-panel {
  --panel-width: 280px;
  --panel-padding: 0;
  pointer-events: none;
}

.tab-preview-text-container {
  padding: var(--space-small);
}

.tab-preview-title {
  overflow: hidden;
  -webkit-line-clamp: 2;
  font-weight: var(--font-weight-bold);
}

.tab-preview-uri {
  color: var(--text-color-deemphasized);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tab-preview-pid-activeness {
  color: var(--text-color-deemphasized);
  display: flex;
  justify-content: space-between;
}

.tab-preview-thumbnail-container {
  width: 280px;
  height: 140px;
  border-top: 1px solid var(--panel-border-color);
  &.hide-thumbnail {
    display: none;
  }
  @media (width < 640px) {
    display: none;
  }
  > canvas {
    display: block;
    width: 100%;
    animation: tab-hover-preview-fadein 0.2s ease;
  }
}

@keyframes tab-hover-preview-fadein {
  from { opacity: 0; }
  to { opacity: 100; }
}
