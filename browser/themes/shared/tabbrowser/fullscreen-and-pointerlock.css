/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root[inDOMFullscreen] #navigator-toolbox,
:root[inDOMFullscreen] #fullscr-toggler,
:root[inDOMFullscreen] #sidebar-box,
:root[inDOMFullscreen] #sidebar-main,
:root[inDOMFullscreen] #sidebar-splitter,
:root[inFullscreen]:not([macOSNativeFullscreen]) toolbar:not([fullscreentoolbar=true]),
:root[inFullscreen] .global-notificationbox {
  visibility: collapse;
}

#fullscr-toggler {
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  position: fixed;
  z-index: **********;
}

.pointerlockfswarning {
  will-change: translate;
  transition: translate 300ms ease-in;
  translate: 0 -100px;

  pointer-events: none;
  position: fixed;
  top: 0;
  bottom: auto;

  box-sizing: border-box;
  width: max-content;
  max-width: 95%;

  font: message-box;

  align-items: center;
  background-color: rgba(45, 62, 72, 0.9);
  color: #fafafa;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  border: 2px solid currentColor;
  padding: 24px 16px;

  @starting-style {
    translate: 0 -100px;
  }

  &:popover-open {
    display: flex;
  }

  &[onscreen] {
    translate: 0 50px;
  }

  &[ontop] {
    /* Use -10px to hide the border and border-radius on the top */
    translate: 0 -10px;
  }

  :root[macOSNativeFullscreen] &[ontop] {
    translate: 0 80px;
  }

  &:is([data-identity="verifiedDomain"], [data-identity="unknownIdentity"])::before {
    content: "";
    background-size: cover;
    margin: 0;
    width: 24px;
    height: 24px;
    -moz-context-properties: fill;
    fill: currentColor;
  }

  &[data-identity="verifiedDomain"]::before {
    background-image: url("chrome://global/skin/icons/security.svg");
  }

  &[data-identity="unknownIdentity"]::before {
    background-image: url("chrome://global/skin/icons/security-broken.svg");
  }
}

.pointerlockfswarning-domain-text,
.pointerlockfswarning-generic-text {
  font-size: 21px;
  margin: 0 16px;
  word-wrap: break-word;
  /* We must specify a min-width, otherwise word-wrap:break-word doesn't work. Bug 630864. */
  min-width: 1px;
}

.pointerlockfswarning-domain-text:not([hidden]) + .pointerlockfswarning-generic-text {
  display: none;
}

.pointerlockfswarning-domain {
  font-weight: bold;
  margin: 0;
}

#fullscreen-exit-button {
  pointer-events: auto;
  margin: 0;
}
