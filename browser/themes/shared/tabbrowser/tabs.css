/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root {
  --tabstrip-inner-border: 1px solid color-mix(in srgb, currentColor 25%, transparent);
  --tabstrip-min-height: calc(var(--tab-min-height) + 2 * var(--tab-block-margin));
  --tab-min-height: 36px;
  &[uidensity=compact] {
    --tab-min-height: 29px;
  }
  &[uidensity=touch] {
    --tab-min-height: 41px;
  }
  --tab-group-label-height: max(1.5em, var(--tab-min-height) - 14px);
  --tab-group-label-padding: 4px;
  --tab-group-line-thickness: 2px;
  /* Collapsed tabs should be square, so set width to match the min height */
  --tab-collapsed-background-width: var(--tab-min-height);
  --tab-collapsed-width: calc(var(--tab-collapsed-background-width) + 2 * var(--tab-inner-inline-margin));
  --tab-inner-inline-margin: var(--space-medium);
  --tab-inline-padding: 8px;
  --tab-pinned-expanded-background-width: 40px;
  --tab-pinned-margin-inline-expanded: var(--space-xsmall);
  --tab-pinned-min-width-expanded: calc(var(--tab-pinned-expanded-background-width) + 2 * var(--tab-pinned-margin-inline-expanded));
  --tab-pinned-container-margin-inline-expanded: var(--space-small);
  --tab-border-radius: 4px;
  --tab-overflow-clip-margin: 2px;
  --tab-close-button-padding: 6px;
  --tab-shadow-max-size: 6px;
  --tab-block-margin: 4px;
  --tab-icon-end-margin: 5.5px;
  --tab-icon-overlay-fill: light-dark(white, black);
  --tab-icon-overlay-stroke: light-dark(black, white);
  --tab-label-line-height: 1.7;
  --tab-loading-fill: #0A84FF;
  --tab-hover-background-color: color-mix(in srgb, currentColor 11%, transparent);
  --tab-selected-textcolor: var(--toolbar-color);
  --tab-selected-bgcolor: var(--toolbar-bgcolor);
  --tab-selected-color-scheme: var(--toolbar-color-scheme);
  &[lwt-tab-selected="light"] {
    --tab-selected-color-scheme: light;
  }
  &[lwt-tab-selected="dark"] {
    --tab-selected-color-scheme: dark;
  }
  --tab-selected-shadow: 0 0 4px rgba(0,0,0,.4);
  --tab-outline: 1px solid var(--tab-outline-color);
  --tab-outline-color: transparent;
  --tab-outline-offset: -1px;
  --tab-selected-outline-color: transparent;
  --tab-hover-outline-color: transparent;
  @media (prefers-contrast) {
    --tab-selected-outline-color: currentColor;
    --tab-hover-outline-color: currentColor;
  }
  @media (forced-colors) {
    &:not([lwtheme]) {
      --tab-outline-color: ButtonText;
      --tab-hover-outline-color: SelectedItem;
      --tab-selected-outline-color: SelectedItem;
    }
  }
  &[lwtheme] {
    --tab-selected-outline-color: var(--lwt-tab-line-color, currentColor);
  }
  --tab-dragover-transition: transform 200ms var(--animation-easing-function);

  --vertical-tabs-scrollbar-color: auto;
  @media (-moz-platform: macos) {
    --vertical-tabs-scrollbar-color: color-mix(in srgb, currentColor 26%, transparent) transparent;
  }

  /* TAB GROUP COLORS
   * tab-group-color-* is used for expanded groups in light mode and collapsed groups in dark mode
   * tab-group-color-*-invert is used for expanded groups in dark mode
   * tab-group-color-*-pale is used for collapsed groups in light mode */
  --tab-group-color-blue: #0069CC;
  --tab-group-color-blue-invert: #52A9FE;
  --tab-group-color-blue-pale: #F2F9FF;
  --tab-group-color-purple: #8C39BC;
  --tab-group-color-purple-invert: #C188EA;
  --tab-group-color-purple-pale: #FBF6FF;
  --tab-group-color-cyan: #007B7C;
  --tab-group-color-cyan-invert: #4AB6C7;
  --tab-group-color-cyan-pale: #EFFBFC;
  --tab-group-color-orange: #B14200;
  --tab-group-color-orange-invert: #EE8545;
  --tab-group-color-orange-pale: #FFF6F1;
  --tab-group-color-yellow: #955C00;
  --tab-group-color-yellow-invert: #F5AE39;
  --tab-group-color-yellow-pale: #FCF8EF;
  --tab-group-color-pink: #BB1562;
  --tab-group-color-pink-invert: #E17CC2;
  --tab-group-color-pink-pale: #FFF5FB;
  --tab-group-color-green: #477A00;
  --tab-group-color-green-invert: #6EBB5D;
  --tab-group-color-green-pale: #F4FBF2;
  --tab-group-color-red: #C21725;
  --tab-group-color-red-invert: #F47B74;
  --tab-group-color-red-pale: #FFF5F5;
  --tab-group-color-gray: #5E6A77;
  --tab-group-color-gray-invert: #99A6B4;
  --tab-group-color-gray-pale: #F2F9FF;

  --tab-group-label-text-dark: var(--color-gray-100);
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "sidebar.verticalTabs") {
  :root {
    --tab-min-height: max(32px, calc(var(--tab-label-line-height) * 1em));
    --tab-block-margin: 2px;
    --tab-inline-padding: var(--space-medium);
  }
}

#tabbrowser-tabs {
  /* Use a bigger flex value than the searchbar to prevent it from
   * taking all the toolbar space. */
  flex: 1000 1000;

  &[orient="horizontal"] {
    --tab-overflow-pinned-tabs-width: 0px;
    padding-inline: var(--tab-overflow-pinned-tabs-width) 0;
    min-height: var(--tabstrip-min-height);

    --tab-min-width: 76px;
    :root[uidensity="touch"] & {
      /* Touch mode needs additional space for the close button. */
      --tab-min-width: 86px;
    }

    &[positionpinnedtabs] > #tabbrowser-arrowscrollbox::part(scrollbox) {
      /* Add padding to match the shadow's blur radius so the
         shadow doesn't get clipped when either the first or
         last tab is selected */
      padding-inline: var(--tab-shadow-max-size);
    }

    /* Make it easier to drag tabs by expanding the drag area downwards. */
    &[movingtab] {
      padding-bottom: 15px;
      margin-bottom: -15px;
    }
  }

  &[noshadowfortests] {
    --tab-selected-shadow: none;
  }
}

#navigator-toolbox[movingtab] > #nav-bar {
  pointer-events: none;

  /* Allow dropping a tab on buttons with associated drop actions. */
  > #nav-bar-customization-target {
    > #personal-bookmarks,
    > #home-button,
    > #downloads-button,
    > #bookmarks-menu-button {
      pointer-events: auto;
    }
  }
}

.closing-tabs-spacer {
  pointer-events: none;

  #tabbrowser-arrowscrollbox:not(:hover) > #tabbrowser-arrowscrollbox-periphery > & {
    transition: width .15s ease-out;
  }
}

.tabbrowser-tab {
  --tab-label-mask-size: 1em;

  appearance: none;
  background-color: transparent;
  color: inherit;
  color-scheme: unset;
  border-radius: 0;
  border-width: 0;
  margin: 0;
  padding: 0 var(--tab-overflow-clip-margin);
  align-items: stretch;
  /* Needed so that overflowing content overflows towards the end rather than
     getting centered. That prevents tab opening animation from looking off at
     the start, see bug 1759221. */
  justify-content: flex-start;
  overflow: clip;
  /* Needed to avoid clipping the tab-background shadow, which has a 4px blur. */
  overflow-clip-margin: var(--tab-overflow-clip-margin);

  &:not([pinned]) {
    flex: 100 100;
    max-width: 225px;
    min-width: var(--tab-min-width);
    transition: min-width 100ms ease-out,
                max-width 100ms ease-out;
  }

  &:not([pinned], [fadein]) {
    max-width: 0.1px;
    min-width: 0.1px;
    visibility: hidden;
  }

  #tabbrowser-tabs[positionpinnedtabs] > #tabbrowser-arrowscrollbox > &[pinned] {
    position: absolute !important;
    display: block;
  }

  #tabbrowser-tabs[movingtab] &:is([selected], [multiselected]) {
    position: relative;
    z-index: 2;
    pointer-events: none; /* avoid blocking dragover events on scroll buttons */
  }

  @media (prefers-reduced-motion: no-preference) {
    #tabbrowser-tabs[movingtab] &[fadein]:not([selected]):not([multiselected]),
    &[multiselected-move-together],
    &[tabdrop-samewindow] {
      transition: var(--tab-dragover-transition);
    }
  }

  &[multiselected-move-together][multiselected]:not([selected]) {
    z-index: 2;
  }
}

.tab-content {
  padding: 0 var(--tab-inline-padding);

  &:not([pinned]) {
    min-width: 0;
  }

  :root:not([uidensity=compact]) &[pinned] {
    padding: 0 10px;
  }

  &:is([selected], [multiselected]) {
    color: var(--tab-selected-textcolor);
    color-scheme: var(--tab-selected-color-scheme);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .tab-loading-burst {
    border-radius: inherit;
    position: relative;
    overflow: hidden;

    &::before {
      position: absolute;
      content: "";
      /* We set the width to be a percentage of the tab's width so that we can use
         the `scale` transform to scale it up to the full size of the tab when the
         burst occurs. We also need to set the margin-inline-start so that the
         center of the burst matches the center of the favicon. */
      width: 5%;
      height: 100%;
      /* Center the burst over the .tab-loading-burst; it's 9px from the edge thanks
         to .tab-content, plus 8px more since .tab-loading-burst is 16px wide. */
      margin-inline-start: calc(17px - 2.5%);
    }

    &[pinned]::before {
      /* This is like the margin-inline-start rule above, except that icons for
         pinned tabs are 12px from the edge. */
      margin-inline-start: calc(20px - 2.5%);
    }

    &[bursting]::before {
      background-image: url("chrome://browser/skin/tabbrowser/loading-burst.svg");
      background-position: center center;
      background-size: 100% auto;
      background-repeat: no-repeat;
      animation: tab-burst-animation 375ms cubic-bezier(0,0,.58,1);
      -moz-context-properties: fill;
      fill: var(--tab-loading-fill);
    }

    &[bursting][notselectedsinceload]::before {
      animation-name: tab-burst-animation-light;
    }
  }

  @keyframes tab-burst-animation {
    0% {
      opacity: 0.4;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(40);
    }
  }

  @keyframes tab-burst-animation-light {
    0% {
      opacity: 0.2;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(40);
    }
  }
}

.tab-icon-pending:not([fadein]),
.tab-icon-image:not([fadein]),
.tab-close-button:not([fadein]),
.tab-background:not([fadein]) {
  visibility: hidden;
}

.tab-label:not([fadein]),
.tab-throbber:not([fadein]) {
  display: none;
}

/* Width/height & margins apply on tab icon stack children */
.tab-throbber,
.tab-icon-pending,
.tab-icon-image,
.tab-sharing-icon-overlay,
.tab-icon-overlay {
  height: 16px;
  width: 16px;

  /* apply end margin to not-pinned tabs which are:
     - oriented horizontally, or
     - vertical but expanded
  */
  #tabbrowser-tabs:is([orient="horizontal"], [orient="vertical"][expanded]) &:not([pinned]) {
    margin-inline-end: var(--tab-icon-end-margin);
  }
}

.tab-throbber {
  &:not([busy]) {
    display: none;
  }

  @media (prefers-reduced-motion: reduce) {
    background-image: url("chrome://global/skin/icons/loading.svg");
    background-position: center;
    background-repeat: no-repeat;
    -moz-context-properties: fill;
    fill: currentColor;
    opacity: 0.4;

    &[progress] {
      opacity: 0.8;
    }
  }

  @media (prefers-reduced-motion: no-preference) {
    :root[sessionrestored] & {
      &[busy] {
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          background-image: url("chrome://browser/skin/tabbrowser/loading.svg");
          background-position: left center;
          background-repeat: no-repeat;
          width: 480px;
          height: 100%;
          animation: tab-throbber-animation 1.05s steps(30) infinite;
          -moz-context-properties: fill;
          fill: currentColor;
          opacity: 0.7;
        }

        &:-moz-locale-dir(rtl)::before {
          animation-name: tab-throbber-animation-rtl;
        }
      }

      &[progress]::before {
        fill: var(--tab-loading-fill);
        opacity: 1;
      }

      #TabsToolbar[brighttext] &[progress]:not([selected])::before {
        fill: #84c1ff;
      }
    }
  }
}

@keyframes tab-throbber-animation {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

@keyframes tab-throbber-animation-rtl {
  0% { transform: translateX(0); }
  100% { transform: translateX(100%); }
}

.tab-icon-pending:is(:not([pendingicon]), [busy], [pinned]) {
  display: none;
}

:root {
  --tab-sharing-icon-animation: 3s linear tab-sharing-icon-pulse infinite;
}

@keyframes tab-sharing-icon-pulse {
  0%, 16.66%, 83.33%, 100% {
    opacity: 0;
  }
  33.33%, 66.66% {
    opacity: 1;
  }
}

.tab-icon-image {
  -moz-context-properties: fill, stroke;
  fill: currentColor;

  /* Apply crisp rendering for favicons at exactly 2dppx resolution */
  @media (resolution: 2dppx) {
    image-rendering: -moz-crisp-edges;
  }

  &:not([src]),
  &:-moz-broken {
    content: url("chrome://global/skin/icons/defaultFavicon.svg");
  }

  &[sharing]:not([selected]) {
    animation: var(--tab-sharing-icon-animation);
    animation-delay: -1.5s;
  }
  /* Hide the generic favicon only in horizontal mode. In vertical mode, tabs may not
  have labels (collapsed state) or we want icons and labels to line up vertically
  (expanded state).  */
  #tabbrowser-tabs[orient="horizontal"] &:not([src], [pinned], [crashed], [sharing], [pictureinpicture]),
  &[busy] {
    display: none;
  }

  &.profiles-tab {
    fill: var(--toolbar-bgcolor);
    stroke: var(--toolbar-color);
  }
}

.tab-sharing-icon-overlay,
.tab-icon-overlay {
  display: none;
}

.tab-sharing-icon-overlay[sharing]:not([selected]),
.tab-icon-overlay:is([soundplaying], [muted], [activemedia-blocked], [crashed]) {
  display: revert;
}

.tab-sharing-icon-overlay {
  position: relative;
  -moz-context-properties: fill;
  fill: rgb(224, 41, 29);
  animation: var(--tab-sharing-icon-animation);

  &[sharing="camera"] {
    list-style-image: url("chrome://browser/skin/notification-icons/camera.svg");
  }

  &[sharing="microphone"] {
    list-style-image: url("chrome://browser/skin/notification-icons/microphone.svg");
  }

  &[sharing="screen"] {
    list-style-image: url("chrome://browser/skin/notification-icons/screen.svg");
  }
}

.tab-icon-overlay {
  position: relative;
  padding: 2px;

  &[crashed] {
    list-style-image: url("chrome://browser/skin/tabbrowser/crashed.svg");
  }

  &:not([crashed]) {
    &[soundplaying] {
      background-image: url("chrome://browser/skin/tabbrowser/tab-audio-playing-small.svg");
    }

    &[muted] {
      background-image: url("chrome://browser/skin/tabbrowser/tab-audio-muted-small.svg");
    }

    &[activemedia-blocked] {
      background-image: url("chrome://browser/skin/tabbrowser/tab-audio-blocked-small.svg");
    }

    &:is([soundplaying], [muted], [activemedia-blocked]) {
      background-color: var(--tab-icon-overlay-stroke);
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 10px;
      -moz-context-properties: fill;
      fill: var(--tab-icon-overlay-fill);
    }
  }

  &:not([indicator-replaces-favicon]) {
    top: -5.5px;
    inset-inline-end: -6px;
    z-index: 1; /* Overlay tab title */

    &:is([soundplaying], [muted], [activemedia-blocked]) {
      top: -8px;
      inset-inline-end: -6px;
    }
  }

  &[indicator-replaces-favicon] {
    border-radius: 2px;
    fill-opacity: 0.75;

    &:hover {
      background-color: color-mix(in srgb, currentColor 15%, transparent);
      fill-opacity: 0.95;
    }

    &:hover:active {
      background-color: color-mix(in srgb, currentColor 30%, transparent);
    }

    &:is([soundplaying], [muted], [activemedia-blocked]) {
      &:hover,
      &:hover:active {
        background-color: color-mix(in srgb, currentColor 50%, transparent);
      }
    }
  }

  :is(
    :root[uidensity=compact],
    #tabbrowser-tabs[secondarytext-unsupported],
    :root:not([uidensity=compact]) #tabbrowser-tabs:not([secondarytext-unsupported]) .tabbrowser-tab:hover
  ) .tab-icon-stack[indicator-replaces-favicon] > :not(&),
  :root:not([uidensity=compact]) #tabbrowser-tabs:not([secondarytext-unsupported]) .tabbrowser-tab:not(:hover) &[indicator-replaces-favicon] {
    opacity: 0;
  }
}

.tab-label-container {
  overflow: hidden;

  #tabbrowser-tabs:not([secondarytext-unsupported], [orient=vertical]) & {
    height: 2.7em;
  }

  &[pinned] {
    width: 0;
  }

  &[textoverflow] {
    &[labeldirection=ltr]:not([pinned]),
    &:not([labeldirection], [pinned]):-moz-locale-dir(ltr) {
      direction: ltr;
      mask-image: linear-gradient(to left, transparent, black var(--tab-label-mask-size));
    }

    &[labeldirection=rtl]:not([pinned]),
    &:not([labeldirection], [pinned]):-moz-locale-dir(rtl) {
      direction: rtl;
      mask-image: linear-gradient(to right, transparent, black var(--tab-label-mask-size));
    }
  }
}

.tab-label {
  margin-inline: 0;
  line-height: var(--tab-label-line-height);  /* override 'normal' in case of fallback math fonts with huge metrics */
  white-space: nowrap;
}

.tab-close-button {
  -moz-context-properties: fill, fill-opacity;
  margin-inline-end: calc(var(--tab-inline-padding) / -2);
  width: 24px;
  height: 24px;
  padding: var(--tab-close-button-padding);
  border-radius: var(--tab-border-radius);
  list-style-image: url(chrome://global/skin/icons/close-12.svg);

  &[pinned],
  #tabbrowser-tabs[closebuttons="activetab"][orient="horizontal"] &:not([selected]) {
    display: none;
  }
}

/* The following rulesets allow showing more of the tab title by shrinking the
 * width of the close button. We don't do this in forced-colors mode since
 * the button has a visible outline shown. */
@media not (forced-colors) {
  #tabbrowser-tabs[orient="horizontal"] {
    .tabbrowser-tab:not([labelendaligned], :hover) > .tab-stack > .tab-content > .tab-close-button {
      padding-inline-start: 0;
      width: 18px;
    }

    .tabbrowser-tab[visuallyselected]:not([labelendaligned]):hover,
    &:not([closebuttons="activetab"]) .tabbrowser-tab:not([visuallyselected], [labelendaligned]):hover {
      --tab-label-mask-size: 2em;
    }
  }
}

.tab-secondary-label {
  font-size: .75em;
  margin: -.3em 0 .3em; /* adjust margins to compensate for line-height of .tab-label */
  opacity: .8;

  #tabbrowser-tabs:is([secondarytext-unsupported], [orient="vertical"]) &,
  :root[uidensity=compact] &,
  &:not([soundplaying], [muted], [activemedia-blocked], [pictureinpicture]),
  &:not([activemedia-blocked]) > .tab-icon-sound-blocked-label,
  &[muted][activemedia-blocked] > .tab-icon-sound-blocked-label,
  &[activemedia-blocked] > .tab-icon-sound-playing-label,
  &[muted] > .tab-icon-sound-playing-label,
  &[pictureinpicture] > .tab-icon-sound-playing-label,
  &[pictureinpicture] > .tab-icon-sound-muted-label,
  &:not([pictureinpicture]) > .tab-icon-sound-pip-label,
  &:not([muted]) > .tab-icon-sound-muted-label,
  &:not([showtooltip]) > .tab-icon-sound-tooltip-label,
  &[showtooltip] > .tab-icon-sound-label:not(.tab-icon-sound-tooltip-label) {
    display: none;
  }

  &[soundplaying-scheduledremoval]:not([muted]):not(:hover) {
    transition: opacity .3s linear var(--soundplaying-removal-delay);
    opacity: 0;
  }
}

.tab-icon-sound-label {
  /* Set height back to equivalent of parent's 1em based
     on the .tab-icon-sound having a reduced font-size */
  height: 1.3333em;
  white-space: nowrap;
  margin: 0;
}

.tab-background {
  border-radius: var(--tab-border-radius);
  margin-block: var(--tab-block-margin);
  min-height: var(--tab-min-height);
  outline: var(--tab-outline);
  outline-offset: var(--tab-outline-offset);

  .tabbrowser-tab:hover > .tab-stack > &:not([selected], [multiselected]) {
    background-color: var(--tab-hover-background-color);
    outline-color: var(--tab-hover-outline-color);
  }

  &:is([selected], [multiselected]) {
    background-color: var(--tab-selected-bgcolor);
    box-shadow: var(--tab-selected-shadow);
    outline-color: var(--tab-selected-outline-color);
  }

  &[multiselected] {
    outline-color: var(--focus-outline-color);

    &[selected] {
      outline-width: 2px;
      outline-offset: -2px;
    }
  }

  #tabbrowser-tabs[movingtab-createGroup] & {
    transition: background-color 100ms ease, color 100ms ease, outline-color 100ms ease;

    &[dragover-createGroup] {
      background-color: light-dark(var(--dragover-tab-group-color-pale), var(--dragover-tab-group-color));
      color: light-dark(var(--dragover-tab-group-color), var(--dragover-tab-group-color-pale));
      outline-color: light-dark(var(--dragover-tab-group-color), var(--dragover-tab-group-color-pale));
    }

    &:is([selected], [multiselected]) {
      outline-color: var(--dragover-tab-group-color);
      outline-width: 2px;
      outline-offset: -2px;
    }
  }
}

/* Keyboard focus outline */

#TabsToolbar #firefox-view-button:focus-visible > .toolbarbutton-icon,
#tabbrowser-tabs[tablist-has-focus] .tabbrowser-tab.tablist-keyboard-focus > .tab-stack > .tab-background {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-inset);
}

/* Pinned tabs */

.tabbrowser-tab:is([image], [pinned]) > .tab-stack > .tab-content[attention]:not([selected]),
.tabbrowser-tab > .tab-stack > .tab-content[pinned][titlechanged]:not([selected]),
#tabbrowser-tabs[orient="vertical"]:not([expanded]) .tabbrowser-tab > .tab-stack > .tab-content[titlechanged]:not([selected]) {
  background-image: radial-gradient(circle, var(--attention-dot-color), var(--attention-dot-color) 2px, transparent 2px);
  background-position: center bottom 6.5px;
  background-size: 4px 4px;
  background-repeat: no-repeat;

  #tabbrowser-tabs[orient="vertical"] & {
    background-position-y: bottom 4px;
  }
}

.tabbrowser-tab[image] > .tab-stack > .tab-content[attention]:not([pinned], [selected]) {
  background-position-x: left calc(var(--tab-inline-padding) + 6px);

  &:-moz-locale-dir(rtl) {
    background-position-x: right calc(var(--tab-inline-padding) + 6px);
  }
}

#tabbrowser-tabs[haspinnedtabs]:not([positionpinnedtabs])[orient="horizontal"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:nth-child(1 of :not([pinned], [hidden])) {
  margin-inline-start: 12px;
}

.tab-label[attention]:not([selected]) {
  font-weight: bold;
}

/* Tab Groups */

/* stylelint-disable-next-line media-query-no-invalid */
@media not (-moz-bool-pref: "sidebar.verticalTabs") {
  /*
   * .tab-group-line needs to span the drop shadows + space between tabs in the
   * same tab group so that the whole tab group appears to be underlined by an
   * unbroken line. However, the last tab in a tab group should have its group
   * underline stop at the right edge of the tab itself, otherwise it looks
   * like the tab group extends too far past the right edge of the tab.
   */
  .tab-group-line {
    display: none;
    background-color: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
    height: var(--tab-group-line-thickness);
    margin: 0 calc(-1 * var(--tab-overflow-clip-margin)) calc(-1 * var(--tab-block-margin));

    .tabbrowser-tab:last-of-type > .tab-stack > .tab-background > & {
      margin-inline-end: 0;
      border-start-end-radius: calc(var(--tab-group-line-thickness) / 2);
      border-end-end-radius: calc(var(--tab-group-line-thickness) / 2);
    }

    tab-group & {
      display: flex;
    }

    #tabbrowser-tabs[movingtab] &:is([selected],[multiselected]) {
      display: flex;
      background-color: light-dark(var(--dragover-tab-group-color), var(--dragover-tab-group-color-invert));
      border-radius: 1px;
    }

    #tabbrowser-tabs:is([movingtab-createGroup],[movingtab-ungroup]) &:is([selected],[multiselected]) {
      display: none;
    }
  }

  tab-group {
    /*
     * Let the tab bar flexbox distribute space between all tabs evenly, regardless of
     * whether they are children of the tab bar directly or children of tab groups
     */
    display: contents;

    &[collapsed] > .tabbrowser-tab {
      min-width: 0;
      max-width: 0;
      padding: 0;
      overflow-clip-margin: 0;
    }
  }
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "sidebar.verticalTabs") {
  tab-group {
    flex-direction: column;
    position: relative;

    &:not([collapsed])::before {
      content: "";
      display: block;
      position: absolute;
      inset-inline-start: var(--tab-group-line-thickness);
      inset-block-start: var(--space-medium);
      inset-block-end: 0;
      width: var(--tab-group-line-thickness);
      background-color: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
      border-radius: 1px;
    }

    &[collapsed] > .tabbrowser-tab {
      display: none;
    }

    #tabbrowser-tabs[expanded] & {
      margin-inline-start: var(--space-medium);

      &:not([collapsed])::before {
        inset-inline-start: 0;

        /* Move the line down by the height of the group label */
        inset-block-start: calc(var(--tab-group-label-height) + 2 * var(--tab-group-label-padding) + var(--space-medium));
      }
    }
  }
}

.tab-group-label-container {
  transform: var(--tabgroup-dragover-transform);
  transition: var(--tab-dragover-transition);

  /* stylelint-disable-next-line media-query-no-invalid */
  @media not (-moz-bool-pref: "sidebar.verticalTabs") {
    position: relative;

    /*
     * Provide some empty space to either side of the tab group label.
     * Use margin on the left because the ::after underline should not extend
     * to the left beyond the tab group label.
     * Use padding on the right because the ::after underline should extend
     * to the right to connect to the underline of the first tab in the group.
     */
    margin-inline-start: 3px;
    padding-inline-end: 3px;

    tab-group:not([collapsed]) > &::after {
      content: "";
      background-color: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
      height: var(--tab-group-line-thickness);
      position: absolute;
      inset: auto 0 0;
      border-start-start-radius: 1px;
      border-end-start-radius: 1px;
    }
  }

  #tabbrowser-tabs[orient="vertical"]:not([expanded]) & {
    width: var(--tab-collapsed-width);
    height: var(--tab-collapsed-width);
  }

  #tabbrowser-tabs[orient="vertical"][expanded] & {
    contain: inline-size;
  }
}

.tab-group-label {
  -moz-window-dragging: no-drag;
  text-align: center;
  font-weight: var(--font-weight-bold);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-shadow: none;
  border-radius: var(--tab-border-radius);
  line-height: calc(var(--tab-group-label-height) - var(--tab-group-line-thickness));
  min-height: var(--tab-group-label-height);
  min-width: var(--tab-group-label-height);
  max-width: 10em;
  padding-inline: var(--tab-group-label-padding);
  color: light-dark(var(--tab-group-color-pale), var(--tab-group-label-text-dark));
  background-color: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
  margin: auto 0;

  tab-group[collapsed] > .tab-group-label-container > & {
    background-color: light-dark(var(--tab-group-color-pale), var(--tab-group-color));
    color: light-dark(var(--tab-group-color), var(--tab-group-color-pale));
    outline: 1px solid light-dark(var(--tab-group-color), var(--tab-group-color-pale));
    outline-offset: -1px;
  }

  #tabbrowser-tabs[orient="vertical"]:not([expanded]) & {
    width: 24px;
    min-height: 24px;
    max-width: 24px;
    margin: auto;
    font-size: 0;

    &:first-letter {
      font: message-box;
      font-weight: var(--font-weight-bold);
      line-height: 24px;
    }
  }

  #tabbrowser-tabs[orient="vertical"][expanded] & {
    align-self: start;
    max-width: -moz-available;
    margin-block: var(--space-small);
    margin-inline-end: var(--space-medium);
    padding-block: var(--tab-group-label-padding);
  }

  #tabbrowser-tabs[tablist-has-focus] &.tablist-keyboard-focus {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-offset);
  }
}

.tab-group-editor-panel {
  --panel-width: 20em;
  --panel-padding: var(--space-large);
  --panel-separator-margin: var(--panel-separator-margin-vertical) 0;
  font: menu;

  .panel-header {
    min-height: auto;
    > h1 {
      margin-top: 0;
      margin-bottom: var(--space-small);
    }
  }

  toolbarseparator {
    margin-block: var(--space-medium);
  }

  .panel-body {
    padding-block: var(--space-medium);
  }

  &.tab-group-editor-mode-create .tab-group-edit-mode-only,
  &:not(.tab-group-editor-mode-create) .tab-group-create-mode-only {
    display: none;
  }

  .tab-group-editor-name {
    display: flex;
    flex-direction: column;
    > label {
      margin-inline: 0;
      margin-bottom: var(--space-small);
    }
    > input[type="text"] {
      padding: var(--space-medium);
    }
  }

  .tab-group-editor-swatches {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
  }

  input[name="tab-group-color"] {
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
    position: absolute;
  }

  .tab-group-editor-swatch {
    width: 16px;
    height: 16px;
    padding: var(--focus-outline-offset);
    border: var(--focus-outline-width) solid transparent;
    border-radius: var(--border-radius-medium);
    background-clip: content-box;
    background-color: light-dark(var(--tabgroup-swatch-color), var(--tabgroup-swatch-color-invert));

    input:checked + & {
      border-color: var(--focus-outline-color);
    }

    input:focus-visible + & {
      outline: 1px solid var(--focus-outline-color);
      outline-offset: 1px;
    }
  }

  .tab-group-edit-actions,
  .tab-group-delete {
    padding-block: 0;
    > toolbarbutton {
     justify-content: flex-start;
    }
  }

  toolbarbutton {
    margin: 0;
  }
}

#tabGroupEditor_deleteGroup {
  color: var(--text-color-error);
}

/* Tab Overflow */

#tabbrowser-arrowscrollbox {
  &[orient="horizontal"] {
    min-width: 1px;

    &::part(scrollbox) {
      contain: inline-size;
    }

    &:not([scrolledtostart])::part(overflow-start-indicator),
    &:not([scrolledtoend])::part(overflow-end-indicator) {
      width: 7px; /* The width is the sum of the inline margins */
      background-image: radial-gradient(ellipse at bottom,
                                        rgba(0,0,0,0.1) 0%,
                                        rgba(0,0,0,0.1) 7.6%,
                                        rgba(0,0,0,0) 87.5%);
      background-repeat: no-repeat;
      background-position: -3px;
      border-left: .5px solid rgba(255,255,255,.2);
      pointer-events: none;
      position: relative;
      border-bottom: .5px solid transparent;
    }

    &:not([scrolledtostart])::part(overflow-start-indicator) {
      margin-inline: -.5px -6.5px;
    }

    &:not([scrolledtoend])::part(overflow-end-indicator) {
      margin-inline: -6.5px -.5px;
    }

    &:-moz-locale-dir(rtl)::part(overflow-start-indicator),
    &:-moz-locale-dir(ltr)::part(overflow-end-indicator) {
      transform: scaleX(-1);
    }
  }


  &[orient="vertical"] {
    &::part(overflow-start-indicator),
    &::part(overflow-end-indicator) {
      background-image: linear-gradient(
        to top,
        transparent 0%,
        var(--toolbox-bgcolor)
      );
      background-repeat: no-repeat;
      pointer-events: none;

      position: relative;
      height: 16px;
      z-index: 1;

      margin-block: 0 -16px;
    }

    /* Flip the fade at the bottom */
    &::part(overflow-end-indicator) {
      transform: scaleY(-1);
      margin-block: -16px 0;
    }
  }

  &[scrolledtostart]::part(overflow-start-indicator),
  &[scrolledtoend]::part(overflow-end-indicator) {
    opacity: 0;
  }

  &::part(overflow-start-indicator),
  &::part(overflow-end-indicator) {
    transition: opacity 150ms ease;
  }

  /* Scroll arrows */

  &::part(scrollbutton-up),
  &::part(scrollbutton-down) {
    appearance: none;
    background-clip: padding-box;
    border: 4px solid transparent;
    border-radius: calc(var(--tab-border-radius) + 4px);
    fill: var(--toolbarbutton-icon-fill);
    margin: 0;
    padding: 0 calc(var(--toolbarbutton-inner-padding) - 6px);
  }

  #navigator-toolbox:not(:hover) &:not([highlight])::part(scrollbutton-down) {
    transition: 1s background-color ease-out;
  }

  &[highlight]::part(scrollbutton-down) {
    background-color: SelectedItem;
  }

  &:not([scrolledtostart])::part(scrollbutton-up):hover,
  &:not([scrolledtoend])::part(scrollbutton-down):hover {
    background-color: var(--toolbarbutton-hover-background);
    color: inherit;
  }

  &:not([scrolledtostart])::part(scrollbutton-up):hover:active,
  &:not([scrolledtoend])::part(scrollbutton-down):hover:active {
    background-color: var(--toolbarbutton-active-background);
    color: inherit;
  }
}

/* Vertical tabs styling */

#tabbrowser-arrowscrollbox[orient="vertical"] {
  min-height: 1px;

  &::part(scrollbutton-up),
  &::part(scrollbutton-down) {
    display: none;
  }

  &::part(scrollbox) {
    scrollbar-width: thin;
    scrollbar-color: var(--vertical-tabs-scrollbar-color);
    overflow-y: auto;
  }
}

#tabbrowser-arrowscrollbox[orient="vertical"] > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button,
#vertical-tabs-newtab-button {
  appearance: none;
  min-height: var(--tab-min-height);
  line-height: var(--tab-label-line-height);
  border-radius: var(--border-radius-medium);
  padding: 0 calc(var(--tab-inline-padding) - var(--tab-inner-inline-margin));
  width: var(--tab-collapsed-background-width);
  margin-inline: var(--tab-inner-inline-margin);

  #tabbrowser-tabs[orient="vertical"]:not([expanded]) & > .toolbarbutton-text {
    display: none;
  }

  &:hover {
    background-color: var(--tab-hover-background-color);
    outline-color: var(--tab-hover-outline-color);
  }

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
  }

  > .toolbarbutton-text {
    text-align: start;
    padding-inline-start: var(--tab-icon-end-margin);
  }
}

/* For #vertical-tabs-newtab-button, shown when tabs are overflowing, the gap
 * with the other items comes from the grid-gap on tabbrowser-tabs which is a
 * flex container. #tabs-newtab-button is a child of the arrowscrollbox where
 * we don't want a gap (between tabs), so we have to add some margin.
 */
#tabbrowser-arrowscrollbox[orient="vertical"] > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button {
  margin-block: var(--tab-block-margin);
}

#tabbrowser-tabs[expanded] {
  > #vertical-tabs-newtab-button {
    width: auto;
  }

  > #tabbrowser-arrowscrollbox[orient="vertical"] > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button {
    width: 100%;
  }
}

#vertical-tabs {
  overflow: hidden;
  display: none;

  &[visible] {
    display: flex;
  }
}

#vertical-pinned-tabs-container {
  --tab-inline-padding: calc((calc(var(--tab-collapsed-background-width) + 2 * var(--tab-pinned-margin-inline-expanded) - var(--icon-size-default)) / 2));
  display: none;
  grid-template-columns: repeat(auto-fit, minmax(var(--tab-pinned-min-width-expanded), auto));
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--vertical-tabs-scrollbar-color);
  /* Fit slightly more than 5 tabs + padding before overflowing */
  max-height: calc(5 * var(--tabstrip-min-height) + var(--space-large));

  .tab-content {
    justify-content: center;
  }

  #tabbrowser-tabs[orient="vertical"] > &:not(:empty) {
    display: grid;
  }

  .tab-label-container {
    display: none;
  }

  .tab-background {
    border-radius: var(--border-radius-medium);

    .tabbrowser-tab:not(:hover) > .tab-stack > &:not([selected], [multiselected]) {
      background-color: color-mix(in srgb, currentColor 7%, transparent);
    }
  }

  #tabbrowser-tabs[expanded] > & {
    padding-inline: var(--tab-pinned-container-margin-inline-expanded);

    .tab-background {
      margin-inline: var(--tab-pinned-margin-inline-expanded);
    }
  }
}

#vertical-pinned-tabs-container-separator {
  display: block;
  border-bottom: var(--tabstrip-inner-border);
  margin-inline: var(--tab-inner-inline-margin);

  #tabbrowser-tabs[expanded] > &,
  #vertical-pinned-tabs-container:empty + & {
    display: none;
  }
}

#tabbrowser-tabs[orient="vertical"] {
  --tab-min-width: unset;
  --tab-inline-padding: calc((var(--tab-collapsed-width) - var(--icon-size-default)) / 2);

  min-height: 0;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  grid-gap: var(--space-small);
  padding: 0; /* override tabbox.css on macOS */

  &[overflow]::after {
    content: "";
    border-bottom: var(--tabstrip-inner-border);
    margin-inline: var(--tab-inner-inline-margin);
  }

  .tabbrowser-tab {
    flex: none;
    padding: 0;
  }

  .tab-label-container {
    contain: inline-size;
  }

  .tab-background {
    border-radius: var(--border-radius-medium);
    margin-inline: var(--tab-inner-inline-margin);
  }

  .tab-close-button {
    margin-inline-end: calc(-1 * var(--tab-close-button-padding));
  }

  &:not([expanded]) {
    .tabbrowser-tab {
      padding: 0;

      &[pinned] {
        width: var(--tab-collapsed-width);
      }
    }

    .tab-background {
      width: var(--tab-collapsed-background-width);
    }

    .tab-label-container,
    .tab-close-button {
      display: none;
    }
  }

  &[expanded] {
    --tab-icon-end-margin: 7.5px;

    .tabbrowser-tab {
      max-width: none;

      .tab-close-button {
        margin-inline-end: calc(-1 * var(--tab-close-button-padding));
      }

      &:not(:hover) .tab-close-button:not([selected]) {
        display: none;
      }
    }
  }
}

/* Tab drag and drop */

.tab-drop-indicator {
  width: 12px;
  margin-inline-start: -12px;
  background: url(chrome://browser/skin/tabbrowser/tab-drag-indicator.svg) no-repeat center;
  position: relative;
  z-index: 2;
  pointer-events: none;
}

.dragfeedback-tab {
  appearance: none;
  opacity: 0.65;
  -moz-window-shadow: none;
}

/* Firefox View button and menu item */

#TabsToolbar #firefox-view-button[open] > .toolbarbutton-icon {
  background-color: var(--tab-selected-bgcolor);
  box-shadow: var(--tab-selected-shadow);
  outline-color: var(--tab-selected-outline-color);
  color: var(--tab-selected-textcolor);
  color-scheme: var(--tab-selected-color-scheme);
}

:root:not([privatebrowsingmode]) :is(toolbarbutton, toolbarpaletteitem) ~ #tabbrowser-tabs,
:root[privatebrowsingmode] :is(
  toolbarbutton:not(#firefox-view-button),
  toolbarpaletteitem:not(#wrapper-firefox-view-button)
) ~ #tabbrowser-tabs {
  border-inline-start: var(--tabstrip-inner-border);
  padding-inline-start: calc(var(--tab-overflow-pinned-tabs-width) + 2px);
  margin-inline-start: 2px;
}

:root[privatebrowsingmode] :is(#firefox-view-button, #menu_openFirefoxView) {
  display: none;
}

toolbar:not(#TabsToolbar) #firefox-view-button {
  background-position: top 25% right 25%;

  /* RTL notification dot */
  &:-moz-locale-dir(rtl) {
    background-position-x: left 25%;
  }
}

:is(#widget-overflow-mainView, #widget-overflow-fixed-list) #firefox-view-button {
  background-position: top 25% left 22px;

  &:-moz-locale-dir(rtl) {
    background-position-x: right 22px;
  }
}

/* New tab button */

#tabs-newtab-button,
#vertical-tabs-newtab-button,
#TabsToolbar #new-tab-button {
  list-style-image: url(chrome://global/skin/icons/plus.svg);
}

#tabbrowser-tabs[hasadjacentnewtabbutton]:not([overflow]) ~ #new-tab-button,
#tabbrowser-tabs[orient="horizontal"] > #vertical-tabs-newtab-button,
#tabbrowser-tabs[orient="vertical"]:not([overflow]) > #vertical-tabs-newtab-button,
#tabbrowser-arrowscrollbox[overflowing] > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button,
#tabbrowser-tabs:not([hasadjacentnewtabbutton])[orient="horizontal"] > #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button,
#TabsToolbar[customizing] #tabs-newtab-button {
  display: none;
}

/* All tabs button and menupopup */

#alltabs-button {
  list-style-image: url("chrome://browser/content/firefoxview/view-opentabs.svg");

  > .toolbarbutton-badge-stack > .toolbarbutton-icon {
    --panel-and-palette-icon-size: 20px;
    width: 20px;
    margin: -2px;
  }

  #TabsToolbar & {
    list-style-image: url("chrome://global/skin/icons/arrow-down.svg");

    > .toolbarbutton-badge-stack > .toolbarbutton-icon {
      width: 16px;
      margin: 0;
    }
  }

  #tabbrowser-tabs[hiddensoundplaying] ~ & > .toolbarbutton-badge-stack > .toolbarbutton-badge {
    background: transparent url(chrome://browser/skin/tabbrowser/tab-audio-playing-small.svg);
    box-shadow: none;
    /* Match the color of the button, rather than label default. */
    color: inherit;
    display: block;
    -moz-context-properties: fill, fill-opacity, stroke;
    fill: currentColor;
    stroke: transparent;
    /* "!important" is necessary to override the rule in toolbarbutton.css */
    margin: -7px 0 0 !important;
    margin-inline-end: -4px !important;
    min-width: 12px;
    min-height: 12px;
  }
}

/* The list of tabs is in its own panel-subview-body which will scroll. We don't
   want any padding below the scrollbar, so remove the bottom padding
   from the outer panel-subview-body. */
#allTabsMenu-allTabsView > .panel-subview-body {
  padding-bottom: 0;
}

#allTabsMenu-allTabsView-tabs {
  padding-top: 0;
}

#allTabsMenu-dropIndicatorHolder {
  display: block;
  position: relative;
}

#allTabsMenu-dropIndicator {
  background: url(chrome://browser/skin/tabbrowser/tab-drag-indicator.svg) no-repeat center;
  display: block;
  position: absolute;
  transform: rotate(-90deg);
  width: 12px;
  height: 29px;
  inset-inline-start: 8px;
  top: 0;
  pointer-events: none;

  &:-moz-locale-dir(rtl) {
    transform: rotate(90deg);
  }
}

#allTabsMenu-groupsView {
  flex: auto;
  flex-grow: 0;

  &:empty,
  &:empty + toolbarseparator {
    display: none;
  }
}

.all-tabs-item {
  border-radius: var(--arrowpanel-menuitem-border-radius);
  margin-inline: var(--arrowpanel-menuitem-margin-inline);

  &.grouped {
    /* Indent further by the size of a favicon and the favicon's end margin. */
    margin-inline-start: calc(var(--arrowpanel-menuitem-margin-inline) + 16px + 8px);
  }

  &[selected] {
    font-weight: var(--font-weight-bold);
  }

  > toolbarbutton {
    margin: 0;

    &:hover {
      background-color: var(--panel-item-hover-bgcolor);
    }

    &:hover:active {
      background-color: var(--panel-item-active-bgcolor);
    }

    &.all-tabs-container-indicator {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        inset: 2px -3px;
        background: var(--identity-tab-color);
        width: 2px;
      }
    }
  }
}

.all-tabs-group-item {
  > .subviewbutton-iconic > .toolbarbutton-icon {
    list-style-image: url("chrome://browser/skin/tabbrowser/tab-group-chicklet.svg");
    fill: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
  }

  > .all-tabs-group-saved-group > .toolbarbutton-icon {
    -moz-context-properties: fill, stroke;
    fill: transparent;
    stroke: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
  }
}

.all-tabs-button {
  list-style-image: url("chrome://global/skin/icons/defaultFavicon.svg");
}

.all-tabs-secondary-button {
  -moz-context-properties: fill;
  fill: currentColor;

  > label {
    display: none !important; /* override panelUI-shared.css */
  }

  &:hover {
    opacity: 1;
  }
}

.all-tabs-mute-button[soundplaying] {
  list-style-image: url(chrome://global/skin/media/audio.svg);
}

.all-tabs-mute-button[muted] {
  list-style-image: url(chrome://global/skin/media/audio-muted.svg);
}

.all-tabs-close-button {
  list-style-image: url(chrome://global/skin/icons/close-12.svg);

  > .toolbarbutton-icon {
    margin-inline: 2px !important; /* override panelUI-shared.css */
  }
}

.tab-throbber-tabslist {
  height: 16px;
  width: 16px;

  &[busy] {
    list-style-image: url("chrome://global/skin/icons/loading.svg");
    -moz-context-properties: fill;
    fill: currentColor;
    opacity: 0.7;
  }

  &[progress] {
    color: var(--tab-loading-fill);
    opacity: 1;
  }
}

.menu-iconic.tab-group-icon,
.menuitem-iconic.tab-group-icon,
.subviewbutton-iconic.tab-group-icon {
  list-style-image: url("chrome://browser/skin/tabbrowser/tab-group-chicklet.svg");
  -moz-context-properties: fill, stroke;
  fill: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
  stroke: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
}

.menu-iconic.tab-group-icon-collapsed,
.menuitem-iconic.tab-group-icon-collapsed,
.subviewbutton-iconic.tab-group-icon-collapsed {
  list-style-image: url("chrome://browser/skin/tabbrowser/tab-group-chicklet.svg");
  -moz-context-properties: fill, stroke;
  fill: light-dark(var(--tab-group-color-pale), var(--tab-group-color));
  stroke: light-dark(var(--tab-group-color), var(--tab-group-color-pale));
}

.menu-iconic.tab-group-icon-closed,
.menuitem-iconic.tab-group-icon-closed,
.subviewbutton-iconic.tab-group-icon-closed {
  list-style-image: url("chrome://browser/skin/tabbrowser/tab-group-chicklet.svg");
  -moz-context-properties: fill, stroke;
  fill: transparent;
  stroke: light-dark(var(--tab-group-color), var(--tab-group-color-invert));
}
