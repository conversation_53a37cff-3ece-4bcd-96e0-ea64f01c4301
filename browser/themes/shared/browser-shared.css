/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://browser/skin/browser-colors.css");
@import url("chrome://browser/skin/addons/extension-controlled.css");
@import url("chrome://browser/skin/addons/unified-extensions.css");
@import url("chrome://browser/skin/downloads/indicator.css");
@import url("chrome://browser/skin/tabbrowser/content-area.css");
@import url("chrome://browser/skin/tabbrowser/ctrlTab.css");
@import url("chrome://browser/skin/tabbrowser/fullscreen-and-pointerlock.css");
@import url("chrome://browser/skin/tabbrowser/tabs.css");
@import url("chrome://browser/skin/tabbrowser/tab-hover-preview.css");
@import url("chrome://browser/skin/toolbarbuttons.css");
@import url("chrome://browser/skin/toolbarbutton-icons.css");
@import url("chrome://browser/skin/menupanel.css");
@import url("chrome://browser/skin/urlbar-searchbar.css");
@import url("chrome://browser/skin/identity-block/identity-block.css");
@import url("chrome://browser/skin/notification-icons.css");
@import url("chrome://browser/skin/addon-notification.css");
@import url("chrome://browser/skin/identity-credential-notification.css");
@import url("chrome://browser/skin/urlbarView.css");
@import url("chrome://browser/skin/autocomplete.css");
@import url("chrome://browser/skin/places/editBookmarkPanel.css");
@import url("chrome://browser/skin/searchbar.css");
@import url("chrome://browser/skin/sidebar.css");
@import url("chrome://browser/skin/customizableui/customizeMode.css");
@import url("chrome://browser/skin/UITour.css");
@import url("chrome://browser/skin/formautofill-notification.css");

:root,
body {
  height: 100%;
  width: 100%;
  overflow: clip;
}

:root {
  user-select: none;
  color-scheme: light dark;
  accent-color: var(--color-accent-primary);
  text-rendering: optimizeLegibility;
  min-height: 95px;
  min-width: 95px;

  &:-moz-locale-dir(rtl) {
    direction: rtl;
  }

  &:not([chromehidden~="toolbar"]) {
    min-width: 450px;
    min-height: 120px;

    /* stylelint-disable-next-line media-query-no-invalid */
    @media (-moz-bool-pref: "sidebar.verticalTabs") {
      min-width: 732px;

      @media (-moz-platform: macos) {
        min-width: 670px;
      }
    }
  }

  /* z-indices that fight on the browser stack */
  --browser-stack-z-index-devtools-splitter: 1;
  --browser-stack-z-index-dialog-stack: 2;
  --browser-stack-z-index-rdm-toolbar: 3;

  /* z-indices that fight in the general browser / toolbox area */
  --browser-area-z-index-toolbox: 0;
  --browser-area-z-index-sidebar: 1;
  --browser-area-z-index-tabbox: 2;
  --browser-area-z-index-sidebar-splitter: 3;

  /* on macOS the animation of the toolbox in fullscreen needs the toolbox to be higher */
  --browser-area-z-index-toolbox-while-animating: 4;

  --toolbarbutton-border-radius: 4px;
  --identity-box-margin-inline: 4px;
  --urlbar-min-height: max(32px, 1.4em);
  --urlbar-icon-padding: calc((var(--urlbar-min-height) - 2px /* border */ - 2px /* padding */ - 16px /* icon */) / 2);

  /* This should be used for icons and chiclets inside the input field. It makes
     the gap around them more uniform when they are close to the field edges */
  --urlbar-icon-border-radius: calc(var(--toolbarbutton-border-radius) - 1px);
  --urlbar-searchmodeswitcher-inline-padding: 6px;
  --urlbar-searchmodeswitcher-margin-inline-end: .45em;

  --pocket-icon-fill: #ef4056;

  --lwt-additional-images: none;
  --lwt-background-alignment: right top;
  --lwt-background-tiling: no-repeat;

  --ext-theme-background-transition: background-color 0.1s cubic-bezier(.17,.67,.83,.67);

  --inactive-window-transition: 0.2s ease;
  --inactive-titlebar-opacity: .6;
  /**
   * On macOS and Linux, fading the whole titlebar is the native behavior.
   *
   * On Windows there's no particular standard, but the default .6 opacity
   * feels like too much, specially because Windows "fades" the titlebar of
   * unresponsive applications. Fade substantially less the titlebar on windows
   * to account for this platform difference.
   */
  @media (-moz-platform: windows) {
    --inactive-titlebar-opacity: .75;
  }
  @media (prefers-reduced-motion) {
    --inactive-window-transition: 0s;
  }

  &[lwtheme] {
    color: var(--lwt-text-color);
  }

  &[uidensity=compact] {
    --urlbar-min-height: max(26px, 1.4em);
  }

  &[uidensity=touch] {
    --urlbar-min-height: max(34px, 1.4em);
  }
}

body {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin: 0;
}

/* Toolbox and Toolbars */

#navigator-toolbox {
  appearance: none;
  z-index: var(--browser-area-z-index-toolbox);

  &[fullscreenShouldAnimate] {
    transition: 0.8s margin-top ease-out;
  }

  /* While we are animating / have animated in the titlebar from macOS,
   * the z-indices are subtly different to accommodate the sidebar better.
   *
   * We also give the toolbox a shadow and border in this case.
   */
  &.fullscreen-with-menubar {
    z-index: var(--browser-area-z-index-toolbox-while-animating);
    box-shadow: var(--content-area-shadow);
    border-bottom-color: var(--chrome-content-separator-color);
  }

  /* Themes define a set of toolbox foreground and background colors which we
   * want to use across the toolbox and sidebar areas. That happens via the
   * .browser-toolbox-background class. It's hard to make a theme's background
   * images work with something other than the toolbox, so for now we ignore
   * them on other things like the sidebar that could have
   * .browser-toolbox-background */
  :root[lwtheme] & {
    background-image: var(--lwt-additional-images);
    background-repeat: var(--lwt-background-tiling);
    background-position: var(--lwt-background-alignment);
  }

  /* When a theme defines both theme_frame and additional_backgrounds, show
     the latter atop the former. */
  :root[lwtheme-image] & {
    background-image: var(--lwt-header-image), var(--lwt-additional-images);
    background-repeat: no-repeat, var(--lwt-background-tiling);
    background-position: right top, var(--lwt-background-alignment);
  }
}

.browser-toolbox-background {
  position: relative;
  background-color: var(--toolbox-bgcolor);
  color: var(--toolbox-textcolor);

  will-change: background-color;
  transition: background-color var(--inactive-window-transition);

  &:-moz-window-inactive {
    background-color: var(--toolbox-bgcolor-inactive);
    color: var(--toolbox-textcolor-inactive);
  }

  :root[customizing] & {
    border-bottom-style: none;
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-platform: macos) and (-moz-bool-pref: "browser.theme.native-theme") {
    /* Don't make the toolbox vibrant when in full-screen. macOS fullscreen has a
     * native titlebar outside of the window (revealed on hover) anyways. */
    :root[customtitlebar]:not([lwtheme], [macOSNativeFullscreen]) & {
      background-color: transparent;

      /* This is conceptually a background, but putting this on a
       * pseudo-element avoids it from suppressing the chrome-content separator
       * border, etc */
      &::after {
        -moz-default-appearance: -moz-window-titlebar;
        appearance: auto;
        content: "";
        position: absolute;
        inset: 0;
        z-index: -1;
      }
    }
  }
}

.browser-toolbar {
  appearance: none;
  /* Reset linux padding */
  padding: 0;
  border-style: none;
  background-color: transparent;
  color: inherit;

  /* stylelint-disable-next-line media-query-no-invalid */
  @media not (-moz-bool-pref: "sidebar.verticalTabs") {
    &:not(.browser-titlebar) {
      background-color: var(--toolbar-bgcolor);
      color: var(--toolbar-color);
    }
  }
}

/* Title bar */

.browser-titlebar {
  -moz-window-dragging: drag;

  :root[customtitlebar] &,
  :root[customtitlebar] & #urlbar:popover-open {
    will-change: opacity;
    transition: opacity var(--inactive-window-transition);

    &:-moz-window-inactive {
      opacity: var(--inactive-titlebar-opacity);
    }
  }
}

.titlebar-buttonbox-container {
  order: 1000;

  @media (-moz-platform: macos) and (not (-moz-mac-rtl)) {
    &:-moz-locale-dir(ltr) {
      order: -1;
    }
  }

  @media (-moz-mac-rtl) {
    &:-moz-locale-dir(rtl) {
      order: -1;
    }
  }

  #toolbar-menubar[autohide="true"]:not([inactive]) ~ :is(#TabsToolbar, #nav-bar) > & {
    visibility: hidden;
  }

  :root:not([customtitlebar], [inFullscreen]) & {
    display: none;
  }
}

.titlebar-spacer {
  width: 40px;

  :root[inFullscreen] &,
  :root:not([customtitlebar]) & {
    display: none;
  }

  @media (-moz-platform: windows) {
    :root:not([sizemode=normal]) &[type="pre-tabs"] {
      display: none;
    }
  }

  @media (-moz-platform: linux) and (-moz-gtk-csd-reversed-placement: 0) {
    :root:not([sizemode=normal]) &[type="pre-tabs"],
    :root[gtktiledwindow=true] &[type="pre-tabs"] {
      display: none;
    }
  }

  @media (-moz-gtk-csd-reversed-placement) {
    :root:not([sizemode=normal]) &[type="post-tabs"],
    :root[gtktiledwindow=true] &[type="post-tabs"] {
      display: none;
    }
  }

  @media (max-width: 500px) {
    &[type="post-tabs"] {
      display: none;
    }
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "sidebar.verticalTabs") {
    &[type="pre-tabs"], &[type="post-tabs"] {
      display: none;
    }
  }
}

/* Hide the TabsToolbar/nav-bar titlebar controls if the menubar is permanently shown.
 * (That is, if the menu bar doesn't autohide, and we're not in a fullscreen or
 * popup window.) */
:root:not([chromehidden~="menubar"], [inFullscreen]) #toolbar-menubar[autohide="false"] ~ :is(#TabsToolbar, #nav-bar) > :is(.titlebar-buttonbox-container, .titlebar-spacer) {
  display: none;
}

/* Only show the titlebar buttons and spacers in the first visible toolbar. */
#navigator-toolbox:not([tabs-hidden]) > #nav-bar > :is(.titlebar-buttonbox-container, .titlebar-spacer) {
  display: none;
}

:root:not([sizemode=maximized], [sizemode=fullscreen]) .titlebar-restore,
:root:is([sizemode=maximized], [sizemode=fullscreen]) .titlebar-max {
  display: none;
}

.titlebar-buttonbox {
  appearance: auto;
  -moz-default-appearance: -moz-window-button-box;
  position: relative;

  :root:not([customtitlebar], [sizemode=fullscreen]) & {
    display: none;
  }
}

/* Menu bar */

#toolbar-menubar {
  &[autohide="true"] {
    overflow: hidden;

    /* For linux and windows, when temporarily showing the menu bar, make it at least
       as tall as the tab toolbar such that the window controls don't appear to move up.
       For MacOS, autohide is never true on the menubar.
    */
    :root[customtitlebar] #navigator-toolbox:not([tabs-hidden]) > & {
      min-height: var(--tabstrip-min-height);
    }
    /* adjust the height of the auto-hiding toolbar menubar when the tabstrip is
       hidden and we need to match the height of the nav-bar. */
    :root[customtitlebar] #navigator-toolbox[tabs-hidden] > & {
      min-height: calc(var(--urlbar-min-height) + 2 * var(--urlbar-padding-block));
    }

    &[inactive]:not([customizing]) {
      min-height: 0 !important;
      height: 0 !important;
      padding: 0 !important;
      appearance: none !important;
    }
  }

  &:not([autohide]) {
    visibility: collapse;
  }
}

/* Hide menu elements intended for keyboard access support */
#main-menubar[openedwithkey=false] .show-only-for-keyboard {
  display: none;
}

/* Profiles menu */

.menuitem-iconic-profile {
  -moz-context-properties: fill, stroke;
  fill: var(--menu-profiles-theme-bg);
  stroke: var(--menu-profiles-theme-fg);
}

/* Navigation toolbar */

#nav-bar {
  /* The toolbar buttons that animate use position:absolute and require a
   * positioned #nav-bar. */
  position: relative;
  border-top: 0.01px var(--tabs-navbar-separator-style) var(--tabs-navbar-separator-color);

  &[tabs-hidden] {
    border-top-style: none;
  }

  :root[sessionrestored][lwtheme] & {
    transition: var(--ext-theme-background-transition);
  }
}

#nav-bar-customization-target {
  /* Don't grow if potentially-user-sized elements (like the searchbar or the
   * bookmarks toolbar item list) are too wide. This forces them to flex to the
   * available space as much as possible, see bug 1795260. */
  min-width: 0;

  /* Add space to beginning of toolbar and make that space click the first <toolbarbutton> */
  > :is(toolbarbutton, toolbaritem):first-child,
  > toolbarpaletteitem:first-child > :is(toolbarbutton, toolbaritem) {
    padding-inline-start: var(--toolbar-start-end-padding);
  }
}

/* stylelint-disable-next-line media-query-no-invalid */
@media not (-moz-bool-pref: "sidebar.verticalTabs") {
  #vertical-spacer {
    display: none;
  }
}

/* Bookmarks toolbar */

#PersonalToolbar {
  /* Make the toolbar not take any vertical space when it's empty, before
     bookmarks load. Otherwise, showing the toolbar might resize the content
     area twice. */
  min-height: 0;
  padding-inline: 6px;

  :root[uidensity=compact] & {
    padding-inline: 2px;
  }

  /* Vertical sliding animation */

  overflow: clip;
  max-height: 4em;

  :root[sessionrestored] &:not(.instant) {
    transition: min-height 170ms ease-out, max-height 170ms ease-out, var(--ext-theme-background-transition);

    &[collapsed=true] {
      transition: min-height 170ms ease-out, max-height 170ms ease-out, visibility 170ms linear;
    }
  }

  &[collapsed=true] {
    max-height: 0;

    &.instant {
      visibility: collapse;
    }
  }

  /* Bookmarks toolbar empty message */

  /* If the toolbar is not initialized set a zero width, but retain height. */
  &[collapsed=false]:not([initialized]) > #personal-toolbar-empty {
    visibility: hidden;
    width: 0;
    overflow-x: hidden;
  }

  /* Bookmarks toolbar in customize mode */

  &[customizing] {
    outline: 1px dashed;
    outline-offset: -3px;
    /* Avoid the toolbar having no height when there's no items in it */
    min-height: 22px;
  }
}

#personal-toolbar-empty-description {
  white-space: nowrap;
}

#personal-bookmarks {
  position: relative;
  -moz-window-dragging: inherit;
}

#fxms-bmb-button-wrapper,
toolbarpaletteitem > #personal-bookmarks > #PlacesToolbar,
#personal-bookmarks:is([overflowedItem=true], [cui-areatype="panel"]) > #PlacesToolbar {
  display: none;
}

/* Bookmarks toolbar drop indicator */

#PlacesToolbarDropIndicatorHolder {
  display: block;
  position: absolute;
}

#PlacesToolbarDropIndicator {
  list-style-image: url(chrome://browser/skin/toolbar-drag-indicator.svg);
  pointer-events: none;
}

/* Menupopup drop indicators */

menupopup::part(drop-indicator-container) {
  /* TODO(emilio): When menupopups get ported to modern flex layout we can
   * probably simplify this substantially, by making the indicator position:
   * absolute or so (which was never properly supported in XUL). The container
   * should become completely unnecessary then. */
  min-width: 0;
  min-height: 0;
  max-width: 100%;
  max-height: 100%;
}

menupopup::part(drop-indicator-bar) {
  position: relative;
  /* these two margins must together compensate the indicator's height */
  margin-block: -1px;
}

menupopup::part(drop-indicator) {
  list-style-image: none;
  height: 2px;
  margin-inline-end: -4em;
  background-color: SelectedItem;
  pointer-events: none;
}

/* Back / Forward buttons context menu */

.unified-nav-current {
  font-weight: var(--font-weight-bold);
}

.unified-nav-back,
.unified-nav-forward {
  -moz-context-properties: fill;
  fill: currentColor;
}

.unified-nav-back[_moz-menuactive]:-moz-locale-dir(ltr),
.unified-nav-forward[_moz-menuactive]:-moz-locale-dir(rtl) {
  list-style-image: url("chrome://browser/skin/back.svg") !important;
}

.unified-nav-forward[_moz-menuactive]:-moz-locale-dir(ltr),
.unified-nav-back[_moz-menuactive]:-moz-locale-dir(rtl) {
  list-style-image: url("chrome://browser/skin/forward.svg") !important;
}

/* Find bar */

.browserContainer > findbar {
  background-color: var(--toolbar-bgcolor);
  color: var(--toolbar-color);
  color-scheme: var(--toolbar-color-scheme);
  border-top-color: var(--chrome-content-separator-color);

  :root[lwtheme] & {
    background-color: var(--lwt-accent-color);
    background-image: linear-gradient(var(--toolbar-bgcolor), var(--toolbar-bgcolor)), var(--lwt-additional-images);
    background-repeat: no-repeat, var(--lwt-background-tiling);
    background-position: right bottom, var(--lwt-background-alignment);
    background-position-y: bottom !important;
  }

  :root[lwtheme]:not([lwtheme-image]) &:-moz-window-inactive {
    background-color: var(--lwt-accent-color-inactive, var(--lwt-accent-color));
  }

  :root[lwtheme-image] & {
    background-image: linear-gradient(var(--toolbar-bgcolor), var(--toolbar-bgcolor)), var(--lwt-header-image), var(--lwt-additional-images);
    background-repeat: no-repeat, no-repeat, var(--lwt-background-tiling);
    background-position: center, right bottom, var(--lwt-background-alignment);
  }
}

/* Private browsing indicator */

.private-browsing-indicator-with-label {
  align-items: center;
  margin-inline: 7px;

  :root:not([privatebrowsingmode=temporary]) &,
  #navigator-toolbox:not([tabs-hidden]) #nav-bar > & {
    display: none;
  }

  #navigator-toolbox[tabs-hidden] #nav-bar > & {
    margin-inline-end: 12px;

  /* Hide the private browsing indicator
    label when vertical tabs are enabled */
    > .private-browsing-indicator-label {
      display: none;
    }
  }
}

.private-browsing-indicator-icon {
  list-style-image: url("chrome://global/skin/icons/indicator-private-browsing.svg");
  width: 16px;
  height: 16px;
}

/* Content analysis indicator */

:root:not([contentanalysisactive]) #content-analysis-indicator {
  display: none;
}

/* Various panels and popup notifications */

#screenshotsPagePanel {
  position: relative;
  max-height: 0;
  z-index: 1; /* Above the web content in this stack. */
}

/* Override theme colors since the picker uses extra colors that
   themes cannot set */
#DateTimePickerPanel {
  color-scheme: env(-moz-content-preferred-color-scheme);
  --arrowpanel-background: Field;
  --arrowpanel-color: FieldText;
}

#widget-overflow .webextension-popup-browser {
  background: #fff;

  /* height set via JS can be lower than the stack min-height,
     ensure the browser takes up at least that min-height */
  min-height: 100%;
}

#addon-progress-notification-progressmeter {
  margin: 2px 4px;
}

#invalid-form-popup {
  white-space: pre-wrap;
  word-break: break-word;
  unicode-bidi: plaintext;

  > description {
    max-width: 280px;
  }
}

.popup-notification-invalid-input {
  box-shadow: 0 0 1.5px 1px red;

  &[focused] {
    box-shadow: 0 0 2px 2px rgba(255,0,0,0.4);
  }
}

.popup-notification-description[popupid="webauthn-prompt-register-direct"] {
  white-space: pre-line;
}

/* Contextual Feature Recommendation popup-notification */

#cfr-notification-header {
  width: 100%;
  display: block;
  text-align: center;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
}

#cfr-notification-header-stack {
  width: 100%;
}

#cfr-notification-header-label {
  margin: 9px;
  font-weight: 600;
}

#cfr-notification-header-link {
  margin: 7px;
  color: inherit;
  cursor: default;
  justify-self: end;
}

#cfr-notification-header-image {
  width: 19px;
  height: 19px;
  padding: 2px;
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: 0.6;
  list-style-image: url(chrome://global/skin/icons/help.svg);

  &:hover {
    background-color: hsla(0,0%,70%,.2);
    border-radius: 2px;
  }
}

#contextual-feature-recommendation-notification {
  width: 400px;

  .popup-notification-icon {
    margin-inline-end: 4px;
  }

  .cfr-doorhanger-large-icon {
    width: 64px;
    height: 64px;
    margin-inline-end: 12px;
  }

  .cfr-doorhanger-medium-icon {
    width: 50px;
    height: 50px;
    margin-inline-end: 12px;
  }

  .popup-notification-body-container {
    width: 100%;
    padding-bottom: 2px;
  }

  popupnotificationcontent {
    margin-top: 0;
  }

  description {
    margin-bottom: 0;
  }

  &[data-notification-bucket="CFR_MILESTONE_MESSAGE"] {
    &:not([hidden]) {
      display: flex;
      flex-direction: row;
      align-items: center;
      background: radial-gradient(circle farthest-side at top right, var(--short-notification-gradient), var(--short-notification-background));
      width: unset;
      max-width: 700px;
      overflow-wrap: break-word;
      padding: 4px;
    }

    &:-moz-locale-dir(rtl) {
      background: radial-gradient(circle farthest-side at top left, var(--short-notification-gradient), var(--short-notification-background));
    }

    .popup-notification-body-container,
    .popup-notification-footer-container,
    #cfr-notification-header-link {
      display: none;
    }

    #cfr-notification-header {
      box-shadow: none;
    }

    description {
      font-weight: 400;
      margin: unset;
      margin-inline: 12px;
    }

    .panel-footer,
    .popup-notification-header-container {
      display: inline-flex;
    }

    .panel-footer {
      float: inline-end;
      background-color: transparent;
      flex-direction: row-reverse;
      /* Override doorhanger default styles that increase the size of the button */
      margin: 0;
    }

    .popup-notification-primary-button {
      background-color: rgba(216, 216, 216, 0.2);
      color: white;
      border: none;
      border-radius: 2px;
      margin: 4px;
      padding: 3px 12px;
      flex: 1;
      min-height: 22px;

      &::after {
        content: url(chrome://global/skin/icons/arrow-left-12.svg);
        -moz-context-properties: fill;
        fill: currentColor;
        transform: translateY(2px);
        float: inline-end;
      }

      &:-moz-locale-dir(ltr)::after {
        content: url(chrome://global/skin/icons/arrow-right-12.svg);
      }

      &:hover {
        background-color: rgba(216, 216, 216, 0.4);
      }

      &:hover:active {
        background-color: rgba(216, 216, 216, 0.5);
      }
    }

    .popup-notification-secondary-button {
      margin: 3px;
      border: none;
      border-radius: 2px;
      background-image: url(chrome://global/skin/icons/close.svg);
      background-color: transparent;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 10px;
      -moz-context-properties: fill;
      fill: white;
      min-height: 22px;
      width: 22px;
      padding: 0;
      align-self: center;
      flex: none;
      font-size: 0;

      &:hover {
        background-color: rgba(216, 216, 216, 0.4);
      }
    }

    .popup-notification-header-container {
      color: white;
      max-width: unset;
    }
  }

  &[data-notification-bucket="CFR_SOCIAL_TRACKING_PROTECTION"] {
    width: 386px;
  }

  &[data-notification-category="addon_recommendation"] {
    .popup-notification-description > b {
      padding-inline-start: 5px;
    }

    #cfr-notification-footer-learn-more-link {
      margin-inline-start: 5px;
    }

    #cfr-notification-feature-steps,
    #cfr-notification-header-image {
      display: none;
    }

    #cfr-notification-header-label {
      margin-block-end: 9px;
    }
  }

  &[data-notification-category="icon_and_message"] {
    /*
     * `icon_and_message` CFR doorhanger with: icon, title and subtitle.
     * No panel header is shown
     */
    #cfr-notification-header,
    .popup-notification-footer-container {
      display: none;
    }

    .popup-notification-description {
      font-weight: 600;
      margin-bottom: 15px;
    }

    popupnotificationcontent {
      display: block; /* This forces the subtitle content to wrap */
    }
  }
}

#cfr-notification-footer-text-and-addon-info {
  display: block;
  padding: 10px var(--arrowpanel-padding);
}

#cfr-notification-feature-steps {
  display: flex;
  flex-direction: column;
  margin-top: 10px;

  li {
    margin-inline-start: 10px;
    position: relative;
    display: list-item;

    &:not(:last-child) {
      margin-bottom: 5px;
    }
  }
}

#cfr-notification-footer-text,
#cfr-notification-footer-users,
#cfr-notification-footer-learn-more-link {
  margin: 0;
}

#cfr-notification-footer-addon-info {
  align-items: center;
  display: flex;
  margin-block: 10px 6px;
}

#cfr-notification-footer-filled-stars,
#cfr-notification-footer-empty-stars {
  -moz-context-properties: fill, fill-opacity;
  background-image: url(chrome://browser/skin/bookmark.svg);
  fill: currentColor;
  fill-opacity: 0.7;
  height: 16px;
}

#cfr-notification-footer-filled-stars:-moz-locale-dir(rtl),
#cfr-notification-footer-empty-stars {
  background-position-x: right;
}

#cfr-notification-footer-filled-stars,
#cfr-notification-footer-empty-stars:-moz-locale-dir(rtl) {
  background-position-x: left;
}

#cfr-notification-footer-empty-stars[tooltiptext] {
  margin-inline-end: 6px;
  opacity: 0.3;
}

#cfr-notification-footer-users {
  opacity: 0.7;
}

#cfr-notification-footer-spacer {
  flex-grow: 1;
}

/* Sharing tabs warning */

#sharing-tabs-warning-panel {
  max-width: 400px;

  > hbox[type="window"] > vbox > label > #sharing-warning-screen-panel-header {
    display: none;
  }

  > hbox[type="screen"] > vbox > label > #sharing-warning-window-panel-header {
    display: none;
  }
}

#sharing-warning-proceed-to-tab {
  appearance: none;
  border-style: none;
  margin: 0;
  background-color: rgb(0,96,223);
  color: rgb(255,255,255);
  border-radius: 5px;
  padding: 10px;
  margin-top: 15px;
  margin-bottom: 10px;

  &:hover {
    background-color: rgb(0,62,170);
  }
}

/* webRTC device sharing selector */

.webRTC-selectDevice-selector-container {
  display: flex;
  align-items: center;

  > menulist {
    flex: 1;
    min-width: 0;
  }
}

#webRTC-selectSpeaker-richlistbox {
  flex: 1;
  /* Keep the richlistbox small enough that a large number of devices doesn't
     push the main and secondary action buttons off the bottom of the
     popupnotification window when limited by screen size.  Only enough
     richlistitems need to be shown to indicate that there are options
     available but give enough space for the common case of only a few
     devices.  */
  max-height: 600px;
}

#webRTC-selectCamera-label,
#webRTC-selectMicrophone-label {
  display: none;
}

.webRTC-selectDevice-icon {
  -moz-context-properties: fill;
  fill: currentColor;
  margin-inline-end: 8px;
  /* Setting width explicitly so it's still visible if the device label is very long */
  width: 16px;
}

#webRTC-selectCamera-icon {
  list-style-image: url("chrome://browser/skin/notification-icons/camera.svg");
}

#webRTC-selectMicrophone-icon {
  list-style-image: url("chrome://browser/skin/notification-icons/microphone.svg");
}

#webRTC-selectSpeaker-icon {
  list-style-image: url("chrome://browser/skin/notification-icons/speaker.svg");
}

/* TODO: move these to popupnotification.css */

popupnotificationcontent {
  margin-block-start: 8px;
}

.popup-notification-body menulist {
  margin: 0;
}

.popup-notification-checkbox {
  margin-block: 12px 0;
}

.popup-notification-body-container {
  padding-block-end: 0;
}

.popup-notification-body :is(description, label:not(.learnMore), checkbox, input) {
  margin-inline: 0;
}

.popup-notification-description {
  font-weight: 600;
  margin-top: 0;

  > b {
    font-weight: inherit;
  }
}

/* Password Manager Doorhanger */

.ac-dropmarker {
  appearance: none;
  justify-self: end;
  margin-inline-end: 8px;
  align-self: center;
  content: url(chrome://global/skin/icons/arrow-down-12.svg);
  -moz-context-properties: fill;
  fill: var(--input-color, -moz-DialogText);
}

.ac-has-end-icon {
  text-overflow: ellipsis;
  overflow: hidden;
}

.popup-notification-body input.ac-has-end-icon {
  --input-padding-from-arrow-side: calc(8px + 16px + 8px); /* spacing from the end + arrow element width + extra spacing) */
  padding-inline-end: var(--input-padding-from-arrow-side);
}

/* Avoid overlapping the text on the arrow icon when switching input text direction */
.popup-notification-body:-moz-locale-dir(rtl) input.ac-has-end-icon:dir(ltr) {
  padding-left: var(--input-padding-from-arrow-side);
  padding-right: 8px;
}
.popup-notification-body:-moz-locale-dir(ltr) input.ac-has-end-icon:dir(rtl) {
  padding-right: var(--input-padding-from-arrow-side);
  padding-left: 8px;
}

.password-notification-label {
  margin-block-start: 16px;

  /**
   * There's already a margin-block-start of 8px on popupnotificationcontent,
   * so the first label only needs 8px of margin-block-start to match the
   * spec.
   */
  &:first-of-type {
    margin-block-start: 8px;
  }
}

#password-notification-visibilityToggle {
  margin-block-start: 14px;

  > .checkbox-label-box > .checkbox-label {
    opacity: 0.7;
  }
}

#tab-notification-deck {
  display: block;
}

/* Relay Integration */

.relay-integration-header {
  display: flex;
  align-items: center;
  font-size: 2em;

  > div:nth-of-type(1) {
    width: 32px;
    height: 32px;
    background-image: url("chrome://browser/content/logos/relay.svg");
    background-repeat: no-repeat;
  }

  > div:nth-of-type(2) {
    direction: ltr;
    margin-inline: 8px;

    > span:first-of-type {
      font-weight: bold;
    }

    > span:last-of-type {
      font-weight: normal;
    }
  }
}

.relay-integration-header-variation {
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: 1.5em;
  margin: auto;

  > p {
    margin-block: auto;
  }
}

.popup-notification-description[popupid="relay-integration-offer-notification"] > div:first-of-type {
  font-weight: normal;
  display: grid;
  grid-template-columns: auto;
  grid-template-rows: auto auto auto auto;
}

#relay-integration-reuse-masks-notification > popupnotificationcontent > div {
  font-weight: normal;
  display: grid;
  grid-template-columns: auto;
  grid-template-rows: auto auto auto auto;
}

.reusable-relay-masks {
  display: flex;
  flex-direction: column;

  > button {
    padding: 4px;
    margin: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    > span:first-child {
      margin-inline-end: 20px;
    }

    > span:last-child {
      opacity: 0.6;
    }
  }
}

/* History swipe navigation animation */

#historySwipeAnimationContainer {
  overflow: hidden;
  pointer-events: none;
}

#historySwipeAnimationPreviousArrow,
#historySwipeAnimationNextArrow {
  --swipe-nav-icon-primary-color: SelectedItemText;
  --swipe-nav-icon-accent-color: SelectedItem;

  will-change: transform;

  @media not (prefers-contrast) {
    /* TODO: Should this be in browser-colors to respect system theming on Linux? */
    --swipe-nav-icon-primary-color: light-dark(#0060DF, #00DDFF);
    --swipe-nav-icon-accent-color: light-dark(#FFFFFF, #000000);
  }

  > svg {
    margin: auto 0;
  }
}

#historySwipeAnimationPreviousArrow:-moz-locale-dir(rtl),
#historySwipeAnimationNextArrow:-moz-locale-dir(ltr) {
  transform: scaleX(-1);
}

.swipe-nav-icon-circle,
.swipe-nav-icon-circle-outline,
.swipe-nav-icon-arrow {
  transition: fill 1s;
}

@media not (-moz-platform: macos) {
  /*
   * Styles for the swipe navigation SVG icon.
   *
   * `will-navigate` class name is added to the <svg> element when the user's
   * swipe gesture reached to the point where history navigation will be
   * triggered. It's used for triggering three animations, an arrow color change,
   * a circle color change and a glowing effect outer the circle.
   */
  .swipe-nav-icon-circle {
    fill: var(--swipe-nav-icon-accent-color);
    stroke: var(--swipe-nav-icon-primary-color);

    .will-navigate > & {
      fill: var(--swipe-nav-icon-primary-color);
    }
  }

  .swipe-nav-icon-circle-outline {
    fill: var(--swipe-nav-icon-primary-color);
    stroke: var(--swipe-nav-icon-primary-color);
    transition: opacity 1s;
    will-change: opacity; /* a workaround for bug 1804189 */
    opacity: 0;

    .will-navigate > & {
      transition-delay: 0.2s;
      opacity: 0.2;
    }
  }

  .swipe-nav-icon-arrow {
    fill: var(--swipe-nav-icon-primary-color);

    .will-navigate > & {
      fill: var(--swipe-nav-icon-accent-color);
      stroke: var(--swipe-nav-icon-accent-color);
    }
  }
}

@media (-moz-platform: macos) {
  /*
   * Styles for the swipe navigation SVG icon.
   *
   * `will-navigate` class name is added to the <svg> element when the user's
   * swipe gesture reached to the point where history navigation will be
   * triggered.
   */
  .swipe-nav-icon-circle-outline,
  .swipe-nav-icon-arrow {
    fill: var(--swipe-nav-icon-primary-color);

    .will-navigate > & {
      fill: var(--swipe-nav-icon-accent-color);
    }
  }

  .swipe-nav-icon-circle {
    fill: var(--swipe-nav-icon-accent-color);

    .will-navigate > & {
      fill: var(--swipe-nav-icon-primary-color);
    }
  }
}

/* TODO: move this to toolkit */
panel toolbarseparator {
  appearance: none;
  min-height: 0;
  border-top: 1px solid var(--panel-separator-color);
  border-bottom: none;
  margin: var(--panel-separator-margin);
  padding: 0;
}

/* Picture-in-Picture panel */

#PictureInPicturePanel {
  width: var(--menu-panel-width-wide);
  font: caption;
}

#PictureInPicturePanelBody {
  gap: 8px;
  padding: 8px 16px;
}

#PictureInPicturePanelFooter {
  margin: 8px 16px 16px;
}

/* Shopping sidebar */

shopping-sidebar {
  border-inline-start: 1px solid var(--chrome-content-separator-color);
  min-width: 16.92em;
  max-width: 32.31em;
  width: 320px;
  box-sizing: content-box;
}
