<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>
  .thermometer-stroke {
    stroke: context-stroke #7542E5;
  }
  .thermometer-fill {
    fill: context-fill #7542E5;
  }
  .snowflake-stroke {
    stroke: context-stroke #80808F;
  }
  .snowflake-fill {
    fill: context-fill #80808F;
  }

  @media (prefers-color-scheme: dark) {
    .thermometer-stroke {
      stroke: context-stroke #D9BFFF;
    }
    .thermometer-fill {
      fill: context-fill #D9BFFF;
    }
    .snowflake-stroke {
      stroke: context-stroke white;
    }
    .snowflake-fill {
      fill: context-fill white;
    }
  }
</style>
<g clip-path="url(#clip0_1504_37149)">
<path class="thermometer-stroke" d="M11.083 14.7962V8.42919C11.083 7.14019 10.038 6.09619 8.75 6.09619C7.461 6.09619 6.417 7.14119 6.417 8.42919V14.7962C5.566 15.5632 5.081 16.7272 5.305 18.0092C5.552 19.4232 6.708 20.5872 8.122 20.8332C10.332 21.2172 12.25 19.5262 12.25 17.3882C12.25 16.3552 11.794 15.4372 11.083 14.7962Z" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="thermometer-stroke" d="M8.75 17.3672V14.6172" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="thermometer-fill" d="M8.75 19.1172C9.57843 19.1172 10.25 18.4456 10.25 17.6172C10.25 16.7888 9.57843 16.1172 8.75 16.1172C7.92157 16.1172 7.25 16.7888 7.25 17.6172C7.25 18.4456 7.92157 19.1172 8.75 19.1172Z"/>
<path class="snowflake-stroke" d="M15.5229 2.77625V13.3222" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-fill" d="M13 7.47119H20.795C21.114 7.47119 21.373 7.72919 21.373 8.04819C21.373 8.36719 21.114 8.62519 20.795 8.62519H13V7.47119Z"/>
<path class="snowflake-stroke" d="M20.25 5.50317L17.705 8.04917" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-stroke" d="M20.25 10.5942L17.705 8.04919" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-stroke" d="M18.0681 3.50317L15.5231 6.04917" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-stroke" d="M12.9771 3.50317L15.5231 6.04917" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-stroke" d="M18.0681 12.5942L15.5231 10.0492" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snowflake-stroke" d="M12.9771 12.5942L15.5231 10.0492" stroke-width="1.1552" stroke-miterlimit="10" stroke-linecap="round"/>
</g>
<defs>
<clipPath id="clip0_1504_37149">
<rect fill="white" width="22" height="22" transform="translate(0 0.617188)"/>
</clipPath>
</defs>
</svg>
