<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>
  .cloud {
    fill: context-fill #5B5B66;
  }
  .snow {
    stroke: context-stroke #80808F;
  }

  @media (prefers-color-scheme: dark) {
    .cloud {
      fill: context-fill #BFBFC9;
    }
    .snow {
      stroke: context-stroke #FFFFFF;
    }
  }
</style>
<g clip-path="url(#clip0_1504_37131)">
<path class="cloud" d="M2.00103 11.7762C1.97803 10.8052 2.30003 9.85619 2.90803 9.09919C3.49503 8.34519 4.32203 7.81319 5.25203 7.59419C5.50003 6.45619 6.13503 5.44119 7.04803 4.71919C7.95303 4.00519 9.07303 3.61719 10.226 3.61719C11.379 3.61719 12.498 4.00519 13.404 4.71919C14.304 5.41219 14.938 6.39419 15.2 7.49919H15.474C16.033 7.49319 16.587 7.59819 17.105 7.80819C17.623 8.01819 18.095 8.32819 18.492 8.72119C18.889 9.11419 19.205 9.58119 19.421 10.0972C19.637 10.6132 19.749 11.1662 19.75 11.7252C19.746 12.8182 19.319 13.8672 18.558 14.6522C17.797 15.4372 16.762 15.8962 15.67 15.9342C15.559 15.9342 15.499 15.8832 15.499 15.7882V14.6592C15.499 14.5562 15.559 14.5052 15.67 14.5052C16.022 14.4942 16.369 14.4132 16.689 14.2662C17.009 14.1192 17.297 13.9102 17.535 13.6502C17.907 13.2522 18.154 12.7532 18.246 12.2172C18.338 11.6802 18.272 11.1282 18.055 10.6282C17.838 10.1282 17.48 9.70319 17.024 9.40419C16.569 9.10519 16.036 8.94619 15.491 8.94619H14.08C13.977 8.94619 13.926 8.89519 13.926 8.80119L13.858 8.30519C13.765 7.40719 13.344 6.57419 12.674 5.96819C12.005 5.36219 11.135 5.02419 10.231 5.02119C9.31503 5.01619 8.43103 5.35619 7.75503 5.97519C7.07903 6.59119 6.66103 7.44219 6.58603 8.35419L6.53503 8.78219C6.53603 8.80419 6.53303 8.82719 6.52503 8.84819C6.51703 8.86919 6.50403 8.88819 6.48803 8.90319C6.47103 8.91919 6.45103 8.93019 6.43003 8.93819C6.40803 8.94519 6.38603 8.94719 6.36403 8.94519L5.91003 9.00419C5.22303 9.04419 4.57803 9.35119 4.11403 9.86019C3.76103 10.2522 3.52503 10.7362 3.43603 11.2562C3.34703 11.7762 3.40703 12.3112 3.60903 12.7982C3.81103 13.2852 4.14803 13.7052 4.58003 14.0092C5.01203 14.3132 5.52003 14.4882 6.04703 14.5142C6.14103 14.5142 6.19203 14.5652 6.19203 14.6682V15.8062C6.19503 15.8262 6.19303 15.8462 6.18703 15.8652C6.18103 15.8842 6.17003 15.9012 6.15603 15.9152C6.14203 15.9292 6.12503 15.9402 6.10603 15.9462C6.08703 15.9522 6.06703 15.9542 6.04703 15.9512C5.50503 15.9402 4.97103 15.8202 4.47603 15.6002C3.98103 15.3802 3.53503 15.0632 3.16403 14.6682C2.42703 13.8842 2.01103 12.8522 2.00103 11.7762Z"/>
<path class="snow" d="M11 13.1172V18.6172" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snow" d="M13.5 14.4242L8.5 17.3102" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
<path class="snow" d="M8.5 14.4242L13.5 17.3102" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
</g>
<defs>
<clipPath id="clip0_1504_37131">
<rect width="22" height="22" fill="white" transform="translate(0 0.617188)"/>
</clipPath>
</defs>
</svg>
