/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/in-content/info-pages.css");

@media not (prefers-contrast) {
html.private {
  --in-content-page-color: white;
  --in-content-text-color: white;
  --in-content-page-background: #25003e;
  --link-color: white;
  --link-color-hover: white;
  --link-color-active: white;
  --in-content-banner-background: #f9f9fe;
  --in-content-banner-text-color: #0c0c0d;
  --in-content-button-background-hover: rgba(12,12,13,.1);
  --in-content-button-background-active: rgba(12,12,13,.15);
}
}

/**
 * When the showBanner class is applied, the banner is displayed at the top
 * of the page, and we need to adjust the padding, so that the banner is
 * spread across the full page width.
 */
body.showBanner {
  padding: 0 0 40px;
  min-height: 0;
}

.showBanner > .showPrivate {
  padding: 40px 48px 0;
}

a:link,
.text-link {
  color: inherit;
  text-decoration: underline;

  &:hover {
    color: inherit;

    &:active {
      text-decoration: none;
    }
  }
}

.container {
  max-width: 768px;
}

p {
  line-height: 1.5em;
}

.logo-and-wordmark {
  align-items: center;
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
}

.logo {
  background: image-set(url("chrome://branding/content/about-logo-private.png"), url('chrome://branding/content/<EMAIL>') 2x) no-repeat center center;
  background-size: 96px;
  display: inline-block;
  height: 96px;
  width: 96px;
}

.wordmark {
  display: inline-block;
  background: url("chrome://branding/content/firefox-wordmark.svg") no-repeat center center;
  background-size: 172px;
  margin-inline-start: 15px;
  -moz-context-properties: fill;
  fill: currentColor;
  height: 96px;
  width: 172px;
}

.search-inner-wrapper {
  display: flex;
  height: 48px;
  padding: 0 22px;
  min-width: 70vw;
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.privatebrowsing.felt-privacy-v1") {
  .search-inner-wrapper {
    height: 52px;
    padding: 0;
  }
}

.promo-image-large {
  margin-inline-end: 6px;
  min-width: 160px;
}

.promo-image-small {
  display: none;
}

.promo.top {
  background: rgba(255, 255, 255, .2);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid rgba(255, 255, 255, .3);
}

.promo.top .promo-image-large {
  min-width: 100px;
  margin-inline: 6px;
}

.promo.top ~ .container {
  margin-top: 120px;
}

@media (min-width: 764px) {
  .search-inner-wrapper {
    min-width: 680px;
  }

  .promo-image-large {
    margin-inline-end: 36px;
    min-width: 200px;
  }

  .promo-image-small {
    display: block;
  }

  .promo.top {
    padding: 10px 22%;
  }

  .promo.top .promo-image-large {
    min-width: 120px;
  }

  .promo.top .vpn-promo {
    margin: 10px 0;
  }
}

@media (max-height: 780px) {
  /* On screens with not enough height we need to push down the content
   * to make room for the banner */
  .promo.top ~ .container {
    margin-top: 140px;
  }
}

.search-handoff-button,
.search-handoff-button:active,
.search-handoff-button:enabled:hover:active {
  background: #fff var(--newtab-search-icon) 12px center no-repeat;
  background-size: 24px;
  border: solid 1px rgba(249, 249, 250, 0.2);
  border-radius: 3px;
  box-shadow: 0 1px 4px 0 rgba(12, 12, 13, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.15);
  cursor: text;
  font-size: 15px;
  font-weight: normal;
  margin: 0;
  padding-block: 0;
  padding-inline: 46px 48px;
  position: relative;
  opacity: 1;
  width: 100%;
  -moz-context-properties: fill;
  fill: rgba(12, 12, 13, 0.4);
}

.search-handoff-button.focused:not(.disabled) {
  border: solid 1px #0060df;
  box-shadow: 0 0 0 1px #0060df, 0 0 0 4px rgba(0, 96, 223, 0.3);
}

.search-handoff-button.disabled {
  opacity: 0.5;
  box-shadow: none;
}

.search-handoff-button:dir(rtl),
.search-handoff-button:active:dir(rtl),
.search-handoff-button:enabled:hover:active:dir(rtl) {
  background-position-x: right 12px;
}

.search-inner-wrapper .search-handoff-button:hover,
.search-inner-wrapper .search-handoff-button:hover:active {
  background-color: #fff;
  color: inherit;
}

.search-handoff-button.focused:not(.disabled) .fake-caret {
  display: block;
}

.fake-editable:focus {
  outline: none;
  caret-color: transparent;
}

.fake-editable {
  border: 0;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  opacity: 0;
  padding: 0;
  position: absolute;
  width: 100%;
}

.fake-textbox {
  color: rgb(12, 12, 13);
  opacity: 0.54;
  text-align: start;
}

@keyframes caret-animation {
  to {
    visibility: hidden;
  }
}

.fake-caret {
  animation: caret-animation 1.3s steps(5, start) infinite;
  background-color: rgb(12, 12, 13);
  display: none;
  inset-inline-start: 47px;
  height: 17px;
  position: absolute;
  top: 16px;
  width: 1px;
}

@media (prefers-contrast) {
  .fake-caret {
    background-color: ButtonText;
  }
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.privatebrowsing.felt-privacy-v1") {
  .search-handoff-button,
  .search-handoff-button:hover:not(.focused),
  .search-handoff-button:active,
  .search-handoff-button:enabled:hover:active {
    border: 3px solid ButtonText;
    border-radius: 6px;
    box-shadow: none;

    @media not (prefers-contrast) {
      border-color: #952bb9;
    }
  }

  .search-handoff-button,
  .search-handoff-button:hover:not(.focused),
  .search-handoff-button:active,
  .search-handoff-button:enabled:hover:active,
  .search-inner-wrapper .search-handoff-button:hover,
  .search-inner-wrapper .search-handoff-button:hover:active {
    background-color: ButtonFace;
    background-image: url(chrome://global/skin/icons/search-glass.svg);
    background-repeat: no-repeat;
    background-size: 16px;
    fill: ButtonText;

    @media (prefers-contrast) {
      border-color: ButtonText;
      color: ButtonText;
    }

    @media not (prefers-contrast) {
      background-color: #321c64;
      fill: rgb(251, 251, 254);
    }
  }

  .search-handoff-button.focused:not(.disabled) {
    border-width: 3px;

    @media (prefers-contrast) {
      outline: 2px solid SelectedItem;
    }
  }

  .fake-textbox {
    color: ButtonText;
    font-size: 0.87em;
    opacity: 1;

    @media not (prefers-contrast) {
      color: #fbfbfe;
    }
  }

  @media not (prefers-contrast) {
    .fake-caret {
      background-color: #fbfbfe;
    }
  }
}

.search-banner {
  width: 100%;
  background-color: var(--in-content-banner-background);
  color: var(--in-content-banner-text-color);
}

.banner-body {
  margin: auto;
  width: max-content;
  background-image: url("chrome://global/skin/icons/settings.svg");
  background-position: left 0 top 50px;
  background-repeat: no-repeat;
  background-size: 32px;
  letter-spacing: -.2px;
  padding: 50px 0;
  padding-inline-start: 44px;
}

.banner-body:dir(rtl) {
  background-position-x: right;
}

.banner-body h1 {
  font-size: 18px;
  font-weight: bold;
  color: var(--in-content-banner-text-color);
  line-height: 1em;
  margin: 7px 0;
}

.banner-body p {
  font-size: 15px;
  line-height: 1em;
  margin: 4px 0;
}

.info {
  margin-top: 64px;
  background-color: rgba(0, 0, 0, 0.2);
  background-image: url("chrome://global/skin/icons/indicator-private-browsing.svg");
  background-position: left 32px top 20px;
  background-repeat: no-repeat;
  background-size: 32px;
  border: 1px solid transparent;
  border-radius: 6px;
  letter-spacing: -0.2px;
  padding: 20px;
  padding-inline-start: 76px;
}

.info:dir(rtl) {
  background-position-x: right 32px;
}

.info h1,
.promo h1 {
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
}

.info p {
  margin-top: 0;
  line-height: 1.67;
}

.info #info-title:not([hidden]) + p {
  margin-top: 15px;
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.privatebrowsing.felt-privacy-v1") {
  .info-border {
    border-radius: 8px;
    margin-top: 64px;
    padding: 17px;

    @media not (prefers-contrast) {
      box-shadow: 0 2px 6px 0 rgba(58, 57, 68, 0.20);
      background: rgba(149, 43, 185, 0.5);
    }
  }

  .info {
    background-image: none;
    border-radius: 6px;
    font-size: 0.93em;
    margin: 0;
    padding-inline-start: 20px;
    padding-block-end: 25px;

    @media not (prefers-contrast) {
      background-color: #321c64;
      box-shadow: 0 2px 6px 0 rgba(58, 57, 68, 0.20);
    }
  }

  #info-title:not([hidden]) + #info-body {
    margin-block: 0;
  }

  #info-title {
    font-size: 1em;
    margin-block: 0;
  }
}

.search-banner-close-button {
  float: inline-end;
  /* min-width and min-height override values set on button elements. */
  min-width: 28px;
  min-height: 28px;
  /* Forcing height and line-height ensure the button hover is displayed correctly
     as a square box (and focus box). No text gets displayed here so that's ok. */
  height: 28px;
  line-height: 0;
  margin: 16px;
  padding: 0;
  background-color: inherit;
  border: 0;
}

.search-banner-close-image {
  -moz-context-properties: fill;
  fill: currentColor;
  width: 22px;
}

.promo {
  text-align: center;
  align-items: center;
}

.promo-visible {
  display: flex;
  border: 1px solid transparent;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.promo-dismiss {
  padding: 10px;
  min-width: 28px;
  min-height: 28px;
  display: inline flow-root;
  background: url("chrome://global/skin/icons/close.svg") center no-repeat;
  cursor: pointer;
  -moz-context-properties: fill;
  fill: currentColor;
  position: relative;
  align-self: start;
  top: -11px;
  inset-inline-end: -11px;

  @media not (prefers-contrast) {
    opacity: 0.6;

    &:hover {
      background-color: color-mix(in srgb, currentColor 10%, transparent) !important;
    }

    &:hover:active {
      background-color: color-mix(in srgb, currentColor 20%, transparent) !important;
    }
  }
}

.promo-content {
  width: 100%;
}

.promo-image-large img {
  width: 100%;
}

.promo-cta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* The colors for .promo-cta .primary must be kept in sync with the dark mode
   primary button colors from common-shared.cs */
.promo-cta .primary {
  padding: 0.54em 1.15em;
  margin: 8px 0;
  font-weight: 600;
  cursor: pointer;
  background-color: rgb(0,221,255);
  color: rgb(43,42,51);
}

.promo-cta .primary:focus-visible {
  outline-color: rgb(0,221,255);
}

.promo-cta .primary:hover {
  background-color: rgb(128,235,255) !important;
  color: rgb(43,42,51) !important;
}

.promo-cta .primary:hover:active {
  background-color: rgb(170,242,255) !important;
  color: rgb(43,42,51) !important;
}

@media (prefers-contrast) {
  .promo-cta .primary {
    background-color: ButtonText;
    color: ButtonFace;
    border-color: ButtonFace;
  }

  .promo-cta .primary:focus-visible {
    outline-color: -moz-DialogText;
  }

  .promo-cta .primary:hover {
    background-color: SelectedItem !important;
    color: SelectedItemText !important;
    border-color: SelectedItemText;
  }

  .promo-cta .primary:hover:active {
    background-color: SelectedItemText !important;
    color: SelectedItem !important;
    border-color: SelectedItem;
  }
}

.promo.bottom .promo-cta {
  display: unset;
}

.promo.bottom h1 {
  margin-bottom: 1em;
}

.promo a {
  color: inherit;
  text-decoration: underline;
  flex: 1;
}

.promo.top a,
.promo.below-search a {
  flex: unset;
}

.promo.top,
.promo.below-search {
  text-align: start;
}

.promo.below-search {
  padding: 22px;
  margin-top: 25px;

  #promo-header {
    margin-block: 0 1em;
  }

  #private-browsing-promo-text {
    margin-block: 0;
  }

  #private-browsing-promo-link:not(.text-link) {
    margin-block: 1.5em 0;
  }
}

.promo.bottom {
  margin-top: 30px;
}

.promo.bottom .promo-image-small {
  margin-top: 40px;
}

.promo.bottom .promo-image-small img {
  height: 40px;
}

.promo.top > .promo-content {
  padding: 10px 6px;
}

.promo-link {
  margin: 0;
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.privatebrowsing.felt-privacy-v1") {
  .promo.below-search.promo-visible {
    margin-block: 0 25px;

    @media not (prefers-contrast) {
      background-color: #321c64;
    }
  }

  @media not (prefers-contrast) {
    html.private {
      background: linear-gradient(45deg, #722291 0%, #45278D 50%, #393473 100%);
    }
  }
}
