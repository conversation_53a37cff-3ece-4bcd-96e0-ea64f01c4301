/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

.translations-panel-view {
  font: menu;
  width: 31em;
}

:where(.translations-panel) :is(description, label, menulist) {
  margin: 0;
}

.translations-panel-header {
  text-align: initial;
  padding: var(--arrowpanel-padding);
  padding-bottom: 0;
}

h1.translations-panel-header-wrapper {
  flex-grow: 0;
}

.translations-panel-beta {
  flex-shrink: 0;
  flex-grow: 1;
}

.translations-panel-beta-icon {
  list-style-image: url(chrome://browser/skin/translations/beta.svg);
  width: 34px;
  height: 20px;
  -moz-context-properties: fill, stroke;
  fill: currentColor;
  stroke: currentColor;
  margin: 2px 8px;
}

@media (prefers-contrast: no-preference) and (prefers-color-scheme: light) {
  .translations-panel-beta-icon {
    opacity: 0.8;
  }
}

#full-page-translations-panel-intro {
  line-height: 1.6;
  margin-block-end: var(--arrowpanel-padding);
}

.translations-panel-settings-gear-icon > image {
  /* Override the panel-info-button with a gear icon. */
  list-style-image: url(chrome://global/skin/icons/settings.svg);
}

.translations-panel-content {
  padding: var(--arrowpanel-padding);
  padding-block-end: 8px;
}

#full-page-translations-panel-lang-selection > label {
  margin-block: var(--arrowpanel-padding) 6px;
}

/* The default styling is to dim the default, but here override it so that it still uses
   the primary color. */
.translations-panel-button-group > button[default][disabled="true"] {
  color: var(--button-text-color-primary);
  background-color: var(--color-accent-primary);
}

#full-page-translations-panel-translate-hint-action {
  appearance: none;
  background-color: var(--button-background-color);
  border-radius: 4px;
  color: var(--button-text-color);
  padding: 8px 16px;
  font-size: 0.9em;

  &:hover {
    background-color: var(--button-background-color-hover);
  }

  &:hover:active {
    background-color: var(--button-background-color-active);
  }

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-offset);
  }
}

#full-page-translations-panel-error-message-hint {
  margin-inline-start: 21px;
  margin-block: 8px;
}

#full-page-translations-panel-error-message {
  font-weight: 600;
}

.translations-panel-error-icon {
  -moz-context-properties: fill;
  fill: currentColor;
  list-style-image: url(chrome://global/skin/icons/error.svg);
  margin-inline-end: 5px;
  width: 16px;
}

.translations-panel-error-header {
  align-items: start;
}

#full-page-translations-panel-error {
  border: 1px solid currentColor;
  border-radius: 4px;
  padding: 12px;
}

.select-translations-panel-content {
  margin-block: var(--space-xsmall);
  padding-inline: var(--arrowpanel-padding);
}

#select-translations-panel-header-row {
  padding-block: var(--space-small) 0;
}

.select-translations-panel-label {
  margin-block-end: var(--space-small);
}

#select-translations-panel-lang-selection {
  column-gap: var(--space-medium);
}

#select-translations-panel-text-area {
  height: 8em;
  min-height: 3.5em;
  resize: none;
  margin-block-start: var(--space-large);
  padding-block: var(--space-medium);
  padding-inline: var(--space-medium);
  color: inherit;
  background-color: transparent;

  &:disabled {
    color: var(--text-color-disabled);
    border-color: var(--border-color-deemphasized);
  }

  &.translating {
    -moz-context-properties: fill;
    fill: currentColor;
    background-image: url("chrome://global/skin/icons/loading.svg");
    background-repeat: no-repeat;
    background-position: var(--space-medium) var(--space-medium);
    background-size: var(--size-item-small);
    padding-inline-start: calc(var(--space-medium) + var(--size-item-small) + var(--space-small));

    &:-moz-locale-dir(rtl) {
      background-position-x: right var(--space-medium);
    }
  }
}

.select-translations-panel-message-bar {
  margin-block-start: var(--space-xsmall);
}

#select-translations-panel-unsupported-language-message-bar {
  margin-block-end: var(--space-medium);
}

#select-translations-panel-translation-failure-message-bar {
  margin-block-start: var(--space-large);
}

#select-translations-panel-footer {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-block: var(--space-medium);
  padding-block: var(--space-small);
  column-gap: var(--space-small);
  row-gap: var(--space-large);
}

.select-translations-panel-copy-button {
  height: var(--size-item-large);
  min-height: var(--size-item-large);
  margin: 0;
  justify-content: flex-start;
  list-style-image: url(chrome://global/skin/icons/edit-copy.svg);
  background-color: transparent;
  -moz-context-properties: fill;
  fill: currentColor;

  > .button-box {
    gap: var(--space-xsmall);
  }

  &.copied {
    list-style-image: url(chrome://global/skin/icons/check.svg);
  }
}

#select-translations-panel-footer-button-group {
  flex: 1;
  justify-content: flex-end;
  column-gap: var(--space-xsmall);

  > .footer-button {
    height: var(--size-item-large);
    min-height: var(--size-item-large);
  }
}
