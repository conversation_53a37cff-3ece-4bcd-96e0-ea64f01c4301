/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

/* Combined context-menu items */

#context-navigation > .menuitem-iconic {
  flex: 1;
  justify-content: center;
  align-items: center;

  > .menu-iconic-text,
  > .menu-accel-container {
    display: none;
  }

  > .menu-iconic-left {
    appearance: none;
    margin: 0;
    padding: 0;

    > .menu-iconic-icon {
      width: auto;
      height: auto;
      padding: 7px;
      margin: 0;
      -moz-context-properties: fill;
      fill: currentColor;
    }
  }
}

#context-back {
  list-style-image: url("chrome://browser/skin/back.svg");
}

#context-forward {
  list-style-image: url("chrome://browser/skin/forward.svg");
}

#context-reload {
  list-style-image: url("chrome://global/skin/icons/reload.svg");
}

#context-stop {
  list-style-image: url("chrome://global/skin/icons/close.svg");
}

#context-bookmarkpage {
  list-style-image: url("chrome://browser/skin/bookmark-hollow.svg");
}

#context-bookmarkpage[starred=true] {
  list-style-image: url("chrome://browser/skin/bookmark.svg");
}

#context-back:-moz-locale-dir(rtl),
#context-forward:-moz-locale-dir(rtl),
#context-reload:-moz-locale-dir(rtl) {
  transform: scaleX(-1);
}

#context-media-eme-learnmore {
  list-style-image: url("chrome://browser/skin/drm-icon.svg");
  -moz-context-properties: fill;
  fill: currentColor;
}
