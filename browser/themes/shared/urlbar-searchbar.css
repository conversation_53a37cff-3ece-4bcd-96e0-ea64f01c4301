/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

:root {
  --urlbar-container-padding: 1px;
  --urlbar-margin-inline: 5px;
  --urlbar-padding-block: 4px;
}

#urlbar[actiontype="switchtab"][action-override] > .urlbar-input-container > #urlbar-label-box,
#urlbar:not([actiontype="switchtab"], [actiontype="extension"], [searchmode]) > .urlbar-input-container > #urlbar-label-box,
#urlbar:not([actiontype="switchtab"]) > .urlbar-input-container > #urlbar-label-box > #urlbar-label-switchtab,
#urlbar:not([actiontype="extension"]) > .urlbar-input-container > #urlbar-label-box > #urlbar-label-extension,
#urlbar[searchmode][breakout-extend] > .urlbar-input-container > #urlbar-label-box,
#urlbar:not([searchmode]) > .urlbar-input-container > #urlbar-label-box > #urlbar-label-search-mode,
#urlbar[breakout-extend] > .urlbar-input-container > #urlbar-label-box > #urlbar-label-search-mode,
.urlbar-input-container[pageproxystate=invalid] > #page-action-buttons > .urlbar-page-action,
#identity-box.chromeUI ~ #page-action-buttons > .urlbar-page-action:not(#star-button-box),
#urlbar[usertyping] > .urlbar-input-container > #page-action-buttons > #urlbar-zoom-button,
#urlbar:not([usertyping]) > .urlbar-input-container > .urlbar-go-button,
#urlbar:not([focused]) > .urlbar-input-container > .urlbar-go-button,
#urlbar-revert-button-container {
  display: none;
}

/* When rich suggestions are enabled the urlbar identity icon is given extra padding to
 * align the results and urlbar text */
/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.urlbar.richSuggestions.featureGate") {
  #identity-box[pageproxystate="invalid"] > .identity-box-button {
    padding-inline: calc(6px + var(--urlbar-icon-padding));
  }
}

#urlbar-container,
#search-container {
  padding-block: var(--urlbar-padding-block);
  margin-inline: var(--urlbar-margin-inline);

  :root[uidensity=touch] & {
    padding-block: 5px;
  }
}

#urlbar {
  display: flex;
  flex-direction: row;
  flex: 1;
  box-sizing: border-box;

  /* Reset UA popover rules */
  width: initial;
  height: initial;
  inset: auto;
  border: none;
  padding: initial;
  overflow: initial;
  color: initial;
  background-color: initial;
}

#urlbar,
#searchbar {
  /* Setting a min-width to let the location & search bars maintain a constant
   * width in case they haven't been resized manually. (bug 965772) */
  min-width: 1px;
  min-height: var(--urlbar-min-height);
  text-shadow: none;
  color: var(--toolbar-field-color);

  /**
   * System colors and widgets are set based on toolbar color. Since toolbar
   * fields can be styled differently from the toolbar, we need to use the
   * correct color scheme in toolbar fields.
   */
  :root[lwt-toolbar-field="light"] & {
    color-scheme: light;
  }
  :root[lwt-toolbar-field="dark"] & {
    color-scheme: dark;
  }
}

#urlbar[focused],
#searchbar:focus-within {
  :root[lwt-toolbar-field-focus="light"] & {
    color-scheme: light;
  }
  :root[lwt-toolbar-field-focus="dark"] & {
    color-scheme: dark;
  }
}

#urlbar-background,
#searchbar {
  background-color: var(--toolbar-field-background-color);
  background-clip: border-box;
  border: 1px solid var(--toolbar-field-border-color);
  border-radius: var(--toolbarbutton-border-radius);
}

.urlbar-input-container,
#searchbar {
  border-radius: var(--toolbarbutton-border-radius);
  overflow: clip;
}

.urlbar-input,
#urlbar-scheme,
.searchbar-textbox {
  flex: 1;
  background-color: transparent;
  color: inherit;
  border: none;
  margin: 0;
  padding: 0;
  outline: none;
}

.urlbar-input {
  mask-repeat: no-repeat;
  unicode-bidi: plaintext;
  text-align: match-parent;

  /* Align URLs to the right in RTL mode. */
  &:-moz-locale-dir(rtl) {
    text-align: right !important;
  }

  /* Make sure that the location bar's alignment changes according
     to the input box direction if the user switches the text direction using
     cmd_switchTextDirection (which applies a dir attribute to the <input>). */
  &[dir=ltr]:-moz-locale-dir(rtl) {
    text-align: left !important;
  }

  &[dir=rtl]:-moz-locale-dir(ltr) {
    text-align: right !important;
  }

  #urlbar:not([focused])[domaindir="ltr"]> .urlbar-input-container > .urlbar-input-box > & {
    direction: ltr;
    unicode-bidi: embed;
  }

  /* The following rules apply overflow masks to the unfocused urlbar
     This mask may be overriden when a Contextual Feature Recommendation is shown. */
  #urlbar:not([focused])[textoverflow="both"] > .urlbar-input-container > .urlbar-input-box > & {
    mask-image: linear-gradient(to right, transparent, black 3ch, black calc(100% - 3ch), transparent);
  }
  #urlbar:not([focused])[textoverflow="right"] > .urlbar-input-container > .urlbar-input-box > & {
    mask-image: linear-gradient(to left, transparent, black 3ch);
  }
  #urlbar:not([focused])[textoverflow="left"] > .urlbar-input-container > .urlbar-input-box > & {
    mask-image: linear-gradient(to right, transparent, black 3ch);
  }
  #urlbar:not([focused])[textoverflow="left"][domaindir="rtl"] > .urlbar-input-container > .urlbar-input-box > & {
    mask-image: linear-gradient(to right, transparent var(--urlbar-scheme-size), black calc(var(--urlbar-scheme-size) + 3ch));
  }
}

#urlbar-scheme {
  position: absolute;
  height: 100%;
  visibility: hidden;
  direction: ltr;
  pointer-events: none;

  /* The protocol is visible if there is an RTL domain and we overflow to the left.
     Uses the required-valid trick to check if it contains a value */
  #urlbar:not([focused])[textoverflow="left"][domaindir="rtl"] > .urlbar-input-container > .urlbar-input-box > &:valid {
    visibility: visible;
  }
}

#urlbar[focused]:not([suppress-focus-border]) > #urlbar-background,
#searchbar:focus-within {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-inset);

  /* We used --focus-outline above to inherit its width and style properties,
     but we still want to use the theme's border-color.
     --toolbar-field-focus-border-color is set equal to --focus-outline-color
     on :root, but LWT themes can override this value. */
  outline-color: var(--toolbar-field-focus-border-color);
  border-color: transparent;
}

#urlbar[focused] > #urlbar-background,
#searchbar:focus-within {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.23);
}

#urlbar[open] > #urlbar-background {
  border-color: var(--arrowpanel-border-color);
  box-shadow: 0 2px 14px rgba(0, 0, 0, 0.13);
}

#urlbar:is([focused], [open]),
#searchbar:focus-within {
  color: var(--toolbar-field-focus-color);
}

#urlbar:is([focused], [open]) > #urlbar-background,
#searchbar:focus-within {
  background-color: var(--toolbar-field-focus-background-color);
}

.urlbar-input::placeholder,
.searchbar-textbox::placeholder {
  opacity: 0.69;
}

#TabsToolbar #searchbar:not(:focus-within) {
  /* The tabs toolbar is usually a different color than the other toolbars, and
     we can't predict it, to avoid a transparent field we enforce a border. */
  border-color: color-mix(in srgb, currentColor 20%, transparent);
}

:root[lwtheme] {
  .urlbar-input::selection,
  .searchbar-textbox::selection {
    background-color: var(--lwt-toolbar-field-highlight, Highlight);
    color: var(--lwt-toolbar-field-highlight-text, HighlightText);
  }

  .urlbar-input:not(:focus)::selection,
  .searchbar-textbox:not(:focus-within)::selection {
    background-color: var(--lwt-toolbar-field-highlight, text-select-disabled-background);
  }
}

#urlbar:not([focused]) {
  caret-color: transparent;
}

/**
 * The urlbar background is a separate element so we can animate it
 * independently from the content. It's positioned absolutely and stretched to
 * the bounds of the urlbar.
 */

#urlbar,
.urlbar-input-container,
.urlbarView {
  position: relative;
}

#urlbar-background {
  display: block;
  position: absolute;
  inset: 0;
}

.urlbar-input-container {
  /* Match urlbar-background's border. */
  border: 1px solid transparent;
  padding: var(--urlbar-container-padding);
  min-width: 0;
}

#urlbar-container[breakout] {
  min-height: var(--urlbar-container-height);
}

#urlbar[breakout] {
  display: block;
  /* This is technically not needed, because popover takes care of it, but
   * helps for clarity. */
  position: absolute;
  height: var(--urlbar-height);
  width: var(--urlbar-width);

  > .urlbar-input-container {
    width: 100%;
    height: 100%;
  }
}

#urlbar:not([open]) > .urlbarView,
#urlbar:not([breakout]) > .urlbarView {
  display: none;
}

#urlbar[breakout][breakout-extend],
#urlbar[breakout][breakout-extend-disabled][open] {
  height: auto;
}

#urlbar[breakout][breakout-extend] {
  margin-left: calc(-1 * var(--urlbar-margin-inline));
  width: calc(var(--urlbar-width) + 2 * var(--urlbar-margin-inline));

  > .urlbar-input-container {
    height: var(--urlbar-container-height);
    padding-block: calc((var(--urlbar-container-height) - var(--urlbar-height)) / 2 + var(--urlbar-container-padding));
    padding-inline: calc(var(--urlbar-margin-inline) + var(--urlbar-container-padding));
  }
}

@keyframes urlbar-grow {
  0% {
    transform: scaleX(.99) scaleY(.98);
  }
  100% {
    transform: scale(1.0);
  }
}

#urlbar[breakout][breakout-extend] > #urlbar-background {
  animation-name: urlbar-grow;
  animation-duration: 0s;
  animation-timing-function: var(--animation-easing-function);
}

@media (prefers-reduced-motion: no-preference) {
  #urlbar[breakout][breakout-extend][breakout-extend-animate] > #urlbar-background {
    animation-duration: 150ms;
  }
}

:root[chromehidden~="toolbar"] #urlbar-container {
  /* Remove excess space between the address bar and the menu button in popups. */
  padding-inline-end: 0;
}


.urlbar-input-box {
  /* Show the url scheme in a static box when overflowing to the left */
  position: relative;
  direction: ltr;

  :root[customizing] & {
    visibility: hidden;
  }
}

#urlbar-container {
  align-items: center;
  /* We leave 310px plus whatever space the download and unified extensions
   * buttons will need when they *both* appear. Normally, for each button, this
   * should be 16px for the icon, plus 2 * 2px padding plus the
   * toolbarbutton-inner-padding. We're adding 4px to ensure things like rounding
   * on hidpi don't accidentally result in the buttons going into overflow.
   */
  width: calc(310px + 2 * (24px + 2 * var(--toolbarbutton-inner-padding)));

  /* When the download button OR the unified extensions button is shown, we leave
   * 310px plus the space needed for a single button as described above. */
  #nav-bar:is([downloadsbuttonshown], [unifiedextensionsbuttonshown]) & {
    width: calc(310px + 24px + 2 * var(--toolbarbutton-inner-padding));
  }

  /* When both the download and unified extensions buttons are visible, we use
   * the base min-width value. */
  #nav-bar[downloadsbuttonshown][unifiedextensionsbuttonshown] & {
    width: 310px;
  }

  /* Customize mode is difficult to use at moderate window width if the Urlbar
   * remains 310px wide. */
  :root[customizing] & {
    width: 280px;
  }
}

#urlbar-container,
#wrapper-urlbar-container {
  flex: 400 0 auto;
}

#nav-bar:not([keyNav=true]) #identity-box,
#nav-bar:not([keyNav=true]) #tracking-protection-icon-container {
  -moz-user-focus: normal;
}

#identity-icon-box {
  max-width: calc(30px + 13em);
}

@media (max-width: 770px) {
  #urlbar-container {
    width: calc(240px + 2 * (24px + 2 * var(--toolbarbutton-inner-padding)));
  }
  #nav-bar:is([downloadsbuttonshown], [unifiedextensionsbuttonshown]) #urlbar-container {
    width: calc(240px + 24px + 2 * var(--toolbarbutton-inner-padding));
  }
  #nav-bar[downloadsbuttonshown][unifiedextensionsbuttonshown] #urlbar-container {
    width: 240px;
  }
  :root[customizing] #urlbar-container {
    width: 245px;
  }
  #identity-icon-box {
    max-width: 80px;
  }
  /* This label expresses the non secure status. However, as the padlock icon is
     enough to tell the status to user, hide the label when window gets small.
     Except this usage, #identity-icon-label is used to show additional
     information that could not tell by only icons. */
  #identity-box[pageproxystate="valid"].notSecureText #identity-icon-label {
    display: none;
  }
  /* Contenxtual identity labels are user-customizable and can be very long,
     so we only show the colored icon when the window gets small. */
  #userContext-label {
    display: none;
  }
}

/* The page actions menu is hidden by default, it is only shown in small
   windows as the overflow target of multiple page action buttons */
#pageActionButton {
  visibility: collapse;
}

/* 680px is just below half of popular 1366px wide screens, so when putting two
   browser windows next to each other on such a screen, they'll be above this
   threshold. */
@media (max-width: 680px) {
  /* Page action buttons are duplicated in the page action menu so we can
      safely hide them in small windows. */
  #pageActionSeparator,
  #pageActionButton[multiple-children] ~ .urlbar-page-action {
    display: none;
  }
  #pageActionButton[multiple-children] {
    visibility: visible;
  }
}

@media (max-width: 550px) {
  #urlbar-container {
    width: calc(176px + 2 * (24px + 2 * var(--toolbarbutton-inner-padding)));
  }
  #nav-bar[downloadsbuttonshown] #urlbar-container,
  #nav-bar[unifiedextensionsbuttonshown] #urlbar-container {
    width: calc(176px + 24px + 2 * var(--toolbarbutton-inner-padding));
  }
  #nav-bar[downloadsbuttonshown][unifiedextensionsbuttonshown] #urlbar-container {
    width: 176px;
  }
  #identity-icon-box {
    max-width: 70px;
  }
  #urlbar-zoom-button {
    display: none;
  }
}

/* Allow shrinking the urlbar on popup windows */
:root[chromehidden~="toolbar"] :is(#urlbar-container, #wrapper-urlbar-container) {
  flex-shrink: 1;
  min-width: 0;
}

#urlbar-search-splitter {
  /* The splitter width should equal the location and search bars' combined
     neighboring margin and border width. */
  min-width: 12px;
  margin: 0 -6px;
  position: relative;
  border: none;
  background: transparent;
  appearance: none;
}

/* Urlbar search mode indicator */
#urlbar-search-mode-indicator {
  display: none;
  background-color: var(--urlbar-box-bgcolor);
  color: var(--urlbar-box-text-color);
  margin-inline-end: 8px;
  align-items: center;
  border-radius: var(--urlbar-icon-border-radius);
  padding-inline: 8px 6px;

  #urlbar[focused] > .urlbar-input-container > & {
    background-color: var(--urlbar-box-focus-bgcolor);
  }

  #urlbar[searchmode] > .urlbar-input-container > & {
    display: inline-flex;
  }
}

#urlbar[searchmode] > .urlbar-input-container > #urlbar-label-box {
  display: none;
}

#urlbar-search-mode-indicator-close,
#searchmode-switcher-close {
  background: url(chrome://global/skin/icons/close.svg) no-repeat center;
  background-size: round(62.5%, 2px);
  width: round(max(16px, 1em), 2px);
  height: round(max(16px, 1em), 2px);
  -moz-context-properties: fill, fill-opacity;
  fill-opacity: 0.6;
  fill: currentColor;
  border-radius: var(--urlbar-icon-border-radius);

  @media (prefers-contrast) {
    fill-opacity: 1.0;
  }

  &:hover {
    background-color: var(--urlbar-box-hover-bgcolor);
    color: var(--urlbar-box-hover-text-color);

    &:active {
      background-color: var(--urlbar-box-active-bgcolor);
      color: var(--urlbar-box-hover-text-color);
    }
  }
}

#urlbar-search-mode-indicator-title {
  padding-inline: 0 3px;
}

/* Page action panel */
.pageAction-panel-button > .toolbarbutton-icon {
  list-style-image: var(--pageAction-image, inherit);
  width: 16px;
  height: 16px;
}

#pageAction-panel-bookmark,
#star-button {
  list-style-image: url("chrome://browser/skin/bookmark-hollow.svg");

  &[starred] {
    list-style-image: url("chrome://browser/skin/bookmark.svg");
  }
}

#star-button[starred] {
  fill-opacity: 1;
  fill: var(--toolbarbutton-icon-fill-attention);
}

/* URL bar and page action buttons */

/*
 * The background can be very dark and if the add-on uses a black-ish svg it
 * will be barely visible. For now we try to make them more visible through
 * some filtering tricks.
 *
 * TODO(emilio): Evaluate removing this. SVGs can use prefers-color-scheme to
 * determine the right color-scheme to use, see bug 1779457.
 */
.urlbar-addon-page-action[style*=".svg"] > .urlbar-icon {
  :root[lwt-toolbar-field="dark"] #urlbar:not([focused]) &,
  :root[lwt-toolbar-field-focus="dark"] #urlbar[focused] & {
    filter: grayscale(100%) brightness(20%) invert();
  }

  /* As above, but for the default theme in dark mode, which suffers from the same issue */
  @media (prefers-color-scheme: dark) {
    :root:not([lwtheme]) & {
      filter: grayscale(100%) brightness(20%) invert();
    }
  }
}

#userContext-icons,
#urlbar-zoom-button {
  margin-inline: 6px;
}

.urlbar-icon {
  width: 16px;
  height: 16px;
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
}

.urlbar-page-action,
.urlbar-revert-button,
.urlbar-go-button,
.search-go-button {
  width: calc(var(--urlbar-min-height) - 2px /* border */ - 2 * var(--urlbar-container-padding));
  height: calc(var(--urlbar-min-height) - 2px /* border */ - 2 * var(--urlbar-container-padding));
  border-radius: var(--urlbar-icon-border-radius);
  padding: var(--urlbar-icon-padding);
  color: inherit;
  outline: var(--toolbarbutton-outline);
  outline-offset: var(--toolbarbutton-outline-offset);

  &:not([disabled]):hover {
    background-color: var(--urlbar-box-hover-bgcolor);
    color: var(--urlbar-box-hover-text-color);
    outline-color: var(--toolbarbutton-hover-outline-color);
  }

  &:not([disabled])[open],
  &:not([disabled]):hover:active {
    background-color: var(--urlbar-box-active-bgcolor);
    color: var(--urlbar-box-hover-text-color);
    outline-color: var(--toolbarbutton-active-outline-color);
  }
}

.urlbar-page-action {
  list-style-image: var(--pageAction-image, inherit);

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
  }
}

.urlbar-go-button,
.search-go-button {
  list-style-image: url("chrome://browser/skin/forward.svg");

  &:-moz-locale-dir(rtl) {
    transform: scaleX(-1);
  }
}

#pageActionButton,
.share-more-button {
  list-style-image: url("chrome://global/skin/icons/more.svg");
}

/*
 * Display visual cue that browser is under remote control.
 * This is to help users visually distinguish a user agent session that
 * is under remote control from those used for normal browsing sessions.
 *
 * Attribute is controlled by browser.js:/gRemoteControl.
 */
:root[remotecontrol] {
  #remote-control-box {
    visibility: visible;
    padding-inline: var(--urlbar-icon-padding);
  }

  #remote-control-icon {
    list-style-image: url(chrome://browser/content/static-robot.png);
    width: 16px;
    height: 16px;
  }

  #urlbar-background {
    background-image: repeating-linear-gradient(
      -45deg,
      rgba(255, 60, 60, 0.25) 0 25px,
      rgba(175, 0, 0, 0.25) 25px 50px
    );

    background-attachment: fixed;
    /* Override the usual breakout animation so the gradient doesn't shift around
       when the panel opens. */
    animation: none !important;
  }
}

/**
 * Contextual Feature Recommendation
 *
 * Animate the recommendation icon to expand outwards and display a text label.
 * Fake the effect of a smoothly expanding width without animating any widths
 * by (continuously) animating only `mask-position-x` and `transform`.
 *
 * In a few places, transition a property using the timing-function `step-start`
 * while collapsed and `step-end` while expanded in order to (discretely) modify
 * the value while expanded and while transitioning in either direction.
 *
 * This UI is part of an experiment launching in LTR locales only. Fixing the
 * RTL issues is tracked by Bug 1485725.
 */

:root {
  --cfr-animation-duration: 0.35s;
  --cfr-button-addons-icon: url(chrome://activity-stream/content/data/content/assets/glyph-webextension-16.svg);
  --cfr-button-features-icon: url(chrome://browser/content/asrouter/assets/glyph-cfr-feature-16.svg);
  --cfr-button-highlights-icon: url(chrome://global/skin/icons/highlights.svg);
  --cfr-active-color: #0060df;
  --cfr-active-text-color: white;
}

#contextual-feature-recommendation {
  width: 28px;
  margin-inline-start: 0;
  transition: margin-inline-start step-end var(--cfr-animation-duration);

  #urlbar[cfr-recommendation-state="expanded"] & {
    margin-inline-start: calc(var(--cfr-label-width) * -1);
    transition: margin-inline-start step-start var(--cfr-animation-duration);
  }

  &[data-cfr-icon="recommendations-icon"] {
    width: auto;

    #cfr-button {
      list-style-image: var(--cfr-button-features-icon);
      height: auto;
      align-items: center;
    }
  }

  &[data-cfr-icon="highlights-icon"] {
    width: auto;

    #cfr-button {
      list-style-image: var(--cfr-button-highlights-icon);
      height: auto;
      align-items: center;
    }
  }

  &[data-cfr-icon="webextensions-icon"] #cfr-button {
    list-style-image: var(--cfr-button-addons-icon);
  }
}

#cfr-button {
  transition-property: fill, fill-opacity, transform;
  transition-timing-function: ease-in-out;
  transition-duration: var(--cfr-animation-duration);

  #urlbar[cfr-recommendation-state="expanded"] & {
    background-color: transparent;
    transform: translateX(calc(var(--cfr-label-width) * -1));

    &:-moz-locale-dir(rtl) {
      transform: translateX(calc(var(--cfr-label-width)));
    }

    &,
    .urlbar-icon {
      fill: var(--cfr-active-text-color);
      fill-opacity: 1;
    }
  }

  #urlbar[cfr-recommendation-state="collapsed"] & {
    animation: cfr-button-fade-through-active-color calc(3 * var(--cfr-animation-duration));
  }
}

@keyframes cfr-button-fade-through-active-color {
  33% {
    fill-opacity: 1;
    fill: var(--cfr-active-color);
  }
  67% {
    fill-opacity: 1;
    fill: var(--cfr-active-color);
  }
}

#cfr-label-container {
  width: 0;
  overflow: hidden;
  border-radius: var(--urlbar-icon-border-radius);
  padding-inline-start: 28px;
  mask-image: linear-gradient(to right, transparent 0, black 0);
  mask-position-x: var(--cfr-label-width);
  mask-repeat: no-repeat;
  transition-property: background-color, mask-position-x, width, margin-inline-end;
  transition-timing-function: ease-in-out, ease-in-out, step-end, step-end;
  transition-duration: var(--cfr-animation-duration);
  align-items: center;
  margin-block: calc((var(--urlbar-min-height) - var(--urlbar-container-padding) - 2px /* border */ - 24px) / 2);

  &:-moz-locale-dir(rtl) {
    mask-position-x: calc(var(--cfr-label-width) * -1);
  }

  #urlbar[cfr-recommendation-state="expanded"] & {
    width: calc(var(--cfr-label-width) + 28px);
    background-color: var(--cfr-active-color);
    margin-inline-end: -28px;
    mask-position-x: 0;
    transition-timing-function: ease-in-out, ease-in-out, step-start, step-start;
  }
}

#cfr-label {
  margin: 0;
  padding: 3px 5px;
  padding-inline-start: 0;
  color: white;
  mask-image: linear-gradient(to right, transparent 0, black 0);
  mask-position-x: var(--cfr-label-width);
  mask-repeat: no-repeat;
  transition: mask-position-x ease-in-out var(--cfr-animation-duration);

  &:-moz-locale-dir(rtl) {
    mask-position-x: calc(var(--cfr-label-width) * -1);
  }

  #urlbar[cfr-recommendation-state="expanded"] & {
    mask-position-x: 0;
  }
}

/* Shift the url-bar text fading to stop the recommendation overlapping */
#urlbar[cfr-recommendation-state] .urlbar-input {
  /* A mask-image is usually only defined for the url bar when text overflows.
     We need to re-define the mask image here so that it shows up correctly when
     we transition to or from an `expanded` state (in which the right end of the
     url bar may be obscured without overflow). */
  mask-image: linear-gradient(to left, transparent, black 3ch);
  transition: mask-position-x ease-in-out var(--cfr-animation-duration);

  &:-moz-locale-dir(rtl) {
    mask-image: linear-gradient(to right, transparent, black 3ch);
  }
}
#urlbar[cfr-recommendation-state="expanded"] .urlbar-input {
  mask-position-x: calc(var(--cfr-label-width) * -1);

  &:-moz-locale-dir(rtl) {
    mask-position-x: calc(var(--cfr-label-width));
  }
}

/* Persisted Search Terms rules specific when the Unified Search Button is present */
/* stylelint-disable-next-line media-query-no-invalid */
#urlbar[persistsearchterms] {
  #urlbar-revert-button-container {
    display: inherit;
  }
  .urlbar-revert-button {
    list-style-image: url(chrome://global/skin/icons/defaultFavicon.svg);
    fill: var(--color-accent-primary);
  }
  .urlbar-revert-button:focus-visible {
    outline: var(--focus-outline);
  }
  .urlbar-go-button {
    display: none;
  }
}

/* Reader mode icon */

#reader-mode-button > .urlbar-icon {
  list-style-image: url(chrome://browser/skin/reader-mode.svg);
}

#reader-mode-button[readeractive] > .urlbar-icon {
  fill: var(--toolbarbutton-icon-fill-attention);
  fill-opacity: 1;
}

/* Picture-in-Picture icon */
#picture-in-picture-button > .urlbar-icon {
  list-style-image: url("chrome://global/skin/media/picture-in-picture-open.svg");
}

#picture-in-picture-button[pipactive] > .urlbar-icon {
  list-style-image: url("chrome://global/skin/media/picture-in-picture-closed.svg");
}

#picture-in-picture-button:-moz-locale-dir(rtl) > .urlbar-icon {
  transform: scaleX(-1);
}

/* Translations button */

#translations-button-icon {
  list-style-image: url(chrome://browser/skin/translations.svg);
  /* The translation icon's right letter is 1px too long, so account for that here.
     Don't use margin-inline-end as this is adjusting the art of the icon. */
  margin-right: -1px;

  #translations-button[translationsactive] > & {
    fill: var(--toolbarbutton-icon-fill-attention);
    fill-opacity: 1;
  }
}

#translations-button-circle-arrows {
  height: 16px;
  width: 16px;
  list-style-image: url("chrome://global/skin/icons/loading.svg");
  fill: var(--toolbarbutton-icon-fill-attention);
  fill-opacity: 1;
  margin-inline: 1px;
}

#translations-button-locale {
  background-color: var(--toolbarbutton-icon-fill-attention);
  color: var(--toolbarbutton-icon-fill-attention-text);
  border-radius: 4px;
  font-size: 0.8em;
  height: 20px;
  min-width: 20px;
  text-align: center;
  padding-top: 3px;
  padding-inline: 1px;
  box-sizing: border-box;
  margin-inline: 2px -2px;
  margin-block: -2px;
}

#translations-button[translationsactive="true"] {
  width: auto;
}

@media (prefers-contrast) and (-moz-platform: windows) {
  #translations-button-locale {
    font-weight: bold;
  }

  #translations-button[translationsactive]:is(:hover, [open]) > #translations-button-icon {
    fill: currentColor;
  }

  #translations-button:is(:hover, [open]) > #translations-button-locale {
    background-color: var(--toolbar-field-background-color);
    color: var(--toolbarbutton-icon-fill-attention);
  }

  #translations-button:is(:hover, [open]) > #translations-button-circle-arrows {
    fill: currentColor;
  }
}

/* Zoom button */

#urlbar-zoom-button {
  appearance: none;
  font-size: .8em;
  padding: 3px 7px;
  border-radius: var(--urlbar-icon-border-radius);
  background-color: var(--urlbar-box-bgcolor);
  color: var(--urlbar-box-text-color);
  margin-block: calc((var(--urlbar-min-height) - 20px) / 2 - 1px /* border */ - var(--urlbar-container-padding));
  overflow: hidden;

  :where(#urlbar[focused]) & {
    background-color: var(--urlbar-box-focus-bgcolor);
  }

  &:hover {
    background-color: var(--urlbar-box-hover-bgcolor);
    color: var(--urlbar-box-hover-text-color);

    &:active {
      background-color: var(--urlbar-box-active-bgcolor);
      color: var(--urlbar-box-hover-text-color);
    }
  }

  &[animate="true"] {
    animation-name: urlbar-zoom-reset-pulse;
    animation-duration: 250ms;
  }

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
  }

  > .toolbarbutton-text {
    display: flex;
  }

  > .toolbarbutton-icon {
    display: none;
  }
}

@keyframes urlbar-zoom-reset-pulse {
  0% {
    transform: scale(0);
  }
  75% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1.0);
  }
}

/* Shopping Sidebar icon */

#shopping-sidebar-button > .urlbar-icon {
  list-style-image: url(chrome://browser/content/shopping/assets/shopping.svg);
  pointer-events: none;
}

#shopping-sidebar-button[shoppingsidebaropen="true"] > .urlbar-icon {
  fill: var(--toolbarbutton-icon-fill-attention);
  fill-opacity: 1;
}

@media (prefers-contrast) {
  #shopping-sidebar-button[shoppingsidebaropen="true"]:hover > .urlbar-icon {
    fill: currentColor;
  }
}

/* Search bar */

#search-container,
#wrapper-search-container {
  flex: 175 175 auto;
}

#search-container {
  min-width: 125px;
  width: 125px;

  &[width] {
    /* Take something closer to the explicit width when requested. Also important
     * to use flex-basis auto so that the width is actually honored. */
    flex: 1 auto;
  }

  .widget-overflow-list > & {
    margin: var(--arrowpanel-menuitem-margin);
    width: auto;

    :root[lwtheme] & > #searchbar {
      /* Theme authors usually only consider contrast against the toolbar when
         picking a border color for the search bar. Since the search bar can be
         dragged into the overflow panel, we need to show a high-contrast border
         around the searchbar in case the searchbar and panel background are the
         same color. */
      border-color: var(--panel-separator-color);
    }
  }
}

.search-go-button {
  margin-inline: 1px;
}

.searchbar-search-button {
  align-items: center;
  fill: currentColor;
}

.searchbar-search-icon {
  list-style-image: url(chrome://global/skin/icons/search-glass.svg);
  -moz-context-properties: fill, fill-opacity;
  fill-opacity: var(--urlbar-icon-fill-opacity);
  pointer-events: none;
  margin-inline: 8px 6px;
  width: 16px;
}

.searchbar-search-icon-overlay {
  pointer-events: none;

  .searchbar-search-button[addengines=true] > & {
    list-style-image: url(chrome://browser/skin/search-indicator-badge-add.svg);
    margin-inline: -15px 4px;
    margin-top: -10px;
    width: 11px;
    height: 11px;

    &:-moz-locale-dir(rtl) {
      margin-inline: -25px 14px;
    }
  }

  .searchbar-search-button:hover:not([addengines=true]) > & {
    list-style-image: url(chrome://global/skin/icons/arrow-down-12.svg);
    -moz-context-properties: fill;
    margin-inline: -8px 2px;
    width: 6px;
    height: 6px;

    &:-moz-locale-dir(rtl) {
      margin-inline: -26px 20px;
    }
  }
}

.searchbar-engine-one-off-add-engine:not([image]),
moz-input-box > menupopup .context-menu-add-engine:not([image]) {
  list-style-image: image-set(url("chrome://browser/skin/search-engine-placeholder.png"), url("chrome://browser/skin/<EMAIL>") 2x);
}

.searchbar-engine-one-off-add-engine > .button-box::after,
moz-input-box > menupopup .context-menu-add-engine > .menu-iconic-left::after {
  content: "";
  position: relative;
  display: flex;
  background: url(chrome://browser/skin/search-indicator-badge-add.svg) no-repeat center;
  height: 11px;
  width: 11px;
  margin-inline: -4px -7px;
  margin-top: -13px;
}

moz-input-box > menupopup .context-menu-add-engine > .menu-iconic-left::after {
  /* These differ from the urlbar and searchbar because the context menu
     may not have enough space */
  margin-inline: -7px -4px;
  margin-top: -8px;
}

/* Fade a little the icon so that the + badge is more evident and the button
   is less likely to be visually confused with a normal search shortcut */
.searchbar-engine-one-off-add-engine[image]:not(:hover) > .button-box > .button-icon {
  opacity: 0.7;
}
.searchbar-engine-one-off-add-engine[image]:hover > .button-box > .button-icon {
  opacity: 1;
}

#urlbar-searchmode-switcher {
  appearance: none;
  border-radius: var(--urlbar-icon-border-radius);
  margin: 0;
  margin-inline-end: var(--urlbar-searchmodeswitcher-margin-inline-end);
  padding-inline: var(--urlbar-searchmodeswitcher-inline-padding);

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
  }

  &[open] {
    background-color: var(--urlbar-box-focus-bgcolor);
  }

  /* No need space if page information icons such as lock and shield is shown */
  #urlbar[pageproxystate=valid] & {
    margin-inline-end: 0;
  }

  #urlbar[searchmode] & {
    background-color: var(--urlbar-box-bgcolor);
    margin-inline-end: 0;
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }

  /* The background rule should match to #searchmode-switcher-chicklet */
  #urlbar[focused] & {
    background-color: var(--urlbar-box-focus-bgcolor);
  }

  /* stylelint-disable media-query-no-invalid */
  @media (not (-moz-bool-pref: "browser.urlbar.searchModeSwitcher.featureGate")) and
         (not (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride")) {
    display: none;
  }
  /* stylelint-enable media-query-no-invalid */
}

#searchmode-switcher-icon {
  width: 16px;
  height: 16px;
  border-radius: var(--urlbar-icon-border-radius);
  -moz-context-properties: fill;
  fill: var(--text-color-deemphasized);
  list-style-image: url("chrome://global/skin/icons/search-glass.svg");
}

#searchmode-switcher-chicklet {
  background-color: var(--urlbar-box-bgcolor);
  border-start-end-radius: var(--toolbarbutton-border-radius);
  border-end-end-radius: var(--urlbar-icon-border-radius);
  align-items: center;
  height: 100%;
  margin-inline-end: var(--urlbar-searchmodeswitcher-margin-inline-end);
  display: none;

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "browser.urlbar.searchModeSwitcher.featureGate") or (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride") {
    #urlbar[searchmode] & {
      display: inline-flex;
    }
  }

  #urlbar[focused] & {
    background-color: var(--urlbar-box-focus-bgcolor);
  }
}

#searchmode-switcher-title {
  margin: 0;
  align-items: center;

  &:empty {
    display: none;
  }
}

#searchmode-switcher-close {
  appearance: none;
  pointer-events: all;
  margin-inline: var(--urlbar-searchmodeswitcher-inline-padding);

  #urlbar[searchmode] & {
    display: inline-flex;
  }
}

#searchmode-switcher-dropmarker {
  #urlbar[searchmode] & {
    display: none;
  }
  #urlbar-searchmode-switcher[open] & {
    transform: rotate(180deg);
  }
}

.searchmode-switcher-addEngine::before {
  content: "";
  position: relative;
  display: flex;
  background: url(chrome://browser/skin/search-indicator-badge-add.svg) no-repeat center;
  height: 11px;
  width: 11px;
  margin-inline: 10px -21px;
  margin-top: -13px;
}

#searchmode-switcher-popup-description {
  margin: var(--space-small);
  margin-bottom: var(--space-xsmall);
}

#searchmode-switcher-popup-search-settings-icon {
  list-style-image: url("chrome://global/skin/icons/settings.svg");
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.urlbar.searchModeSwitcher.featureGate") or (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride") {
  #urlbar[searchmode] > .urlbar-input-container > #urlbar-search-mode-indicator {
    display: none;
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media not (-moz-bool-pref: "browser.urlbar.unifiedSearchButton.always") {
    #urlbar-searchmode-switcher {
      background-color: var(--urlbar-box-bgcolor);
      #urlbar:not([unifiedsearchbutton-available]) > .urlbar-input-container > & {
        /* Display as off-screen to get focus */
        position: fixed;
        top: -999px;
      }
    }

    #searchmode-switcher-dropmarker {
      margin-inline-start: var(--urlbar-searchmodeswitcher-inline-padding);
    }

    #urlbar[pageproxystate=invalid] {
      #identity-box,
      #tracking-protection-icon-container {
        display: none;
      }
    }
  }
}
