/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace xul url("http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul");
@namespace html url("http://www.w3.org/1999/xhtml");

:root {
  appearance: none;
  background: transparent;
  border: 0;
  border-radius: 5px;
  --indicator-height: 32px;
  --indicator-background-color: rgb(249,249,250);
  --indicator-color: rgb(12,12,13);
  --indicator-border-color: hsla(0,0%,0%,.32);
  --indicator-icon-fill-opacity: 0.8;
  --indicator-item-separator: 1px solid hsla(210,4%,10%,.14);
  --indicator-stop-button-background-color: rgb(0,96,223);
  --indicator-stop-button-hover-background-color: rgb(0,62,170);
  --indicator-stop-button-color: rgb(255,255,255);
  --minimize-button-background-color: hsla(240,5%,5%,.1);
  --minimize-button-hover-background-color: hsla(240,5%,5%,.15);
  --minimize-button-active-background-color: hsla(240,5%,5%,.2);
  --control-icon-unchecked-hover-background-color: hsla(0,0%,70%,.4);
  --control-icon-unchecked-active-background-color: hsla(0,0%,70%,.6);
  --control-icon-checked-background-color: hsla(10,100%,50%,.16);
  --control-icon-checked-icon-fill: rgb(215,0,34);
  --control-icon-checked-hover-background-color: hsla(10,100%,50%,.24);
  --control-icon-checked-active-background-color: hsla(10,100%,50%,.32);
  max-height: var(--indicator-height);
}

@media (prefers-color-scheme: dark) {
  :root {
    --indicator-background-color: hsl(240, 1%, 20%);
    --indicator-color: rgb(249, 249, 250);
    --indicator-border-color: hsl(240, 1%, 40%);
    --indicator-item-separator: 1px solid rgb(249, 249, 250);
    --minimize-button-background-color: rgba(249,249,250,0.1);
    --minimize-button-hover-background-color: rgba(249,249,250,0.15);
    --minimize-button-active-background-color: rgba(249,249,250,0.2);
    --control-icon-unchecked-hover-background-color: rgba(249,249,250,0.15);
    --control-icon-unchecked-active-background-color: rgba(249,249,250,0.2);
    --control-icon-checked-background-color: hsla(343,100%,58%,.16);
    --control-icon-checked-icon-fill: rgb(255,40,102);
    --control-icon-checked-hover-background-color: hsla(343,100%,58%,.24);
    --control-icon-checked-active-background-color: hsla(343,100%,58%,.32);
  }
}

body {
  display: inline-flex;
  background-color: var(--indicator-background-color);
  color: var(--indicator-color);
  margin: 0;
  user-select: none;
  -moz-window-dragging: drag;
  /**
   * On macOS, for windows with small enough dimensions, we seem to get
   * vertical scrollbars on the body, even when the contents initially
   * fit the window. We sidestep this by making sure we never display
   * scrollbars.
   */
  overflow: hidden;
  cursor: move;
  border: 1px solid;
  border-color: var(--indicator-border-color);
  /* max-height = indicator height minus top and bottom border */
  max-height: calc(var(--indicator-height) - 2px);
}

button,
input[type="checkbox"] {
  appearance: none;
  border-style: none;
  margin: 0;
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--indicator-icon-fill-opacity);
  background-repeat: no-repeat;
  -moz-window-dragging: no-drag;
  color: inherit;
}

.row-item {
  display: flex;
  align-items: center;
  min-width: 30px;
  margin-block: 5px;
  margin-inline: 0 5px;
  box-sizing: border-box;
  padding: 0 5px;
}

.separator {
  border-inline-end: var(--indicator-item-separator);
  min-width: 1px;
  padding: 0;
}

/**
 * For display sharing, if we happen to be sharing both
 * a window and a screen, we want to show the UI for sharing
 * the screen, since that's the more privacy-sensitive one.
 */
:root[sharingwindow]:not([sharingscreen]) > body > #display-share > #screen-share-info,
:root[sharingwindow]:not([sharingbrowserwindow]) > body > #display-share > #browser-window-share-info,
:root[sharingwindow][sharingbrowserwindow] > body > #display-share > #window-share-info,
:root[sharingscreen] > body > #display-share > #window-share-info,
:root[sharingscreen] > body > #display-share > #browser-window-share-info,

/**
 * If we're not sharing either the screen or the window, we can
 * hide the entire display sharing section.
 */
:root:not([sharingscreen],[sharingwindow]) > body > #display-share,
:root:not([sharingscreen],[sharingwindow]) > body > #display-share + .separator,
:root:not([sharingvideo]) > body > #device-share > #camera-mute-toggle,
:root:not([sharingaudio]) > body > #device-share > #microphone-mute-toggle,
:root:not([sharingvideo],[sharingaudio]) > body > #device-share,
:root:not([sharingvideo],[sharingaudio]) > body > #device-share + .separator {
  display:none;
}

xul|menu {
  overflow: hidden;
  min-height: 0 !important;
  height: 0 !important;
  width: 0 !important;
  appearance: none !important;
  padding: 0 !important;
}

.control-icon {
  background-position: center;
  background-size: 16px;
  background-color: transparent;
  padding: 10px 16px;
  border-radius: 5px;
}

.control-icon + .control-icon {
  margin-inline-start: 6px;
}

.control-icon:hover {
  background-color: var(--control-icon-unchecked-hover-background-color);
}

.control-icon:active {
  background-color: var(--control-icon-unchecked-active-background-color);
}

.control-icon:checked {
  background-color: var(--control-icon-checked-background-color);
  -moz-context-properties: fill;
  fill: var(--control-icon-checked-icon-fill);
}

.control-icon:checked:hover {
  background-color: var(--control-icon-checked-hover-background-color);
}

.control-icon:checked:active {
  background-color: var(--control-icon-checked-active-background-color);
}

#display-share-icon {
  background-image: url("chrome://browser/skin/notification-icons/screen.svg");
  width: 16px;
  height: 16px;
  margin-inline: 5px 10px;
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--indicator-icon-fill-opacity);
}

#camera-mute-toggle {
  background-image: url("chrome://browser/skin/notification-icons/camera.svg");
}

#camera-mute-toggle:checked {
  background-image: url("chrome://browser/skin/notification-icons/camera-blocked.svg");
}

#microphone-mute-toggle {
  background-image: url("chrome://browser/skin/notification-icons/microphone.svg");
}

#microphone-mute-toggle:checked {
  background-image: url("chrome://browser/skin/notification-icons/microphone-blocked.svg");
}

.stop-button {
  background-color: var(--indicator-stop-button-background-color);
  color: var(--indicator-stop-button-color);
  border-radius: 5px;
  padding: 3px 5px;
  margin-inline-start: 15px;
}

.stop-button:hover {
  background-color:  var(--indicator-stop-button-hover-background-color);
}

#window-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#minimize {
  padding: 10px;
  min-width: unset;
  background-image: url("chrome://browser/skin/notification-icons/minimize.svg");
  background-color: var(--minimize-button-background-color);
}

#minimize:hover {
  background-color: var(--minimize-button-hover-background-color);
}

#minimize:active {
  background-color: var(--minimize-button-active-background-color);
}

#drag-indicator {
  background-image: url("chrome://browser/skin/notification-icons/drag-indicator.svg");
  background-repeat: no-repeat;
  background-position: center;
  width: 5px;
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: 0.4;
  margin: 5px;
}

#webRTC-sharingCamera-menu,
#webRTC-sharingMicrophone-menu,
#webRTC-sharingScreen-menu {
  -moz-context-properties: fill;
  fill: currentColor;
}

#webRTC-sharingCamera-menu {
  list-style-image: url("chrome://browser/skin/notification-icons/camera.svg");
}

#webRTC-sharingMicrophone-menu {
  list-style-image: url("chrome://browser/skin/notification-icons/microphone.svg");
}

#webRTC-sharingScreen-menu {
  list-style-image: url("chrome://browser/skin/notification-icons/screen.svg");
}

#webRTC-sharingCamera-menu > menupopup,
#webRTC-sharingMicrophone-menu > menupopup,
#webRTC-sharingScreen-menu > menupopup {
  list-style-image: none; /* don't inherit into menu items */
}

@media (-moz-platform: macos) {
  /**
   * On macOS, the ordering of the drag indicator and window controls is reversed
   * to be more native. We re-order all items including their separators so that
   * the markup and separator hiding and showing logic in the shared CSS file
   * doesn't need to be macOS-specific.
   */
  #window-controls {
    order: 1;
  }

  #display-share + .separator {
    order: 2;
  }

  #display-share {
    order: 3;
  }

  #device-share + .separator {
    order: 4;
  }

  #device-share {
    order: 5;
  }

  #drag-indicator {
    order: 6;
  }

  /**
   * The minimize button on macOS should be circular, and the icon should be
   * centered. The asset we have for the minimize icon puts it along the bottom,
   * so we manually offset it here.
   */
  #minimize {
    background-position: center -5px;
    padding: 8px;
    border: 1px solid transparent;
    border-radius: 1000px;
  }
}

@media (-moz-platform: windows) {
  @media (prefers-contrast) {
    :root {
      --indicator-background-color: -moz-Dialog;
      --indicator-color: -moz-DialogText;
      --indicator-border-color: InactiveBorder;
      --indicator-item-separator: 1px solid ThreeDShadow;
      --indicator-stop-button-background-color: hsla(0,0%,70%,.2);
      --indicator-stop-button-hover-background-color: hsla(0,0%,70%,.4);
      --indicator-stop-button-color: inherit;
      --minimize-button-background-color: hsla(0,0%,70%,.2);
      --minimize-button-hover-background-color: hsla(0,0%,70%,.4);
      --minimize-button-active-background-color: hsla(0,0%,70%,.6);
      --control-icon-unchecked-hover-background-color: hsla(0,0%,70%,.2);
      --control-icon-unchecked-active-background-color: hsla(0,0%,70%,.6);
      --control-icon-checked-background-color: SelectedItem;
      --control-icon-checked-icon-fill: SelectedItemText;
      --control-icon-checked-hover-background-color: hsla(0,0%,70%,.2);
      --control-icon-checked-active-background-color: hsla(0,0%,70%,.6);
    }

    #minimize,
    .stop-button {
      border: 1px outset ThreeDLightShadow;
    }
  }

  #webRTC-sharingCamera-menu {
    list-style-image: url("chrome://browser/skin/notification-icons/camera.png");
  }

  #webRTC-sharingMicrophone-menu {
    list-style-image: url("chrome://browser/skin/notification-icons/microphone.png");
  }

  #webRTC-sharingScreen-menu {
    list-style-image: url("chrome://browser/skin/notification-icons/screen.png");
  }
}
