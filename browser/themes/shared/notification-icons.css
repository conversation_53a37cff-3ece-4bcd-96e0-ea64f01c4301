/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

#notification-popup-box {
  height: calc(var(--urlbar-min-height) - 2px /* border */ - 2 * var(--urlbar-container-padding));
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
  color: inherit;
  border-radius: var(--urlbar-icon-border-radius);

  &:hover {
    background-color: hsla(0,0%,70%,.2);
  }

  &:hover:active,
  &[open] {
    background-color: hsla(0,0%,70%,.3);
  }
}

.popup-notification-icon,
.permission-popup-permission-icon {
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
}

/* Notification anchors should only be visible when their associated
   notifications are. */
#blocked-permissions-container > .blocked-permission-icon:not([showing]),
.notification-anchor-icon:not([showing]) {
  display: none;
}

/* This class can be used alone or in combination with the class defining the
   type of icon displayed. This rule must be defined before the others in order
   for its list-style-image to be overridden. */
.notification-anchor-icon {
  list-style-image: url(chrome://global/skin/icons/info-filled.svg);
  padding: var(--urlbar-icon-padding);

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
    border-radius: var(--urlbar-icon-border-radius);
  }

  #nav-bar:not([keyNav]) & {
    -moz-user-focus: normal;
  }
}

.blocked-permission-icon:focus-visible {
  outline: var(--focus-outline);
  outline-offset: calc(var(--urlbar-icon-padding) + var(--focus-outline-inset));
  border-radius: 1px;
}

/* INDIVIDUAL NOTIFICATIONS */

.focus-tab-by-prompt-icon {
  list-style-image: url(chrome://global/skin/icons/info.svg);
}

.storage-access-icon {
  list-style-image: url(chrome://browser/skin/controlcenter/3rdpartycookies.svg);
}

.persistent-storage-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/persistent-storage.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/persistent-storage-blocked.svg);
  }
}

.desktop-notification-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/desktop-notification.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/desktop-notification-blocked.svg);
  }
}

.geo-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/geo.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/geo-blocked.svg);
  }
}

.open-protocol-handler-icon {
  list-style-image: url(chrome://global/skin/icons/open-in-new.svg);

  &:-moz-locale-dir(rtl) {
    transform: scaleX(-1);
  }
}

.xr-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/xr.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/xr-blocked.svg);
  }
}

.autoplay-media-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/autoplay-media.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/autoplay-media-blocked.svg);
  }
}

.indexedDB-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/persistent-storage.svg);
}

#password-notification-icon {
  list-style-image: url(chrome://browser/skin/login.svg);

  &[extraAttr="attention"] {
    fill: var(--toolbarbutton-icon-fill-attention);
    fill-opacity: 1;
  }
}

.camera-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/camera.svg);

  &.in-use {
    list-style-image: url(chrome://browser/skin/notification-icons/camera.svg);
  }

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/camera-blocked.svg);
  }
}

.microphone-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/microphone.svg);

  &.in-use {
    list-style-image: url(chrome://browser/skin/notification-icons/microphone.svg);
  }

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/microphone-blocked.svg);
  }

  &.popup-notification-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/microphone.svg);
  }
}


.screen-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/screen.svg);

  &.in-use {
    list-style-image: url(chrome://browser/skin/notification-icons/screen.svg);
  }

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/notification-icons/screen-blocked.svg);
  }
}

.speaker-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/speaker.svg);
}

.midi-icon {
  list-style-image: url(chrome://browser/skin/notification-icons/midi.svg);
}

#canvas-notification-icon,
.canvas-icon {
  list-style-image: url(chrome://browser/skin/canvas.svg);
}

.canvas-icon.blocked-permission-icon {
  list-style-image: url(chrome://browser/skin/canvas-blocked.svg);
}

#webauthn-notification-icon,
.popup-notification-icon[popupid^="webauthn-prompt-"] {
  list-style-image: url(chrome://browser/skin/fingerprint.svg);
}

#identity-credential-notification-icon,
#credential-chooser-notification-icon {
  list-style-image: url(chrome://browser/skin/login.svg);
}

#webRTC-previewVideo {
  border-radius: var(--border-radius-small);
  border: 1px solid var(--panel-separator-color);
  min-width: 0;
  min-height: 10em;
  max-height: 250px;
}

#webRTC-all-windows-shared,
#webRTC-previewWarning {
  font-size: 0.8em;

  @media not (prefers-contrast) {
    opacity: 0.6;
  }
}

#webRTC-previewWarning {
  margin-block-start: var(--space-large);
}

/**
 * All learn-more links tend to have an !important margin-top, but
 * the small one below the WebRTC preview warning is special and
 * doesn't have one by design.
 */
#webRTC-previewWarning-learnMore {
  margin-top: 0 !important;
  font-size: 0.75em;
}

/* This icon has a block sign in it, so we don't need a blocked version. */
.popup-icon {
  list-style-image: url("chrome://browser/skin/notification-icons/popup.svg");
}

/**
 * Bug 1708716: The blocked popup icon is just slightly vertically offset from
 * the other icons that can appear alongside it in the URL bar due to how it's
 * placed in the SVG file. We nudge it a little here to make it line up in a
 * more aesthetically pleasing way.
 */
#blocked-permissions-container > .popup-icon {
  transform: translateY(1px);
}

/* EME */

.drm-icon {
  list-style-image: url("chrome://browser/skin/drm-icon.svg");
}

.notification-anchor-icon[animate=true],
#eme-notification-icon[firstplay=true] {
  animation: emeTeachingMoment 0.2s linear 0s 5 normal;
}

@keyframes emeTeachingMoment {
  0% {transform: translateX(0); }
  25% {transform: translateX(3px) }
  75% {transform: translateX(-3px) }
  100% { transform: translateX(0); }
}

/* INSTALL ADDONS */

.install-icon {
  list-style-image: url(chrome://mozapps/skin/extensions/extension.svg);

  &.blocked-permission-icon {
    list-style-image: url(chrome://browser/skin/addons/addon-install-blocked.svg);
  }
}

/* UPDATE */

.popup-notification-icon[popupid="update-available"],
.popup-notification-icon[popupid="update-manual"],
.popup-notification-icon[popupid="update-restart"] {
  background: url(chrome://browser/skin/update-badge.svg) no-repeat center;
  -moz-context-properties: fill;
  fill: var(--panel-banner-item-update-supported-bgcolor);
}

.popup-notification-icon[popupid="update-unsupported"] {
  background: url(chrome://global/skin/icons/warning.svg) no-repeat center;
  -moz-context-properties: fill;
  fill: var(--warning-icon-bgcolor);
}
