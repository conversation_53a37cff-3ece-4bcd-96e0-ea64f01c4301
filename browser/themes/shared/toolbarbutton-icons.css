/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

.toolbarbutton-animatable-box,
.toolbarbutton-1 {
  color: inherit;
  -moz-context-properties: fill, fill-opacity;
  fill: var(--toolbarbutton-icon-fill);
}

#back-button:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#forward-button:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#reload-button:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#reload-button:-moz-locale-dir(rtl) > .toolbarbutton-animatable-box > .toolbarbutton-animatable-image,
#stop-button:-moz-locale-dir(rtl) > .toolbarbutton-animatable-box > .toolbarbutton-animatable-image,
#nav-bar-overflow-button:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#nav-bar-overflow-button:-moz-locale-dir(rtl) > .toolbarbutton-animatable-box > .toolbarbutton-animatable-image,
#PlacesChevron:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#panic-button:-moz-locale-dir(rtl) > .toolbarbutton-icon,
#PanelUI-menu-button:-moz-locale-dir(rtl) > .toolbarbutton-badge-stack > .toolbarbutton-icon {
  scale: -1 1;
}

#back-button {
  list-style-image: url("chrome://browser/skin/back.svg");
}

#forward-button {
  list-style-image: url("chrome://browser/skin/forward.svg");
}

#reload-button {
  list-style-image: url("chrome://global/skin/icons/reload.svg");
}

#stop-button {
  list-style-image: url("chrome://global/skin/icons/close.svg");
}

#home-button {
  list-style-image: url("chrome://browser/skin/home.svg");
}

#bookmarks-toolbar-button,
#bookmarks-toolbar-placeholder {
  list-style-image: url("chrome://browser/skin/bookmarks-toolbar.svg");
}

#bookmarks-menu-button {
  list-style-image: url("chrome://browser/skin/bookmark-star-on-tray.svg");
}

#history-panelmenu {
  list-style-image: url("chrome://browser/skin/history.svg");
}

#downloads-button {
  list-style-image: url("chrome://browser/skin/downloads/downloads.svg");
}

#open-file-button {
  list-style-image: url("chrome://browser/skin/open.svg");
}

#screenshot-button {
  list-style-image: url("chrome://browser/skin/screenshot.svg");
}

#save-page-button {
  list-style-image: url("chrome://browser/skin/save.svg");
}

#sync-button {
  list-style-image: url("chrome://browser/skin/synced-tabs.svg");
}

#characterencoding-button {
  list-style-image: url("chrome://browser/skin/characterEncoding.svg");
}

#new-window-button {
  list-style-image: url("chrome://browser/skin/window.svg");
}

#new-tab-button {
  list-style-image: url("chrome://browser/skin/new-tab.svg");
}

#privatebrowsing-button {
  list-style-image: url("chrome://browser/skin/privateBrowsing.svg");
}

#find-button {
  list-style-image: url("chrome://global/skin/icons/search-glass.svg");
}

#print-button {
  list-style-image: url("chrome://global/skin/icons/print.svg");
}

#fullscreen-button {
  list-style-image: url("chrome://browser/skin/fullscreen.svg");
}

#developer-button {
  list-style-image: url("chrome://global/skin/icons/developer.svg");
}

#profiler-button-button > .toolbarbutton-icon {
  list-style-image: url("chrome://devtools/skin/images/tool-profiler.svg");
}

#profiler-button-button:not(.profiler-active) > image {
  transform: scaleX(-1);
}

#profiler-button-button.profiler-active > image {
  fill: #0060df;
  fill-opacity: 1;
  background-color: #0060df33;
}

#profiler-button-button.profiler-active:hover > image {
  background-color: #0060df22;
}

#profiler-button-button.profiler-paused > image {
  opacity: 0.4;
}

#preferences-button {
  list-style-image: url("chrome://global/skin/icons/settings.svg");
}

#PanelUI-menu-button {
  list-style-image: url("chrome://browser/skin/menu.svg");
}

#PanelUI-menu-button[badge-status="update-available"],
#PanelUI-menu-button[badge-status="update-downloading"],
#PanelUI-menu-button[badge-status="update-manual"],
#PanelUI-menu-button[badge-status="update-restart"],
#PanelUI-menu-button[badge-status="update-unsupported"],
#PanelUI-menu-button[badge-status="addon-alert"],
#PanelUI-menu-button[badge-status="fxa-needs-authentication"] {
  list-style-image: url("chrome://browser/skin/menu-badged.svg");
}

#cut-button {
  list-style-image: url("chrome://browser/skin/edit-cut.svg");
}

#copy-button {
  list-style-image: url("chrome://global/skin/icons/edit-copy.svg");
}

#paste-button {
  list-style-image: url("chrome://browser/skin/edit-paste.svg");
}

#zoom-out-button {
  list-style-image: url("chrome://global/skin/icons/minus.svg");
}

#zoom-in-button {
  list-style-image: url("chrome://global/skin/icons/plus.svg");
}

#PlacesChevron,
#nav-bar-overflow-button {
  list-style-image: url("chrome://global/skin/icons/chevron.svg");
}

#unified-extensions-button {
  list-style-image: url("chrome://mozapps/skin/extensions/extension.svg");
}

#reset-pbm-toolbar-button {
  list-style-image: url("chrome://browser/skin/flame.svg");
}

#email-link-button {
  list-style-image: url("chrome://browser/skin/mail.svg");
}

#logins-button {
  list-style-image: url("chrome://browser/skin/login.svg");
}

#sidebar-button {
  /* stylelint-disable-next-line media-query-no-invalid */
  @media not (-moz-bool-pref: "sidebar.revamp") {
    list-style-image: url("chrome://browser/skin/sidebars-right.svg");

    &:-moz-locale-dir(ltr):not([positionend]),
    &:-moz-locale-dir(rtl)[positionend] {
      list-style-image: url("chrome://browser/skin/sidebars.svg");
    }
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "sidebar.revamp") {
    list-style-image: url("chrome://browser/skin/sidebar-collapsed-right.svg");

    &:hover {
      list-style-image: url("chrome://browser/skin/sidebar-expanded-right.svg");
    }

    &[expanded] {
      list-style-image: url("chrome://browser/skin/sidebar-expanded-right.svg");

      &:hover {
        list-style-image: url("chrome://browser/skin/sidebar-collapsed-right.svg");
      }
    }

    &:-moz-locale-dir(ltr):not([positionend]),
    &:-moz-locale-dir(rtl)[positionend] {
      list-style-image: url("chrome://browser/skin/sidebar-collapsed.svg");

      &:hover {
        list-style-image: url("chrome://browser/skin/sidebar-expanded.svg");
      }

      &[expanded] {
        list-style-image: url("chrome://browser/skin/sidebar-expanded.svg");

        /* stylelint-disable max-nesting-depth */
        &:hover {
          list-style-image: url("chrome://browser/skin/sidebar-collapsed.svg");
        }
      }
    }
  }
}

#panic-button {
  list-style-image: url("chrome://browser/skin/forget.svg");
}

#panic-button[open] {
  fill: light-dark(rgb(213, 32, 20), #ff848b);
}

#library-button {
  list-style-image: url("chrome://browser/skin/library.svg");
}

#save-to-pocket-button {
  list-style-image: url("chrome://global/skin/icons/pocket-outline.svg");
  fill: var(--toolbarbutton-icon-fill);
}

#save-to-pocket-button[open="true"],
#save-to-pocket-button[pocketed="true"] {
  list-style-image: url("chrome://global/skin/icons/pocket.svg");
  fill: var(--pocket-icon-fill);
}

/* Bookmark toolbar buttons and menu items */

.bookmark-item {
  list-style-image: url("chrome://global/skin/icons/defaultFavicon.svg");
  -moz-context-properties: fill;
  fill: currentColor;

  &:is(toolbarbutton) {
    -moz-context-properties: fill, fill-opacity;
    fill: var(--toolbarbutton-icon-fill);
  }

  &[container] {
    list-style-image: url("chrome://global/skin/icons/folder.svg");
  }

  &[query] {
    list-style-image: url("chrome://browser/skin/places/folder-smart.svg");
  }

  &[tagContainer] {
    list-style-image: url("chrome://browser/skin/places/tag.svg");
  }

  &[dayContainer] {
    list-style-image: url("chrome://browser/skin/history.svg");
  }

  &[hostContainer] {
    list-style-image: url("chrome://global/skin/icons/folder.svg");
  }
}

/* Apply crisp rendering for favicons at exactly 2x resolution */
@media (resolution: 2x) {
  :is(.bookmark-item, .menuitem-with-favicon) > .menu-iconic-left > .menu-iconic-icon,
  .bookmark-item > .toolbarbutton-icon {
    image-rendering: -moz-crisp-edges;
  }
}

#import-button {
  list-style-image: url("chrome://browser/skin/import.svg");
}

#aboutwelcome-button {
  list-style-image: url("chrome://browser/skin/circle-check-dotted.svg");
}

#firefox-view-button {
  list-style-image: url("chrome://browser/skin/firefox-view.svg");
}

#content-analysis-indicator {
  -moz-context-properties: fill, stroke;
  stroke: var(--toolbarbutton-icon-fill);
  list-style-image: url("chrome://global/skin/icons/content-analysis.svg");
}

#fxms-bmb-button {
  list-style-image: url("chrome://branding/content/about-logo.png");
}
