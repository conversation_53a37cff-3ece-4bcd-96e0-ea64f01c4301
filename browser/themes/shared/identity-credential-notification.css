/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#identity-credential-notification,
#credential-chooser-notification {
  --list-item-border: color-mix(in srgb, currentColor 10%, transparent);
  --list-item-checked-bgcolor: color-mix(in srgb, var(--color-accent-primary) 6%, transparent);
  --list-item-checked-border: color-mix(in srgb, var(--color-accent-primary) 20%, transparent);
  @media (prefers-contrast) {
    --list-item-border: ThreeDShadow;
    --list-item-checked-bgcolor: transparent;
    --list-item-checked-border: AccentColor;
  }
}

#identity-credential-provider-selector-container,
#identity-credential-account-selector-container,
#credential-chooser-entry-selector-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 540px;
  overflow: auto;
}

#identity-credential-header-text,
#credential-chooser-header-text {
  font-weight: 600;
}

#identity-credential-header-container,
#credential-chooser-header-container {
  margin: 16px 16px -16px 16px;
  display: flex;
}

.identity-credential-header-icon {
  width: 16px;
  height: 16px;
  margin-inline-end: 8px;
}

.identity-credential-list-item {
  display: flex;
  gap: 10px;
  padding-block: max(calc(var(--arrowpanel-menuitem-padding-block) * 2), 4px);
  padding-inline: calc(var(--arrowpanel-menuitem-padding-inline) * 2);
  border: 2px solid var(--list-item-border);
  border-radius: 4px;
}

.identity-credential-list-item.checked,
.identity-credential-list-item:has(> input:checked) {
  background-color: var(--list-item-checked-bgcolor);
  border-color: var(--list-item-checked-border);
}

.identity-credential-list-item-icon {
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: 0.6;
  clip-path: circle(50%);
  width: 32px;
  height: 32px;
}

.identity-credential-list-item > .identity-credential-list-item-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
}

.identity-credential-list-item .identity-credential-list-item-label-primary {
  font-weight: 600;
}

.identity-credential-list-item .identity-credential-list-item-label-secondary {
  font-size: 80%;
}

.identity-credential-list-item > .identity-credential-list-item-label-stack {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
}
