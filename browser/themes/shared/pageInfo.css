/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/global.css");
@namespace html url("http://www.w3.org/1999/xhtml");

@media (-moz-platform: macos) {
  #topBar {
    position: relative;
    -moz-window-dragging: drag;
    padding-top: env(-moz-mac-titlebar-height);
    border-bottom: 1px solid ThreeDShadow;

    align-items: center;
    justify-content: center;

    &::after {
      content: "";
      position: absolute;
      inset: 0;
      appearance: auto;
      -moz-default-appearance: -moz-window-titlebar;
      z-index: -1;
    }
  }
}

@media not (-moz-platform: macos) {
  #topBar {
    border-bottom: 1px solid ThreeDShadow;
    padding-inline-start: 10px;
    background-color: Field;
    color: FieldText;
  }
}

/* View buttons */
@media not (-moz-platform: macos) {
  #viewGroup > radio {
    appearance: none;
    list-style-image: none;
    min-width: 4.5em;
    margin: 0;
    padding: 3px;
  }

  #viewGroup > radio > .radio-label-box {
    margin: 0;
    padding: 0 6px;
  }

  #viewGroup:focus-visible > radio[focused="true"] > .radio-label-box {
    outline: var(--default-focusring);
  }

  #viewGroup .radio-icon {
    background-image: url("chrome://browser/skin/pageInfo.png");
    width: 32px;
    height: 32px;
    /* Avoid anti-aliasing seams in HiDPI */
    image-rendering: crisp-edges;
  }

  #generalTab .radio-icon {
    background-position-x: 0;
  }

  #mediaTab .radio-icon {
    background-position-x: -32px;
  }

  #permTab .radio-icon {
    background-position-x: -96px;
  }

  #securityTab .radio-icon {
    background-position-x: -128px;
  }
}

@media (-moz-platform: linux) {
  #viewGroup > radio {
    color: FieldText;
  }

  #viewGroup > radio[selected="true"] {
    background-color: SelectedItem;
    color: SelectedItemText;
  }
}

@media (-moz-platform: windows) {
  #viewGroup {
    padding-inline-start: 10px;
  }

  #viewGroup > radio:hover {
    background-color: #E0E8F6;
    color: black;
  }

  #viewGroup > radio[selected="true"] {
    background-color: #C1D2EE;
    color: black;
  }

  #viewGroup > radio:is(:hover, [selected="true"]) .radio-icon {
    background-position-y: -32px;
  }
}

@media (-moz-platform: macos) {
  #viewGroup {
    margin: 4px 0 9px;
  }

  #viewGroup > radio,
  #viewGroup > toolbarbutton {
    flex-direction: column;
    align-items: center;
    appearance: auto;
    -moz-default-appearance: toolbarbutton;
    font: menu;
    margin: 0;
    padding: 0 6px;
    height: 22px;
  }

  @media (-moz-mac-big-sur-theme: 0) {
    #viewGroup > radio[selected=true],
    #viewGroup > toolbarbutton[checked=true] {
      color: #FFF !important;
      text-shadow: rgba(0, 0, 0, 0.4) 0 1px;
    }
  }
}

/* Misc */
tree {
  margin: .5em;
}

html|input {
  background: transparent;
  border: none;
  padding: 0;
}

html|input.header {
  margin-inline-start: 0;
}

#imagecontainerbox {
  margin: .5em;
  background: white;
  overflow: auto;
  border: 1px solid ThreeDLightShadow;
}

html|input:disabled {
  font-style: italic;
}

@media (-moz-platform: macos) {
  .help-button {
    appearance: auto;
    -moz-default-appearance: -moz-mac-help-button;
  }
}

/* General Tab */

@media (-moz-platform: linux) {
  #generalPanel > #titletext {
    margin-inline-start: 5px;
  }
}

#securityBox description {
  margin-inline-start: 10px;
}

#general-security-identity {
  white-space: pre-wrap;
  line-height: 2em;
}

/* Media Tab */
#imagetree {
  min-height: 10em;
  margin-block: 2px 0;
}

#mediaSplitter {
  background: none;
  appearance: none;
  border-style: none;
}

@media not (-moz-platform: macos) {
  #mediaSplitter {
    height: .8em;
  }
}

#mediaTable {
  margin-bottom: 2em;
}

#mediaLabelColumn {
  min-width: 10em;
}

treechildren::-moz-tree-cell-text(broken) {
  font-style: italic;
  color: graytext;
}

/* Permissions Tab */

#permList {
  appearance: auto;
  -moz-default-appearance: listbox;
  margin: .5em;
  overflow: auto;
  color: FieldText;
}

.permission {
  padding: 6px 7px;
  border-bottom: 1px dotted ThreeDShadow;
}

.permissionLabel {
  font-weight: bold;
}

.permission:hover {
  background-color: -moz-dialog;
  color: -moz-DialogText;
}

/* Security Tab */

#securityPanel table {
  margin-bottom: 1em;
}
