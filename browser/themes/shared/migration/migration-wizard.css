/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://global/skin/global.css");
@import url("chrome://global/skin/in-content/common.css");

:host {
  display: flex;
  flex-direction: column;
  text-align: start;
  width: 22.34em;
}

h1 {
  font-size: var(--embedded-wizard-header-font-size, 1.30em);
  font-weight: var(--embedded-wizard-header-font-weight, normal);
  margin-block: var(--embedded-wizard-header-margin-block, 0 16px);
  min-height: 1em;
}

ol {
  padding-inline-start: 24px;
  margin-block: 16px 0;
}

li:not(:last-child) {
  margin-block-end: 16px;
}

input[type="checkbox"] {
  margin-block: 0;
}

div[name="page-loading"] > .buttons > button {
  color: transparent;
}

.loading-block {
  background-color: var(--in-content-button-background);
  border-radius: 4px;
  opacity: 0.4;
  margin-block-start: 16px;
}

.loading-block.large {
  height: 36px;
}

.loading-block.small {
  height: 16px;
}

.buttons {
  margin-block-start: 16px;
}

.deemphasized-text {
  font-size: 0.84em;
}

@media not (prefers-contrast) {
  .deemphasized-text {
    color: var(--text-color-deemphasized);
  }
}

fieldset {
  border: 0;
}

label:not([hidden]) {
  display: flex;
  align-items: center;
  width: fit-content;
}

#wizard-deck > div {
  display: flex;
  flex-direction: column;
}

#browser-profile-selector {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 0;
  font-weight: unset;
}

.migrator-icon {
  content: url(chrome://global/skin/icons/defaultFavicon.svg);
  height: 32px;
  width: 32px;
  -moz-context-properties: fill;
  fill: currentColor;
}

.migrator-icon,
.migrator-description,
.dropdown-icon {
  pointer-events: none;
}

.dropdown-icon {
  content: url("chrome://global/skin/icons/arrow-down.svg");
  -moz-context-properties: fill;
  fill: currentColor;
  height: 16px;
  width: 16px;
}

.migrator-description {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  margin-inline-start: 11px;
}

#profile-name:empty {
  display: none;
}

#profile-name {
  font-size: 0.73em;
  text-transform: uppercase;
}

/**
 * When the panel-list is embedded in a XUL panel, we don't want the default
 * padding that a XUL panel gets.
 */
panel {
  --panel-padding: 0;
}

panel-item::part(button) {
  min-height: 40px;
}

panel-item[type="file"]::part(button) {
  background-size: 20px;
  background-position: 6px center;
}

panel-item[type="file"]:-moz-locale-dir(rtl)::part(button),
panel-item[type="file"]:dir(rtl)::part(button) {
  background-position-x: right 6px;
}

div[name="page-selection"]:not([show-import-all]) #select-all,
div[name="page-selection"][show-import-all][single-item] #select-all,
div[name="page-selection"]:not([show-import-all]) summary {
  display: none;
}

div[name="page-selection"][show-import-all]:not([single-item]) label:not(#select-all)  {
  margin-inline: var(--resource-type-label-margin-inline, 24px 0);
}

div[name="page-selection"][show-import-all] #select-all  {
  display: flex;
}

div[name="page-selection"][show-import-all][hide-select-all] #select-all  {
  display: none;
}

div[name="page-selection"][migrator-type="browser"] > .buttons > #import-from-file,
div[name="page-selection"][migrator-type="browser"][no-permissions] > .buttons > #import,
div[name="page-selection"][migrator-type="browser"]:not([no-permissions]) > .buttons > #get-permissions,
div[name="page-selection"][migrator-type="file"] > .buttons > #get-permissions,
div[name="page-selection"][migrator-type="file"] > .buttons > #import,
div[name="page-selection"][migrator-type="file"] > .resource-selection-details,
div[name="page-selection"][migrator-type="file"] > .resource-selection-preamble,
div[name="page-selection"][show-import-all] .resource-selection-preamble {
  display: none;
}

/**
 * We're using <code> to ease legibility for paths on
 * the user's system, but intentionally reduce the font
 * size to more closely match the surrounding text.
 */
.migration-no-permissions-instructions-step2 > code {
  font-size: 0.94em;
}

.resource-selection-preamble {
  margin-block-start: 16px;
}

details[open] .expand-collapse-icon {
  content: url("chrome://global/skin/icons/minus.svg");
}

.expand-collapse-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  content: url("chrome://global/skin/icons/plus.svg");
  grid-area: b;
  -moz-context-properties: fill;
  fill: currentColor;
  justify-self: end;
  margin-inline-end: 8px;
}

details:not([open]) summary {
  margin-block-end: 16px;
}

summary {
  cursor: pointer;
}

#resource-type-list {
  padding: 0;
}

#resource-type-list > label {
  margin-block: var(--resource-type-label-margin-block, 16px 0);
  margin-inline: var(--resource-type-label-margin-inline, 0);
}

:host(:not([dialog-mode])) .cancel-close,
:host(:not([dialog-mode])) .done-button,
:host([dialog-mode]) .continue-button {
  display: none;
}

.resource-progress {
  display: grid;
  gap: 16px;
}

.resource-progress-group {
  display: grid;
  grid-template-columns: min-content auto;
  grid-template-areas: "icon type"
                       "icon status"
                       "icon support";
  align-items: center;
  justify-items: start;
}

.resource-progress-group[hidden] {
  display: none;
}

.message-text,
.error-text {
  grid-area: status;
}

.support-text {
  grid-area: support;
}

/**
 * Workaround for bug 1671784, which shows a slight outline around the
 * rotating masked element.
 */
.progress-icon-parent {
  border-radius: 0.01px;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.progress-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  -moz-context-properties: fill;
  margin-inline-end: 8px;
}

/**
 * For now, both the success and info icon states appear the same.
 */
.progress-icon[state="success"],
.progress-icon[state="info"] {
  content: url("chrome://browser/skin/migration/success.svg");
  fill: var(--icon-color-success);
}

.progress-icon[state="warning"] {
  content: url("chrome://global/skin/icons/warning.svg");
  fill: var(--icon-color-warning);
}

@media (prefers-reduced-motion: reduce) {
  .progress-icon[state="loading"] {
    content: url("chrome://global/skin/icons/loading.svg");
    fill: var(--text-color-deemphasized);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .progress-icon[state="loading"] {
    content: "";
    background-image: conic-gradient(rgba(255, 255, 255, 0.31) 0%, rgba(255, 189, 79, 0.57) 38%, rgba(255, 74, 162, 0.79) 70%, rgb(144, 89, 255) 100%);
    mask: url("chrome://browser/skin/migration/progress-mask.svg");
    mask-composite: exclude;
    animation-name: rotate;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-icon {
  content: url("chrome://global/skin/icons/error.svg");
  -moz-context-properties: fill;
  fill: var(--in-content-danger-button-background);
  margin-block: 0;
  margin-inline: 0 8px;
  width: 16px;
  height: 16px;
}

.page-portrait-icon {
  width: 16px;
  height: 16px;
  content: url("chrome://global/skin/icons/page-portrait.svg");
  -moz-context-properties: fill;
  fill: currentColor;
  vertical-align: middle;
}

.safari-icon-3dots {
  width: 16px;
  height: 16px;
  vertical-align: middle;
  content: url("chrome://browser/skin/migration/safari-icon-3dots.svg");
  -moz-context-properties: fill, stroke;
  fill: currentColor;
  stroke: color-mix(in srgb, currentColor 10%, transparent 90%);
}

.no-browsers-found-message {
  display: flex;
}

.error-message {
  display: flex;
  align-items: start;
}

.file-import-error,
.no-resources-found {
  font-size: 0.84em;
  margin-block-start: 16px;
}

.file-import-error {
  margin-inline-start: 8px;
}

div[name="page-selection"]:not([no-resources]) .no-resources-found,
div[name="page-selection"]:not([no-permissions]) .no-permissions-message,
div[name="page-selection"]:is([no-resources],[no-permissions]) .hide-on-error,
div[name="page-selection"]:not([file-import-error]) .file-import-error {
  display: none;
}

div[name="page-selection"][show-import-all] .resource-selection-details {
  padding-inline-start: 8px;
}

.resource-selection-details > summary {
  list-style: none;
  display: grid;
  grid-template-areas: "a b"
                       "c b";
  margin-block: 16px 0;
}

:host([hide-option-expander-subtitle]) .selected-data {
  display: none;
}

p.migration-wizard-subheader {
  font-size: var(--embedded-wizard-subheader-font-size, .85em);
  font-weight: var(--embedded-wizard-subheader-font-weight, 400);
  margin-block: var(--embedded-wizard-subheader-margin-block, 0 28px);
  line-height: 1.2em;

  &[hidden] {
    display: none;
  }
}
