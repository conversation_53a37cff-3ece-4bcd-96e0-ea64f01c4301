/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

/* UI Tour */

@keyframes uitour-wobble {
  from {
    transform: rotate(0deg) translateX(3px) rotate(0deg);
  }
  50% {
    transform: rotate(360deg) translateX(3px) rotate(-360deg);
  }
  to {
    transform: rotate(720deg) translateX(0) rotate(-720deg);
  }
}

@keyframes uitour-zoom {
  from {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.0);
  }
  to {
    transform: scale(0.8);
  }
}

@keyframes uitour-color {
  from {
    border-color: #5B9CD9;
  }
  50% {
    border-color: #FF0000;
  }
  to {
    border-color: #5B9CD9;
  }
}

#UITourHighlightContainer {
  appearance: none;
  pointer-events: none;

  /* Resets the native styles in windows and macOS */
  border: none;
  background-color: transparent;
  -moz-window-shadow: none;

  --panel-border-color: transparent;
  --panel-background: transparent;
  /* This is a buffer to compensate for the movement in the "wobble" effect,
     and for the box-shadow of #UITourHighlight. */
  --panel-padding: 4px;
  /* Compensate the displacement caused by padding. */
  margin: -4px;
}

#UITourHighlight {
  background-color: rgba(0, 200, 215, 0.3);
  min-height: 24px;
  min-width: 24px;

  &.rounded-highlight {
    border-radius: 4px;
  }

  &[active] {
    animation-delay: 2s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }

  &[active="wobble"] {
    animation-name: uitour-wobble;
    animation-delay: 0s;
    animation-duration: 1.5s;
    animation-iteration-count: 1;
  }

  &[active="zoom"] {
    animation-name: uitour-zoom;
    animation-duration: 1s;
  }

  &[active="color"] {
    animation-name: uitour-color;
    animation-duration: 2s;
  }
}

#UITourTooltipBody {
  align-items: flex-start;
}

#UITourTooltipTitleContainer {
  align-items: flex-start;
  margin-bottom: 10px;
}

#UITourTooltipIcon {
  width: 48px;
  height: 48px;
  margin-inline-end: 10px;
}

#UITourTooltipTitle,
#UITourTooltipDescription {
  max-width: 20rem;
}

#UITourTooltipTitle {
  font-size: 1.45rem;
  font-weight: bold;
  margin: 0;
}

#UITourTooltipDescription {
  margin-inline: 0;
  font-size: 1.15rem;
  line-height: 1.8rem;
  margin-bottom: 0; /* Override global.css */
}

#UITourTooltipClose {
  position: relative;
  appearance: none;
  border: none;
  background-color: transparent;
  min-width: 0;
  margin-inline-start: 4px;
  margin-top: -2px;
}

#UITourTooltipClose > .toolbarbutton-text {
  display: none;
}

#UITourTooltipButtons {
  justify-content: flex-end;
  background-color: var(--arrowpanel-dimmed);
  border-top: 1px solid var(--panel-separator-color);
  margin: 10px -16px -16px;
  padding: 16px;
}

#UITourTooltipButtons > label,
#UITourTooltipButtons > button {
  margin: 0 15px;
}

#UITourTooltipButtons > label:first-child,
#UITourTooltipButtons > button:first-child {
  margin-inline-start: 0;
}

#UITourTooltipButtons > label:last-child,
#UITourTooltipButtons > button:last-child {
  margin-inline-end: 0;
}

#UITourTooltipButtons > button[image] > .button-box > .button-icon {
  width: 16px;
  height: 16px;
  margin-inline-end: 5px;
}

#UITourTooltipButtons > label,
#UITourTooltipButtons > button .button-text {
  font-size: 1.15rem;
}

#UITourTooltipButtons > button:not(.button-link) {
  appearance: none;
  background-color: rgb(251,251,251);
  border-radius: 3px;
  border: 1px solid;
  border-color: rgb(192,192,192);
  color: rgb(71,71,71);
  padding: 4px 30px;
  transition-property: background-color, border-color;
  transition-duration: 150ms;
}

#UITourTooltipButtons > button:not(.button-link, :active):hover {
  background-color: hsla(210,4%,10%,.15);
  border-color: hsla(210,4%,10%,.15);
  box-shadow: 0 1px 0 0 hsla(210,4%,10%,.05) inset;
}

#UITourTooltipButtons > label,
#UITourTooltipButtons > button.button-link:not(:hover) {
  appearance: none;
  background: transparent;
  border: none;
  box-shadow: none;
  color: var(--text-color-disabled);
  padding-inline: 10px;
}

/* The primary button gets the same color as the customize button. */
#UITourTooltipButtons > button.button-primary {
  background-color: rgb(116,191,67);
  color: white;
  padding-inline: 30px;
}

#UITourTooltipButtons > button.button-primary:not(:active):hover {
  background-color: rgb(105,173,61);
}
