/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://browser/skin/urlbar-dynamic-results.css");

@namespace html url("http://www.w3.org/1999/xhtml");

:root {
  --urlbarView-hover-background: var(--arrowpanel-dimmed);
  --urlbarView-separator-color: color-mix(in srgb, currentColor 14%, transparent);

  --urlbarView-highlight-background: SelectedItem;
  --urlbarView-highlight-color: SelectedItemText;

  --urlbarView-secondary-text-color: color-mix(in srgb, currentColor 73%, transparent);

  --urlbarView-action-color: LinkText;
  --urlbarView-action-slide-in-distance: 200px;

  --urlbarView-small-font-size: 0.85em;

  --urlbarView-results-padding: 4px;
  --urlbarView-row-gutter: 2px;
  --urlbarView-item-inline-padding: var(--urlbar-icon-padding);
  --urlbarView-item-block-padding: 6px;

  /* The inline margins of a row icon. Rows with different icon sizes will define
     different values for these variables so that the icons of all rows are
     center-aligned along one vertical axis. Row icons happen to use the
     `.urlbarView-favicon` class but not all icons are favicons (e.g., rich
     search suggestion icons are images from the search engine). */
  --urlbarView-icon-margin-start: 0px;
  --urlbarView-icon-margin-end: calc(var(--urlbar-icon-padding) + var(--identity-box-margin-inline));

  /* The size and inline margins of a standard 16px favicon. The margin variables
     can be used to align any 16px content along the vertical icon axis. */
  --urlbarView-favicon-width: 16px;
  --urlbarView-favicon-margin-start: var(--urlbarView-icon-margin-start);
  --urlbarView-favicon-margin-end: var(--urlbarView-icon-margin-end);

  --urlbarView-rich-suggestion-default-icon-size: 28px;

  --urlbarView-result-button-size: 24px;
  --urlbarView-result-button-background-opacity: 60%;
  --urlbarView-result-button-selected-color: var(--toolbar-field-focus-color);
  --urlbarView-result-button-selected-background-color: color-mix(in srgb, var(--toolbar-field-focus-background-color) var(--urlbarView-result-button-background-opacity), transparent);
  --urlbarView-result-button-hover-color: var(--toolbar-field-focus-background-color);
  --urlbarView-result-button-hover-background-color: color-mix(in srgb, var(--toolbar-field-focus-color) var(--urlbarView-result-button-background-opacity), transparent);

  --urlbarView-labeled-row-margin-top: calc(1.46em + 4px);
  --urlbarView-labeled-row-label-top: calc(-1.27em - 2px);
  --urlbarView-labeled-tip-margin-top-extra: 8px;

  --urlbarView-action-button-background-color: color-mix(in srgb, var(--urlbarView-hover-background) 50%, transparent);
  --urlbarView-action-button-hover-background-color: var(--urlbarView-hover-background);

  &:-moz-locale-dir(rtl) {
    --urlbarView-action-slide-in-distance: -200px;
  }

  &[lwtheme] {
    --urlbarView-action-color: light-dark(rgb(91, 91, 102), rgb(191, 191, 201));
    --urlbarView-highlight-background: light-dark(rgb(0, 99, 255), rgb(0, 99, 225));
    --urlbarView-highlight-color: white;
  }

  @media (prefers-contrast) {
    --urlbarView-separator-color: color-mix(in srgb, currentColor 86%, transparent);
    --urlbarView-result-button-background-opacity: 100%;
    --urlbarView-secondary-text-color: currentColor;
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "browser.urlbar.richSuggestions.featureGate") {
    --urlbarView-favicon-margin-start: calc((var(--urlbarView-rich-suggestion-default-icon-size) - var(--urlbarView-favicon-width)) / 2);
    --urlbarView-favicon-margin-end: calc(var(--urlbar-icon-padding) + var(--identity-box-margin-inline) + ((var(--urlbarView-rich-suggestion-default-icon-size) - var(--urlbarView-favicon-width)) / 2));
    }
}

.urlbarView {
  /* Don't handle window drag events in case we are overlapping a toolbar */
  -moz-window-dragging: no-drag;

  display: block;
  text-shadow: none;
  overflow: clip;
  margin-inline: calc(5px + var(--urlbar-container-padding));
  width: calc(100% - 2 * (5px + var(--urlbar-container-padding)));
  /* Match urlbar-background's border. */
  border-inline: 1px solid transparent;

  &[hide-temporarily] {
    display: none;
  }
}

.urlbarView-body-inner {
  width: calc(100% + 2 * var(--urlbarView-row-gutter));
  margin-inline: calc(-1 * var(--urlbarView-row-gutter));

  #urlbar[open] > .urlbarView > .urlbarView-body-outer > & {
    border-top: 1px solid var(--urlbarView-separator-color);
  }
}

.urlbarView-results {
  padding-block: var(--urlbarView-results-padding);
  white-space: nowrap;

  /* Vertically center the one-offs when no results are present. */
  .urlbarView[noresults] > .urlbarView-body-outer > .urlbarView-body-inner > & {
    padding-block: 0;
  }
}

.urlbarView-row {
  --urlbarView-second-line-indent: calc(var(--urlbarView-icon-margin-start) + var(--urlbarView-icon-margin-end) + var(--urlbarView-favicon-width));

  /* Align icons that are smaller than the default rich suggestion icon size
    (28px) with default-size rich suggestion icons. */
  /* stylelint-disable-next-line media-query-no-invalid */
  @media (-moz-bool-pref: "browser.urlbar.richSuggestions.featureGate") {
    &:not([rich-suggestion]),
    &[rich-suggestion][icon-size="16"] {
      --urlbarView-icon-margin-start: var(--urlbarView-favicon-margin-start);
      --urlbarView-icon-margin-end: var(--urlbarView-favicon-margin-end);
    }
    &[rich-suggestion][icon-size="24"] {
      --urlbarView-icon-margin-start: calc((var(--urlbarView-rich-suggestion-default-icon-size) - 24px) / 2);
      --urlbarView-icon-margin-end: calc(var(--urlbar-icon-padding) + var(--identity-box-margin-inline) + ((var(--urlbarView-rich-suggestion-default-icon-size) - 24px) / 2));
    }
  }

  display: flex;
  align-items: center;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);

  /* Use a transparent border rather than margin such that the view's entire
     hover area is contiguous and a row is always hovered as the user moves the
     mouse across rows, even though each row's highlighted area is smaller. */
  border: var(--urlbarView-row-gutter) solid transparent;
  border-radius: calc(var(--urlbarView-row-gutter) + var(--toolbarbutton-border-radius));
  background-clip: padding-box;

  &[row-selectable]:not([selected]):hover {
    background-color: var(--urlbarView-hover-background);
  }

  &[selected] {
    background-color: var(--urlbarView-highlight-background);
    color: var(--urlbarView-highlight-color);
  }

  &:not([type=tip], [type=dynamic]) {
    :root:not([uidensity=compact]) & {
      min-height: 32px;
    }
    :root[uidensity=touch] & {
      padding-block: 11px;
    }
  }

  &[rich-suggestion][type=search] {
    :root:not([uidensity=compact]) & {
      min-height: 46px;
    }
    :root[uidensity=touch] & {
      padding-block: 4px;
    }
  }
}

.urlbarView-row-inner,
.urlbarView-no-wrap {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: baseline;
  justify-content: start;
}

.urlbarView-row-inner {
  flex: 1 1;
  overflow: hidden;
  padding-inline: var(--urlbarView-item-inline-padding);
  padding-block: var(--urlbarView-item-block-padding);

  :root:not([uidensity=compact]) & {
    min-height: 20px; /* row min-height 32px - (2 * padding-block 6px) */
  }
}

.urlbarView-no-wrap {
  max-width: 100%;
  flex-grow: 0;
  flex-shrink: 0;

  .urlbarView[action-override] .urlbarView-row[has-url] > .urlbarView-row-inner > &,
  .urlbarView-row[has-url]:not([type$=tab]) > .urlbarView-row-inner > &,
  .urlbarView-row[has-url]:is([type=remotetab], [sponsored]):is(:hover, [selected]) > .urlbarView-row-inner > & {
    /* We prioritize icons + title + action over the url, so they can grow freely,
       but the url should never disappear when it's visible */
    max-width: calc(70% - 2 * (var(--urlbarView-favicon-width) + (6px + 2px)));
  }
}

/* Wrap the url to a second line when the window is narrow. Do not wrap when the
   window is also short, because fewer results will be shown. */
@media screen and (min-height: 600px) {
  .urlbarView-results[wrap] > .urlbarView-row {
    &:where(:not([rich-suggestion])) > .urlbarView-row-inner {
      flex-wrap: wrap;

      > .urlbarView-no-wrap {
        max-width: 100% !important;
        flex-basis: 100%;
      }
    }

    &[has-url] > .urlbarView-row-inner > .urlbarView-url {
      margin-top: 2px;

      /* urlbarView-url is forced to be LTR for RTL locales, so set the padding based on
         the browser's directionality. */
      &:-moz-locale-dir(ltr) {
        margin-left: var(--urlbarView-second-line-indent);
      }
      &:-moz-locale-dir(rtl) {
        margin-right: var(--urlbarView-second-line-indent);
      }
    }

    /* Note: switchtab entries show the url only in override mode,
       remotetab and sponsored ones only when selected or :hover. */
    .urlbarView[action-override] &[has-url]:not([restyled-search]),
    &[has-url]:not([type$=tab], [sponsored], [restyled-search]),
    &[has-url]:is([type=remotetab], [sponsored]):is(:hover, [selected]),
    &[type=tabtosearch] {
      /* This targets both rich and non-rich results, so not using the child selector here. */
      .urlbarView-title-separator {
        display: none;
      }
    }

    &[type=tabtosearch] > .urlbarView-row-inner > .urlbarView-no-wrap {
      flex-wrap: wrap;

      > .urlbarView-action {
        flex-basis: 100%;
        margin-inline-start: var(--urlbarView-second-line-indent);
      }
    }

    &[rich-suggestion] > .urlbarView-row-inner > .urlbarView-row-body > .urlbarView-row-body-top {
      flex-wrap: wrap;

      > .urlbarView-row-body-top-no-wrap {
        flex-basis: 100%;
      }

      > .urlbarView-url {
        margin-top: 2px;
      }
    }
  }
}

.urlbarView-overflowable,
.urlbarView-url {
  overflow: hidden;
}

.urlbarView-overflowable[overflow],
.urlbarView-url[overflow] {
  mask-image: linear-gradient(to left, transparent, black 2em);
}

.urlbarView-overflowable[overflow]:not(.urlbarView-title[is-url]):-moz-locale-dir(rtl) {
  mask-image: linear-gradient(to right, transparent, black 2em);
}

.urlbarView-title[is-url]:-moz-locale-dir(rtl),
.urlbarView-url:-moz-locale-dir(rtl) {
  direction: ltr !important;
}

.urlbarView-url:-moz-locale-dir(rtl) {
  /* .urlbarView-url can grow due to `flex-grow: 1`, so without `text-align:
     right`, the URL text would be left-aligned within .urlbarView-url for RTL
     due to the `direction: ltr` rule.  .urlbarView-title does not have this
     problem since it does not have flex. */
  text-align: right;
}

/* Favicon */

.urlbarView-favicon {
  width: var(--urlbarView-favicon-width);
  height: var(--urlbarView-favicon-width);
  align-self: center;
  margin-inline-start: var(--urlbarView-icon-margin-start);
  margin-inline-end: var(--urlbarView-icon-margin-end);
  background-repeat: no-repeat;
  background-size: contain;
  object-fit: contain;
  flex-shrink: 0;
  -moz-context-properties: fill, fill-opacity;

  .urlbarView-row[tail-suggestion] > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    visibility: hidden;
  }

  .urlbarView-row[type=tabtosearch]:not([selected]) > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    /* We use the URL color for this icon to inherit its accessibility
       properties, like adapting to high contrast modes. We also want to ensure
       contrast from the result highlight. */
    color: var(--link-color);
    -moz-context-properties: fill;
  }
}

/* Type icon */

.urlbarView-type-icon {
  position: absolute;
  width: 12px;
  height: 12px;
  align-self: center;
  margin-top: var(--urlbarView-favicon-width);
  margin-inline-start: calc(var(--urlbarView-icon-margin-start) + var(--urlbarView-favicon-width) - 6px);
  background-repeat: no-repeat;
  background-size: contain;
  -moz-context-properties: fill, stroke;
  stroke: var(--toolbar-field-focus-background-color);

  /* Favicon badges have this priority: pinned > bookmark. */
  .urlbarView-row[type=bookmark] > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    background-image: url(chrome://browser/skin/bookmark-12.svg);
    fill: var(--toolbarbutton-icon-fill-attention);
  }

  .urlbarView-row[pinned] > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    background-image: url(chrome://browser/skin/pin-12.svg);
    fill: light-dark(rgb(91, 91, 102), rgb(251, 251, 254));
  }
}

/* Buttons */

.urlbarView-button {
  display: inline-block;
  min-width: var(--urlbarView-result-button-size);
  min-height: var(--urlbarView-result-button-size);
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  margin-inline: 4px;
  -moz-context-properties: fill, fill-opacity;
  border-radius: 100%;

  &:not(:hover, [selected], [primary], [open]) {
    @media (prefers-contrast) {
      outline: 1px solid currentColor;
      outline-offset: -1px;
    }
  }

  &[selected] {
    outline: var(--focus-outline);
    outline-color: var(--urlbarView-highlight-color);
    outline-offset: 1px;
  }

  .urlbarView-row:is([row-selectable]:hover, [selected]) > &:not(:hover, [open]) {
    color: var(--urlbarView-result-button-selected-color);
    background-color: var(--urlbarView-result-button-selected-background-color);
  }

  &:is(:hover, [open]) {
    color: var(--urlbarView-result-button-hover-color);
    background-color: var(--urlbarView-result-button-hover-background-color);
  }

  & + & {
    margin-inline-start: 0;
  }
}

/* Result menu button */

.urlbarView-button-menu {
  background-image: url("chrome://global/skin/icons/more.svg");
  background-color: var(--button-background-color);

  .urlbarView-results:not([disable-resultmenu-autohide]) > .urlbarView-row:not(:hover, [selected]) > .urlbarView-row-inner + &:not([open]):empty {
    display: none;
  }

  /* Labeled result menu button */

  &:not(:empty) {
    .urlbarView-results:not([wrap]) > .urlbarView-row > & {
      display: inline-flex;
      align-items: center;
      border-radius: var(--urlbarView-result-button-size);
      padding-inline: 8px 26px;
      background-position-x: right 4px;
      font-size: var(--urlbarView-small-font-size);

      &:-moz-locale-dir(rtl) {
        background-position-x: left 4px;
      }
    }

    .urlbarView-results[wrap] > .urlbarView-row > & {
      /* Hide the label in narrow windows. */
      font-size: 0;
    }
  }
}

/* Button with label, e.g. tip button */

.urlbarView-button:not(:empty):not(.urlbarView-button-menu) {
  border-radius: var(--toolbarbutton-border-radius);
  padding: .5em 1em;
  margin-inline-end: 1.4em;
  font-size: 0.93em;
  font-weight: var(--font-weight-bold);
  background-clip: padding-box;
  min-height: 16px;
  min-width: 6em;
  text-align: center;
  flex-basis: initial;
  flex-shrink: 0;

  &:not(:hover) {
    background-color: var(--button-background-color);
  }

  &:is([selected], [primary]) {
    color: var(--button-text-color-primary);
    background-color: var(--color-accent-primary);
    outline-color: var(--color-accent-primary);
    outline-offset: var(--focus-outline-offset);

    &:hover {
      background-color: var(--color-accent-primary-hover);
    }

    &:hover:active {
      background-color: var(--color-accent-primary-active);
    }
  }
}

/* Tip rows */

.urlbarView-row[type=tip] {
  padding-block: 18px;
  border-block: 0;
  border-radius: 0;

  &:not(:last-child) {
    border-bottom: 1px solid var(--urlbarView-separator-color);
    margin-bottom: 4px;
  }

  &:not(:first-child) {
    border-top: 1px solid var(--urlbarView-separator-color);
    margin-top: 4px;
  }

  /* For tips with row labels, move the entire row down by an additional
     var(--urlbarView-labeled-tip-margin-top-extra) so there's more space
     between the tip's top border and the label. They're too close otherwise.
     The row label must also be adjusted; see the row label rules. */
  &[label] {
    margin-top: calc(var(--urlbarView-labeled-row-margin-top) + var(--urlbarView-labeled-tip-margin-top-extra));
  }

  > .urlbarView-row-inner {
    min-height: 32px;
    align-items: center;

    /* Add space between the tip title (and the rest of row-inner) and its
       button. */
    margin-inline-end: 1.8em;

    > .urlbarView-no-wrap > .urlbarView-title-separator,
    > .urlbarView-url {
      display: none;
    }

    > .urlbarView-no-wrap > .urlbarView-title {
      white-space: normal;
    }
  }

  .urlbarView-results[wrap] > & {
    display: block;
    text-align: end;

    > .urlbarView-row-inner {
      display: block;
      text-align: start;
    }

    > .urlbarView-button {
      margin-top: 1em;
      margin-bottom: var(--urlbarView-item-block-padding);
    }
  }

  &:not([tip-type=dismissalAcknowledgment]) > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon {
    width: 24px;
    height: 24px;
    margin-inline-end: 12px;
    flex-basis: 24px;
    flex-grow:  0;
  }

  &[tip-type=dismissalAcknowledgment] {
    padding-block: 6px;

    &:last-child {
      padding-block-end: max(0px, 6px - var(--urlbarView-results-padding));
    }
  }
}


/* Row label (a.k.a. group label) */

.urlbarView-row[label] {
  position: relative;
  /* `margin-block-start` controls how far the main part of the row is from the
     main part of the previous row. */
  margin-block-start: var(--urlbarView-labeled-row-margin-top);

  &::before {
    content: attr(label);
    display: block;
    /* Remove the label from the document flow so it doesn't affect the row
       selection and hover states. */
    position: absolute;
    /* `top` controls how far the label is from the main part of the row. */
    top: var(--urlbarView-labeled-row-label-top);
    margin-inline-start: var(--urlbarView-item-inline-padding);
    font-size: 0.8em;
    /* The color and opacity of labels is the same as the "This time, search with"
       text in the search shortcuts. See `.search-panel-header > label` in
       searchbar.css. */
    color: var(--toolbar-field-focus-color);
    opacity: 0.6;
    pointer-events: none;
  }

  /* For tips with row labels, the entire row is moved down by an additional
     var(--urlbarView-labeled-tip-margin-top-extra) so there's more space
     between the tip's top border and the label; see the tip rules. Here we
     compensate so that the label remains the same distance from the previous
     row as it would have had we not moved the tip row down. +1px compensates
     for the tip's top 1px border. */
  &[type=tip]::before {
    top: calc(var(--urlbarView-labeled-row-label-top) - var(--urlbarView-labeled-tip-margin-top-extra) + 1px);
  }

  :root[lwt-toolbar-field-focus="dark"] &::before {
    /* Same as `.search-panel-header > label` in searchbar.css */
    opacity: 1;
  }
}

/* Feedback acknowledgment */

.urlbarView-row[feedback-acknowledgment] {
  position: relative;
  padding-bottom: 1.94em;

  &::after {
    content: attr(feedback-acknowledgment);
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0.72em;
    font-size: var(--urlbarView-small-font-size);
    margin-inline-start: calc(var(--urlbarView-item-inline-padding) + var(--urlbarView-favicon-margin-start));
    padding-inline-start: calc(var(--urlbarView-favicon-width) + var(--urlbarView-favicon-margin-end));
    background-image: url("chrome://branding/content/icon32.png");
    background-repeat: no-repeat;
    background-position: 0 center;
    background-size: var(--urlbarView-favicon-width);
    min-height: var(--urlbarView-favicon-width);
  }
}

/* Title */

.urlbarView-title {
  white-space: nowrap;
  /* Explicitly set line-height to avoid excessively tall rows if the title triggers use of
     fallback fonts with extreme metrics. */
  line-height: 1.4;
}

/* user context box */
.urlbarView-userContext {
  border-top: 4px solid var(--identity-tab-color);
}

.urlbarView-userContext-icon {
  height: 14px;
  width: 14px;
  -moz-context-properties: fill;
  vertical-align: top;
  margin-inline-start: 0.35em;
}

.urlbarView-userContext-textMode > span {
  font-variant: small-caps;
}

/* Display userContext icon instead of text, when window is too narrow. */
.urlbarView-results[wrap] .urlbarView-userContext-textMode,
.urlbarView-results:not([wrap]) .urlbarView-userContext-iconMode {
  display: none;
}

/* Tail suggestions */

.urlbarView-tail-prefix {
  display: none;
  justify-content: flex-end;
  white-space: pre;

  .urlbarView-row[tail-suggestion] > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    display: inline-flex;
  }

  > .urlbarView-tail-prefix-string {
    visibility: hidden;
  }

  > .urlbarView-tail-prefix-char {
    position: absolute;
  }
}

/* Title separator */

.urlbarView-title-separator {
  &::before {
    content: "\2014";
    margin: 0 .4em;
    opacity: 0.6;
  }

  .urlbarView-title:empty + .urlbarView-tags:empty + &,
  /* This targets both rich and non-rich results, so not using the child selector here. */
  .urlbarView-row:is([type=search], [restyled-search]):not([selected], [show-action-text], :hover) &,
  .urlbarView-row:not([has-action], [has-url], [restyled-search]) & {
    display: none;
  }
}

/* Action labels */

.urlbarView-action {
  .urlbarView-title:not(:empty) ~ & {
    font-size: var(--urlbarView-small-font-size);

    /* This targets both rich and non-rich results, so not using the child selector here. */
    .urlbarView-row:not(:hover, [selected], [sponsored]) & {
      color: var(--urlbarView-action-color);
    }

    &[slide-in] {
      @media (prefers-reduced-motion: no-preference) {
        animation-name: urlbarView-action-slide-in;
        animation-duration: 350ms;
        animation-timing-function: var(--animation-easing-function);
      }
    }
  }

  .urlbarView-row[sponsored]:not([selected]) > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    opacity: .6;
  }

  .urlbarView-row:not([has-action], [has-url], [restyled-search]) > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    display: none;
  }

  /* If action visibility can be toggled the height should always stay the same. */
  .urlbarView-row[has-url]:is([type=remotetab], [sponsored]):is([selected], :hover) > .urlbarView-row-inner > .urlbarView-no-wrap > &,
  /* This targets both rich and non-rich results, so not using the child selector here. */
  .urlbarView-row:is([type=search], [restyled-search]):not([selected], [show-action-text], :hover) &,
  .urlbarView[action-override] .urlbarView-row[type=switchtab] > .urlbarView-row-inner > .urlbarView-no-wrap > & {
    visibility: hidden;
    width: 0;
    padding-inline: 0;
    border-inline: 0;
  }
}

/* The slide-in effect is delayed ~100ms to reduce motion during result
   composition. We set opacity: 0 at the 0% keyframe to ensure the text is not
   seen by the user until after the delay. */
@keyframes urlbarView-action-slide-in {
  0%, 28.6% {
    translate: var(--urlbarView-action-slide-in-distance);
    opacity: 0;
  }
  100% {
    translate: 0;
    opacity: 1;
  }
}

/* Switch-to-tab and Clipboard action text is styled as a chiclet. */
.urlbarView-row[has-action]:is([type=switchtab], [type=remotetab], [type=clipboard]) {
  > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-action {
    border-radius: var(--toolbarbutton-border-radius);
    padding: 4px 8px;
    margin-block: -2px;
    margin-inline-start: 8px;

    :root[uidensity=compact] & {
      padding-block: 3px;

      &.urlbarView-userContext {
        border-block-start-width: 3px;
      }
    }

    /* stylelint-disable-next-line media-query-no-invalid */
    @media not (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride") {
      color: var(--urlbar-box-text-color);
      background-color: var(--urlbar-box-focus-bgcolor);

      &.urlbarView-userContext {
        padding-top: 0;
      }
    }

    /* stylelint-disable-next-line media-query-no-invalid */
    @media (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride") {
      -moz-context-properties: fill, fill-opacity;
      color: var(--toolbar-field-focus-color);
      padding-inline-start: calc(var(--urlbarView-favicon-width) + 8px);
      background-image: url("chrome://browser/content/firefoxview/view-opentabs.svg");
      background-repeat: no-repeat;
      background-position: 4px center;
      background-size: var(--urlbarView-favicon-width);

      border-inline: 1px solid var(--urlbarView-separator-color);
      border-block-end: 1px solid var(--urlbarView-separator-color);
      &:not(.urlbarView-userContext) {
        border-block-start: 1px solid var(--urlbarView-separator-color);
      }

      &:-moz-locale-dir(rtl) {
        background-position-x: right 4px;
      }
    }
  }

  /* stylelint-disable-next-line media-query-no-invalid */
  @media not (-moz-bool-pref: "browser.urlbar.scotchBonnet.enableOverride") {
    &:is([selected], :hover) > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-action {
      color: var(--urlbarView-result-button-selected-color);
      background-color: var(--urlbarView-result-button-selected-background-color);
    }
  }
}

.urlbarView:not([action-override]) .urlbarView-row[type=switchtab],
.urlbarView-row[type=remotetab]:not([selected], :hover),
.urlbarView-row[type=clipboard] {
  > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-title-separator::before {
    /* We make the title separator transparent so it stays in the accessibility
       tree. We want screen readers to pause between the result title and the
       switch-to-tab action text. */
    opacity: 0;
    /* -1em for the width of the \2014 character. -.4em to offset the
       margin-inline-start already set on this element. */
    margin-inline-end: -1.4em;
  }
}

/* URLs */

.urlbarView-url {
  flex-grow: 1;
  flex-shrink: 1;
  font-size: var(--urlbarView-small-font-size);
  /* Increase line-height to avoid cutting overhanging glyphs due to masking on
     the element */
  line-height: 1.4;

  /* This targets both rich and non-rich results, so not using the child selector here. */
  .urlbarView-row:not([selected]) & {
    color: var(--link-color);
  }

  .urlbarView-row:is([type=remotetab], [sponsored]):not([selected], :hover) > .urlbarView-row-inner > &,
  .urlbarView-row:is([show-action-text], [restyled-search]) > .urlbarView-row-inner > &,
  .urlbarView:not([action-override]) .urlbarView-row[type=switchtab] > .urlbarView-row-inner > & {
    /* Use visibility:collapse instead of display:none since the latter can
       confuse the overflow state of these elements when their content
       changes while they're hidden (bug 1549787). */
    visibility: collapse;
  }
}

/* Tags */

.urlbarView-tags {
  display: flex;
  font-size: var(--urlbarView-small-font-size);
  /* Increase line-height to avoid cutting overhanging glyphs due to masking on
     the element */
  line-height: 1.4;
}

.urlbarView-tag {
  border: 1px solid color-mix(in srgb, currentColor 30%, transparent);
  padding: 0 4px;
  margin-inline-start: .4em;
  border-radius: 4px;
}

/* Rich suggestions */

.urlbarView-row[rich-suggestion] > .urlbarView-row-inner {
  align-items: center;
}

.urlbarView-row[rich-suggestion] > .urlbarView-row-inner > .urlbarView-favicon {
  width: var(--urlbarView-rich-suggestion-default-icon-size);
  height: var(--urlbarView-rich-suggestion-default-icon-size);
  flex-basis: var(--urlbarView-rich-suggestion-default-icon-size);
  flex-shrink: 0;
  flex-grow: 0;

  &[icon-size="16"] {
    width: 16px;
    height: 16px;
    flex-basis: 16px;
  }

  &[icon-size="24"] {
    width: 24px;
    height: 24px;
    flex-basis: 24px;
  }

  &[icon-size="38"] {
    width: 38px;
    height: 38px;
    flex-basis: 38px;
  }

  &[icon-size="52"] {
    width: 52px;
    height: 52px;
    flex-basis: 52px;
    border-radius: 2px;
  }
}

.urlbarView-row[rich-suggestion] > .urlbarView-row-inner > .urlbarView-row-body {
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;

  > .urlbarView-row-body-top {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    align-items: baseline;

    > .urlbarView-row-body-top-no-wrap {
      display: flex;
      flex-wrap: nowrap;
      flex-direction: row;
      align-items: baseline;
      flex-shrink: 0;
      min-width: 0;

      .urlbarView-results:not([wrap]) > & {
        /* Limit the title (which is inside .urlbarView-row-body-top-no-wrap) to 70%
           of the width so the URL is never completely hidden. */
        max-width: 70%;
      }
    }
  }

  > .urlbarView-row-body-description {
    max-width: 80ch;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow-y: hidden;

    &:not(:empty) + .urlbarView-row-body-bottom:not(:empty) {
      margin-block-start: 1em;
    }
  }

  > :is(.urlbarView-row-body-description, .urlbarView-row-body-bottom) {
    font-size: var(--urlbarView-small-font-size);
    white-space: normal;
  }
}

.urlbarView-row[rich-suggestion]:not([selected]) > .urlbarView-row-inner > .urlbarView-row-body > :is(.urlbarView-row-body-description, .urlbarView-row-body-bottom) {
  color: var(--urlbarView-secondary-text-color);
}

/* Sponsored Firefox Suggest rows */

.urlbarView-row[type$=_adm_sponsored][icon-size="16"] > .urlbarView-row-inner {
  /* Keep the status quo before these rows were rich suggestions, where the row
     height is roughly the same as usual rows. */
  padding-block: 0;
}

/* Other Firefox Suggest rows */

.urlbarView-row[type$=_pocket] > .urlbarView-row-inner > .urlbarView-favicon,
.urlbarView-row[type$=_pocket] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon {
  fill: var(--pocket-icon-fill);
  fill-opacity: 1;
}

.urlbarView-row:is([type$=_amo], [type$=_pocket])[icon-size="24"] > .urlbarView-row-inner > .urlbarView-favicon {
  padding: calc((52px - 24px) / 2);
  background-color: var(--urlbar-box-focus-bgcolor);
  border-radius: 2px;
}

.urlbarView-row:is([type$=_amo], [type$=_pocket])[icon-size="24"]:is([selected], :hover) > .urlbarView-row-inner > .urlbarView-favicon {
  background-color: var(--urlbarView-result-button-selected-background-color);
}

.urlbarView-row[dynamicType=weather],
.urlbarView-row[type=weather] {
  /* Use the colors in the icon SVG files except in HCM and when the row is
     selected. Note that the SVG uses light or dark colors as appropriate. */
  &:not([selected]) > .urlbarView-row-inner > .urlbarView-favicon {
    @media not (prefers-contrast) {
      -moz-context-properties: unset;
    }
  }

  /* Map AccuWeather icon IDs to our icons. These IDs come straight from the
     AccuWeather API response via Merino. These rules apply to both the older
     weather UI treatments ("simpler" and "full", which use a dynamic result)
     and the new treatment ("simplest", which is a standard rich suggestion). */
  > .urlbarView-row-inner > .urlbarView-dynamic-weather-currentConditions > .urlbarView-dynamic-weather-currentTemperature > .urlbarView-dynamic-weather-weatherIcon,
  > .urlbarView-row-inner > .urlbarView-favicon {
    /* icon-variation="1" is "Sunny", which we'll use as the default to ensure an
       image is always visible. We don't expect AccuWeather to ever include an
       invalid icon ID, but they may add new ones that aren't recognized by this
       version of Firefox for example. */
    content: url("chrome://browser/skin/weather/sunny.svg");
    &[icon-variation="2"] {
      content: url("chrome://browser/skin/weather/mostly-sunny.svg");
    }
    &:is([icon-variation="3"], [icon-variation="4"], [icon-variation="6"]) {
      content: url("chrome://browser/skin/weather/partly-sunny.svg");
    }
    &[icon-variation="5"] {
      content: url("chrome://browser/skin/weather/hazy-sunshine.svg");
    }
    &:is([icon-variation="7"], [icon-variation="8"]) {
      content: url("chrome://browser/skin/weather/cloudy.svg");
    }
    &[icon-variation="11"] {
      content: url("chrome://browser/skin/weather/fog.svg");
    }
    &[icon-variation="12"] {
      content: url("chrome://browser/skin/weather/showers.svg");
    }
    &:is([icon-variation="13"], [icon-variation="14"]) {
      content: url("chrome://browser/skin/weather/mostly-cloudy-with-showers.svg");
    }
    &[icon-variation="15"] {
      content: url("chrome://browser/skin/weather/thunderstorms.svg");
    }
    &:is([icon-variation="16"], [icon-variation="17"]) {
      content: url("chrome://browser/skin/weather/mostly-cloudy-with-thunderstorms.svg");
    }
    &[icon-variation="18"] {
      content: url("chrome://browser/skin/weather/rain.svg");
    }
    &:is([icon-variation="19"], [icon-variation="20"], [icon-variation="25"]) {
      content: url("chrome://browser/skin/weather/flurries.svg");
    }
    &[icon-variation="21"] {
      content: url("chrome://browser/skin/weather/partly-sunny-with-flurries.svg");
    }
    &:is([icon-variation="22"], [icon-variation="23"]) {
      content: url("chrome://browser/skin/weather/snow.svg");
    }
    &:is([icon-variation="24"], [icon-variation="31"]) {
      content: url("chrome://browser/skin/weather/ice.svg");
    }
    &:is([icon-variation="26"], [icon-variation="29"]) {
      content: url("chrome://browser/skin/weather/freezing-rain.svg");
    }
    &[icon-variation="30"] {
      content: url("chrome://browser/skin/weather/hot.svg");
    }
    &[icon-variation="32"] {
      content: url("chrome://browser/skin/weather/windy.svg");
    }
    &[icon-variation="33"] {
      content: url("chrome://browser/skin/weather/night-clear.svg");
    }
    &:is([icon-variation="34"], [icon-variation="35"], [icon-variation="36"], [icon-variation="38"]) {
      content: url("chrome://browser/skin/weather/night-mostly-clear.svg");
    }
    &[icon-variation="37"] {
      content: url("chrome://browser/skin/weather/night-hazy-moonlight.svg");
    }
    &:is([icon-variation="39"], [icon-variation="40"]) {
      content: url("chrome://browser/skin/weather/night-partly-cloudy-with-showers.svg");
    }
    &:is([icon-variation="41"], [icon-variation="42"]) {
      content: url("chrome://browser/skin/weather/night-partly-cloudy-with-thunderstorms.svg");
    }
    &:is([icon-variation="43"], [icon-variation="44"]) {
      content: url("chrome://browser/skin/weather/night-mostly-cloudy-with-flurries.svg");
    }
  }
}

/* Actions */
.urlbarView-results[actionmode] {
  white-space: wrap;
}

.urlbarView-row[dynamicType="actions"] {
  display: inline-flex;
}

/* To align the label and action button */
.urlbarView-row[secondary-action] {
  flex-direction: column;
  align-items: flex-start;
}

.urlbarView-actions-container {
  margin-inline-start: calc(var(--urlbarView-item-inline-padding) + var(--urlbarView-favicon-margin-start) + var(--urlbarView-favicon-width) + var(--urlbarView-icon-margin-end));
  margin-block-end: var(--urlbarView-item-block-padding);
}

.urlbarView-action-btn {
  font-size: smaller;
  font-weight: var(--font-weight-bold);
  border-radius: var(--toolbarbutton-border-radius);
  border: 1px solid transparent;
  padding: .4em .6em;
  display: inline-flex;
  align-items: center;
  background-color: var(--urlbarView-action-button-background-color);
  margin-inline-end: var(--space-large);

  > img {
    width: 16px;
    height: 16px;
    margin-inline-end: .4em;
    -moz-context-properties: fill, fill-opacity;
  }

  &:hover {
    background-color: var(--urlbarView-action-button-hover-background-color);
  }

  &[selected],
  &:hover:active {
    outline: var(--focus-outline);
  }

  &.urlbarView-userContext {
    border-top-color: var(--identity-tab-color);
  }

  .urlbarView[action-override] &[data-action=tabswitch] {
    display: none;
  }

  &[data-action^=tabgroup-] {
    color: light-dark(var(--tab-group-color-pale), var(--tab-group-label-text-dark));
    background-color: light-dark(var(--tab-group-color), var(--tab-group-color-invert));

    /* Add offset to the focus / selection indicator as the outline color could
       be close to the group color. */
    outline-offset: 1px;
    margin-inline-start: 1px;

    &:hover {
      opacity: 0.8;
    }
  }
}

/* Search one-offs */

#urlbar .search-one-offs:not([hidden]) {
  display: flex;
  align-items: start;
  padding-block: 10px;
  flex-wrap: wrap;
}

.urlbarView:not([noresults]) > .search-one-offs:not([hidden]) {
  border-top: 1px solid var(--urlbarView-separator-color);
}

:root[uidensity=touch] #urlbar .search-one-offs:not([hidden]) {
  padding-block: 15px;
}

#urlbar .search-panel-one-offs-container {
  /* Make sure horizontally we can fit four buttons, empty space, settings. */
  min-width: calc(4 * /* one-off with end margin */ 40px + /* settings with start margin */ 56px);
}

#urlbar .search-panel-header {
  padding: 0;
  min-height: 28px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#urlbar .search-panel-one-offs-header-label {
  white-space: nowrap;
  margin: 0;
  padding-inline: var(--urlbarView-item-inline-padding) 18px;
}

#urlbar .searchbar-engine-one-off-item {
  min-width: 28px;
  height: 28px;
  margin-inline: 0 12px;
  border-radius: var(--toolbarbutton-border-radius);
}

#urlbar .searchbar-engine-one-off-item:last-child {
  /* This applies to both the last one-off and the compact settings button */
  margin-inline-end: 0;
}

#urlbar .search-setting-button {
  /* Force empty space before the button */
  margin-inline-start: calc(32px - /* settings start padding */ 8px );
}

.urlbarView-row[source="bookmarks"] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon,
#urlbar-engine-one-off-item-bookmarks {
  list-style-image: url("chrome://browser/skin/bookmark.svg");
  fill: var(--toolbarbutton-icon-fill-attention);
  fill-opacity: 1;
  -moz-context-properties: fill, fill-opacity;
}

.urlbarView-row[source="tabs"] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon,
#urlbar-engine-one-off-item-tabs {
  list-style-image: url("chrome://browser/skin/tab.svg");
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
}

.urlbarView-row[source="history"] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon,
#urlbar-engine-one-off-item-history {
  list-style-image: url("chrome://browser/skin/history.svg");
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
}

.urlbarView-row[source="actions"] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon,
#urlbar-engine-one-off-item-actions {
  list-style-image: url("chrome://browser/skin/quickactions.svg");
  -moz-context-properties: fill, fill-opacity;
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
}

/**
 * We remove the blue fill from the bookmark icon when it is selected. We use
 * a blue color as the selection background-color in some instances (Linux with
 * blue system color; fallback for third-party themes) and we want to ensure
 * contrast.
 */
.urlbarView-row[source="bookmarks"][selected] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-favicon,
#urlbar-engine-one-off-item-bookmarks[selected] {
  fill: currentColor;
  fill-opacity: var(--urlbar-icon-fill-opacity);
}

/* search bar popup */

#PopupSearchAutoComplete {
  --panel-color: var(--toolbar-field-focus-color);
  --panel-background: var(--toolbar-field-focus-background-color);
  --panel-border-color: var(--arrowpanel-border-color);
}

#PopupSearchAutoComplete::part(content)  {
  --panel-padding: var(--panel-subview-body-padding);
  /* NOTE(emilio): Once bug 1551637 is fixed we don't need to use block layout
   * for this (though it doesn't really hurt) */
  display: block;
}

@media not (prefers-contrast) {
  #PopupSearchAutoComplete::part(content)  {
    /* Remove the top border since the panel is flush with the input. */
   border-top-width: 0;
  }
}

#PopupSearchAutoComplete .autocomplete-richlistitem[selected] {
  background: var(--urlbarView-highlight-background);
  color: var(--urlbarView-highlight-color);
}
