/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#editBookmarkPanelContent {
  display: grid;
  grid-template-columns: auto;
}

#editBMPanel_folderTree,
#editBMPanel_tagsSelector {
  margin: 0;
  height: 12.5em;
  flex: 1 auto;
  border: 1px solid ThreeDShadow;
  border-radius: 4px;
}

#editBMPanel_folderTree {
  appearance: none;
  overflow: hidden;
}

#editBMPanel_folderMenuList::part(icon) {
  width: 16px;
  height: 16px;
}

@media (-moz-platform: macos) {
  /* FIXME(emilio): Is this rule needed at all? */
  .folder-icon > .menu-iconic-left > .menu-iconic-icon {
    width: 16px;
    height: 16px;
  }
}

.folder-icon > .menu-iconic-left {
  display: flex;
}

.folder-icon {
  list-style-image: url("chrome://global/skin/icons/folder.svg") !important;
  -moz-context-properties: fill;
  fill: currentColor;
}

#editBMPanel_tagsSelector > richlistitem > image {
  appearance: auto;
  -moz-default-appearance: checkbox;
  align-items: center;
  margin: 0 2px;
  min-width: 13px;
  min-height: 13px;
}

@media (-moz-platform: windows) {
  #editBMPanel_tagsSelector > richlistitem > image {
    border: 1px solid -moz-DialogText;
    background: Field no-repeat 50% 50%;
  }
}

#bookmarkpropertiesdialog #editBMPanel_tagsSelector {
  border-color: var(--in-content-border-color);
  padding: 12px;
}

/* Reset default margins for tags so we can apply custom ones */
#bookmarkpropertiesdialog #editBMPanel_tagsSelector > richlistitem > image,
#bookmarkpropertiesdialog #editBMPanel_tagsSelector > richlistitem > label {
  margin: 0;
}

/* Set spacing between tags */
#bookmarkpropertiesdialog #editBMPanel_tagsSelector > richlistitem:not(:last-child) {
  margin-bottom: 8px;
}

#bookmarkpropertiesdialog #editBMPanel_tagsSelector > richlistitem > image {
  margin-inline-end: 8px;
}

/* Bookmark panel dropdown icons */

#editBMPanel_folderMenuList[selectedGuid="toolbar_____"],
#editBMPanel_toolbarFolderItem {
  list-style-image: url("chrome://browser/skin/places/bookmarksToolbar.svg") !important;
}

#editBMPanel_folderMenuList[selectedGuid="menu________"],
#editBMPanel_bmRootItem {
  list-style-image: url("chrome://browser/skin/places/bookmarksMenu.svg") !important;
}

#bookmarkpropertiesdialog #editBookmarkPanelContent > label:not(.editBMPanel_nameRow),
#bookmarkpropertiesdialog #editBookmarkPanelContent > #editBMPanel_folderTreeRow,
#bookmarkpropertiesdialog #editBookmarkPanelContent > #editBMPanel_tagsSelectorRow {
  padding-block: var(--editbookmarkdialog-padding) 0;
}

#bookmarkpropertiesdialog #editBookmarkPanelContent label[control] {
  margin-inline-start: 0;
  margin-block: 0 4px;
}

#bookmarkpropertiesdialog #editBookmarkPanelContent {
  padding: calc(var(--editbookmarkdialog-padding) / 2) var(--editbookmarkdialog-padding) var(--editbookmarkdialog-padding);
}

#bookmarkpropertiesdialog .expander-up,
#bookmarkpropertiesdialog .expander-down {
  appearance: none;
  -moz-context-properties: fill;
  fill: currentColor;
  margin: 0;
  min-width: 32px;
  min-height: 32px;
  padding: 0;
}

#bookmarkpropertiesdialog .expander-up {
  list-style-image: url("chrome://global/skin/icons/arrow-up.svg");
}

#bookmarkpropertiesdialog .expander-down {
  list-style-image: url("chrome://global/skin/icons/arrow-down.svg");
}

#bookmarkpropertiesdialog #editBookmarkPanelContent > input,
#bookmarkpropertiesdialog #editBookmarkPanelContent > hbox > input,
#bookmarkpropertiesdialog #editBMPanel_folderMenuList {
  margin: 0;
}

/* dialog-specific #editBookmarkPanel styles */

#bookmarkpropertiesdialog #editBookmarkPanelContent {
  padding-inline: 0;
  margin-inline: 4px;
}

#bookmarkpropertiesdialog #editBookmarkPanelContent,
#bookmarkpropertiesdialog vbox#editBMPanel_nameRow {
  padding-top: 0;
}

#bookmarkpropertiesdialog #editBookmarkPanelContent #editBMPanel_tagsField,
#bookmarkpropertiesdialog #editBookmarkPanelContent #editBMPanel_folderMenuList {
  margin-inline-end: 16px;
}

#bookmarkpropertiesdialog .caption-label {
  margin: 4px;
  color: var(--text-color-deemphasized);
}

.caption-label {
  font-size: 0.9em;
  margin: 2px 4px;
}
