/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

#editBookmarkPanel > .panel-subview-body {
  padding-bottom: 0;
}

#editBMPanel_newFolderButton {
  appearance: none;
  margin: 0;
  border: 0;
  border-radius: 4px;
  background-color: var(--button-background-color);
  color: var(--button-text-color);
  font-weight: 600;
  min-width: 0;
  padding: 8px 16px;

  /* This button is deeper in the visual hierarchy than others (notably the
     buttons at the bottom of the panel), so it should be slightly smaller. */
  font-size: 90%;

  &:hover {
    background-color: var(--button-background-color-hover);
  }

  &:hover:active {
    background-color: var(--button-background-color-active);
  }
}

#editBookmarkPanel > #editBookmarkHeaderSeparator {
  margin-bottom: 0;
  margin-inline: 16px;
}

#editBookmarkPanel {
  font: caption;
}

#editBookmarkPanelContent > label:not(.editBMPanel_nameRow) {
  padding-top: var(--arrowpanel-padding);
}

#editBookmarkPanelContent > #editBMPanel_folderTreeRow,
#editBookmarkPanelContent > #editBMPanel_folderTreeRow > #editBMPanel_newFolderBox,
#editBookmarkPanelContent > #editBMPanel_tagsSelectorRow {
  padding-top: 4px;
}

#editBookmarkPanelContent label[control] {
  margin: 0;
  margin-bottom: 4px;
}

#editBookmarkPanel .caption-label {
  display: none;
}

#editBookmarkPanelContent {
  padding: 0.8em var(--arrowpanel-padding) var(--arrowpanel-padding);
}

#editBMPanel_selectionCount {
  padding-top: 0.8em;
}

#editBookmarkPanelBottomContent {
  padding: 0 var(--arrowpanel-padding) calc(var(--arrowpanel-padding) / 2);
}

#editBookmarkPanelBottomContent > checkbox {
  margin-inline-start: 0;
}

#editBookmarkPanel_showForNewBookmarks > .checkbox-label-box > .checkbox-label {
  opacity: 0.7;
}

#editBookmarkPanel .expander-up > .button-box > .button-text,
#editBookmarkPanel .expander-down > .button-box > .button-text {
  display: none;
}

#editBookmarkPanel .expander-up,
#editBookmarkPanel .expander-down {
  appearance: none; /* override button.css */
  -moz-context-properties: fill;
  fill: currentColor;
  margin: 0;
  margin-inline-start: 4px;
  min-width: 32px;
  min-height: 32px;
  color: var(--button-text-color);
  background-color: var(--button-background-color);
  border-radius: 4px;

  &:hover {
    background-color: var(--button-background-color-hover);
  }

  &:hover:active {
    background-color: var(--button-background-color-hover);
  }
}

#editBookmarkPanel .expander-up {
  list-style-image: url("chrome://global/skin/icons/arrow-up.svg");
}

#editBookmarkPanel .expander-down {
  list-style-image: url("chrome://global/skin/icons/arrow-down.svg");
}

#editBMPanel_folderMenuList {
  margin: 0;
}

/* Focus outlines */

#editBookmarkPanel .expander-up:focus-visible,
#editBookmarkPanel .expander-down:focus-visible,
#editBMPanel_newFolderButton:focus-visible {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-offset);
}

#editBMPanel_folderTree:focus-visible,
#editBMPanel_tagsSelector:focus-visible,
#editBookmarkPanelContent > html|input:focus-visible,
#editBookmarkPanelContent > hbox > html|input:focus-visible {
  border-color: transparent;
  outline: var(--focus-outline);
  outline-offset: -1px;
}
