/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace html url("http://www.w3.org/1999/xhtml");

/* General popup rules */

#PopupAutoComplete > richlistbox {
  padding: 0;
}

/* Define the minimum width based on the style of result rows.
   The order of the min-width rules below must be in increasing order. */
#PopupAutoComplete:is([resultstyles~="loginsFooter"], [resultstyles~="insecureWarning"])::part(content) {
  min-width: 22em;
}

#PopupAutoComplete:is([resultstyles~="importableLogins"], [resultstyles~="generatedPassword"])::part(content) {
  min-width: 22em;
}

#PopupAutoComplete > richlistbox > richlistitem {
  min-height: 20px;
  border: 0;
  border-radius: 0;
  padding: 0 1px;

  --status-text-color: currentColor;
  --status-background-color: rgba(248, 232, 28, .2);

  > .ac-site-icon {
    margin-inline: 4px 0;
  }

  > .ac-login-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    > .ac-secondary-action {
      visibility: hidden;
      height: 16px;
      width: 16px;
      border: 0;
      color: inherit;
      -moz-context-properties: fill;
      fill: currentColor;
      margin-inline: 8px;
      cursor: pointer;
      background: url("chrome://global/skin/icons/settings.svg") center no-repeat;
    }
  }

  &:is(:hover, [selected]) > .ac-login-item > .ac-secondary-action {
    visibility: visible;
  }

  > .ac-title {
    font: icon;
    margin-inline-start: 4px;
  }

  &[originaltype="importableLearnMore"] {
    padding-bottom: 2px;
    padding-inline-start: 21px;
  }

  &[originaltype="loginsFooter"] {
    background-color: transparent;
    color: currentColor;
    min-height: 2.6666em;
    position: relative;

    &:hover,
    &[selected] {
      background-color: hsla(0,0%,80%,.5); /* match arrowpanel-dimmed-further */
    }
  }

  &[originaltype="loginsFooter"]::before {
    content: "";
    position: absolute;
    top: 0;
    inset-inline-start: 4px;
    width: calc(100% - 8px);
    border-top: 1px solid var(--panel-separator-color);
  }

  &[originaltype="insecureWarning"] {
    height: auto;

    & > .ac-title > .ac-text-overflow-container > .ac-title-text {
      text-overflow: initial;
      white-space: initial;
    }

    & > .ac-title > label {
      margin-inline-start: 0;
    }
  }

  &[originaltype="loginWithOrigin"] > .ac-site-icon,
  &[originaltype="insecureWarning"] > .ac-site-icon {
    margin-inline-start: 0;
    display: initial;
  }

  &[originaltype="login"] > .ac-site-icon,
  &[originaltype="possible-username"] > .ac-site-icon,
  > .two-line-wrapper > .ac-site-icon {
    display: block;
    margin-inline: 4px;
  }

  &[originaltype="possible-username"] > .ac-site-icon {
    visibility: hidden;
  }

  /* Autocomplete richlistitem support for a two-line label display */

  > .two-line-wrapper {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 8px;
    margin: 0;
    min-width: 0;

    > .ac-site-icon {
      margin-left: 6px;
    }

    > .ac-site-icon[src=""] {
      display: none;
    }

    > .labels-wrapper {
      /* The text should flex while the icon should not */
      flex: 1;
      /* min-width is needed to get the text-overflow: ellipsis to work for the children */
      min-width: 0;

      > .label-row {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .line2-label {
        padding-top: 2px !important;
        color: var(--text-color-deemphasized);
      }
    }
  }

  &[originaltype="generatedPassword"] > .two-line-wrapper > .labels-wrapper > .generated-password-autosave,
  &[originaltype="importableLearnMore"] > .ac-title,
  > .two-line-wrapper > .labels-wrapper > .line2-label {
    padding-top: 2px !important;
    color: var(--text-color-deemphasized);
  }

  /* Login form autocompletion (with and without origin showing) and generated passwords */
  &[originaltype="generatedPassword"] > .two-line-wrapper > .ac-site-icon,
  &[originaltype="loginWithOrigin"] > .two-line-wrapper > .ac-site-icon,
  &[originaltype="login"] > .ac-site-icon {
    fill: GrayText;
  }

  &[originaltype="importableLogins"] > .two-line-wrapper > .ac-site-icon {
    fill: GrayText;
    list-style-image: url(chrome://browser/skin/import.svg);
  }

  &[originaltype="generatedPassword"][selected] > .two-line-wrapper > .ac-site-icon,
  &[originaltype="importableLogins"][selected] > .two-line-wrapper > .ac-site-icon,
  &[originaltype="loginWithOrigin"][selected] > .two-line-wrapper > .ac-site-icon,
  &[originaltype="login"] > .ac-site-icon[selected] {
    fill: SelectedItemText;
  }

    /* Login form autocompletion */
    > .two-line-wrapper {
      padding: 4px;
      padding-inline-start: 6px;
    }

  &[originaltype="action"] > .two-line-wrapper {
    flex: 1;
  }

  &[originaltype="generatedPassword"] {
    &:not([collapsed="true"]) {
      /* Workaround bug 451997 and/or bug 492645 */
      display: block;
    }

    > .two-line-wrapper > .labels-wrapper {
      &.line2-label > span {
        /* The font-family is only adjusted on the inner span so that the
           line-height of .line2-label matches that of .line1-label */
        font-family: monospace;
      }

      > .generated-password-autosave > span {
        /* The font-* properties are only adjusted on the inner span so that the
           line-height of .generated-password-autosave matches that of .line1-label */
        font-style: italic;
        font-size: 0.85em;
        white-space: normal;
      }
    }
  }

  &[originaltype="login"] + richlistitem[originaltype="generatedPassword"],
  &[originaltype="loginWithOrigin"] + richlistitem[originaltype="generatedPassword"] {
    /* Separator between logins and generated passwords. This uses --panel-separator-color from default
     * themes since autocomplete doesn't yet switch to dark. */
    border-top: 1px solid hsla(210,4%,10%,.14);
  }

  &[originaltype="action"] {
    text-align: center;
  }

  /* status items */
  > .ac-status {
    padding: var(--space-xsmall) var(--space-small);
    text-align: center;
    background-color: var(--status-background-color);
    color: var(--status-text-color);
    width: 100%;
    border-bottom: 1px solid var(--panel-separator-color);
    font-size: calc(10 / 12 * 1em);
  }

  &:has(> .ac-status) {
    opacity: 1;
  }

  &[originaltype="autofill"][ac-image]:not([ac-image=""]) > .two-line-wrapper {
    display: grid;
    grid-template-columns: 32px 1fr;

    > .ac-site-icon {
      width: auto;
      height: 16px;
      max-width: 32px;
      max-height: 16px;
    }
  }

  /* Insecure field warning */
  &[originaltype="insecureWarning"] {
    background-color: var(--arrowpanel-dimmed);
    color: inherit;
    border-bottom: 1px solid var(--panel-separator-color);
    padding-block: 4px;

    &[selected] {
      background-color: var(--arrowpanel-dimmed-further);
      color: inherit;
    }

    > .ac-title {
      font-size: 1em;
    }

    > .ac-title .ac-emphasize-text-title {
      font-weight: 600;
    }

    > .ac-site-icon {
      list-style-image: url(chrome://global/skin/icons/security-broken.svg);
    }
  }
}

/* Popup states */
.autocomplete-richlistitem {
  &:not([disabled="true"]):not([selected]):hover {
    background-color: var(--arrowpanel-dimmed);
  }

  &[selected] {
    background-color: SelectedItem;
    color: SelectedItemText;
  }
}
