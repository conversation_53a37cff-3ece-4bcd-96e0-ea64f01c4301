/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@namespace url("http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul");
@namespace html url("http://www.w3.org/1999/xhtml");

html|canvas#screen {
  border-style: solid;
  border-width: 12px 11px;
  border-image: url("chrome://browser/skin/monitor-border.png") 12 11 stretch;
}

#monitor-base {
  list-style-image: url("chrome://browser/skin/monitor-base.png");
}

html|p#preview-unavailable {
  margin: 12px 11px;
  text-align: center;
  color: #9B2423;
  font-weight: bold;
}
