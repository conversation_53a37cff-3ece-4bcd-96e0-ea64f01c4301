/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://browser/skin/tab-list-tree.css");

#tabsToggle {
  cursor: pointer;
  /* Override button background-color and color from common.css */
  background-color: transparent;
  color: var(--link-color);
  -moz-context-properties: fill;
  fill: currentColor;
  border: none;
  padding: 0;
  margin: 0;
  padding-inline-end: 45px;
  position: relative;
}

#tabsToggle::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  background-image: url("chrome://global/skin/icons/arrow-down.svg");
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: right center;
}

#tabsToggle:dir(rtl)::after {
  background-position-x: left;
}

#tabsToggle:not(.tabs-hidden)::after {
  background-image: url("chrome://global/skin/icons/arrow-up.svg");
}

#tabsToggle.tabs-hidden > #hideTabs,
#tabsToggle:not(.tabs-hidden) > #showTabs {
  display: none;
}

#tabList {
  flex: 1;
  min-height: 12em;
  margin-top: 1.2em;
}

.button-container {
  text-align: end;
}
