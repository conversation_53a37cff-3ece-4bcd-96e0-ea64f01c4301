/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/**
 * Firefox Suggest contextual opt-in
 */

.urlbarView-row[dynamicType=quickSuggestContextualOptIn] {
  background-color: color-mix(in srgb, currentColor 3%, transparent);

  > .urlbarView-row-inner {
    align-items: center;

    > .urlbarView-no-wrap {
      align-items: center;

      > .urlbarView-favicon {
        background-color: color-mix(in srgb, currentColor 3%, transparent);
        min-width: 32px;
        height: 32px;
        padding: 16px;
        margin-inline: 0 .7em;
      }
    }
  }

  .urlbarView-results[wrap] > &,
  .search-one-offs[wrap] > .urlbarView-quickSuggestContextualOptIn-one-off-container > & {
    display: block;
    text-align: end;
    white-space: normal;

    > .urlbarView-row-inner {
      display: block;
      text-align: start;
    }

    > .urlbarView-button {
      margin-bottom: var(--urlbarView-item-block-padding);
    }
  }
}

.urlbarView-dynamic-quickSuggestContextualOptIn-text-container {
  display: flex;
  flex-direction: column;
  white-space: normal;
}

.urlbarView-dynamic-quickSuggestContextualOptIn-title {
  font-weight: var(--font-weight-bold);
}

.urlbarView-dynamic-quickSuggestContextualOptIn-description {
  margin-block-start: 2px;
  color: var(--urlbarView-secondary-text-color);
  font-size: var(--urlbarView-small-font-size);
}

.urlbarView-dynamic-quickSuggestContextualOptIn-learn_more {
  white-space: nowrap;

  &[selected] {
    border-radius: var(--focus-outline-offset);
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-offset);
  }
}

.urlbarView-quickSuggestContextualOptIn-one-off-container {
  border-top: 1px solid var(--urlbarView-separator-color);
  margin-top: 10px;
  padding-top: 5px;
  width: 100%;
}

/**
 * Tab To Search onboarding
 */

.urlbarView-row[dynamicType=onboardTabToSearch] > .urlbarView-row-inner {
  min-height: 64px !important; /* Overriding :root:not([uidensity=compact]) .urlbarView-row-inner { min-height } in urlbarView.inc.css */
  align-items: center;

  > .urlbarView-no-wrap {
    align-items: center;

    > .urlbarView-favicon {
      min-width: 32px;
      height: 32px;
      -moz-context-properties: fill;
    }
  }

  &:not([selected]) > .urlbarView-no-wrap {
    > .urlbarView-favicon {
      color: var(--link-color);
    }

    > .urlbarView-dynamic-onboardTabToSearch-text-container > .urlbarView-dynamic-onboardTabToSearch-description {
      color: var(--urlbarView-secondary-text-color);
    }
  }
}

.urlbarView-row[dynamicType=onboardTabToSearch][selected] {
  fill-opacity: 1;
}

.urlbarView-dynamic-onboardTabToSearch-text-container {
  display: flex;
  flex-direction: column;
}

/* First row of text. */
.urlbarView-dynamic-onboardTabToSearch-first-row-container {
  display: flex;
  align-items: baseline;
}

.urlbarView-dynamic-onboardTabToSearch-description {
  margin-block-start: 2px;
}

.urlbarView-results[wrap] > .urlbarView-row[dynamicType=onboardTabToSearch] > .urlbarView-row-inner > .urlbarView-no-wrap > .urlbarView-dynamic-onboardTabToSearch-text-container {
  > .urlbarView-dynamic-onboardTabToSearch-first-row-container {
    flex-wrap: wrap;

    > .urlbarView-action {
      max-width: 100%;
      flex-basis: 100%;
    }

    > .urlbarView-title-separator {
      visibility: collapse;
    }
  }

  > .urlbarView-dynamic-onboardTabToSearch-description {
    max-width: 100%;
    flex-basis: 100%;
  }
}

/**
 * Calculator & Unit Conversion
 */

.urlbarView-dynamic-calculator-action,
.urlbarView-dynamic-unitConversion-action {
  font-size: var(--urlbarView-small-font-size);
  font-weight: normal;

  .urlbarView-row:not(:hover, [selected]) & {
    visibility: collapse;
  }

  &::before {
    content: "\2014";
    margin: 0 .4em;
    opacity: 0.6;
  }
}

/**
 * Actions
 */

.urlbarView-row[dynamicType=actions] > .urlbarView-row-inner {
  /* Reduce the padding to 2px so the outline does not get
     cropped and the actions + outline are aligned with the
     rest of the results */
  padding-inline: 2px;
}

/**
 * Site-specific search
 */

.urlbarView-title-separator.urlbarView-dynamic-contextualSearch-separator {
  display: block !important /* override .urlbarView-title-separator rules */;
}

.urlbarView-dynamic-contextualSearch-description {
  font-size: var(--urlbarView-small-font-size);

  .urlbarView-row:not([selected]) & {
    color: var(--urlbarView-action-color);
  }
}

/**
 * Weather
 */

.urlbarView-row[dynamicType=weather] > .urlbarView-row-inner {
  align-items: center;
}

.urlbarView-dynamic-weather-currentConditions {
  color: var(--urlbar-box-text-color);
  background-color: var(--urlbar-box-focus-bgcolor);
  padding: 0.61em 0.61em 0.84em;
  margin-inline-end: 0.92em;
  border-radius: 2px;
  text-align: center;

  .urlbarView-row[dynamicType=weather]:is([selected], :hover) > .urlbarView-row-inner > & {
    color: var(--urlbarView-result-button-selected-color);
    background-color: var(--urlbarView-result-button-selected-background-color);
  }
}

.urlbarView-dynamic-weather-currently {
  font-size: var(--urlbarView-small-font-size);
  margin-bottom: 0.53em;
}

.urlbarView-dynamic-weather-temperature {
  margin-inline-end: 0.3em;
  font-weight: 600;
  font-size: 1.385em;
  vertical-align: middle;
}

.urlbarView-dynamic-weather-weatherIcon {
  width: 22px;
  height: 23px;
  vertical-align: middle;

  @media (prefers-contrast) {
    -moz-context-properties: fill, stroke;
    fill: currentColor;
    stroke: currentColor;
  }
}

.urlbarView-dynamic-weather-summary {
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
}

.urlbarView-dynamic-weather-top {
  display: flex;
  align-items: center;
}

.urlbarView-dynamic-weather-topNoWrap {
  display: inline-flex;
  align-items: center;
}

.urlbarView-results[wrap] > .urlbarView-row[dynamicType=weather] > .urlbarView-row-inner {
  flex-wrap: nowrap;

  > .urlbarView-dynamic-weather-summary > .urlbarView-dynamic-weather-top {
    flex-wrap: wrap;

    > .urlbarView-dynamic-weather-topNoWrap > .urlbarView-dynamic-weather-titleSeparator {
      display: none;
    }
  }
}

.urlbarView-dynamic-weather-middle {
  font-size: var(--urlbarView-small-font-size);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.urlbarView-dynamic-weather-middleNoWrap {
  display: inline-flex;
  align-items: center;

  /* When middleNoWrap has overflowed, we want to hide the summaryTextSeparator.
     We also want to keep the overflow state stable without it going back to
     an underflow state. This is why we are using `visibility: hidden` so the
     space is allocated for the summaryTextSeparator and highLow but they are
     not visible on the page. Thus, keeping the middleNoWrap in an overflow
     state while hiding the summaryTextSeparator.
   */
  &[overflow] > :is(.urlbarView-dynamic-weather-summaryTextSeparator, .urlbarView-dynamic-weather-highLow) {
    visibility: hidden;
  }

  /* The highLowWrap remains hidden when middleNoWrap is not overflowed. Once it
     has overflowed, we display the highLowWrap element.
  */
  &:not([overflow]) + .urlbarView-dynamic-weather-highLowWrap {
    display: none;
  }
}

.urlbarView-dynamic-weather-summaryTextSeparator::before {
  content: '\00B7';
  margin: 0.25em;
}

.urlbarView-dynamic-weather-bottom {
  font-size: var(--urlbarView-small-font-size);
  margin-top: 0.40em;

  .urlbarView-row[dynamicType=weather]:not([selected]) > .urlbarView-row-inner > .urlbarView-dynamic-weather-summary > & {
    opacity: 0.6;
  }
}

/**
 * Fakespot suggestions
 */

.urlbarView-results[wrap] > .urlbarView-row[dynamicType=fakespot] > .urlbarView-row-inner {
  flex-wrap: nowrap;
}

.urlbarView-dynamic-fakespot-body {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
}

.urlbarView-dynamic-fakespot-description {
  display: flex;
  align-items: baseline;
  font-size: var(--urlbarView-small-font-size);
  margin-block: 0.18em;
}

.urlbarView-dynamic-fakespot-rating-five-stars {
  --icon-color: currentColor;
}

.urlbarView-dynamic-fakespot-rating-and-total-reviews {
  margin-inline: 0.25em 1.23em;
}

.urlbarView-dynamic-fakespot-badge {
  display: flex;
  align-items: center;
  color: #15141A;
  font-size: 0.75em;
  padding-inline: 0 1em;
  padding-block: 0.27em;
  border-radius: var(--border-radius-small);
  border: 1px solid rgba(0, 0, 0, 0.15);

  &::before {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    font-weight: 590;
    padding: 0.27em 0.5em;
    margin-inline: -1px 1em;
    margin-block: calc(-0.27em - 1px);
    border-start-start-radius: var(--border-radius-small);
    border-end-start-radius: var(--border-radius-small);
    border-block-start-style: solid;
    border-block-end-style: solid;
    border-inline-start-style: solid;
    border-inline-end-style: none;
    border-color: rgba(0, 0, 0, 0.15);
    border-width: 1px;
  }

  &[grade=A] {
    background-color: rgba(231, 255, 246, 0.7);
    &::before {
      content: "A";
      background-color: rgb(179, 255, 227);
    }
  }
  &[grade=B] {
    background-color: rgba(222, 250, 255, 0.7);
    &::before {
      content: "B";
      background-color: rgb(128, 235, 255);
    }
  }
}

.urlbarView-dynamic-fakespot-footer {
  font-size: var(--urlbarView-small-font-size);
}

.urlbarView-row:not([selected]) > .urlbarView-row-inner > .urlbarView-dynamic-fakespot-body {
  > .urlbarView-dynamic-fakespot-description,
  > .urlbarView-dynamic-fakespot-footer {
    color: var(--urlbarView-secondary-text-color);
  }
}
