/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* This file is used by both about:sessionrestore and about:welcomeback */

.tab-list-tree-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
}

treechildren::-moz-tree-image(icon),
treechildren::-moz-tree-image(noicon) {
  -moz-context-properties: fill;
  fill: currentColor;
  padding-inline-end: 2px;
  margin: 0 2px;
  width: 16px;
  height: 16px;
}

treechildren::-moz-tree-image(noicon) {
  list-style-image: url("chrome://global/skin/icons/defaultFavicon.svg");
}

treechildren::-moz-tree-image(container, noicon) {
  list-style-image: url("chrome://browser/skin/window.svg");
}

treechildren::-moz-tree-image(checked),
treechildren::-moz-tree-image(partial) {
  -moz-context-properties: fill, stroke;
  fill: var(--color-accent-primary);
}

treechildren::-moz-tree-image(checked, selected),
treechildren::-moz-tree-image(partial, selected) {
  fill: var(--in-content-item-selected-text);
  stroke: var(--in-content-item-selected);
}

treechildren::-moz-tree-image(checked) {
  list-style-image: url("chrome://global/skin/icons/check.svg");
}

treechildren::-moz-tree-image(partial) {
  list-style-image: url("chrome://global/skin/icons/check-partial.svg");
}
