/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import url("chrome://browser/skin/browser-shared.css");
@import url("chrome://browser/skin/contextmenu.css");

#menubar-items {
  flex-direction: column; /* for flex hack */
  justify-content: normal; /* align the menubar to the top also in customize mode */
}

#main-menubar > menu {
  appearance: none;
  color: inherit;

  &[_moz-menuactive] {
    background-color: light-dark(hsla(0,0%,0%,.12), hsla(0,0%,100%,.22));
    color: inherit;

    @media (prefers-contrast) {
      background-color: -moz-menuhover;
      color: -moz-menuhovertext;
    }
  }
}

@media (-moz-windows-accent-color-in-titlebar) or (-moz-windows-mica) {
  :root[customtitlebar] {
    @media (-moz-windows-mica) {
      &:not([lwtheme]) {
        background-color: transparent;
      }
    }

    /* stylelint-disable-next-line media-query-no-invalid */
    @media (-moz-windows-mica) or (-moz-bool-pref: "browser.theme.windows.accent-color-in-tabs.enabled") {
      &:not([lwtheme]) #TabsToolbar {
        /* These colors match the Linux/HCM default button colors. We need to
         * override these on the tabs toolbar because the accent color is
         * arbitrary, so the hardcoded colors from browser-custom-colors might
         * not provide sufficient contrast. */
        --toolbarbutton-icon-fill: currentColor;
        --toolbarbutton-hover-background: color-mix(in srgb, currentColor 17%, transparent);
        --toolbarbutton-active-background: color-mix(in srgb, currentColor 30%, transparent);
      }
    }

    &[sizemode="normal"] #navigator-toolbox {
      border-top: .5px solid ActiveBorder;
      &:-moz-window-inactive {
        border-top-color: InactiveBorder;
      }
    }
  }
}

/* Titlebar */

.titlebar-buttonbox {
  appearance: none;
  /* The button box must appear on top of the navigator-toolbox in order for
   * click and hover mouse events to work properly for the button in the restored
   * window state. Otherwise, elements in the navigator-toolbox, like the menubar,
   * can swallow those events.
   */
  z-index: 1;
}

.titlebar-buttonbox-container {
  align-items: stretch;
}

/* Window control buttons */

.titlebar-button {
  appearance: none;
  border: none;
  margin: 0;
  padding: 8px 17px;
  -moz-context-properties: stroke;
  stroke: currentColor;

  /* Win10's tablet mode is non-windowed -- windows can't be moved or resized
     freely. In theory "minimize" might still make sense, but for simplicity we
     just hide everything but "close".

     (This is not called for in Win11's tablet mode, which _is_ windowed.)
  */
  :root[win10-tablet-mode] &:not(.titlebar-close) {
    display: none;
  }

  &:focus-visible {
    outline: var(--focus-outline);
    outline-offset: var(--focus-outline-inset);
  }

  > .toolbarbutton-icon {
    width: 12px;
    height: 12px;

    &:-moz-locale-dir(rtl) {
      transform: scaleX(-1);
    }
  }
}

.titlebar-min {
  /* Even though we use appearance: none, -moz-default-appearance is necessary
   * for Windows 11's "snap layouts" feature, see
   * DealWithWindowsAppearanceHacks */
  -moz-default-appearance: -moz-window-button-minimize;
  list-style-image: url(chrome://browser/skin/window-controls/minimize.svg);
}

.titlebar-max {
  -moz-default-appearance: -moz-window-button-maximize;
  list-style-image: url(chrome://browser/skin/window-controls/maximize.svg);
}

.titlebar-restore {
  -moz-default-appearance: -moz-window-button-restore;
  list-style-image: url(chrome://browser/skin/window-controls/restore.svg);
}

.titlebar-close {
  -moz-default-appearance: -moz-window-button-close;
  list-style-image: url(chrome://browser/skin/window-controls/close.svg);
}

:root[lwtheme-image] {
  .titlebar-button {
    -moz-context-properties: unset;
  }
  .titlebar-min {
    list-style-image: url(chrome://browser/skin/window-controls/minimize-themes.svg);
  }
  .titlebar-max {
    list-style-image: url(chrome://browser/skin/window-controls/maximize-themes.svg);
  }
  .titlebar-restore {
    list-style-image: url(chrome://browser/skin/window-controls/restore-themes.svg);
  }
  .titlebar-close {
    list-style-image: url(chrome://browser/skin/window-controls/close-themes.svg);
  }
}

@media not (prefers-contrast) {
  .titlebar-button:hover {
    background-color: light-dark(hsla(0,0%,0%,.12), hsla(0,0%,100%,.22));

    &:active {
      background-color: light-dark(hsla(0,0%,0%,.22), hsla(0,0%,100%,.32));
    }
  }

  .titlebar-close:hover {
    stroke: white;
    background-color: hsl(355,86%,49%);

    &:active {
      background-color: hsl(355,82%,69%);
    }
  }
}

@media (prefers-contrast) {
  .titlebar-button {
    background-color: -moz-field;
    stroke: ButtonText;

    &:hover {
      background-color: SelectedItem;
      stroke: SelectedItemText;
    }
  }

  .titlebar-min {
    list-style-image: url(chrome://browser/skin/window-controls/minimize-highcontrast.svg);
  }

  .titlebar-max {
    list-style-image: url(chrome://browser/skin/window-controls/maximize-highcontrast.svg);
  }

  .titlebar-restore {
    list-style-image: url(chrome://browser/skin/window-controls/restore-highcontrast.svg);
  }

  .titlebar-close {
    list-style-image: url(chrome://browser/skin/window-controls/close-highcontrast.svg);
  }
}

/* the 12px image renders a 10px icon, and the 10px upscaled gets rounded to 12.5, which
 * rounds up to 13px, which makes the icon one pixel too big on 1.25dppx. Fix: */
@media (1.20dppx <= resolution <= 1.45dppx) {
  .titlebar-button > .toolbarbutton-icon {
    width: 11.5px;
    height: 11.5px;
  }
}

/* 175% dpi should result in the same device pixel sizes as 150% dpi. */
@media (1.70dppx <= resolution <= 1.95dppx) {
  .titlebar-button {
    padding-inline: 14.1px;

    > .toolbarbutton-icon {
      width: 10.8px;
      height: 10.8px;
    }
  }
}

/* 225% dpi should result in the same device pixel sizes as 200% dpi. */
@media (2.20dppx <= resolution <= 2.45dppx) {
  .titlebar-button {
    padding-inline: 15.3333px;

    > .toolbarbutton-icon {
      width: 10.8px;
      height: 10.8px;
    }
  }
}

/* 275% dpi should result in the same device pixel sizes as 250% dpi. */
@media (2.70dppx <= resolution <= 2.95dppx) {
  /* NB: todo: this should also change padding on the buttons
   * themselves, but without a device to test this on, it's
   * impossible to know by how much. */
  .titlebar-button > .toolbarbutton-icon {
    width: 10.8px;
    height: 10.8px;
  }
}

/* Bookmark menus */

.bookmark-item {
  &:is(menu, menuitem) {
    min-width: 0;
    max-width: 32em;
  }

  &:not(.subviewbutton) > .menu-iconic-left {
    margin-block: 0;
  }

  &[cutting] {
    > .toolbarbutton-icon,
    > .menu-iconic-left > .menu-iconic-icon {
      opacity: 0.5;
    }

    > .toolbarbutton-text,
    > .menu-iconic-left > .menu-iconic-text {
      opacity: 0.7;
    }
  }

  &:is(toolbarbutton)[dragover="true"][open="true"] {
    appearance: none;
    background: SelectedItem !important;
    color: SelectedItemText !important;
    fill: SelectedItemText;
  }
}

/* Bookmarks roots menu-items */

#bookmarksToolbarFolderMenu,
#BMB_bookmarksToolbar,
#panelMenu_bookmarksToolbar {
  list-style-image: url("chrome://browser/skin/places/bookmarksToolbar.svg");
}

/* Address bar */

#urlbar,
#searchbar {
  font-size: 1.15em;
}

/* stylelint-disable-next-line media-query-no-invalid */
@media (-moz-bool-pref: "browser.urlbar.experimental.expandTextOnFocus") {
  #urlbar[breakout-extend] {
    font-size: 1.25em;
  }
}

@media (prefers-contrast) {
  :root {
    /* Windows HCM conventions use these colors for chiclets. We can't use them on
       other platforms because AccentColor can be shown atop SelectedItem,
       which has zero contrast. */
    --urlbar-box-hover-bgcolor: SelectedItem;
    --urlbar-box-active-bgcolor: SelectedItem;
    --urlbar-box-hover-text-color: SelectedItemText;
  }

  :root:not([lwtheme]) {
    #urlbar:not([focused]) > #urlbar-background,
    #searchbar:not(:focus-within),
    .findbar-textbox:not(:focus) {
      border-color: ThreeDShadow;
    }
  }
}

/* Autocomplete */

#PopupAutoComplete > richlistbox > richlistitem[originaltype~="datalist-first"] {
  border-top: 1px solid ThreeDShadow;
}

/* Tabstrip */

#TabsToolbar {
  min-height: 0;
  padding: 0;
}

@media (prefers-contrast) {
  /* For high contrast themes. XXX is this needed anymore? */
  #tabbrowser-tabpanels,
  :root[privatebrowsingmode=temporary] #tabbrowser-tabpanels {
    background-color: -moz-default-background-color;
  }
}

/* All tabs menupopup */

.alltabs-item[selected="true"] {
  font-weight: bold;
}

#UITourTooltipButtons {
  /**
   * Override the --arrowpanel-padding so the background extends
   * to the sides and bottom of the panel.
   */
  margin-inline: -10px;
  margin-bottom: -10px;
}

/* Make menu items larger when opened through touch. */
panel[touchmode],
menupopup[touchmode] {
  --arrowpanel-menuitem-padding-block: 12px;
  --arrowpanel-menuitem-padding: var(--arrowpanel-menuitem-padding-block) var(--arrowpanel-menuitem-padding-inline);
  /* The value for the header back icon padding is selected in a way that the
   * height of the header and its separator will be equal to the panel inner
   * top padding plus standard menuitem, so that the header's separator will
   * be located excatly where a normal toolbarseparator would be located after
   * the first menuitem, in a menu without a header. */
  --arrowpanel-header-back-icon-padding: 10px;
}

:root[uidensity="compact"] panel[touchmode] {
  --arrowpanel-header-back-icon-padding: 12px;
}

menupopup[touchmode] :is(menu, menuitem, menucaption) {
  /* Use the var set in the rule above to increase menu items height in menupopups when
   * they are opened through touch.
   * Panel menu items already have their padding set in panelUI-shared.css with the same var. */
  padding-block: var(--arrowpanel-menuitem-padding-block);
}

#contentAreaContextMenu[touchmode] > #context-navigation > menuitem {
  /* The navigation menuitems are bigger to begin with because of their inner padding,
   * so calculate the padding-block to match normal menuitems height when in touchmode.
   * 3.5em is the desired menuitem height (~42px), minus the menuitem actual height devided by 2. */
  padding-block: calc((3.5em - 32px) / 2);
}

/* Other menu separators don't extend all the way to the menu edges, but the
   one below the navigation buttons in the content context menu should. */
#context-sep-navigation {
  padding-inline: 0;
}
