/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

@import "chrome://browser/skin/downloads/allDownloadsView.inc.css";

/*** List items ***/

@media not (prefers-contrast) {
  .downloadProgress::-moz-progress-bar {
    background-color: #3c9af8;
  }

  .downloadProgress[paused]::-moz-progress-bar {
    background-color: #a6a6a6;
  }

  /*** Highlighted list items ***/

  /*
  -moz-default-appearance: menuitem is almost right, but the hover effect is not
  transparent and is lighter than desired.

  Copied from the autocomplete richlistbox styling in
  toolkit/themes/windows/global/autocomplete.css

  This styling should be kept in sync with the style from the above file.
  */
  #downloadsListBox:focus > richlistitem[selected] {
    color: inherit;
    background-color: transparent;
    /* four gradients for the bevel highlights on each edge, one for blue background */
    background-image:
      linear-gradient(to bottom, rgba(255,255,255,0.9) 3px, transparent 3px),
      linear-gradient(to right, rgba(255,255,255,0.5) 3px, transparent 3px),
      linear-gradient(to left, rgba(255,255,255,0.5) 3px, transparent 3px),
      linear-gradient(to top, rgba(255,255,255,0.4) 3px, transparent 3px),
      linear-gradient(to bottom, rgba(163,196,247,0.3), rgba(122,180,246,0.3));
    background-clip: content-box;
    border-radius: 6px;
    outline: 1px solid rgb(124,163,206);
    outline-offset: -2px;
  }
}

/*** Progress bars ***/

@media (prefers-contrast) {
  .downloadProgress {
    background-color: -moz-Dialog;
    border: 1px solid ButtonText;
  }

  .downloadProgress::-moz-progress-bar {
    --download-progress-fill-color: SelectedItem;
  }

  #downloadsListBox richlistitem[selected] .downloadProgress::-moz-progress-bar {
    --download-progress-fill-color: ButtonText;
  }
}
