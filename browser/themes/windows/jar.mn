# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

browser.jar:
% skin browser classic/1.0 %skin/classic/browser/
#include ../shared/jar.inc.mn
  skin/classic/browser/sanitizeDialog.css
  skin/classic/browser/browser.css
  skin/classic/browser/contextmenu.css            (../shared/contextmenu.css)
  skin/classic/browser/monitor-base.png
  skin/classic/browser/monitor-border.png
  skin/classic/browser/pageInfo.png
  skin/classic/browser/customizableui/panelUI.css       (customizableui/panelUI.css)
  skin/classic/browser/downloads/allDownloadsView.css   (downloads/allDownloadsView.css)
  skin/classic/browser/downloads/downloads.css                 (downloads/downloads.css)
  skin/classic/browser/notification-icons/camera.png           (notification-icons/camera.png)
  skin/classic/browser/notification-icons/microphone.png       (notification-icons/microphone.png)
  skin/classic/browser/notification-icons/screen.png           (notification-icons/screen.png)
  skin/classic/browser/places/organizer.css                    (places/organizer.css)
  skin/classic/browser/preferences/alwaysAsk.png               (preferences/alwaysAsk.png)
  skin/classic/browser/preferences/application.png             (preferences/application.png)
  skin/classic/browser/preferences/saveFile.png                (preferences/saveFile.png)
  skin/classic/browser/preferences/applications.css            (preferences/applications.css)
  skin/classic/browser/window-controls/close.svg                 (window-controls/close.svg)
  skin/classic/browser/window-controls/close-highcontrast.svg    (window-controls/close-highcontrast.svg)
  skin/classic/browser/window-controls/close-themes.svg          (window-controls/close-themes.svg)
  skin/classic/browser/window-controls/maximize.svg              (window-controls/maximize.svg)
  skin/classic/browser/window-controls/maximize-highcontrast.svg (window-controls/maximize-highcontrast.svg)
  skin/classic/browser/window-controls/maximize-themes.svg       (window-controls/maximize-themes.svg)
  skin/classic/browser/window-controls/minimize.svg              (window-controls/minimize.svg)
  skin/classic/browser/window-controls/minimize-highcontrast.svg (window-controls/minimize-highcontrast.svg)
  skin/classic/browser/window-controls/minimize-themes.svg       (window-controls/minimize-themes.svg)
  skin/classic/browser/window-controls/restore.svg               (window-controls/restore.svg)
  skin/classic/browser/window-controls/restore-highcontrast.svg  (window-controls/restore-highcontrast.svg)
  skin/classic/browser/window-controls/restore-themes.svg        (window-controls/restore-themes.svg)
