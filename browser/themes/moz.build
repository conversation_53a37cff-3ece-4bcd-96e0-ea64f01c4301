# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Firefox", "Theme")

with Files("ThemeVariableMap.sys.mjs"):
    BUG_COMPONENT = ("WebExtensions", "Themes")

EXTRA_JS_MODULES += [
    "BuiltInThemeConfig.sys.mjs",
    "BuiltInThemes.sys.mjs",
    "ThemeVariableMap.sys.mjs",
]

BROWSER_CHROME_MANIFESTS += [
    "test/browser/browser.toml",
]

toolkit = CONFIG["MOZ_WIDGET_TOOLKIT"]

if toolkit == "cocoa":
    DIRS += ["osx"]
elif toolkit == "gtk":
    DIRS += ["linux"]
else:
    DIRS += ["windows"]

DIRS += ["addons", "shared/app-marketplace-icons"]

with Files("osx/**"):
    SCHEDULES.exclusive = ["macosx"]

with Files("linux/**"):
    SCHEDULES.exclusive = ["linux"]

with Files("windows/**"):
    SCHEDULES.exclusive = ["windows"]
