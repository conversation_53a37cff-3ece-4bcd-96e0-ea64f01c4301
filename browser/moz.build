# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

CONFIGURE_SUBST_FILES += ["installer/Makefile"]

SPHINX_TREES["/browser"] = "docs"

EXTRA_COMPONENTS += [
    "l10n-registry.manifest",
]

DIRS += [
    "actors",
    "base",
    "components",
    "fonts",
    "fxr",
    "locales",
    "modules",
    "themes",
    "extensions",
    "branding",
]

DIRS += [
    "app",
]

if CONFIG["MAKENSISU"]:
    DIRS += ["installer/windows"]

TEST_DIRS += [
    "tools/mozscreenshots",
]

DIST_SUBDIR = "browser"
export("DIST_SUBDIR")

# These defines are read in firefox.js
DEFINES["APP_VERSION"] = CONFIG["MOZ_APP_VERSION"]

for cdm in CONFIG["MOZ_EME_MODULES"]:
    DEFINES["MOZ_%s_EME" % cdm.upper()] = True

if CONFIG["MOZ_UPDATE_AGENT"]:
    DEFINES["MOZ_UPDATE_AGENT"] = True

if CONFIG["MOZ_ARTIFACT_BUILDS"]:
    DEFINES["MOZ_ARTIFACT_BUILDS"] = True

# These files are specified in this moz.build to pick up DIST_SUBDIR as set in
# this directory, which is un-set in browser/app.
JS_PREFERENCE_PP_FILES += [
    "app/profile/firefox.js",
]
FINAL_TARGET_FILES.defaults += ["app/permissions"]

with Files("**"):
    BUG_COMPONENT = ("Firefox", "General")
    SCHEDULES.exclusive = ["linux", "macosx", "windows", "firefox"]

with Files("docs/**"):
    SCHEDULES.exclusive = ["docs"]

with Files("Makefile.in"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("*.mk"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("**/moz.build"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("moz.configure"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("app.mozbuild"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("moz.build"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("confvars.sh"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("LICENSE"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("branding/**"):
    BUG_COMPONENT = ("Firefox", "General")

with Files("config/**"):
    BUG_COMPONENT = ("Firefox Build System", "General")

with Files("docs/**"):
    BUG_COMPONENT = ("Toolkit", "Telemetry")

with Files("fonts/**"):
    BUG_COMPONENT = ("Core", "Graphics: Text")

with Files("installer/**"):
    BUG_COMPONENT = ("Firefox", "Installer")

with Files("tools/**"):
    BUG_COMPONENT = ("Firefox", "General")

with Files("l10n-registry.manifest"):
    BUG_COMPONENT = ("Core", "Localization")
