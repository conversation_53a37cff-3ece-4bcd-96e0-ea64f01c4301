{"nonPublishedName": "mozilla-central", "notes(nonPublishedName)": "This package is never published, but the nonPublishedName helps our ESLint package to find the top-level of mozilla-central", "description": "This package file is for node modules used in mozilla-central", "repository": {}, "license": "MPL-2.0", "devDependencies": {"@microsoft/eslint-plugin-sdl": "0.2.2", "@stylistic/stylelint-plugin": "^3.1.0", "@types/gecko": "file:tools/@types", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-eslint-plugin": "6.3.2", "eslint-plugin-html": "8.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "28.9.0", "eslint-plugin-jsdoc": "50.6.0", "eslint-plugin-json": "4.0.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-mozilla": "file:tools/lint/eslint/eslint-plugin-mozilla", "eslint-plugin-no-unsanitized": "4.1.2", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-spidermonkey-js": "file:tools/lint/eslint/eslint-plugin-spidermonkey-js", "jsdoc": "4.0.4", "prettier": "3.4.1", "stylelint": "16.11.0", "stylelint-config-recommended": "14.0.1", "stylelint-config-standard-scss": "13.1.0", "yarn": "1.22.22"}, "notes(private)": "We don't want to publish to npm, so this is marked as private", "private": true}