# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "General")

with Files("code-coverage/**"):
    BUG_COMPONENT = ("Testing", "Code Coverage")

with Files("compare-locales/mach_commands.py"):
    BUG_COMPONENT = ("Localization Infrastructure and Tools", "compare-locales")

with Files("lint/**"):
    BUG_COMPONENT = ("Developer Infrastructure", "Lint and Formatting")

with Files("moztreedocs/**"):
    BUG_COMPONENT = ("Developer Infrastructure", "Source Documentation")
    SCHEDULES.exclusive = ["docs"]

with Files("profiler/**"):
    BUG_COMPONENT = ("Core", "Gecko Profiler")

with Files("performance/**"):
    BUG_COMPONENT = ("Core", "Gecko Profiler")

with Files("quitter/**"):
    BUG_COMPONENT = ("Testing", "General")

with Files("rb/**"):
    BUG_COMPONENT = ("Core", "XPCOM")

with Files("rewriting/**"):
    BUG_COMPONENT = ("Firefox Build System", "Source Code Analysis")

with Files("tryselect/**"):
    BUG_COMPONENT = ("Developer Infrastructure", "Try")

with Files("tryselect/selectors/release.py"):
    BUG_COMPONENT = ("Release Engineering", "General")

with Files("ts/**"):
    BUG_COMPONENT = ("Developer Infrastructure", "Lint and Formatting")

with Files("update-packaging/**"):
    BUG_COMPONENT = ("Release Engineering", "General")

with Files("update-verify/**"):
    BUG_COMPONENT = ("Release Engineering", "Release Automation")

with Files("vcs/**"):
    BUG_COMPONENT = ("Firefox Build System", "General")

SPHINX_TREES["moztreedocs"] = "moztreedocs/docs"

SPHINX_TREES["try"] = "tryselect/docs"

SPHINX_TREES["fuzzing"] = "fuzzing/docs"

SPHINX_TREES["sanitizer"] = "sanitizer/docs"

SPHINX_TREES["code-coverage"] = "code-coverage/docs"

SPHINX_TREES["profiler"] = "profiler/docs"

with Files("tryselect/docs/**"):
    SCHEDULES.exclusive = ["docs"]

PYTHON_UNITTEST_MANIFESTS += [
    "fuzzing/smoke/python.toml",
    "lint/test/python.toml",
    "tryselect/test/python.toml",
]
