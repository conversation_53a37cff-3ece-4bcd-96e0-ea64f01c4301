/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

"use strict";

module.exports = {
  plugins: ["eslint-plugin"],
  extends: ["plugin:eslint-plugin/recommended"],
  env: {
    browser: false,
    node: true,
  },
  rules: {
    "no-console": "off",
    strict: ["error", "global"],
  },
};
