/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
"use strict";

/**
 * Build: <objdir>/dist/@types/lib.gecko.services.d.ts,
 *
 * from:  <objdir>/xpcom/components/services.json,
 *        generated by a previous build step.
 */

const fs = require("fs");

const HEADER = `/**
 * NOTE: Do not modify this file by hand.
 * Content was generated from services.json.
 * If you're updating some of the sources, see README for instructions.
 */
`;

const SERVICES = {
  cpmm: ["ContentProcessMessageManager"],
  mm: ["ChromeMessageBroadcaster"],
  ppmm: ["ParentProcessMessageManager"],
};

function main(lib_dts, services_json) {
  let interfaces = JSON.parse(fs.readFileSync(services_json, "utf8"));

  for (let [iface, svc] of Object.entries(interfaces)) {
    SERVICES[svc] = SERVICES[svc] ?? [];
    SERVICES[svc].push(iface);
  }

  let lines = [HEADER];
  lines.push("interface JSServices {");
  for (let [svc, ifaces] of Object.entries(SERVICES).sort()) {
    lines.push(`  ${svc}: ${ifaces.join(" & ")};`);
  }
  lines.push("}\n");

  let dts = lines.join("\n");
  console.log(`[INFO] ${lib_dts} (${dts.length.toLocaleString()} bytes)`);
  fs.writeFileSync(lib_dts, dts);
}

main(...process.argv.slice(2));
