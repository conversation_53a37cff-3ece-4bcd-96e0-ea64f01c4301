.gradle/
build/vs/vs2022.yaml
browser/components/aboutwelcome/content/aboutwelcome.bundle.js
browser/components/aboutwelcome/logs/
browser/components/aboutwelcome/node_modules/
browser/components/asrouter/node_modules/
browser/components/asrouter/content/asrouter-admin.bundle.js
browser/components/asrouter/logs/
browser/components/asrouter/content-src/schemas/BackgroundTaskMessagingExperiment.schema.json
browser/components/asrouter/content-src/schemas/MessagingExperiment.schema.json
browser/components/asrouter/tests/InflightAssetsMessageProvider.sys.mjs
browser/components/asrouter/tests/NimbusRolloutMessageProvider.sys.mjs
browser/components/newtab/logs/
browser/components/newtab/node_modules/
browser/components/storybook/storybook-static/
browser/locales/l10n-changesets.json
browser/locales/l10n-onchange-changesets.json
devtools/client/aboutdebugging/test/jest/node_modules/
devtools/client/application/test/components/node_modules/
devtools/client/debugger/node_modules/
dom/base/use_counter_metrics.yaml
dom/tests/ajax/jquery/
dom/tests/ajax/mochikit/
gradle/wrapper/
intl/components/src/UnicodeScriptCodes.h
intl/unicharutil/util/nsSpecialCasingData.cpp
intl/unicharutil/util/nsUnicodePropertyData.cpp
mobile/android/**/.build-cache
mobile/android/**/.gradle
mobile/android/**/build
mobile/android/**/bin
mobile/android/**/generated
mobile/android/**\/local.properties
mobile/android/**\/manifest.json
mobile/android/android-components/components/feature/search/src/main/assets/search/search_telemetry_v2.json
mobile/android/android-components/components/browser/engine-system/src/main/res/
mobile/android/android-components/components/browser/errorpages/src/main/res/
mobile/android/android-components/components/browser/menu/src/main/res/
mobile/android/android-components/components/browser/menu2/src/main/res/
mobile/android/android-components/components/browser/toolbar/src/main/res/
mobile/android/android-components/components/browser/toolbar2/src/main/res/
mobile/android/android-components/components/compose/awesomebar/src/main/res/
mobile/android/android-components/components/compose/browser-toolbar/src/main/res/
mobile/android/android-components/components/compose/cfr/src/main/res/
mobile/android/android-components/components/compose/tabstray/src/main/res/
mobile/android/android-components/components/feature/addons/src/main/res/
mobile/android/android-components/components/feature/app-links/src/main/res/
mobile/android/android-components/components/feature/autofill/src/main/res/
mobile/android/android-components/components/feature/awesomebar/src/main/res/
mobile/android/android-components/components/feature/contextmenu/src/main/res/
mobile/android/android-components/components/feature/customtabs/src/main/res/
mobile/android/android-components/components/feature/downloads/src/main/res/
mobile/android/android-components/components/feature/findinpage/src/main/res/
mobile/android/android-components/components/feature/fxsuggest/src/main/res/
mobile/android/android-components/components/feature/media/src/main/res/
mobile/android/android-components/components/feature/privatemode/src/main/res/
mobile/android/android-components/components/feature/prompts/src/main/res/
mobile/android/android-components/components/feature/pwa/src/main/res/
mobile/android/android-components/components/feature/qr/src/main/res/
mobile/android/android-components/components/feature/readerview/src/main/res/
mobile/android/android-components/components/feature/search/src/main/res/
mobile/android/android-components/components/feature/sitepermissions/
mobile/android/android-components/components/feature/tabs/src/main/res/
mobile/android/android-components/components/feature/webnotifications/src/main/res/
mobile/android/android-components/components/lib/crash/src/main/res/
mobile/android/android-components/components/service/nimbus/src/main/res/
mobile/android/android-components/components/support/base/src/main/res/
mobile/android/android-components/components/support/ktx/src/main/res/
mobile/android/android-components/components/support/utils/src/main/res/
mobile/android/android-components/components/ui/tabcounter/src/main/res/
mobile/android/android-components/components/ui/widgets/src/main/res/
mobile/android/android-components/config/detekt-baseline.xml
mobile/android/android-components/gradle/wrapper/
mobile/android/android-components/samples/glean/src/main/res/raw/initial_experiments.json
mobile/android/fenix/app/lint-baseline.xml
mobile/android/fenix/app/src/debug/res/raw/initial_experiments.json
mobile/android/fenix/app/src/main/res/
mobile/android/fenix/app/src/main/res/raw/initial_experiments.json
mobile/android/fenix/config/detekt-baseline.xml
mobile/android/fenix/gradle/wrapper/
mobile/android/focus-android/app/lint-baseline.xml
mobile/android/focus-android/app/src/main/res/
mobile/android/focus-android/gradle/wrapper/
mobile/android/focus-android/quality/detekt-baseline.xml
mobile/locales/l10n-changesets.json
mobile/locales/l10n-onchange-changesets.json
node_modules/
python/mozperftest/mozperftest/tests/data/
security/manager/tools/KnownRootHashes.json
security/manager/tools/PreloadedHPKPins.json
services/settings/dumps/
toolkit/components/nimbus/schemas/ExperimentFeature.schema.json
toolkit/components/nimbus/schemas/ExperimentFeatureManifest.schema.json
toolkit/components/nimbus/schemas/NimbusExperiment.schema.json
toolkit/components/pdfjs/PdfJsDefaultPrefs.js
toolkit/components/uniffi-js/UniFFIGeneratedScaffolding.cpp
toolkit/components/uniffi-js/UniFFIFixtureScaffolding.cpp
toolkit/components/uniffi-bindgen-gecko-js/fixtures/generated
tools/browsertime/package.json
tools/browsertime/package-lock.json
tools/ts/error_list.json
try_task_config.json
xpcom/idl-parser/xpidl/fixtures/xpctest.d.json
