browser/components/newtab/vendor/
browser/components/pocket/content/panels/css/normalize.scss
browser/components/pocket/content/panels/js/vendor/
browser/components/storybook/node_modules/
browser/extensions/formautofill/content/third-party/
browser/extensions/formautofill/test/fixtures/third_party/
devtools/client/inspector/markup/test/lib_*
devtools/client/jsonview/lib/require.js
devtools/client/shared/build/babel.js
devtools/client/shared/source-map/
devtools/client/shared/sourceeditor/codemirror/
devtools/client/shared/sourceeditor/codemirror6/
devtools/client/shared/sourceeditor/test/cm_mode_ruby.js
devtools/client/shared/sourceeditor/test/codemirror/
devtools/client/shared/vendor/
devtools/client/inspector/markup/test/helper_diff.js
devtools/client/debugger/src/workers/parser/utils/parse-script-tags/
devtools/shared/acorn/
devtools/shared/compatibility/dataset/css-properties.json
devtools/shared/heapsnapshot/CoreDump.pb.cc
devtools/shared/heapsnapshot/CoreDump.pb.h
devtools/shared/jsbeautify/
devtools/shared/node-properties/
devtools/shared/qrcode/decoder/
devtools/shared/qrcode/encoder/
devtools/shared/sprintfjs/
devtools/shared/storage/vendor/
dom/canvas/test/webgl-conf/checkout/
dom/imptests/
dom/media/gmp/rlz/
dom/media/gmp/widevine-adapter/content_decryption_module_export.h
dom/media/gmp/widevine-adapter/content_decryption_module_ext.h
dom/media/gmp/widevine-adapter/content_decryption_module.h
dom/media/gmp/widevine-adapter/content_decryption_module_proxy.h
dom/media/platforms/ffmpeg/ffmpeg57/
dom/media/platforms/ffmpeg/ffmpeg58/
dom/media/platforms/ffmpeg/ffmpeg59/
dom/media/platforms/ffmpeg/ffmpeg60/
dom/media/platforms/ffmpeg/ffmpeg61/
dom/media/platforms/ffmpeg/libav53/
dom/media/platforms/ffmpeg/libav54/
dom/media/platforms/ffmpeg/libav55/
dom/media/webaudio/test/blink/
dom/media/webrtc/tests/mochitests/helpers_from_wpt/sdp.js
dom/media/webrtc/transport/third_party/
dom/media/webspeech/recognition/endpointer.cc
dom/media/webspeech/recognition/endpointer.h
dom/media/webspeech/recognition/energy_endpointer.cc
dom/media/webspeech/recognition/energy_endpointer.h
dom/media/webspeech/recognition/energy_endpointer_params.cc
dom/media/webspeech/recognition/energy_endpointer_params.h
dom/media/webvtt/vtt.sys.mjs
dom/tests/mochitest/ajax/
dom/tests/mochitest/dom-level1-core/
dom/tests/mochitest/dom-level2-core/
dom/tests/mochitest/dom-level2-html/
dom/u2f/tests/pkijs/
dom/webauthn/tests/pkijs/
dom/webgpu/tests/cts/checkout/
editor/libeditor/tests/browserscope/lib/richtext/
editor/libeditor/tests/browserscope/lib/richtext2/
extensions/spellcheck/hunspell/src/
function2/
fmt/
gfx/angle/checkout/
gfx/cairo/
gfx/graphite2/
gfx/harfbuzz/
gfx/ots/
gfx/qcms/
gfx/sfntly/
gfx/skia/
gfx/vr/service/openvr/
gfx/vr/service/openvr/headers/openvr.h
gfx/vr/service/openvr/src/README
gfx/vr/service/openvr/src/dirtools_public.cpp
gfx/vr/service/openvr/src/dirtools_public.h
gfx/vr/service/openvr/src/envvartools_public.cpp
gfx/vr/service/openvr/src/envvartools_public.h
gfx/vr/service/openvr/src/hmderrors_public.cpp
gfx/vr/service/openvr/src/hmderrors_public.h
gfx/vr/service/openvr/src/ivrclientcore.h
gfx/vr/service/openvr/src/openvr_api_public.cpp
gfx/vr/service/openvr/src/pathtools_public.cpp
gfx/vr/service/openvr/src/pathtools_public.h
gfx/vr/service/openvr/src/sharedlibtools_public.cpp
gfx/vr/service/openvr/src/sharedlibtools_public.h
gfx/vr/service/openvr/src/strtools_public.cpp
gfx/vr/service/openvr/src/strtools_public.h
gfx/vr/service/openvr/src/vrpathregistry_public.cpp
gfx/vr/service/openvr/src/vrpathregistry_public.h
gfx/wr/
gfx/ycbcr/
intl/icu/
intl/icu_capi/
ipc/chromium/src/third_party/
js/src/ctypes/libffi/
js/src/dtoa.c
js/src/editline/
js/src/jit/arm64/vixl/
js/src/octane/
js/src/tests/test262/
js/src/vtune/disable_warnings.h
js/src/vtune/ittnotify_config.h
js/src/vtune/ittnotify.h
js/src/vtune/ittnotify_static.c
js/src/vtune/ittnotify_static.h
js/src/vtune/ittnotify_types.h
js/src/vtune/jitprofiling.c
js/src/vtune/jitprofiling.h
js/src/vtune/legacy/
js/src/zydis/
layout/docs/css-gap-decorations/
media/ffvpx/
media/kiss_fft/
media/libaom/
media/libcubeb/
media/libdav1d/
media/libjpeg/
media/libmkv/
media/libnestegg/
media/libogg/
media/libopus/
media/libpng/
media/libsoundtouch/
media/libspeex_resampler/
media/libvorbis/
media/libvpx/
media/libwebp/
media/libyuv/
media/mozva/va
media/mp4parse-rust/
media/openmax_dl/
media/openmax_il/
media/webrtc/signaling/gtest/MockCall.h
mfbt/double-conversion/double-conversion/
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/JSDOMParser-0.4.2.js
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/readability-0.4.2.js
mobile/android/android-components/components/feature/readerview/src/main/assets/extensions/readerview/readability/readability-readerable-0.4.2.js
mobile/android/exoplayer2/
modules/brotli/
modules/fdlibm/
modules/freetype2/
modules/woff2/
modules/xz-embedded/
modules/zlib/
mozglue/misc/decimal/
mozglue/static/lz4/.*
mozglue/tests/glibc_printf_tests/
netwerk/dns/nsIDNKitInterface.h
netwerk/sctp/src/
netwerk/srtp/src/
nsprpub/
other-licenses/
parser/expat/
remote/cdp/test/browser/chrome-remote-interface.js
remote/test/puppeteer/
security/manager/tools/log_list.json
security/nss/
security/sandbox/chromium/
security/sandbox/chromium-shim/
security/sandbox/linux/launch/glibc_hack/sandbox/linux/services/namespace_sandbox.cc
services/common/kinto-http-client.sys.mjs
services/common/kinto-offline-client.sys.mjs
testing/gtest/gmock/
testing/gtest/gtest/
testing/mochitest/pywebsocket3/
testing/modules/sinon-7.2.7.js
testing/mozbase/mozproxy/mozproxy/backends/mitm/scripts/catapult/
testing/talos/talos/tests/devtools/addon/content/pages/
testing/talos/talos/tests/dromaeo/
testing/talos/talos/tests/kraken/
testing/talos/talos/tests/offscreencanvas/benchmarks/video/demuxer_mp4.js
testing/talos/talos/tests/offscreencanvas/benchmarks/video/mp4box.all.min.js
testing/talos/talos/tests/v8_7/
testing/web-platform/tests/resources/webidl2/
testing/web-platform/tests/tools/third_party/
testing/web-platform/mozilla/tests/webgpu/
testing/xpcshell/dns-packet/
testing/xpcshell/node_ip/
testing/xpcshell/node-http2/
testing/xpcshell/node-ws/
third_party/
toolkit/components/certviewer/content/vendor/
toolkit/components/jsoncpp/
toolkit/components/ml/vendor/
toolkit/components/normandy/vendor/
toolkit/components/passwordmgr/shared/PasswordRulesParser.sys.mjs
toolkit/components/protobuf/
toolkit/components/reader/readability/
toolkit/components/resistfingerprinting/content/gl-matrix.js
toolkit/components/translation/cld2/
toolkit/components/translations/bergamot-translator/thirdparty
toolkit/components/translations/bergamot-translator/bergamot-translator.js
toolkit/components/url-classifier/chromium/
toolkit/components/utils/mozjexl.sys.mjs
toolkit/components/viaduct/fetch_msg_types.pb.cc
toolkit/components/viaduct/fetch_msg_types.pb.h
toolkit/content/widgets/vendor/lit.all.mjs
toolkit/crashreporter/breakpad-client/
toolkit/crashreporter/google-breakpad/
tools/fuzzing/libfuzzer/
tools/profiler/core/vtune/
xpcom/build/mach_override.c
xpcom/build/mach_override.h
xpcom/io/crc32c.c
rlbox/rlbox_sandbox.hpp
