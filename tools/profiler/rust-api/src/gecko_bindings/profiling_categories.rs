// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at https://mozilla.org/MPL/2.0/.

//! This file contains the generated ProfilingCategory and ProfilingCategoryPair enums.
//!
//! The contents of this module are generated by
//! `mozglue/baseprofiler/generate_profiling_categories.py`, from
//! 'mozglue/baseprofiler/core/profiling_categories.yaml`.

include!(mozbuild::objdir_path!(
    "tools/profiler/rust-api/src/gecko_bindings/profiling_categories.rs"
));

/// Helper macro that returns the profiling category pair from either only
/// "category", or "category + sub category" pair. Refer to `profiling_categories.yaml`
/// or generated `profiling_categories.rs` to see all the marker categories.
/// This is useful to make the APIs similar to each other since
/// `gecko_profiler_label!` API also requires the same syntax.
///
/// Example usages:
///  - `gecko_profiler_category!(DOM)`
///  - `gecko_profiler_category!(JavaScript, Parsing)`
#[macro_export]
macro_rules! gecko_profiler_category {
    ($category:ident) => {
        $crate::ProfilingCategoryPair::$category(None)
    };
    ($category:ident, $subcategory:ident) => {
        $crate::ProfilingCategoryPair::$category(Some($crate::$category::$subcategory))
    };
}
