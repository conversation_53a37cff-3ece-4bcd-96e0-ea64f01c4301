[package]
name = "gecko-profiler"
version = "0.1.0"
authors = ["The Mozilla Project Developers"]
edition = "2018"
license = "MPL-2.0"

[dependencies]
profiler-macros = { path = "./macros" }
lazy_static = "1"
serde = { version = "1.0", features = ["derive"] }
bincode = "1"
mozbuild = "0.1"

[build-dependencies]
lazy_static = "1"
bindgen = {version = "0.69", default-features = false}
mozbuild = "0.1"

[features]
# This feature is being set by Gecko. If it's not set, all public functions and
# structs will be no-op.
enabled = []
