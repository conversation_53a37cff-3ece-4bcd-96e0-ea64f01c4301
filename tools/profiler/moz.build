# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

if CONFIG["MOZ_GECKO_PROFILER"]:
    DEFINES["MOZ_REPLACE_MALLOC_PREFIX"] = "profiler"
    XPIDL_MODULE = "profiler"
    XPIDL_SOURCES += [
        "gecko/nsIProfiler.idl",
    ]
    EXPORTS += [
        "public/GeckoProfilerReporter.h",
        "public/ProfilerChild.h",
        "public/ProfilerCodeAddressService.h",
    ]
    UNIFIED_SOURCES += [
        "core/PageInformation.cpp",
        "core/platform.cpp",
        "core/ProfileBuffer.cpp",
        "core/ProfileBufferEntry.cpp",
        "core/ProfiledThreadData.cpp",
        "core/ProfilerBacktrace.cpp",
        "core/ProfilerCodeAddressService.cpp",
        "core/ProfilerMarkers.cpp",
        "gecko/ChildProfilerController.cpp",
        "gecko/nsProfilerStartParams.cpp",
        "gecko/ProfilerChild.cpp",
        "gecko/ProfilerIOInterposeObserver.cpp",
    ]
    if CONFIG["MOZ_REPLACE_MALLOC"] and CONFIG["MOZ_PROFILER_MEMORY"]:
        SOURCES += [
            "core/memory_hooks.cpp",  # Non-unified because of order of #includes
        ]

    XPCOM_MANIFESTS += [
        "gecko/components.conf",
    ]

    if CONFIG["OS_TARGET"] == "Darwin":
        # This file cannot be built in unified mode because it includes
        # "nsLocalFile.h", which pulls in a system header which uses a type
        # called TextRange, which conflicts with mozilla::TextRange due to
        # a "using namespace mozilla;" declaration from a different file.
        SOURCES += [
            "gecko/nsProfiler.cpp",
        ]
    else:
        UNIFIED_SOURCES += [
            "gecko/nsProfiler.cpp",
        ]

    if CONFIG["OS_TARGET"] in ("Android", "Linux"):
        UNIFIED_SOURCES += [
            "core/ProfilerCPUFreq-linux-android.cpp",
        ]
    if CONFIG["OS_TARGET"] in ("Android", "Linux", "FreeBSD"):
        if CONFIG["TARGET_CPU"] in ("arm", "aarch64", "x86", "x86_64", "mips64"):
            UNIFIED_SOURCES += [
                "lul/AutoObjectMapper.cpp",
                "lul/LulCommon.cpp",
                "lul/LulDwarf.cpp",
                "lul/LulDwarfSummariser.cpp",
                "lul/LulElf.cpp",
                "lul/LulMain.cpp",
                "lul/platform-linux-lul.cpp",
            ]
        if not CONFIG["MOZ_CRASHREPORTER"]:
            SOURCES += [
                "/toolkit/crashreporter/google-breakpad/src/common/linux/elfutils.cc",
                "/toolkit/crashreporter/google-breakpad/src/common/linux/file_id.cc",
                "/toolkit/crashreporter/google-breakpad/src/common/linux/linux_libc_support.cc",
                "/toolkit/crashreporter/google-breakpad/src/common/linux/memory_mapped_file.cc",
            ]
            if not CONFIG["HAVE_GETCONTEXT"]:
                SOURCES += [
                    "/toolkit/crashreporter/google-breakpad/src/common/linux/breakpad_getcontext.S"
                ]
        if CONFIG["TARGET_CPU"] == "x86_64" and CONFIG["OS_TARGET"] == "Linux":
            UNIFIED_SOURCES += [
                "core/PowerCounters-linux.cpp",
            ]
        elif CONFIG["TARGET_CPU"] == "aarch64" and CONFIG["OS_TARGET"] == "Android":
            SOURCES += [
                "core/PowerCounters-android.cpp",
            ]
        if CONFIG["TARGET_CPU"] == "arm" and CONFIG["OS_TARGET"] != "FreeBSD":
            SOURCES += [
                "core/EHABIStackWalk.cpp",
            ]
    elif CONFIG["OS_TARGET"] == "Darwin":
        if CONFIG["TARGET_CPU"] == "aarch64":
            UNIFIED_SOURCES += [
                "core/PowerCounters-mac-arm64.cpp",
            ]
        if CONFIG["TARGET_CPU"] == "x86_64":
            UNIFIED_SOURCES += [
                "core/PowerCounters-mac-amd64.cpp",
            ]
    elif CONFIG["OS_TARGET"] == "WINNT":
        if CONFIG["CC_TYPE"] == "clang-cl":
            UNIFIED_SOURCES += [
                "core/PowerCounters-win.cpp",
            ]
            SOURCES += {
                "core/ETWTools.cpp",
            }
        SOURCES += [
            "core/ProfilerCPUFreq-win.cpp",
        ]

    LOCAL_INCLUDES += [
        "/caps",
        "/docshell/base",
        "/ipc/chromium/src",
        "/mozglue/linker",
        "/netwerk/base",
        "/netwerk/protocol/http",
        "/toolkit/components/jsoncpp/include",
        "/toolkit/crashreporter/google-breakpad/src",
        "/tools/profiler/core/",
        "/tools/profiler/gecko/",
        "/xpcom/base",
    ]

    if CONFIG["OS_TARGET"] == "Android":
        DEFINES["ANDROID_NDK_MAJOR_VERSION"] = CONFIG["ANDROID_NDK_MAJOR_VERSION"]
        DEFINES["ANDROID_NDK_MINOR_VERSION"] = CONFIG["ANDROID_NDK_MINOR_VERSION"]
        LOCAL_INCLUDES += [
            # We need access to Breakpad's getcontext(3) which is suitable for Android
            "/toolkit/crashreporter/google-breakpad/src/common/android/include",
        ]

    if CONFIG["MOZ_VTUNE"]:
        DEFINES["MOZ_VTUNE_INSTRUMENTATION"] = True
        UNIFIED_SOURCES += [
            "core/VTuneProfiler.cpp",
        ]

    XPCSHELL_TESTS_MANIFESTS += ["tests/xpcshell/xpcshell.toml"]
    MOCHITEST_CHROME_MANIFESTS += ["tests/chrome/chrome.toml"]
    BROWSER_CHROME_MANIFESTS += ["tests/browser/browser.toml"]
    TESTING_JS_MODULES += ["tests/ProfilerTestUtils.sys.mjs"]

UNIFIED_SOURCES += [
    "core/MicroGeckoProfiler.cpp",
    "core/ProfileAdditionalInformation.cpp",
    "core/ProfilerBindings.cpp",
    "core/ProfilerThreadRegistration.cpp",
    "core/ProfilerThreadRegistrationData.cpp",
    "core/ProfilerThreadRegistry.cpp",
    "core/ProfilerUtils.cpp",
    "gecko/ProfilerParent.cpp",
]

IPDL_SOURCES += [
    "gecko/PProfiler.ipdl",
    "gecko/ProfilerTypes.ipdlh",
]

include("/ipc/chromium/chromium-config.mozbuild")

EXPORTS += [
    "public/ChildProfilerController.h",
    "public/ETWTools.h",
    "public/GeckoProfiler.h",
    "public/MicroGeckoProfiler.h",
    "public/ProfileAdditionalInformation.h",
    "public/ProfilerBindings.h",
    "public/ProfilerControl.h",
    "public/ProfilerNativeStack.h",
    "public/ProfilerParent.h",
    "public/ProfilerRustBindings.h",
    "public/ProfilerStackWalk.h",
]

EXPORTS.mozilla += [
    "public/FlowMarkers.h",
    "public/ProfileBufferEntrySerializationGeckoExtensions.h",
    "public/ProfileJSONWriter.h",
    "public/ProfilerBandwidthCounter.h",
    "public/ProfilerCounts.h",
    "public/ProfilerLabels.h",
    "public/ProfilerMarkers.h",
    "public/ProfilerMarkersDetail.h",
    "public/ProfilerMarkersPrerequisites.h",
    "public/ProfilerMarkerTypes.h",
    "public/ProfilerRunnable.h",
    "public/ProfilerState.h",
    "public/ProfilerThreadPlatformData.h",
    "public/ProfilerThreadRegistration.h",
    "public/ProfilerThreadRegistrationData.h",
    "public/ProfilerThreadRegistrationInfo.h",
    "public/ProfilerThreadRegistry.h",
    "public/ProfilerThreadSleep.h",
    "public/ProfilerThreadState.h",
    "public/ProfilerUtils.h",
]

GeneratedFile(
    "rust-api/src/gecko_bindings/profiling_categories.rs",
    script="../../mozglue/baseprofiler/build/generate_profiling_categories.py",
    entry_point="generate_rust_enums",
    inputs=["../../mozglue/baseprofiler/build/profiling_categories.yaml"],
)

CONFIGURE_SUBST_FILES += [
    "rust-api/extra-bindgen-flags",
]


if CONFIG["COMPILE_ENVIRONMENT"]:
    CbindgenHeader("profiler_ffi_generated.h", inputs=["rust-api"])

    EXPORTS.mozilla += [
        "!profiler_ffi_generated.h",
    ]

USE_LIBS += [
    "jsoncpp",
]

FINAL_LIBRARY = "xul"

if CONFIG["ENABLE_TESTS"]:
    DIRS += ["tests/gtest"]

if CONFIG["CC_TYPE"] in ("clang", "gcc"):
    CXXFLAGS += [
        "-Wno-error=stack-protector",
        "-Wno-ignored-qualifiers",  # due to use of breakpad headers
    ]

with Files("**"):
    BUG_COMPONENT = ("Core", "Gecko Profiler")
