[package]
edition = "2015"
name = "profiler_helper"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
license = "MPL-2.0"

[dependencies]
memmap2 = "0.9"
rustc-demangle = "0.1"
uuid = "1.0"

[dependencies.object]
version = "0.36.0"
optional = true
default-features = false
features = ["std", "read_core", "elf"]

[dependencies.thin-vec]
version = "0.2.1"
features = ["gecko-ffi"]

[features]
parse_elf = ["object"]
