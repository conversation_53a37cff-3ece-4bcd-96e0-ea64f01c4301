# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'js_name': 'profiler',
        'cid': '{25db9b8e-8123-4de1-b66d-8bbbedf2cdf4}',
        'contract_ids': ['@mozilla.org/tools/profiler;1'],
        'interfaces': ['nsIProfiler'],
        'type': 'nsProfiler',
        'headers': ['/tools/profiler/gecko/nsProfiler.h'],
        'init_method': 'Init',
    },
]
