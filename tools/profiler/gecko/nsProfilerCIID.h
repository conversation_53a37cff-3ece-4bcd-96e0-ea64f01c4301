/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef nsProfilerCIID_h__
#define nsProfilerCIID_h__

#define NS_PROFILER_CID                              \
  {                                                  \
    0x25db9b8e, 0x8123, 0x4de1, {                    \
      0xb6, 0x6d, 0x8b, 0xbb, 0xed, 0xf2, 0xcd, 0xf4 \
    }                                                \
  }

#endif
