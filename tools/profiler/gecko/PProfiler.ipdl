/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

include ProfilerTypes;

namespace mozilla {

// PProfiler is a top-level protocol. It is used to let the main process
// control the Gecko Profiler in other processes, and request profiles from
// those processes.
// It is a top-level protocol so that its child endpoint can be on a
// background thread, so that profiles can be gathered even if the main thread
// is unresponsive.
[ChildImpl=virtual, ParentImpl=virtual, ChildProc=anychild]
async protocol PProfiler
{
child:
  // The unused returned value is to have a promise we can await.
  async Start(ProfilerInitParams params) returns (bool unused);
  async EnsureStarted(ProfilerInitParams params) returns (bool unused);
  async Stop() returns (bool unused);
  async Pause() returns (bool unused);
  async Resume() returns (bool unused);
  async PauseSampling() returns (bool unused);
  async ResumeSampling() returns (bool unused);

  async WaitOnePeriodicSampling() returns (bool sampled);

  async AwaitNextChunkManagerUpdate() returns (ProfileBufferChunkManagerUpdate update);
  async DestroyReleasedChunksAtOrBefore(TimeStamp timeStamp);

  // The returned shmem may contain an empty string (unavailable), an error
  // message starting with '*', or a profile as a stringified JSON object.
  async GatherProfile() returns (IPCProfileAndAdditionalInformation profileAndAdditionalInformation);
  async GetGatherProfileProgress() returns (GatherProfileProgress progress);

  async ClearAllPages();
};

} // namespace mozilla

