[DEFAULT]
head = "head.js"
support-files = ["../shared-head.js"]

["test_active_configuration.js"]
skip-if = ["tsan"] # Intermittent timeouts, bug 1781449

["test_addProfilerMarker.js"]

["test_asm.js"]
skip-if = ["os == 'linux' && os_version == '18.04' && processor == 'x86_64' && tsan"] # bug 1302198, permanent failure since GCP machine type change from n2-standard- to c2-standard-4

["test_assertion_helper.js"]

["test_enterjit_osr.js"]
skip-if = ["os == 'linux' && os_version == '18.04' && processor == 'x86_64' && tsan"] # bug 1783179, frequent failure since GCP machine type change from n2-standard- to c2-standard-4

["test_enterjit_osr_disabling.js"]
skip-if = ["!debug"]

["test_enterjit_osr_enabling.js"]
skip-if = ["!debug"]

["test_feature_cpufreq.js"]
skip-if = ["tsan"] # Times out, intermittently before (bug 1879744), perma after bug 1915433.

["test_feature_fileioall.js"]
skip-if = ["release_or_beta"]

["test_feature_java.js"]
skip-if = ["os != 'android'"]

["test_feature_js.js"]
skip-if = ["tsan"] # Times out on TSan, bug 1612707

# See the comment on test_feature_stackwalking.js

["test_feature_mainthreadio.js"]
skip-if = [
  "release_or_beta",
  "os == 'win' && socketprocess_networking",
  "tsan", # bug 1885381
]

["test_feature_nativeallocations.js"]
skip-if = [
  "os == 'android' && verify", # bug 1757528
  "asan",
  "tsan",
  "socketprocess_networking",
]

["test_feature_posix_signals.js"]
skip-if = [
  "tsan", # We have intermittent timeout issues in TSan, see Bug 1889828
  "ccov", # The signals for the profiler conflict with the ccov signals
  "os == 'win'", # Not yet supported on windows - Bug 1867328
  "os == 'android'", # Not yet supported on android - Bug 1904639
]

# Native stackwalking is somewhat unreliable depending on the platform.
#
# We don't have frame pointers on macOS release and beta, so stack walking does not
# work. See Bug 1571216 for more details.
#
# Linux can be very unreliable when native stackwalking through JavaScript code.
# See Bug 1434402 for more details.
#
# For sanitizer builds, there were many intermittents, and we're not getting much
# additional coverage there, so it's better to be a bit more reliable.

# The sanitizer checks appears to overwrite our own memory hooks in xpcshell tests,
# and no allocation markers are gathered. Skip this test in that configuration.

["test_feature_stackwalking.js"]
skip-if = [
  "os == 'mac' && release_or_beta",
  "os == 'linux' && release_or_beta && !debug",
  "asan",
  "tsan",
]

["test_get_features.js"]

["test_merged_stacks.js"]
skip-if = [
  "os == 'mac' && release_or_beta",
  "os == 'linux' && release_or_beta && !debug",
  "asan",
  "tsan",
]

["test_pause.js"]
skip-if = ["os == 'linux' && os_version == '18.04' && processor == 'x86_64' && tsan"] # bug 1783179, frequent failure since GCP machine type change from n2-standard- to c2-standard-4

["test_responsiveness.js"]
skip-if = ["tsan"] # Times out on TSan, bug 1612707

["test_run.js"]
skip-if = ["true"]

["test_shared_library.js"]

["test_start.js"]
skip-if = ["true"]
