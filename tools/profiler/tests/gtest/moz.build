# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, you can obtain one at http://mozilla.org/MPL/2.0/.

if (
    CONFIG["MOZ_GECKO_PROFILER"]
    and CONFIG["OS_TARGET"] in ("Android", "Linux")
    and CONFIG["TARGET_CPU"]
    in (
        "arm",
        "aarch64",
        "x86_64",
    )
):
    UNIFIED_SOURCES += [
        "LulTest.cpp",
        "LulTestDwarf.cpp",
        "LulTestInfrastructure.cpp",
    ]

LOCAL_INCLUDES += [
    "/netwerk/base",
    "/netwerk/protocol/http",
    "/toolkit/components/jsoncpp/include",
    "/tools/profiler/core",
    "/tools/profiler/gecko",
    "/tools/profiler/lul",
]

# Bug 1885381 - Hangs/timeouts under TSAN
if (
    CONFIG["OS_TARGET"] != "Android"
    and not CONFIG["MOZ_TSAN"]
    # Bug 1930156 - Frequent OOMs on 32-bit platforms.
    and CONFIG["TARGET_CPU"] != "x86"
):
    UNIFIED_SOURCES += [
        "GeckoProfiler.cpp",
        "ThreadProfileTest.cpp",
    ]

USE_LIBS += [
    "jsoncpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul-gtest"
