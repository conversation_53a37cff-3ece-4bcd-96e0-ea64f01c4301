[DEFAULT]
support-files = [
  "../shared-head.js",
  "head.js",
]
skip-if = ["tsan"] # Bug 1804081 - TSan times out on pretty much all of these tests

["browser_test_feature_ipcmessages.js"]
support-files = ["simple.html"]

["browser_test_feature_jsallocations.js"]
support-files = ["do_work_500ms.html"]

["browser_test_feature_jstracing.js"]
support-files = ["tracing.html"]
skip-if = [
  # This features is only enabled on nightly via MOZ_EXECUTION_TRACING build flag
  "!nightly_build"
]

["browser_test_feature_multiprocess_capture_with_signal.js"]
support-files = ["do_work_500ms.html"]
skip-if = [
  # Enabling the JS feature leaks an 8-byte object. This causes failures when
  # the leak checker is enabled, and run with test-verify (i.e. with both
  # "debug" and "verify"). Having JS support for this feature is more important
  # than these tests, so we're disabling them for now. See Bug 1904897.
  "verify && debug",
  "ccov",            # The signals for the profiler conflict with the ccov signals
  "os == 'win'",     # Not yet supported on windows - Bug 1867328
]

["browser_test_feature_nostacksampling.js"]
support-files = ["do_work_500ms.html"]

["browser_test_marker_network_cancel.js"]
support-files = ["simple.html"]

["browser_test_marker_network_private_browsing.js"]
support-files = ["simple.html"]

["browser_test_marker_network_redirect.js"]
support-files = [
  "redirect.sjs",
  "simple.html",
  "page_with_resources.html",
  "firefox-logo-nightly.svg",
]

["browser_test_marker_network_serviceworker_cache_first.js"]
support-files = [
  "serviceworkers/serviceworker-utils.js",
  "serviceworkers/serviceworker_register.html",
  "serviceworkers/serviceworker_page.html",
  "serviceworkers/firefox-logo-nightly.svg",
  "serviceworkers/serviceworker_cache_first.js",
]

["browser_test_marker_network_serviceworker_no_fetch_handler.js"]
support-files = [
  "serviceworkers/serviceworker-utils.js",
  "serviceworkers/serviceworker_register.html",
  "serviceworkers/serviceworker_page.html",
  "serviceworkers/firefox-logo-nightly.svg",
  "serviceworkers/serviceworker_no_fetch_handler.js",
]

["browser_test_marker_network_serviceworker_no_respondWith_in_fetch_handler.js"]
support-files = [
  "serviceworkers/serviceworker-utils.js",
  "serviceworkers/serviceworker_register.html",
  "serviceworkers/serviceworker_page.html",
  "serviceworkers/firefox-logo-nightly.svg",
  "serviceworkers/serviceworker_no_respondWith_in_fetch_handler.js",
]

["browser_test_marker_network_serviceworker_synthetized_response.js"]
support-files = [
  "serviceworkers/serviceworker-utils.js",
  "serviceworkers/serviceworker_register.html",
  "serviceworkers/serviceworker_simple.html",
  "serviceworkers/firefox-logo-nightly.svg",
  "serviceworkers/serviceworker_synthetized_response.js",
]

["browser_test_marker_network_simple.js"]
support-files = ["simple.html"]

["browser_test_marker_network_sts.js"]
support-files = ["simple.html"]

["browser_test_markers_gc_cc.js"]

["browser_test_markers_parent_process.js"]

["browser_test_markers_preferencereads.js"]
support-files = ["single_frame.html"]

["browser_test_profile_capture_by_pid.js"]
support-files = ["single_frame.html"]

["browser_test_profile_fission.js"]
support-files = ["single_frame.html"]

["browser_test_profile_multi_frame_page_info.js"]
support-files = [
  "multi_frame.html",
  "single_frame.html",
]

["browser_test_profile_single_frame_page_info.js"]
support-files = ["single_frame.html"]

["browser_test_profile_slow_capture.js"]
support-files = ["single_frame.html"]
run-if = ["debug"]
