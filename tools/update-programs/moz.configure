# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


# Spoof a stub of `js/moz.configure` for the included scripts.
@dependable
def js_standalone():
    return False


@depends(target)
def fold_libs(target):
    return target.os in ("WINNT", "OSX", "iOS", "Android")


set_config("MOZ_FOLD_LIBS", fold_libs)


include("../../build/moz.configure/rust.configure")
include("../../build/moz.configure/nspr.configure")
include("../../build/moz.configure/nss.configure")
include("../../build/moz.configure/update-programs.configure")
