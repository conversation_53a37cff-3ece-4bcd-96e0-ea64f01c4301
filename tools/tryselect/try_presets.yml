---
# Presets defined here will be available to all users. Run them with:
#   $ mach try --preset <name>
#
# If editing this file, make sure to run:
#   $ mach python-test tools/tryselect/test/test_presets.py
#
# Descriptions are required. Please keep this in alphabetical order.

# yamllint disable rule:line-length

android-components:
    selector: fuzzy
    description: >-
        Run android-components builds and tests.
    query:
        - "'build-components"
        - "'test-components"
        - "^source-test 'android-components"

android-geckoview:
    selector: fuzzy
    full: true
    description: >-
        Run android-geckoview builds and tests.
    query:
        # Lint
        - "^source-test-mozlint android-lints$ | eslint$ | rejected-words$ | file-perm$ | file-whitespace$"
        # Miscellaneous Tests
        - "^test-android !shippable '64 'gtest | 'cpp | junit-fis"
        # Mochitests
        - "^test-android !shippable !cf !gli !iso      'em 'media | 'plain"
        - "^test-android !shippable !cf !gli !iso !swr 'hw 'media 'debug"
        - "^test-android !shippable !cf !gli !iso !swr 'hw 'mochitest-webgl 'nofis"
        # Reftests
        - "^test-android !shippable !iso 'geckoview-crashtest | 'geckoview-reftest 'nofis"
        # Web Platform Tests
        - "^test-android !shippable !headless 'wdspec"
        - "^test-android !shippable       !cf !iso            'web-platform-tests-crashtest 'debug"
        - "^test-android !shippable       !cf !iso       !swr 'web-platform-tests-crashtest"
        - "^test-android !shippable !back !cf !iso !ship !swr 'web-platform-tests-nofis"
        - "^test-android !shippable !back !cf !iso            'web-platform-tests-reftest 'nofis 'debug"
        - "^test-android !shippable !back !cf !iso       !swr 'web-platform-tests-reftest 'nofis"
        # XPCShell
        - "^test-android !shippable !iso 'xpcshell"

builds:
    selector: fuzzy
    description: >-
        Run builds without any of the extras.
    query:
        - "^build- !fuzzing !notarization !reproduced !rusttests !signing !upload-symbols !components !apk !bundle"

builds-debug:
    selector: fuzzy
    description: >-
        Run the bare minimum of debug build jobs to ensure builds work on
        all tier-1 platforms.
    query:
        - "^build- 'debug !fuzzing !rusttests !signing !plain !asan !tsan !noopt !toolchain !upload-symbols !apk !bundle"

builds-debugopt:
    selector: fuzzy
    description: >-
        Run the bare minimum of debug and opt build jobs to ensure builds work on
        all tier-1 platforms.
    query:
        - "^build- !fuzzing !rusttests !signing !plain !asan !tsan !noopt !toolchain !upload-symbols !components !apk !bundle"

desktop-frontend:
    description: >-
        Run mochitest-browser, xpcshell, mochitest-chrome, mochitest-a11y,
        marionette, firefox-ui-functional on all desktop platforms.
        Excludes non-shipped/duplicate configurations like asan/tsan/msix
        to reduce the runtime of the push as well as infra load.
        Use with --artifact to speed up your trypush.
        If this is green, you can be 99% sure that any frontend change will
        stick on central.
    selector: fuzzy
    query:
        # Runs desktop frontend-tests. Tries to avoid running
        # asan/tsan because they're not available as artifact builds, and
        # rarely offer different results from debug/opt. It also avoids running
        # msix/swr/a11y-checks/gpu/nofis/headless variants of otherwise
        # identical tests, as again those are unlikely to show different
        # results for frontend-only changes.
        # This won't run 32-bit debug tests, which seems an acceptable
        # trade-off for query complexity + runtime on infrastructure.
        - "'browser-chrome                 !spi !asan !tsan !msix !a11y !swr | 'linux"
        - "'mochitest-chrome               !spi !asan !tsan             !swr !gpu"
        - "'xpcshell                       !spi !asan !tsan !msix !nofis !condprof !android"
        - "'browser-a11y | 'mochitest-a11y !spi !asan !tsan !no-cache   !swr"
        - "'marionette                          !asan       !source !headless !swr"
        - "'firefox-ui-functional               !asan !tsan"

devtools:
    selector: fuzzy
    description: >-
        Runs the tests relevant to the Firefox Devtools
    query:
        - "'node-debugger | 'node-devtools"
        - "'mozlint-eslint"
        # Windows: skip jobs on asan and 32 bits platforms
        - "'mochitest-devtools-chrome | 'mochitest-chrome-1proc 'windows !asan !-32"
        # macos: no extra platform to filter out
        - "'mochitest-devtools-chrome | 'mochitest-chrome-1proc 'macosx"
        # Linux is being named "linux1804" and may change over time, so use a more flexible search
        - "'mochitest-devtools-chrome | 'mochitest-chrome-1proc 'linux '64-qr/ !swr"
        - "'xpcshell 'linux !nofis '64-qr/"

devtools-linux:
    selector: fuzzy
    description: >-
        Runs the tests relevant to the Firefox Devtools, on Linux only.
    query:
        - "'node-debugger | 'node-devtools"
        - "'mozlint-eslint"
        - "'mochitest-devtools-chrome | 'mochitest-chrome-1proc 'linux '64-qr/ !swr"
        - "'xpcshell 'linux !nofis '64-qr/"

fenix:
    selector: fuzzy
    description: >-
        Run Fenix builds and tests.
    query:
        - "'build-apk-fenix-debug"
        - "'signing-apk-fenix-debug"
        - "'build-apk-fenix-android-test-debug"
        - "'signing-apk-fenix-android-test-debug"
        - "'test-apk-fenix-debug"
        - "'ui-test-apk-fenix-arm-debug"
        - "^source-test 'fenix"

firefox-android:
    selector: fuzzy
    description: >-
        Run android-components, Focus, and Fenix builds and tests.
    query:
        - "'build-components"
        - "'test-components"
        - "'build-apk-fenix-debug"
        - "'signing-apk-fenix-debug"
        - "'build-apk-fenix-android-test-debug"
        - "'signing-apk-fenix-android-test-debug"
        - "'test-apk-fenix-debug"
        - "'ui-test-apk-fenix-arm-debug"
        - "'build-apk-focus-debug"
        - "'signing-apk-focus-debug"
        - "'build-apk-focus-android-test-debug"
        - "'signing-apk-focus-android-test-debug"
        - "'test-apk-focus-debug"
        - "'ui-test-apk-focus-arm-debug"
        - "^source-test 'android | 'fenix | 'focus"

focus:
    selector: fuzzy
    description: >-
        Run Focus builds and tests.
    query:
        - "'build-apk-focus-debug"
        - "'signing-apk-focus-debug"
        - "'build-apk-focus-android-test-debug"
        - "'signing-apk-focus-android-test-debug"
        - "'test-apk-focus-debug"
        - "'ui-test-apk-focus-arm-debug"
        - "^source-test 'focus"

fpush-linux-android:
    selector: fuzzy
    description: >-
        Runs correctness test suites on Linux and Android emulator platforms, as
        well as builds across main platforms. The resulting jobs on TreeHerder
        used to end up looking like a "F" shape (not so much these days) and so
        this is typically referred to as an F-push. This is useful to do as a
        general sanity check on changes to cross-platform Gecko code where you
        unsure of what tests might be affected. Linux and Android (emulator)
        test coverage are relatively cheap to run and cover a lot of the
        codebase, while the builds on other platforms catch compilation problems
        that might result from only building locally on one platform.
    query:
        - "'test-linux1804 'debug- !-shippable !-asan"
        - "'test-android-em 'debug"
        - "^build !-shippable !-signing !-asan !-fuzzing !-rusttests !-base-toolchain !-aar- !components !apk !bundle"

geckodriver:
    selector: fuzzy
    description: >-
        Runs the tests relevant to geckodriver, which implements the WebDriver
        specification. This preset can be filtered down further to limit it to
        a specific platform or other tasks only. For example:
        |mach try --preset geckodriver -xq "'linux"|
    query:
        - "'rusttests"
        - "'platform 'wdspec 'debug 'nofis"
        - "'browsertime 'amazon 'shippable 'firefox 'nofis"

layout:
    selector: fuzzy
    description: >-
        Runs the tests most relevant to layout.
        This preset can be filtered down further to limit it to
        a specific platform or build configuration. For example:
        |mach try --preset layout -xq "linux64 'opt"|
    query:
        # Most mochitest + reftest + crashtest + wpt
        - "!asan !tsan !jsreftest !shippable !webgl !condprof !media !webgpu 'mochitest | 'web-platform | 'crashtest | 'reftest"
        # Style system unit tests
        - "'rusttests"

media-full:
    selector: fuzzy
    description: >-
        Runs tests that exercise media playback and WebRTC code.
    query:
        - "mochitest-media !dfpi !nofis"
        - "mochitest-media android !spi !swr !lite"
        - "mochitest-browser-chrome !dfpi !nofis !a11y"
        - "mochitest-browser-media"
        - "web-platform-tests !dfpi !nofis !shippable"
        - "web-platform-tests android !wdspec !spi !swr !lite"
        - "crashtest !wdspec !nofis"
        - "crashtest android !wdspec !spi !swr !lite"
        - "'gtest"

mochitest-bc:
    description: >-
        Runs mochitest-browser-chrome on all Desktop platforms in both opt
        and debug. Excludes jobs that require non-artifact builds (asan,
        tsan, msix, etc.) and some non-default configurations. For frontend
        only changes, use this with --artifact to speed up your trypushes.
    query:
        - "'browser-chrome !tsan !asan !msix !spi !a11y !swr | 'linux"
    selector: fuzzy

os_integration:
    selector: fuzzy
    description: >-
        run tests that have os_integration tag.
    env:
        - MOZHARNESS_TEST_TAG=["os_integration"]
    query:
        - "'source-test-python"
        - "'opt-talos- !swr !nightly !damp   'xperf | 'other | 'webgl"
        - "'perftest        !nightly         'service-worker | 'startup-geckoview"
        - "'browsertime     !nightly         speedometer3"
        - "'browsertime     !nightly         firefox billgates-ama"
        - "'browsertime     !nightly         firefox playback-widevine"
        - "'browsertime     !nightly         tp6-essential-firefox amazon"
        - "'marionette      !-cf        !unit   !64-qr/opt !64/opt !2009-qr/opt"
        - "'mochitest-      !-cf        !webgl !-gpu !devtools !dfpi !nofis !a11y !trans !wmfme !64-qr/opt !64/opt !2009-qr/opt"
        - "'mochitest-webgl1-core !-cf  !64-qr/opt !64/opt !2009-qr/opt"
        - "'xpcshell        !64-qr/opt !64/opt !2009-qr/opt"

perf:
    selector: fuzzy
    description: >-
        Runs all performance (raptor and talos) tasks across all platforms.
        This preset can be filtered down further (e.g to limit it to a specific
        platform) via |mach try --preset perf -xq "'windows"|.

        Android hardware platforms are excluded due to resource limitations.
    query:
        - "^test- !android-hw 'raptor | 'talos"
    rebuild: 5

perf-chrome:
    description: >-
        Runs the talos tests most likely to change when making a change to
        the browser chrome. This skips a number of talos jobs that are unlikely
        to be affected in order to conserve resources.
    query:
        - "opt-talos- 'chrome | 'svg | 'session | 'tabswitch | 'other | 'g5"
    rebuild: 6
    selector: fuzzy

remote-protocol:
    selector: fuzzy
    description: >-
        Runs the tests relevant to the Remote protocol, which underpins
        many test harnesses as well as our CDP and WebDriver implementations.
        This preset can be filtered down further to limit it to a specific
        platform or to opt/debug tasks only. For example:
        |mach try --preset remote-protocol -xq "'linux 'opt"|
    query:
        - "'awsy-base"
        - "'firefox-ui"
        - "'marionette !swr | harness"
        - "'mochitest-browser !spi !swr !nofis '-1$"
        - "'mochitest-remote !spi !swr"
        - "'platform 'reftest !swr !nofis | 'android !-lite -1$"
        - "'platform 'wdspec !swr"
        - "'platform !reftest !wdspec !swr !nofis | 'android !-lite -1$"
        - "'puppeteer"
        - "'reftest !platform !gpu !swr !no-accel !nofis | 'android !-lite -1$"
        - "'xpcshell !spi !tsan !-lite"

sample-suites:
    selector: fuzzy
    description: >-
        Runs one chunk of every test suite plus all suites that aren't chunked.
        It is useful for testing infrastructure changes that can affect the
        harnesses themselves but are unlikely to break specific tests.
    query:
        - ^test- -1$
        # Only run a single talos + raptor suite per platform
        - ^test- !1$ !2$ !3$ !4$ !5$ !6$ !7$ !8$ !9$ !0$ !raptor !talos !components !apk
        - ^test- 'raptor-speedometer | 'talos-g1

sm-shell-all:
    selector: fuzzy
    description: <-
        Runs a set of tests aimed to give a reasonable level of confidence for
        basic SpiderMonkey changes (shell only), all platforms
    query:
        - "'spidermonkey | 'shell-haz"
        - "!shippable !android 'jittest"  # macosx64 jittests

sm-shell:
    selector: fuzzy
    description: <-
        Runs a set of tests aimed to give a reasonable level of confidence for
        basic SpiderMonkey changes (shell only) (linux only)
    query:
        - "!win !osx 'spidermonkey | 'shell-haz"


sm-all:
    selector: fuzzy
    description: <-
        Runs a set of tests aimed to give a reasonable level of confidence for
        basic SpiderMonkey changes, including those that would require a
        browser build.
    query:
        - "'spidermonkey | 'hazard"
        - "!android !asan !shippable 'xpcshell"
        - "!android !asan !shippable 'jsreftest"
        - "!shippable !android 'jittest"  # macosx64 jittests

webextensions:
    selector: fuzzy
    description: <-
        Runs most of the unit tests of WebExtension code across all desktop
        platforms and Android, including mochitests, xpcshell and test-verify.
        GeckoView JUnit tests are NOT run.
    paths:  # must be duplicate of test_paths, see bug 1556445
        - browser/components/extensions/test/
        - browser/base/content/test/webextensions/
        - mobile/shared/components/extensions/test/
        - toolkit/components/extensions/test/
        - toolkit/mozapps/extensions/test/
    test_paths:  # must be duplicate of paths, see bug 1556445
        - browser/components/extensions/test/
        - browser/base/content/test/webextensions/
        - mobile/shared/components/extensions/test/
        - toolkit/components/extensions/test/
        - toolkit/mozapps/extensions/test/
    query:
        - "'64-qr/ | 'windows11-64-2009-qr !wpt !gpu !msix"

webgpu:
    selector: fuzzy
    description: >-
        Runs the tests relevant to WebGPU.
    query:
        - "'webgpu"
        - "source-test-mozlint-updatebot"
        - "source-test-vendor-rust"

webrender:
    selector: fuzzy
    description: >-
        Runs the conformance tests relevant to WebRender.
    query:
        - "!talos !raptor !shippable !asan '-qr !components"
        - "^webrender-"

webrender-reftests:
    selector: fuzzy
    description: >-
        Runs the reftests relevant to WebRender.
    query:
        - "!talos !raptor !shippable !asan !nofis 'reftest"

webrender-reftests-linux:
    selector: fuzzy
    description: >-
        Runs the reftests relevant to WebRender on linux only.
    query:
        - "!talos !raptor !shippable !asan !nofis 'linux 'reftest"

webrender-perf:
    selector: fuzzy
    description: >-
        Runs the performance tests relevant to WebRender.
    query:
        - "'-qr 'svgr"
        - "'-qr 'g1"
        - "'-qr 'g4"
        - "'-qr 'tp5"
        - "'-qr 'talos-webgl"
        - "'-qr 'motionmark-animometer"
