/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

"use strict";

/**
 * This is the current list of rollouts for ESLint rules in mozilla-central. The
 * meta bug for the rollouts is https://bugzilla.mozilla.org/show_bug.cgi?id=1596191
 *
 * New exclusions SHOULD NOT be added to this list, unless they are part of a
 * rollout for a new rule, or otherwise agreed with the JavaScript usage, tools,
 * and style team: https://firefox-source-docs.mozilla.org/mots/index.html#javascript-usage-tools-and-style
 *
 * See https://firefox-source-docs.mozilla.org/code-quality/lint/linters/eslint/enabling-rules.html#enabling-a-new-rule
 * for more information about roll-outs.
 */
const rollouts = [
  {
    files: [
      // Bug 1773475 - For now, turn off no-unresolved on some paths where we import
      // from node_modules, as the ESLint setup only installs modules at the
      // top-level.
      "devtools/shared/compatibility/**",
      "browser/components/storybook/**",
    ],
    rules: {
      "import/no-unresolved": "off",
    },
  },
  {
    files: ["*.html", "*.xhtml", "*.xml"],
    rules: {
      // Curly brackets are required for all the tree via recommended.js,
      // however these files aren't auto-fixable at the moment.
      curly: "off",
    },
  },
  {
    // TODO: Bug 1515949. Enable no-undef for gfx/
    files: "gfx/layers/apz/test/mochitest/**",
    rules: {
      "no-undef": "off",
    },
  },
  {
    // Bug 881389 - Complete switching to console.createInstance from custom
    // modules. To support the gradual switch, we log these as warnings until
    // they have been transitioned.
    files: [
      "browser/base/content/browser-data-submission-info-bar.js",
      "browser/base/content/browser-sync.js",
      "browser/base/content/browser.js",
      "browser/components/BrowserGlue.sys.mjs",
      "browser/components/attribution/AttributionCode.sys.mjs",
      "browser/components/customizableui/**",
      "browser/components/downloads/**",
      "browser/components/enterprisepolicies/**",
      "browser/components/firefoxview/**",
      "browser/components/migration/**",
      "browser/components/protocolhandler/WebProtocolHandlerRegistrar.sys.mjs",
      "browser/components/shell/ShellService.sys.mjs",
      "browser/components/syncedtabs/TabListComponent.sys.mjs",
      "browser/components/uitour/UITour.sys.mjs",
      "browser/tools/mozscreenshots/mozscreenshots/extension/Screenshot.sys.mjs",
      "devtools/client/webconsole/test/**",
      "devtools/shared/tests/xpcshell/test_console_filtering.js",
      "devtools/shared/webconsole/test/chrome/test_consoleapi_innerID.html",
      "mobile/android/modules/geckoview/AndroidLog.sys.mjs",
      "mobile/shared/modules/geckoview/GeckoViewUtils.sys.mjs",
      "remote/shared/**",
      "security/manager/ssl/RemoteSecuritySettings.sys.mjs",
      "services/**",
      "testing/mochitest/api.js",
      "toolkit/components/backgroundtasks/**",
      "toolkit/components/crashes/**",
      "toolkit/components/enterprisepolicies/**",
      "toolkit/components/extensions/**",
      "toolkit/components/formautofill/**",
      "toolkit/components/messaging-system/**",
      "toolkit/components/normandy/**",
      "toolkit/components/places/**",
      "toolkit/components/printing/**",
      "toolkit/components/taskscheduler/**",
      "toolkit/components/telemetry/**",
      "toolkit/components/utils/JsonSchemaValidator.sys.mjs",
      "toolkit/modules/AsanReporter.sys.mjs",
      "toolkit/modules/GMPInstallManager.sys.mjs",
      "toolkit/modules/OSKeyStore.sys.mjs",
      "toolkit/modules/ProfileAge.sys.mjs",
      "toolkit/modules/tests/xpcshell/test_Log*.js",
      "toolkit/mozapps/defaultagent/BackgroundTask_defaultagent.sys.mjs",
      "toolkit/mozapps/extensions/**",
      "toolkit/mozapps/update/**",
    ],
    rules: {
      "mozilla/use-console-createInstance": "off",
    },
  },
  {
    // Bug 1881270 - Gradually roll-out no-case-declarations to more areas.
    files: [
      "accessible/tests/mochitest/promisified-events.js",
      "browser/actors/**",
      "browser/base/content/**",
      "browser/components/**",
      "browser/modules/BrowserUsageTelemetry.sys.mjs",
      "browser/modules/URILoadingHelper.sys.mjs",
      "devtools/client/**",
      "devtools/server/**",
      "devtools/shared/layout/dom-matrix-2d.js",
      "devtools/startup/DevToolsStartup.sys.mjs",
      "docshell/test/navigation/file_blockBFCache.html",
      "docshell/test/navigation/test_bug1375833.html",
      "docshell/test/unit/AllowJavascriptParent.sys.mjs",
      "dom/base/test/chrome/window_nsITextInputProcessor.xhtml",
      "dom/html/test/forms/test_input_sanitization.html",
      "dom/media/PeerConnection.sys.mjs",
      "dom/media/autoplay/test/mochitest/test_autoplay_policy_web_audio_AudioParamStream.html",
      "dom/payments/test/PayerDetailsChromeScript.js",
      "dom/payments/test/simple_payment_request.html",
      "dom/security/test/referrer-policy/browser_referrer_disallow_cross_site_relaxing.js",
      "dom/tests/mochitest/bugs/test_bug622361.html",
      "dom/workers/test/sourcemap_header_debugger.js",
      "gfx/layers/apz/test/mochitest/**",
      "intl/locale/tests/unit/test_localeService.js",
      "layout/tools/layout-debug/LayoutDebugChild.sys.mjs",
      "layout/tools/reftest/reftest.sys.mjs",
      "mobile/android/modules/geckoview/**",
      "mobile/shared/actors/**",
      "mobile/shared/components/geckoview/GeckoViewStartup.sys.mjs",
      "mobile/shared/modules/geckoview/**",
      "netwerk/test/browser/browser_test_data_channel_observer.js",
      "netwerk/test/unit/test_proxyconnect.js",
      "remote/**",
      "services/fxaccounts/**",
      "services/sync/**",
      "testing/mochitest/ShutdownLeaksCollector.sys.mjs",
      "testing/specialpowers/content/SpecialPowers*.sys.mjs",
      "toolkit/actors/**",
      "toolkit/components/**",
      "toolkit/modules/**",
      "toolkit/mozapps/downloads/DownloadLastDir.sys.mjs",
      "toolkit/mozapps/extensions/**",
      "toolkit/mozapps/update/UpdateListener.sys.mjs",
      "toolkit/content/widgets/**",
      "uriloader/exthandler/tests/mochitest/browser_download_open_with_internal_handler.js",
      "widget/tests/file_test_ime_state_on_focus_move.js",
    ],
    rules: {
      "no-case-declarations": "warn",
    },
  },
  {
    // Bug 1881268 - Gradually roll-out no-constant-condition to more areas.
    files: [
      "accessible/tests/browser/mac/browser_attributed_text.js",
      "accessible/tests/mochitest/text.js",
      "browser/base/content/test/general/browser_bug734076.js",
      "browser/base/content/test/static/**",
      "browser/components/BrowserContentHandler.sys.mjs",
      "browser/components/extensions/test/browser/browser_ext_slow_script.js",
      "browser/components/places/content/places-tree.js",
      "browser/components/translations/tests/browser/browser_translations_panel_fuzzing.js",
      "browser/components/translations/tests/browser/browser_translations_full_page_panel_fuzzing.js",
      "browser/components/urlbar/UrlbarUtils.sys.mjs",
      "browser/components/urlbar/tests/browser/browser_copying.js",
      "devtools/client/dom/test/head.js",
      "devtools/client/framework/browser-toolbox/test/helpers-browser-toolbox.js",
      "devtools/client/fronts/inspector/rule-rewriter.js",
      "devtools/client/inspector/**",
      "devtools/client/netmonitor/**",
      "devtools/client/performance-new/test/browser/helpers.js",
      "devtools/client/shared/**",
      "devtools/client/webconsole/**",
      "devtools/server/actors/**",
      "devtools/server/socket/websocket-server.js",
      "devtools/shared/css/parsing-utils.js",
      "devtools/shared/inspector/css-logic.js",
      "devtools/shared/tests/xpcshell/test_csslexer.js",
      "docshell/test/mochitest/test_bug529119-1.html",
      "docshell/test/mochitest/test_bug529119-2.html",
      "docshell/test/navigation/file_sessionhistory_iframe_removal.html",
      "docshell/test/navigation/test_online_offline_bfcache.html",
      "dom/base/test/chrome/window_nsITextInputProcessor.xhtml",
      "dom/base/test/fullscreen/file_fullscreen-bug-1798219.html",
      "dom/base/test/unit/test_isequalnode.js",
      "dom/filesystem/tests/script_fileList.js",
      "dom/media/mediasource/test/test_Eviction_mp4.html",
      "dom/media/mediasource/test/test_ExperimentalAsync.html",
      "dom/media/webspeech/recognition/test/test_online_http.html",
      "dom/media/webspeech/recognition/test/test_online_http_webkit.html",
      "dom/streams/test/xpcshell/large-pipeto.js",
      "dom/webtransport/test/xpcshell/test_simple_stream.js",
      "dom/xhr/tests/terminateSyncXHR_worker.js",
      "editor/libeditor/tests/test_contenteditable_text_input_handling.html",
      "editor/libeditor/tests/test_selection_move_commands.html",
      "gfx/layers/apz/test/mochitest/**",
      "gfx/layers/layerviewer/layerTreeView.js",
      "intl/uconv/tests/unit/test_charset_conversion.js",
      "js/src/builtin/**",
      "layout/inspector/tests/test_getMatchingCSSRules.html",
      "layout/inspector/tests/test_is_valid_css_color.html",
      "layout/style/test/property_database.js",
      "layout/style/test/test_computed_style_grid_with_pseudo.html",
      "layout/style/test/test_visited_reftests.html",
      "mobile/android/geckoview/src/androidTest/assets/www/getusermedia_xorigin_container.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_events.html",
      "modules/libjar/test/unit/test_fault_handler.js",
      "netwerk/test/httpserver/httpd.sys.mjs",
      "netwerk/test/unit/**",
      "remote/cdp/domains/content/runtime/ExecutionContext.sys.mjs",
      "remote/cdp/test/browser/page/browser_printToPDF.js",
      "remote/marionette/actors/MarionetteCommandsParent.sys.mjs",
      "remote/shared/messagehandler/transports/RootTransport.sys.mjs",
      "services/settings/Attachments.sys.mjs",
      "storage/test/unit/test_storage_service.js",
      "testing/talos/talos/pageloader/chrome/report.js",
      "testing/talos/talos/**",
      "toolkit/components/asyncshutdown/tests/xpcshell/head.js",
      "toolkit/components/extensions/NativeMessaging.sys.mjs",
      "toolkit/components/extensions/test/xpcshell/test_ext_i18n.js",
      "toolkit/components/extensions/test/xpcshell/test_ext_i18n_css.js",
      "toolkit/components/formautofill/default/FormAutofillPrompter.sys.mjs",
      "toolkit/components/pictureinpicture/tests/browser_videoEmptied.js",
      "toolkit/components/printing/tests/browser_print_stream.js",
      "toolkit/components/telemetry/tests/unit/test_ThirdPartyModulesPing.js",
      "toolkit/components/terminator/tests/xpcshell/test_terminator_record.js",
      "toolkit/content/aboutwebrtc/aboutWebrtc.mjs",
      "toolkit/content/tests/widgets/tree_shared.js",
      "toolkit/content/widgets/tabbox.js",
      "toolkit/crashreporter/test/browser/crashreport.sjs",
      "toolkit/modules/CertUtils.sys.mjs",
      "toolkit/mozapps/downloads/tests/unit/test_DownloadUtils.js",
      "toolkit/mozapps/extensions/internal/XPIInstall.sys.mjs",
      "tools/profiler/tests/**",
      "uriloader/exthandler/tests/mochitest/browser_save_filenames.js",
      "widget/tests/browser/browser_test_AZERTY_digit_shortcut.js",
      "widget/tests/window_composition_text_querycontent.xhtml",
    ],
    rules: { "no-constant-condition": "warn" },
  },
  {
    files: [
      "browser/actors/AboutPocketParent.sys.mjs",
      "browser/actors/SpeechDispatcherParent.sys.mjs",
      "browser/base/content/browser-sync.js",
      "browser/components/BrowserContentHandler.sys.mjs",
      "browser/components/enterprisepolicies/Policies.sys.mjs",
      "browser/components/messagepreview/actors/AboutMessagePreviewChild.sys.mjs",
      "browser/components/messagepreview/actors/AboutMessagePreviewParent.sys.mjs",
      "browser/components/migration/ChromeMigrationUtils.sys.mjs",
      "browser/components/migration/SafariProfileMigrator.sys.mjs",
      "browser/components/places/content/places-tree.js",
      "browser/components/places/content/treeView.js",
      "browser/components/tabbrowser/content/tabbrowser.js",
      "browser/extensions/screenshots/build/shot.js",
      "browser/extensions/webcompat/**",
      "browser/modules/BackgroundTask_*.sys.mjs",
      "browser/themes/BuiltInThemes.sys.mjs",
      "browser/tools/mozscreenshots/mozscreenshots/extension/lib/mozscreenshots-script.js",
      "browser/tools/mozscreenshots/mozscreenshots/extension/lib/mozscreenshots.html",
      "devtools/client/**",
      "devtools/server/actors/inspector/css-logic.js",
      "devtools/shared/compatibility/bin/update.js",
      "devtools/shared/discovery/discovery.js",
      "devtools/shared/protocol/Front.js",
      "dom/media/webvtt/update-webvtt.js",
      "gfx/layers/layerviewer/layerTreeView.js",
      "layout/tools/reftest/reftest-analyzer.xhtml",
      "mobile/android/geckoview/src/androidTest/**",
      "mobile/android/android-components/components/feature/webcompat/**",
      "services/automation/ServicesAutomation.sys.mjs",
      "services/settings/RemoteSettings.worker.mjs",
      "services/sync/modules/SyncDisconnect.sys.mjs",
      "taskcluster/docker/index-task/insert-indexes.js",
      "testing/**",
      "toolkit/actors/NetErrorParent.sys.mjs",
      "toolkit/components/aboutcheckerboard/content/aboutCheckerboard.js",
      "toolkit/components/backgroundtasks/BackgroundTask_message.sys.mjs",
      "toolkit/components/backgroundtasks/BackgroundTasksTestUtils.sys.mjs",
      "toolkit/components/credentialmanagement/IdentityCredentialPromptService.sys.mjs",
      "toolkit/components/ml/content/MLEngine.worker.mjs",
      "toolkit/components/telemetry/pings/BackgroundTask_pingsender.sys.mjs",
      "toolkit/components/translations/actors/TranslationsParent.sys.mjs",
      "toolkit/components/translations/content/translations-engine.worker.js",
      "toolkit/components/xulstore/XULStore.sys.mjs",
      "toolkit/content/aboutTelemetry.js",
      "toolkit/content/customElements.js",
      "toolkit/content/widgets/dialog.js",
      "toolkit/content/widgets/menu.js",
      "toolkit/mozapps/update/BackgroundUpdate.sys.mjs",
    ],
    rules: {
      "no-console": "off",
    },
  },
  {
    // Bug 877389 - Gradually migrate from Cu.reportError to console.error.
    // Enable these as we fix more areas.
    files: [
      "dom/push/test/mockpushserviceparent.js",
      "browser/components/extensions/**",
      "toolkit/components/extensions/**",
      "toolkit/mozapps/extensions/**",
    ],
    rules: {
      "mozilla/no-cu-reportError": "off",
    },
  },
  {
    files: ["**"],
    excludedFiles: [
      "accessible/tests/**",
      "browser/actors/**",
      "browser/base/content/**",
      "browser/components/Browser*",
      "browser/components/aboutlogins/**",
      "browser/components/aboutwelcome/**",
      "browser/components/asrouter/**",
      "browser/components/attribution/**",
      "browser/components/customizableui/**",
      "browser/components/downloads/**",
      "browser/components/newtab/**",
      "browser/components/originattributes/test/browser/**",
      "browser/components/pocket/content/pkt*",
      "browser/components/preferences/**",
      "browser/components/privatebrowsing/**",
      "browser/components/safebrowsing/content/test/**",
      "browser/components/screenshots/**",
      "browser/components/sessionstore/**",
      "browser/components/storybook/.storybook/**",
      "browser/components/tabbrowser/**",
      "browser/components/tests/browser/**",
      "browser/extensions/screenshots/**",
      "browser/modules/**",
      "devtools/**",
      "docshell/base/URIFixup.sys.mjs",
      "dom/**",
      "editor/**",
      "extensions/permissions/test/PermissionTestUtils.sys.mjs",
      "gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js",
      "image/test/**",
      "intl/**",
      "js/xpconnect/tests/**",
      "layout/**",
      "mobile/android/**",
      "mobile/shared/**",
      "modules/**",
      "netwerk/**",
      "parser/htmlparser/**",
      "services/**",
      "storage/**",
      "testing/**",
      "toolkit/actors/**",
      "toolkit/components/aboutmemory/**",
      "toolkit/components/aboutprocesses/content/aboutProcesses.js",
      "toolkit/components/antitracking/**",
      "toolkit/components/asyncshutdown/**",
      "toolkit/components/backgroundtasks/BackgroundTasks*.sys.mjs",
      "toolkit/components/cleardata/**",
      "toolkit/components/contentprefs/ContentPrefService2.sys.mjs",
      "toolkit/components/cookiebanners/**",
      "toolkit/components/crashes/**",
      "toolkit/components/crashmonitor/CrashMonitor.sys.mjs",
      "toolkit/components/credentialmanagement/IdentityCredentialPromptService.sys.mjs",
      "toolkit/components/downloads/**",
      "toolkit/components/featuregates/FeatureGate*.*",
      "toolkit/components/forgetaboutsite/test/unit/test_removeDataFromDomain.js",
      "toolkit/components/glean/tests/browser/**",
      "toolkit/components/kvstore/kvstore.sys.mjs",
      "toolkit/components/lz4/lz4.js",
      "toolkit/components/messaging-system/**",
      "toolkit/components/mozintl/mozIntl.sys.mjs",
      "toolkit/components/nimbus/**",
      "toolkit/components/normandy/**",
      "toolkit/components/passwordmgr/**",
      "toolkit/components/pdfjs/**",
      "toolkit/components/pictureinpicture/**",
      "toolkit/components/places/**",
      "toolkit/components/printing/content/print*.*",
      "toolkit/components/processtools/tests/browser/browser_test_powerMetrics.js",
      "toolkit/components/promiseworker/**/PromiseWorker.*",
      "toolkit/components/prompts/**",
      "toolkit/components/reader/**",
      "toolkit/components/reputationservice/test/unit/test_app_rep_windows.js",
      "toolkit/components/taskscheduler/TaskScheduler*.*",
      "toolkit/components/telemetry/**",
      "toolkit/components/thumbnails/**",
      "toolkit/components/timermanager/UpdateTimerManager.*",
      "toolkit/components/translation/LanguageDetector.*",
      "toolkit/components/url-classifier/**",
      "toolkit/components/utils/**",
      "toolkit/components/viewsource/**",
      "toolkit/components/windowwatcher/**",
      "toolkit/components/workerloader/require.js",
      "toolkit/content/**",
      "toolkit/crashreporter/**",
      "toolkit/modules/**",
      "toolkit/mozapps/downloads/**",
      "toolkit/mozapps/extensions/**",
      "toolkit/mozapps/handling/**",
      "toolkit/mozapps/update/**",
      "toolkit/profile/test/chrome/test_create_profile.xhtml",
      "tools/code-coverage/tests/mochitest/test_coverage_specialpowers.html",
      "tools/lint/eslint/**",
      "tools/profiler/tests/**",
      "uriloader/**",
      "widget/tests/window_composition_text_querycontent.xhtml",
    ],
    extends: ["plugin:mozilla/valid-jsdoc"],
  },
  {
    files: ["**"],
    excludedFiles: [
      "accessible/tests/**",
      "browser/actors/**",
      "browser/base/content/**",
      "browser/components/Browser*",
      "browser/components/aboutlogins/**",
      "browser/components/aboutwelcome/**",
      "browser/components/asrouter/**",
      "browser/components/attribution/**",
      "browser/components/contentanalysis/content/ContentAnalysis.sys.mjs",
      "browser/components/customizableui/**",
      "browser/components/doh/TRRPerformance.sys.mjs",
      "browser/components/downloads/**",
      "browser/components/enterprisepolicies/Policies.sys.mjs",
      "browser/components/extensions/**",
      "browser/components/firefoxview/**",
      "browser/components/messagepreview/actors/**",
      "browser/components/newtab/**",
      "browser/components/originattributes/test/browser/**",
      "browser/components/pocket/content/**",
      "browser/components/preferences/**",
      "browser/components/privatebrowsing/**",
      "browser/components/profiles/Profiles**",
      "browser/components/protections/content/*card.mjs",
      "browser/components/protocolhandler/WebProtocolHandlerRegistrar.sys.mjs",
      "browser/components/reportbrokensite/ReportBrokenSite.sys.mjs",
      "browser/components/reportbrokensite/test/browser/head.js",
      "browser/components/resistfingerprinting/test/browser/head.js",
      "browser/components/safebrowsing/content/test/**",
      "browser/components/screenshots/**",
      "browser/components/sidebar/**",
      "browser/components/shell/**",
      "browser/components/sessionstore/**",
      "browser/components/shopping/**",
      "browser/components/storybook/.storybook/**",
      "browser/components/storybook/custom-elements-manifest.config.mjs",
      "browser/components/syncedtabs/**",
      "browser/components/tabbrowser/**",
      "browser/components/tabpreview/tabpreview.mjs",
      "browser/components/tests/browser/**",
      "browser/components/textrecognition/**",
      "browser/components/topsites/**",
      "browser/components/touchbar/**",
      "browser/components/translations/**",
      "browser/components/uitour/**",
      "browser/extensions/formautofill/**",
      "browser/extensions/pictureinpicture/**",
      "browser/extensions/report-site-issue/test/browser/head.js",
      "browser/extensions/search-detection/extension/background.js",
      "browser/extensions/screenshots/**",
      "browser/extensions/webcompat/**",
      "browser/fxr/content/permissions.js",
      "browser/modules/**",
      "browser/themes/BuiltInThemes.sys.mjs",
      "browser/tools/mozscreenshots/mozscreenshots/extension/TestRunner.sys.mjs",
      "caps/tests/mochitest/**",
      "devtools/**",
      "docshell/base/URIFixup.sys.mjs",
      "docshell/test/**",
      "dom/**",
      "editor/**",
      "extensions/permissions/test/PermissionTestUtils.sys.mjs",
      "gfx/layers/apz/test/mochitest/**",
      "image/test/**",
      "intl/**",
      "js/src/builtin/**",
      "js/xpconnect/**",
      "layout/**",
      "mobile/android/**",
      "mobile/shared/**",
      "modules/**",
      "netwerk/**",
      "parser/htmlparser/**",
      "remote/cdp/**",
      "remote/components/**",
      "remote/marionette/**",
      "remote/server/WebSocketHandshake.sys.mjs",
      "remote/shared/**",
      "remote/webdriver-bidi/**",
      "security/manager/**",
      "services/**",
      "storage/**",
      "testing/**",
      "toolkit/actors/**",
      "toolkit/components/aboutconfig/**",
      "toolkit/components/aboutmemory/**",
      "toolkit/components/aboutprocesses/content/aboutProcesses.js",
      "toolkit/components/antitracking/**",
      "toolkit/components/apppicker/content/appPicker.js",
      "toolkit/components/asyncshutdown/**",
      "toolkit/components/autocomplete/**",
      "toolkit/components/backgroundtasks/**",
      "toolkit/components/bitsdownload/Bits.sys.mjs",
      "toolkit/components/certviewer/**",
      "toolkit/components/cleardata/**",
      "toolkit/components/contentprefs/ContentPrefService*.sys.mjs",
      "toolkit/components/contentrelevancy/ContentRelevancyManager.sys.mjs",
      "toolkit/components/cookiebanners/**",
      "toolkit/components/crashes/**",
      "toolkit/components/crashmonitor/CrashMonitor.sys.mjs",
      "toolkit/components/credentialmanagement/IdentityCredentialPromptService.sys.mjs",
      "toolkit/components/ctypes/tests/**",
      "toolkit/components/downloads/**",
      "toolkit/components/enterprisepolicies/EnterprisePolicies*.sys.mjs",
      "toolkit/components/extensions/**",
      "toolkit/components/featuregates/**",
      "toolkit/components/forgetaboutsite/**",
      "toolkit/components/formautofill/**",
      "toolkit/components/glean/tests/browser/**",
      "toolkit/components/httpsonlyerror/tests/browser/head.js",
      "toolkit/components/kvstore/kvstore.sys.mjs",
      "toolkit/components/lz4/lz4.js",
      "toolkit/components/messaging-system/**",
      "toolkit/components/ml/**",
      "toolkit/components/mozintl/mozIntl.sys.mjs",
      "toolkit/components/narrate/**",
      "toolkit/components/nimbus/**",
      "toolkit/components/normandy/**",
      "toolkit/components/passwordmgr/**",
      "toolkit/components/pdfjs/**",
      "toolkit/components/pictureinpicture/**",
      "toolkit/components/places/**",
      "toolkit/components/printing/content/print*.*",
      "toolkit/components/printing/tests/head.js",
      "toolkit/components/processtools/tests/browser/browser_test_powerMetrics.js",
      "toolkit/components/promiseworker/**/PromiseWorker.*",
      "toolkit/components/prompts/**",
      "toolkit/components/reader/**",
      "toolkit/components/remotebrowserutils/RemoteWebNavigation.sys.mjs",
      "toolkit/components/reportbrokensite/ReportBrokenSite*.sys.mjs",
      "toolkit/components/reputationservice/test/unit/test_app_rep_windows.js",
      "toolkit/components/resistfingerprinting/**",
      "toolkit/components/satchel/**",
      "toolkit/components/shopping/content/ShoppingProduct.mjs",
      "toolkit/components/taskscheduler/TaskScheduler*.*",
      "toolkit/components/telemetry/**",
      "toolkit/components/thumbnails/**",
      "toolkit/components/timermanager/UpdateTimerManager.*",
      "toolkit/components/translation/LanguageDetector.*",
      "toolkit/components/translations/**",
      "toolkit/components/uniffi-bindgen-gecko-js/fixtures/tests/xpcshell/test_callbacks.js",
      "toolkit/components/uniffi-js/js/UniFFI.sys.mjs",
      "toolkit/components/url-classifier/**",
      "toolkit/components/utils/**",
      "toolkit/components/viewsource/**",
      "toolkit/components/windowwatcher/**",
      "toolkit/components/workerloader/require.js",
      "toolkit/content/**",
      "toolkit/crashreporter/**",
      "toolkit/modules/**",
      "toolkit/mozapps/downloads/**",
      "toolkit/mozapps/extensions/**",
      "toolkit/mozapps/handling/**",
      "toolkit/mozapps/update/**",
      "toolkit/profile/content/profileSelection.js",
      "toolkit/profile/test/xpcshell/head.js",
      "toolkit/profile/test/chrome/test_create_profile.xhtml",
      "toolkit/themes/shared/design-system/tokens-config.js",
      "tools/code-coverage/tests/mochitest/test_coverage_specialpowers.html",
      "tools/lint/eslint/**",
      "tools/profiler/tests/**",
      "uriloader/**",
      "widget/tests/file*.js",
      "widget/tests/window_composition_text_querycontent.xhtml",
    ],
    extends: ["plugin:mozilla/require-jsdoc"],
  },
  {
    files: ["layout/**"],
    excludedFiles: ["layout/tools/reftest/**"],
    rules: {
      "object-shorthand": "off",
      "mozilla/avoid-removeChild": "off",
      "mozilla/reject-importGlobalProperties": "off",
      "mozilla/no-arbitrary-setTimeout": "off",
      "mozilla/no-define-cc-etc": "off",
      "mozilla/prefer-boolean-length-check": "off",
      "mozilla/use-default-preference-values": "off",
      "mozilla/use-includes-instead-of-indexOf": "off",
      "mozilla/use-services": "off",
      "mozilla/use-ownerGlobal": "off",
      complexity: "off",
      "consistent-return": "off",
      "no-array-constructor": "off",
      "no-caller": "off",
      "no-cond-assign": "off",
      "no-extra-boolean-cast": "off",
      "no-eval": "off",
      "no-func-assign": "off",
      "no-global-assign": "off",
      "no-implied-eval": "off",
      "no-lonely-if": "off",
      "no-nested-ternary": "off",
      "no-new-wrappers": "off",
      "no-redeclare": "off",
      "no-restricted-globals": "off",
      "no-sequences": "off",
      "no-throw-literal": "off",
      "no-useless-concat": "off",
      "no-undef": "off",
      "no-unreachable": "off",
      "no-unsafe-negation": "off",
      "no-unused-vars": "off",
      "no-useless-return": "off",
    },
  },
  {
    files: [
      "dom/animation/test/**",
      "dom/base/test/*.*",
      "dom/base/test/jsmodules/**",
      "dom/canvas/test/**",
      "dom/events/test/**",
      "dom/file/tests/**",
      "dom/html/test/**",
      "dom/media/webaudio/test/**",
      "dom/media/webrtc/tests/**",
      "dom/payments/test/**",
      "dom/performance/tests/**",
      "dom/quota/test/browser/**",
      "dom/quota/test/xpcshell/**",
      "dom/security/test/cors/**",
      "dom/security/test/csp/**",
      "dom/security/test/mixedcontentblocker/**",
      "dom/serviceworkers/test/**",
      "dom/smil/test/**",
      "dom/tests/mochitest/**",
      "dom/vr/test/**",
      "dom/webauthn/tests/**",
      "dom/workers/test/**",
    ],
    rules: {
      "mozilla/avoid-removeChild": "off",
      "mozilla/no-compare-against-boolean-literals": "off",
      "mozilla/use-includes-instead-of-indexOf": "off",
      "mozilla/use-ownerGlobal": "off",
      "mozilla/use-services": "off",
      "no-array-constructor": "off",
      "no-caller": "off",
      "no-constant-condition": "off",
      "no-control-regex": "off",
      "no-else-return": "off",
      "no-empty": "off",
      "no-func-assign": "off",
      "no-global-assign": "off",
      "no-lone-blocks": "off",
      "no-lonely-if": "off",
      "no-nested-ternary": "off",
      "no-new-wrappers": "off",
      "no-object-constructor": "off",
      "no-redeclare": "off",
      "no-restricted-globals": "off",
      "no-shadow": "off",
      "no-sparse-arrays": "off",
      "no-throw-literal": "off",
      "no-useless-concat": "off",
    },
  },
  {
    // Bug 1792693: Gradually enable no-undef and no-unused-vars on these
    // directories.
    files: [
      "dom/animation/test/**",
      "dom/base/test/*.*",
      "dom/base/test/unit/test_serializers_entities*.js",
      "dom/base/test/jsmodules/**",
      "dom/canvas/test/**",
      "dom/events/test/**",
      "dom/file/tests/**",
      "dom/html/test/**",
      "dom/media/webrtc/tests/**",
      "dom/media/webspeech/recognition/test/**",
      "dom/media/webspeech/synth/test/**",
      "dom/payments/test/**",
      "dom/performance/tests/**",
      "dom/quota/test/browser/**",
      "dom/quota/test/common/**",
      "dom/quota/test/mochitest/**",
      "dom/quota/test/xpcshell/**",
      "dom/security/test/cors/**",
      "dom/security/test/csp/**",
      "dom/security/test/mixedcontentblocker/**",
      "dom/serviceworkers/test/**",
      "dom/smil/test/**",
      "dom/tests/mochitest/**",
      "dom/vr/test/**",
      "dom/webauthn/tests/**",
      "dom/webgpu/mochitest/**",
      "dom/workers/test/**",
    ],
    rules: {
      "no-undef": "off",
      "no-unused-vars": "off",
    },
  },
  {
    // Exempt all components and test files that explicitly want to test http urls from 'no-insecure-url' rule.
    // Gradually change test cases such that this list gets smaller and more precisely. Bug 1758951
    files: [
      // Exempt tests that set https_first_disable to true Bug 1758951
      "browser/components/downloads/test/browser/browser_image_mimetype_issues.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_cookie_settings.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_extensionsettings.js",
      "devtools/server/tests/browser/browser_storage_listings.js",
      "image/test/browser/browser_offscreen_image_in_out_of_process_iframe.js",
      "security/manager/ssl/tests/mochitest/browser/browser_HSTS.js",
      "testing/mochitest/tests/browser/browser_browserLoaded_content_loaded.js",
      "toolkit/components/messaging-system/schemas/TriggerActionSchemas/test/browser/browser_asrouter_trigger_listeners.js",
      "toolkit/components/normandy/test/browser/browser_about_studies.js",
      "toolkit/components/remotebrowserutils/tests/browser/browser_RemoteWebNavigation.js",
      "toolkit/components/viewsource/test/browser/browser_bug464222.js",
      "toolkit/components/viewsource/test/browser/browser_viewsource_newwindow.js",
      "toolkit/content/tests/browser/browser_label_textlink.js",
      "toolkit/crashreporter/test/browser/browser_aboutCrashesResubmit.js",
      // Exempt tests that set pref dom.security.https_first to false Bug 1758951
      "dom/manifest/test/browser_ManifestObtainer_obtain.js",
      "dom/media/test/test_access_control.html",
      "dom/websocket/tests/test_bug1384658.html",
      "devtools/client/shared/components/test/node/components/reps/string-with-url.test.js", // no test case
      // Exempt files that have comment which says "not [to] enforce https"
      // https://searchfox.org/mozilla-central/search?q=%2F%2F+We+should+not+enforce+https+for+tests+using+this+page.&path=
      "devtools/client/storage/test/browser_storage_basic_usercontextid_1.js",
      "devtools/client/storage/test/browser_storage_basic_usercontextid_2.js",
      "devtools/client/storage/test/browser_storage_basic_with_fragment.js",
      "devtools/client/storage/test/browser_storage_cookies_delete_all.js",
      "devtools/client/storage/test/browser_storage_delete.js",
      "devtools/client/storage/test/browser_storage_delete_all.js",
      "devtools/client/storage/test/browser_storage_delete_tree.js",
      "devtools/client/storage/test/browser_storage_delete_usercontextid.js",
      "devtools/client/storage/test/browser_storage_sidebar.js",
      "devtools/client/storage/test/browser_storage_sidebar_toggle.js",
      "devtools/client/storage/test/head.js",
      "devtools/client/storage/test/storage-complex-values.html",
      "devtools/client/storage/test/storage-sidebar-parsetree.html",
      // No .(s)js or html test files Bug 1780024
      "services/sync/tests/tps/**",
      // Files that test https and http already
      "browser/base/content/test/general/browser_remoteTroubleshoot.js",
      "browser/base/content/test/favicons/browser_mixed_content.js",
      "browser/base/content/test/sanitize/browser_sanitize-history.js",
      "devtools/client/shared/test/shared-head.js",
      "devtools/client/shared/test/head.js",
      // uses new HttpServer which doesn't support https
      "browser/base/content/test/favicons/browser_favicon_nostore.js",
      // That are all files that produes warnings in the existing test infrastructure.
      // Since our focus is that new tests won't use http without thinking twice we exempt
      // these test files for now. Bug 1758951
      "browser/components/aboutlogins/tests/browser/browser_createLogin.js",
      "browser/components/aboutlogins/tests/chrome/test_login_item.html",
      "browser/components/contextualidentity/test/browser/browser_broadcastchannel.js",
      "browser/components/contextualidentity/test/browser/browser_eme.js",
      "browser/components/contextualidentity/test/browser/browser_favicon.js",
      "browser/components/contextualidentity/test/browser/browser_forgetAPI_EME_forgetThisSite.js",
      "browser/components/contextualidentity/test/browser/browser_forgetAPI_cookie_getCookiesWithOriginAttributes.js",
      "browser/components/contextualidentity/test/browser/browser_forgetAPI_quota_clearStoragesForPrincipal.js",
      "browser/components/contextualidentity/test/browser/browser_forgetaboutsite.js",
      "browser/components/contextualidentity/test/browser/browser_middleClick.js",
      "browser/components/contextualidentity/test/browser/browser_reopenIn.js",
      "browser/components/contextualidentity/test/browser/browser_restore_getCookiesWithOriginAttributes.js",
      "browser/components/contextualidentity/test/browser/browser_usercontextid_tabdrop.js",
      "browser/components/contextualidentity/test/browser/saveLink.sjs",
      "browser/components/customizableui/test/browser_947914_button_history.js",
      "browser/components/customizableui/test/browser_947914_button_print.js",
      "browser/components/customizableui/test/browser_947914_button_zoomReset.js",
      "browser/components/customizableui/test/browser_backfwd_enabled_post_customize.js",
      "browser/components/customizableui/test/browser_customization_context_menus.js",
      "browser/components/customizableui/test/browser_exit_background_customize_mode.js",
      "browser/components/customizableui/test/browser_history_recently_closed.js",
      "browser/components/customizableui/test/browser_history_recently_closed_middleclick.js",
      "browser/components/customizableui/test/browser_synced_tabs_menu.js",
      "browser/components/downloads/test/browser/browser_download_overwrite.js",
      "browser/components/downloads/test/browser/browser_go_to_download_page.js",
      "browser/components/downloads/test/browser/browser_iframe_gone_mid_download.js",
      "browser/components/downloads/test/browser/head.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_disable_feedback_commands.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_handlers.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_set_homepage.js",
      "browser/components/enterprisepolicies/tests/browser/browser_policy_set_startpage.js",
      "browser/components/extensions/test/browser/browser_ext_autocompletepopup.js",
      "browser/components/extensions/test/browser/browser_ext_browserAction_context.js",
      "browser/components/extensions/test/browser/browser_ext_browserAction_contextMenu.js",
      "browser/components/extensions/test/browser/browser_ext_browserAction_popup_preload.js",
      "browser/components/extensions/test/browser/browser_ext_browsingData_history.js",
      "browser/components/extensions/test/browser/browser_ext_chrome_settings_overrides_home.js",
      "browser/components/extensions/test/browser/browser_ext_commands_execute_page_action.js",
      "browser/components/extensions/test/browser/browser_ext_contentscript_cross_docGroup_adoption.js",
      "browser/components/extensions/test/browser/browser_ext_contentscript_cross_docGroup_adoption_xhr.js",
      "browser/components/extensions/test/browser/browser_ext_contextMenus_targetUrlPatterns.js",
      "browser/components/extensions/test/browser/browser_ext_contextMenus_urlPatterns.js",
      "browser/components/extensions/test/browser/browser_ext_devtools_inspectedWindow_targetSwitch.js",
      "browser/components/extensions/test/browser/browser_ext_devtools_network.js",
      "browser/components/extensions/test/browser/browser_ext_devtools_network_targetSwitch.js",
      "browser/components/extensions/test/browser/browser_ext_find.js",
      "browser/components/extensions/test/browser/browser_ext_management.js",
      "browser/components/extensions/test/browser/browser_ext_menus.js",
      "browser/components/extensions/test/browser/browser_ext_menus_replace_menu_context.js",
      "browser/components/extensions/test/browser/browser_ext_menus_targetElement_shadow.js",
      "browser/components/extensions/test/browser/browser_ext_nontab_process_switch.js",
      "browser/components/extensions/test/browser/browser_ext_optionsPage_privileges.js",
      "browser/components/extensions/test/browser/browser_ext_originControls.js",
      "browser/components/extensions/test/browser/browser_ext_pageAction_context.js",
      "browser/components/extensions/test/browser/browser_ext_pageAction_popup.js",
      "browser/components/extensions/test/browser/browser_ext_pageAction_show_matches.js",
      "browser/components/extensions/test/browser/browser_ext_pageAction_simple.js",
      "browser/components/extensions/test/browser/browser_ext_popup_api_injection.js",
      "browser/components/extensions/test/browser/browser_ext_popup_focus.js",
      "browser/components/extensions/test/browser/browser_ext_port_disconnect_on_crash.js",
      "browser/components/extensions/test/browser/browser_ext_runtime_openOptionsPage.js",
      "browser/components/extensions/test/browser/browser_ext_runtime_openOptionsPage_uninstall.js",
      "browser/components/extensions/test/browser/browser_ext_runtime_setUninstallURL.js",
      "browser/components/extensions/test/browser/browser_ext_search_favicon.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_forgetClosedTab.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_forgetClosedWindow.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_getRecentlyClosed_private.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_getRecentlyClosed_tabs.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_incognito.js",
      "browser/components/extensions/test/browser/browser_ext_sessions_window_tab_value.js",
      "browser/components/extensions/test/browser/browser_ext_slow_script.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_attention.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_containerIsolation.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_create.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_discard.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_discard_reversed.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_discarded.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_duplicate.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_events_order.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_executeScript.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_executeScript_bad.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_hide.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_hide_update.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_highlight.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_insertCSS.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_printPreview.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_query.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_readerMode.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_saveAsPDF.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_sendMessage.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_sharingState.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_update_url.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_warmup.js",
      "browser/components/extensions/test/browser/browser_ext_tabs_zoom.js",
      "browser/components/extensions/test/browser/browser_ext_topSites.js",
      "browser/components/extensions/test/browser/browser_ext_webNavigation_containerIsolation.js",
      "browser/components/extensions/test/browser/browser_ext_webNavigation_getFrames.js",
      "browser/components/extensions/test/browser/browser_ext_webNavigation_urlbar_transitions.js",
      "browser/components/extensions/test/browser/browser_ext_windows.js",
      "browser/components/extensions/test/browser/browser_ext_windows_allowScriptsToClose.js",
      "browser/components/extensions/test/browser/browser_ext_windows_create_tabId.js",
      "browser/components/originattributes/test/browser/browser_broadcastChannel.js",
      "browser/components/originattributes/test/browser/browser_cache.js",
      "browser/components/originattributes/test/browser/browser_firstPartyIsolation.js",
      "browser/components/originattributes/test/browser/browser_firstPartyIsolation_blobURI.js",
      "browser/components/originattributes/test/browser/browser_firstPartyIsolation_js_uri.js",
      "browser/components/originattributes/test/browser/browser_firstPartyIsolation_saveAs.js",
      "browser/components/originattributes/test/browser/browser_postMessage.js",
      "browser/components/originattributes/test/browser/file_postMessage.html",
      "browser/components/originattributes/test/browser/file_saveAs.sjs",
      "browser/components/originattributes/test/browser/file_thirdPartyChild.iframe.html",
      "browser/components/originattributes/test/browser/file_thirdPartyChild.worker.js",
      "browser/components/originattributes/test/browser/head.js",
      "browser/components/pagedata/tests/browser/browser_pagedata_background.js",
      "browser/components/pagedata/tests/browser/browser_pagedata_cache.js",
      "browser/components/preferences/tests/browser_applications_selection.js",
      "browser/components/preferences/tests/browser_bug410900.js",
      "browser/components/preferences/tests/browser_contentblocking.js",
      "browser/components/preferences/tests/browser_cookie_exceptions_addRemove.js",
      "browser/components/preferences/tests/browser_cookies_exceptions.js",
      "browser/components/preferences/tests/browser_extension_controlled.js",
      "browser/components/preferences/tests/browser_filetype_dialog.js",
      "browser/components/preferences/tests/browser_homepages_use_bookmark.js",
      "browser/components/preferences/tests/browser_https_only_exceptions.js",
      "browser/components/preferences/tests/browser_permissions_checkPermissionsWereAdded.js",
      "browser/components/preferences/tests/browser_permissions_dialog.js",
      "browser/components/preferences/tests/browser_permissions_dialog_default_perm.js",
      "browser/components/preferences/tests/browser_site_login_exceptions.js",
      "browser/components/preferences/tests/siteData/browser_siteData2.js",
      "browser/components/preferences/tests/siteData/browser_siteData3.js",
      "browser/components/preferences/tests/siteData/browser_siteData_multi_select.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_DownloadLastDirWithCPS.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_about_nimbus.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_about_nimbus_dismiss.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_about_nimbus_impressions.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_about_nimbus_messaging.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_cleanup.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_downloadLastDir.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_downloadLastDir_c.js",
      "browser/components/privatebrowsing/test/browser/browser_privatebrowsing_history_shift_click.js",
      "browser/components/protocolhandler/test/test_registerHandler.html",
      "browser/components/resistfingerprinting/test/browser/head.js",
      "browser/components/safebrowsing/content/test/browser_bug400731.js",
      "browser/components/safebrowsing/content/test/browser_bug415846.js",
      "browser/components/safebrowsing/content/test/browser_whitelisted.js",
      "browser/components/sessionstore/test/browser_1284886_suspend_tab.js",
      "browser/components/sessionstore/test/browser_394759_behavior.js",
      "browser/components/sessionstore/test/browser_394759_purge.js",
      "browser/components/sessionstore/test/browser_461634.js",
      "browser/components/sessionstore/test/browser_463205.js",
      "browser/components/sessionstore/test/browser_464199.js",
      "browser/components/sessionstore/test/browser_490040.js",
      "browser/components/sessionstore/test/browser_491168.js",
      "browser/components/sessionstore/test/browser_491577.js",
      "browser/components/sessionstore/test/browser_500328.js",
      "browser/components/sessionstore/test/browser_522545.js",
      "browser/components/sessionstore/test/browser_526613.js",
      "browser/components/sessionstore/test/browser_586068-apptabs.js",
      "browser/components/sessionstore/test/browser_586068-apptabs_ondemand.js",
      "browser/components/sessionstore/test/browser_586068-browser_state_interrupted.js",
      "browser/components/sessionstore/test/browser_586068-cascade.js",
      "browser/components/sessionstore/test/browser_586068-multi_window.js",
      "browser/components/sessionstore/test/browser_586068-reload.js",
      "browser/components/sessionstore/test/browser_586068-select.js",
      "browser/components/sessionstore/test/browser_586068-window_state.js",
      "browser/components/sessionstore/test/browser_586068-window_state_override.js",
      "browser/components/sessionstore/test/browser_589246.js",
      "browser/components/sessionstore/test/browser_590268.js",
      "browser/components/sessionstore/test/browser_595601-restore_hidden.js",
      "browser/components/sessionstore/test/browser_597071.js",
      "browser/components/sessionstore/test/browser_600545.js",
      "browser/components/sessionstore/test/browser_607016.js",
      "browser/components/sessionstore/test/browser_615394-SSWindowState_events_setBrowserState.js",
      "browser/components/sessionstore/test/browser_615394-SSWindowState_events_setTabState.js",
      "browser/components/sessionstore/test/browser_615394-SSWindowState_events_setWindowState.js",
      "browser/components/sessionstore/test/browser_615394-SSWindowState_events_undoCloseWindow.js",
      "browser/components/sessionstore/test/browser_618151.js",
      "browser/components/sessionstore/test/browser_625016.js",
      "browser/components/sessionstore/test/browser_636279.js",
      "browser/components/sessionstore/test/browser_687710.js",
      "browser/components/sessionstore/test/browser_687710_2.js",
      "browser/components/sessionstore/test/browser_701377.js",
      "browser/components/sessionstore/test/browser_819510_perwindowpb.js",
      "browser/components/sessionstore/test/browser_906076_lazy_tabs.js",
      "browser/components/sessionstore/test/browser_async_duplicate_tab.js",
      "browser/components/sessionstore/test/browser_async_flushes.js",
      "browser/components/sessionstore/test/browser_async_window_flushing.js",
      "browser/components/sessionstore/test/browser_background_tab_crash.js",
      "browser/components/sessionstore/test/browser_backup_recovery.js",
      "browser/components/sessionstore/test/browser_broadcast.js",
      "browser/components/sessionstore/test/browser_cleaner.js",
      "browser/components/sessionstore/test/browser_closed_objects_changed_notifications_tabs.js",
      "browser/components/sessionstore/test/browser_cookies.js",
      "browser/components/sessionstore/test/browser_cookies_legacy.js",
      "browser/components/sessionstore/test/browser_cookies_sameSite.js",
      "browser/components/sessionstore/test/browser_duplicate_history.js",
      "browser/components/sessionstore/test/browser_focus_after_restore.js",
      "browser/components/sessionstore/test/browser_forget_async_closings.js",
      "browser/components/sessionstore/test/browser_movePendingTabToNewWindow.js",
      "browser/components/sessionstore/test/browser_old_favicon.js",
      "browser/components/sessionstore/test/browser_parentProcessRestoreHash.js",
      "browser/components/sessionstore/test/browser_remoteness_flip_on_restore.js",
      "browser/components/sessionstore/test/browser_reopen_all_windows.js",
      "browser/components/sessionstore/test/browser_restore_cookies_noOriginAttributes.js",
      "browser/components/sessionstore/test/browser_restore_redirect.js",
      "browser/components/sessionstore/test/browser_restored_window_features.js",
      "browser/components/sessionstore/test/browser_scrollPositions.js",
      "browser/components/sessionstore/test/browser_scrollPositionsReaderMode.js",
      "browser/components/sessionstore/test/browser_sessionHistory.js",
      "browser/components/sessionstore/test/browser_sessionStorage.js",
      "browser/components/sessionstore/test/browser_sessionStoreContainer.js",
      "browser/components/sessionstore/test/browser_switch_remoteness.js",
      "browser/components/sessionstore/test/browser_tab_label_during_restore.js",
      "browser/components/sessionstore/test/browser_tabs_in_urlbar.js",
      "browser/components/sessionstore/test/browser_windowStateContainer.js",
      "browser/components/sessionstore/test/head.js",
      "browser/components/tests/browser/browser_bug538331.js",
      "browser/components/tests/browser/browser_initial_tab_remoteType.js",
      "browser/components/tests/browser/browser_startup_homepage.js",
      "browser/components/touchbar/tests/browser/browser_touchbar_searchrestrictions.js",
      "browser/components/touchbar/tests/browser/browser_touchbar_tests.js",
      "browser/components/uitour/test/browser_UITour.js",
      "browser/components/uitour/test/head.js",
      "browser/components/urlbar/tests/UrlbarTestUtils.sys.mjs",
      "browser/components/urlbar/tests/browser-tips/browser_picks.js",
      "browser/components/urlbar/tests/browser-tips/browser_searchTips.js",
      "browser/components/urlbar/tests/browser-tips/browser_searchTips_interaction.js",
      "browser/components/urlbar/tests/browser-tips/browser_selection.js",
      "browser/components/urlbar/tests/browser-tips/head.js",
      "browser/components/urlbar/tests/browser-updateResults/browser_appendSpanCount.js",
      "browser/components/urlbar/tests/browser-updateResults/head.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_overflow.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_overflow_resize.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_searchTerms.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_setURI.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_tooltip.js",
      "browser/components/urlbar/tests/browser/browser_UrlbarInput_trimURLs.js",
      "browser/components/urlbar/tests/browser/browser_aboutHomeLoading.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_backspaced.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_caretNotAtEnd.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_firstResult.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_paste.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_placeholder.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_preserve.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_trimURLs.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_typed.js",
      "browser/components/urlbar/tests/browser/browser_autoFill_undo.js",
      "browser/components/urlbar/tests/browser/browser_autoOpen.js",
      "browser/components/urlbar/tests/browser/browser_autocomplete_autoselect.js",
      "browser/components/urlbar/tests/browser/browser_autocomplete_edit_completed.js",
      "browser/components/urlbar/tests/browser/browser_autocomplete_no_title.js",
      "browser/components/urlbar/tests/browser/browser_autocomplete_readline_navigation.js",
      "browser/components/urlbar/tests/browser/browser_autocomplete_tag_star_visibility.js",
      "browser/components/urlbar/tests/browser/browser_canonizeURL.js",
      "browser/components/urlbar/tests/browser/browser_copy_during_load.js",
      "browser/components/urlbar/tests/browser/browser_copying.js",
      "browser/components/urlbar/tests/browser/browser_cutting.js",
      "browser/components/urlbar/tests/browser/browser_decode.js",
      "browser/components/urlbar/tests/browser/browser_delete.js",
      "browser/components/urlbar/tests/browser/browser_deleteAllText.js",
      "browser/components/urlbar/tests/browser/browser_downArrowKeySearch.js",
      "browser/components/urlbar/tests/browser/browser_dragdropURL.js",
      "browser/components/urlbar/tests/browser/browser_dynamicResults.js",
      "browser/components/urlbar/tests/browser/browser_engagement.js",
      "browser/components/urlbar/tests/browser/browser_enter.js",
      "browser/components/urlbar/tests/browser/browser_enterAfterMouseOver.js",
      "browser/components/urlbar/tests/browser/browser_groupLabels.js",
      "browser/components/urlbar/tests/browser/browser_heuristicNotAddedFirst.js",
      "browser/components/urlbar/tests/browser/browser_hideHeuristic.js",
      "browser/components/urlbar/tests/browser/browser_ime_composition.js",
      "browser/components/urlbar/tests/browser/browser_inputHistory.js",
      "browser/components/urlbar/tests/browser/browser_inputHistory_autofill.js",
      "browser/components/urlbar/tests/browser/browser_inputHistory_emptystring.js",
      "browser/components/urlbar/tests/browser/browser_keepStateAcrossTabSwitches.js",
      "browser/components/urlbar/tests/browser/browser_keyword_override.js",
      "browser/components/urlbar/tests/browser/browser_keyword_select_and_type.js",
      "browser/components/urlbar/tests/browser/browser_locationBarCommand.js",
      "browser/components/urlbar/tests/browser/browser_oneOffs.js",
      "browser/components/urlbar/tests/browser/browser_oneOffs_settings.js",
      "browser/components/urlbar/tests/browser/browser_pasteAndGo.js",
      "browser/components/urlbar/tests/browser/browser_paste_multi_lines.js",
      "browser/components/urlbar/tests/browser/browser_paste_then_focus.js",
      "browser/components/urlbar/tests/browser/browser_remoteness_switch.js",
      "browser/components/urlbar/tests/browser/browser_removeUnsafeProtocolsFromURLBarPaste.js",
      "browser/components/urlbar/tests/browser/browser_remove_match.js",
      "browser/components/urlbar/tests/browser/browser_restoreEmptyInput.js",
      "browser/components/urlbar/tests/browser/browser_resultSpan.js",
      "browser/components/urlbar/tests/browser/browser_result_onSelection.js",
      "browser/components/urlbar/tests/browser/browser_revert.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_autofill.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_clickLink.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_indicator.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_localOneOffs_actionText.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_no_results.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_pickResult.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_sessionStore.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_setURI.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_suggestions.js",
      "browser/components/urlbar/tests/browser/browser_searchMode_switchTabs.js",
      "browser/components/urlbar/tests/browser/browser_searchSingleWordNotification.js",
      "browser/components/urlbar/tests/browser/browser_selectStaleResults.js",
      "browser/components/urlbar/tests/browser/browser_selectionKeyNavigation.js",
      "browser/components/urlbar/tests/browser/browser_separatePrivateDefault.js",
      "browser/components/urlbar/tests/browser/browser_separatePrivateDefault_differentEngine.js",
      "browser/components/urlbar/tests/browser/browser_stopSearchOnSelection.js",
      "browser/components/urlbar/tests/browser/browser_stop_pending.js",
      "browser/components/urlbar/tests/browser/browser_suggestedIndex.js",
      "browser/components/urlbar/tests/browser/browser_suppressFocusBorder.js",
      "browser/components/urlbar/tests/browser/browser_switchTab_closesUrlbarPopup.js",
      "browser/components/urlbar/tests/browser/browser_switchToTab_closes_newtab.js",
      "browser/components/urlbar/tests/browser/browser_tabKeyBehavior.js",
      "browser/components/urlbar/tests/browser/browser_tabMatchesInAwesomebar.js",
      "browser/components/urlbar/tests/browser/browser_top_sites.js",
      "browser/components/urlbar/tests/browser/browser_top_sites_private.js",
      "browser/components/urlbar/tests/browser/browser_typed_value.js",
      "browser/components/urlbar/tests/browser/browser_urlbar_telemetry_autofill.js",
      "browser/components/urlbar/tests/browser/browser_urlbar_telemetry_places.js",
      "browser/components/urlbar/tests/browser/browser_urlbar_telemetry_remotetab.js",
      "browser/components/urlbar/tests/browser/browser_urlbar_telemetry_searchmode.js",
      "browser/components/urlbar/tests/browser/browser_view_resultDisplay.js",
      "browser/components/urlbar/tests/browser/browser_view_resultTypes_display.js",
      "browser/components/urlbar/tests/quicksuggest/browser/browser_quicksuggest_configuration.js",
      "browser/components/urlbar/tests/quicksuggest/browser/browser_quicksuggest_indexes.js",
      "browser/extensions/formautofill/test/browser/browser_dropdown_layout.js",
      "browser/extensions/formautofill/test/browser/creditCard/browser_anti_clickjacking.js",
      "browser/extensions/report-site-issue/test/browser/browser_button_state.js",
      "browser/extensions/report-site-issue/test/browser/browser_disabled_cleanup.js",
      "browser/extensions/report-site-issue/test/browser/browser_report_site_issue.js",
      "browser/extensions/report-site-issue/test/browser/head.js",
      "browser/extensions/screenshots/test/browser/head.js",
      "browser/extensions/webcompat/tests/browser/head.js",
      "browser/modules/test/browser/browser_PageActions.js",
      "browser/modules/test/browser/browser_PageActions_contextMenus.js",
      "browser/modules/test/browser/browser_PageActions_newWindow.js",
      "browser/modules/test/browser/browser_PermissionUI.js",
      "browser/modules/test/browser/browser_PermissionUI_prompts.js",
      "browser/modules/test/browser/browser_SitePermissions_tab_urls.js",
      "browser/modules/test/browser/browser_Telemetry_numberOfSiteOriginsPerDocument.js",
      "browser/modules/test/browser/browser_UnsubmittedCrashHandler.js",
      "browser/modules/test/browser/browser_UsageTelemetry.js",
      "browser/modules/test/browser/browser_UsageTelemetry_domains.js",
      "browser/modules/test/browser/browser_UsageTelemetry_private_and_restore.js",
      "browser/modules/test/browser/browser_UsageTelemetry_toolbars.js",
      "browser/modules/test/browser/browser_UsageTelemetry_uniqueOriginsVisitedInPast24Hours.js",
      "caps/tests/mochitest/browser_checkloaduri.js",
      "caps/tests/mochitest/test_addonMayLoad.html",
      "caps/tests/mochitest/test_bug995943.xhtml",
      "caps/tests/mochitest/test_disableScript.xhtml",
      "devtools/client/aboutdebugging/test/browser/browser_aboutdebugging_addons_remote_runtime.js",
      "devtools/client/aboutdebugging/test/browser/browser_aboutdebugging_tab_navigate.js",
      "devtools/client/aboutdebugging/test/browser/browser_aboutdebugging_workers_remote_runtime.js",
      "devtools/client/accessibility/test/browser/head.js",
      "devtools/client/accessibility/test/chrome/test_accessible_learnMoreLink.html",
      "devtools/client/accessibility/test/chrome/test_accessible_openLink.html",
      "devtools/client/application/test/node/fixtures/data/constants.js",
      "devtools/client/debugger/src/components/SecondaryPanes/Frames/tests/Frames.spec.js",
      "devtools/client/debugger/src/components/SecondaryPanes/Frames/tests/Group.spec.js",
      "devtools/client/debugger/src/utils/sources-tree/tests/getUrl.spec.js",
      "devtools/client/debugger/src/utils/tests/source.spec.js",
      "devtools/client/debugger/src/utils/tests/url.spec.js",
      "devtools/client/debugger/test/mochitest/browser_dbg-project-root.js",
      "devtools/client/framework/browser-toolbox/test/browser_browser_toolbox_debugger.js",
      "devtools/client/framework/test/allocations/browser_allocations_browser_console.js",
      "devtools/client/framework/test/allocations/browser_allocations_reload_no_devtools.js",
      "devtools/client/framework/test/allocations/reload-test.js",
      "devtools/client/framework/test/browser_toolbox_error_count_reset_on_navigation.js",
      "devtools/client/inspector/markup/test/browser_markup_tag_edit_05.js",
      "devtools/client/inspector/shared/test/browser_styleinspector_output-parser.js",
      "devtools/client/inspector/shared/test/head.js",
      "devtools/client/inspector/test/browser_inspector_highlighter-eyedropper-zoom.js",
      "devtools/client/jsonview/test/browser_jsonview_data_blocking.js",
      "devtools/client/memory/test/browser/browser_memory_allocationStackDisplay_01.js",
      "devtools/client/memory/test/browser/browser_memory_clear_snapshots.js",
      "devtools/client/memory/test/browser/browser_memory_diff_01.js",
      "devtools/client/memory/test/browser/browser_memory_displays_01.js",
      "devtools/client/memory/test/browser/browser_memory_dominator_trees_01.js",
      "devtools/client/memory/test/browser/browser_memory_dominator_trees_02.js",
      "devtools/client/memory/test/browser/browser_memory_filter_01.js",
      "devtools/client/memory/test/browser/browser_memory_individuals_01.js",
      "devtools/client/memory/test/browser/browser_memory_keyboard-snapshot-list.js",
      "devtools/client/memory/test/browser/browser_memory_keyboard.js",
      "devtools/client/memory/test/browser/browser_memory_no_allocation_stacks.js",
      "devtools/client/memory/test/browser/browser_memory_no_auto_expand.js",
      "devtools/client/memory/test/browser/browser_memory_percents_01.js",
      "devtools/client/memory/test/browser/browser_memory_refresh_does_not_leak.js",
      "devtools/client/memory/test/browser/browser_memory_simple_01.js",
      "devtools/client/netmonitor/test/browser_net_cached-status.js",
      "devtools/client/netmonitor/test/browser_net_details_copy.js",
      "devtools/client/netmonitor/test/browser_net_headers-link_clickable.js",
      "devtools/client/netmonitor/test/browser_net_new_request_panel_send_request.js",
      "devtools/client/netmonitor/test/browser_net_resend_headers.js",
      "devtools/client/netmonitor/test/browser_net_resend_hidden_headers.js",
      "devtools/client/netmonitor/test/browser_net_security-state.js",
      "devtools/client/netmonitor/test/browser_net_security-tab-deselect.js",
      "devtools/client/netmonitor/test/browser_net_security-tab-visibility.js",
      "devtools/client/netmonitor/test/browser_net_stacktraces-visibility.js",
      "devtools/client/netmonitor/test/head.js",
      "devtools/client/netmonitor/test/sjs_status-codes-test-server.sjs",
      "devtools/client/performance-new/test/browser/browser_devtools-record-capture.js",
      "devtools/client/performance-new/test/browser/browser_devtools-record-discard.js",
      "devtools/client/performance-new/test/browser/browser_popup-profiler-states.js",
      "devtools/client/performance-new/test/browser/browser_popup-record-capture-view.js",
      "devtools/client/performance-new/test/browser/browser_popup-record-capture.js",
      "devtools/client/performance-new/test/browser/browser_popup-record-discard.js",
      "devtools/client/performance-new/test/browser/browser_split-toolbar-button.js",
      "devtools/client/performance-new/test/browser/helpers.js",
      "devtools/client/responsive/test/browser/browser_in_rdm_pane.js",
      "devtools/client/responsive/test/browser/browser_permission_doorhanger.js",
      "devtools/client/responsive/test/browser/browser_screenshot_button_warning.js",
      "devtools/client/responsive/test/browser/browser_tab_close.js",
      "devtools/client/responsive/test/browser/browser_tab_not_selected.js",
      "devtools/client/responsive/test/browser/browser_tab_remoteness_change.js",
      "devtools/client/responsive/test/browser/browser_tab_remoteness_change_fission_switch_target.js",
      "devtools/client/responsive/test/browser/browser_toolbox_swap_browsers.js",
      "devtools/client/responsive/test/browser/browser_toolbox_swap_inspector.js",
      "devtools/client/responsive/test/browser/head.js",
      "devtools/client/responsive/test/browser/sjs_redirection.sjs",
      "devtools/client/storage/test/browser_storage_basic.js",
      "devtools/client/storage/test/browser_storage_cache_delete.js",
      "devtools/client/storage/test/browser_storage_cookies_add.js",
      "devtools/client/storage/test/browser_storage_cookies_domain.js",
      "devtools/client/storage/test/browser_storage_cookies_domain_port.js",
      "devtools/client/storage/test/browser_storage_cookies_samesite.js",
      "devtools/client/storage/test/browser_storage_values.js",
      "devtools/client/styleeditor/test/browser_styleeditor_inline_friendly_names.js",
      "devtools/client/styleeditor/test/browser_styleeditor_private_perwindowpb.js",
      "devtools/client/styleeditor/test/head.js",
      "devtools/client/webconsole/test/browser/browser_console.js",
      "devtools/client/webconsole/test/browser/browser_console_clear_closed_tab.js",
      "devtools/client/webconsole/test/browser/browser_console_context_menu_entries.js",
      "devtools/client/webconsole/test/browser/browser_console_enable_network_monitoring.js",
      "devtools/client/webconsole/test/browser/browser_console_webconsole_iframe_messages.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_autocomplete_in_debugger_stackframe.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_autocomplete_mapped_variables.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_await_dynamic_import.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_editor_reverse_search_keyboard_navigation.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_multiline.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_screenshot_command_clipboard.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_screenshot_command_file.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_screenshot_command_fixed_header.js",
      "devtools/client/webconsole/test/browser/browser_jsterm_screenshot_command_selector.js",
      "devtools/client/webconsole/test/browser/browser_toolbox_console_new_process.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_batching.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_cached_messages_cross_domain_iframe.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_checkloaduri_errors.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_click_function_to_mapped_source.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_click_function_to_prettyprinted_source.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_click_function_to_source.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_clickable_urls.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_close_unfocused_window.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_closing_after_completion.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_console_api_iframe.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_console_group.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_console_logging_workers_api.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_console_table.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_console_trace_duplicates.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_context_menu_copy_link_location.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_context_menu_open_url.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_csp_ignore_reflected_xss_message.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_csp_violation.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_cspro.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_duplicate_errors.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_errors_after_page_reload.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_eval_error.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_eval_in_debugger_stackframe.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_eval_in_debugger_stackframe2.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_eval_sources.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_execution_scope.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_external_script_errors.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filter_buttons_overflow.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filter_by_regex_input.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filter_groups.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filter_navigation_marker.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filters.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_filters_persist.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_iframe_wrong_hud.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_init.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_insecure_passwords_about_blank_web_console_warning.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_location_debugger_link.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_location_styleeditor_link.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_network_requests_from_chrome.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_network_unicode.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_object_inspector_local_session_storage.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_optimized_out_vars.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_output_order.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_output_trimmed.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_reopen_closed_tab.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_same_origin_errors.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_select_all.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_sourcemap_error.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_stacktrace_location_debugger_link.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_string.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_time_methods.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_warning_group_cookies.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_warning_group_storage_isolation.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_worker_error.js",
      "devtools/client/webconsole/test/browser/browser_webconsole_worker_evaluate.js",
      "devtools/client/webconsole/test/browser/test-console-filter-by-regex-input.html",
      "devtools/server/tests/browser/browser_storage_cookies-duplicate-names.js",
      "devtools/server/tests/browser/browser_storage_dynamic_windows.js",
      "devtools/server/tests/browser/head.js",
      "devtools/shared/commands/resource/tests/browser_resources_sources.js",
      "dom/animation/test/document-timeline/test_document-timeline.html",
      "dom/animation/test/mozilla/file_restyles.html",
      "dom/base/test/browser_aboutnewtab_process_selection.js",
      "dom/base/test/browser_blocking_image.js",
      "dom/base/test/browser_bug1011748.js",
      "dom/base/test/browser_inputStream_structuredClone.js",
      "dom/base/test/browser_refresh_content.js",
      "dom/base/test/bug435425_redirect.sjs",
      "dom/base/test/bug461735-redirect1.sjs",
      "dom/base/test/chrome/test_bug421622.xhtml",
      "dom/base/test/chrome/test_chromeOuterWindowID.xhtml",
      "dom/base/test/file_bug1268962.sjs",
      "dom/base/test/iframe1_bug1640766.html",
      "dom/base/test/iframe_meta_refresh.sjs",
      "dom/base/test/jsmodules/test_import_meta_resolve.html",
      "dom/base/test/referrerHelper.js",
      "dom/base/test/referrer_change_server.sjs",
      "dom/base/test/referrer_testserver.sjs",
      "dom/base/test/test_blocking_image.html",
      "dom/base/test/test_bug1091883.html",
      "dom/base/test/test_bug1222633.html",
      "dom/base/test/test_bug1222633_link_update.html",
      "dom/base/test/test_bug1268962.html",
      "dom/base/test/test_bug282547.html",
      "dom/base/test/test_bug338583.html",
      "dom/base/test/test_bug362391.xhtml",
      "dom/base/test/test_bug364413.xhtml",
      "dom/base/test/test_bug384003.xhtml",
      "dom/base/test/test_bug426308.html",
      "dom/base/test/test_bug435425.html",
      "dom/base/test/test_bug466080.html",
      "dom/base/test/test_bug515401.html",
      "dom/base/test/test_bug574596.html",
      "dom/base/test/test_bug601803.html",
      "dom/base/test/test_bug694754.xhtml",
      "dom/base/test/test_bug696301-1.html",
      "dom/base/test/test_bug696301-2.html",
      "dom/base/test/test_bug744830.html",
      "dom/base/test/test_current_inner_window.html",
      "dom/base/test/test_embed_xorigin_document.html",
      "dom/base/test/test_fragment_sanitization.xhtml",
      "dom/base/test/test_intersectionobservers.html",
      "dom/base/test/test_messagemanager_send_principal.html",
      "dom/base/test/test_navigator_cookieEnabled.html",
      "dom/base/test/test_postMessage_solidus.html",
      "dom/base/test/test_postMessages_window.html",
      "dom/base/test/test_warning_for_blocked_cross_site_request.html",
      "dom/bindings/test/test_dom_xrays.html",
      "dom/bindings/test/test_setWithNamedGetterNoNamedSetter.html",
      "dom/cache/test/browser/browser_cache_pb_window.js",
      "dom/canvas/test/test_capture.html",
      "dom/canvas/test/test_imagebitmap.html",
      "dom/canvas/test/test_imagebitmap_cropping.html",
      "dom/encoding/test/test_TLD.html",
      "dom/encoding/test/test_in_lk_TLD.html",
      "dom/events/test/browser_beforeinput_by_execCommand_in_contentscript.js",
      "dom/events/test/browser_dragimage.js",
      "dom/events/test/browser_keyboard_event_init_key_event_enabled_in_contentscript.js",
      "dom/events/test/browser_mouse_enterleave_switch_tab.js",
      "dom/events/test/test_DataTransferItemList.html",
      "dom/events/test/test_bug1037990.html",
      "dom/events/test/test_bug1264380.html",
      "dom/events/test/test_bug812744.html",
      "dom/events/test/test_dragstart.html",
      "dom/events/test/test_mouse_capture_iframe.html",
      "dom/events/test/test_mouse_enterleave_iframe.html",
      "dom/events/test/test_use_split_keypress_event_model_on_old_Confluence.html",
      "dom/html/test/browser_form_post_from_file_to_http.js",
      "dom/html/test/forms/test_input_url.html",
      "dom/html/test/forms/test_max_attribute.html",
      "dom/html/test/forms/test_min_attribute.html",
      "dom/html/test/forms/test_pattern_attribute.html",
      "dom/html/test/forms/test_required_attribute.html",
      "dom/html/test/forms/test_step_attribute.html",
      "dom/html/test/test_anchor_href_cache_invalidation.html",
      "dom/html/test/test_bug1292522_same_domain_with_different_port_number.html",
      "dom/html/test/test_bug1315146.html",
      "dom/html/test/test_bug209275.xhtml",
      "dom/html/test/test_bug445004.html",
      "dom/html/test/test_bug448166.html",
      "dom/html/test/test_bug558788-2.html",
      "dom/html/test/test_bug590353-2.html",
      "dom/html/test/test_bug598643.html",
      "dom/html/test/test_bug607145.html",
      "dom/html/test/test_bug839913.html",
      "dom/html/test/test_formSubmission.html",
      "dom/indexedDB/test/browser_forgetThisSite.js",
      "dom/indexedDB/test/browser_private_idb.js",
      "dom/indexedDB/test/test_third_party.html",
      "dom/l10n/tests/mochitest/dom_localization/test_overlay.html",
      "dom/l10n/tests/mochitest/dom_localization/test_overlay_missing_children.html",
      "dom/l10n/tests/mochitest/dom_localization/test_overlay_repeated.html",
      "dom/l10n/tests/mochitest/dom_localization/test_repeated_l10nid.html",
      "dom/localstorage/test/browser_private_ls.js",
      "dom/manifest/test/browser_ManifestFinder_browserHasManifestLink.js",
      "dom/manifest/test/browser_Manifest_install.js",
      "dom/manifest/test/test_ManifestProcessor_id.html",
      "dom/manifest/test/test_ManifestProcessor_scope.html",
      "dom/manifest/test/test_ManifestProcessor_start_url.html",
      "dom/manifest/test/test_ManifestProcessor_warnings.html",
      "dom/media/autoplay/test/browser/head.js",
      "dom/media/autoplay/test/mochitest/file_autoplay_gv_play_request_window.html",
      "dom/media/autoplay/test/mochitest/file_autoplay_policy_activation_window.html",
      "dom/media/autoplay/test/mochitest/test_autoplay_policy_permission.html",
      "dom/media/doctor/test/browser/browser_decoderDoctor.js",
      "dom/media/mediasession/test/file_trigger_actionhandler_window.html",
      "dom/media/test/browser/browser_telemetry_video_hardware_decoding_support.js",
      "dom/media/test/file_access_controls.html",
      "dom/media/test/manifest.js",
      "dom/media/test/midflight-redirect.sjs",
      "dom/media/test/redirect.sjs",
      "dom/media/test/test_mixed_principals.html",
      "dom/media/test/test_seek_promise_bug1344357.html",
      "dom/media/test/test_streams_capture_origin.html",
      "dom/media/webrtc/tests/mochitests/test_peerConnection_basicAudioVideoVerifyExtmap.html",
      "dom/media/webrtc/tests/mochitests/test_peerConnection_basicAudioVideoVerifyExtmapSendonly.html",
      "dom/notification/test/mochitest/test_notification_tag.html",
      "dom/payments/test/test_pmi_validation.html",
      "dom/performance/tests/test_performance_paint_observer_helper.html",
      "dom/performance/tests/test_performance_paint_timing_helper.html",
      "dom/security/test/cors/file_bug1456721.html",
      "dom/security/test/cors/test_CrossSiteXHR_cache.html",
      "dom/security/test/cors/test_CrossSiteXHR_origin.html",
      "dom/security/test/csp/browser_manifest-src-override-default-src.js",
      "dom/security/test/csp/browser_test_uir_optional_clicks.js",
      "dom/security/test/csp/browser_test_web_manifest.js",
      "dom/security/test/csp/file_blob_uri_blocks_modals.html",
      "dom/security/test/csp/file_blocked_uri_in_violation_event_after_redirects.html",
      "dom/security/test/csp/file_blocked_uri_in_violation_event_after_redirects.sjs",
      "dom/security/test/csp/file_blocked_uri_redirect_frame_src_server.sjs",
      "dom/security/test/csp/file_bug1505412.sjs",
      "dom/security/test/csp/file_bug802872.js",
      "dom/security/test/csp/file_bug941404.html",
      "dom/security/test/csp/file_frameancestors_main.js",
      "dom/security/test/csp/file_main.js",
      "dom/security/test/csp/file_null_baseuri.html",
      "dom/security/test/csp/file_path_matching_redirect_server.sjs",
      "dom/security/test/csp/file_punycode_host_src.sjs",
      "dom/security/test/csp/file_redirect_content.sjs",
      "dom/security/test/csp/file_redirect_report.sjs",
      "dom/security/test/csp/file_redirect_worker.sjs",
      "dom/security/test/csp/file_redirects_main.html",
      "dom/security/test/csp/file_redirects_resource.sjs",
      "dom/security/test/csp/file_sendbeacon.html",
      "dom/security/test/csp/file_strict_dynamic_non_parser_inserted.html",
      "dom/security/test/csp/file_uir_top_nav.html",
      "dom/security/test/csp/file_windowwatcher_subframeB.html",
      "dom/security/test/csp/main_csp_worker.html",
      "dom/security/test/csp/test_301_redirect.html",
      "dom/security/test/csp/test_302_redirect.html",
      "dom/security/test/csp/test_303_redirect.html",
      "dom/security/test/csp/test_307_redirect.html",
      "dom/security/test/csp/test_allow_https_schemes.html",
      "dom/security/test/csp/test_base-uri.html",
      "dom/security/test/csp/test_blocked_uri_in_reports.html",
      "dom/security/test/csp/test_blocked_uri_in_violation_event_after_redirects.html",
      "dom/security/test/csp/test_blocked_uri_redirect_frame_src.html",
      "dom/security/test/csp/test_null_baseuri.html",
      "dom/security/test/csp/test_path_matching.html",
      "dom/security/test/csp/test_report_for_import.html",
      "dom/security/test/csp/test_uir_top_nav.html",
      "dom/security/test/csp/test_upgrade_insecure.html",
      "dom/security/test/csp/test_upgrade_insecure_navigation.html",
      "dom/security/test/csp/test_websocket_self.html",
      "dom/security/test/general/browser_test_data_download.js",
      "dom/security/test/general/browser_test_data_text_csv.js",
      "dom/security/test/general/browser_test_toplevel_data_navigations.js",
      "dom/security/test/general/file_same_site_cookies_redirect.sjs",
      "dom/security/test/general/file_same_site_cookies_toplevel_set_cookie.sjs",
      "dom/security/test/general/test_bug1450853.html",
      "dom/security/test/general/test_same_site_cookies_about.html",
      "dom/security/test/general/test_same_site_cookies_from_script.html",
      "dom/security/test/general/test_same_site_cookies_iframe.html",
      "dom/security/test/general/test_same_site_cookies_laxByDefault.html",
      "dom/security/test/general/test_same_site_cookies_redirect.html",
      "dom/security/test/general/test_same_site_cookies_subrequest.html",
      "dom/security/test/general/test_same_site_cookies_toplevel_nav.html",
      "dom/security/test/general/test_same_site_cookies_toplevel_set_cookie.html",
      "dom/security/test/https-first/browser_download_attribute.js",
      "dom/security/test/https-first/browser_httpsfirst.js",
      "dom/security/test/https-first/browser_httpsfirst_console_logging.js",
      "dom/security/test/https-first/browser_httpsfirst_speculative_connect.js",
      "dom/security/test/https-first/browser_mixed_content_console.js",
      "dom/security/test/https-first/browser_mixed_content_download.js",
      "dom/security/test/https-first/browser_navigation.js",
      "dom/security/test/https-first/browser_slow_download.js",
      "dom/security/test/https-first/browser_upgrade_onion.js",
      "dom/security/test/https-first/download_page.html",
      "dom/security/test/https-first/file_multiple_redirection.sjs",
      "dom/security/test/https-first/file_redirect.sjs",
      "dom/security/test/https-first/test_bad_cert.html",
      "dom/security/test/https-first/test_form_submission.html",
      "dom/security/test/https-first/test_fragment.html",
      "dom/security/test/https-first/test_multiple_redirection.html",
      "dom/security/test/https-first/test_referrer_policy.html",
      "dom/security/test/https-first/test_resource_upgrade.html",
      "dom/security/test/https-first/test_toplevel_cookies.html",
      "dom/security/test/https-only/browser_background_redirect.js",
      "dom/security/test/https-only/browser_console_logging.js",
      "dom/security/test/https-only/browser_hsts_host.js",
      "dom/security/test/https-only/browser_httpsonly_prefs.js",
      "dom/security/test/https-only/browser_httpsonly_speculative_connect.js",
      "dom/security/test/https-only/browser_iframe_test.js",
      "dom/security/test/https-only/browser_triggering_principal_exemption.js",
      "dom/security/test/https-only/browser_upgrade_exceptions.js",
      "dom/security/test/https-only/browser_user_gesture.js",
      "dom/security/test/https-only/file_redirect.sjs",
      "dom/security/test/https-only/test_fragment.html",
      "dom/security/test/https-only/test_http_background_auth_request.html",
      "dom/security/test/https-only/test_http_background_request.html",
      "dom/security/test/https-only/test_resource_upgrade.html",
      "dom/security/test/https-only/test_user_suggestion_box.html",
      "dom/security/test/mixedcontentblocker/browser_test_mixed_content_download.js",
      "dom/security/test/mixedcontentblocker/download_page.html",
      "dom/security/test/mixedcontentblocker/file_auth_download_page.html",
      "dom/security/test/mixedcontentblocker/file_frameNavigation.html",
      "dom/security/test/mixedcontentblocker/file_frameNavigation_blankTarget.html",
      "dom/security/test/mixedcontentblocker/file_frameNavigation_secure_grandchild.html",
      "dom/security/test/mixedcontentblocker/file_main.html",
      "dom/security/test/mixedcontentblocker/file_main_bug803225.html",
      "dom/security/test/mixedcontentblocker/file_redirect_handler.sjs",
      "dom/security/test/referrer-policy/browser_referrer_disallow_cross_site_relaxing.js",
      "dom/security/test/referrer-policy/img_referrer_testserver.sjs",
      "dom/security/test/referrer-policy/referrer_testserver.sjs",
      "dom/serviceworkers/test/browser_devtools_serviceworker_interception.js",
      "dom/serviceworkers/test/eventsource/eventsource_opaque_response_intercept_worker.js",
      "dom/serviceworkers/test/fetch/fetch_tests.js",
      "dom/serviceworkers/test/fetch/hsts/image.html",
      "dom/serviceworkers/test/fetch/importscript-mixedcontent/https_test.js",
      "dom/serviceworkers/test/fetch/origin/index.sjs",
      "dom/serviceworkers/test/fetch/requesturl/redirect.sjs",
      "dom/serviceworkers/test/fetch_event_worker.js",
      "dom/serviceworkers/test/opaque_intercept_worker.js",
      "dom/serviceworkers/test/simpleregister/index.html",
      "dom/serviceworkers/test/test_hsts_upgrade_intercept.html",
      "dom/serviceworkers/test/test_installation_simple.html",
      "dom/serviceworkers/test/test_origin_after_redirect.html",
      "dom/serviceworkers/test/test_origin_after_redirect_cached.html",
      "dom/serviceworkers/test/test_sanitize_domain.html",
      "dom/serviceworkers/test/xslt_worker.js",
      "dom/tests/browser/browser_ConsoleStorageAPITests.js",
      "dom/tests/browser/browser_ConsoleStoragePBTest_perwindowpb.js",
      "dom/tests/browser/browser_beforeunload_between_chrome_content.js",
      "dom/tests/browser/browser_bug1004814.js",
      "dom/tests/browser/browser_bug1238427.js",
      "dom/tests/browser/browser_bytecode_cache_asm_js.js",
      "dom/tests/browser/browser_frame_elements.js",
      "dom/tests/browser/browser_hasbeforeunload.js",
      "dom/tests/browser/browser_localStorage_e10s.js",
      "dom/tests/browser/browser_test_toolbars_visibility.js",
      "dom/tests/browser/browser_windowProxy_transplant.js",
      "dom/tests/mochitest/beacon/file_beaconSafelist.html",
      "dom/tests/mochitest/beacon/test_beaconOriginHeader.html",
      "dom/tests/mochitest/beacon/test_beaconPreflightWithCustomContentType.html",
      "dom/tests/mochitest/beacon/test_beaconRedirect.html",
      "dom/tests/mochitest/beacon/test_beaconWithSafelistedContentType.html",
      "dom/tests/mochitest/bugs/grandchild_bug260264.html",
      "dom/tests/mochitest/bugs/test_bug132255.html",
      "dom/tests/mochitest/bugs/test_bug260264.html",
      "dom/tests/mochitest/bugs/test_bug260264_nested.html",
      "dom/tests/mochitest/bugs/test_bug304459.html",
      "dom/tests/mochitest/bugs/test_bug42976.html",
      "dom/tests/mochitest/bugs/test_bug440572.html",
      "dom/tests/mochitest/bugs/test_bug850517.html",
      "dom/tests/mochitest/bugs/test_bug873229.html",
      "dom/tests/mochitest/bugs/window_bug1171215.html",
      "dom/tests/mochitest/chrome/test_sandbox_eventhandler.xhtml",
      "dom/tests/mochitest/dom-level0/test_background_loading_iframes.html",
      "dom/tests/mochitest/dom-level0/test_location.html",
      "dom/tests/mochitest/dom-level0/test_location_setters.html",
      "dom/tests/mochitest/dom-level0/test_setting_document.domain_idn.html",
      "dom/tests/mochitest/fetch/test_request.js",
      "dom/tests/mochitest/fetch/test_response.js",
      "dom/tests/mochitest/general/resource_timing_cross_origin.html",
      "dom/tests/mochitest/general/test_frameElementWrapping.html",
      "dom/tests/mochitest/general/window_clipboard_events.html",
      "dom/tests/mochitest/localstorage/test_keySync.html",
      "dom/tests/mochitest/localstorage/test_localStorageFromChrome.xhtml",
      "dom/tests/mochitest/localstorage/test_localStorageOriginsDiff.html",
      "dom/tests/mochitest/localstorage/test_localStorageOriginsDomainDiffs.html",
      "dom/tests/mochitest/localstorage/test_localStorageOriginsEquals.html",
      "dom/tests/mochitest/localstorage/test_localStorageOriginsPortDiffs.html",
      "dom/tests/mochitest/localstorage/test_localStorageOriginsSchemaDiffs.html",
      "dom/tests/mochitest/localstorage/test_localStorageQuota.html",
      "dom/tests/mochitest/localstorage/test_localStorageQuotaPrivateBrowsing_perwindowpb.html",
      "dom/tests/mochitest/localstorage/test_localStorageQuotaSessionOnly.html",
      "dom/tests/mochitest/localstorage/test_localStorageQuotaSessionOnly2.html",
      "dom/tests/mochitest/localstorage/test_localStorageReplace.html",
      "dom/tests/mochitest/sessionstorage/test_sessionStorageHttpHttps.html",
      "dom/tests/mochitest/sessionstorage/test_sessionStorageReplace.html",
      "dom/tests/mochitest/storageevent/frameLocalStorageSlaveEqual.html",
      "dom/tests/mochitest/storageevent/frameSessionStorageMasterNotEqual.html",
      "dom/tests/mochitest/storageevent/frameSessionStorageSlaveEqual.html",
      "dom/tests/mochitest/storageevent/test_storageLocalStorageEventCheckNoPropagation.html",
      "dom/tests/mochitest/storageevent/test_storageLocalStorageEventCheckPropagation.html",
      "dom/tests/mochitest/storageevent/test_storageSessionStorageEventCheckNoPropagation.html",
      "dom/tests/mochitest/storageevent/test_storageSessionStorageEventCheckPropagation.html",
      "dom/tests/mochitest/webcomponents/test_custom_element_htmlconstructor_chrome.html",
      "dom/tests/mochitest/webcomponents/test_custom_element_upgrade_chrome.html",
      "dom/tests/mochitest/webcomponents/test_shadowroot_inert_element.html",
      "dom/tests/mochitest/whatwg/postMessage_joined_helper.html",
      "dom/tests/mochitest/whatwg/postMessage_joined_helper2.html",
      "dom/tests/mochitest/whatwg/postMessage_onOther.html",
      "dom/tests/mochitest/whatwg/test_MessageEvent.html",
      "dom/tests/mochitest/whatwg/test_MessageEvent_dispatchToOther.html",
      "dom/tests/mochitest/whatwg/test_bug500328.html",
      "dom/tests/mochitest/whatwg/test_postMessage.html",
      "dom/tests/mochitest/whatwg/test_postMessage_chrome.html",
      "dom/tests/mochitest/whatwg/test_postMessage_idn.xhtml",
      "dom/tests/mochitest/whatwg/test_postMessage_joined.html",
      "dom/tests/mochitest/whatwg/test_postMessage_onOther.html",
      "dom/tests/mochitest/whatwg/test_postMessage_origin.xhtml",
      "dom/tests/mochitest/whatwg/test_postMessage_override.html",
      "dom/tests/mochitest/whatwg/test_postMessage_structured_clone.html",
      "dom/tests/mochitest/whatwg/test_postMessage_throw.html",
      "dom/tests/mochitest/whatwg/test_postMessage_transfer.html",
      "dom/tests/mochitest/whatwg/test_postMessage_userpass.html",
      "dom/url/tests/browser_download_after_revoke.js",
      "dom/url/tests/protocol_worker.js",
      "dom/url/tests/test_unknown_url_origin.html",
      "dom/url/tests/test_url.html",
      "dom/url/tests/test_urlExceptions.html",
      "dom/url/tests/test_url_empty_port.html",
      "dom/url/tests/test_url_malformedHost.html",
      "dom/url/tests/test_urlutils_stringify.html",
      "dom/url/tests/urlApi_worker.js",
      "dom/url/tests/urlSearchParams_commons.js",
      "dom/url/tests/url_exceptions_worker.js",
      "dom/websocket/tests/test_websocket_mixed_content_opener.html",
      "dom/websocket/tests/window_websocket_wss.html",
      "dom/workers/test/browser_fileURL.js",
      "dom/workers/test/bug1063538_worker.js",
      "dom/workers/test/importForeignScripts_worker.js",
      "dom/workers/test/importScripts_worker.js",
      "dom/workers/test/redirect_to_foreign.sjs",
      "dom/workers/test/referrer_test_server.sjs",
      "dom/workers/test/sharedWorker_thirdparty_window.html",
      "dom/workers/test/test_multi_sharedWorker.html",
      "dom/worklet/tests/specifier_with_user.mjs",
      "dom/xhr/tests/file_XHRResponseURL.js",
      "dom/xhr/tests/file_XHRSendData.sjs",
      "dom/xhr/tests/test_XHRDocURI.html",
      "dom/xhr/tests/test_XHR_anon.html",
      "dom/xhr/tests/test_XHR_system.html",
      "dom/xhr/tests/test_bug1070763.html",
      "dom/xhr/tests/test_worker_xhr_headers.html",
      "dom/xhr/tests/test_worker_xhr_system.js",
      "dom/xhr/tests/test_xhr_forbidden_headers.html",
      "dom/xslt/tests/mochitest/test_bug440974.html",
      "editor/libeditor/tests/browser_bug527935.js",
      "editor/libeditor/tests/test_bug1181130-2.html",
      "editor/libeditor/tests/test_bug372345.html",
      "extensions/permissions/test/browser_permmgr_sync.js",
      "gfx/layers/apz/test/mochitest/browser_test_select_popup_position.js",
      "gfx/layers/apz/test/mochitest/helper_wheelevents_handoff_on_non_scrollable_iframe.html",
      "image/test/browser/head.js",
      "image/test/mochitest/test_animated_gif.html",
      "image/test/mochitest/test_bug671906.html",
      "intl/locale/tests/LangPackMatcherTestUtils.sys.mjs",
      "layout/base/tests/browser_disableDialogs_onbeforeunload.js",
      "layout/base/tests/browser_onbeforeunload_only_after_interaction.js",
      "layout/base/tests/browser_onbeforeunload_only_after_interaction_in_frame.js",
      "layout/forms/test/test_bug536567_perwindowpb.html",
      "layout/generic/test/frame_visibility_in_iframe.html",
      "layout/generic/test/test_movement_by_words.html",
      "layout/style/test/browser_sourcemap.js",
      "layout/style/test/test_bug397427.html",
      "layout/style/test/test_load_events_on_stylesheets.html",
      "layout/style/test/test_root_node_display.html",
      "layout/style/test/test_selectors.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_create.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_events.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_executeScript.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_executeScript_bad.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_query.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_sendMessage.html",
      "mobile/shared/components/extensions/test/mochitest/test_ext_tabs_update_url.html",
      "netwerk/cookie/test/browser/browser_cookies_ipv6.js",
      "netwerk/cookie/test/browser/browser_originattributes.js",
      "netwerk/cookie/test/browser/browser_oversize.js",
      "netwerk/cookie/test/browser/browser_sameSiteConsole.js",
      "netwerk/test/browser/browser_103_assets.js",
      "netwerk/test/browser/browser_103_preload.js",
      "netwerk/test/browser/browser_cookie_filtering_subdomain.js",
      "netwerk/test/browser/browser_test_favicon.js",
      "netwerk/test/browser/cookie_filtering_helper.sys.mjs",
      "netwerk/test/browser/early_hint_preload_test_helper.sys.mjs",
      "netwerk/test/httpserver/httpd.sys.mjs",
      "netwerk/test/mochitests/file_loadinfo_redirectchain.sjs",
      "netwerk/test/mochitests/file_testloadflags_chromescript.js",
      "netwerk/test/mochitests/test_1396395.html",
      "netwerk/test/mochitests/test_loadinfo_redirectchain.html",
      "netwerk/test/mochitests/test_uri_scheme.html",
      "remote/cdp/test/browser/browser_httpd.js",
      "remote/cdp/test/browser/head.js",
      "remote/cdp/test/browser/network/browser_deleteCookies.js",
      "remote/cdp/test/browser/network/browser_getAllCookies.js",
      "remote/cdp/test/browser/network/browser_getCookies.js",
      "remote/cdp/test/browser/network/browser_navigationEvents.js",
      "remote/cdp/test/browser/network/browser_setCookie.js",
      "remote/shared/messagehandler/test/browser/browser_session_data.js",
      "security/manager/ssl/tests/mochitest/browser/some_content_framed.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/iframeunsecredirect.sjs",
      "security/manager/ssl/tests/mochitest/mixedcontent/imgunsecredirect.sjs",
      "security/manager/ssl/tests/mochitest/mixedcontent/redirecttoemptyimage.sjs",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_bug329869.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_dynDelayedUnsecurePicture.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_dynDelayedUnsecureXHR.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_dynUnsecureBackground.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_dynUnsecurePicture.html",
      "security/manager/ssl/tests/mochitest/mixedcontent/test_dynUnsecurePicturePreload.html",
      "security/sandbox/test/browser_bug1393259.js",
      "testing/mochitest/tests/SimpleTest/TestRunner.js",
      "testing/mozbase/mozprofile/tests/files/prefs_with_interpolation.js",
      "testing/talos/talos/tests/cpstartup/extension/api.js",
      "testing/talos/talos/tests/devtools/addon/content/tests/debugger/custom.js",
      "testing/talos/talos/tests/devtools/addon/content/tests/head.js",
      "testing/talos/talos/tests/devtools/addon/content/tests/source-map/source-map-loader.js",
      "testing/talos/talos/tests/tabswitch/actors/TalosTabSwitchParent.sys.mjs",
      "toolkit/components/aboutprocesses/tests/browser/head.js",
      "toolkit/components/antitracking/test/browser/browser_contentBlockingAllowListPrincipal.js",
      "toolkit/components/antitracking/test/browser/browser_permissionInNormalWindows.js",
      "toolkit/components/antitracking/test/browser/browser_permissionInNormalWindows_alwaysPartition.js",
      "toolkit/components/antitracking/test/browser/browser_socialtracking_save_image.js",
      "toolkit/components/antitracking/test/browser/browser_staticPartition_CORS_preflight.js",
      "toolkit/components/antitracking/test/browser/browser_staticPartition_HSTS.js",
      "toolkit/components/antitracking/test/browser/browser_staticPartition_cache.js",
      "toolkit/components/antitracking/test/browser/browser_staticPartition_websocket.js",
      "toolkit/components/antitracking/test/browser/browser_storageAccessSandboxed.js",
      "toolkit/components/antitracking/test/browser/browser_storageAccessSandboxed_alwaysPartition.js",
      "toolkit/components/antitracking/test/browser/browser_storageAccessThirdPartyChecks_alwaysPartition.js",
      "toolkit/components/antitracking/test/browser/browser_storageAccess_TopLevel_Embed.js",
      "toolkit/components/antitracking/test/browser/browser_thirdPartyStorageRejectionForCORS.js",
      "toolkit/components/antitracking/test/browser/browser_urlQueryStringStripping.js",
      "toolkit/components/antitracking/test/browser/cookiesCORS.sjs",
      "toolkit/components/antitracking/test/browser/head.js",
      "toolkit/components/antitracking/test/browser/storageAccessAPIHelpers.js",
      "toolkit/components/certviewer/tests/browser/adjustedCerts.js",
      "toolkit/components/certviewer/tests/chrome/CSoutput.mjs",
      "toolkit/components/certviewer/tests/chrome/parseOutput.mjs",
      "toolkit/components/cleardata/tests/browser/browser_css_cache.js",
      "toolkit/components/cleardata/tests/browser/browser_image_cache.js",
      "toolkit/components/cleardata/tests/browser/browser_quota.js",
      "toolkit/components/cleardata/tests/browser/browser_sessionStorage.js",
      "toolkit/components/cookiebanners/test/browser/browser_cookiebannerservice.js",
      "toolkit/components/extensions/test/browser/browser_ext_downloads_filters.js",
      "toolkit/components/extensions/test/browser/browser_ext_themes_autocomplete_popup.js",
      "toolkit/components/extensions/test/browser/browser_ext_thumbnails_bg_extension.js",
      "toolkit/components/extensions/test/mochitest/file_third_party.html",
      "toolkit/components/extensions/test/mochitest/test_chrome_ext_permissions.html",
      "toolkit/components/extensions/test/mochitest/test_chrome_ext_trackingprotection.html",
      "toolkit/components/extensions/test/mochitest/test_chrome_ext_webrequest_host_permissions.html",
      "toolkit/components/extensions/test/mochitest/test_ext_contentscript_activeTab.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_containers.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_expiry.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_first_party.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_incognito.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_permissions_bad.html",
      "toolkit/components/extensions/test/mochitest/test_ext_cookies_permissions_good.html",
      "toolkit/components/extensions/test/mochitest/test_ext_embeddedimg_iframe_frameAncestors.html",
      "toolkit/components/extensions/test/mochitest/test_ext_optional_permissions.html",
      "toolkit/components/extensions/test/mochitest/test_ext_protocolHandlers.html",
      "toolkit/components/extensions/test/mochitest/test_ext_request_urlClassification.html",
      "toolkit/components/extensions/test/mochitest/test_ext_storage_manager_capabilities.html",
      "toolkit/components/extensions/test/mochitest/test_ext_streamfilter_multiple.html",
      "toolkit/components/extensions/test/mochitest/test_ext_streamfilter_processswitch.html",
      "toolkit/components/extensions/test/mochitest/test_ext_tabs_executeScript_good.html",
      "toolkit/components/extensions/test/mochitest/test_ext_tabs_query_popup.html",
      "toolkit/components/extensions/test/mochitest/test_ext_tabs_sendMessage.html",
      "toolkit/components/extensions/test/mochitest/test_ext_web_accessible_incognito.html",
      "toolkit/components/extensions/test/mochitest/test_ext_web_accessible_resources.html",
      "toolkit/components/extensions/test/mochitest/test_ext_webrequest_and_proxy_filter.html",
      "toolkit/components/extensions/test/mochitest/test_ext_webrequest_hsts.html",
      "toolkit/components/glean/tests/browser/browser_fog_rdd.js",
      "toolkit/components/httpsonlyerror/tests/browser/browser_errorpage.js",
      "toolkit/components/httpsonlyerror/tests/browser/browser_errorpage_timeout.js",
      "toolkit/components/httpsonlyerror/tests/browser/browser_errorpage_www_suggestion.js",
      "toolkit/components/httpsonlyerror/tests/browser/browser_exception.js",
      "toolkit/components/narrate/test/head.js",
      "toolkit/components/normandy/test/NormandyTestUtils.sys.mjs",
      "toolkit/components/normandy/test/browser/browser_RecipeRunner.js",
      "toolkit/components/normandy/test/browser/browser_actions_ShowHeartbeatAction.js",
      "toolkit/components/normandy/test/browser/head.js",
      "toolkit/components/passwordmgr/test/LoginTestUtils.sys.mjs",
      "toolkit/components/passwordmgr/test/browser/browser_autofill_http.js",
      "toolkit/components/passwordmgr/test/browser/browser_context_menu.js",
      "toolkit/components/passwordmgr/test/browser/browser_context_menu_iframe.js",
      "toolkit/components/passwordmgr/test/browser/browser_doorhanger_httpsUpgrade.js",
      "toolkit/components/passwordmgr/test/browser/browser_doorhanger_remembering.js",
      "toolkit/components/passwordmgr/test/browser/browser_localip_frame.js",
      "toolkit/components/passwordmgr/test/browser/head.js",
      "toolkit/components/passwordmgr/test/mochitest/test_autocomplete_basic_form_insecure.html",
      "toolkit/components/passwordmgr/test/mochitest/test_autocomplete_highlight_non_login.html",
      "toolkit/components/passwordmgr/test/mochitest/test_autocomplete_https_upgrade.html",
      "toolkit/components/passwordmgr/test/mochitest/test_autofill_different_formActionOrigin.html",
      "toolkit/components/passwordmgr/test/mochitest/test_autofill_https_upgrade.html",
      "toolkit/components/passwordmgr/test/mochitest/test_autofill_password-only.html",
      "toolkit/components/passwordmgr/test/mochitest/test_basic_form_html5.html",
      "toolkit/components/passwordmgr/test/mochitest/test_bug_627616.html",
      "toolkit/components/passwordmgr/test/mochitest/test_formless_submit_form_removal.html",
      "toolkit/components/passwordmgr/test/mochitest/test_formless_submit_form_removal_negative.html",
      "toolkit/components/passwordmgr/test/mochitest/test_formless_submit_navigation.html",
      "toolkit/components/passwordmgr/test/mochitest/test_formless_submit_navigation_negative.html",
      "toolkit/components/passwordmgr/test/mochitest/test_password_field_autocomplete.html",
      "toolkit/components/passwordmgr/test/mochitest/test_prompt.html",
      "toolkit/components/passwordmgr/test/mochitest/test_prompt_async.html",
      "toolkit/components/passwordmgr/test/mochitest/test_prompt_http.html",
      "toolkit/components/passwordmgr/test/mochitest/test_prompt_promptAuth.html",
      "toolkit/components/passwordmgr/test/mochitest/test_prompt_promptAuth_proxy.html",
      "toolkit/components/passwordmgr/test/mochitest/test_username_focus.html",
      "toolkit/components/pdfjs/test/browser_pdfjs_editing_contextmenu.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_editing_telemetry.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_fill_login.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_form.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_hcm.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_js.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_main.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_navigation.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_saveas.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_savedialog.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_views.js",
      "toolkit/components/pdfjs/test/browser_pdfjs_zoom.js",
      "toolkit/components/pictureinpicture/tests/head.js",
      "toolkit/components/printing/tests/head.js",
      "toolkit/components/processtools/tests/browser/browser_test_procinfo.js",
      "toolkit/components/prompts/test/test_modal_prompts.html",
      "toolkit/components/prompts/test/test_subresources_prompts.html",
      "toolkit/components/reader/tests/browser/browser_bug1453818_samesite_cookie.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_colorSchemePref.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_hidden_nodes.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_pocket.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_readingTime.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_refresh.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_remoteType.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_samesite_cookie_redirect.js",
      "toolkit/components/reader/tests/browser/browser_readerMode_with_anchor.js",
      "toolkit/components/satchel/test/browser/browser_privbrowsing_perwindowpb.js",
      "toolkit/components/startup/tests/browser/browser_bug511456.js",
      "toolkit/components/startup/tests/browser/browser_bug537449.js",
      "toolkit/components/url-classifier/tests/mochitest/allowlistAnnotatedFrame.html",
      "toolkit/components/url-classifier/tests/mochitest/classifiedAnnotatedFrame.html",
      "toolkit/components/url-classifier/tests/mochitest/features.js",
      "toolkit/components/url-classifier/tests/mochitest/redirect_tracker.sjs",
      "toolkit/components/url-classifier/tests/mochitest/test_classifier.html",
      "toolkit/components/url-classifier/tests/mochitest/test_classifier_match.html",
      "toolkit/components/url-classifier/tests/mochitest/test_classifier_worker.html",
      "toolkit/components/url-classifier/tests/mochitest/test_classify_ping.html",
      "toolkit/components/url-classifier/tests/mochitest/test_classify_track.html",
      "toolkit/components/url-classifier/tests/mochitest/test_cryptomining.html",
      "toolkit/components/url-classifier/tests/mochitest/test_emailtracking.html",
      "toolkit/components/url-classifier/tests/mochitest/test_fingerprinting.html",
      "toolkit/components/url-classifier/tests/mochitest/test_reporturl.html",
      "toolkit/components/url-classifier/tests/mochitest/test_socialtracking.html",
      "toolkit/components/url-classifier/tests/mochitest/test_threathit_report.html",
      "toolkit/components/url-classifier/tests/mochitest/test_trackingprotection_bug1312515.html",
      "toolkit/components/url-classifier/tests/mochitest/test_trackingprotection_bug1580416.html",
      "toolkit/components/url-classifier/tests/mochitest/test_trackingprotection_whitelist.html",
      "toolkit/components/viewsource/test/browser/browser_contextmenu.js",
      "toolkit/components/viewsource/test/browser/browser_open_docgroup.js",
      "toolkit/components/viewsource/test/browser/browser_validatefilename.js",
      "toolkit/components/windowcreator/test/browser_save_form_input_state.js",
      "toolkit/content/tests/browser/browser_crash_previous_frameloader.js",
      "toolkit/content/tests/browser/browser_isSynthetic.js",
      "toolkit/content/tests/browser/head.js",
      "toolkit/content/tests/chrome/findbar_window.xhtml",
      "toolkit/content/tests/chrome/window_browser_drop.xhtml",
      "toolkit/modules/tests/browser/browser_AsyncPrefs.js",
      "toolkit/modules/tests/browser/browser_BrowserUtils.js",
      "toolkit/modules/tests/browser/browser_web_channel.js",
      "toolkit/modules/tests/browser/file_web_channel_iframe.html",
      "toolkit/modules/tests/chrome/test_bug544442_checkCert.xhtml",
      "toolkit/mozapps/downloads/tests/browser/browser_unknownContentType_extension.js",
      "toolkit/mozapps/downloads/tests/browser/browser_unknownContentType_policy.js",
      "toolkit/mozapps/extensions/test/browser/browser_file_xpi_no_process_switch.js",
      "toolkit/mozapps/extensions/test/browser/browser_history_navigation.js",
      "toolkit/mozapps/extensions/test/browser/browser_html_detail_view.js",
      "toolkit/mozapps/extensions/test/browser/browser_html_discover_view.js",
      "toolkit/mozapps/extensions/test/browser/browser_html_recent_updates.js",
      "toolkit/mozapps/extensions/test/browser/browser_html_recommendations.js",
      "toolkit/mozapps/extensions/test/browser/browser_html_updates.js",
      "toolkit/mozapps/extensions/test/browser/browser_installssl.js",
      "toolkit/mozapps/extensions/test/browser/browser_updatessl.js",
      "toolkit/mozapps/extensions/test/browser/head.js",
      "toolkit/mozapps/extensions/test/browser/head_abuse_report.js",
      "toolkit/mozapps/extensions/test/browser/head_disco.js",
      "toolkit/mozapps/extensions/test/browser/webapi_checknavigatedwindow.html",
      "toolkit/mozapps/extensions/test/xpinstall/browser_amosigned_trigger.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_amosigned_trigger_iframe.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_auth.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_auth2.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_auth3.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_auth4.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_badhash.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_badhashtype.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_block_fullscreen_prompt.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_bug540558.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_bug645699.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_bug645699_postDownload.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_bug672485.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_containers.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_cookies.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_cookies2.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_cookies3.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_cookies4.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_corrupt.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_datauri.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_doorhanger_installs.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_empty.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_hash.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_hash2.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash2.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash3.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash4.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash5.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_httphash6.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_installchrome.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_newwindow.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_offline.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_privatebrowsing.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_relative.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_softwareupdate.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_trigger_redirect.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_unsigned_trigger.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_unsigned_trigger_iframe.js",
      "toolkit/mozapps/extensions/test/xpinstall/browser_unsigned_trigger_xorigin.js",
      "toolkit/mozapps/extensions/test/xpinstall/bug645699.html",
      "toolkit/mozapps/extensions/test/xpinstall/head.js",
      "tools/profiler/tests/browser/head.js",
      "uriloader/exthandler/tests/mochitest/browser_download_idn_blocklist.js",
      "uriloader/exthandler/tests/mochitest/head.js",
      "xpfe/appshell/test/test_windowlessBrowser.xhtml",
    ],
    rules: {
      "@microsoft/sdl/no-insecure-url": "off",
    },
  },
  {
    files: [
      // Tests specific to JSM
      "dom/encoding/test/test_stringencoding.xhtml",
      "dom/url/tests/test_bug883784.xhtml",
      "dom/url/tests/test_url.xhtml",
      "dom/url/tests/test_worker_url.xhtml",
      "dom/workers/test/test_chromeWorkerJSM.xhtml",
      "js/xpconnect/tests/browser/browser_import_mapped_jsm.js",
      "js/xpconnect/tests/chrome/test_chrometoSource.xhtml",
      "js/xpconnect/tests/chrome/test_expandosharing.xhtml",
      "js/xpconnect/tests/chrome/test_xrayic.xhtml",
      // Code that can't be cleaned until we're ready to remove the old loader.
      "js/xpconnect/loader/XPCOMUtils.sys.mjs",
    ],
    rules: {
      "mozilla/reject-chromeutils-import": "off",
    },
  },
  {
    files: ["toolkit/**"],
    excludedFiles: ["toolkit/**/test/**", "toolkit/**/tests/**"],
    rules: {
      "mozilla/no-browser-refs-in-toolkit": "warn",
    },
  },
  {
    // TODO: Bug TBD - Finish enabling no-shadow with builtinGlobals: true
    // for system modules.
    files: [
      "browser/components/extensions/Extension*.sys.mjs",
      "docshell/base/URIFixup.sys.mjs",
      "dom/base/ContentAreaDropListener.sys.mjs",
      "dom/manifest/ImageObjectProcessor.sys.mjs",
      "dom/media/PeerConnection.sys.mjs",
      "dom/system/NetworkGeolocationProvider.sys.mjs",
      "dom/xslt/xslt/txEXSLTRegExFunctions.sys.mjs",
      "layout/tools/reftest/reftest.sys.mjs",
      "mobile/shared/**/*.sys.mjs",
      "netwerk/test/browser/cookie_filtering_helper.sys.mjs",
      "netwerk/test/httpserver/httpd.sys.mjs",
      "remote/cdp/**/*.sys.mjs",
      "remote/marionette/**/*.sys.mjs",
      "remote/server/WebSocketHandshake.sys.mjs",
      "remote/shared/**/*.sys.mjs",
      "remote/webdriver-bidi/**/*.sys.mjs",
      "security/manager/ssl/RemoteSecuritySettings.sys.mjs",
      "services/common/**/*.sys.mjs",
      "services/crypto/**/*.sys.mjs",
      "services/fxaccounts/**/*.sys.mjs",
      "services/settings/**/*.sys.mjs",
      "services/sync/**/*.sys.mjs",
      "testing/mochitest/BrowserTestUtils/BrowserTestUtils.sys.mjs",
      "testing/modules/**/*.sys.mjs",
      "testing/specialpowers/content/SpecialPowersChild.sys.mjs",
      "testing/talos/talos/**/*.sys.mjs",
      "toolkit/actors/**/*.sys.mjs",
      "toolkit/components/**/*.sys.mjs",
      "toolkit/crashreporter/CrashSubmit.sys.mjs",
      "toolkit/modules/**/*.sys.mjs",
      "toolkit/mozapps/**/*.sys.mjs",
    ],
    rules: {
      "no-shadow": ["warn", { allow: ["event"], builtinGlobals: true }],
    },
  },
  {
    // TODO: Bug TBD - Finish enabling no-shadow for all files.
    files: [
      "browser/components/extensions/**",
      "docshell/test/**",
      "devtools/client/framework/**",
      "dom/fetch/tests/**",
      "dom/indexedDB/test/**",
      "dom/media/**",
      "dom/notification/test/browser/browser_permission_dismiss.js",
      "dom/security/**",
      "dom/tests/browser/**",
      "dom/xslt/tests/browser/file_bug1309630.html",
      "extensions/spellcheck/tests/chrome/test_add_remove_dictionaries.xhtml",
      "gfx/layers/layerviewer/layerTreeView.js",
      "image/test/browser/browser_animated_css_image.js",
      "js/src/builtin/Promise.js",
      "js/xpconnect/tests/**",
      "layout/tools/reftest/reftest-content.js",
      "mobile/android/geckoview/**",
      "mobile/shared/components/extensions/**",
      "netwerk/**",
      "remote/cdp/**",
      "remote/shared/**",
      "remote/webdriver-bidi/**",
      "security/manager/**",
      "security/sandbox/**",
      "taskcluster/docker/periodic-updates/scripts/genHPKPStaticPins.js",
      "testing/condprofile/condprof/scenarii/bookmark.js",
      "testing/**",
      "toolkit/components/**",
      "toolkit/content/**",
      "toolkit/crashreporter/**",
      "toolkit/modules/subprocess/subprocess_shared.js",
      "toolkit/modules/tests/**",
      "toolkit/mozapps/**",
      "toolkit/themes/shared/design-system/tests/try-runner.js",
      "tools/profiler/tests/**",
      "tools/tryselect/selectors/chooser/static/filter.js",
    ],
    excludedFiles: [
      "**/*.sys.mjs",
      "toolkit/components/narrate/**",
      "toolkit/components/reader/**",
    ],
    rules: {
      "no-shadow": "off",
    },
  },
];

module.exports = { rollouts };
