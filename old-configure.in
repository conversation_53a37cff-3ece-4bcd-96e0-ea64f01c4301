dnl -*- Mode: Autoconf; tab-width: 4; indent-tabs-mode: nil; -*-
dnl vi: set tabstop=4 shiftwidth=4 expandtab syntax=m4:
dnl This Source Code Form is subject to the terms of the Mozilla Public
dnl License, v. 2.0. If a copy of the MPL was not distributed with this
dnl file, You can obtain one at http://mozilla.org/MPL/2.0/.

dnl Process this file with autoconf to produce a configure script.
dnl ========================================================
AC_PREREQ(2.13)
AC_INIT(config/config.mk)
AC_CONFIG_AUX_DIR(${srcdir}/build/autoconf)

dnl ========================================================
dnl =
dnl = Application
dnl =
dnl ========================================================

MOZ_BRANDING_DIRECTORY=
MOZ_OFFICIAL_BRANDING=

dnl ========================================================
dnl = Trademarked Branding
dnl ========================================================
MOZ_ARG_ENABLE_BOOL(official-branding,
[  --enable-official-branding
                          Enable Official mozilla.org Branding
                          Do not distribute builds with
                          --enable-official-branding unless you have
                          permission to use trademarks per
                          http://www.mozilla.org/foundation/trademarks/ .],
    MOZ_OFFICIAL_BRANDING=1,
    MOZ_OFFICIAL_BRANDING=)

# Allow the application to influence configure with a confvars.sh script.
AC_MSG_CHECKING([if app-specific confvars.sh exists])
if test -f "${srcdir}/${MOZ_BUILD_APP}/confvars.sh" ; then
  AC_MSG_RESULT([${srcdir}/${MOZ_BUILD_APP}/confvars.sh])
  . "${srcdir}/${MOZ_BUILD_APP}/confvars.sh"
else
  AC_MSG_RESULT([no])
fi

AC_SUBST(MOZ_OFFICIAL_BRANDING)
if test -n "$MOZ_OFFICIAL_BRANDING"; then
  if test -z "$MOZ_OFFICIAL_BRANDING_DIRECTORY"; then
    AC_MSG_ERROR([You must specify MOZ_OFFICIAL_BRANDING_DIRECTORY to use --enable-official-branding.])
  else
    MOZ_BRANDING_DIRECTORY=${MOZ_OFFICIAL_BRANDING_DIRECTORY}
    AC_DEFINE(MOZ_OFFICIAL_BRANDING)
  fi
fi

MOZ_ARG_WITH_STRING(branding,
[  --with-branding=dir     Use branding from the specified directory.],
    MOZ_BRANDING_DIRECTORY=$withval)

REAL_BRANDING_DIRECTORY="${MOZ_BRANDING_DIRECTORY}"
if test -z "$REAL_BRANDING_DIRECTORY"; then
  REAL_BRANDING_DIRECTORY=${MOZ_BUILD_APP}/branding/nightly
fi

if test -f "${_topsrcdir}/$REAL_BRANDING_DIRECTORY/configure.sh"; then
  . "${_topsrcdir}/$REAL_BRANDING_DIRECTORY/configure.sh"
fi

AC_SUBST(MOZ_BRANDING_DIRECTORY)


dnl ========================================================
dnl = Mac bundle name
dnl ========================================================

MOZ_MACBUNDLE_NAME=$MOZ_APP_DISPLAYNAME
if test "$MOZ_MACBUNDLE_NAME_PREFIX"; then
  MOZ_MACBUNDLE_NAME="${MOZ_MACBUNDLE_NAME_PREFIX}${MOZ_MACBUNDLE_NAME}"
fi

if test "$MOZ_DEBUG"; then
  MOZ_MACBUNDLE_NAME="${MOZ_MACBUNDLE_NAME}Debug.app"
else
  MOZ_MACBUNDLE_NAME=${MOZ_MACBUNDLE_NAME}.app
fi
AC_SUBST(MOZ_MACBUNDLE_NAME)

dnl Mac bundle identifier (based on MOZ_APP_DISPLAYNAME)
# If the MOZ_MACBUNDLE_ID is defined in the configure.sh, use it
# Otherwise, use MOZ_APP_DISPLAYNAME
if test -z "$MOZ_MACBUNDLE_ID"; then
   MOZ_MACBUNDLE_ID=`echo $MOZ_APP_DISPLAYNAME | tr 'A-Z' 'a-z' | tr -dc 'a-z-'`
fi
MOZ_MACBUNDLE_ID=${MOZ_DISTRIBUTION_ID}.${MOZ_MACBUNDLE_ID}
if test "$MOZ_DEBUG"; then
  MOZ_MACBUNDLE_ID=${MOZ_MACBUNDLE_ID}debug
fi

AC_DEFINE_UNQUOTED(MOZ_MACBUNDLE_ID,$MOZ_MACBUNDLE_ID)
AC_SUBST(MOZ_MACBUNDLE_ID)

dnl ========================================================
dnl = Child Process Name for IPC
dnl ========================================================
MOZ_CHILD_PROCESS_APPNAME="${MOZ_APP_DISPLAYNAME}CP"

AC_SUBST(MOZ_CHILD_PROCESS_APPNAME)

dnl ==========================================================
dnl = Mac Media Plugin Helper (GMP Child) Process Name for IPC
dnl ==========================================================
MOZ_EME_PROCESS_NAME="media-plugin-helper"
MOZ_EME_PROCESS_NAME_BRANDED="$MOZ_APP_DISPLAYNAME Media Plugin Helper"
MOZ_EME_PROCESS_BUNDLENAME="${MOZ_EME_PROCESS_NAME}.app"
# Generate a lower case string with no spaces to be used as the bundle ID
# for the EME helper .app of the form org.mozilla.<executable-name>.
MOZ_EME_PROCESS_BUNDLEID=`echo "$MOZ_APP_DISPLAYNAME" | tr ' ' '-'`
MOZ_EME_PROCESS_BUNDLEID=`echo "$MOZ_EME_PROCESS_BUNDLEID" | tr 'A-Z' 'a-z'`
MOZ_EME_PROCESS_BUNDLEID=${MOZ_EME_PROCESS_BUNDLEID}-${MOZ_EME_PROCESS_NAME}
MOZ_EME_PROCESS_BUNDLEID=${MOZ_DISTRIBUTION_ID}.${MOZ_EME_PROCESS_BUNDLEID}

AC_SUBST(MOZ_EME_PROCESS_NAME)
AC_SUBST(MOZ_EME_PROCESS_NAME_BRANDED)
AC_SUBST(MOZ_EME_PROCESS_BUNDLENAME)
AC_SUBST(MOZ_EME_PROCESS_BUNDLEID)

# The following variables are available to branding and application
# configuration ($BRANDING/configure.sh and $APPLICATION/confvars.sh):
# - MOZ_APP_DISPLAYNAME: Used in user-visible fields (DLL properties,
# Mac Bundle name, Updater, Installer), it is typically used for nightly
# builds (e.g. Aurora for Firefox).
# - MOZ_APP_PROFILE: When set, used for application.ini's
# "Profile" field, which controls profile location.

# The following environment variables used to have an effect, but don't anymore:
# - MOZ_APP_VERSION: Defines the application version number. This was replaced with
# the contents from the version.txt file in the application directory, or
# browser/config/version.txt if there isn't one.
# - MOZ_APP_VERSION_DISPLAY: Defines the application version number. Used
# in the "About" window. This was replaced with the contents from the
# version_display.txt or version.txt in the application directory, or
# browser/config/version_display.txt.

AC_SUBST(MOZ_APP_DISPLAYNAME)
AC_SUBST(MOZ_APP_PROFILE)
AC_DEFINE_UNQUOTED(MOZ_APP_UA_VERSION, "$MOZ_APP_VERSION")

dnl Set various defines and substitutions
dnl ========================================================

AC_SUBST(MOZ_DEV_EDITION)
if test -n "$MOZ_DEV_EDITION"; then
    AC_DEFINE(MOZ_DEV_EDITION)
fi

dnl Spit out some output
dnl ========================================================

MOZ_CREATE_CONFIG_STATUS()

rm -fr confdefs* $ac_clean_files
