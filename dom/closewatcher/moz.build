# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: Core & HTML")

EXPORTS.mozilla.dom += [
    "CloseWatcher.h",
    "CloseWatcherManager.h",
]

UNIFIED_SOURCES += [
    "CloseWatcher.cpp",
    "CloseWatcherManager.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"
