<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background-color: rgb(255, 255, 255);" version="1.1" width="841px" height="861px" viewBox="-0.5 -0.5 841 861" content="&lt;mxfile modified=&quot;2019-04-26T17:33:16.547Z&quot; host=&quot;www.draw.io&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:68.0) Gecko/20100101 Firefox/68.0&quot; etag=&quot;ryQDoUVJ1oS_b3fguxas&quot; version=&quot;10.6.5&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;GcJpJ4Eku4OUxVByAJcz&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="20" y="160.5" width="760" height="200" rx="30" ry="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="20" y="440.5" width="480" height="400" rx="60" ry="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="540" y="440.5" width="240" height="400" rx="36" ry="36" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(80.5,168.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="98" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 99px; white-space: nowrap; overflow-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Browser Process</div></div></foreignObject><text x="49" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Browser Process</text></switch></g><g transform="translate(82.5,814.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="95" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 96px; white-space: nowrap; overflow-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Content Process</div></div></div></foreignObject><text x="48" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">&lt;div&gt;Content Process&lt;/div&gt;</text></switch></g><g transform="translate(612.5,814.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="95" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 96px; white-space: nowrap; overflow-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Content Process</div></div></div></foreignObject><text x="48" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">&lt;div&gt;Content Process&lt;/div&gt;</text></switch></g><path d="M 130 256.87 L 130 271 L 130 261 L 130 274.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 251.62 L 133.5 258.62 L 130 256.87 L 126.5 258.62 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 279.38 L 126.5 272.38 L 130 274.13 L 133.5 272.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="70" y="190.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(71.5,199.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="116" height="41" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 116px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>&lt;xul:browser src="a.com"/&gt;</div><div>nsFrameLoader<br /></div></div></div></foreignObject><text x="58" y="27" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 130 346.87 L 130 454.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 341.62 L 133.5 348.62 L 130 346.87 L 126.5 348.62 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 459.88 L 126.5 452.88 L 130 454.63 L 133.5 452.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(103.5,394.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:#ffffff;"><font style="font-size: 12px">PBrowser</font></div></div></foreignObject><text x="26" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="280.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(90.5,304.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="79" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserParent</div></div></foreignObject><text x="40" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserParent</text></switch></g><path d="M 130 526.87 L 130 541 L 130 531 L 130 544.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 521.62 L 133.5 528.62 L 130 526.87 L 126.5 528.62 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 549.38 L 126.5 542.38 L 130 544.13 L 133.5 542.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="70" y="460.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(94.5,484.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserChild</div></div></foreignObject><text x="36" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserChild</text></switch></g><path d="M 130 617.37 L 130 631.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 612.12 L 133.5 619.12 L 130 617.37 L 126.5 619.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 636.88 L 126.5 629.88 L 130 631.63 L 133.5 629.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="70" y="550.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(89.5,574.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="81" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 82px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">nsWebBrowser</div></div></foreignObject><text x="41" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">nsWebBrowser</text></switch></g><path d="M 130 704.37 L 130 723.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 699.12 L 133.5 706.12 L 130 704.37 L 126.5 706.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 130 728.88 L 126.5 721.88 L 130 723.63 L 133.5 721.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="70" y="638" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(71.5,647.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="116" height="41" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 116px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>&lt;iframe src="b.com"/&gt;</div><div>nsFrameLoader</div></div></div></foreignObject><text x="58" y="27" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 196.37 760 L 480 760 L 480 236 L 593.63 236" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 191.12 760 L 198.12 756.5 L 196.37 760 L 198.12 763.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 598.88 236 L 591.88 239.5 L 593.63 236 L 591.88 232.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(436.5,384.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="87" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:#ffffff;">PBrowserBridge</div></div></foreignObject><text x="44" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">PBrowserBridge</text></switch></g><rect x="70" y="729.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(76.5,753.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserBridgeChild</div></div></foreignObject><text x="53" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserBridgeChild</text></switch></g><path d="M 390 347.37 L 390 454.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 342.12 L 393.5 349.12 L 390 347.37 L 386.5 349.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 459.88 L 386.5 452.88 L 390 454.63 L 393.5 452.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(363.5,394.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:#ffffff;"><div style="font-size: 12px"><font style="font-size: 12px">PBrowser</font></div></div></div></foreignObject><text x="26" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="330" y="280.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(350.5,304.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="79" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserParent</div></div></foreignObject><text x="40" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserParent</text></switch></g><path d="M 390 527.37 L 390 541 L 390 531 L 390 544.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 522.12 L 393.5 529.12 L 390 527.37 L 386.5 529.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 549.38 L 386.5 542.38 L 390 544.13 L 393.5 542.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="330" y="460.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(354.5,484.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserChild</div></div></foreignObject><text x="36" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserChild</text></switch></g><path d="M 660 347.37 L 660 454.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 342.12 L 663.5 349.12 L 660 347.37 L 656.5 349.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 459.38 L 656.5 452.38 L 660 454.13 L 663.5 452.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(633.5,394.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:#ffffff;"><font style="font-size: 12px">PBrowser</font></div></div></foreignObject><text x="26" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="600" y="280.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(620.5,304.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="79" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserParent</div></div></foreignObject><text x="40" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserParent</text></switch></g><path d="M 660 527.37 L 660 541 L 660 531 L 660 544.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 522.12 L 663.5 529.12 L 660 527.37 L 656.5 529.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 549.38 L 656.5 542.38 L 660 544.13 L 663.5 542.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="600" y="460.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(624.5,484.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserChild</div></div></foreignObject><text x="36" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserChild</text></switch></g><path d="M 390 257.37 L 390 271 L 390 261 L 390 274.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 252.12 L 393.5 259.12 L 390 257.37 L 386.5 259.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 279.38 L 386.5 272.38 L 390 274.13 L 393.5 272.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="330" y="190.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(332.5,214.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="114" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 115px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserBridgeParent</div></div></foreignObject><text x="57" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserBridgeParent</text></switch></g><path d="M 660 257.37 L 660 274.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 252.12 L 663.5 259.12 L 660 257.37 L 656.5 259.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 279.88 L 656.5 272.88 L 660 274.63 L 663.5 272.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="600" y="190.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(602.5,214.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="114" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 115px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserBridgeParent</div></div></foreignObject><text x="57" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserBridgeParent</text></switch></g><rect x="330" y="550.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(349.5,574.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="81" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 82px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">nsWebBrowser</div></div></foreignObject><text x="41" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">nsWebBrowser</text></switch></g><path d="M 660 617.37 L 660 631.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 612.12 L 663.5 619.12 L 660 617.37 L 656.5 619.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 636.88 L 656.5 629.88 L 660 631.63 L 663.5 629.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="600" y="550.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(619.5,574.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="81" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 82px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">nsWebBrowser</div></div></foreignObject><text x="41" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">nsWebBrowser</text></switch></g><path d="M 660 704.37 L 660 723.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 699.12 L 663.5 706.12 L 660 704.37 L 656.5 706.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 728.88 L 656.5 721.88 L 660 723.63 L 663.5 721.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="600" y="638" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(601.5,647.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="116" height="41" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 116px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>&lt;iframe src="a.com"/&gt;</div><div>nsFrameLoader</div></div></div></foreignObject><text x="58" y="27" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 593.63 760 L 560 760 L 560 239 C 563.9 239 563.9 233 560 233 L 560 233 L 560 206 L 456.37 206" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 598.88 760 L 591.88 763.5 L 593.63 760 L 591.88 756.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 451.12 206 L 458.12 202.5 L 456.37 206 L 458.12 209.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(517.5,412.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="87" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:#ffffff;">PBrowserBridge</div></div></foreignObject><text x="44" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">PBrowserBridge</text></switch></g><rect x="600" y="729.5" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(606.5,753.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BrowserBridgeChild</div></div></foreignObject><text x="53" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">BrowserBridgeChild</text></switch></g><g transform="translate(24.5,19.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="249" height="130" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 130px; max-width: 790px; width: 250px; white-space: normal; overflow-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><h1>Gecko IPC for Fission<br /></h1><div>Example:</div><div>&lt;xul:browser src="a.com"/&gt;</div><div>  &lt;iframe src="b.com"/&gt;</div><div>    &lt;iframe src="a.com"/&gt;<br /></div><div><br /></div></div></div></foreignObject><text x="125" y="71" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g></g></svg>