<!DOCTYPE html>
<html>

<head>
<title>WebCrypto Test Suite</title>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
<link rel="stylesheet" href="./test_WebCrypto.css"/>
<script src="/tests/SimpleTest/SimpleTest.js"></script>

<!-- Utilities for manipulating ABVs -->
<script src="util.js"></script>

<!-- A simple wrapper around IndexedDB -->
<script src="simpledb.js"></script>

<!-- Test vectors drawn from the literature -->
<script src="./test-vectors.js"></script>

<!-- General testing framework -->
<script src="./test-array.js"></script>

<script>/* <![CDATA[*/
"use strict";

TestArray.addTest(
  "Test that we properly normalize algorithm names",
  function() {
    var that = this;
    var alg = { name: "hmac", hash: {name: "sHa-256"} };

    function doGenerateAesKey() {
      return crypto.subtle.generateKey({ name: "AES-gcm", length: 192 }, false, ["encrypt"]);
    }

    function doGenerateRsaOaepKey() {
      var algo = {
        name: "rsa-OAEP",
        hash: "sha-1",
        modulusLength: 2048,
        publicExponent: new Uint8Array([0x01, 0x00, 0x01]),
      };
      return crypto.subtle.generateKey(algo, false, ["encrypt", "decrypt"]);
    }

    function doGenerateRsaSsaPkcs1Key() {
      return crypto.subtle.importKey("pkcs8", tv.pkcs8, { name: "RSASSA-pkcs1-V1_5", hash: "SHA-1" }, true, ["sign"]);
    }

    crypto.subtle.generateKey(alg, false, ["sign"])
      .then(doGenerateAesKey)
      .then(doGenerateRsaOaepKey)
      .then(doGenerateRsaSsaPkcs1Key)
      .then(complete(that), error(that));
  }
);

/* ]]>*/</script>
</head>

<body>

<div id="content">
	<div id="head">
		<b>Web</b>Crypto<br>
	</div>

    <div id="start" onclick="start();">RUN ALL</div>

    <div id="resultDiv" class="content">
    Summary:
    <span class="pass"><span id="passN">0</span> passed, </span>
    <span class="fail"><span id="failN">0</span> failed, </span>
    <span class="pending"><span id="pendingN">0</span> pending.</span>
    <br/>
    <br/>

    <table id="results">
        <tr>
            <th>Test</th>
            <th>Result</th>
            <th>Time</th>
        </tr>
    </table>

    </div>

    <div id="foot"></div>
</div>

</body>
</html>
