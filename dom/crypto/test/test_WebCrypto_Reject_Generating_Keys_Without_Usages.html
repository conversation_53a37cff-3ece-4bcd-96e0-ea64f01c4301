<!DOCTYPE html>
<html>

<head>
<title>WebCrypto Test Suite</title>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
<link rel="stylesheet" href="./test_WebCrypto.css"/>
<script src="/tests/SimpleTest/SimpleTest.js"></script>

<!-- Utilities for manipulating ABVs -->
<script src="util.js"></script>

<!-- A simple wrapper around IndexedDB -->
<script src="simpledb.js"></script>

<!-- Test vectors drawn from the literature -->
<script src="./test-vectors.js"></script>

<!-- General testing framework -->
<script src="./test-array.js"></script>

<script>/* <![CDATA[*/
"use strict";

// Generating 2048-bit keys takes some time.
SimpleTest.requestLongerTimeout(2);

TestArray.addTest(
  "Test that we reject generating keys without any usage",
  function() {
    var that = this;
    var alg = {
      name: "RSA-OAEP",
      hash: "SHA-256",
      modulusLength: 2048,
      publicExponent: new Uint8Array([0x01, 0x00, 0x01]),
    };

    function generateKey(usages) {
      return crypto.subtle.generateKey(alg, false, usages);
    }

    generateKey(["encrypt", "decrypt"]).then(function() {
      return generateKey(["decrypt"]);
    }).then(function() {
      return Promise.any([generateKey(["encrypt"]), generateKey(["sign"])]);
    }, error(that)).then(error(that), complete(that));
  }
);
/* ]]>*/</script>
</head>

<body>

<div id="content">
    <div id="head">
        <b>Web</b>Crypto<br>
    </div>

    <div id="start" onclick="start();">RUN ALL</div>

    <div id="resultDiv" class="content">
    Summary:
    <span class="pass"><span id="passN">0</span> passed, </span>
    <span class="fail"><span id="failN">0</span> failed, </span>
    <span class="pending"><span id="pendingN">0</span> pending.</span>
    <br/>
    <br/>

    <table id="results">
        <tr>
            <th>Test</th>
            <th>Result</th>
            <th>Time</th>
        </tr>
    </table>

    </div>

    <div id="foot"></div>
</div>

</body>
</html>
