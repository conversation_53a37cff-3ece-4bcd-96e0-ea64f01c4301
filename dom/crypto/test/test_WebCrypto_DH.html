<!DOCTYPE html>
<html>

<head>
<title>WebCrypto Test Suite</title>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
<link rel="stylesheet" href="./test_WebCrypto.css"/>
<script src="/tests/SimpleTest/SimpleTest.js"></script>

<!-- Utilities for manipulating ABVs -->
<script src="util.js"></script>

<!-- A simple wrapper around IndexedDB -->
<script src="simpledb.js"></script>

<!-- Test vectors drawn from the literature -->
<script src="./test-vectors.js"></script>

<!-- General testing framework -->
<script src="./test-array.js"></script>

<script>/* <![CDATA[*/
"use strict";
// DH is not permitted, so ensure all entry point methods error
// -----------------------------------------------------------------------------
TestArray.addTest(
  "Generate a DH key",
  function() {
    var that = this;
    var alg = {
      name: "DH",
      prime: tv.dh.prime,
      generator: new Uint8Array([0x02]),
    };
    crypto.subtle.generateKey(alg, false, ["deriveKey", "deriveBits"])
      .then(error(that), complete(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Raw import/export of a public DH key",
  function() {
    var that = this;
    var alg = {
      name: "DH",
      prime: tv.dh_nist.prime,
      generator: tv.dh_nist.gen,
    };

    crypto.subtle.importKey("raw", tv.dh_nist.raw, alg, true, ["deriveBits"])
      .then(error(that), complete(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "SPKI import/export of a public DH key",
  function() {
    var that = this;

    crypto.subtle.importKey("spki", tv.dh_nist.spki, "DH", true, ["deriveBits"])
      .then(error(that), complete(that));
  }
);
/* ]]>*/</script>
</head>

<body>

<div id="content">
	<div id="head">
		<b>Web</b>Crypto<br>
	</div>

    <div id="start" onclick="start();">RUN ALL</div>

    <div id="resultDiv" class="content">
    Summary:
    <span class="pass"><span id="passN">0</span> passed, </span>
    <span class="fail"><span id="failN">0</span> failed, </span>
    <span class="pending"><span id="pendingN">0</span> pending.</span>
    <br/>
    <br/>

    <table id="results">
        <tr>
            <th>Test</th>
            <th>Result</th>
            <th>Time</th>
        </tr>
    </table>

    </div>

    <div id="foot"></div>
</div>

</body>
</html>
