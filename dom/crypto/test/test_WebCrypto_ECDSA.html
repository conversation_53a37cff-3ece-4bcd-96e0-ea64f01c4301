<!DOCTYPE html>
<html>

<head>
<title>WebCrypto Test Suite</title>
<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
<link rel="stylesheet" href="./test_WebCrypto.css"/>
<script src="/tests/SimpleTest/SimpleTest.js"></script>

<!-- Utilities for manipulating ABVs -->
<script src="util.js"></script>

<!-- A simple wrapper around IndexedDB -->
<script src="simpledb.js"></script>

<!-- Test vectors drawn from the literature -->
<script src="./test-vectors.js"></script>

<!-- General testing framework -->
<script src="./test-array.js"></script>

<script>/* <![CDATA[*/
"use strict";

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Generate an ECDSA key for named curve P-256",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-256" };
    crypto.subtle.generateKey(alg, false, ["sign", "verify"]).then(
      complete(that, function(x) {
        return exists(x.publicKey) &&
               (x.publicKey.algorithm.name == alg.name) &&
               (x.publicKey.algorithm.namedCurve == alg.namedCurve) &&
               (x.publicKey.type == "public") &&
               x.publicKey.extractable &&
               (x.publicKey.usages.length == 1) &&
               (x.publicKey.usages[0] == "verify") &&
               exists(x.privateKey) &&
               (x.privateKey.algorithm.name == alg.name) &&
               (x.privateKey.algorithm.namedCurve == alg.namedCurve) &&
               (x.privateKey.type == "private") &&
               !x.privateKey.extractable &&
               (x.privateKey.usages.length == 1) &&
               (x.privateKey.usages[0] == "sign");
      }),
      error(that)
    );
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "ECDSA JWK import and verify a known-good signature",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    function doVerify(x) {
      return crypto.subtle.verify(alg, x, tv.ecdsa_verify.sig, tv.ecdsa_verify.data);
    }

    crypto.subtle.importKey("jwk", tv.ecdsa_verify.pub_jwk, alg, true, ["verify"])
      .then(doVerify)
      .then(complete(that, x => x), error(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "ECDSA key generation with public key export",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-256", hash: "SHA-256" };
    var msg = Uint8Array.from([1]);

    crypto.subtle.generateKey(alg, false, ["sign", "verify"])
      .then(pair => Promise.all([
          crypto.subtle.sign(alg, pair.privateKey, msg),
          crypto.subtle.exportKey("spki", pair.publicKey)
            .then(spki => crypto.subtle.importKey("spki", spki, alg, false, ["verify"])),
      ]))
      .then(sigAndKey => crypto.subtle.verify(alg, sigAndKey[1], sigAndKey[0], msg))
      .then(complete(that, x => x), error(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "ECDSA JWK import and reject a known-bad signature",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    function doVerify(x) {
      return crypto.subtle.verify(alg, x, tv.ecdsa_verify.sig_tampered,
                                          tv.ecdsa_verify.data);
    }

    crypto.subtle.importKey("jwk", tv.ecdsa_verify.pub_jwk, alg, true, ["verify"])
      .then(doVerify)
      .then(complete(that, x => !x), error(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "ECDSA sign/verify round-trip",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };
    var pubKey;


    function doSign(keyPair) {
      pubKey = keyPair.publicKey;
      return crypto.subtle.sign(alg, keyPair.privateKey, tv.ecdsa_verify.data);
    }
    function doVerify(sig) {
      return crypto.subtle.verify(alg, pubKey, sig, tv.ecdsa_verify.data);
    }

    crypto.subtle.generateKey(alg, true, ["sign", "verify"])
      .then(doSign)
      .then(doVerify)
      .then(complete(that, x => x), error(that));
  }
);


// -----------------------------------------------------------------------------
TestArray.addTest(
  "Verify that ECDSA import fails with a key with a mismatched 'alg' field",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    crypto.subtle.importKey("jwk", tv.ecdsa_jwk_alg_mismatch.pub_jwk, alg, true, ["verify"])
      .then(error(that), complete(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Verify that ECDSA import fails with a key with a mismatched 'crv' field",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    crypto.subtle.importKey("jwk", tv.ecdsa_jwk_crv_mismatch.pub_jwk, alg, true, ["verify"])
      .then(error(that), complete(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Verify that ECDSA import fails with a known-bad public key",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    crypto.subtle.importKey("jwk", tv.ecdsa_bad.pub_jwk, alg, true, ["verify"])
      .then(error(that), complete(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Raw import/export of a public ECDSA key (P-521)",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    function doExport(x) {
      return crypto.subtle.exportKey("raw", x);
    }

    crypto.subtle.importKey("raw", tv.ecdsa_verify.raw, alg, true, ["verify"])
      .then(doExport)
      .then(memcmp_complete(that, tv.ecdsa_verify.raw), error(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "ECDSA raw import and verify a known-good signature",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-521", hash: "SHA-512" };

    function doVerify(x) {
      return crypto.subtle.verify(alg, x, tv.ecdsa_verify.sig, tv.ecdsa_verify.data);
    }

    crypto.subtle.importKey("raw", tv.ecdsa_verify.raw, alg, true, ["verify"])
      .then(doVerify)
      .then(complete(that, x => x), error(that));
  }
);

// -----------------------------------------------------------------------------
TestArray.addTest(
  "Importing an RSA key as an ECDSA key should fail",
  function() {
    var that = this;
    var alg = { name: "ECDSA", namedCurve: "P-256" };

    // tv.spki is the SPKI for an RSA key, not an ECDSA key
    crypto.subtle.importKey("spki", tv.spki, alg, false, ["verify"])
      .then(error(that), complete(that));
  }
);

/* ]]>*/</script>
</head>

<body>

<div id="content">
        <div id="head">
                <b>Web</b>Crypto<br>
        </div>

    <div id="start" onclick="start();">RUN ALL</div>

    <div id="resultDiv" class="content">
    Summary:
    <span class="pass"><span id="passN">0</span> passed, </span>
    <span class="fail"><span id="failN">0</span> failed, </span>
    <span class="pending"><span id="pendingN">0</span> pending.</span>
    <br/>
    <br/>

    <table id="results">
        <tr>
            <th>Test</th>
            <th>Result</th>
            <th>Time</th>
        </tr>
    </table>

    </div>

    <div id="foot"></div>
</div>

</body>
</html>
