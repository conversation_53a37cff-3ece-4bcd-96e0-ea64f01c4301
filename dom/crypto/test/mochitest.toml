[DEFAULT]
scheme = "https"
support-files = [
  "file_indexedDB.html",
  "test-array.js",
  "test-vectors.js",
  "test-worker.js",
  "test_WebCrypto.css",
  "util.js",
]

["test_WebCrypto.html"]

["test_WebCrypto_DH.html"]

["test_WebCrypto_ECDH.html"]

["test_WebCrypto_ECDSA.html"]

["test_WebCrypto_HKDF.html"]

["test_WebCrypto_Import_Keys_Too_Long.html"]

["test_WebCrypto_Import_Multiple_Identical_Keys.html"]

["test_WebCrypto_JWK.html"]

["test_WebCrypto_Normalize.html"]

["test_WebCrypto_PBKDF2.html"]

["test_WebCrypto_RSA_OAEP.html"]

["test_WebCrypto_RSA_PSS.html"]

["test_WebCrypto_Reject_Generating_Keys_Without_Usages.html"]

["test_WebCrypto_Structured_Cloning.html"]

["test_WebCrypto_Workers.html"]

["test_WebCrypto_Wrap_Unwrap.html"]

["test_indexedDB.html"]
skip-if = ["!fission"] # Requires iframes to run in separate processes.
fail-if = ["xorigin"] # JavaScript error: https://example.org/tests/dom/crypto/test/test_indexedDB.html?currentTestURL=dom%2Fcrypto%2Ftest%2Ftest_indexedDB.html&closeWhenDone=undefined&showTestReport=true&expected=pass, line 27: SecurityError: Permission denied to access property "frameElement" on cross-origin object
