<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Bug 1188750 - WebCrypto must ensure NSS is initialized before deserializing</title>
</head>
<body>
  <script type="application/javascript">
    let db;

    function err(resolve) {
      return e => resolve(e.target.error.message);
    }

    function openDatabase() {
      return new Promise((resolve, reject) => {
        let request = indexedDB.open("keystore", 1);

        request.onerror = err(reject);
        request.onsuccess = function(event) {
          db = event.target.result;
          resolve();
        };

        request.onupgradeneeded = function(event) {
          db = event.target.result;
          let objectStore = db.createObjectStore("keys", {autoIncrement: true});
          objectStore.transaction.oncomplete = resolve;
        };
      });
    }

    function storeKey(key) {
      return new Promise((resolve, reject) => {
        let transaction = db.transaction("keys", "readwrite");
        transaction.objectStore("keys").put(key, key.type);

        transaction.onabort = err(reject);
        transaction.onerror = err(reject);

        transaction.oncomplete = function() {
          resolve(key);
        };
      });
    }

    function retrieveKey() {
      return new Promise((resolve, reject) => {
        let transaction = db.transaction("keys", "readonly");
        let cursor = transaction.objectStore("keys").openCursor();

        cursor.onerror = err(reject);
        cursor.onabort = err(reject);

        cursor.onsuccess = function(event) {
          try {
            let result = event.target.result;
            resolve(result && result.value);
          } catch (e) {
            reject(e.message);
          }
        };
      });
    }

    function generateKey() {
      let algorithm = {
        name: "RSASSA-PKCS1-v1_5",
        hash: "SHA-256",
        modulusLength: 1024,
        publicExponent: new Uint8Array([0x01, 0x00, 0x01]),
      };

      return crypto.subtle.generateKey(algorithm, true, ["sign", "verify"]);
    }

    openDatabase()
      .then(retrieveKey).then(generateKey).then(storeKey)
      .then(() => parent.postMessage("ok", "*"))
      .catch(error => {
        parent.postMessage(String(error), "*");
      });
  </script>
</body>
</html>
