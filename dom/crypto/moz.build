# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: Security")

EXPORTS.mozilla.dom += [
    "CryptoBuffer.h",
    "CryptoKey.h",
    "KeyAlgorithmProxy.h",
    "WebCryptoCommon.h",
    "WebCryptoTask.h",
]

UNIFIED_SOURCES += [
    "CryptoBuffer.cpp",
    "CryptoKey.cpp",
    "KeyAlgorithmProxy.cpp",
    "WebCryptoTask.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"

LOCAL_INCLUDES += [
    "/security/manager/ssl",
    "/xpcom/build",
]

MOCHITEST_MANIFESTS += ["test/mochitest.toml"]
BROWSER_CHROME_MANIFESTS += ["test/browser/browser.toml"]

# Add libFuzzer configuration directives
include("/tools/fuzzing/libfuzzer-config.mozbuild")
