# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Internationalization")

EXPORTS.mozilla.dom += [
    "TextDecoder.h",
    "TextDecoderStream.h",
    "TextEncoder.h",
    "TextEncoderStream.h",
]

UNIFIED_SOURCES += [
    "TextDecoder.cpp",
    "TextDecoderStream.cpp",
    "TextEncoder.cpp",
    "TextEncoderStream.cpp",
]

FINAL_LIBRARY = "xul"
LOCAL_INCLUDES += [
    "/intl/locale",
]

MOCHITEST_MANIFESTS += [
    "test/mochitest.toml",
]
MOCHITEST_CHROME_MANIFESTS += ["test/chrome.toml"]
XPCSHELL_TESTS_MANIFESTS += ["test/unit/xpcshell.toml"]
