<!DOCTYPE HTML>
<html>
<head>
  <meta charset=utf-8>
  <title>Test for TextEncoder</title>
  <script type="text/javascript" src="/resources/testharness.js"></script>
  <script type="text/javascript" src="/resources/testharnessreport.js"></script>
  <script type="text/javascript" src="test_TextEncoder.js"></script>
  <script type="text/javascript" src="worker_helper.js"></script>
</head>
<body>
<div id="log"></div>
<script>

setup({explicit_done: true});
runTest();

function runTest()
{
  runTextEncoderTests();
}

runTestInWorker(["test_TextEncoder.js"]);

</script>
</body>
</html>
