<!DOCTYPE html>
<html>
<head>
  <meta charset=utf-8>
  <title>Test loading various utf-16 files (little or big endian, with or without BOM)</title>
  <script type="text/javascript" src="/resources/testharness.js"></script>
  <script type="text/javascript" src="/resources/testharnessreport.js"></script>
  <link rel="stylesheet" href="file_utf16_be_bom.css">
  <link rel="stylesheet" href="file_utf16_le_bom.css">
  <script type="text/javascript" src="file_utf16_be_bom.js"></script>
  <script type="text/javascript" src="file_utf16_le_bom.js"></script>
</head>
<body>
<div id="log"></div>
<iframe name="ifr1" src="file_utf16_be_bom.xhtml"></iframe>
<iframe name="ifr2" src="file_utf16_le_bom.xhtml"></iframe>
<iframe name="ifr3" src="file_utf16_le_nobom.xhtml"></iframe>
<script>

runTest();

function runTest() {
  test(function() {
    assert_equals(document.styleSheets[0].cssRules[0].selectorText, ".css1", ".css1 should exist.");
    assert_equals(document.styleSheets[1].cssRules[0].selectorText, ".css2", ".css2 should exist.");
  }, "Test loading utf-16 css files");
  test(function() {
    assert_equals(js1, "Unicåde", "js1 should be defined.");
    assert_equals(js2, "Unicåde", "js2 should be defined.");
  }, "Test loading utf-16 javascript files");
  var tests = [
    {ifr: ifr1, fails: false, name: "Test loading utf-16, big endian, with BOM"},
    {ifr: ifr2, fails: false, name: "Test loading utf-16, little endian, with BOM"},
    {ifr: ifr3, fails: true, name: "Test loading utf-16le xml fails"},
  ];
  tests.forEach(function(test) {
    var t = async_test(test.name);
    var fails = test.fails;
    var ifr = test.ifr;
    ifr.onload = ifr.onerror = function() {
      t.step(fails ? function() {
        assert_equals(ifr.document.body, null, ifr.name + " should NOT load.");
      } : function() {
        assert_true(ifr.document.body instanceof ifr.HTMLBodyElement, ifr.name + " should load.");
      });
      t.done();
    };
  });
}

</script>
</body>
</html>
