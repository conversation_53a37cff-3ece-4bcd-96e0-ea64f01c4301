[DEFAULT]
support-files = [
  "file_TLD.html",
  "file_in_lk_TLD.html",
  "file_jp_TLD.html",
  "file_utf16_be_bom.css",
  "file_utf16_be_bom.js",
  "file_utf16_be_bom.xhtml",
  "file_utf16_le_bom.css",
  "file_utf16_le_bom.js",
  "file_utf16_le_bom.xhtml",
  "file_utf16_le_nobom.xhtml",
  "test_BOMEncoding.js",
  "test_TextDecoder.js",
  "test_TextEncoder.js",
  "unit/test_big5.js",
  "unit/test_euc-jp.js",
  "unit/test_euc-kr.js",
  "unit/test_gbk.js",
  "unit/test_iso-2022-jp.js",
  "unit/test_shift_jis.js",
  "worker_helper.js",
]

["test_TLD.html"]
skip-if = [
  "http3",
  "http2",
]

["test_TextDecoder.html"]

["test_TextEncoder.html"]

["test_in_lk_TLD.html"]
skip-if = [
  "http3",
  "http2",
]

["test_stringencoding.html"]

["test_submit_euckr.html"]

["test_utf16_files.html"]
