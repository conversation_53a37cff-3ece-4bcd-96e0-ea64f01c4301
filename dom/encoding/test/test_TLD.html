<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=910211
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 910211</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">

  /** Test for Bug 910211 **/

  SimpleTest.waitForExplicitFinish();

  var tlds = [
    {'tld': 'tw', 'encoding': 'Big5'},
    {'tld': 'cn', 'encoding': 'GBK'},
    {'tld': 'co.jp', 'encoding': 'Shift_JIS'},
    {'tld': 'fi', 'encoding': 'windows-1252'},
  ];

  var iframe = null;

  var current = null;

  function runTest() {
    iframe = document.getElementsByTagName("iframe")[0];
    window.addEventListener("message", next);
    next(null);
  }

  function next(event) {
    if (event) {
      is(event.data, current.encoding, "Got bad encoding for " + current.tld);
    }
    current = tlds.shift();
    if (!current) {
      SimpleTest.finish();
      return;
    }
    iframe.src = "http://example." + current.tld + "/tests/dom/encoding/test/file_TLD.html";
  }

  </script>
</head>
<body onload="runTest();">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=910211">Mozilla Bug 910211</a>
<p id="display"></p>
<div id="content" style="display: none">
<iframe></iframe>
</div>
<pre id="test">
</pre>
</body>
</html>
