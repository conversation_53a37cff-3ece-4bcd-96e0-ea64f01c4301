<?xml version="1.0"?>
<?xml-stylesheet type="text/css" href="chrome://global/skin"?>
<?xml-stylesheet type="text/css" href="chrome://mochikit/content/tests/SimpleTest/test.css"?>
<window title="Test for StringEncoding API"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>

  <!-- test results are displayed in the html:body -->
  <body xmlns="http://www.w3.org/1999/xhtml">
  </body>

  <!-- test code goes here -->
  <script type="application/javascript"><![CDATA[

  /** Test for StringEncoding API. **/
  // Import our test ESM. We first strip the filename off
  // the chrome url, then append the filename.
  var base = /.*\//.exec(window.location.href)[0];
  const {checkFromESM} = ChromeUtils.importESModule(base + "file_stringencoding.sys.mjs");

  checkFromESM(is);

  ]]></script>
</window>
