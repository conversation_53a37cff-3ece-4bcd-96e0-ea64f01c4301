<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1603712
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 1603712</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">

  /** Test for Bug 1603712 **/

  SimpleTest.waitForExplicitFinish();

  var tlds = [
    {'tld': 'in', 'expectedEncoding': 'Shift_JIS', 'file': 'file_in_lk_TLD.html'},
    {'tld': 'lk', 'expectedEncoding': 'Shift_JIS', 'file': 'file_in_lk_TLD.html'},
    {'tld': 'co.jp', 'expectedEncoding': 'windows-1251', 'file': 'file_jp_TLD.html'},
  ];

  var iframe = null;

  var current = null;

  function runTest() {
    iframe = document.getElementsByTagName("iframe")[0];
    window.addEventListener("message", next);
    next(null);
  }

  function next(event) {
    if (event) {
      is(event.data, current.expectedEncoding, "Got bad encoding for " + current.tld);
    }
    current = tlds.shift();
    if (!current) {
      SimpleTest.finish();
      return;
    }
    iframe.src = "http://example." + current.tld + "/tests/dom/encoding/test/" + current.file;
  }

  </script>
</head>
<body onload="runTest();">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1603712">Mozilla Bug 1603712</a>
<p id="display"></p>
<div id="content" style="display: none">
<iframe></iframe>
</div>
<pre id="test">
</pre>
</body>
</html>
