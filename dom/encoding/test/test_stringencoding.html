<!DOCTYPE HTML>
<html>
<head>
<meta charset=utf-8>
<title>Encoding API Tests</title>
<link rel="stylesheet" href="/resources/testharness.css">
</head>
<body>
<h1>Encoding API Tests</h1>
<div id="log"></div>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
setup({explicit_timeout: true});
</script>

<script type="text/javascript" src="unit/test_singlebytes.js"></script>
<!-- TODO: test for all single-byte encoding indexes -->
<script type="text/javascript" src="unit/test_gbk.js"></script>
<!-- TODO: gb18030 -->
<script type="text/javascript" src="unit/test_big5.js"></script>
<script type="text/javascript" src="unit/test_euc-jp.js"></script>
<script type="text/javascript" src="unit/test_iso-2022-jp.js"></script>
<script type="text/javascript" src="unit/test_shift_jis.js"></script>
<script type="text/javascript" src="unit/test_euc-kr.js"></script>

</body>
</html>
