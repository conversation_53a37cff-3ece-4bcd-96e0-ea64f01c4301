# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: Web Authentication")

DIRS += ["identity"]

EXPORTS.mozilla.dom += [
    "Credential.h",
    "CredentialsContainer.h",
]

UNIFIED_SOURCES += [
    "Credential.cpp",
    "CredentialsContainer.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"

MOCHITEST_MANIFESTS += ["tests/mochitest/mochitest.toml"]
BROWSER_CHROME_MANIFESTS += ["tests/browser/browser.toml"]
CRASHTEST_MANIFESTS += ["tests/crashtests/crashtests.list"]
