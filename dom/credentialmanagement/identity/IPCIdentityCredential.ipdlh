/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

include "mozilla/dom/PermissionMessageUtils.h";
[RefCounted] using class nsIPrincipal from "nsIPrincipal.h";

namespace mozilla {
namespace dom {

// Most of these fields are taken directly from mozilla::dom::IdentityCredentialInit
// Others are:
//    identityProvider: the principal that created the IdentityCredential this represents.
//    infoExpiresAt: PR_Now() + the mozilla::dom::IdentityCredentialInit's infoExpiresAfter, in ms
struct IPCIdentityCredential
{
  nsString id;
  nsString? token;
  nsCString? name;
  nsCString? iconURL;
  nsCString[] effectiveOrigins;
  nsCString? effectiveQueryURL;
  nsCString? effectiveType;
  uint64_t? infoExpiresAt;
  nullable nsIPrincipal identityProvider;
};

}
}
