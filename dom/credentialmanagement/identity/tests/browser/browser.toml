[DEFAULT]
prefs = [
 "dom.security.credentialmanagement.identity.enabled=true",
 "dom.security.credentialmanagement.identity.heavyweight.enabled=true",
 "dom.security.credentialmanagement.identity.ignore_well_known=true",
 "privacy.antitracking.enableWebcompat=false", # disables opener heuristic
]
scheme = "https"
support-files = [
  "server_accounts.json",
  "server_accounts.json^headers^",
  "server_idtoken.json",
  "server_idtoken.json^headers^",
  "server_manifest.json",
  "server_manifest.json^headers^",
  "server_metadata.json",
  "server_metadata.json^headers^",
]

["browser_close_prompt_on_timeout.js"]

["browser_single_concurrent_identity_request.js"]
