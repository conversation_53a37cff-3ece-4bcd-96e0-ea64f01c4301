<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Empty Provider List</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="head.js"></script>
  <link rel="stylesheet" href="/tests/SimpleTest/test.css"/>
  <script>
    SimpleTest.waitForExplicitFinish();
    setupTest("empty_provider_list")
      .then(
      function () {
        return navigator.credentials.get({
          identity: {
            providers: []
          }
        });
      }
    ).then((x) => {
      if (!x) {
        ok(true, "correctly got null");
        return;
      }
      ok(false, "incorrectly got a credential");
    }).catch(() => {
      ok(true, "correctly got an error");
    }).finally(() => {
      SimpleTest.finish();
    })
  </script>
</head>
<body>
<p id="display"></p>
<div id="content" style="display: none">This test verifies that we do not get a credential when we give no providers to support.</div>
<pre id="test"></pre>
</body>
</html>
