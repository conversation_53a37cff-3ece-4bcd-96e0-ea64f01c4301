<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Server Error On Token Endpoint</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="head.js"></script>
  <link rel="stylesheet" href="/tests/SimpleTest/test.css"/>
  <script>
    SimpleTest.waitForExplicitFinish();
    setupTest("idtoken_error").then(
      function () {
        return navigator.credentials.get({
          identity: {
            providers: [{
              configURL: "https://example.net/tests/dom/credentialmanagement/identity/tests/mochitest/server_manifest.sjs",
              clientId: "mochitest",
              nonce: "nonce"
            }]
          }
        });
      }
    ).then(() => {
      ok(false, "incorrectly got a credential");
    }).catch(() => {
      ok(true, "correctly got an error");
    }).finally(() => {
      SimpleTest.finish();
    })
  </script>
</head>
<body>
<p id="display"></p>
<div id="content" style="display: none">This test verifies that we do not get a credential when the idtoken endpoint returns an error.</div>
<pre id="test"></pre>
</body>
</html>
