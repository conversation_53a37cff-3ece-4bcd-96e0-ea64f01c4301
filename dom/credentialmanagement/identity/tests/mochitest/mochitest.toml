[DEFAULT]
prefs = [
  "dom.security.credentialmanagement.identity.enabled=true",
  "dom.security.credentialmanagement.identity.heavyweight.enabled=true",
  "dom.security.credentialmanagement.identity.select_first_in_ui_lists=true",
  "dom.security.credentialmanagement.identity.reject_delay.enabled=false",
  "privacy.antitracking.enableWebcompat=false", # disables opener heuristic
  "network.cookie.cookieBehavior.optInPartitioning=false", # disables blocking third-party cookies
]
scheme = "https"
skip-if = [
  "xorigin",
  "http3", #  Bug 1838420
  "http2",
]

support-files = [
  "head.js",
  "helper_set_cookie.html",
  "helper_set_cookie.html^headers^",
  "/.well-known/web-identity",
  "/.well-known/web-identity^headers^",
  "server_manifest.sjs",
  "server_manifest_wrong_provider_in_manifest.sjs",
  "server_metadata.json",
  "server_metadata.json^headers^",
  "server_simple_accounts.sjs",
  "server_simple_idtoken.sjs",
  "server_no_accounts_accounts.sjs",
  "server_no_accounts_idtoken.sjs",
  "server_two_accounts_accounts.sjs",
  "server_two_accounts_idtoken.sjs",
  "server_two_providers_accounts.sjs",
  "server_two_providers_idtoken.sjs",
  "server_accounts_error_accounts.sjs",
  "server_accounts_error_idtoken.sjs",
  "server_idtoken_error_accounts.sjs",
  "server_idtoken_error_idtoken.sjs",
  "server_accounts_redirect_accounts.sjs",
  "server_accounts_redirect_idtoken.sjs",
  "server_idtoken_redirect_accounts.sjs",
  "server_idtoken_redirect_idtoken.sjs",
]

["test_accounts_error.html"]

["test_accounts_redirect.html"]

["test_delay_reject.html"]

["test_empty_provider_list.html"]

["test_get_without_providers.html"]

["test_idtoken_error.html"]

["test_idtoken_redirect.html"]

["test_mediation.html"]

["test_no_accounts.html"]

["test_simple.html"]

["test_two_accounts.html"]

["test_two_providers.html"]

["test_wrong_provider_in_manifest.html"]
