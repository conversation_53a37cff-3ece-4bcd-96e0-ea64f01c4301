<!DOCTYPE html>
<head>
  <title>Credential Management: Prohibit use in cross-origin iframes</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
  <meta charset=utf-8>
</head>
<body>
<h1>Credential Management: Prohibit use in cross-origin iframes</h1>
<ul>
  <li><a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1407789">Mozilla Bug 1407789</a></li>
</ul>

<div id="framediv">
  <h2>Same Origin Test</h2>
  <iframe id="frame_top"></iframe>

  <h2>Cross-Origin Test</h2>
  <iframe id="frame_bottom"></iframe>
</div>

<script class="testbody" type="text/javascript">
"use strict";

var _countCompletes = 0;
var _expectedCompletes = 2; // 2 iframes

var _done = new Promise((resolve) => {
  function handleEventMessage(event) {
    if ("test" in event.data) {
      let summary = event.data.test + ": " + event.data.msg;
      ok(event.data.status, summary);
    } else if ("done" in event.data) {
      _countCompletes += 1;
      if (_countCompletes == _expectedCompletes) {
        console.log("Test compeleted. Finished.");
        resolve();
      }
    } else {
      ok(false, "Unexpected message in the test harness: " + event.data);
    }
  }

  window.addEventListener("message", handleEventMessage);
});

async function addVirtualAuthenticator() {
  let id = await SpecialPowers.spawnChrome([], () => {
    let webauthnService = Cc["@mozilla.org/webauthn/service;1"].getService(
      Ci.nsIWebAuthnService
    );
    return webauthnService.addVirtualAuthenticator(
      "ctap2",
      "internal",
      true,
      true,
      true,
      true
    );
  });

  SimpleTest.registerCleanupFunction(async () => {
    await SpecialPowers.spawnChrome([id], (authenticatorId) => {
      let webauthnService = Cc["@mozilla.org/webauthn/service;1"].getService(
        Ci.nsIWebAuthnService
      );
      webauthnService.removeVirtualAuthenticator(authenticatorId);
    });
  });
}

add_task(async () => {
  await SpecialPowers.pushPrefEnv({"set": [["security.webauth.webauthn", true],
                                           ["security.webauth.webauthn_enable_softtoken", true],
                                           ["security.webauth.webauthn_enable_usbtoken", false]]});
  await addVirtualAuthenticator();
});

add_task(async () => {
  document.getElementById("frame_top").src = "https://example.com/tests/dom/credentialmanagement/tests/mochitest/frame_credman_iframes.html";

  document.getElementById("frame_bottom").src = "https://test1.example.com/tests/dom/credentialmanagement/tests/mochitest/frame_credman_iframes.html";

  await _done;
});
</script>
</body>
</html>
