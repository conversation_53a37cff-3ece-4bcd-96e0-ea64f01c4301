/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim:set ts=2 sw=2 sts=2 et cindent: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef mozilla_dom_ImageBitmapSource_h
#define mozilla_dom_ImageBitmapSource_h

namespace mozilla::dom {

// So we don't have to forward declare this elsewhere.
class
    HTMLImageElementOrSVGImageElementOrHTMLCanvasElementOrHTMLVideoElementOrOffscreenCanvasOrImageBitmapOrVideoFrameOrBlobOrCanvasRenderingContext2DOrImageData;
typedef HTMLImageElementOrSVGImageElementOrHTMLCanvasElementOrHTMLVideoElementOrOffscreenCanvasOrImageBitmapOrVideoFrameOrBlobOrCanvasRenderingContext2DOrImageData
    ImageBitmapSource;

}  // namespace mozilla::dom

#endif
