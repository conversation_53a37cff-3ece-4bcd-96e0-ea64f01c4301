# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Graphics: Canvas2D")

with Files("TexUnpackBlob.cpp"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("WebGL*"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("test/webgl-conf/**"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("test/webgl-mochitest/**"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("test/reftest/webgl*"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("test/chrome/*webgl*"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

with Files("test/crossorigin/*webgl*"):
    BUG_COMPONENT = ("Core", "Graphics: CanvasWebGL")

# Change the following line(s) to avoid bug 1081323 (clobber after changing a manifest):
# * Adjust failure errata for webgl-conf.

MOCHITEST_MANIFESTS += [
    "test/crash/mochitest.toml",
    "test/crossorigin/mochitest.toml",
    "test/mochitest.toml",
    "test/webgl-conf/generated-mochitest.toml",
    "test/webgl-mochitest/mochitest.toml",
]

MOCHITEST_CHROME_MANIFESTS += ["test/chrome/chrome.toml"]

EXPORTS += [
    "nsICanvasRenderingContextInternal.h",
]

EXPORTS.mozilla += [
    "IsEnumCase.h",
    "ParamTraits_IsEnumCase.h",
    "ParamTraits_STL.h",
    "ParamTraits_TiedFields.h",
    "TiedFields.h",
    "TupleUtils.h",
]

EXPORTS.mozilla.dom += [
    "CanvasGradient.h",
    "CanvasPath.h",
    "CanvasPattern.h",
    "CanvasRenderingContext2D.h",
    "CanvasRenderingContextHelper.h",
    "CanvasUtils.h",
    "DmdStdContainers.h",
    "GeneratePlaceholderCanvasData.h",
    "ImageBitmap.h",
    "ImageBitmapRenderingContext.h",
    "ImageBitmapSource.h",
    "ImageData.h",
    "ImageUtils.h",
    "OffscreenCanvas.h",
    "OffscreenCanvasDisplayHelper.h",
    "OffscreenCanvasRenderingContext2D.h",
    "QueueParamTraits.h",
    "TextMetrics.h",
    "WebGLChild.h",
    "WebGLCommandQueue.h",
    "WebGLIpdl.h",
    "WebGLParent.h",
    "WebGLTexelConversions.h",
    "WebGLTypes.h",
    "XRWebGLLayer.h",
]
# XRWebGLLayer.h must be exported for use by the generated WebXRBinding.h

EXPORTS.mozilla.gfx += [
    "DrawTargetWebgl.h",
]

# Canvas 2D and common sources
#
# Due to bug 1745384, and the associated clang mingw cross compile crash, we
# must be careful about disturbing the unified file combinations.
# nsICanvasRenderingContextInternal.cpp cannot be built standalone until this
# is resolved, so new files may need to be added to SOURCES for the time being.
UNIFIED_SOURCES += [
    "CanvasGradient.cpp",
    "CanvasImageCache.cpp",
    "CanvasPattern.cpp",
    "CanvasRenderingContext2D.cpp",
    "CanvasRenderingContextHelper.cpp",
    "CanvasUtils.cpp",
    "ImageBitmap.cpp",
    "ImageBitmapRenderingContext.cpp",
    "ImageData.cpp",
    "nsICanvasRenderingContextInternal.cpp",
    "OffscreenCanvas.cpp",
    "XRWebGLLayer.cpp",
]

SOURCES += [
    "DrawTargetWebgl.cpp",  # Isolate Skia
    "ImageUtils.cpp",
    "OffscreenCanvasDisplayHelper.cpp",  # See bug 1745384
    "OffscreenCanvasRenderingContext2D.cpp",  # See bug 1745384
    "SourceSurfaceWebgl.cpp",
]

# WebGL Sources
UNIFIED_SOURCES += [
    "CacheInvalidator.cpp",
    "ClientWebGLContext.cpp",
    "ClientWebGLExtensions.cpp",
    "HostWebGLContext.cpp",
    "SanitizeRenderer.cpp",
    "TexUnpackBlob.cpp",
    "WebGL2Context.cpp",
    "WebGL2ContextBuffers.cpp",
    "WebGL2ContextFramebuffers.cpp",
    "WebGL2ContextMRTs.cpp",
    "WebGL2ContextQueries.cpp",
    "WebGL2ContextRenderbuffers.cpp",
    "WebGL2ContextSamplers.cpp",
    "WebGL2ContextState.cpp",
    "WebGL2ContextSync.cpp",
    "WebGL2ContextTransformFeedback.cpp",
    "WebGL2ContextUniforms.cpp",
    "WebGLBuffer.cpp",
    "WebGLChild.cpp",
    "WebGLContext.cpp",
    "WebGLContextBuffers.cpp",
    "WebGLContextDraw.cpp",
    "WebGLContextExtensions.cpp",
    "WebGLContextFramebufferOperations.cpp",
    "WebGLContextGL.cpp",
    "WebGLContextLossHandler.cpp",
    "WebGLContextState.cpp",
    "WebGLContextTextures.cpp",
    "WebGLContextUtils.cpp",
    "WebGLContextValidate.cpp",
    "WebGLContextVertexArray.cpp",
    "WebGLContextVertices.cpp",
    "WebGLExtensions.cpp",
    "WebGLFormats.cpp",
    "WebGLFramebuffer.cpp",
    "WebGLMemoryTracker.cpp",
    "WebGLParent.cpp",
    "WebGLProgram.cpp",
    "WebGLQuery.cpp",
    "WebGLRenderbuffer.cpp",
    "WebGLSampler.cpp",
    "WebGLShader.cpp",
    "WebGLShaderValidator.cpp",
    "WebGLSync.cpp",
    "WebGLTexelConversions.cpp",
    "WebGLTexture.cpp",
    "WebGLTextureUpload.cpp",
    "WebGLTransformFeedback.cpp",
    "WebGLValidateStrings.cpp",
    "WebGLVertexArray.cpp",
    "WebGLVertexArrayFake.cpp",
    "WebGLVertexArrayGL.cpp",
]

SOURCES += [
    "MurmurHash3.cpp",
]

IPDL_SOURCES += [
    "PWebGL.ipdl",
]

TEST_DIRS += [
    "gtest",
]

# Suppress warnings from third-party code.
SOURCES["MurmurHash3.cpp"].flags += ["-Wno-implicit-fallthrough"]

LOCAL_INCLUDES += [
    "/js/xpconnect/wrappers",
]

include("/ipc/chromium/chromium-config.mozbuild")


USE_LIBS += ["translator"]  # Grab the Angle shader translator.

FINAL_LIBRARY = "xul"
LOCAL_INCLUDES += [
    "/dom/base",
    "/dom/html",
    "/dom/svg",
    "/dom/workers",
    "/dom/xul",
    "/gfx/angle/checkout/include",
    "/gfx/cairo/cairo/src",
    "/gfx/gl",
    "/image",
    "/js/xpconnect/src",
    "/layout/generic",
    "/layout/style",
    "/layout/xul",
    "/media/libyuv/libyuv/include",
]

if CONFIG["MOZ_WIDGET_TOOLKIT"] == "gtk":
    CXXFLAGS += CONFIG["MOZ_GTK3_CFLAGS"]

LOCAL_INCLUDES += CONFIG["SKIA_INCLUDES"]

if CONFIG["CC_TYPE"] in ("clang", "clang-cl"):
    CXXFLAGS += ["-Werror=implicit-int-conversion", "-Wno-shorten-64-to-32"]
    CXXFLAGS += ["-Werror=switch"]

if CONFIG["CC_TYPE"] == "gcc":
    CXXFLAGS += ["-Wno-error=unused-result"]  # GCC doesn't ignore (void)MustUse();

# Add libFuzzer configuration directives
include("/tools/fuzzing/libfuzzer-config.mozbuild")

# DOM Mask
LOCAL_INCLUDES += ["/camoucfg"]