/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*-
 * vim: sw=2 ts=8 et :
 */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

include protocol PCanvasManager;
include "mozilla/layers/LayersMessageUtils.h";

[MoveOnly] using class mozilla::ipc::BigBuffer from "mozilla/ipc/BigBuffer.h";
using mozilla::layers::RemoteTextureOwnerId from "mozilla/layers/LayersTypes.h";
using mozilla::layers::RemoteTextureTxnType from "mozilla/layers/LayersTypes.h";
using mozilla::layers::RemoteTextureTxnId from "mozilla/layers/LayersTypes.h";
using mozilla::layers::SurfaceDescriptor from "mozilla/layers/LayersTypes.h";
using std::string from "string";
using mozilla::uvec2 from "mozilla/dom/WebGLIpdl.h";
using mozilla::uvec3 from "mozilla/dom/WebGLIpdl.h";
using mozilla::void_t from "mozilla/ipc/IPCCore.h";
using mozilla::webgl::CompileResult from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::ContextLossReason from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::FrontBufferSnapshotIpc from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::GetUniformData from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::InitContextDesc from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::InitContextResult from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::Int32Vector from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::LinkResult from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::OpaqueFramebufferOptions from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::PackingInfo from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::ReadPixelsDesc from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::ReadPixelsResultIpc from "mozilla/dom/WebGLIpdl.h";
using mozilla::webgl::ShaderPrecisionFormat from "mozilla/dom/WebGLIpdl.h";
[MoveOnly] using mozilla::webgl::TexUnpackBlobDesc from "mozilla/dom/WebGLIpdl.h";

namespace mozilla {
namespace dom {

union ReadPixelsBuffer {
  // The buffer needs to be allocated, and the value specifies the maximum size.
  uint64_t;
  // The buffer is a pre-allocated Shmem.
  Shmem;
};

/**
 * Represents the connection between a WebGLChild actor that issues WebGL
 * command from the content process, and a WebGLParent in the compositor
 * process that runs the commands.
 */
sync protocol PWebGL
{
  manager PCanvasManager;

parent:
  sync Initialize(InitContextDesc desc)
    returns (InitContextResult res);

  async __delete__();

  async WaitForTxn(RemoteTextureOwnerId ownerId, RemoteTextureTxnType txnType, RemoteTextureTxnId txnId);

  // -

  async DispatchCommands(BigBuffer commands, uint64_t size);
  async Ping() returns (void_t ok);
  sync SyncPing();
  async TexImage(uint32_t level, uint32_t respecFormat, uvec3 offset,
                 PackingInfo pi, TexUnpackBlobDesc src);

  // -

  sync GetBufferSubData(uint32_t target, uint64_t srcByteOffset, uint64_t byteSize) returns (Shmem ret);
  sync GetFrontBufferSnapshot() returns (FrontBufferSnapshotIpc ret);
  sync ReadPixels(ReadPixelsDesc desc, ReadPixelsBuffer buffer) returns (ReadPixelsResultIpc ret);

  // -

  sync CheckFramebufferStatus(uint32_t target) returns (uint32_t ret);
  sync ClientWaitSync(uint64_t id, uint32_t flags, uint64_t timeout) returns (uint32_t ret);
  sync CreateOpaqueFramebuffer(uint64_t id, OpaqueFramebufferOptions options) returns (bool ret);
  sync DrawingBufferSize() returns (uvec2 ret);
  sync Finish();
  sync GetBufferParameter(uint32_t target, uint32_t pname) returns (double? ret);
  sync GetCompileResult(uint64_t id) returns (CompileResult ret);
  sync GetError() returns (uint32_t ret);
  sync GetFragDataLocation(uint64_t id, string name) returns (int32_t ret);
  sync GetFramebufferAttachmentParameter(uint64_t id,
                                                  uint32_t attachment,
                                                  uint32_t pname) returns (double? ret);
  sync GetFrontBuffer(uint64_t fb, bool vr) returns (SurfaceDescriptor? ret);
  sync GetIndexedParameter(uint32_t target, uint32_t index) returns (double? ret);
  sync GetInternalformatParameter(uint32_t target, uint32_t internalFormat, uint32_t pname) returns (Int32Vector? ret);
  sync GetLinkResult(uint64_t id) returns (LinkResult ret);
  sync GetNumber(uint32_t pname) returns (double? ret);
  sync GetQueryParameter(uint64_t id, uint32_t pname) returns (double? ret);
  sync GetRenderbufferParameter(uint64_t id, uint32_t pname) returns (double? ret);
  sync GetSamplerParameter(uint64_t id, uint32_t pname) returns (double? ret);
  sync GetShaderPrecisionFormat(
      uint32_t shaderType, uint32_t precisionType) returns (ShaderPrecisionFormat? ret);
  sync GetString(uint32_t pname) returns (string? ret);
  sync GetTexParameter(uint64_t id, uint32_t pname) returns (double? ret);
  sync GetUniform(uint64_t id, uint32_t loc) returns (GetUniformData ret);
  sync GetVertexAttrib(uint32_t index, uint32_t pname) returns (double? ret);
  sync OnMemoryPressure();
  sync ValidateProgram(uint64_t id) returns (bool ret);

child:
  async JsWarning(string text);

  // Tell client that this queue needs to be shut down
  async OnContextLoss(ContextLossReason aReason);

  // Triggered when the id from FenceSync completes.
  async OnSyncComplete(uint64_t id);
};

} // dom
} // mozilla
