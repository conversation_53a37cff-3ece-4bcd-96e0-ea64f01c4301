<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Test for drawSnapshot</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/WindowSnapshot.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <style>
    html {
      height: 2000px;
      overflow: hidden;
    }
    header {
      position: fixed;
      background: red;
      display: block;
      left: 0;
      right: 0;
      top: 0;
      height: 200px;
    }
  </style>
  <script type="application/javascript">
  function make_canvas() {
    var canvas = document.createElement("canvas");
    canvas.setAttribute("height", 2000);
    canvas.setAttribute("width", 500);
    return canvas;
  }

  SimpleTest.waitForExplicitFinish();
  window.addEventListener("load", runTests);
  
  window.scrollTo(0, 1000);

  async function runTests(event) {
    var testCanvas = make_canvas();
    var testCx = testCanvas.getContext("2d");
    var testWrapCx = SpecialPowers.wrap(testCx);

    // Take a snapshot of the page while scrolled down, so that the fixed header will
    // be visually in the middle of the root scroll frame, but should still be at the
    // top of the snapshot (since snapshots with a rect are taken relative to the document).
    var rect = new window.DOMRect(0, 0, 500, 2000);
    let image = await SpecialPowers.snapshotContext(window, rect, "rgb(255, 255, 255)", true);
    testWrapCx.drawImage(image, 0, 0);

    var refCanvas = make_canvas();
    var refCx = refCanvas.getContext("2d");

    // Construct a reference image with an equivalent red square at the top.
    refCx.fillStyle = "white";
    refCx.fillRect(0, 0, 500, 2000);
    refCx.fillStyle = "red";
    refCx.fillRect(0, 0, 500, 200);

    assertSnapshots(testCanvas, refCanvas, true, null, "position:fixed element with scrolling", "reference");

    SimpleTest.finish();
  }

  </script>
</head>
<body>
  <header></header>
</body>
</html>
