<!DOCTYPE HTML>
<title>Canvas test: 2d.line.cap.closed</title>
<!-- Testing: Line caps are not drawn at the corners of an unclosed rectangle -->
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="50"><p class="fallback">FAIL (fallback content)</p></canvas>
<script>
function isPixel(ctx, x,y, r,g,b,a, pos, colour, d) {
    var pixel = ctx.getImageData(x, y, 1, 1);
    var pr = pixel.data[0],
        pg = pixel.data[1],
        pb = pixel.data[2],
        pa = pixel.data[3];
    ok(r-d <= pr && pr <= r+d &&
       g-d <= pg && pg <= g+d &&
       b-d <= pb && pb <= b+d &&
       a-d <= pa && pa <= a+d,
       "pixel "+pos+" is "+pr+","+pg+","+pb+","+pa+"; expected "+colour+" +/- "+d);
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');

ctx.fillStyle = '#0f0';
ctx.strokeStyle = '#f00';
ctx.fillRect(0, 0, 100, 50);

ctx.lineJoin = 'bevel';
ctx.lineCap = 'square';
ctx.lineWidth = 400;

ctx.beginPath();
ctx.moveTo(200, 200);
ctx.lineTo(200, 1000);
ctx.lineTo(1000, 1000);
ctx.lineTo(1000, 200);
ctx.closePath();
ctx.stroke();

isPixel(ctx, 1,1, 0,255,0,255, "1,1", "0,255,0,255", 0);
isPixel(ctx, 48,1, 0,255,0,255, "48,1", "0,255,0,255", 0);
isPixel(ctx, 48,48, 0,255,0,255, "48,48", "0,255,0,255", 0);
isPixel(ctx, 1,48, 0,255,0,255, "1,48", "0,255,0,255", 0);

SimpleTest.finish();

});
</script>

