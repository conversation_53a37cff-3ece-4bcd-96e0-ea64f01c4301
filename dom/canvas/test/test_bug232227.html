<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=232227
-->
<head>
  <title>Test for Bug 232227</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=232227">Mozilla Bug 232227</a>
<script>

/** Test for Bug 232227 **/

function runTest(testCanvasColors) {
  var colorNames = [
    [ "AppWorkspace", 0xFF, 0xFF, 0xFF ], // deprecated, same as Canvas/Window (bug 1803930)
    [ "Background", 0xFF, 0xFF, 0xFF ], // deprecated, same as Canvas/Window (bug 1803930)
    [ "ButtonFace", 0xE9, 0xE9, 0xED ],
    [ "ButtonHighlight", 0xE9, 0xE9, 0xED ], // deprecated, same as ButtonFace (bug 1803932)
    [ "ButtonShadow", 0xE9, 0xE9, 0xED ], // deprecated, same as ButtonFace (bug 1803932)
    [ "ButtonText", 0x00, 0x00, 0x00 ],
    [ "GrayText", 0x6D, 0x6D, 0x6D ],
    [ "Highlight", 0x33, 0x99, 0xFF ],
    [ "HighlightText", 0xFF, 0xFF, 0xFF ],
    [ "InfoBackground", 0xFF, 0xFF, 0xFF ], // deprecated, same as Canvas/Window (bug 1874065)
    [ "InfoText", 0x00, 0x00, 0x00 ], // deprecated, same as CanvasText/WindowText (bug 1803931)
    [ "Menu", 0xFF, 0xFF, 0xFF ], // deprecated, same as Canvas/Window (bug 1803930)
    [ "MenuText", 0x00, 0x00, 0x00 ], // deprecated, same as CanvasText/WindowText (bug 1873951)
    [ "Scrollbar", 0xFF, 0xFF, 0xFF ], // deprecated, same as Canvas/Window (bug 1803930)
    [ "ThreeDDarkShadow", 0xE3, 0xE3, 0xE3 ], // deprecated, same as ButtonBorder (bug 1803933)
    [ "ThreeDFace", 0xE9, 0xE9, 0xED ], // deprecated, same as ButtonFace (bug 1874064)
    [ "ThreeDHighlight", 0xE3, 0xE3, 0xE3 ], // deprecated, same as ButtonBorder (bug 1803933)
    [ "ThreeDLightShadow", 0xE3, 0xE3, 0xE3 ],
    [ "ThreeDShadow", 0xE3, 0xE3, 0xE3 ], // deprecated, same as ButtonBorder (bug 1803933)
    [ "Window", 0xFF, 0xFF, 0xFF ],
    [ "WindowFrame", 0xE3, 0xE3, 0xE3 ], // deprecated, same as ButtonBorder (bug 1803933)
    [ "WindowText", 0x00, 0x00, 0x00 ],
    [ "-moz-ButtonHoverFace", 0xD0, 0xD0, 0xD7 ],
    [ "-moz-ButtonHoverText", 0x00, 0x00, 0x00 ],
    [ "-moz-CellHighlight", 0xF0, 0xF0, 0xF0 ],
    [ "-moz-CellHighlightText", 0x00, 0x00, 0x00 ],
    [ "-moz-Combobox", 0xE9, 0xE9, 0xED ],
    [ "-moz-ComboboxText", 0x00, 0x00, 0x00 ],
    [ "-moz-Dialog", 0xF0, 0xF0, 0xF0 ],
    [ "-moz-DialogText", 0x00, 0x00, 0x00 ],
    [ "-moz-EvenTreeRow", 0xFF, 0xFF, 0xFF ],
    [ "-moz-Field", 0xFF, 0xFF, 0xFF ],
    [ "-moz-FieldText", 0x00, 0x00, 0x00 ],
    [ "-moz-MenuHover", 0x33, 0x99, 0xFF ],
    [ "-moz-MenuHoverText", 0x00, 0x00, 0x00 ],
    [ "-moz-MenubarHoverText", 0x00, 0x00, 0x00 ],
    [ "-moz-OddTreeRow", 0xFF, 0xFF, 0xFF ],
    [ "SelectedItem", 0x33, 0x99, 0xFF ],
    [ "SelectedItemText", 0xFF, 0xFF, 0xFF ],

    // These five are configured via Tools -> Options -> Content -> Colors.
    //"-moz-ActiveHyperlinkText",
    //"-moz-HyperLinkText",
    //"-moz-VisitedHyperlinkText",
    //"-moz-default-background-color",
    //"-moz-default-color",
  ];

  var colorTestCanvas = document.createElement("canvas");
  colorTestCanvas.width = colorTestCanvas.height = 1;
  colorTestCanvas = colorTestCanvas.getContext("2d");

  var colorTestDiv = document.createElement("div");
  document.body.appendChild(colorTestDiv);

  for (let [colorName, r, g, b] of colorNames) {
    // test value
    var ctest = "rgb(" + r + ", " + g + ", " + b + ")";

    // computed value
    colorTestDiv.style.backgroundColor = "";
    colorTestDiv.style.backgroundColor = colorName;
    var c1 = getComputedStyle(colorTestDiv).backgroundColor;
    is(c1, ctest, "Stand-in computed color: " + colorName + " is correct.");

    // canvas
    if (testCanvasColors && colorTestCanvas) {
      colorTestCanvas.fillStyle = colorName;
      colorTestCanvas.fillRect(0, 0, 1, 1);
      var c2 = colorTestCanvas.getImageData(0, 0, 1, 1).data;
      c2 = "rgb(" + c2[0] + ", " + c2[1] + ", " + c2[2] + ")";
      is(c2, ctest, "Stand-in canvas color: " + colorName + " is correct.");
    }
  }
}

(async function() {
  SimpleTest.waitForExplicitFinish();
  await SpecialPowers.pushPrefEnv({ "set" : [
    [ "ui.use_standins_for_native_colors", true ],
  ]});
  runTest(/* testCanvasColors = */ true);
  await SpecialPowers.pushPrefEnv({ "set" : [
    [ "privacy.resistFingerprinting", true ],
    [ "ui.use_standins_for_native_colors", false ],
  ]});
  // With privacy.resistFingerprinting canvas data doesn't reflect the actual
  // canvas, apparently.
  runTest(/* testCanvasColors = */ false);
  SimpleTest.finish();
}());
</script>
</body>
</html>
