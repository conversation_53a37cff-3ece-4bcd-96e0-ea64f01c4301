<!DOCTYPE HTML>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1567544

Test that globalCompositeOperation is applied to SVG images drawn with `drawImage`.
-->
<title>Test for Bug 1567544</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="50"></canvas>
<script>
function isPixel(ctx, x,y, r,g,b,a, pos, colour, d) {
    var pixel = ctx.getImageData(x, y, 1, 1);
    var pr = pixel.data[0],
        pg = pixel.data[1],
        pb = pixel.data[2],
        pa = pixel.data[3];
    ok(r-d <= pr && pr <= r+d &&
       g-d <= pg && pg <= g+d &&
       b-d <= pb && pb <= b+d &&
       a-d <= pa && pa <= a+d,
       "pixel "+pos+" is "+pr+","+pg+","+pb+","+pa+"; expected "+colour+" +/- "+d);
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');

ctx.beginPath();
ctx.arc(50, 50, 37.5, 0, Math.PI * 2);
ctx.fillStyle = 'red';
ctx.fill();
ctx.globalCompositeOperation = 'destination-in';
ctx.drawImage(document.getElementById('roundrectangle.svg'), 50, 40);

isPixel(ctx, 82,38, 0,0,0,0, "82,38", "0,0,0,0", 0);
isPixel(ctx, 82,48, 255,0,0,255, "82,48", "255,0,0,255", 0);

SimpleTest.finish();

});
</script>
<img src="image_roundrectangle.svg" id="roundrectangle.svg" class="resource">
