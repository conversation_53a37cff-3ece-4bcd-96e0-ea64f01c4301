<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=866575
-->
<head>
  <title>Test for Bug 866575</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=866575">Mozilla Bug 866575</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
<script>
/** Test for Bug 866575 **/

var canvas1 = document.createElementNS('http://www.w3.org/1999/xhtml', 'canvas');
canvas1.width = "306368";
var canvas2 = document.createElementNS('http://www.w3.org/1999/xhtml', 'canvas');
try {
  canvas2.getContext('2d').createPattern(canvas1, 'repeat-x');
} catch (e) {}
finally {
  ok(true, "Didn't crash");
}

</script>
</pre>
</body>
</html>
