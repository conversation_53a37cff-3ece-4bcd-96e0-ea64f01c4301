<!DOCTYPE HTML>
<title>Canvas test: 2d.composite.canvas.xor</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="50"><p class="fallback">FAIL (fallback content)</p></canvas>
<script>
function isPixel(ctx, x,y, r,g,b,a, pos, colour, d) {
    var pixel = ctx.getImageData(x, y, 1, 1);
    var pr = pixel.data[0],
        pg = pixel.data[1],
        pb = pixel.data[2],
        pa = pixel.data[3];
    ok(r-d <= pr && pr <= r+d &&
       g-d <= pg && pg <= g+d &&
       b-d <= pb && pb <= b+d &&
       a-d <= pa && pa <= a+d,
       "pixel "+pos+" is "+pr+","+pg+","+pb+","+pa+"; expected "+colour+" +/- "+d);
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');
                                var text                = "I";
                                var colorTop    = 'rgba(255,0,0,1)';
                                var colorBottom = 'rgba(0,255,0,1)';
                                var inset               = 10;
                                var grad                = ctx.createLinearGradient(inset, inset, inset, inset + 30);
                                
                                // add color stops
                                grad.addColorStop(0, colorTop);
                                grad.addColorStop(1, colorBottom);
                                
                                ctx.fillStyle               = grad;
                                ctx.textBaseline    = 'top';
                                ctx.font                    = '110px sans-serif'
                                ctx.fillText(text, -10, -20);
                                ctx.fillText(text, -8, -20);
                                ctx.fillText(text, -6, -20);
                                ctx.fillText(text, -4, -20);
                                ctx.fillText(text, -2, -20);
                                ctx.fillText(text, 0, -20);
                                ctx.fillText(text, 2, -20);
                                ctx.fillText(text, 4, -20);
                                ctx.fillText(text, 6, -20);

                                isPixel(ctx, 15,0, 255,0,0,255, "15,0", "255,0,0,255", 5);
                                isPixel(ctx, 15,49, 0,255,0,255, "15,49", "0,255,0,255", 5);
SimpleTest.finish();

});
</script>

