<!DOCTYPE HTML>
<title>Canvas test: 2d.gradient.radial.cone.beside</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="50"><p class="fallback">FAIL (fallback content)</p></canvas>
<script>
function isPixel(ctx, x,y, r,g,b,a, pos, colour, d) {
    var pixel = ctx.getImageData(x, y, 1, 1);
    var pr = pixel.data[0],
        pg = pixel.data[1],
        pb = pixel.data[2],
        pa = pixel.data[3];
    ok(r-d <= pr && pr <= r+d &&
       g-d <= pg && pg <= g+d &&
       b-d <= pb && pb <= b+d &&
       a-d <= pa && pa <= a+d,
       "pixel "+pos+" is "+pr+","+pg+","+pb+","+pa+"; expected "+colour+" +/- "+d);
}
function todo_isPixel(ctx, x,y, r,g,b,a, pos, colour, d) {
    var pixel = ctx.getImageData(x, y, 1, 1);
    var pr = pixel.data[0],
        pg = pixel.data[1],
        pb = pixel.data[2],
        pa = pixel.data[3];
    todo(r-d <= pr && pr <= r+d &&
       g-d <= pg && pg <= g+d &&
       b-d <= pb && pb <= b+d &&
       a-d <= pa && pa <= a+d,
       "pixel "+pos+" is "+pr+","+pg+","+pb+","+pa+"; expected "+colour+" +/- "+d);
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');

ctx.fillStyle = '#0f0';
ctx.fillRect(0, 0, 100, 50);

var g = ctx.createRadialGradient(0, 100, 40, 100, 100, 50);
g.addColorStop(0, '#f00');
g.addColorStop(1, '#f00');
ctx.fillStyle = g;
ctx.fillRect(0, 0, 100, 50);

isPixel(ctx, 1,1, 0,255,0,255, "1,1", "0,255,0,255", 0);
isPixel(ctx, 50,1, 0,255,0,255, "50,1", "0,255,0,255", 0);
isPixel(ctx, 98,1, 0,255,0,255, "98,1", "0,255,0,255", 0);
isPixel(ctx, 1,25, 0,255,0,255, "1,25", "0,255,0,255", 0);
isPixel(ctx, 50,25, 0,255,0,255, "50,25", "0,255,0,255", 0);
isPixel(ctx, 98,25, 0,255,0,255, "98,25", "0,255,0,255", 0);
isPixel(ctx, 1,48, 0,255,0,255, "1,48", "0,255,0,255", 0);
isPixel(ctx, 50,48, 0,255,0,255, "50,48", "0,255,0,255", 0);
isPixel(ctx, 98,48, 0,255,0,255, "98,48", "0,255,0,255", 0);

SimpleTest.finish();

});
</script>

