<!DOCTYPE HTML>
<title>Canvas test: canvas demotion</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="50"><p class="fallback">FAIL (fallback content)</p></canvas>
<script>

SimpleTest.waitForExplicitFinish();
SimpleTest.requestFlakyTimeout("untriaged");
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');

ctx.fillStyle = 'rgb(50, 50, 50)';
ctx.fillRect(0, 0, 100, 50);
ctx.translate(25, 25);

SpecialPowers.wrap(ctx).demote();

setTimeout(function() {
  ctx.fillStyle = 'rgb(127, 127, 127)';
  ctx.fillRect(0, 0, 10, 10);

  var pixels = ctx.getImageData(0, 0, 1, 1);

  ok(pixels.data[0] === 50, "pixels.data[0] expected 50, got " + pixels.data[0]);
  ok(pixels.data[1] === 50, "pixels.data[1] expected 50, got " + pixels.data[1]);
  ok(pixels.data[2] === 50, "pixels.data[2] expected 50, got " + pixels.data[2]);

  pixels = ctx.getImageData(25, 25, 1, 1);

  ok(pixels.data[0] === 127, "pixels.data[0] expected 127, got " + pixels.data[0]);
  ok(pixels.data[1] === 127, "pixels.data[1] expected 127, got " + pixels.data[1]);
  ok(pixels.data[2] === 127, "pixels.data[2] expected 127, got " + pixels.data[2]);

  SimpleTest.finish();
}, 50);


});
</script>

