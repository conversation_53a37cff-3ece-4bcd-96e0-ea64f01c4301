<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=613794
-->
<head>
  <title>Test for Bug 613794</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=613794">Mozilla Bug 613794</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

/** Test for Bug 613794 **/

var c = document.createElement("canvas");
c.width = c.height = 1;

c = c.getContext("2d");

var threw = true;
try {
  c.putImageData({ width: 1, height: 1, data: [ 0, 0, 0, 0] }, 0, 0);
  threw = false;
} catch(e) {
  threw = true;
}

ok(threw,
   "Should not be able to pass in custom imagedata objects with array data");

</script>
</pre>
</body>
</html>
