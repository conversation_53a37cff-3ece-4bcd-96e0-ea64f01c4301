<!DOCTYPE HTML>
<title>Canvas test: 2d.isPointInPath.winding</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<body>
<canvas id="c" width="100" height="100"><p class="fallback">FAIL (fallback content)</p></canvas>
<script>
SimpleTest.waitForExplicitFinish();
addLoadEvent(function () {

var canvas = document.getElementById('c');
var ctx = canvas.getContext('2d');

ctx.beginPath();
ctx.rect(0, 0, 100, 100);
ctx.rect(25, 25, 50, 50);
ok(ctx.isPointInPath(50, 50));

ctx.beginPath();
ctx.rect(0, 0, 100, 100);
ctx.rect(25, 25, 50, 50);
ok(ctx.isPointInPath(50, 50, 'evenodd') == false);

SimpleTest.finish();

});
</script>

