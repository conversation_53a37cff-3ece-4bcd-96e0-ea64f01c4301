<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=856472
-->
<head>
  <title>Test for Bug 856472</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=856472">Mozilla Bug 856472</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
<script>
/** Test for Bug 856472 **/

var c = document.createElement("canvas");

var ctx = c.getContext("2d");
var pattern = ctx.createPattern(c, null);
pattern.x = 1;
ctx.fillStyle = pattern;
is(ctx.fillStyle.x, 1, "CanvasPattern stored as fillstyle drops expandos");
</script>
</pre>
</body>
</html>
