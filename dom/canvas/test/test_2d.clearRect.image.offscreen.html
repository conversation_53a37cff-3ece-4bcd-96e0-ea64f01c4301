<!DOCTYPE HTML>
<html>
<head>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body onload="doTest()">
<p id="display">
  <canvas id="c" width="1" height="1"></canvas>
  <img id="img" src="image_green-1x1.png">
</p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

var c = document.getElementById("c");
var ctx = c.getContext("2d");
var img = document.getElementById("img");

SimpleTest.waitForExplicitFinish();

function doTest() {
  ctx.fillStyle = "red";
  ctx.fillRect(0, 0, 1, 1);
  ctx.drawImage(img, -1, 0);
  ctx.clearRect(0, 0, 1, 1);

  var data = ctx.getImageData(0, 0, 1, 1).data;
  is(data[0], 0, "Red channel should be 0");
  is(data[1], 0, "Green channel should be 0");
  is(data[2], 0, "Blue channel should be 0")
  is(data[3], 0, "Alpha channel should be 0");

  SimpleTest.finish();
}

</script>
</pre>
</body>
</html>
