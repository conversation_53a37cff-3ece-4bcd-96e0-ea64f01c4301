[DEFAULT]
support-files = [
  "file_drawImage_document_domain.html",
  "image_anim-gr.gif",
  "image_anim-gr.png",
  "image_anim-poster-gr.png",
  "image_broken.png",
  "image_error-early.png",
  "image_ggrr-256x256.png",
  "image_green-16x16.png",
  "image_green-1x1.png",
  "image_green-redirect",
  "image_green-redirect^headers^",
  "image_green.png",
  "image_red-16x16.png",
  "image_red.png",
  "image_red_crossorigin_credentials.png",
  "image_red_crossorigin_credentials.png^headers^",
  "image_redtransparent.png",
  "image_rgrg-256x256.png",
  "image_rrgg-256x256.png",
  "image_transparent.png",
  "image_transparent50.png",
  "image_yellow.png",
  "image_yellow75.png",
  "image_roundrectangle.svg",
  "imagebitmap_bug1239300.js",
  "imagebitmap_bug1239752.js",
  "imagebitmap_on_worker.js",
  "imagebitmap_structuredclone.js",
  "imagebitmap_structuredclone_iframe.html",
  "imagebitmap_structuredclone_utils.js",
  "offscreencanvas.js",
  "offscreencanvas_mask.svg",
  "offscreencanvas_neuter.js",
  "offscreencanvas_serviceworker_inner.html",
  "crossorigin/image.png",
  "crossorigin/video.sjs",
  "../../media/test/320x240.webm",
]
# The video used in those tests crash the Android emulator's VP9 decoder.
prefs = [
  "media.android-media-codec.enabled=false",
]

["test_2d.clearRect.image.offscreen.html"]

["test_2d.clip.winding.html"]

["test_2d.composite.canvas.color-burn.html"]

["test_2d.composite.canvas.color-dodge.html"]

["test_2d.composite.canvas.color.html"]

["test_2d.composite.canvas.darken.html"]

["test_2d.composite.canvas.difference.html"]

["test_2d.composite.canvas.exclusion.html"]

["test_2d.composite.canvas.hard-light.html"]

["test_2d.composite.canvas.hue.html"]

["test_2d.composite.canvas.lighten.html"]

["test_2d.composite.canvas.luminosity.html"]

["test_2d.composite.canvas.multiply.html"]

["test_2d.composite.canvas.overlay.html"]

["test_2d.composite.canvas.saturation.html"]

["test_2d.composite.canvas.screen.html"]

["test_2d.composite.canvas.soft-light.html"]

["test_2d.composite.solid.color-burn.html"]

["test_2d.composite.solid.color-dodge.html"]

["test_2d.composite.solid.color.html"]

["test_2d.composite.solid.darken.html"]

["test_2d.composite.solid.difference.html"]

["test_2d.composite.solid.exclusion.html"]

["test_2d.composite.solid.hard-light.html"]

["test_2d.composite.solid.hue.html"]

["test_2d.composite.solid.lighten.html"]

["test_2d.composite.solid.luminosity.html"]

["test_2d.composite.solid.multiply.html"]

["test_2d.composite.solid.overlay.html"]

["test_2d.composite.solid.saturation.html"]

["test_2d.composite.solid.screen.html"]

["test_2d.composite.solid.soft-light.html"]

["test_2d.composite.uncovered.fill.color-burn.html"]

["test_2d.composite.uncovered.fill.color-dodge.html"]

["test_2d.composite.uncovered.fill.color.html"]

["test_2d.composite.uncovered.fill.darken.html"]

["test_2d.composite.uncovered.fill.difference.html"]

["test_2d.composite.uncovered.fill.exclusion.html"]

["test_2d.composite.uncovered.fill.hard-light.html"]

["test_2d.composite.uncovered.fill.hue.html"]

["test_2d.composite.uncovered.fill.lighten.html"]

["test_2d.composite.uncovered.fill.luminosity.html"]

["test_2d.composite.uncovered.fill.multiply.html"]

["test_2d.composite.uncovered.fill.overlay.html"]

["test_2d.composite.uncovered.fill.saturation.html"]

["test_2d.composite.uncovered.fill.screen.html"]

["test_2d.composite.uncovered.fill.soft-light.html"]

["test_2d.drawImage.zerocanvas.html"]

["test_2d.fill.pattern.imageSmoothingEnabled.html"]

["test_2d.fill.winding.html"]

["test_2d.fillText.gradient.html"]

["test_2d.gradient.radial.cone.behind.html"]

["test_2d.gradient.radial.cone.beside.html"]

["test_2d.gradient.radial.cone.front.html"]

["test_2d.gradient.radial.cone.shape2.html"]

["test_2d.gradient.radial.cone.top.html"]

["test_2d.gradient.radial.equal.html"]

["test_2d.gradient.radial.inside2.html"]

["test_2d.gradient.radial.inside3.html"]

["test_2d.gradient.radial.outside1.html"]

["test_2d.gradient.radial.outside2.html"]

["test_2d.gradient.radial.outside3.html"]

["test_2d.gradient.radial.touch1.html"]

["test_2d.gradient.radial.touch2.html"]

["test_2d.gradient.radial.touch3.html"]

["test_2d.isPointInPath.winding.html"]

["test_2d.line.cap.closed.html"]

["test_2d.line.join.parallel.html"]

["test_2d.path.arc.shape.3.html"]

["test_2d.path.rect.selfintersect.html"]

["test_2d.strokeRect.zero.5.html"]

["test_2d_composite_canvaspattern_setTransform.html"]

["test_ImageData_ctor.html"]

["test_accelerated_canvas_context_loss.html"]
subsuite = "gpu"
skip-if = ["verify || (os == 'win' && debug)"]

["test_bitmaprenderer.html"]

["test_bug232227.html"]

["test_bug613794.html"]

["test_bug764125.html"]

["test_bug856472.html"]

["test_bug866575.html"]

["test_bug902651.html"]

["test_bug1215072.html"]

["test_bug1567544.html"]
subsuite = "gpu"

["test_canvas.html"]
subsuite = "gpu"

["test_canvas_focusring.html"]

["test_canvas_font_setter.html"]

["test_canvas_path.html"]

["test_canvas_strokeStyle_getter.html"]

["test_capture.html"]
support-files = ["captureStream_common.js"]

["test_capture_throttled.html"]
support-files = ["captureStream_common.js"]
skip-if = [
  "os == 'android'", # Bug 1752351
  "os == 'win'", # Bug 1777094
]

["test_drawImageIncomplete.html"]

["test_drawImage_document_domain.html"]

["test_drawImage_edge_cases.html"]

["test_drawSnapshot.html"]
support-files = [
  "file_drawWindow_source.html",
  "file_drawWindow_common.js",
]

["test_drawSnapshot_fixed.html"]

["test_drawWindow.html"]
support-files = [
  "file_drawWindow_source.html",
  "file_drawWindow_common.js",
]

["test_filter.html"]

["test_filter_tainted.html"]

["test_imagebitmap.html"]
tags = "imagebitmap"

["test_imagebitmap_close.html"]
tags = "imagebitmap"

["test_imagebitmap_cropping.html"]
tags = "imagebitmap"

["test_imagebitmap_structuredclone.html"]
tags = "imagebitmap"

["test_imagebitmap_structuredclone_iframe.html"]
tags = "imagebitmap"

["test_imagebitmap_structuredclone_window.html"]
tags = "imagebitmap"

["test_imagebitmap_transfer.html"]
tags = "imagebitmap"

["test_invalid_mime_type_blob.html"]

["test_isPointInStroke.html"]

["test_offscreencanvas_basic_webgl.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_dynamic_fallback.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_font.html"]

["test_offscreencanvas_many.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_neuter.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_serviceworker.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_sharedworker.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_sizechange.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_subworker.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_toblob.html"]
subsuite = "gpu"
tags = "offscreencanvas"
skip-if = ["true"]

["test_offscreencanvas_toimagebitmap.html"]
subsuite = "gpu"
tags = "offscreencanvas"
# https://bugzilla.mozilla.org/show_bug.cgi?id=1795521
skip-if = ["os == 'android' && debug"]

["test_strokeText_throw.html"]

["test_toBlob.html"]

["test_toBlob_zero_dimension.html"]

["test_toDataURL_alpha.html"]

["test_toDataURL_lowercase_ascii.html"]

["test_toDataURL_parameters.html"]

["test_toDataURL_parameters_png.html"]

["test_windingRuleUndefined.html"]
