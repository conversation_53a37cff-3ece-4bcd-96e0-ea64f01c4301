<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=426082
-->
<head>
  <title>Test for Bug 426082</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <script src="/tests/SimpleTest/WindowSnapshot.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>

<pre id="test">
<script type="application/javascript">

/** Test for Bug 426082 **/
SimpleTest.waitForExplicitFinish();
var subwindow = window.open("./bug426082.html", "bug426082", "width=800,height=1000");

function finishTests() {
  subwindow.close();
  SimpleTest.finish();
}
</script>
</pre>

</body>
</html>
