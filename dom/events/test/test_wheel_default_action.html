<!DOCTYPE HTML>
<html>
<head>
  <title>Test for default action of WheelEvent</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
<script type="application/javascript">

SimpleTest.waitForExplicitFinish();
SimpleTest.requestFlakyTimeout("untriaged");
SpecialPowers.pushPrefEnv({"set": [
    ["mousewheel.system_scroll_override.enabled", false]
]}, runTest);

var subWin = null;

function runTest() {
  subWin = window.open("window_wheel_default_action.html", "_blank",
                       "width=500,height=500,scrollbars=yes");
}

function finish()
{
  subWin.close();
  SimpleTest.finish();
}

</script>
</pre>
</body>
</html>
