<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=615597
-->
<head>
  <title>Test for Bug 615597</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=615597">Mozilla Bug 615597</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

/** Test for Bug 615597 **/

window.ondeviceorientation = function(event) {
  is(event.alpha, 1.5);
  is(event.beta, 2.25);
  is(event.gamma, 3.667);
  is(event.absolute, true);
  SimpleTest.finish();
};

var event = DeviceOrientationEvent;
ok(!!event, "Should have seen DeviceOrientationEvent!");

event = document.createEvent("DeviceOrientationEvent");
event.initDeviceOrientationEvent('deviceorientation', true, true, 1.5, 2.25, 3.667, true);
window.dispatchEvent(event);
SimpleTest.waitForExplicitFinish();

</script>
</pre>
</body>
</html>
