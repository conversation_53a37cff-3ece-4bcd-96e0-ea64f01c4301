<!DOCTYPE html>
<script src="/tests/SimpleTest/paint_listener.js"></script>
<script src="/tests/gfx/layers/apz/test/mochitest/apz_test_utils.js"></script>
<style>
html, body {
  height: 200px;
  margin: 0px;
  padding: 0px;
}
</style>
<body>
<script>
  window.addEventListener("message", async (event) => {
    if (event.data == "innerready") {
      // Wait for APZ state stable so that mouse event handling APZ works properly
      // in out-of-process iframes.
      await promiseApzFlushedRepaints();
    }
    parent.postMessage(event.data, "*");
  });
  window.onload = () => {
    parent.postMessage("middleready", "*");
  };
  document.body.onfocus = () => {
    parent.postMessage("middlefocus", "*");
  };
  document.body.onblur = () => {
    parent.postMessage("middleblur", "*");
  };
</script>
<div style="width:100px;height:100px;background-color:gray;"></div>
<iframe width="100" height="100" src="https://example.org/tests/dom/events/test/file_focus_blur_on_click_in_deep_cross_origin_iframe_inner.html"></iframe>
<script>
  document.querySelector("div").addEventListener("click", event => {
    parent.postMessage("middleclick", "*");
  });
</script>
