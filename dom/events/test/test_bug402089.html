<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=402089
-->
<head>
  <title>Test for Bug 402089</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
</head>
<!-- setTimeout so that the test starts after paint suppression ends -->
<body onload="setTimeout(doTest,0);">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=402089">Mozilla Bug 402089</a>
<p id="display"></p>
<div id="content">
  <pre id="result1"></pre>
  <pre id="result2"></pre>
</div>
<pre id="test">
<script class="testbody" type="text/javascript">

/** Test for Bug 402089 **/

var cachedEvent = null;

function testCachedEvent() {
  testEvent('result2');
  ok((document.getElementById('result1').textContent ==
      document.getElementById('result2').textContent),
      "Event coordinates should be the same after dispatching.");
  SimpleTest.finish();
}

function testEvent(res) {
  var s = cachedEvent.type + "\n";
  s += "clientX: " + cachedEvent.clientX + ", clientY: " + cachedEvent.clientY + "\n";
  s += "screenX: " + cachedEvent.screenX + ", screenY: " + cachedEvent.screenY + "\n";
  s += "layerX: " + cachedEvent.layerX + ", layerY: " + cachedEvent.layerY + "\n";
  s += "pageX: " + cachedEvent.pageX + ", pageY: " + cachedEvent.pageY + "\n";
  document.getElementById(res).textContent += s;
}

function clickHandler(e) {
  cachedEvent = e;
  testEvent('result1');
  e.stopPropagation();
  e.preventDefault();
  window.removeEventListener("click", clickHandler, true);
  setTimeout(testCachedEvent, 10);
}

function doTest() {
  window.addEventListener("click", clickHandler, true);
  var utils = SpecialPowers.getDOMWindowUtils(window);
  utils.sendMouseEvent("mousedown", 1, 1, 0, 1, 0);
  utils.sendMouseEvent("mouseup", 1, 1, 0, 1, 0);

}

SimpleTest.waitForExplicitFinish();
SimpleTest.requestFlakyTimeout("untriaged");
</script>
</pre>
</body>
</html>

