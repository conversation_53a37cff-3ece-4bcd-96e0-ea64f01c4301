<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=641477
-->
<head>
  <title>Test for Bug 641477</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=641477">Mozilla Bug 641477</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

/** Test for Bug 641477 **/

var didThrow = false;

var e = document.createEvent("Event");
try {
  is(e.type, "", "Event type should be empty string before initialization");
  document.dispatchEvent(e);
} catch(ex) {
  didThrow = (ex.name == "InvalidStateError" && ex.code == DOMException.INVALID_STATE_ERR);
}

ok(didThrow, "Should have thrown InvalidStateError!");

</script>
</pre>
</body>
</html>
