<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css" type="text/css"?>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=679494
-->
<window title="Mozilla Bug 679494" onload="doTest();"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>

<body  xmlns="http://www.w3.org/1999/xhtml">
  <a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=679494">Mozilla Bug 679494</a>
  <p id="display"></p>
<div id="content" style="display: none">
  <iframe id="contentframe" src="http://mochi.test:8888/tests/dom/events/test/file_bug679494.html"></iframe>
</div>
</body>

<script class="testbody" type="application/javascript"><![CDATA[

/* Test for bug 679494 */
function doTest() {
  SimpleTest.waitForExplicitFinish();

  var w = document.getElementById("contentframe").contentWindow;
  w.addEventListener("message", function(e) {
    is("test", e.data, "We got the data without a compartment mismatch assertion!");
    SimpleTest.finish();
    });
  w.postMessage("test", "*");
}

]]></script>

</window>
