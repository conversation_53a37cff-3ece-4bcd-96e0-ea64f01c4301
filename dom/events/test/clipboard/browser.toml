[DEFAULT]
support-files = [
  "head.js",
  "!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js",
  "!/gfx/layers/apz/test/mochitest/apz_test_utils.js",
]

["browser_navigator_clipboard_clickjacking.js"]
support-files = ["simple_navigator_clipboard_keydown.html"]
run-if = ["os != 'win'"] # The popupmenus dismiss when access keys for disabled items are pressed on windows
skip-if = ["os == 'mac' && verify"]

["browser_navigator_clipboard_contextmenu_suppression.js"]
support-files = [
  "file_toplevel.html",
  "file_iframe.html",
]

["browser_navigator_clipboard_contextmenu_suppression_ext.js"]
support-files = [
  "file_toplevel.html",
  "file_iframe.html",
]

["browser_navigator_clipboard_read.js"]
support-files = ["simple_navigator_clipboard_read.html"]

["browser_navigator_clipboard_readText.js"]
support-files = ["simple_navigator_clipboard_readText.html"]

["browser_navigator_clipboard_readText_multiple.js"]
support-files = [
  "file_toplevel.html",
  "file_iframe.html",
]

["browser_navigator_clipboard_touch.js"]
support-files = ["simple_navigator_clipboard_readText.html"]
