<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=405632
-->
<head>
  <title>Test for Bug 405632</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=405632">Mozilla Bug 405632</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script class="testbody" type="text/javascript">

/** Test for Bug 405632 **/

  var me = document.createEvent("mouseevent");
  me.initMouseEvent("foo", false, false, window, 0, 100, 100, 100, 100,
                    false, false, false, false, 0, null);
  ok(me.clientX == me.pageX,
     "mouseEvent.clientX should be the same as mouseEvent.pageX when event is initialized manually");
  ok(me.clientY == me.pageY,
     "mouseEvent.clientY should be the same as mouseEvent.pageY when event is initialized manually");

</script>
</pre>
</body>
</html>

