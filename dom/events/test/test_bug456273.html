<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=456273
-->
<head>
  <title>Test for Bug 456273</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=456273">Mozilla Bug 456273</a>
<p id="display">PASS if Firefox does not crash.</p>
<div id="content" style="display: none">
  
</div>

<div id="edit456273" contenteditable="true">text</div>

<pre id="test">
<script type="application/javascript">

/** Test for Bug 456273 **/

function doTest() {
  var ev = new KeyboardEvent("keypress", {
    bubbles: true,
    cancelable: true,
    view: null,
    ctrlKey: true,
    keyCode: 0,
    charCode: "z".charCodeAt(0),
  });
  SpecialPowers.dispatchEvent(window, document.getElementById('edit456273'), ev);

  ok(true, "PASS");
  SimpleTest.finish();
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(doTest);

</script>
</pre>
</body>
</html>
