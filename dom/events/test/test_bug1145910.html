<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1145910
-->
<head>
  <title>Test for Bug 1145910</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>

<script type="application/javascript">

/** Test for Bug 1145910 **/

function runTests() {

  var iframe = document.createElement('iframe');
  document.body.appendChild(iframe);
  iframe.contentDocument.body.innerHTML =
    '<style> div:active { color: rgb(0, 255, 0); } </style> <div id="host">Foo</div>';

  var host = iframe.contentDocument.getElementById("host");
  var shadow = host.attachShadow({mode: 'open'});
  shadow.innerHTML = '<style>div:active { color: rgb(0, 255, 0); }</style><div id="inner">Bar</div>';
  var inner = shadow.getElementById("inner");
  var iframeWin = iframe.contentWindow;

  is(iframeWin.getComputedStyle(host).color, "rgb(0, 0, 0)", "The host should not be active");
  is(iframeWin.getComputedStyle(inner).color, "rgb(0, 0, 0)", "The div inside the shadow root should not be active.");

  synthesizeMouseAtCenter(host, { type: "mousedown" }, iframeWin);

  is(iframeWin.getComputedStyle(inner).color, "rgb(0, 255, 0)", "Div inside shadow root should be active.");
  is(iframeWin.getComputedStyle(host).color, "rgb(0, 255, 0)", "Host should be active when the inner div is made active.");

  synthesizeMouseAtCenter(host, { type: "mouseup" }, iframeWin);

  is(iframeWin.getComputedStyle(inner).color, "rgb(0, 0, 0)", "Div inside shadow root should no longer be active.");
  is(iframeWin.getComputedStyle(host).color, "rgb(0, 0, 0)", "Host should no longer be active.");

  SimpleTest.finish();
};

SimpleTest.waitForExplicitFinish();
window.onload = () => {
  SimpleTest.waitForFocus(runTests);
};

</script>
</body>
</html>
