<html>
<head>
  <title>Test the dragstart event on the anchor in side shadow DOM</title>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
<script>

async function runTests()
{
  let dragService = SpecialPowers.Cc["@mozilla.org/widget/dragservice;1"].
    getService(SpecialPowers.Ci.nsIDragService);

  let iframe = document.querySelector("iframe");
  let iframeDoc = iframe.contentDocument;
  let iframeWin = iframe.contentWindow;

  let shadow = iframeDoc.querySelector('#outer').attachShadow({mode: 'open'});

  let target = iframeDoc.createElement('a');
  target.textContent = "Drag me if you can!";
  const URL = "http://www.mozilla.org/";
  target.href = URL;
  shadow.appendChild(target);

  // Some of the drag data we don't actually care about for this test,
  // so we'll use this comparator function to ignore them.
  function ignoreFunc(actualData, expectedData) {
    return true;
  }

  const EXPECTED_DRAG_DATA = [[{
    type: "text/x-moz-url",
    data: "",
    eqTest: ignoreFunc,
  }, {
    type: "text/x-moz-url-data",
    data: "",
    eqTest: ignoreFunc,
  }, {
    type: "text/x-moz-url-desc",
    data: "",
    eqTest: ignoreFunc,
  }, {
    type: "text/uri-list",
    data: URL,
  }, {
    type: "text/_moz_htmlinfo",
    data: "",
    eqTest: ignoreFunc,
  }, {
    type: "text/html",
    data: "",
    eqTest: ignoreFunc,
  }, {
    type: "text/plain",
    data: URL,
  }]];

  let result = await synthesizePlainDragAndCancel(
    {
      srcElement: target,
      srcWindow: iframeWin,
      finalY: -10,  // Avoid clicking the link
    },
    EXPECTED_DRAG_DATA);
  ok(result === true, "Should have gotten the expected drag data.");
  SimpleTest.finish();
}


SimpleTest.waitForExplicitFinish();
window.onload = () => {
  SimpleTest.waitForFocus(runTests);
};

</script>

<body>
  <iframe srcdoc='<div id="outer"/>'></iframe>
</body>
</html>
