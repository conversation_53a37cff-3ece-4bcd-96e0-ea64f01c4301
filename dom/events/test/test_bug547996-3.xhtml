<?xml version="1.0"?>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:xul="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=547996
-->
<head>
  <title>Test for Bug 547996</title>
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>
  <script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"/>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=547996">Mozilla Bug 547996</a>
<p id="display"></p>
<div id="content" style="display: none"></div>
<pre id="test">
<script>//<![CDATA[

/** Test for Bug 547996 **/
/* mouseEvent.inputSource attribute */

var expectedInputSource = null;

function check(event) {
  is(event.inputSource, expectedInputSource, ".inputSource");
}

function doTest() {
  setup();

  expectedInputSource = MouseEvent.MOZ_SOURCE_MOUSE;
  testMouse();

  expectedInputSource = MouseEvent.MOZ_SOURCE_UNKNOWN;
  testScriptedClicks();

  cleanup();
  SimpleTest.finish();
}

function testMouse() {
  synthesizeMouse($("xulButtonTarget"), 0, 0, {});
}

function testScriptedClicks() {
  $("xulButtonTarget").click();
}

function setup() {
  $("xulButtonTarget").addEventListener("click", check);
}

function cleanup() {
  $("xulButtonTarget").removeEventListener("click", check);
}

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(doTest, window);

//]]>
</script>
</pre>
<xul:button id="xulButtonTarget" accesskey="t">XUL Button</xul:button>
</body>
</html>
