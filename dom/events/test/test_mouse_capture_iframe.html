<!doctype html>
<meta charset="utf-8">
<title>Test mouse capture for iframe</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script src="/tests/SimpleTest/EventUtils.js"></script>
<script src="/tests/SimpleTest/paint_listener.js"></script>
<script src="/tests/gfx/layers/apz/test/mochitest/apz_test_utils.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css"/>
<style>
#target {
  width: 150px;
  height: 150px;
}
</style>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1680405">Mozilla Bug 1680405</a>
<iframe id="target" frameborder="0" scrolling="no"></iframe>
<script>

function waitForMessage(aEventType) {
  return new Promise(function(aResolve, aReject) {
    window.addEventListener("message", function listener(aEvent) {
      is(aEvent.data, aEventType, `check received message ${aEvent.data}`);
      aResolve();
    }, { once: true });
  });
}

let iframe = document.getElementById("target");

add_setup(async function() {
  await SimpleTest.promiseFocus();
  await SpecialPowers.pushPrefEnv({ set: [["test.events.async.enabled", true]] });
  disableNonTestMouseEvents(true);
  SimpleTest.registerCleanupFunction(() => {
    disableNonTestMouseEvents(false);
  });

  iframe.src = "http://example.com/tests/dom/events/test/file_empty.html";
  await waitForMessage("ready");

  await SpecialPowers.spawn(iframe, [], () => {
    let handler = function(e) {
      content.parent.postMessage(e.type, "*");
    };
    content.document.addEventListener("mousedown", handler);
    content.document.addEventListener("mousemove", handler);
    content.document.addEventListener("mouseup", handler);
  });

  await waitUntilApzStable();
});

add_task(async function testMouseCaptureOnXoriginIframe() {
  let unexpectedHandler = function(e) {
    ok(false, `receive unexpected ${e.type} event`);
  };
  document.addEventListener("mousedown", unexpectedHandler);
  document.addEventListener("mousemove", unexpectedHandler);
  document.addEventListener("mouseup", unexpectedHandler);

  synthesizeMouseAtCenter(iframe, { type: "mousedown" });
  await waitForMessage("mousedown");

  synthesizeMouse(iframe, 200, 200, { type: "mousemove" });
  await waitForMessage("mousemove");

  synthesizeMouse(iframe, 200, 200, { type: "mouseup" });
  await waitForMessage("mouseup");
});
</script>
