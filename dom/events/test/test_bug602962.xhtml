<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css" type="text/css"?>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=602962
-->
<window title="Mozilla Bug 602962" onload="openWindow()"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>

<body  xmlns="http://www.w3.org/1999/xhtml">
  <a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=602962">Mozilla Bug 602962</a>
  <p id="display"></p>
<div id="content" style="display: none">
</div>
</body>

<script class="testbody" type="application/javascript"><![CDATA[
/** Test for Bug 602962 **/
var scrollbox, content;
var scrollX = 0, scrollY = 0;

var oldWidth = 0, oldHeight = 0;
var win = null;

function openWindow() {
  win = window.open("chrome://mochitests/content/chrome/dom/events/test/bug602962.xhtml", "_blank", "width=600,height=600");
}

function doTest() {
  scrollbox = win.document.getElementById("page-scrollbox");
  content = win.document.getElementById("page-box");
  content.style.width = 400 + "px";

  win.addEventListener("resize", function() {
    setTimeout(function(){
      scrollbox.scrollBy(200,0);
      resize();
    },0);
  }, { once: true });

  oldWidth = win.outerWidth;
  oldHeight = win.outerHeight;
  win.resizeTo(200, 400);
}

function resize() {
 scrollX = scrollbox.scrollLeft;
 scrollY = scrollbox.scrollTop;

  win.addEventListener("resize", function() {
    content.style.width = (oldWidth + 400) + "px";
    win.removeEventListener("resize", arguments.callee, true);

    setTimeout(function() {
      finish();
    }, 0);
  }, true);

  win.resizeTo(oldWidth, oldHeight);
}

function finish() {
  if (win.outerWidth != oldWidth ||
      win.outerHeight != oldHeight) {
    // We should eventually get back to the original size.
    setTimeout(finish, 0);
    return;
  }
  scrollbox.scrollBy(scrollX, scrollY);

  is(scrollbox.scrollLeft, 200, "Scroll Left should have been restored to the value before the resize");
  is(scrollbox.scrollTop, 0, "Scroll Top should have been restored to the value before the resize");

  is(win.outerWidth, oldWidth, "Width should be resized");
  is(win.outerHeight, oldHeight, "Height should be resized");
  win.close();
  SimpleTest.finish();
}

SimpleTest.waitForExplicitFinish();
]]></script>

</window>
