<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>bug 1539497</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" href="/tests/SimpleTest/test.css"/>
  <script>
    SimpleTest.waitForExplicitFinish();
    function runTest() {
      var win = window.open("about:blank");
      win.onload = function() {
        is(win.navigator.maxTouchPoints, 5, "Should have max touch points");
        win.close();
        SimpleTest.finish();
      }
    }
    function init() {
      SpecialPowers.pushPrefEnv(
        {"set": [["dom.maxtouchpoints.testing.value", 5]]}, runTest);
    }
  </script>
</head>
<body onload="init()">
<p id="display"></p>
<div id="content" style="display: none"></div>
<pre id="test"></pre>
</body>
</html>
