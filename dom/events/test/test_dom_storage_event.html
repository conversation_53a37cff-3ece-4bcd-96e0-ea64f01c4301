<!DOCTYPE HTML>
<html>
<head>
  <title>Test for DOM StorageEvent</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
<script type="application/javascript">

const kTests = [
  { createEventArg: "StorageEvent",
    type: "aaa", bubbles: true, cancelable: true,
    key: null, oldValue: 'a', newValue: 'b', url: 'c', storageArea: null },

  { createEventArg: "storageevent",
    type: "bbb", bubbles: false, cancelable: true,
    key: 'key', oldValue: null, newValue: 'b', url: 'c', storageArea: null },

  { createEventArg: "Storageevent",
    type: "ccc", bubbles: true, cancelable: false,
    key: 'key', oldValue: 'a', newValue: null, url: 'c', storageArea: null },

  { createEventArg: "storageEvent",
    type: "ddd", bubbles: false, cancelable: false,
    key: 'key', oldValue: 'a', newValue: 'b', url: '', storageArea: null },

  { createEventArg: "StorageEvent",
    type: "eee", bubbles: true, cancelable: true,
    key: 'key', oldValue: 'a', newValue: 'b', url: 'c', storageArea: null },

  { createEventArg: "storageevent",
    type: "fff", bubbles: false, cancelable: true,
    key: null, oldValue: null, newValue: null, url: '', storageArea: null },
  ];

for (var i = 0; i < kTests.length; i++) {
  var description = "test, Index: " + i + ", ";
  const kTest = kTests[i];
  var e = document.createEvent(kTest.createEventArg);
  e.initStorageEvent(kTest.type, kTest.bubbles, kTest.cancelable,
                     kTest.key, kTest.oldValue, kTest.newValue, kTest.url,
                     kTest.storageArea);

  for (var attr in kTest) {
    if (attr == 'createEventArg')
      continue;

    is(e[attr], kTest[attr], description + attr + " returns wrong value");
  }
  is(e.isTrusted, false, description + "isTrusted returns wrong value");
}

</script>
</pre>
</body>
</html>
