<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1429572
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 1429572</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">

  /** Test for Bug 1429572 **/
  SimpleTest.waitForExplicitFinish();

  var win;
  function start() {
    SpecialPowers.pushPrefEnv({"set": [["dom.w3c_touch_events.enabled", 1],
                                       ["dom.w3c_touch_events.legacy_apis.enabled", true]]},
    function() {
      ok(true, "Starting the test.");
      win = window.open("window_bug1429572.html", "testwindow",
                        "width=" + window.screen.width +
                        ",height=" + window.screen.height);
    });
  }

  function done() {
    setTimeout(SimpleTest.finish);
  }

  </script>
</head>
<body onload="start();">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1429572">Mozilla Bug 1429572</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
</pre>
</body>
</html>
