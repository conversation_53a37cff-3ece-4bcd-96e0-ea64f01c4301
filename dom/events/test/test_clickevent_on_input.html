<!DOCTYPE HTML>
<html>
<head>
  <title>Test click event on input</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<p id="display">
<input id="input"
  style="position: absolute; top: 5px; left: 5px; border: solid 15px blue; width: 100px; height: 20px;"
  inputmode="none"
  onclick="gClickCount++;">
</p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

var gClickCount = 0;

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(runTests);

var input = document.getElementById("input");

function runTests()
{
  for (var i = 0; i < 3; i++) {
    doTest(i);
  }

  // Re-test left clicking when the input element has some text. 
  gClickCount = 0;
  input.value = "Long text Long text Long text Long text Long text Long text";
  doTest(0);

  input.style.display = "none";
  SimpleTest.finish();
}

function isEnabledMiddleClickPaste()
{
  try {
    return SpecialPowers.getBoolPref("middlemouse.paste");
  } catch (e) {
    return false;
  }
}

function isEnabledAccessibleCaret()
{
  try {
    return SpecialPowers.getBoolPref("layout.accessiblecaret.enabled");
  } catch (e) {
    return false;
  }
}

function doTest(aButton)
{
  // NOTE #1: Non-primary buttons don't generate 'click' events
  // NOTE #2: If touch caret is enabled, touch caret would ovelap input element,
  //          then, the click event isn't generated.
  if (aButton != 2 &&
      aButton != 1 &&
      (aButton != 0 || !isEnabledAccessibleCaret())) {
    gClickCount = 0;
    // click on border of input
    synthesizeMouse(input, 5, 5, { button: aButton });
    is(gClickCount, 1,
       "click event doesn't fired on input element (button is " +
         aButton + ")");

    gClickCount = 0;
    // down on border
    synthesizeMouse(input, 5, 5, { type: "mousedown", button: aButton });
    // up on anonymous div of input
    synthesizeMouse(input, 20, 20, { type: "mouseup", button: aButton });
    is(gClickCount, 1,
       "click event doesn't fired on input element (button is " +
         aButton + ")");

    gClickCount = 0;
    // down on anonymous div of input
    synthesizeMouse(input, 20, 20, { type: "mousedown", button: aButton });
    // up on border
    synthesizeMouse(input, 5, 5, { type: "mouseup", button: aButton });
    is(gClickCount, 1,
       "click event doesn't fired on input element (button is " +
         aButton + ")");
  }

  gClickCount = 0;
  // down on outside of input
  synthesizeMouse(input, -3, -3, { type: "mousedown", button: aButton });
  // up on border
  synthesizeMouse(input, 5, 5, { type: "mouseup", button: aButton });
  is(gClickCount, 0,
     "click event is fired on input element unexpectedly (button is " +
       aButton + ")");
}

</script>
</pre>
</body>
</html>
