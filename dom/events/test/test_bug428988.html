<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=428988
-->
<head>
  <title>Test for Bug 428988</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=428988">Mozilla Bug 428988</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

/** Test for Bug 428988 **/

function listenerForClick(evt) {
  is(Math.round(evt.mozPressure*100), 56, "Wrong .mozPressure");
}

function doTest() {
  var target = document.getElementById("testTarget");
  target.addEventListener("click", listenerForClick, true);
  var me = document.createEvent("MouseEvent");
  me.initNSMouseEvent("click", true, true, window, 0, 0, 0, 0, 0,
                      false, false, false, false, 0, null, 0.56, 0);
  target.dispatchEvent(me);
  target.removeEventListener("click", listenerForClick, true);
  SimpleTest.finish();
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(doTest);

</script>
</pre>
<span id="testTarget" style="border: 1px solid black;">testTarget</span>
</body>
</html>
