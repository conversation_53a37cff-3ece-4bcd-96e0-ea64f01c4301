<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->

<!DOCTYPE html>
<html>
  <!--
       https://bugzilla.mozilla.org/show_bug.cgi?id=648573
     -->
  <head>
    <title>Test for Bug 648573</title>
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  </head>
  <body>
    <a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=648573">Mozilla Bug 648573</a>
    <p id="display"></p>
    <div id="content" style="display: none">

    </div>
    <pre id="test">
      <script type="application/javascript">

      /** Test for Bug 648573 **/
      SimpleTest.waitForExplicitFinish();

      function runTest() {
        var iframe = document.createElement("iframe");
        document.body.appendChild(iframe);
        var win = iframe.contentWindow;
        var doc = iframe.contentDocument;

        var utils = SpecialPowers.getDOMWindowUtils(win);

        ok("createTouch" in doc, "Should have createTouch function");
        ok("createTouchList" in doc, "Should have createTouchList function");
        ok(doc.createEvent("touchevent"), "Should be able to create TouchEvent objects");

        var t1 = doc.createTouch(win, doc, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11);
        is(t1.target, doc, "Wrong target");
        is(t1.identifier, 1, "Wrong identifier");
        is(t1.pageX, 2, "Wrong pageX");
        is(t1.pageY, 3, "Wrong pageY");
        is(t1.screenX, 4, "Wrong screenX");
        is(t1.screenY, 5, "Wrong screenY");
        is(t1.clientX, 6, "Wrong clientX");
        is(t1.clientY, 7, "Wrong clientY");
        is(t1.radiusX, 8, "Wrong radiusX");
        is(t1.radiusY, 9, "Wrong radiusY");
        is(t1.rotationAngle, 10, "Wrong rotationAngle");
        is(t1.force, 11, "Wrong force");

        var t2 = doc.createTouch();

        var l1 = doc.createTouchList(t1);
        is(l1.length, 1, "Wrong length");
        is(l1.item(0), t1, "Wront item (1)");
        is(l1[0], t1, "Wront item (2)");

        var l2 = doc.createTouchList([t1, t2]);
        is(l2.length, 2, "Wrong length");
        is(l2.item(0), t1, "Wront item (3)");
        is(l2.item(1), t2, "Wront item (4)");
        is(l2[0], t1, "Wront item (5)");
        is(l2[1], t2, "Wront item (6)");

        var l3 = doc.createTouchList();

        var e = doc.createEvent("touchevent");
        e.initTouchEvent("touchmove", true, true, win, 0, true, true, true, true,
                         l1, l2, l3);
        is(e.touches, l1, "Wrong list (1)");
        is(e.targetTouches, l2, "Wrong list (2)");
        is(e.changedTouches, l3, "Wrong list (3)");
        ok(e.altKey, "Alt should be true");
        ok(e.metaKey, "Meta should be true");
        ok(e.ctrlKey, "Ctrl should be true");
        ok(e.shiftKey, "Shift should be true");


        var events =
        ["touchstart",
         "touchend",
         "touchmove",
         "touchcancel"];

        function runEventTest(type) {
          var event = doc.createEvent("touchevent");
          event.initTouchEvent(type, true, true, win, 0, true, true, true, true,
                               l1, l2, l3);
          var t = doc.createElement("div");
          // Testing target.onFoo;
          var didCall = false;
          t["on" + type] = function (evt) {
            is(evt, event, "Wrong event");
            evt.target.didCall = true;
          }
          t.dispatchEvent(event);
          ok(t.didCall, "Should have called the listener(1)");

          // Testing <element onFoo="">
          t = doc.createElement("div");
          t.setAttribute("on" + type, "this.didCall = true;");
          t.dispatchEvent(event);
          ok(t.didCall, "Should have called the listener(2)");
        }

        for (var i = 0; i < events.length; ++i) {
          runEventTest(events[i]);
        }

        SimpleTest.finish();
      }

      SpecialPowers.pushPrefEnv(
        {"set": [["dom.w3c_touch_events.legacy_apis.enabled", true]]}, runTest);
      </script>
    </pre>
  </body>
</html>
