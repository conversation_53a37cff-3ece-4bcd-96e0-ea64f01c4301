<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1013412
https://bugzilla.mozilla.org/show_bug.cgi?id=1168182
-->
<head>
  <title>Test for Bug 1013412 and 1168182</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/paint_listener.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <style>
  #content {
    height: 800px;
    overflow: scroll;
  }

  #scroller {
    height: 2000px;
    background: repeating-linear-gradient(#EEE, #EEE 100px, #DDD 100px, #DDD 200px);
  }

  #scrollbox {
    margin-top: 200px;
    width: 500px;
    height: 500px;
    border-radius: 250px;
    box-shadow: inset 0 0 0 60px #555;
    background: #777;
  }

  #circle {
    position: relative;
    left: 240px;
    top: 20px;
    border: 10px solid white;
    border-radius: 10px;
    width: 0px;
    height: 0px;
    transform-origin: 10px 230px;
    will-change: transform;
  }
  </style>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1013412">Mozilla Bug 1013412</a>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1168182">Mozilla Bug 1168182</a>
<p id="display"></p>
<div id="content">
  <p>Scrolling the page should be async and scrolling over the dark circle should scroll the page and avoid rotating the white ball.</p>
  <div id="scroller">
   <div id="scrollbox">
    <div id="circle"></div>
   </div>
  </div>
</div>
<pre id="test">
<script type="application/javascript">

var rotation = 0;
var rotationAdjusted = false;

var incrementForMode = function (mode) {
  switch (mode) {
    case WheelEvent.DOM_DELTA_PIXEL: return 1;
    case WheelEvent.DOM_DELTA_LINE: return 15;
    case WheelEvent.DOM_DELTA_PAGE: return 400;
  }
  return 0;
};

document.getElementById("scrollbox").addEventListener("wheel", function (e) {
  rotation += e.deltaY * incrementForMode(e.deltaMode) * 0.2;
  document.getElementById("circle").style.transform = "rotate(" + rotation + "deg)";
  rotationAdjusted = true;
  e.preventDefault();
});

var iteration = 0;
function runTest() {
  var content = document.getElementById('content');
  // enough iterations that we would scroll to the bottom of 'content'
  if (iteration < 600 && content.scrollTop != content.scrollTopMax) {
    iteration++;
    sendWheelAndPaint(content, 100, 10,
                     { deltaMode: WheelEvent.DOM_DELTA_LINE,
                       deltaY: 1.0, lineOrPageDeltaY: 1 },
                     runTest);
    return;
  }
  var scrollbox = document.getElementById('scrollbox');
  is(content.scrollTop, content.scrollTopMax, "We should have scrolled to the bottom of the scrollframe");
  is(rotationAdjusted, false, "The rotation should not have been adjusted");
  SimpleTest.finish();
}

function startTest() {
  // If we allow smooth scrolling the "smooth" scrolling may cause the page to
  // glide past the scrollbox (which is supposed to stop the scrolling) and so
  // we might end up at the bottom of the page.
  SpecialPowers.pushPrefEnv({"set": [["general.smoothScroll", false],
                                     ["test.events.async.enabled", true],
                                     ["mousewheel.transaction.timeout", 100000],
                                     ["dom.event.wheel-event-groups.enabled", true]]},
                            runTest);
}

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(startTest, window);

</script>
</pre>

</body>
</html>
