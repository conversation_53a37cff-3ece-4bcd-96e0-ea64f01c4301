<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=828554
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 828554</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">

  /** Test for Bug 828554 **/
  SimpleTest.waitForExplicitFinish();
  window.addEventListener("message", function() {
    ok(true, "We got called");
    SimpleTest.finish();
  }, false, undefined);
  window.postMessage("Hey there", "*");
  </script>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=828554">Mozilla Bug 828554</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
</pre>
</body>
</html>
