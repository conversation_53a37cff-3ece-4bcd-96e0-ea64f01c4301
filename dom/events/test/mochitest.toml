[DEFAULT]
# Skip migration work in BG__migrateUI for browser_startup.js since it increases
# the occurrence of the leak reported in bug 1398563 with test_bug1327798.html.
# Run the font-loader eagerly to minimize the risk that font list finalization
# may disrupt the events received or result in a timeout.
tags = "condprof"
prefs = [
  "browser.migration.version=9999999",
  "gfx.font_loader.delay=0",
  "ui.dragThresholdX=4", # Bug 1873142
  "ui.dragThresholdY=4", # Bug 1873142
]
support-files = [
  "bug226361_iframe.xhtml",
  "bug299673.js",
  "bug322588-popup.html",
  "bug426082.html",
  "bug545268.html",
  "bug574663.html",
  "bug607464.html",
  "bug656379-1.html",
  "bug418986-3.js",
  "error_event_worker.js",
  "empty.js",
  "event_leak_utils.js",
  "window_bug493251.html",
  "window_bug659071.html",
  "window_wheel_default_action.html",
  "!/gfx/layers/apz/test/mochitest/apz_test_utils.js",
]

["test_accel_virtual_modifier.html"]
tags = "os_integration"

["test_accesskey.html"]
tags = "os_integration"

["test_addEventListenerExtraArg.html"]

["test_all_synthetic_events.html"]
skip-if = [
  "http3",
  "http2",
]
tags = "os_integration"

["test_auxclick_autoscroll_off.html"]
tags = "os_integration"

["test_bug226361.xhtml"]

["test_bug238987.html"]

["test_bug288392.html"]

["test_bug299673-1.html"]

["test_bug299673-2.html"]

["test_bug322588.html"]

["test_bug328885.html"]

["test_bug336682_1.html"]
support-files = ["test_bug336682.js"]

["test_bug367781.html"]

["test_bug379120.html"]

["test_bug402089.html"]

["test_bug405632.html"]

["test_bug409604.html"]
skip-if = ["os == 'android'"] # android: TIMED_OUT

["test_bug412567.html"]

["test_bug418986-3.html"]

["test_bug422132.html"]

["test_bug426082.html"]
skip-if = [
  "http3",
  "http2",
]

["test_bug427537.html"]
skip-if = [
  "http3",
  "http2",
]

["test_bug428988.html"]

["test_bug432698.html"]

["test_bug443985.html"]
skip-if = ["verify"]

["test_bug447736.html"]

["test_bug448602.html"]

["test_bug450876.html"]

["test_bug456273.html"]

["test_bug457672.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug489671.html"]

["test_bug493251.html"]

["test_bug508479.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM # drag event fails

["test_bug517851.html"]

["test_bug534833.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug545268.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug547996-1.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug547996-2.xhtml"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug556493.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug563329.html"]
skip-if = ["true"] # Disabled due to timeouts.

["test_bug574663.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug591815.html"]

["test_bug593959.html"]

["test_bug603008.html"]
skip-if = ["os == 'android'"]

["test_bug605242.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug607464.html"]
skip-if = [
  "os == 'android'",
  "e10s", #CRASH_DUMP, RANDOM, bug 1400586
]

["test_bug613634.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug615597.html"]
skip-if = ["os == 'android'"] # failed

["test_bug624127.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug635465.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug641477.html"]

["test_bug648573.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug650493.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug656379-1.html"]
skip-if = ["os == 'android'"]

["test_bug656379-2.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug656954.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug659071.html"]
skip-if = [
  "os == 'android'", # fail
  "http3",
  "http2",
]

["test_bug659350.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug662678.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug667612.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug667919-1.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug684208.html"]

["test_bug687787.html"]

["test_bug689564.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug698929.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_bug704423.html"]

["test_bug741666.html"]
skip-if = ["os == 'android'"] # fail

["test_bug812744.html"]
skip-if = [
  "http3",
  "http2",
]

["test_bug822898.html"]

["test_bug855741.html"]

["test_bug864040.html"]

["test_bug924087.html"]

["test_bug930374-content.html"]

["test_bug944011.html"]

["test_bug944847.html"]

["test_bug946632.html"]
skip-if = ["os == 'android'"] # fail

["test_bug967796.html"]

["test_bug985988.html"]

["test_bug998809.html"]

["test_bug1003432.html"]
support-files = ["test_bug1003432.js"]

["test_bug1013412.html"]

["test_bug1017086_enable.html"]
support-files = ["bug1017086_inner.html"]

["test_bug1037990.html"]

["test_bug1079236.html"]

["test_bug1127588.html"]

["test_bug1145910.html"]

["test_bug1150308.html"]

["test_bug1248459.html"]

["test_bug1264380.html"]
skip-if = ["os == 'android'"] # some clipboard types and drag aren't supported

["test_bug1298970.html"]

["test_bug1304044.html"]

["test_bug1305458.html"]

["test_bug1327798.html"]
skip-if = ["headless"]

["test_bug1332699.html"]

["test_bug1339758.html"]

["test_bug1369072.html"]
support-files = ["window_bug1369072.html"]
skip-if = ["os == 'android'"]

["test_bug1429572.html"]
support-files = ["window_bug1429572.html"]
skip-if = ["os == 'android'"] # failed

["test_bug1446834.html"]
support-files = ["file_bug1446834.html"]

["test_bug1447993.html"]
support-files = ["window_bug1447993.html"]
skip-if = ["os == 'android'"]

["test_bug1484371.html"]
support-files = ["file_bug1484371.html"]

["test_bug1534562.html"]
skip-if = ["os == 'android'"] # Bug 1312791

["test_bug1539497.html"]

["test_bug1581192.html"]

["test_bug1635018.html"]

["test_bug1637259.html"]
disabled = "Enable this when the taskcluster Windows machine upgrades to RS5+"
run-if = ["os == 'win'"] # Only Windows supports pen input synthesis
support-files = ["!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"]

["test_bug1673434.html"]

["test_bug1681800.html"]

["test_bug1686716.html"]

["test_bug1692052.html"]
support-files = ["file_bug1692052.html"]

["test_bug1692277.html"]
disabled = "Enable this when the taskcluster Windows machine upgrades to RS5+"
run-if = ["os == 'win'"] # Only Windows supports pen input synthesis
support-files = [
  "!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js",
  "!/dom/base/test/Ahem.ttf",
]

["test_bug1709832.html"]
support-files = ["!/dom/base/test/Ahem.ttf"]

["test_bug1710509.html"]
disabled = "Enable this when the taskcluster Windows machine upgrades to RS5+"
run-if = ["os == 'win'"] # Only Windows supports pen input synthesis
support-files = ["!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"]

["test_bug1728171.html"]
run-if = ["os == 'win'"] # Only Windows 1809+ supports pen input synthesis
support-files = ["!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"]

["test_click_hold_context_menus.html"]
tags = "os_integration"
skip-if = [
  "os == 'mac' && os_version == '10.15' && processor == 'x86_64'",
  "os == 'mac' && os_version == '14.70' && processor == 'x86_64'", # Bug 1929440
]

["test_click_on_reframed_generated_text.html"]

["test_click_on_restyled_element.html"]

["test_clickevent_on_input.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM
tags = "os_integration"

["test_coalesce_mousewheel.html"]
skip-if = ["os == 'android'"] # fail
tags = "os_integration"

["test_coalesce_touchmove.html"]
support-files = [
  "file_coalesce_touchmove_ipc.html",
  "file_coalesce_touchmove_browserchild.html",
  "file_coalesce_touchmove_browserchild2.html",
]
skip-if = ["debug"] #In order to be able to test touchmoves, the test needs to synthesize touchstart in a way which asserts
tags = "os_integration"

["test_continuous_wheel_events.html"]
skip-if = [
  "verify && debug && (os == 'linux' || os == 'win')",
  "os == 'android'", # wheel event isn't supported
]

["test_dblclick_explicit_original_target.html"]

["test_deltaMode_lines_always_enabled.html"]

["test_deviceSensor.html"]
tags = "os_integration"

["test_disabled_events.html"]

["test_dnd_with_modifiers.html"]
tags = "os_integration"

["test_dom_activate_event.html"]

["test_dom_keyboard_event.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM
tags = "os_integration"

["test_dom_mouse_event.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM
tags = "os_integration"

["test_dom_storage_event.html"]
tags = "os_integration"

["test_dom_wheel_event.html"]
skip-if = ["os == 'android'"] # wheel scroll isn't supported
tags = "os_integration"

["test_drag_coords.html"]
skip-if = ["os == 'android'"] # Need calculate screen coordinates.
tags = "os_integration"

["test_drag_image_file.html"]
skip-if = [
  "xorigin", # Bug 1802904
]
support-files = ["green.png"]

["test_draggableprop.html"]

["test_dragstart.html"]

["test_error_events.html"]
skip-if = ["os == 'android'"] #TIMED_OUT

["test_eventTimeStamp.html"]

["test_event_handler_cc.html"]

["test_event_screenXY_in_cross_origin_iframe.html"]
support-files = [
  "file_event_screenXY.html",
  "!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js",
]
skip-if = ["os == 'android'"] # fail

["test_event_screenXY_with_zoom.html"]
skip-if = ["os == 'android'"] # Android doesn't have full zoom.
tags = "os_integration"

["test_eventctors.html"]
skip-if = ["os == 'android'"] #CRASH_DUMP, RANDOM

["test_eventctors_sensors.html"]

["test_eventhandler_scoping.html"]

["test_focus_abspos.html"]

["test_focus_blur_on_click_in_cross_origin_iframe.html"]
support-files = ["file_focus_blur_on_click_in_cross_origin_iframe.html"]
skip-if = [
  "os == 'android'", # Bug 1701546
  "condprof && os == 'linux'", # Bug 1781387
]
tags = "os_integration"

["test_focus_blur_on_click_in_deep_cross_origin_iframe.html"]
support-files = [
  "file_focus_blur_on_click_in_deep_cross_origin_iframe_inner.html",
  "file_focus_blur_on_click_in_deep_cross_origin_iframe_middle.html",
]
skip-if = [
  "os == 'android'", # Bug 1701546
  "condprof && os == 'win'", # 1773806
  "condprof && os == 'linux'", # Bug 1781387
]

["test_hover_mouseleave.html"]

["test_legacy_event.html"]

["test_legacy_touch_api.html"]
tags = "os_integration"

["test_messageEvent.html"]

["test_messageEvent_init.html"]

["test_mouse_capture_iframe.html"]
support-files = ["file_empty.html"]
skip-if = [
  "os == 'android'", # timeout
  "http3",
  "http2",
]

["test_mouse_enterleave_iframe.html"]
support-files = ["file_mouse_enterleave.html"]
skip-if = [
  "!debug", # Bug 1781668
  "os == 'android'", # timeout
  "display == 'wayland' && os_version == '22.04'",  # Bug 1857022
  "http3",
  "http2",
]

["test_mouse_events_after_touchend.html"]
skip-if = [
  "os == 'mac' && os_version == '10.15' && processor == 'x86_64'", # Bug 1881864
  "os == 'mac' && os_version == '14.70' && processor == 'x86_64'", # Bug 1929440
]

["test_mouse_over_at_removing_down_target.html"]

["test_moving_and_expanding_selection_per_page.html"]
support-files = ["window_empty_document.html"]

["test_moz_mouse_pixel_scroll_event.html"]

["test_offsetxy.html"]

["test_onerror_handler_args.html"]

["test_passive_listeners.html"]

["test_scroll_per_page.html"]
support-files = ["window_empty_document.html"]
skip-if = ["os == 'android'"] # fail

["test_selection_after_right_click.html"]
tags = "os_integration"

["test_slotted_mouse_event.html"]
skip-if = ["os == 'android'"] # timeout

["test_slotted_text_click.html"]

["test_submitevent_on_form.html"]

["test_text_event_in_content.html"]

["test_unbound_before_in_active_chain.html"]

["test_unicode_input_on_windows_with_emulation.html"]
tags = "os_integration"

["test_use_conflated_keypress_event_model_on_newer_Office_Online_Server.html"]
tags = "os_integration"

["test_use_split_keypress_event_model_on_old_Confluence.html"]
skip-if = ["!debug"] # The mode change event is available only on debug build

["test_use_split_keypress_event_model_on_old_Office_Online_Server.html"]
skip-if = ["!debug"] # The mode change event is available only on debug build

["test_wheel_default_action.html"]
skip-if = [
  "os == 'linux'",
  "os == 'android'",
]

["test_wheel_zoom_on_form_controls.html"]
skip-if = [
  "verify",
  "os == 'android'", # wheel isn't supported
]
