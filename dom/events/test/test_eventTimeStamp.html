<!doctype html>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=77992
-->
<head>
  <meta charset="utf-8">
  <title>Test for Event.timeStamp (Bug 77992)</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank"
  href="https://bugzilla.mozilla.org/show_bug.cgi?id=77992">Mozilla Bug 77992</a>
<p id="display"></p>
<pre id="test">
<script type="text/js-worker" id="worker-src">
  // Simply returns the event timestamp
  onmessage = function(evt) {
    postMessage(evt.timeStamp + performance.timeOrigin);
  }
</script>
<script type="text/js-worker" id="shared-worker-src">
  // Simply returns the event timestamp
  onconnect = function(evt) {
    var port = evt.ports[0];
    port.onmessage = function(messageEvt) {
      port.postMessage(messageEvt.timeStamp + performance.timeOrigin);
    };
  };
</script>
<script type="application/javascript">
"use strict";

SimpleTest.waitForExplicitFinish();
SimpleTest.requestFlakyTimeout("untriaged");

// This file performs tests that normalize the timeOrigin within a worker
// and compare it to the page. When this occurs, time can appear to go backwards.
// This is a known (and accepted) regression of privacy.reduceTimerPrecision so
// we need to turn it off.
SpecialPowers.pushPrefEnv({ "set": [
    ["privacy.reduceTimerPrecision", false]
  ]}, testRegularEvents);

// Event.timeStamp should be relative to the time origin which is:
//
//   Non-worker context: navigation start
//   Dedicated worker: navigation start of the document that created the worker
//   Shared worker: creation time of the shared worker
//
// See: https://w3c.github.io/web-performance/specs/HighResolutionTime2/Overview.html#sec-time-origin

function testRegularEvents() {
  var timeBeforeEvent = performance.now();
  document.getElementById('test').addEventListener("click", function(evt) {
    var timeAfterEvent = performance.now();
    ok(evt.timeStamp >= timeBeforeEvent &&
       evt.timeStamp <= timeAfterEvent,
       "Event timestamp (" + evt.timeStamp + ") is in expected range: [" +
         timeBeforeEvent + ", " + timeAfterEvent + "]");
    testWorkerEvents();
  });
  document.getElementById('test').click();
}

function testWorkerEvents() {
  var blob = new Blob([ document.getElementById("worker-src").textContent ],
                      { type: "text/javascript" });
  var worker = new Worker(URL.createObjectURL(blob));
  worker.onmessage = function(evt) {
    var timeAfterEvent = performance.now() + performance.timeOrigin;
    ok(evt.data >= timeBeforeEvent &&
       evt.data <= timeAfterEvent,
       "Event timestamp in dedicated worker (" + evt.data +
         ") is in expected range: [" +
         timeBeforeEvent + ", " + timeAfterEvent + "]");
    worker.terminate();
    testSharedWorkerEvents();
  };
  var timeBeforeEvent = performance.now() + performance.timeOrigin;
  worker.postMessage("");
}

function testSharedWorkerEvents() {
  var blob =
    new Blob([ document.getElementById("shared-worker-src").textContent ],
             { type: "text/javascript" });
  // Delay creation of worker slightly so it is easier to distinguish
  // shared worker creation time from this document's navigation start
  setTimeout(function() {
  var timeBeforeEvent = performance.now() + performance.timeOrigin;
    var worker = new SharedWorker(URL.createObjectURL(blob));
    worker.port.onmessage = function(evt) {
      var timeAfterEvent = performance.now() + performance.timeOrigin;
      ok(evt.data >= timeBeforeEvent &&
         evt.data <= timeAfterEvent,
         "Event timestamp in shared worker (" + evt.data +
           ") is in expected range: [" +
           timeBeforeEvent + ", " + timeAfterEvent + "]");
      worker.port.close();
      finishTests();
    };
    worker.port.start();
    worker.port.postMessage("");
  }, 500);
}

var finishTests = function() {
  SimpleTest.finish();
};

</script>
</pre>
</body>
</html>
