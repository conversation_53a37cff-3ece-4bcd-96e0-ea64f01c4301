<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Check whether `mouseup` events are fired after pending boundary events</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script src="/tests/SimpleTest/EventUtils.js"></script>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<style>
div#parent {
  width: 100%;
  height: 50px;
  background-color: gray;
}
div#child {
  width: 100%;
  height: 40px;
  background-color: lime;
}
</style>
<script>
"use strict";

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(async () => {
  await SpecialPowers.pushPrefEnv({ set: [["layout.reflow.synthMouseMove", true]] });
  const winUtils = SpecialPowers.wrap(window).windowUtils;
  try {
    winUtils.disableNonTestMouseEvents(true);
    const parent = document.querySelector("div");
    const child = parent.querySelector("div");
    synthesizeMouseAtCenter(child, { type: "mousemove" });
    await new Promise(resolve => requestAnimationFrame(
      () => requestAnimationFrame(resolve)
    ));

    const mouseEvents = [];
    child.addEventListener("mousedown", event => {
      event.target.remove();
      mouseEvents.push("mousedown@div#child");
    });
    document.addEventListener("mouseover", event => {
      mouseEvents.push(`mouseover@${event.target.localName}${event.target.id ? `#${event.target.id}` : ""}`);
    }, {capture: true});
    document.addEventListener("mouseup", event => {
      mouseEvents.push(`mouseup@${event.target.localName}${event.target.id ? `#${event.target.id}` : ""}`);
    }, {capture: true});
    winUtils.advanceTimeAndRefresh(100);
    // Click in the child, then, the child will be removed by the "mousedown"
    // event listener and that should cause "mouseover" on the parent and that
    // must be fired before "mouseup".
    synthesizeMouseAtCenter(child, {});
    winUtils.restoreNormalRefresh();
    await new Promise(resolve => requestAnimationFrame(
      () => requestAnimationFrame(resolve)
    ));
    is(
      mouseEvents.toString(),
      "mousedown@div#child,mouseover@div#parent,mouseup@div#parent",
      "mouseover should be fired before mouseup on the ex-parent of the removed child"
    );
  } finally {
    winUtils.disableNonTestMouseEvents(false);
    document.querySelector("style").remove();
  }
  SimpleTest.finish();
});
</script>
</head>
<body>
<div id="parent">
 <div id="child"></div>
</div>
</body>
</html>
