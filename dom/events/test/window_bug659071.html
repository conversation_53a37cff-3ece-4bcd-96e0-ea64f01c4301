<!DOCTYPE HTML>
<html>
<head>
  <title>Test for Bug 659071</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<video id="v" controls></video>
<script type="application/javascript">

SimpleTest.waitForFocus(runTests, window);

function ok() {
  window.opener.ok.apply(window.opener, arguments);
}

function info() {
  window.opener.info.apply(window.opener, arguments);
}

async function waitForCondition(aFunc) {
  for (let i = 0; i < 30; i++) {
    await (new Promise(resolve => requestAnimationFrame(() => requestAnimationFrame(resolve))));
    if (aFunc(SpecialPowers.getFullZoom(window))) {
      return true;
    }
  }
  return false;
}

async function runTests() {
  await SpecialPowers.pushPrefEnv(
    {
      "set": [
        ["mousewheel.with_control.action", 3],
        ["test.events.async.enabled", true],
      ]
    }
  );

  const video = document.getElementById("v");

  for (let i = 0; i < 3; i++) {
    synthesizeWheel(video, 10, 10,
      { deltaMode: WheelEvent.DOM_DELTA_LINE, ctrlKey: true,
        deltaX: 0, deltaY: 1.0, lineOrPageDeltaX: 0, lineOrPageDeltaY: 1 });
    if (await waitForCondition(aZoomLevel => aZoomLevel < 1.0)) {
      break;
    }
    info("Retrying zoom out...");
  }
  let zoomLevel = SpecialPowers.getFullZoom(window);
  ok(
    zoomLevel < 1.0,
    `Failed to zoom out with turning wheel to bottom, got: ${zoomLevel}`
  );

  for (let i = 0; i < 4; i++) {
    synthesizeWheel(video, 10, 10,
      { deltaMode: WheelEvent.DOM_DELTA_LINE, ctrlKey: true,
        deltaX: 0, deltaY: -1.0, lineOrPageDeltaX: 0, lineOrPageDeltaY: -1 });
    if (await waitForCondition(aZoomLevel => aZoomLevel > 1.0)) {
      break;
    }
    info("Retrying zoom in...");
  }
  zoomLevel = SpecialPowers.getFullZoom(window);
  ok(
    zoomLevel > 1.0,
    `Failed to zoom in with turning wheel to top, got: ${zoomLevel}`
  );

  synthesizeKey("0", { accelKey: true });
  ok(
    await waitForCondition(aZoomLevel => aZoomLevel == 1.0),
    "Failed to reset zoom at finish the test"
  );
  opener.finish();
}

</script>
</body>
</html>
