<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=422132
-->
<head>
  <title>Test for Bug 422132</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <script src="/tests/SimpleTest/paint_listener.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=422132">Mozilla Bug 422132</a>
<p id="display"></p>
<div id="target" style="font-size: 0; width: 200px; height: 200px; overflow: auto;">
  <div style="width: 1000px; height: 1000px;"></div>
</div>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script class="testbody" type="text/javascript">

/** Test for Bug 422132 **/

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(function() {
  SpecialPowers.pushPrefEnv({
    "set":[["general.smoothScroll", false],
           ["mousewheel.min_line_scroll_amount", 1],
           ["mousewheel.system_scroll_override.enabled", false],
           ["mousewheel.transaction.timeout", 100000],
           ["test.events.async.enabled", true]]}, runTests)}, window);

function runTests()
{
  var target = document.getElementById("target");

  var scrollLeft = target.scrollLeft;
  var scrollTop = target.scrollTop;

  var tests = [
    {
      prepare() {
        scrollLeft = target.scrollLeft;
        scrollTop = target.scrollTop;
      },
      event: {
        deltaMode: WheelEvent.DOM_DELTA_PIXEL,
        deltaX: 0.5,
        deltaY: 0.5,
        lineOrPageDeltaX: 0,
        lineOrPageDeltaY: 0
      },
    }, {
      event: {
        deltaMode: WheelEvent.DOM_DELTA_PIXEL,
        deltaX: 0.5,
        deltaY: 0.5,
        lineOrPageDeltaX: 0,
        lineOrPageDeltaY: 0
      },
      check() {
        is(target.scrollLeft - scrollLeft, 1,
           "not scrolled to right by 0.5px delta value with pending 0.5px delta");
        is(target.scrollTop - scrollTop, 1,
           "not scrolled to bottom by 0.5px delta value with pending 0.5px delta");
      },
    }, {
      prepare() {
        scrollLeft = target.scrollLeft;
        scrollTop = target.scrollTop;
      },
      event: {
        deltaMode: WheelEvent.DOM_DELTA_LINE,
        deltaX: 0.5,
        deltaY: 0.5,
        lineOrPageDeltaX: 0,
        lineOrPageDeltaY: 0
      },
    }, {
      event: {
        deltaMode: WheelEvent.DOM_DELTA_LINE,
        deltaX: 0.5,
        deltaY: 0.5,
        lineOrPageDeltaX: 1,
        lineOrPageDeltaY: 1
      },
      check() {
        is(target.scrollLeft - scrollLeft, 1,
           "not scrolled to right by 0.5 line delta value with pending 0.5 line delta");
        is(target.scrollTop - scrollTop, 1,
           "not scrolled to bottom by 0.5 line delta value with pending 0.5 line delta");
      }
    }
  ];

  var nextTest = function() {
    var test = tests.shift();
    if (test.prepare) {
      test.prepare();
    }

    sendWheelAndPaint(target, 10, 10, test.event, function() {
      if (test.check) {
        test.check();
      }
      if (!tests.length) {
        SimpleTest.finish();
        return;
      }

      setTimeout(nextTest, 0);
    });
  }

  nextTest();
}

</script>
</pre>
</body>
</html>
