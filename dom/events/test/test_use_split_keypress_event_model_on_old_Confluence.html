<!DOCTYPE html>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1514940
-->
<head>
  <meta charset="utf-8">
  <title>Testing whether "keypress" event model is forcibly split model if the document is old Confluence instance</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1514940">Bug 1514940</a>
<p id="display"></p>
<pre id="test"></pre>
<input id="input">
<iframe id="iframe" srcdoc="<html><body><p>Here is editor</p></body></html>"></iframe>
<script>
// Emulate window.tinyMCE.CursorTargetPlugin().getInfo() which is referred by
// KeyPresEventModelCheckerChild.
class CursorTargetPluginImpl {
  getInfo() {
    return {
      longname: "Cursor Target plugin",
      author: "Atlassian",
      authorurl: "http://www.atlassian.com",
      version: "1.0",
    };
  }
}
var tinyMCE = {
  plugins: {
    CursorTargetPlugin: CursorTargetPluginImpl,
  },
};

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(async function doTests() {
  let iframe = document.getElementById("iframe");
  let waitForCheckKeyPressEventModelEvent = new Promise(resolve => {
    SpecialPowers.wrap(iframe.contentDocument).addEventListener("CheckKeyPressEventModel", resolve, {mozSystemGroup: true, once: true});
  });
  iframe.contentDocument.body.setAttribute("contenteditable", "true");
  await waitForCheckKeyPressEventModelEvent;
  iframe.contentDocument.body.focus();
  let keypressEvent;
  iframe.contentDocument.body.addEventListener("keypress", aEvent => keypressEvent = aEvent, {once: true});
  synthesizeKey("a", {}, iframe.contentWindow);
  is(keypressEvent.keyCode, 0,
     "keyCode value of 'a' should be 0");
  is(keypressEvent.charCode, "a".charCodeAt(0),
     "charCode value of 'a' should be 'a'");

  iframe.contentDocument.body.addEventListener("keypress", aEvent => keypressEvent = aEvent, {once: true});
  synthesizeKey("KEY_Enter", {}, iframe.contentWindow);
  is(keypressEvent.keyCode, KeyboardEvent.DOM_VK_RETURN,
     "keyCode value of 'Enter' should be DOM_VK_RETURN");
  is(keypressEvent.charCode, 0,
     "charCode value of 'Enter' should be 0");

  let input = document.getElementById("input");
  input.focus();
  input.addEventListener("keypress", aEvent => keypressEvent = aEvent, {once: true});
  synthesizeKey("a", {});
  is(keypressEvent.keyCode, "a".charCodeAt(0),
     "keyCode value of 'a' in the parent document should be 'a'");
  is(keypressEvent.charCode, "a".charCodeAt(0),
     "charCode value of 'a' in the parent document should be 'a'");

  input.addEventListener("keypress", aEvent => keypressEvent = aEvent, {once: true});
  synthesizeKey("KEY_Enter");
  is(keypressEvent.keyCode, KeyboardEvent.DOM_VK_RETURN,
     "keyCode value of 'Enter' in the parent document should be DOM_VK_RETURN");
  is(keypressEvent.charCode, KeyboardEvent.DOM_VK_RETURN,
     "charCode value of 'Enter' in the parent document should be DOM_VK_RETURN");

  SimpleTest.finish();
});
</script>
</body>
</html>
