<!doctype html>
<meta charset="utf-8">
<title>Test :hover state on mouseleave.</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script src="/tests/SimpleTest/EventUtils.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css"/>
<style>
div {
  width: 100px;
  height: 100px;
}
</style>
<div id="target" style="background: green;"></div>
<div id="outside" style="background: blue;"></div>
<script>
SimpleTest.waitForExplicitFinish();
let mouseLeaveCount = 0;
let mouseOutCount = 0;

target.addEventListener("mouseleave", () => {
  if (mouseLeaveCount++ != 0)
    return;
  is(target.matches(":hover"), false,
     "Should've been not hovered on mouseleave");
  is(outside.matches(":hover"), true,
     "New target should be hovered on mouseleave");
  if (mouseOutCount)
    SimpleTest.finish();
});

target.addEventListener("mouseout", () => {
  if (mouseOutCount++ != 0)
    return;
  is(target.matches(":hover"), false,
     "Should've been not hovered on mouseleave");
  is(outside.matches(":hover"), true,
     "New target should be hovered on mouseleave");
  if (mouseLeaveCount)
    SimpleTest.finish();
});

SimpleTest.waitForFocus(() => {
  synthesizeMouseAtCenter(outside, { type: "mousemove" });
  synthesizeMouseAtCenter(target, { type: "mousemove" });
  synthesizeMouseAtCenter(outside, { type: "mousemove" });
});
</script>
