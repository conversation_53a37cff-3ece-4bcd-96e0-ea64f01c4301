<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css" type="text/css"?>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=586961
-->
<window title="Mozilla Bug 586961"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>
  <script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"/>
<body  xmlns="http://www.w3.org/1999/xhtml">
  <a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=586961">Mozilla Bug 586961</a>

  <p id="display"></p>
<div id="content" style="display: none">
</div>
</body>

<box onclick="clicked(event)">
  <label id="controllabel" control="controlbutton" accesskey="k" value="Click here" />
  <button id="controlbutton" label="Button" />
</box>

<script class="testbody" type="application/javascript"><![CDATA[

/** Test for Bug 586961 **/

function clicked(event) {
  is(event.target.id, "controlbutton", "Accesskey was directed to controlled element.");
  SimpleTest.finish();
}

function test() {
  var accessKeyDetails = (navigator.platform.includes("Mac")) ?
                         { altKey : true, ctrlKey : true } : 
                         { altKey : true, shiftKey: true };
  synthesizeKey("k", accessKeyDetails);
}

SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(test, window);

]]></script>

</window>
