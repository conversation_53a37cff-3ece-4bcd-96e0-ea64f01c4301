<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=998809
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 998809</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">

  /** Test for Bug 998809 **/
  var event1 = document.createEvent("Event");
  event1.initEvent("a", false, false);
  event1.initEvent("b", false, false);
  is(event1.type, "b");
  var event2 = document.createEvent("Event");
  event2.initEvent("a", false, false);
  is(event2.type, "a");
  event2.initEvent("b", false, false);
  is(event2.type, "b");

  </script>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=998809">Mozilla Bug 998809</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
</pre>
</body>
</html>
