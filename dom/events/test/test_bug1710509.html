<!DOCTYPE html>
<meta charset="utf-8">
<title>Bug 1710509</title>
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script src="/tests/gfx/layers/apz/test/mochitest/apz_test_utils.js"></script>
<script src="/tests/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"></script>
<link rel="stylesheet" href="/tests/SimpleTest/test.css" />

<style>
  #container {
    width: 100px;
    height: 100px;
    touch-action: none;
  }
</style>

<div id="container"></div>

<script>
  /**
   * @template {keyof HTMLElementEventMap} K
   * @param {HTMLElemnt} target
   * @param {K} eventName
   * @return {HTMLElementEventMap[K]}
   */
  function waitForEvent(target, eventName) {
    return new Promise(resolve => {
      target.addEventListener(eventName, resolve, { once: true });
    });
  }

  add_task(async function testPenDrag() {
    await SpecialPowers.pushPrefEnv({
      set: [
        ["dom.w3c_pointer_events.dispatch_by_pointer_messages", false],
      ],
    });

    await SimpleTest.promiseFocus();
    const container = document.getElementById("container");
    const touchMovePromise = waitForEvent(container, "touchmove");
    await promiseNativePointerDrag(container, "pen", 50, 50, -50, -50);

    const touchmove = await touchMovePromise;
    const [touch] = touchmove.touches;
    is(touch.radiusX, 1, ".radiusX");
    is(touch.radiusY, 1, ".radiusX");
  });
</script>
