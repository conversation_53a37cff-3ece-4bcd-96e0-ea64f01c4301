<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=656379
-->
<head>
  <title>Test for Bug 656379</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <style>
    input[type="button"]:hover { color: green; }
  </style>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=656379">Mozilla Bug 656379</a>
<p id="display">
  <label for="button1" id="label1">Label 1</label>
  <input type="button" id="button1" value="Button 1">
  <label>
    <span id="label2">Label 2</span>
    <input type="button" id="button2" value="Button 2">
  </label>
</p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script type="application/javascript">

function log(aEvent) {
  function getPath() {
    if (!aEvent.target) {
      return "(null)";
    }
    function getNodeName(aNode) {
      if (aNode.id) {
        return `${aNode.nodeName}#${aNode.id}`;
      }
      return aNode.nodeName;
    }
    let path = getNodeName(aEvent.target);
    for (let parent = aEvent.target.parentElement;
         parent && document.body != parent;
         parent = parent.parentElement) {
      path = `${getNodeName(parent)} > ${path}`;
    }
    return path;
  }
  info(`${aEvent.type} on ${getPath()}`);
}

window.addEventListener("mousemove", log, {capture: true});
window.addEventListener("mouseenter", log, {capture: true});
window.addEventListener("mouseleave", log, {capture: true});
window.addEventListener("mouseover", log, {capture: true});
window.addEventListener("mouseout", log, {capture: true});

/** Test for Bug 656379 **/
SimpleTest.waitForExplicitFinish();
function* tests() {
  info("Synthesizing mousemove on label1...");
  synthesizeMouseAtCenter($("label1"), { type: "mousemove" });
  yield undefined;
  is($("button1").matches(":hover"), true,
     "Button 1 should be hovered after mousemove over label1");
  is($("label1").matches(":hover"), true,
     "Label 1 should be hovered after mousemove over label1");
  is($("button2").matches(":hover"), false,
     "Button 2 should not be hovered after mousemove over label1");
  is($("label2").matches(":hover"), false,
     "Label 2 should not be hovered after mousemove over label1");
  info("Synthesizing mousemove on button2...");
  synthesizeMouseAtCenter($("button2"), { type: "mousemove" });
  yield undefined;
  is($("button1").matches(":hover"), false,
     "Button 1 should not be hovered after mousemove over button2");
  is($("label1").matches(":hover"), false,
     "Label 1 should not be hovered after mousemove over button2");
  is($("button2").matches(":hover"), true,
     "Button 2 should be hovered after mousemove over button2");
  is($("label2").matches(":hover"), false,
     "Label 2 should not be hovered after mousemove over label2");
  info("Synthesizing mousemove on label2...");
  synthesizeMouseAtCenter($("label2"), { type: "mousemove" });
  yield undefined;
  is($("button1").matches(":hover"), false,
     "Button 1 should not be hovered after mousemove over label2");
  is($("label1").matches(":hover"), false,
     "Label 1 should not be hovered after mousemove over label2");
  is($("button2").matches(":hover"), true,
     "Button 2 should be hovered after mousemove over label2");
  is($("label2").matches(":hover"), true,
     "Label 2 should be hovered after mousemove over label2");
  SimpleTest.finish();
}

function executeTests() {
  var testYielder = tests();
  function execNext() {
    let {done} = testYielder.next();
    if (done) {
      return;
    }
    SimpleTest.executeSoon(execNext);
  }
  execNext();
}

SimpleTest.waitForFocus(executeTests);

</script>
</pre>
</body>
</html>
