<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css" type="text/css"?>
<!--
Bug 418986
-->
<window title="Mozilla Bug 418986"
  xmlns:html="http://www.w3.org/1999/xhtml"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>      
  <script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"></script>

<body id="body" xmlns="http://www.w3.org/1999/xhtml">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=418986">
Mozilla Bug 418986</a>
</body>

<script type="application/javascript" src="bug418986-3.js"></script>
<script type="application/javascript"><![CDATA[
  // This test produces fake mouse events and checks that the screenX and screenY
  // properties of the received event objects provide client window coordinates.
  // Run the test once the window has loaded.
  test(false);
]]></script>  

</window>
