<!DOCTYPE html>
<meta charset=utf-8>
<title>Test for error events being ErrorEvent</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<div id="log"></div>
<script>
  setup({allow_uncaught_exception:true});
  var errorEvent;
  var file;
  var line;
  var msg;
  var column;
  var error;
  window.addEventListener("error", function(e) {
    errorEvent = e;
  }, {once: true});
  var oldOnerror = window.onerror;
  window.onerror = function(message, filename, lineno, columnno, errorObject) {
    window.onerror = oldOnerror;
    file = filename;
    line = lineno;
    msg = message;
    column = columnno;
    error = errorObject;
  }
  var thrown = new Error("hello");
  throw thrown;
</script>
<script>
  generate_tests(assert_equals, [
    [ "Event filename", errorEvent.filename, location.href ],
    [ "Callback filename", file, location.href ],
    [ "Event line number", errorEvent.lineno, 27 ],
    [ "Callback line number", line, 27 ],
    [ "Event message", errorEvent.message, "Error: hello" ],
    [ "Callback message", msg, "Error: hello" ],
    [ "Event error-object", errorEvent.error, thrown],
    [ "Callback error-object", error, thrown ],
    [ "Event column", errorEvent.colno, 16 ],
    [ "Callback column", column, 16 ]
  ]);
</script>
<script>
  var workerLocation = location.protocol + "//" + location.host +
    location.pathname.replace("test_error_events.html", "error_event_worker.js");
  var eventFileTest = async_test("Worker event filename");
  var eventLineTest = async_test("Worker event line number");
  var eventMessageTest = async_test("Worker event message");
  var callbackFileTest = async_test("Worker callback filename");
  var callbackLineTest = async_test("Worker callback line number");
  var callbackMessageTest = async_test("Worker callback message");
  var w = new Worker("error_event_worker.js");
  w.addEventListener("message", function(msg) {
    if (msg.data.type == "event") {
      eventFileTest.step(function() { assert_equals(msg.data.filename, workerLocation); });
      eventFileTest.done();
      eventLineTest.step(function() { assert_equals(msg.data.lineno, 19); });
      eventLineTest.done();
      eventMessageTest.step(function() { assert_equals(msg.data.message, "Error: workerhello"); });
      eventMessageTest.done();
    } else {
      callbackFileTest.step(function() { assert_equals(msg.data.filename, workerLocation); });
      callbackFileTest.done();
      callbackLineTest.step(function() { assert_equals(msg.data.lineno, 19); });
      callbackLineTest.done();
      callbackMessageTest.step(function() { assert_equals(msg.data.message, "Error: workerhello"); });
      callbackMessageTest.done();
    }
  });
</script>
