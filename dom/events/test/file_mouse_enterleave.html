<html>
<body>
<style>
#target {
  width: 100%;
  height: 100%;
}
#reflow {
  width: 100%;
  height: 10px;
  background-color: red;
}
</style>
<div id="target"></div>
<div id="reflow"></div>
<script>
function listener(e) {
  parent.postMessage({ eventType: e.type, targetName: e.target.localName }, "*");
}

window.addEventListener("message", function(aEvent) {
  if (aEvent.data === "reflow") {
    let reflow = document.getElementById("reflow");
    reflow.style.display = "none";
    reflow.getBoundingClientRect();
    reflow.style.display = "block";
    reflow.getBoundingClientRect();

    // Now wait a bit to see if there is any unexpected events fired.
    requestAnimationFrame(
      () => parent.postMessage({ eventType: "reflowed" }, "*")
    );
  }
});

let target = document.getElementById("target");
target.addEventListener("mouseenter", listener);
target.addEventListener("mouseleave", listener);

let root = document.documentElement;
root.addEventListener("mouseenter", listener);
root.addEventListener("mouseleave", listener);
</script>
</body>
</html>
