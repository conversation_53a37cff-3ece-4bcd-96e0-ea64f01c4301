<!DOCTYPE HTML>
<html>
<head>
  <title>Test for default action of WheelEvent</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <script src="/tests/SimpleTest/paint_listener.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<p id="display"></p>
<div id="scrollable" style="overflow: auto; width: 200px; height: 200px;">
  <div id="clipper" style="margin: 0; padding: 0; overflow: hidden; width: 3000px; height: 3000px;">
    <div id="scrolled" style="width: 5000px; height: 5000px;">
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
      Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text. Tere is a lot of text.<br>
    </div>
  </div>
</div>
<div id="spacerForBody"></div>
<div id="content" style="display: none">

</div>
<pre id="test">
<script type="application/javascript">

// Until the first non-blank paint, the parent will set the opacity of our
// browser to 0 using the 'blank' attribute.
// Until the blank attribute is removed, we can't send scroll events.
SimpleTest.waitForFocus(function() {
  let chromeScript = SpecialPowers.loadChromeScript(_ => {
    /* eslint-env mozilla/chrome-script */
    let win = Services.wm.getMostRecentWindow("navigator:browser");
    win.requestAnimationFrame(() => {
      win.gBrowser.selectedBrowser.removeAttribute("blank");
      win.requestAnimationFrame(() => {
        sendAsyncMessage("blank-attribute-removed");
      });
    });
  });
  chromeScript.promiseOneMessage("blank-attribute-removed").then(() => {
    chromeScript.destroy();
    runTests();
  });
}, window);

SimpleTest.requestLongerTimeout(6);
SimpleTest.requestFlakyTimeout("untriaged");

var winUtils = SpecialPowers.getDOMWindowUtils(window);
// grab refresh driver
winUtils.advanceTimeAndRefresh(100);

var gScrollableElement = document.getElementById("scrollable");
var gScrolledElement = document.getElementById("scrolled");
var gSpacerForBodyElement = document.getElementById("spacerForBody");

const kDefaultActionNone                 = 0;
const kDefaultActionScroll               = 1;
const kDefaultActionHistory              = 2;
const kDefaultActionZoom                 = 3;
const kDefaultActionHorizontalizedScroll = 4;

const kDefaultActionOverrideXNoOverride = -1;
const kDefaultActionOverrideXNone       = kDefaultActionNone;
const kDefaultActionOverrideXScroll     = kDefaultActionScroll;
const kDefaultActionOverrideXHistory    = kDefaultActionHistory;
const kDefaultActionOverrideXZoom       = kDefaultActionZoom;

// We sometimes rely on the scroll position not getting preserved across tests.
function resetScrollPosition(element) {
  element.style.display = "none";
  element.offsetTop;
  element.style.display = "";
}

function is()
{
  window.opener.is.apply(window.opener, arguments);
}

function ok()
{
  window.opener.ok.apply(window.opener, arguments);
}

function sendWheelAndWait(aX, aY, aEvent, aCallback)
{
  sendWheelAndPaint(gScrollableElement, aX, aY, aEvent, aCallback);
}

function hitEventLoop(aFunc, aTimes)
{
  winUtils.advanceTimeAndRefresh(100);

  if (--aTimes) {
    setTimeout(hitEventLoop, 0, aFunc, aTimes);
  } else {
    setTimeout(aFunc, 20);
  }
}

function onZoomReset(aCallback) {
  var fullZoom = SpecialPowers.getFullZoom(window);
  if (fullZoom == 1) {
    SimpleTest.executeSoon(aCallback);
    return;
  }
  // Zoom causes a resize of the viewport.
  window.addEventListener("resize", function onResize(event) {
    is(SpecialPowers.getFullZoom(window), 1, "Zoom should be reset to 1");
    window.removeEventListener("resize", onResize);
    SimpleTest.executeSoon(aCallback);
  });
}

function setDeltaMultiplierSettings(aSettings, aCallback)
{
  SpecialPowers.pushPrefEnv({"set": [
    ["mousewheel.default.delta_multiplier_x", aSettings.deltaMultiplierX * 100],
    ["mousewheel.default.delta_multiplier_y", aSettings.deltaMultiplierY * 100],
    ["mousewheel.default.delta_multiplier_z", aSettings.deltaMultiplierZ * 100],
    ["mousewheel.with_alt.delta_multiplier_x", aSettings.deltaMultiplierX * 100],
    ["mousewheel.with_alt.delta_multiplier_y", aSettings.deltaMultiplierY * 100],
    ["mousewheel.with_alt.delta_multiplier_z", aSettings.deltaMultiplierZ * 100],
    ["mousewheel.with_control.delta_multiplier_x", aSettings.deltaMultiplierX * 100],
    ["mousewheel.with_control.delta_multiplier_y", aSettings.deltaMultiplierY * 100],
    ["mousewheel.with_control.delta_multiplier_z", aSettings.deltaMultiplierZ * 100],
    ["mousewheel.with_meta.delta_multiplier_x", aSettings.deltaMultiplierX * 100],
    ["mousewheel.with_meta.delta_multiplier_y", aSettings.deltaMultiplierY * 100],
    ["mousewheel.with_meta.delta_multiplier_z", aSettings.deltaMultiplierZ * 100],
    ["mousewheel.with_shift.delta_multiplier_x", aSettings.deltaMultiplierX * 100],
    ["mousewheel.with_shift.delta_multiplier_y", aSettings.deltaMultiplierY * 100],
    ["mousewheel.with_shift.delta_multiplier_z", aSettings.deltaMultiplierZ * 100],
   ]}, aCallback);
}

function doTestScroll(aSettings, aCallback)
{
  const kNoScroll    = 0x00;
  const kScrollUp    = 0x01;
  const kScrollDown  = 0x02;
  const kScrollLeft  = 0x04;
  const kScrollRight = 0x08;

  const kTests = [
    { description: "Scroll to bottom by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to bottom by pixel scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to top by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to top by pixel scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to right by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by pixel scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by pixel scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to bottom-right by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollRight },
    { description: "Scroll to bottom-left by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollLeft },
    { description: "Scroll to top-left by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollLeft },
    { description: "Scroll to top-right by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollRight },
    { description: "Not Scroll by pixel scroll for z",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },

    { description: "Scroll to bottom by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to bottom by line scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to top by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to top by line scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to right by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by line scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by line scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to bottom-right by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollRight },
    { description: "Scroll to bottom-left by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollLeft },
    { description: "Scroll to top-left by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollLeft },
    { description: "Scroll to top-right by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollRight },
    { description: "Not Scroll by line scroll for z",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },

    { description: "Scroll to bottom by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to bottom by page scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to top by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to top by page scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to right by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by page scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by page scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to bottom-right by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollRight },
    { description: "Scroll to bottom-left by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollLeft },
    { description: "Scroll to top-left by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollLeft },
    { description: "Scroll to top-right by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollRight },
    { description: "Not Scroll by page scroll for z",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },

    // special cases.

    // momentum scroll should cause scroll even if the action is zoom, but if the default action is none,
    // shouldn't do it.
    { description: "Scroll to bottom by momentum pixel scroll when lineOrPageDelta is 0, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to bottom by momentum pixel scroll when lineOrPageDelta is 1, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to top by momentum pixel scroll when lineOrPageDelta is 0, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to top by momentum pixel scroll when lineOrPageDelta is -1, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to right by momentum pixel scroll when lineOrPageDelta is 0, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by momentum pixel scroll when lineOrPageDelta is 1, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by momentum pixel scroll when lineOrPageDelta is 0, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by momentum pixel scroll when lineOrPageDelta is -1, even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to bottom-right by momentum pixel scroll even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollRight },
    { description: "Scroll to bottom-left by momentum pixel scroll even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollDown | kScrollLeft },
    { description: "Scroll to top-left by momentum pixel scroll even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollLeft },
    { description: "Scroll to top-right by momentum pixel scroll even if the action is zoom",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kScrollUp | kScrollRight },
    { description: "Not Scroll by momentum pixel scroll for z (action is zoom)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Not Scroll by momentum pixel scroll if default action is none (action is zoom)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: true, altKey: false, metaKey: false },
      expected: kNoScroll,
      prepare (cb) { SpecialPowers.pushPrefEnv({"set": [["mousewheel.default.action", 0]]}, cb); },
      cleanup (cb) { SpecialPowers.pushPrefEnv({"set": [["mousewheel.default.action", 1]]}, cb); } },

    // momentum scroll should cause scroll even if the action is history, but if the default action is none,
    // shouldn't do it.
    { description: "Scroll to bottom by momentum pixel scroll when lineOrPageDelta is 0, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to bottom by momentum pixel scroll when lineOrPageDelta is 1, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to top by momentum pixel scroll when lineOrPageDelta is 0, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to top by momentum pixel scroll when lineOrPageDelta is -1, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to right by momentum pixel scroll when lineOrPageDelta is 0, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by momentum pixel scroll when lineOrPageDelta is 1, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by momentum pixel scroll when lineOrPageDelta is 0, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by momentum pixel scroll when lineOrPageDelta is -1, even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to bottom-right by momentum pixel scroll even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollDown | kScrollRight },
    { description: "Scroll to bottom-left by momentum pixel scroll even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollDown | kScrollLeft },
    { description: "Scroll to top-left by momentum pixel scroll even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollUp | kScrollLeft },
    { description: "Scroll to top-right by momentum pixel scroll even if the action is history",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kScrollUp | kScrollRight },
    { description: "Not Scroll by momentum pixel scroll for z (action is history)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kNoScroll },
    { description: "Not Scroll by momentum pixel scroll if default action is none (action is history)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: true,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: true, metaKey: false },
      expected: kNoScroll,
      prepare (cb) { SpecialPowers.pushPrefEnv({"set": [["mousewheel.default.action", 0]]}, cb); },
      cleanup (cb) { SpecialPowers.pushPrefEnv({"set": [["mousewheel.default.action", 1]]}, cb); } },

    // Don't scroll along axis whose overflow style is hidden.
    { description: "Scroll to only bottom by oblique pixel wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 16.0, deltaY: 16.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown,
      prepare(cb) { gScrollableElement.style.overflowX = "hidden"; cb(); } },
    { description: "Scroll to only bottom by oblique line wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to only bottom by oblique page wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to only top by oblique pixel wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -16.0, deltaY: -16.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to only top by oblique line wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to only top by oblique page wheel event with overflow-x: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp,
      cleanup (cb) { gScrollableElement.style.overflowX = "auto"; cb(); } },
    { description: "Scroll to only right by oblique pixel wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 16.0, deltaY: 16.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight,
      prepare(cb) { gScrollableElement.style.overflowY = "hidden"; cb(); } },
    { description: "Scroll to only right by oblique line wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to only right by oblique page wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to only left by oblique pixel wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -16.0, deltaY: -16.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to only top by oblique line wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to only top by oblique page wheel event with overflow-y: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft,
      cleanup (cb) { gScrollableElement.style.overflowY = "auto"; cb(); } },
    { description: "Don't be scrolled by oblique pixel wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 16.0, deltaY: 16.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll,
      prepare(cb) { gScrollableElement.style.overflow = "hidden"; cb(); } },
    { description: "Don't be scrolled by oblique line wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't be scrolled by oblique page wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 1, expectedOverflowDeltaY: 1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't be scrolled by oblique pixel wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -16.0, deltaY: -16.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't be scrolled by oblique line wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't be scrolled by oblique page wheel event with overflow: hidden",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: -1, expectedOverflowDeltaY: -1,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll,
      cleanup (cb) { gScrollableElement.style.overflow = "auto"; cb(); } },

    // Don't scroll along axis whose overflow style is hidden and overflow delta values should
    // be zero if there is ancestor scrollable element.
    { description: "Scroll to only bottom by oblique pixel wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 16.0, deltaY: 16.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown,
      prepare(cb) {
        gScrollableElement.style.overflowX = "hidden";
        gScrollableElement.style.position = "fixed";
        gScrollableElement.style.top = "30px";
        gScrollableElement.style.left = "30px";
        // Make body element scrollable.
        gSpacerForBodyElement.style.width = "5000px";
        gSpacerForBodyElement.style.height = "5000px";
        document.documentElement.scrollTop = 500;
        document.documentElement.scrollLeft = 500;
        cb();
      } },
    { description: "Scroll to only bottom by oblique line wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to only bottom by oblique page wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollDown },
    { description: "Scroll to only top by oblique pixel wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -16.0, deltaY: -16.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to only top by oblique line wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp },
    { description: "Scroll to only top by oblique page wheel event with overflow-x: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollUp,
      cleanup (cb) { gScrollableElement.style.overflowX = "auto"; cb(); } },
    { description: "Scroll to only right by oblique pixel wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 16.0, deltaY: 16.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight,
      prepare(cb) { gScrollableElement.style.overflowY = "hidden"; cb(); } },
    { description: "Scroll to only right by oblique line wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to only right by oblique page wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 1.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to only left by oblique pixel wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -16.0, deltaY: -16.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to only top by oblique line wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to only top by oblique page wheel event with overflow-y: hidden (body is scrollable)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: -1.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft,
      cleanup (cb) {
        gScrollableElement.style.overflowY = "auto";
        gScrollableElement.style.position = "static";
        gSpacerForBodyElement.style.width = "";
        gSpacerForBodyElement.style.height = "";
        cb();
      } },
  ];

  var description;

  var currentTestIndex = -1;
  var isXReverted = (aSettings.deltaMultiplierX < 0);
  var isYReverted = (aSettings.deltaMultiplierY < 0);

  function doNextTest()
  {
    if (++currentTestIndex >= kTests.length) {
      SimpleTest.executeSoon(aCallback);
      return;
    }

    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;

    var currentTest = kTests[currentTestIndex];
    description = "doTestScroll(aSettings=" + aSettings.description + "), " + currentTest.description + ": ";
    if (currentTest.prepare) {
      // prepare() can make changes to a page such as
      // changing the 'overflow' property which requires
      // a repaint to take effect before sending
      // scroll-wheel events.
      currentTest.prepare(doWaitForPaintsAndScroll);
    } else {
      doTestCurrentScroll();
    }
  }

  function doWaitForPaintsAndScroll() {
   waitForAllPaintsFlushed(doTestCurrentScroll);
  }

  function doTestCurrentScroll() {
    var currentTest = kTests[currentTestIndex];
    sendWheelAndWait(10, 10, currentTest.event, function () {
      if (currentTest.expected == kNoScroll) {
        is(gScrollableElement.scrollTop, 1000, description + "scrolled vertical");
        is(gScrollableElement.scrollLeft, 1000, description + "scrolled horizontal");
      } else {
        var scrollUp = !isYReverted ? (currentTest.expected & kScrollUp) :
                                      (currentTest.expected & kScrollDown);
        var scrollDown = !isYReverted ? (currentTest.expected & kScrollDown) :
                                        (currentTest.expected & kScrollUp);
        if (scrollUp) {
          ok(gScrollableElement.scrollTop < 1000, description + "not scrolled up, got " + gScrollableElement.scrollTop);
        } else if (scrollDown) {
          ok(gScrollableElement.scrollTop > 1000, description + "not scrolled down, got " + gScrollableElement.scrollTop);
        } else {
          is(gScrollableElement.scrollTop, 1000, description + "scrolled vertical");
        }
        var scrollLeft = !isXReverted ? (currentTest.expected & kScrollLeft) :
                                        (currentTest.expected & kScrollRight);
        var scrollRight = !isXReverted ? (currentTest.expected & kScrollRight) :
                                         (currentTest.expected & kScrollLeft);
        if (scrollLeft) {
          ok(gScrollableElement.scrollLeft < 1000, description + "not scrolled to left, got " + gScrollableElement.scrollLeft);
        } else if (scrollRight) {
          ok(gScrollableElement.scrollLeft > 1000, description + "not scrolled to right, got " + gScrollableElement.scrollLeft);
        } else {
          is(gScrollableElement.scrollLeft, 1000, description + "scrolled horizontal");
        }
      }
      if (currentTest.cleanup) {
        currentTest.cleanup(nextStep);
      } else {
        nextStep();
      }

      function nextStep() {
        winUtils.advanceTimeAndRefresh(100);
        doNextTest();
      }
    });
  }
  doNextTest();
}

function doTestHorizontalizedScroll(aSettings, aCallback)
{
  const kNoScroll    = 0x00;
  const kScrollLeft  = 0x01;
  const kScrollRight = 0x02;

  const kTests = [
    { description: "Scroll to right by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by pixel scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by pixel scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by deltaX (pixel scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by deltaX (pixel scroll, lineOrPageDelta is 1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by negative deltaX (pixel scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by negative deltaX (pixel scroll, lineOrPageDelta is -1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Scroll only to right by diagonal pixel scroll (to bottom-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to right by diagonal pixel scroll (to bottom-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to left by diagonal pixel scroll (to top-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll only to left by pixel scroll (to bottom-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by pixel scroll for z-axis",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },

    { description: "Scroll to right by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by line scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by line scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by deltaX (line scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by deltaX (line scroll, lineOrPageDelta is 1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by negative deltaX (line scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by negative deltaY (line scroll, lineOrPageDelta is -1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Scroll only to right by diagonal line scroll (to bottom-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to right by diagonal line scroll (to bottom-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to left by diagonal line scroll (to top-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll only to left by line scroll (to top-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by line scroll for z-axis",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },

    { description: "Scroll to right by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to right by page scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll to left by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll to left by page scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by deltaX (page scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by deltaX (page scroll, lineOrPageDelta is 1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by deltaX (page scroll, lineOrPageDelta is 0)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Don't scroll by deltaX (page scroll, lineOrPageDelta is -1)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
    { description: "Scroll only to right by diagonal page scroll (to bottom-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to right by diagonal page scroll (to bottom-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollRight },
    { description: "Scroll only to left by diagonal page scroll (to top-left)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Scroll only to left by diagonal page scroll (to top-right)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kScrollLeft },
    { description: "Don't scroll by page scroll for z-axis",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: true, ctrlKey: false, altKey: false, metaKey: false },
      expected: kNoScroll },
  ];

  var description;

  var currentTestIndex = -1;
  // deltaY should cause horizontal scroll and affected by deltaMultiplierY.
  // So, horizontal scroll amount and direction is affected by deltaMultiplierY.
  var isXReverted = (aSettings.deltaMultiplierY < 0);

  function doNextTest()
  {
    if (++currentTestIndex >= kTests.length) {
      SimpleTest.executeSoon(aCallback);
      return;
    }

    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;

    var currentTest = kTests[currentTestIndex];
    description = "doTestHorizontalizedScroll(aSettings=" + aSettings.description + "), " + currentTest.description + ": ";
    if (currentTest.prepare) {
      currentTest.prepare(doTestCurrentScroll);
    } else {
      doTestCurrentScroll();
    }
  }

  function doTestCurrentScroll() {
    var currentTest = kTests[currentTestIndex];
    sendWheelAndWait(10, 10, currentTest.event, function () {
      is(gScrollableElement.scrollTop, 1000, description + "scrolled vertical");
      if (currentTest.expected == kNoScroll) {
        is(gScrollableElement.scrollLeft, 1000, description + "scrolled horizontal");
      } else {
        var scrollLeft = !isXReverted ? (currentTest.expected & kScrollLeft) :
                                        (currentTest.expected & kScrollRight);
        var scrollRight = !isXReverted ? (currentTest.expected & kScrollRight) :
                                         (currentTest.expected & kScrollLeft);
        if (scrollLeft) {
          ok(gScrollableElement.scrollLeft < 1000, description + "not scrolled to left, got " + gScrollableElement.scrollLeft);
        } else if (scrollRight) {
          ok(gScrollableElement.scrollLeft > 1000, description + "not scrolled to right, got " + gScrollableElement.scrollLeft);
        } else {
          is(gScrollableElement.scrollLeft, 1000, description + "scrolled horizontal");
        }
      }
      if (currentTest.cleanup) {
        currentTest.cleanup(nextStep);
      } else {
        nextStep();
      }

      function nextStep() {
        winUtils.advanceTimeAndRefresh(100);
        doNextTest();
      }
    });
  }
  doNextTest();
}

// It will take *freaking* long time(maybe *hours*) to test all the writing mode
// combinations for the scroll target and its root, because there are altogether
// *one hundred* combinations (10 x 10)!
//
// So unless you really don't care a snap on time-consuming testing or a strict
// criteria is required for testing, it is strongly recommeneded that you
// comment out at least the writing modes which are marked as "peculiar" before
// running this test, you are encouraged to also comment out those "uncommon"
// writing modes in order to further shorten testing time.
//
// Note that if you are going to run time-consuming tests without commenting out
// most of the writing modes, don't forget to increase the value of the
// parameter in SimpleTest.requestLongerTimeout in this file; otherwise it'll
// most likely lead you to a timed-out failure.
//
// Also note that |isBTT| has nothing to do with the behaviour of auto-dir
// scrolling, it's just used to set the sign of |kOrigScrollTop|.
const kWritingModes = [
  {
    isRTL: true,
    isBTT: false,
    styles: [
      {
        writingMode: "horizontal-tb",
        direction:   "rtl",
      },
      {
        writingMode: "vertical-rl",
        direction:   "ltr",
      },
      // uncommon
      //{
      //  writingMode: "sideways-rl",
      //  direction:   "ltr",
      //},
    ],
  },
  {
    isRTL: false,
    isBTT: false,
    styles: [
      {
        writingMode: "horizontal-tb",
        direction:   "ltr",
      },
      // uncommon
      //{
      //  writingMode: "vertical-lr",
      //  direction:   "ltr",
      //},
      // uncommon
      //{
      //  writingMode: "sideways-lr",
      //  direction:   "ltr",
      //},
    ],
  },
  {
    isRTL: true,
    isBTT: true,
    styles: [
      // peculiar
      //{
      //  writingMode: "vertical-rl",
      //  direction:   "rtl",
      //},
      // peculiar
      //{
      //  writingMode: "sideways-rl",
      //  direction:   "rtl",
      //},
    ],
  },
  {
    isRTL: false,
    isBTT: true,
    styles: [
      // peculiar
      //{
      //  writingMode: "vertical-lr",
      //  direction:   "rtl",
      //},
      // peculiar
      //{
      //  writingMode: "sideways-lr",
      //  direction:   "rtl",
      //},
    ],
  },
];

function getFirstWritingModeStyle()
{
  if (kWritingModes.length < 1) {
    return false;
  }
  let typeIndex = 0;
  while (!kWritingModes[typeIndex].styles.length) {
    typeIndex++;
    if (typeIndex >= kWritingModes.length) {
      return false;
    }
  }
  return {typeIndex, styleIndex: 0};
}

function getNextWritingModeStyle(curStyle)
{
  let typeIndex  = curStyle.typeIndex;
  let styleIndex = curStyle.styleIndex + 1;
  while (typeIndex < kWritingModes.length) {
    if (styleIndex < kWritingModes[typeIndex].styles.length) {
      return {typeIndex, styleIndex};
    }
    typeIndex++;
    styleIndex = 0;
  }
  return false;
}

function doTestAutoDirScroll(aSettings, aAutoDirTrait, aCallback)
{
  // Go through all the writing-mode combinations for the scroll target and its
  // root.

  let firstStyle = getFirstWritingModeStyle();
  if (!firstStyle) {
    // The whole writing mode list is empty, no need to do any test for auto-dir
    // scrolling. Go ahead with the subsequent tests.
    SimpleTest.executeSoon(aCallback);
    return;
  }

  // Begin with the first style for both the root and the scroll target.
  // doTestAutoDirScroll2 will recursively call itself back for every
  // style combination with getNextWritingModeStyle until all combinations have
  // been enumerated, and then it will call SimpleTest.executeSoon(aCallback).
  doTestAutoDirScroll2(aSettings, aAutoDirTrait,
                       firstStyle, firstStyle,
                       aCallback);
}

function doTestAutoDirScroll2(aSettings, aAutoDirTrait,
                              aStyleForRoot, aStyleForTarget,
                              aCallback)
{
  const kStyleTypeForRoot   = kWritingModes[aStyleForRoot.typeIndex];
  const kStyleTypeForTarget = kWritingModes[aStyleForTarget.typeIndex];

  const kStyleForRoot   = kStyleTypeForRoot.styles[aStyleForRoot.styleIndex];
  const kStyleForTarget = kStyleTypeForTarget.styles[aStyleForTarget.styleIndex];

  const kIsRootRTL   = kStyleTypeForRoot.isRTL;
  const kIsTargetRTL = kStyleTypeForTarget.isRTL;
  // Just used to set the sign of |kOrigScrollTop|, not related to the auto-dir
  // behaviour.
  const kIsTargetBTT = kStyleTypeForTarget.isBTT;

  const kOldStyleForRoot   = {};
  const kOldStyleForTarget = {};

  const kHonoursRoot = Boolean(aAutoDirTrait.honoursRoot);

  const kNoScroll    = 0x00;
  const kScrollUp    = 0x01;
  const kScrollDown  = 0x02;
  const kScrollLeft  = 0x04;
  const kScrollRight = 0x08;

  // The four constants indicate the expected result if the scroll direction is
  // adjusted.
  const kAdjustedForUp    = {};
  const kAdjustedForDown  = {};
  const kAdjustedForLeft  = {};
  const kAdjustedForRight = {};
  if (kHonoursRoot ? kIsRootRTL : kIsTargetRTL) {
    kAdjustedForUp.result    = kScrollRight;
    kAdjustedForUp.desc      = "right";
    kAdjustedForDown.result  = kScrollLeft;
    kAdjustedForDown.desc    = "left";
    kAdjustedForLeft.result  = kScrollDown;
    kAdjustedForLeft.desc    = "bottom";
    kAdjustedForRight.result = kScrollUp;
    kAdjustedForRight.desc   = "top";
  } else {
    kAdjustedForUp.result    = kScrollLeft;
    kAdjustedForUp.desc      = "left";
    kAdjustedForDown.result  = kScrollRight;
    kAdjustedForDown.desc    = "right";
    kAdjustedForLeft.result  = kScrollUp;
    kAdjustedForLeft.desc    = "top";
    kAdjustedForRight.result = kScrollDown;
    kAdjustedForRight.desc   = "bottom";
  }

  const kTests = [
    // Tests:   Test pixel scrolling towards four edges when the target
    //          overflows in both the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions.
    { description: "auto-dir scroll to bottom by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown,
      prepare (cb) {
                 // Static contents will not start from the topleft side in some
                 // writing modes, for ease of coding, we simply absolutely
                 // position the target to the topleft in every case.
                 gScrollableElement.style.position = "absolute";
                 gScrollableElement.style.top      = "10px";
                 gScrollableElement.style.left     = "10px";
                 SpecialPowers.pushPrefEnv({
                   "set": [["mousewheel.autodir.enabled", true],
                           ["mousewheel.autodir.honourroot", kHonoursRoot]]
                 }, cb);
               } },
    { description: "auto-dir scroll to bottom by pixel scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by pixel scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to right by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by pixel scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by pixel scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by pixel scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test diagonal pixel scrolling when the target overflows in both
    //          the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions, furthermore, it never applies to
    //          diagonal scrolling.
    { description: "auto-dir scroll to bottom-right by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollRight },
    { description: "auto-dir scroll to bottom-left by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollLeft },
    { description: "auto-dir scroll to top-left by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollLeft },
    { description: "auto-dir scroll to top-right by pixel scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollRight },

    // Tests:   Test line scrolling towards four edges when the target overflows
    //          in both the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions.
    { description: "auto-dir scroll to bottom by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to bottom by line scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by line scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to right by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by line scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by line scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by line scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test diagonal line scrolling when the target overflows in both
    //          the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions, furthermore, it never applies to
    //          diagonal scrolling.
    { description: "auto-dir scroll to bottom-right by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollRight },
    { description: "auto-dir scroll to bottom-left by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollLeft },
    { description: "auto-dir scroll to top-left by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollLeft },
    { description: "auto-dir scroll to top-right by line scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollRight },

    // Tests:   Test page scrolling towards four edges when the target overflows
    //          in both the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions.
    { description: "auto-dir scroll to bottom by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to bottom by page scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by page scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to right by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by page scroll when lineOrPageDelta is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by page scroll even if lineOrPageDelta is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by page scroll when lineOrPageDelta is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test diagonal page scrolling when the target overflows in both
    //          the two directions.
    // Results: All are unadjusted.
    // Reason:  Auto-dir adjustment never applies to a target which overflows in
    //          both the two directions, furthermore, it never applies to
    //          diagonal scrolling.
    { description: "auto-dir scroll to bottom-right by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollRight },
    { description: "auto-dir scroll to bottom-left by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown | kScrollLeft },
    { description: "auto-dir scroll to top-left by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollLeft },
    { description: "auto-dir scroll to top-right by page scroll",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp | kScrollRight },

    // The tests above in this function are all for testing a target with two
    // scrollbars. All of them should not be adjustable.
    // From here on, the tests below in this function are all for testing a
    // target with only one scrollbar, either a vertical scrollbar or horizontal
    // scrollbar. Some of them are adjustable.

    // Tests:   Test pixel scrolling towards four edges when the target
    //          overflows only in the horizontal direction.
    // Results: Vertical wheel scrolls are adjusted to be horizontal whereas the
    //          horizontal wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "auto";
                 gScrollableElement.style.overflowY = "hidden";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by pixel scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by pixel scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to right by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by pixel scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by pixel scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test pixel scrolling towards four edges when the target
    //          overflows only in the vertical direction.
    // Results: Horizontal wheel scrolls are adjusted to be vertical whereas the
    //          vertical wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to bottom by pixel scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "hidden";
                 gScrollableElement.style.overflowY = "auto";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to bottom by pixel scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by pixel scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by pixel scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by pixel scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by pixel scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result },

    // Tests:   Test line scrolling towards four edges when the target overflows
    //          only in the horizontal direction.
    // Results: Vertical wheel scrolls are adjusted to be horizontal whereas the
    //          horizontal wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by line scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "auto";
                 gScrollableElement.style.overflowY = "hidden";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by line scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by line scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by line scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to right by line scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by line scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by line scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by line scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test line scrolling towards four edges when the target overflows
    //          only in the vertical direction.
    // Results: Horizontal wheel scrolls are adjusted to be vertical whereas the
    //          vertical wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to bottom by line scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "hidden";
                 gScrollableElement.style.overflowY = "auto";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to bottom by line scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by line scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by line scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by line scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by line scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by line scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by line scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result },

    // Tests:   Test page scrolling towards four edges when the target overflows
    //          only in the horizontal direction.
    // Results: Vertical wheel scrolls are adjusted to be horizontal whereas the
    //          horizontal wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by page scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "auto";
                 gScrollableElement.style.overflowY = "hidden";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by page scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by page scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by page scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "auto-dir scroll to right by page scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to right by page scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "auto-dir scroll to left by page scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "auto-dir scroll to left by page scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },

    // Tests:   Test page scrolling towards four edges when the target overflows
    //          only in the vertical direction.
    // Results: Horizontal wheel scrolls are adjusted to be vertical whereas the
    //          vertical wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "auto-dir scroll to bottom by page scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "hidden";
                 gScrollableElement.style.overflowY = "auto";
                 resetScrollPosition(gScrollableElement);
                 cb();
               } },
    { description: "auto-dir scroll to bottom by page scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollDown },
    { description: "auto-dir scroll to top by page scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to top by page scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollUp },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by page scroll when lineOrPageDelta is 1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForRight.desc +
                   "(originally right) by page scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForRight.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by page scroll when lineOrPageDelta is -1, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result },
    { description: "auto-dir scroll to " + kAdjustedForLeft.desc +
                   "(originally left) by page scroll even if lineOrPageDelta is 0, " +
                   "no horizontal scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForLeft.result,
      cleanup (cb) {
                 SpecialPowers.pushPrefEnv({"set":
                                               [["mousewheel.autodir.enabled",
                                                 false]]},
                                            cb);
               } },
    // Tests:   Test that autodir scrolling can be force-enabled using windowUtils.
    //          This only tests vertical wheel scrolls being adjusted to be
    //          horizontal, rather than re-testing all autodir behaviours just for
    //          this way of enabling it.
    // Results: Vertical wheel scrolls are adjusted to be horizontal whereas the
    //          horizontal wheel scrolls are unadjusted.
    // Reason:  Auto-dir adjustment applies to a target if the target overflows
    //          in only one direction and the direction is orthogonal to the
    //          wheel and deltaZ is zero.
    { description: "force-enabled auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result,
      prepare (cb) {
                 gScrollableElement.style.overflowX = "auto";
                 gScrollableElement.style.overflowY = "hidden";
                 resetScrollPosition(gScrollableElement);
                 winUtils.setMousewheelAutodir(gScrollableElement, true, kHonoursRoot)
                 cb();
               } },
    { description: "force-enabled auto-dir scroll to " + kAdjustedForDown.desc +
                   "(originally bottom) by pixel scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForDown.result },
    { description: "force-enabled auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "force-enabled auto-dir scroll to " + kAdjustedForUp.desc +
                   "(originally top) by pixel scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: true,
      expected: kAdjustedForUp.result },
    { description: "force-enabled auto-dir scroll to right by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "force-enabled auto-dir scroll to right by pixel scroll when lineOrPageDelta is 1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollRight },
    { description: "force-enabled auto-dir scroll to left by pixel scroll even if lineOrPageDelta is 0, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft },
    { description: "force-enabled auto-dir scroll to left by pixel scroll when lineOrPageDelta is -1, " +
                   "no vertical scrollbar",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0, isMomentum: false,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0,
               shiftKey: false, ctrlKey: false, altKey: false, metaKey: false },
      adjusted: false,
      expected: kScrollLeft,
      cleanup (cb) {
                 gScrollableElement.style.position = "static";
                 gScrollableElement.style.top = "auto";
                 gScrollableElement.style.left = "auto";
                 gScrollableElement.style.overflow = "auto";
                 Object.assign(document.body.style, kOldStyleForRoot);
                 Object.assign(gScrollableElement.style, kOldStyleForTarget);
                 winUtils.setMousewheelAutodir(gScrollableElement, false, false);
                 cb();
              } },
  ];

  let styleDescForRoot = "";
  let styleDescForTarget = "";
  Object.keys(kStyleForRoot).forEach(function(property)
  {
    kOldStyleForRoot[property] = document.body.style[property];
    document.body.style[property] = kStyleForRoot[property];
    if ("" !== styleDescForRoot) {
      styleDescForRoot += " ";
    }
    styleDescForRoot += property + ": " + kStyleForRoot[property] + ";";
  });
  Object.keys(kStyleForTarget).forEach(function(property)
  {
    kOldStyleForTarget[property] = gScrollableElement.style[property];
    gScrollableElement.style[property] = kStyleForTarget[property];
    if ("" !== styleDescForTarget) {
      styleDescForTarget += " ";
    }
    styleDescForTarget += property + ": " +
                            kStyleForTarget[property] + ";";
  });

  let description;
  let currentTestIndex = -1;
  let isXReverted = aSettings.deltaMultiplierX < 0;
  let isYReverted = aSettings.deltaMultiplierY < 0;

  // We are doing a "trick" here:
  // If the `kHonoursRoot` is true and the scroll target and the root's contents
  // are both LTR or both RTL, we can safely skip these tests, because the same
  // behavior is tested when the `kHonoursRoot` is false.
  if (kHonoursRoot && kIsRootRTL === kIsTargetRTL) {
    currentTestIndex = kTests.length;
  }

  const kOrigScrollLeft = kIsTargetRTL ? -1000 : 1000;
  const kOrigScrollTop = kIsTargetBTT ? -1000 : 1000;

  function doNextTest()
  {
    if (++currentTestIndex >= kTests.length) {
      // The tests for the current writing mode combination have been finished.
      // Continue the tests for the next writing mode combination, if any.
      let nextStyleForRoot;
      let nextStyleForTarget;
      nextStyleForTarget = getNextWritingModeStyle(aStyleForTarget);
      if (nextStyleForTarget) {
        nextStyleForRoot = aStyleForRoot;
      } else {
        nextStyleForRoot = getNextWritingModeStyle(aStyleForRoot);
        if (!nextStyleForRoot) {
          // All writing mode combinations have been enumerated, so stop
          // recursively calling doTestAutoDirScroll2, and go ahead with the
          // subsequent tests.
          SimpleTest.executeSoon(aCallback);
          return;
        }
        nextStyleForTarget = getFirstWritingModeStyle();
      }
      doTestAutoDirScroll2(aSettings, aAutoDirTrait,
                           nextStyleForRoot, nextStyleForTarget,
                           aCallback);
      return;
    }

    gScrollableElement.scrollTop = kOrigScrollTop;
    gScrollableElement.scrollLeft = kOrigScrollLeft;

    var currentTest = kTests[currentTestIndex];
    description = "doTestAutoDirScroll(aSettings=" + aSettings.description + ", ";
    if (kHonoursRoot) {
      description += "{honoursRoot: true}), ";
    } else {
      description += "{honoursRoot: false}), ";
    }
    description += "root = " + styleDescForRoot + " ";
    description += "target = " + styleDescForTarget + " ";
    if (currentTest.adjusted) {
      description += "adjusted ";
    } else {
      description += "unadjusted ";
    }
    description += currentTest.description + ": ";
    if (currentTest.prepare) {
      currentTest.prepare(doTestCurrentScroll);
    } else {
      doTestCurrentScroll();
    }
  }

  function doTestCurrentScroll() {
    var currentTest = kTests[currentTestIndex];
    sendWheelAndWait(100, 100, currentTest.event, function () {
      if (currentTest.expected == kNoScroll) {
        is(gScrollableElement.scrollTop, kOrigScrollTop, description + "scrolled vertical");
        is(gScrollableElement.scrollLeft, kOrigScrollLeft, description + "scrolled horizontal");
      } else {
        // If auto-dir adjustment occurs, temporarily swap |isYReverted| and
        // |isXReverted|.
        if (currentTest.adjusted) {
          [isYReverted, isXReverted] = [isXReverted, isYReverted];
        }
        let scrollUp = !isYReverted ? (currentTest.expected & kScrollUp) :
                                      (currentTest.expected & kScrollDown);
        let scrollDown = !isYReverted ? (currentTest.expected & kScrollDown) :
                                        (currentTest.expected & kScrollUp);
        if (scrollUp) {
          ok(gScrollableElement.scrollTop < kOrigScrollTop,
             description + "not scrolled up, got " + gScrollableElement.scrollTop);
        } else if (scrollDown) {
          ok(gScrollableElement.scrollTop > kOrigScrollTop,
             description + "not scrolled down, got " + gScrollableElement.scrollTop);
        } else {
          is(gScrollableElement.scrollTop, kOrigScrollTop,
             description + "scrolled vertical");
        }
        var scrollLeft = !isXReverted ? (currentTest.expected & kScrollLeft) :
                                        (currentTest.expected & kScrollRight);
        var scrollRight = !isXReverted ? (currentTest.expected & kScrollRight) :
                                         (currentTest.expected & kScrollLeft);
        if (scrollLeft) {
          ok(gScrollableElement.scrollLeft < kOrigScrollLeft,
             description + "not scrolled to left, got " + gScrollableElement.scrollLeft);
        } else if (scrollRight) {
          ok(gScrollableElement.scrollLeft > kOrigScrollLeft,
             description + "not scrolled to right, got " + gScrollableElement.scrollLeft);
        } else {
          is(gScrollableElement.scrollLeft, kOrigScrollLeft,
             description + "scrolled horizontal");
        }
        // |isYReverted| and |isXReverted| have been temporarily swaped for
        // auto-dir adjustment, restore them.
        if (currentTest.adjusted) {
          [isYReverted, isXReverted] = [isXReverted, isYReverted];
        }
      }
      if (currentTest.cleanup) {
        currentTest.cleanup(nextStep);
      } else {
        nextStep();
      }

      function nextStep() {
        winUtils.advanceTimeAndRefresh(100);
        doNextTest();
      }
    });
  }
  doNextTest();
}

function doTestZoom(aSettings, aCallback)
{
  if ((aSettings.deltaMultiplierX != 1.0  && aSettings.deltaMultiplierX != -1.0) ||
      (aSettings.deltaMultiplierY != 1.0  && aSettings.deltaMultiplierY != -1.0)) {
    todo(false, "doTestZoom doesn't support to test with aSettings=" + aSettings.description);
    SimpleTest.executeSoon(aCallback);
    return;
  }

  const kNone     = 0x00;
  const kPositive = 0x01;
  const kNegative = 0x02;
  const kUseX     = 0x10;
  const kUseY     = 0x20;
  const kTests = [
    { description: "by vertical/positive pixel event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/positive pixel event when its lineOrPageDeltaY is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseY },
    { description: "by vertical/negative pixel event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/negative pixel event when its lineOrPageDeltaY is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -8.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseY },
    { description: "by horizotal/positive pixel event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/positive pixel event when its lineOrPageDeltaX is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseX },
    { description: "by horizotal/negative pixel event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/negative pixel event when its lineOrPageDeltaX is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -8.0, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseX },
    { description: "by z pixel event",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 16.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },

    { description: "by vertical/positive line event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/positive line event when its lineOrPageDeltaY is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseY },
    { description: "by vertical/negative line event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/negative line event when its lineOrPageDeltaY is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseY },
    { description: "by horizotal/positive line event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/positive line event when its lineOrPageDeltaX is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseX },
    { description: "by horizotal/negative line event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/negative line event when its lineOrPageDeltaX is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseX },
    { description: "by z line event",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },

    { description: "by vertical/positive page event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/positive page event when its lineOrPageDeltaY is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseY },
    { description: "by vertical/negative page event when its lineOrPageDeltaY is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by vertical/negative page event when its lineOrPageDeltaY is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -0.5, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseY },
    { description: "by horizotal/positive page event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/positive page event when its lineOrPageDeltaX is 1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kPositive | kUseX },
    { description: "by horizotal/negative page event when its lineOrPageDeltaX is 0",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
    { description: "by horizotal/negative page event when its lineOrPageDeltaX is -1",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -0.5, deltaY: 0.0, deltaZ: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNegative | kUseX },
    { description: "by z page event",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 0.0, deltaZ: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0,
               expectedOverflowDeltaX: 0, expectedOverflowDeltaY: 0 },
      expected: kNone },
  ];

  var description, currentTest;
  var currentTestIndex = -1;
  var isXReverted = (aSettings.deltaMultiplierX < 0);
  var isYReverted = (aSettings.deltaMultiplierY < 0);

  function doNextTest() {
    if (++currentTestIndex >= kTests.length) {
      SimpleTest.executeSoon(aCallback);
      return;
    }

    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;

    currentTest = kTests[currentTestIndex];
    description = "doTestZoom(aSettings=" + aSettings.description + "), ";
    if (currentTest.expected == kNone) {
      description += "Shouldn't ";
    } else {
      description += "Should ";
    }
    description += "zoom " + currentTest.description + ": ";

    var event = currentTest.event;
    event.ctrlKey  = true;

    // NOTE: Zooming might change scrollTop and scrollLeft by rounding fraction.
    //       This test assume that zoom happens synchronously and scrolling
    //       happens asynchronously.
    var scrollTop = gScrollableElement.scrollTop;
    var scrollLeft = gScrollableElement.scrollLeft;

    fullZoomChangePromise = new Promise(resolve => {
      if (currentTest.expected & (kNegative | kPositive)) {
        // Zoom causes a resize of the viewport.
        window.addEventListener("resize", function onResize() {
          if (SpecialPowers.getFullZoom(window) != 1) {
            window.removeEventListener("resize", onResize);
            setTimeout(() => resolve(), 0);
          }
        });
      } else {
        resolve();
      }
    });

    sendWheelAndWait(10, 10, event, function () {
      is(gScrollableElement.scrollTop, scrollTop, description + "scrolled vertical");
      is(gScrollableElement.scrollLeft, scrollLeft, description + "scrolled horizontal");

      fullZoomChangePromise.then(() => {
        // When input event prioritization is enabled, the wheel event may be
        // dispatched to the content process before the message 'FullZoom' to
        // zoom in/out. Waiting for the event 'FullZoomChange' and then check
        // the result.
        if (!(currentTest.expected & (kNegative | kPositive))) {
          is(SpecialPowers.getFullZoom(window), 1.0, description + "zoomed");
        } else {
          var isReverted = (currentTest.expected & kUseX) ? isXReverted :
                           (currentTest.expected & kUseY) ? isYReverted : false;
          if ((!isReverted && (currentTest.expected & kNegative)) ||
              (isReverted && (currentTest.expected & kPositive))) {
            ok(SpecialPowers.getFullZoom(window) > 1.0,
               description + "not zoomed in, got " + SpecialPowers.getFullZoom(window));
          } else {
            ok(SpecialPowers.getFullZoom(window) < 1.0,
               description + "not zoomed out, got " + SpecialPowers.getFullZoom(window));
          }
        }
        if (SpecialPowers.getFullZoom(window) != 1) {
          // Only synthesizes key event to reset zoom when necessary to avoid
          // triggering the next test before the key event is handled. In that
          // case, the key event may break the next test.
          synthesizeKey("0", { accelKey: true });
        }
        onZoomReset(function () {
          hitEventLoop(doNextTest, 20);
        });
      });
    });
  }
  doNextTest();
}

function doTestZoomedScroll(aCallback)
{
  var zoom = 1.0;
  function setFullZoom(aWindow, aZoom)
  {
    zoom = aZoom;
    SpecialPowers.setFullZoom(aWindow, aZoom);
  }

  function prepareTestZoomedPixelScroll()
  {
    // Reset zoom and store the scroll amount into the data.
    synthesizeKey("0", { accelKey: true });
    zoom = 1.0;
    onZoomReset(testZoomedPixelScroll);
  }

  function testZoomedPixelScroll()
  {
    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;
    // Ensure not to be in reflow.
    hitEventLoop(function () {
      function mousePixelScrollHandler(aEvent)
      {
        if (aEvent.axis == MouseScrollEvent.HORIZONTAL_AXIS) {
          is(aEvent.detail, 16,
             "doTestZoomedScroll: The detail of horizontal MozMousePixelScroll for pixel wheel event is wrong");
        } else if (aEvent.axis == MouseScrollEvent.VERTICAL_AXIS) {
          is(aEvent.detail, 16,
             "doTestZoomedScroll: The detail of vertical MozMousePixelScroll for pixel wheel event is wrong");
        } else {
          ok(false, "doTestZoomedScroll: The axis of MozMousePixelScroll for pixel wheel event is invalid, got " + aEvent.axis);
        }
      }
      function wheelHandler(aEvent)
      {
        is(aEvent.deltaX, 16.0 / zoom,
           "doTestZoomedScroll: The deltaX of wheel for pixel is wrong");
        is(aEvent.deltaY, 16.0 / zoom,
           "doTestZoomedScroll: The deltaY of wheel for pixel is wrong");
      }
      window.addEventListener("MozMousePixelScroll", mousePixelScrollHandler, true);
      window.addEventListener("wheel", wheelHandler, true);
      var event = {
        deltaMode: WheelEvent.DOM_DELTA_PIXEL,
        deltaX: 16.0,
        deltaY: 16.0,
        lineOrPageDeltaX: 0,
        lineOrPageDeltaY: 0
      };
      // wait scrolled actually.
      sendWheelAndWait(10, 10, event, function () {
        var scrolledX = gScrollableElement.scrollLeft;
        var scrolledY = gScrollableElement.scrollTop;
        ok(scrolledX > 1000,
           "doTestZoomedScroll: scrolledX must be larger than 1000 for pixel wheel event, got " + scrolledX);
        ok(scrolledY > 1000,
           "doTestZoomedScroll: scrolledY must be larger than 1000 for pixel wheel event, got " + scrolledY);

        // Zoom
        setFullZoom(window, 2.0);
        // Ensure not to be in reflow.
        hitEventLoop(function () {
          gScrollableElement.scrollTop = 1000;
          gScrollableElement.scrollLeft = 1000;
          var evt = {
            deltaMode: WheelEvent.DOM_DELTA_PIXEL,
            deltaX: 16.0,
            deltaY: 16.0,
            lineOrPageDeltaX: 0,
            lineOrPageDeltaY: 0
          };
          // wait scrolled actually.
          sendWheelAndWait(10, 10, evt, function () {
            ok(Math.abs(gScrollableElement.scrollLeft - (1000 + (scrolledX - 1000) / 2)) <= 1,
               "doTestZoomedScroll: zoomed horizontal scroll amount by pixel wheel event is different from normal, scrollLeft=" +
                 gScrollableElement.scrollLeft + ", scrolledX=" + scrolledX);
            ok(Math.abs(gScrollableElement.scrollTop - (1000 + (scrolledY - 1000) / 2)) <= 1,
               "doTestZoomedScroll: zoomed vertical scroll amount by pixel wheel event is different from normal, scrollTop=" +
                 gScrollableElement.scrollTop + ", scrolledY=" + scrolledY);
            window.removeEventListener("MozMousePixelScroll", mousePixelScrollHandler, true);
            window.removeEventListener("wheel", wheelHandler, true);

            synthesizeKey("0", { accelKey: true });
            onZoomReset(prepareTestZoomedLineScroll);
          });
        }, 20);
      });
    }, 20);
  }

  function prepareTestZoomedLineScroll()
  {
    // Reset zoom and store the scroll amount into the data.
    synthesizeKey("0", { accelKey: true });
    zoom = 1.0;
    onZoomReset(testZoomedLineScroll);
  }
  function testZoomedLineScroll()
  {
    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;
    // Ensure not to be in reflow.
    hitEventLoop(function () {
      var lineHeightX, lineHeightY;
      function handler(aEvent)
      {
        if (aEvent.axis == MouseScrollEvent.HORIZONTAL_AXIS) {
          if (lineHeightX == undefined) {
            lineHeightX = aEvent.detail;
          } else {
            ok(Math.abs(aEvent.detail - lineHeightX) <= 1,
               "doTestZoomedScroll: The detail of horizontal MozMousePixelScroll for line wheel event is wrong, aEvent.detail=" +
                 aEvent.detail + ", lineHeightX=" + lineHeightX);
          }
        } else if (aEvent.axis == MouseScrollEvent.VERTICAL_AXIS) {
          if (lineHeightY == undefined) {
            lineHeightY = aEvent.detail;
          } else {
            ok(Math.abs(aEvent.detail - lineHeightY) <= 1,
               "doTestZoomedScroll: The detail of vertical MozMousePixelScroll for line wheel event is wrong, aEvent.detail=" +
                 aEvent.detail + ", lineHeightY=" + lineHeightY);
          }
        } else {
          ok(false, "doTestZoomedScroll: The axis of MozMousePixelScroll for line wheel event is invalid, got " + aEvent.axis);
        }
      }
      window.addEventListener("MozMousePixelScroll", handler, true);
      var event = {
        deltaMode: WheelEvent.DOM_DELTA_LINE,
        deltaX: 1.0,
        deltaY: 1.0,
        lineOrPageDeltaX: 1,
        lineOrPageDeltaY: 1
      };
      // wait scrolled actually.
      sendWheelAndWait(10, 10, event, function () {
        var scrolledX = gScrollableElement.scrollLeft;
        var scrolledY = gScrollableElement.scrollTop;
        ok(scrolledX > 1000,
           "doTestZoomedScroll: scrolledX must be larger than 1000 for line wheel event, got " + scrolledX);
        ok(scrolledY > 1000,
           "doTestZoomedScroll: scrolledY must be larger than 1000 for line wheel event, got " + scrolledY);

        // Zoom
        setFullZoom(window, 2.0);
        // Ensure not to be in reflow.
        hitEventLoop(function () {
          gScrollableElement.scrollTop = 1000;
          gScrollableElement.scrollLeft = 1000;
          var evt = {
            deltaMode: WheelEvent.DOM_DELTA_LINE,
            deltaX: 1.0,
            deltaY: 1.0,
            lineOrPageDeltaX: 1,
            lineOrPageDeltaY: 1
          };
          // wait scrolled actually.
          sendWheelAndWait(10, 10, evt, function () {
            ok(Math.abs(gScrollableElement.scrollLeft - scrolledX) <= 1,
               "doTestZoomedScroll: zoomed horizontal scroll amount by line wheel event is different from normal, scrollLeft=" +
                 gScrollableElement.scrollLeft + ", scrolledX=" + scrolledX);
            ok(Math.abs(gScrollableElement.scrollTop - scrolledY) <= 1,
               "doTestZoomedScroll: zoomed vertical scroll amount by line wheel event is different from normal, scrollTop=" +
                 gScrollableElement.scrollTop + ", scrolledY=" + scrolledY);

            window.removeEventListener("MozMousePixelScroll", handler, true);

            synthesizeKey("0", { accelKey: true });
            onZoomReset(aCallback);
          });
        }, 20);
      });
    }, 20);
  }

  // XXX It's too difficult to test page scroll because the page scroll amount
  //     is computed by complex logic.

  prepareTestZoomedPixelScroll();
}

function doTestWholeScroll(aCallback)
{
  SpecialPowers.pushPrefEnv({"set": [
    ["mousewheel.default.delta_multiplier_x", 999999],
    ["mousewheel.default.delta_multiplier_y", 999999]]},
    function() { doTestWholeScroll2(aCallback); });
}

function doTestWholeScroll2(aCallback)
{
  const kTests = [
    { description: "try whole-scroll to top (line)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to top when scrollTop is already top-most (line)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom (line)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom when scrollTop is already bottom-most (line)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to left (line)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to left when scrollLeft is already left-most (line)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: -1.0, deltaY: 0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to right (line)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },
    { description: "try whole-scroll to right when scrollLeft is already right-most (line)",
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },


    { description: "try whole-scroll to top (pixel)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to top when scrollTop is already top-most (pixel)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom (pixel)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0.0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom when scrollTop is already bottom-most (pixel)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to left (pixel)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -1.0, deltaY: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to left when scrollLeft is already left-most (pixel)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: -1.0, deltaY: 0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to right (pixel)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },
    { description: "try whole-scroll to right when scrollLeft is already right-most (pixel)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PIXEL,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },


    { description: "try whole-scroll to top (page)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to top when scrollTop is already top-most (page)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: -1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: -1 },
      expectedScrollTop: 0,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom (page)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0.0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to bottom when scrollTop is already bottom-most (page)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 0, deltaY: 1.0,
               lineOrPageDeltaX: 0, lineOrPageDeltaY: 1 },
      expectedScrollTop: gScrollableElement.scrollTopMax,
      expectedScrollLeft: 1000
    },
    { description: "try whole-scroll to left (page)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: 0.0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to left when scrollLeft is already left-most (page)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: -1.0, deltaY: 0,
               lineOrPageDeltaX: -1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: 0
    },
    { description: "try whole-scroll to right (page)",
      prepare () {
        gScrollableElement.scrollTop = 1000;
        gScrollableElement.scrollLeft = 1000;
      },
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },
    { description: "try whole-scroll to right when scrollLeft is already right-most (page)",
      event: { deltaMode: WheelEvent.DOM_DELTA_PAGE,
               deltaX: 1.0, deltaY: 0.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 0 },
      expectedScrollTop: 1000,
      expectedScrollLeft: gScrollableElement.scrollLeftMax
    },
  ];

  var index = 0;

  function doIt()
  {
    const kTest = kTests[index];
    if (kTest.prepare) {
      kTest.prepare();
    }
    sendWheelAndWait(10, 10, kTest.event, function () {
      is(gScrollableElement.scrollTop, kTest.expectedScrollTop,
         "doTestWholeScroll, " + kTest.description + ": unexpected scrollTop");
      is(gScrollableElement.scrollLeft, kTest.expectedScrollLeft,
         "doTestWholeScroll, " + kTest.description + ": unexpected scrollLeft");
      if (++index == kTests.length) {
        SimpleTest.executeSoon(aCallback);
      } else {
        doIt();
      }
    });
  }
  doIt();
}

function doTestActionOverride(aCallback)
{
  const kNoScroll    = 0x00;
  const kScrollUp    = 0x01;
  const kScrollDown  = 0x02;
  const kScrollLeft  = 0x04;
  const kScrollRight = 0x08;

  const kTests = [
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 1.0, deltaY: 0.5,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionScroll, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kScrollDown | kScrollRight
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNoOverride,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXNone,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
    { action: kDefaultActionNone, override_x: kDefaultActionOverrideXScroll,
      event: { deltaMode: WheelEvent.DOM_DELTA_LINE,
               deltaX: 0.5, deltaY: 1.0,
               lineOrPageDeltaX: 1, lineOrPageDeltaY: 1 },
      expected: kNoScroll
    },
  ];

  var index = 0;

  function doIt()
  {
    const kTest = kTests[index];
    SpecialPowers.pushPrefEnv({"set": [
      ["mousewheel.default.action", kTest.action],
      ["mousewheel.default.action.override_x", kTest.override_x]]},
      doIt2
    );
  }

  function doIt2()
  {
    const kTest = kTests[index];
    description = "doTestActionOverride(action=" + kTest.action + ", " +
                  "override_x=" + kTest.override_x + ", " +
                  "deltaX=" + kTest.event.deltaX + ", " +
                  "deltaY=" + kTest.event.deltaY + "): ";
    gScrollableElement.scrollTop = 1000;
    gScrollableElement.scrollLeft = 1000;
    sendWheelAndWait(10, 10, kTest.event, function () {
      if (kTest.expected & kScrollUp) {
        ok(gScrollableElement.scrollTop < 1000, description + "not scrolled up, got " + gScrollableElement.scrollTop);
      } else if (kTest.expected & kScrollDown) {
        ok(gScrollableElement.scrollTop > 1000, description + "not scrolled down, got " + gScrollableElement.scrollTop);
      } else {
        is(gScrollableElement.scrollTop, 1000, description + "scrolled vertical");
      }
      if (kTest.expected & kScrollLeft) {
        ok(gScrollableElement.scrollLeft < 1000, description + "not scrolled to left, got " + gScrollableElement.scrollLeft);
      } else if (kTest.expected & kScrollRight) {
        ok(gScrollableElement.scrollLeft > 1000, description + "not scrolled to right, got " + gScrollableElement.scrollLeft);
      } else {
        is(gScrollableElement.scrollLeft, 1000, description + "scrolled horizontal");
      }
      if (++index == kTests.length) {
        SimpleTest.executeSoon(aCallback);
      } else {
        doIt();
      }
    });
  }
  doIt();
}

function runTests()
{
  SpecialPowers.pushPrefEnv({"set": [
    ["test.events.async.enabled", true],
    ["general.smoothScroll", false],
    ["mousewheel.default.action", kDefaultActionScroll],
    ["mousewheel.default.action.override_x", kDefaultActionOverrideXNoOverride],
    ["mousewheel.with_shift.action", kDefaultActionHorizontalizedScroll],
    ["mousewheel.with_shift.action.override_x", kDefaultActionOverrideXNoOverride],
    ["mousewheel.with_control.action", kDefaultActionZoom],
    ["mousewheel.with_control.action.override_x", kDefaultActionOverrideXNoOverride],
    ["mousewheel.with_alt.action", kDefaultActionHistory],
    ["mousewheel.with_alt.action.override_x", kDefaultActionOverrideXNoOverride]]},
    runTests2);
}

function runTests2()
{
  const kSettings = [
    { description: "all delta values are not customized",
      deltaMultiplierX:  1.0, deltaMultiplierY:  1.0, deltaMultiplierZ:  1.0 },
    { description: "deltaX is reverted",
      deltaMultiplierX: -1.0, deltaMultiplierY:  1.0, deltaMultiplierZ:  1.0 },
    { description: "deltaY is reverted",
      deltaMultiplierX:  1.0, deltaMultiplierY: -1.0, deltaMultiplierZ:  1.0 },
    // Unless you really don't care a snap on time-consuming testing or a strict
    // criteria is required for testing, it is strongly recommeneded that you
    // comment the unrealistic case out.
    //{ description: "deltaZ is reverted",
    //  deltaMultiplierX:  1.0, deltaMultiplierY:  1.0, deltaMultiplierZ: -1.0 },*/
    { description: "deltaX is 2.0",
      deltaMultiplierX:  2.0, deltaMultiplierY:  1.0, deltaMultiplierZ:  1.0 },
    { description: "deltaY is 2.0",
      deltaMultiplierX:  1.0, deltaMultiplierY:  2.0, deltaMultiplierZ:  1.0 },
    // Unless you really don't care a snap on time-consuming testing or a strict
    // criteria is required for testing, it is strongly recommeneded that you
    // comment the unrealistic case out.
    //{ description: "deltaZ is 2.0",
    //  deltaMultiplierX:  1.0, deltaMultiplierY:  1.0, deltaMultiplierZ:  2.0 },
    //{ description: "deltaX is -2.0",
    //  deltaMultiplierX: -2.0, deltaMultiplierY:  1.0, deltaMultiplierZ:  1.0 },
    //{ description: "deltaY is -2.0",
    //  deltaMultiplierX:  1.0, deltaMultiplierY: -2.0, deltaMultiplierZ:  1.0 },
    //{ description: "deltaZ is -2.0",
    //  deltaMultiplierX:  1.0, deltaMultiplierY:  1.0, deltaMultiplierZ: -2.0 },
  ];

  var index = 0;

  function doTest() {
    setDeltaMultiplierSettings(kSettings[index], function () {
      doTestScroll(kSettings[index], function () {
        doTestAutoDirScroll(kSettings[index], {honoursRoot: false}, function () {
          doTestAutoDirScroll(kSettings[index], {honoursRoot: true}, function () {
            doTestHorizontalizedScroll(kSettings[index], function() {
              doTestZoom(kSettings[index], function() {
                if (++index == kSettings.length) {
                  setDeltaMultiplierSettings(kSettings[0], function() {
                    doTestZoomedScroll(function() {
                      doTestWholeScroll(function() {
                        doTestActionOverride(function() {
                          finishTests();
                        });
                      });
                    });
                  });
                } else {
                  doTest();
                }
              });
            });
          });
        });
      });
    });
  }
  doTest();
}

function finishTests()
{
  winUtils.restoreNormalRefresh();

  window.opener.finish();
}

</script>
</pre>
</body>
</html>
