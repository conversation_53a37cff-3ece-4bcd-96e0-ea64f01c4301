<!DOCTYPE html>
<script src="/tests/SimpleTest/paint_listener.js"></script>
<script src="/tests/gfx/layers/apz/test/mochitest/apz_test_utils.js"></script>
<style>
html, body {
  height: 100%;
  margin: 0px;
  padding: 0px;
}
</style>
<div style="width:100%;height:100%;background-color:red;"></div>
<script>
  document.querySelector("div").addEventListener("click", event => {
    parent.postMessage({ screenX: event.screenX,
                         screenY: event.screenY,
                         clientX: event.clientX,
                         clientY: event.clientY }, "*");
  });
  window.onload = async () => {
    await promiseApzFlushedRepaints();
    parent.postMessage("ready", "*");
  }
</script>
