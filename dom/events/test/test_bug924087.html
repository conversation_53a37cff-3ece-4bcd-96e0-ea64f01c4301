<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=924087
-->
<head>
  <title>Test for Bug 924087</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<div contenteditable><a id="editable" href="#">editable link</a></div>
<a id="noneditable" href="#">non-editable link</a>
<input inputmode="none">
<textarea inputmode="none"></textarea>
<pre id="test">
<script type="application/javascript">

/** Test for Bug 924087 **/
SimpleTest.waitForExplicitFinish();
SimpleTest.waitForFocus(function() {
  var editable = document.querySelector("#editable");
  var noneditable = document.querySelector("#noneditable");
  var input = document.querySelector("input");
  var textarea = document.querySelector("textarea");
  synthesizeMouseAtCenter(noneditable, {type:"mousedown"});
  is(document.querySelector(":active:link"), noneditable, "Normal links should become :active");
  synthesizeMouseAtCenter(noneditable, {type:"mouseup"});
  synthesizeMouseAtCenter(editable, {type:"mousedown"});
  is(document.querySelector(":active:link"), null, "Editable links should not become :active");
  synthesizeMouseAtCenter(editable, {type:"mouseup"});
  [input, textarea].forEach(textbox => {
    synthesizeMouseAtCenter(textbox, {type:"mousedown"});
    is(document.querySelector(textbox.localName + ":active"), textbox, "The textbox should become :active");
    synthesizeMouseAtCenter(textbox, {type:"mouseup"});
  });
  SimpleTest.finish();
});

</script>
</pre>

</body>
</html>
