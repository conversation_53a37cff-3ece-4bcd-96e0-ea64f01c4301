<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css" type="text/css"?>
<window xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        xmlns:html="http://www.w3.org/1999/xhtml"
        title="Test for Bug 1697769">
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1697769
-->
<script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>
<script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"></script>

<hbox height="300" align="center">
<vbox width="30" align="center">
<scrollbar id="scrollbar" orient="vertical" curpos="0" maxpos="500" style="height: 300px" />
</vbox>
</hbox>

<body id="html_body" xmlns="http://www.w3.org/1999/xhtml">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1697769">Mozilla Bug 1697769</a>
<p id="display"></p>
<pre id="test"></pre>
</body>

<script class="testbody" type="application/javascript">
<![CDATA[

let scrollbar;
let scrollbarThumb;

add_setup(async function() {
  scrollbar = document.getElementById("scrollbar");
  scrollbarThumb = null;
  for (let child of SpecialPowers.InspectorUtils.getChildrenForNode(scrollbar, true, false)) {
    if (child.nodeName === "slider") {
      scrollbarThumb = SpecialPowers.unwrap(child.childNodes[0]);
    }
  }

  ok(scrollbarThumb, "Should find thumb");
  is(scrollbarThumb.nodeName, "thumb", "Should find thumb");

  // Wait for finishing reflow...
  await new Promise(resolve => SimpleTest.executeSoon(resolve));
});

add_task(function testScrollbarMouse() {
  // Simulate mouse actions.
  synthesizeMouseAtCenter(scrollbarThumb, {type: "mousedown", "inputSource": MouseEvent.MOZ_SOURCE_TOUCH});
  is(scrollbar.getAttribute("curpos"), "0", "scrollbar thumb has been moved already");

  synthesizeMouseAtCenter(scrollbar, {type: "mousemove", "inputSource": MouseEvent.MOZ_SOURCE_TOUCH});
  ok(scrollbar.getAttribute("curpos") > 0, "scrollbar thumb hasn't been dragged");

  synthesizeMouseAtCenter(scrollbar, {type: "mouseup", "inputSource": MouseEvent.MOZ_SOURCE_TOUCH});
});

add_task(function testScrollbarTouch() {
  // reset scrollbar position
  scrollbar.setAttribute("curpos", "0");

  // Simulate touch actions.
  synthesizeTouchAtCenter(scrollbarThumb, {type: "touchstart"});
  is(scrollbar.getAttribute("curpos"), "0", "scrollbar thumb has been moved already");

  synthesizeTouchAtCenter(scrollbar, {type: "touchmove"});
  ok(scrollbar.getAttribute("curpos") > 0, "scrollbar thumb hasn't been dragged");

  synthesizeTouchAtCenter(scrollbar, {type: "touchend"});
});

]]>
</script>
</window>
