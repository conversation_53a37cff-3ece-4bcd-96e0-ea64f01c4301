<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=968148
-->
<head>
  <title>Test for Bug 968148</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="text/javascript">
    function testWithoutImplicitPointerCapture() {
      var iframe = document.getElementById("testFrame");
      iframe.src = "bug968148_inner.html";
    }

    function testWithImplicitPointerCapture() {
      var iframe = document.getElementById("testFrame");
      iframe.src = "bug968148_inner2.html";
    }

    function runTest() {
      SimpleTest.waitForExplicitFinish();
      window.addEventListener("message", (event) => {
        if (event.data == "finishTest") {
          SimpleTest.finish();
        } else if (event.data == "run next") {
          SpecialPowers.pushPrefEnv({
            "set": [
              ["dom.w3c_pointer_events.implicit_capture", true]
            ]
          }, testWithImplicitPointerCapture);
        }
      });

      SpecialPowers.pushPrefEnv({
        "set": [
          ["dom.w3c_pointer_events.implicit_capture", false]
        ]
      }, testWithoutImplicitPointerCapture);
    }
  </script>
</head>
<body onload="runTest();">
  <iframe id="testFrame" height="500" width="500"></iframe>
</body>

