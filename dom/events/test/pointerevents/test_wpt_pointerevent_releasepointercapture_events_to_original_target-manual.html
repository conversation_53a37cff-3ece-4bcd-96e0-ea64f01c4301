<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1000870
-->
  <head>
    <meta charset="utf-8">
    <title>Test for Bug 1000870</title>
    <meta name="author" content="<PERSON><PERSON><PERSON>" />
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <script src="/tests/SimpleTest/EventUtils.js"></script>
    <script type="text/javascript" src="mochitest_support_external.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
    <script type="text/javascript">
      SimpleTest.waitForExplicitFinish();
      function startTest() {
        runTestInNewWindow("wpt/pointerevent_releasepointercapture_events_to_original_target-manual.html");
      }
      function executeTest(int_win) {
        // Synthesize mouse events to run this test.
        sendMouseEvent(int_win, "target0", "mousemove");
        sendMouseEvent(int_win, "target0", "mousedown");
        sendMouseEvent(int_win, "target0", "mousemove", {buttons: 1});
        sendMouseEvent(int_win, "target0", "mousemove", {buttons: 1});
        sendMouseEvent(int_win, "target0", "mouseup");

        window.addEventListener("message", function(aEvent) {
          if (aEvent.data == "Test Touch") {
            // Synthesize touch events to run this test.
            sendTouchEvent(int_win, "target0", "touchstart", {offsetX: 10});
            sendTouchEvent(int_win, "target0", "touchmove", {offsetX: 11});
            sendTouchEvent(int_win, "target0", "touchend", {offsetX: 11});
            window.postMessage("Test Pen", "*");
          } else if (aEvent.data == "Test Pen") {
            // Synthesize pen events to run this test.
            sendMouseEvent(int_win, "target0", "mousemove", {inputSource:MouseEvent.MOZ_SOURCE_PEN});
            sendMouseEvent(int_win, "target0", "mousedown", {inputSource:MouseEvent.MOZ_SOURCE_PEN});
            sendMouseEvent(int_win, "target0", "mousemove", {inputSource:MouseEvent.MOZ_SOURCE_PEN, buttons: 1});
            sendMouseEvent(int_win, "target0", "mousemove", {inputSource:MouseEvent.MOZ_SOURCE_PEN, buttons: 1});
            sendMouseEvent(int_win, "target0", "mouseup", {inputSource:MouseEvent.MOZ_SOURCE_PEN});
          }
        });
        window.postMessage("Test Touch", "*");
      }
    </script>
  </head>
  <body>
  </body>
</html>
