<!doctype html>
<html>
  <head>
    <title>Pointer Event: Event sequence at implicit release on click</title>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type"/>
    <link rel="author" title="Google" href="http://www.google.com "/>
    <meta name="assert" content="When a captured pointer is implicitly released after a click, the boundary events should follow the lostpointercapture event."/>
    <link rel="stylesheet" type="text/css" href="pointerevent_styles.css">
    <script src="/resources/testharness.js"></script>
    <script src="/resources/testharnessreport.js"></script>
    <script type="text/javascript" src="pointerevent_support.js"></script>
    <script type="text/javascript">
      var detected_pointertypes = {};
      var event_log = [];
      var start_logging = false;

      function resetTestState() {
        detected_eventTypes = {};
        event_log = [];
        start_logging = false;
      }

      function run() {
        var test_pointer_event = setup_pointerevent_test("Event sequence at implicit release on click", ALL_POINTERS);

        on_event(document.getElementById("done"), "click", function() {
          test_pointer_event.step(function () {
            var expected_events = "pointerup, lostpointercapture, pointerout, pointerleave";
            assert_equals(event_log.join(", "), expected_events);
          });
          test_pointer_event.done();
        });

        var target = document.getElementById("target");

        All_Pointer_Events.forEach(function(eventName) {
          on_event(target, eventName, function (event) {
            detected_pointertypes[event.pointerType] = true;

            if (event.type == "pointerdown") {
              event.target.setPointerCapture(event.pointerId);

            } else if (event.type == "gotpointercapture") {
              start_logging = true;

            } else if (event.type != "pointermove" && start_logging) {
              event_log.push(event.type);
            }
          });
        });
      }
    </script>
    <style>
      #target {
        margin: 20px;
        background-color: black;
      }

      #done {
        margin: 20px;
        background-color: green;
      }
    </style>
  </head>
  <body onload="run()">
    <h1>Pointer Event: Event sequence at implicit release on click<h1>
    <h2 id="pointerTypeDescription"></h2>
    <h4>
      When a captured pointer is implicitly released after a click, the boundary events should follow the lostpointercapture event.
    </h4>
    <ol>
      <li>Click or tap on Black.</li>
      <li>Click or tap on Green.</li>
    </ol>
    <div id="target"></div>
    <div id="done"></div>
    <div id="complete-notice">
      <p>The following pointer types were detected: <span id="pointertype-log"></span>.</p>
      <p>The following events were logged: <span id="event-log"></span>.</p>
    </div>
    <div id="log"></div>
  </body>
</html>
