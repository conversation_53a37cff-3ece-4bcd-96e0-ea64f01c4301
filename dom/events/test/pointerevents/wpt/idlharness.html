<!doctype html>
<meta charset=utf-8>
<title>idlharness test</title>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src="/resources/WebIDLParser.js"></script>
<script src="/resources/idlharness.js"></script>

<pre id='untested_idl' style='display:none'>
[PrimaryGlobal]
interface Window {
};

[TreatNonObjectAsNull]
callback EventHandlerNonNull = any (Event event);
typedef EventHandlerNonNull? EventHandler;

[NoInterfaceObject]
interface GlobalEventHandlers {
};
Window implements GlobalEventHandlers;

interface Navigator {
};

interface Element {
};

interface HTMLElement : Element {
};
HTMLElement implements GlobalEventHandlers;

interface Document {
};
Document implements GlobalEventHandlers;

interface MouseEvent {
};

</pre>

<pre id='idl'>
dictionary PointerEventInit : MouseEventInit {
    long      pointerId = 0;
    double    width = 1;
    double    height = 1;
    float     pressure = 0;
    float     tangentialPressure = 0;
    long      tiltX = 0;
    long      tiltY = 0;
    long      twist = 0;
    DOMString pointerType = "";
    boolean   isPrimary = false;
};

[Constructor(DOMString type, optional PointerEventInit eventInitDict)]
interface PointerEvent : MouseEvent {
    readonly attribute long      pointerId;
    readonly attribute double    width;
    readonly attribute double    height;
    readonly attribute float     pressure;
    readonly attribute float     tangentialPressure;
    readonly attribute long      tiltX;
    readonly attribute long      tiltY;
    readonly attribute long      twist;
    readonly attribute DOMString pointerType;
    readonly attribute boolean   isPrimary;
};

partial interface Element {
    void    setPointerCapture(long pointerId);
    void    releasePointerCapture(long pointerId);
    boolean hasPointerCapture(long pointerId);
};

partial interface GlobalEventHandlers {
    attribute EventHandler ongotpointercapture;
    attribute EventHandler onlostpointercapture;
    attribute EventHandler onpointerdown;
    attribute EventHandler onpointermove;
    attribute EventHandler onpointerup;
    attribute EventHandler onpointercancel;
    attribute EventHandler onpointerover;
    attribute EventHandler onpointerout;
    attribute EventHandler onpointerenter;
    attribute EventHandler onpointerleave;
};

partial interface Navigator {
    readonly attribute long maxTouchPoints;
};
</pre>
<script>
  var idl_array = new IdlArray();
  idl_array.add_untested_idls(document.getElementById("untested_idl").textContent);
  idl_array.add_idls(document.getElementById("idl").textContent);

  // Note that I don't bother including Document here because there are still
  // a bunch of differences between browsers around Document vs HTMLDocument.
  idl_array.add_objects({
    Window: ["window"],
    Navigator: ["navigator"]});
  idl_array.test();
</script>
