<!doctype html>
<html>
    <head>
        <title>touch-action: pan-left</title>
        <meta name="assert" content="TA15.3 - With `touch-action: pan-left` on a swiped or click/dragged element, only panning on the x-axis left direction should be possible.">
        <meta name="viewport" content="width=device-width">
        <link rel="stylesheet" type="text/css" href="pointerevent_styles.css">
        <script src="/resources/testharness.js"></script>
        <script src="/resources/testharnessreport.js"></script>
        <script src="pointerevent_support.js"></script>
        <style>
            #target0 {
            width: 700px;
            height: 430px;
            touch-action: pan-left;
            }
        </style>
    </head>
    <body onload="run()">
        <h1>Pointer Events touch-action attribute support</h1>
        <h4 id="desc">Test Description: Try to scroll element DOWN (drag up), then RIGHT (drag left), then LEFT (drag right). Tap Complete button under the rectangle when done. Expected: only pans in left direction.</h4>
        <p>Note: this test is for touch-devices only</p>
        <div id="target0">
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
        </div>
        <input type="button" id="btnComplete" value="Complete test">
        <script type='text/javascript'>
            var detected_pointertypes = {};
            var test_touchaction = async_test("touch-action attribute test");
            add_completion_callback(showPointerTypes);

            function run() {
                var target0 = document.getElementById("target0");
                var btnComplete = document.getElementById("btnComplete");
                target0.scrollLeft = 200;

                var scrollListenerExecuted = false;
                target0.addEventListener("scroll", function(event) {
                    scrollListenerExecuted = true;
                    assert_less_than_equal(target0.scrollLeft, 200);
                });

                // Check if "touch-action: pan-left" attribute works properly
                //TA: 15.3
                on_event(btnComplete, 'click', function(event) {
                    detected_pointertypes[event.pointerType] = true;
                    test_touchaction.step(function() {
                        assert_true(scrollListenerExecuted, "scroll listener should have been executed by the end of the test");
                        assert_less_than(target0.scrollLeft, 200, "scroll x offset should be less than 200 in the end of the test");
                        assert_equals(target0.scrollTop, 0, "scroll y offset should be 0 in the end of the test");
                    });
                    test_touchaction.done();
                    updateDescriptionComplete();
                });
            }
        </script>
        <h1>touch-action: pan-left</h1>
        <div id="complete-notice">
            <p>The following pointer types were detected: <span id="pointertype-log"></span>.</p>
        </div>
        <div id="log"></div>
    </body>
</html>
