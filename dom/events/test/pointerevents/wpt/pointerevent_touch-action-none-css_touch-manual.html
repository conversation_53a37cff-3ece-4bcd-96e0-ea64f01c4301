<!doctype html>
<html>
    <head>
        <title>touch-action: none</title>
        <meta name="assert" content="TA15.2 - With `touch-action: none` on a swiped or click/dragged element, `pointerdown+(optional pointermove)+pointerup` must be dispatched.">
        <meta name="viewport" content="width=device-width">
        <link rel="stylesheet" type="text/css" href="pointerevent_styles.css">
        <script src="/resources/testharness.js"></script>
        <script src="/resources/testharnessreport.js"></script>
        <script src="pointerevent_support.js"></script>
        <style>
            #target0 {
            width: 700px;
            height: 430px;
            touch-action: none;
            }
        </style>
    </head>
    <body onload="run()">
        <h1>Pointer Events touch-action attribute support</h1>
        <h4 id="desc">Test Description: Try to scroll element DOWN then RIGHT. Tap Complete button under the rectangle when done. Expected: no panning/zooming/etc.</h4>
        <p>Note: this test is for touch-devices only</p>
        <div id="target0">
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diem
                nonummy nibh euismod tincidunt ut lacreet dolore magna aliguam erat volutpat.
                Ut wisis enim ad minim veniam, quis nostrud exerci tution ullamcorper suscipit
                lobortis nisl ut aliquip ex ea commodo consequat.
            </p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
            <p>Lorem ipsum dolor sit amet...</p>
        </div>
        <input type="button" id="btnComplete" value="Complete test">
        <script type='text/javascript'>
            var detected_pointertypes = {};

            var test_touchaction = async_test("touch-action attribute test");
            add_completion_callback(showPointerTypes);

            function run() {
                var target0 = document.getElementById("target0");
                var btnComplete = document.getElementById("btnComplete");

                // Check if "touch-action: none" attribute works properly
                //TA: 15.2
                on_event(btnComplete, 'click', function(event) {
                    detected_pointertypes[event.pointerType] = true;
                    test_touchaction.step(function() {
                        assert_equals(target0.scrollLeft, 0, "scroll x offset should be 0 in the end of the test");
                        assert_equals(target0.scrollTop, 0, "scroll y offset should be 0 in the end of the test");
                    });
                    test_touchaction.done();
                    updateDescriptionComplete();
                });

                on_event(target0, 'scroll', function(event) {
                    test_touchaction.step(failOnScroll, "scroll received while touch-action is none");
                });
            }
        </script>
        <h1>touch-action: none</h1>
        <div id="complete-notice">
            <p>The following pointer types were detected: <span id="pointertype-log"></span>.</p>
        </div>
        <div id="log"></div>
    </body>
</html>