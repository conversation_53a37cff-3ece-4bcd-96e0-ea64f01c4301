<!doctype html>
<html>
    <head>
        <title>pointerout</title>
        <meta name="viewport" content="width=device-width">
        <link rel="stylesheet" type="text/css" href="pointerevent_styles.css">
        <script src="/resources/testharness.js"></script>
        <script src="/resources/testharnessreport.js"></script>
        <!-- Additional helper script for common checks across event types -->
        <script type="text/javascript" src="pointerevent_support.js"></script>
    </head>
    <body onload="run()">
        <h2>pointerout</h2>
        <h4>Test Description: This test checks if pointerout event triggers for pen. Place your pen over the black rectangle and then pull the pen out of the digitizer's detectable range. </h4>
        <p>Note: this test is for devices that support hover - for pen only</p>
        <div id="target0"></div>
        <script>
            var test_pointerout = async_test("pointerout event received");
            // showPointerTypes is defined in pointerevent_support.js
            // Requirements: the callback function will reference the test_pointerEvent object and
            // will fail unless the async_test is created with the var name "test_pointerEvent".
            add_completion_callback(showPointerTypes);

            var eventTested = false;
            var isPointerupReceived = false;
            var detected_pointertypes = {};

            function run() {
                var target0 = document.getElementById("target0");

                // When a pen stylus leaves the hover range detectable by the digitizer the pointerout event must be dispatched. 
                // TA: 7.2
                on_event(target0, "pointerout", function (event) {
                    detected_pointertypes[event.pointerType] = true;
                    if(event.pointerType == 'pen') {
                        if (eventTested == false) {
                            eventTested = true;
                            test_pointerout.done();
                        }
                    }
                    else {
                        test_pointerout.step(function() {
                            assert_true(false,
                            "you have to use pen for this test");
                        }, "you have to use pen for this test");
                    }
                });
            }

        </script>
        <h1>Pointer Events pointerout tests</h1>
        <div id="complete-notice">
            <p>The following pointer types were detected: <span id="pointertype-log"></span>.</p>
        </div>
        <div id="log"></div>
    </body>
</html>