<!DOCTYPE html>
<html>
 <head>
  <meta charset="utf-8">
  <title>W3C pointerevents/*touch-action*.html tests in Mochitest form</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script type="application/javascript" src="apz_test_utils.js"></script>
  <script type="application/javascript" src="apz_test_native_event_utils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <script type="application/javascript">
var apz_touch_action_prefs = [
  // Dropping the touch slop to 0 makes the tests easier to write because
  // we can just do a one-pixel drag to get over the pan threshold rather
  // than having to hard-code some larger value.
  ["apz.touch_start_tolerance", "0.0"],
  // The touchstart from the drag can turn into a long-tap if the touch-move
  // events get held up. Try to prevent that by making long-taps require
  // a 10 second hold. Note that we also cannot enable chaos mode on this
  // test for this reason, since chaos mode can cause the long-press timer
  // to fire sooner than the pref dictates.
  ["ui.click_hold_context_menus.delay", 10000],
  // The subtests in this test do touch-drags to pan the page, but we don't
  // want those pans to turn into fling animations, so we increase the
  // fling-stop threshold velocity to absurdly high.
  ["apz.fling_stopped_threshold", Number.MAX_VALUE],
  // The helper_div_pan's div gets a displayport on scroll, but if the
  // test takes too long the displayport can expire before the new scroll
  // position is synced back to the main thread. So we disable displayport
  // expiry for these tests.
  ["apz.displayport_expiry_ms", 0],
];

function apzScriptInjector(name) {
  return async function(childWin) {
    childWin._ACTIVE_TEST_NAME = name;
    await injectScript('/tests/SimpleTest/paint_listener.js', childWin);
    await injectScript('../apz_test_utils.js', childWin);
    await injectScript('../apz_test_native_event_utils.js', childWin);
    await injectScript('../touch_action_helpers.js', childWin);
  };
}

// Each of these test names is turned into an entry in the |subtests| array
// below.
var testnames = [
  'pointerevent_touch-action-auto-css_touch-manual',
  'pointerevent_touch-action-button-test_touch-manual',
  // this one runs as a web-platform-test since it's not a manual test
  // 'pointerevent_touch-action-illegal',
  'pointerevent_touch-action-inherit_child-auto-child-none_touch-manual',
  'pointerevent_touch-action-inherit_child-none_touch-manual',
  'pointerevent_touch-action-inherit_child-pan-x-child-pan-x_touch-manual',
  'pointerevent_touch-action-inherit_child-pan-x-child-pan-y_touch-manual',
  'pointerevent_touch-action-inherit_highest-parent-none_touch-manual',
  'pointerevent_touch-action-inherit_parent-none_touch-manual',
  // the keyboard-manual and mouse-manual tests require simulating keyboard/
  // mouse input, rather than touch, so we're not going to do that here.
  //'pointerevent_touch-action-keyboard-manual',
  //'pointerevent_touch-action-mouse-manual',
  'pointerevent_touch-action-none-css_touch-manual',
  'pointerevent_touch-action-pan-x-css_touch-manual',
  'pointerevent_touch-action-pan-x-pan-y-pan-y_touch-manual',
  'pointerevent_touch-action-pan-x-pan-y_touch-manual',
  'pointerevent_touch-action-pan-y-css_touch-manual',
  // disable this one because of intermittent failures. see bug 1292134.
  // 'pointerevent_touch-action-span-test_touch-manual',
  'pointerevent_touch-action-svg-test_touch-manual',
  'pointerevent_touch-action-table-test_touch-manual',
  // this one runs as a web-platform-test since it's not a manual test
  //'pointerevent_touch-action-verification',
];

// Each entry in |subtests| is loaded in a new window. When loaded, it runs
// the function returned by apzScriptInjector, which injects some helper JS
// files into the vanilla unmodified W3C testcase, and simulates the necessary
// user input to run the test.
var subtests = [];
for (var name of testnames) {
  subtests.push({
    'file': 'wpt/' + name + '.html',
    'prefs': apz_touch_action_prefs,
    'onload': apzScriptInjector(name),
  });
}

if (isApzEnabled()) {
  SimpleTest.waitForExplicitFinish();
  window.onload = function() {
    runSubtestsSeriallyInFreshWindows(subtests)
    .then(SimpleTest.finish, SimpleTest.finish);
  };
}

  </script>
 </head>
 <body>
 </body>
</html>
