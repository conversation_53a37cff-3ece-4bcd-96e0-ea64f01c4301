<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1403055
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 1403055</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1403055">Mozilla Bug 1403055</a>
<p id="display"></p>
<div id="target0" style="width: 50px; height: 50px; background: green"></div>
<div id="target1" style="width: 50px; height: 50px; background: red"></div>
<div id="done" style="width: 50px; height: 50px; background: black"></div>
<script type="text/javascript">
/** Test for Bug 1403055 **/
SimpleTest.waitForExplicitFinish();

var target0 = window.document.getElementById("target0");
var target1 = window.document.getElementById("target1");
var done = window.document.getElementById("done");

function withoutImplicitlyPointerCaptureForTouch() {
  let target0_events = ["pointerover", "pointerenter", "pointerdown", "pointermove"];
  let target1_events = ["pointerover", "pointerenter", "pointermove", "pointercancel", "pointerout", "pointerleave"];

  target0_events.forEach((elem, index, arr) => {
    target0.addEventListener(elem, (event) => {
      is(event.type, target0_events[0], "receive " + event.type + " on target0");
      target0_events = target0_events.filter(item => item !== event.type);
    }, { once: true });
  });

  target1_events.forEach((elem, index, arr) => {
    target1.addEventListener(elem, (event) => {
      is(event.type, target1_events[0], "receive " + event.type + " on target1");
      target1_events = target1_events.filter(item => item !== event.type);
    }, { once: true });
  });

  done.addEventListener("mouseup", () => {
    ok(!target0_events.length, " should receive " + target0_events + " on target0");
    ok(!target1_events.length, " should receive " + target1_events + " on target1");
    SpecialPowers.pushPrefEnv({"set": [["dom.w3c_pointer_events.implicit_capture", true]]},
                              withImplicitlyPointerCaptureForTouch);
  }, {once : true});

  synthesizeTouch(target0, 5, 5, { type: "touchstart" });
  synthesizeTouch(target0, 6, 6, { type: "touchmove" });
  synthesizeTouch(target1, 5, 5, { type: "touchmove" });
  synthesizeTouch(target1, 5, 5, { type: "touchcancel" });
  synthesizeMouseAtCenter(done, { type: "mousedown" });
  synthesizeMouseAtCenter(done, { type: "mouseup" });
}

function withImplicitlyPointerCaptureForTouch() {
  let target0_events = ["pointerover", "pointerenter", "pointerdown", "pointermove", "pointercancel", "pointerout", "pointerleave"];

  target0_events.forEach((elem, index, arr) => {
    target0.addEventListener(elem, (event) => {
      is(event.type, target0_events[0], "receive " + event.type + " on target0");
      target0_events = target0_events.filter(item => item !== event.type);
    }, { once: true });
  });

  done.addEventListener("mouseup", () => {
    ok(!target0_events.length, " should receive " + target0_events + " on target0");
    SimpleTest.finish();
  }, {once : true});

  synthesizeTouch(target0, 5, 5, { type: "touchstart" });
  synthesizeTouch(target0, 5, 5, { type: "touchmove" });
  synthesizeTouch(target1, 5, 5, { type: "touchmove" });
  synthesizeTouch(target1, 5, 5, { type: "touchcancel" });
  synthesizeMouseAtCenter(done, { type: "mousedown" });
  synthesizeMouseAtCenter(done, { type: "mouseup" });
}

SimpleTest.waitForFocus(() => {
  SpecialPowers.pushPrefEnv({"set": [["dom.w3c_pointer_events.implicit_capture", false]]},
                            withoutImplicitlyPointerCaptureForTouch);
});

</script>
</body>
</html>
