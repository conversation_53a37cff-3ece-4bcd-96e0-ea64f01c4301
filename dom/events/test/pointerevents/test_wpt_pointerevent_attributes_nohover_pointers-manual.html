<!DOCTYPE HTML>
<html>
  <head>
    <meta charset="utf-8">
    <title>Test pointerevent attributes for non-hoverable pointers</title>
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <script src="/tests/SimpleTest/EventUtils.js"></script>
    <script type="text/javascript" src="mochitest_support_external.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
    <script type="text/javascript">
      SimpleTest.waitForExplicitFinish();
      function startTest() {
        runTestInNewWindow("wpt/pointerevent_attributes_nohover_pointers-manual.html");
      }
      function executeTest(int_win) {
        sendTouchEvent(int_win, "square1", "touchstart");
        sendTouchEvent(int_win, "square1", "touchend");
        let iframe = int_win.document.getElementById("innerFrame");
        sendTouchEvent(iframe.contentWindow, "square2", "touchstart");
        sendTouchEvent(iframe.contentWindow, "square2", "touchend");
      }
    </script>
  </head>
  <body>
  </body>
</html>
