<!DOCTYPE HTML>
<html>
  <head>
    <meta charset="utf-8">
    <title>W3C pointerevent_sequence_at_implicit_release_on_drag-manual.html in Mochitest form</title>
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <script src="/tests/SimpleTest/EventUtils.js"></script>
    <script type="text/javascript" src="mochitest_support_external.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
    <script type="text/javascript">
      SimpleTest.waitForExplicitFinish();
      function startTest() {
        runTestInNewWindow("wpt/pointerevent_sequence_at_implicit_release_on_drag-manual.html");
      }
      function executeTest(int_win) {
        sendTouchEvent(int_win, "target", "touchstart");
        sendTouchEvent(int_win, "target", "touchmove");
        sendTouchEvent(int_win, "target", "touchmove");
        sendTouchEvent(int_win, "target", "touchcancel");
        sendMouseEvent(int_win, "done", "mousedown");
        sendMouseEvent(int_win, "done", "mouseup");
      }
    </script>
  </head>
  <body>
  </body>
</html>
