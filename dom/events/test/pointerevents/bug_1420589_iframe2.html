<body>
	<script>
	    let touchEvents = ["touchstart", "touchmove", "touchend"];
	    let pointerEvents = ["pointerdown", "pointermove", "pointerup"];

	    touchEvents.forEach((event) => {
	        document.addEventListener(event, (e) => {
	            parent.postMessage("iframe2 " + e.type, "*");
	        }, { once: true });
	    });
	    pointerEvents.forEach((event) => {
	        document.addEventListener(event, (e) => {
	            parent.postMessage("iframe2 " + e.type, "*");
	        }, { once: true });
	    });
	</script>
</body>
