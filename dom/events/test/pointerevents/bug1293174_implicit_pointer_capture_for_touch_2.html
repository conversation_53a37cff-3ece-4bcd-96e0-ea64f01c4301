<!doctype html>
<html>
    <head>
        <title>Pointer Events properties tests</title>
        <meta name="viewport" content="width=device-width">
        <link rel="stylesheet" type="text/css" href="wpt/pointerevent_styles.css">
        <script src="/resources/testharness.js"></script>
        <!--script src="/resources/testharnessreport.js"></script-->
        <!-- Additional helper script for common checks across event types -->
        <script type="text/javascript" src="wpt/pointerevent_support.js"></script>
        <script>
          var detected_pointertypes = {};
          var test_pointerEvent = async_test("implicit pointer capture for touch");
          // showPointerTypes is defined in pointerevent_support.js
          // Requirements: the callback function will reference the test_pointerEvent object and
          // will fail unless the async_test is created with the var name "test_pointerEvent".
          add_completion_callback(showPointerTypes);

          function run() {
            let target0 = window.document.getElementById("target0");
            let target1 = window.document.getElementById("target1");

            on_event(target0, "pointerdown", function (event) {
              pointerdown_event = event;
              detected_pointertypes[event.pointerType] = true;
              assert_true(true, "target0 receives pointerdown");
            });

            on_event(target0, "pointermove", function (event) {
              assert_true(true, "target0 receives pointermove");
              assert_false(target0.hasPointerCapture(event.pointerId), "target0.hasPointerCapture should be false");
            });

            on_event(target0, "gotpointercapture", function (event) {
              assert_unreached("target0 should not receive gotpointercapture");
            });

            on_event(target0, "lostpointercapture", function (event) {
              assert_unreached("target0 should not receive lostpointercapture");
            });

            on_event(target1, "pointermove", function (event) {
              assert_true(true, "target1 receives pointermove");
              assert_false(target1.hasPointerCapture(event.pointerId), "target1.hasPointerCapture should be false");
            });

            on_event(target0, "pointerup", function (event) {
              assert_true(true, "target0 receives pointerup");
              test_pointerEvent.done();
            });
          }
        </script>
    </head>
    <body onload="run()">
        <h1>Pointer Events tests</h1>
        <div id="target0" style="width: 200px; height: 200px; background: green" touch-action:none></div>
        <div id="target1" style="width: 200px; height: 200px; background: green" touch-action:none></div>
        <div id="complete-notice">
            <p>The following pointer types were detected: <span id="pointertype-log"></span>.</p>
            <p>Refresh the page to run the tests again with a different pointer type.</p>
        </div>
        <div id="log"></div>
    </body>
</html>
