[DEFAULT]
prefs = [
  "gfx.font_loader.delay=0",
  "dom.w3c_pointer_events.getcoalescedevents_only_in_securecontext=false"
]

support-files = [
  "iframe.html",
  "mochitest_support_external.js",
  "mochitest_support_internal.js",
  "wpt/pointerevent_styles.css",
  "wpt/pointerevent_support.js",
  "pointerevent_utils.js",
  "!/gfx/layers/apz/test/mochitest/apz_test_utils.js",
]

["test_bug968148.html"]
support-files = [
  "bug968148_inner.html",
  "bug968148_inner2.html",
]

["test_bug1285128.html"]

["test_bug1293174_implicit_pointer_capture_for_touch_1.html"]
support-files = ["bug1293174_implicit_pointer_capture_for_touch_1.html"]
skip-if = [
  "http3",
  "http2",
]

["test_bug1293174_implicit_pointer_capture_for_touch_2.html"]
support-files = ["bug1293174_implicit_pointer_capture_for_touch_2.html"]
skip-if = [
  "http3",
  "http2",
]

["test_bug1303704.html"]

["test_bug1315862.html"]

["test_bug1323158.html"]

["test_bug1403055.html"]

["test_bug1420589_1.html"]
support-files = [
  "bug_1420589_iframe1.html",
  "bug_1420589_iframe2.html",
]

["test_bug1420589_2.html"]
support-files = ["bug_1420589_iframe1.html"]

["test_bug1420589_3.html"]
support-files = ["bug_1420589_iframe1.html"]

["test_bug1725416.html"]
skip-if = ["os == 'android'"] # Bug 1312791
support-files = ["!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"]

["test_getCoalescedEvents.html"]
skip-if = [
  "os == 'android'",  # Bug 1312791
  "display == 'wayland' && os_version == '22.04'",  # Bug 1856971
]

["test_getCoalescedEvents_touch.html"]
skip-if = [
  "os == 'android'",  # Bug 1312791
  "verify && os == 'win'",  # Bug 1659744
  "win11_2009", # Bug 1781388
]
support-files = ["!/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"]

["test_multiple_touches.html"]

["test_pointercapture_remove_iframe.html"]

["test_pointercapture_xorigin_iframe.html"]
support-files = [
  "file_pointercapture_xorigin_iframe.html",
  "file_pointercapture_xorigin_iframe_pointerlock.html",
  "file_pointercapture_xorigin_iframe_touch.html",
]
skip-if = [
  "display == 'wayland' && os_version == '22.04'",  # Bug 1856971
]

["test_pointermove_drag_scrollbar.html"]
skip-if = [
  "os == 'android'", # scrollbar not showed on mobile
]

["test_pointermove_isPrimary_subsequent_pens.html"]

["test_remove_frame_when_got_pointer_capture.html"]

["test_synthesized_touch.html"]

["test_trigger_fullscreen_by_pointer_events.html"]
support-files = ["file_test_trigger_fullscreen.html"]

["test_wpt_pointerevent_attributes_hoverable_pointers-manual.html"]
support-files = [
  "wpt/pointerevent_attributes_hoverable_pointers-manual.html",
  "wpt/resources/pointerevent_attributes_hoverable_pointers-iframe.html",
]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_attributes_nohover_pointers-manual.html"]
support-files = [
  "wpt/pointerevent_attributes_nohover_pointers-manual.html",
  "wpt/resources/pointerevent_attributes_hoverable_pointers-iframe.html",
]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_boundary_events_in_capturing-manual.html"]
support-files = ["wpt/pointerevent_boundary_events_in_capturing-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_drag_interaction-manual.html"]
support-files = ["wpt/html/pointerevent_drag_interaction-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_movementxy-manual.html"]
support-files = [
  "wpt/pointerlock/pointerevent_movementxy-manual.html",
  "wpt/pointerlock/resources/pointerevent_movementxy-iframe.html",
]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_multiple_primary_pointers_boundary_events-manual.html"]
support-files = ["wpt/pointerevent_multiple_primary_pointers_boundary_events-manual.html"]
disabled = "should be investigated"

["test_wpt_pointerevent_pointercancel_touch-manual.html"]
support-files = ["wpt/pointerevent_pointercancel_touch-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_pointerleave_after_pointercancel_touch-manual.html"]
support-files = ["wpt/pointerevent_pointerleave_after_pointercancel_touch-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_pointerleave_pen-manual.html"]
support-files = ["wpt/pointerevent_pointerleave_pen-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_pointerout_after_pointercancel_touch-manual.html"]
support-files = ["wpt/pointerevent_pointerout_after_pointercancel_touch-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_pointerout_pen-manual.html"]
support-files = ["wpt/pointerevent_pointerout_pen-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_releasepointercapture_events_to_original_target-manual.html"]
support-files = ["wpt/pointerevent_releasepointercapture_events_to_original_target-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_releasepointercapture_onpointercancel_touch-manual.html"]
support-files = ["wpt/pointerevent_releasepointercapture_onpointercancel_touch-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_pointerevent_sequence_at_implicit_release_on_drag-manual.html"]
support-files = ["wpt/pointerevent_sequence_at_implicit_release_on_drag-manual.html"]
skip-if = [
  "http3",
  "http2",
]

["test_wpt_touch_action.html"]
skip-if = [
  "os == 'android'", # Bug 1312791
  "os == 'linux' && headless", # Bug 1722906
]
support-files = [
  "../../../../gfx/layers/apz/test/mochitest/apz_test_utils.js",
  "../../../../gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js",
  "touch_action_helpers.js",
  "wpt/pointerevent_touch-action-auto-css_touch-manual.html",
  "wpt/pointerevent_touch-action-button-test_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_child-auto-child-none_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_child-none_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_child-pan-x-child-pan-x_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_child-pan-x-child-pan-y_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_highest-parent-none_touch-manual.html",
  "wpt/pointerevent_touch-action-inherit_parent-none_touch-manual.html",
  "wpt/pointerevent_touch-action-none-css_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-x-css_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-x-pan-y-pan-y_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-x-pan-y_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-y-css_touch-manual.html",
  "wpt/pointerevent_touch-action-span-test_touch-manual.html",
  "wpt/pointerevent_touch-action-svg-test_touch-manual.html",
  "wpt/pointerevent_touch-action-table-test_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-down-css_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-left-css_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-right-css_touch-manual.html",
  "wpt/pointerevent_touch-action-pan-up-css_touch-manual.html",
]
