<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Test for triggering popup by pointer events</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<p id="content">
</p>
<script>

SimpleTest.waitForExplicitFinish();

function startTest() {
  let content = document.getElementById('content');
  let iframe = document.createElement('iframe');
  iframe.width = 200;
  iframe.height = 200;
  content.appendChild(iframe);
  iframe.contentDocument.body.innerHTML =
    "<div id='div1' style='width: 50px; height: 50px; background: green'></div>" +
    "<div id='div2' style='width: 50px; height: 50px; background: red'></div>";

  let div1 = iframe.contentDocument.getElementById("div1");
  let div2 = iframe.contentDocument.getElementById("div2");
  let divEvents = [
    "pointerdown",
    "gotpointercapture",
    "pointermove",
    "pointerup",
    "lostpointercapture",
    "mousedown",
    "mousemove",
    "mouseup",
  ];

  let documentEvents = [
    "pointerdown",
    "pointermove",
    "pointerup",
    "mousedown",
    "mousemove",
    "mouseup",
  ];

  divEvents.forEach((event) => {
    div1.addEventListener(event, (e) => {
      ok(divEvents.includes(e.type), " don't expect " + e.type);
      divEvents = divEvents.filter(item => item !== e.type);
    }, { once: true });
  });

  documentEvents.forEach((event) => {
    iframe.contentDocument.addEventListener(event, (e) => {
      is(e.target, div1, e.type + " should be dispatched to div1");
    }, { once: true });
  });

  div1.addEventListener("pointerdown", (e) => {
    div1.setPointerCapture(e.pointerId);
  });

  div1.addEventListener("gotpointercapture", (e) => {
    div1.style.display = "none";
  });

  info("Tests for mouseup");
  synthesizeMouseAtCenter(div1, {type: "mousedown"}, iframe.contentWindow);
  synthesizeMouseAtCenter(div2, {type: "mousemove"}, iframe.contentWindow);
  synthesizeMouseAtCenter(div2, {type: "mouseup"}, iframe.contentWindow);

  ok(!divEvents.length, " expect " + divEvents);

  divEvents = [
    "pointerdown",
    "gotpointercapture",
    "pointermove",
    "pointerup",
    "lostpointercapture",
    "touchstart",
    "touchmove",
    "touchend",
  ];

  documentEvents = [
    "pointerdown",
    "pointermove",
    "pointerup",
    "touchstart",
    "touchmove",
    "touchend",
  ];
  divEvents.forEach((event) => {
    div1.addEventListener(event, (e) => {
      ok(divEvents.includes(e.type), " don't expect " + e.type);
      divEvents = divEvents.filter(item => item !== e.type);
    }, { once: true });
  });

  documentEvents.forEach((event) => {
    iframe.contentDocument.addEventListener(event, (e) => {
      is(e.target, div1, e.type + " should be dispatched to div1");
    }, { once: true });
  });

  info("Tests for touchend");
  div1.style.display = "block";
  synthesizeMouseAtCenter(div1, {type: "mousemove"}, iframe.contentWindow);
  synthesizeTouch(div1, 5, 5, { type: "touchstart" }, iframe.contentWindow);
  synthesizeTouch(div2, 5, 5, { type: "touchmove" }, iframe.contentWindow);
  synthesizeTouch(div2, 5, 5, { type: "touchend" }, iframe.contentWindow);

  ok(!divEvents.length, " expect " + divEvents);

  divEvents = [
    "pointerdown",
    "gotpointercapture",
    "pointermove",
    "pointercancel",
    "lostpointercapture",
    "touchstart",
    "touchmove",
    "touchcancel",
  ];

  documentEvents = [
    "pointerdown",
    "pointermove",
    "pointercancel",
    "touchstart",
    "touchmove",
    "touchcancel",
  ];
  divEvents.forEach((event) => {
    div1.addEventListener(event, (e) => {
      ok(divEvents.includes(e.type), " don't expect " + e.type);
      divEvents = divEvents.filter(item => item !== e.type);
    }, { once: true });
  });

  documentEvents.forEach((event) => {
    iframe.contentDocument.addEventListener(event, (e) => {
      is(e.target, div1, e.type + " should be dispatched to div1");
    }, { once: true });
  });

  info("Tests for touchcancel");
  div1.style.display = "block";
  synthesizeMouseAtCenter(div1, {type: "mousemove"}, iframe.contentWindow);
  synthesizeTouch(div1, 5, 5, { type: "touchstart" }, iframe.contentWindow);
  synthesizeTouch(div2, 5, 5, { type: "touchmove" }, iframe.contentWindow);
  synthesizeTouch(div2, 5, 5, { type: "touchcancel" }, iframe.contentWindow);

  ok(!divEvents.length, " expect " + divEvents);

  SimpleTest.finish();
}

SimpleTest.waitForFocus(startTest);

</script>
</body>
</html>
