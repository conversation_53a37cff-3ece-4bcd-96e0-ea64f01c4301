<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1669673
-->
  <head>
    <meta charset="utf-8">
    <title>Test for Bug 1669673</title>
    <meta name="author" content="<PERSON><PERSON><PERSON>" />
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <script src="/tests/SimpleTest/EventUtils.js"></script>
    <script type="text/javascript" src="mochitest_support_external.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
    <script type="text/javascript">
      SimpleTest.waitForExplicitFinish();
      function startTest() {
        runTestInNewWindow("wpt/html/pointerevent_drag_interaction-manual.html");
      }
      async function executeTest(int_win) {
        info("executeTest");
        // DndWithoutCapture
        await doDragAndDrop(int_win, "target0", "target1");
        // DndWithCapture
        await doDragAndDrop(int_win, "target0", "target1");
        // DndWithCaptureMouse
        await doDragAndDrop(int_win, "target0", "target1");
        // DndPrevented
        await doDragAndDrop(int_win, "target0", "target1", {
          expectCancelDragStart: true,
          // Move mouse to target1.
          stepY: 33
        });
      }
    </script>
  </head>
  <body>
  </body>
</html>
