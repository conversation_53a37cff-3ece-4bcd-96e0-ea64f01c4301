<!DOCTYPE HTML>
<html>
  <head>
    <meta charset="utf-8">
    <title>Test pointerevent attributes for hoverable pointers</title>
    <script src="/tests/SimpleTest/SimpleTest.js"></script>
    <script src="/tests/SimpleTest/EventUtils.js"></script>
    <script type="text/javascript" src="mochitest_support_external.js"></script>
    <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
    <script type="text/javascript">
      SimpleTest.waitForExplicitFinish();
      function startTest() {
        runTestInNewWindow("wpt/pointerevent_attributes_hoverable_pointers-manual.html");
      }
      function executeTest(int_win) {
        let iframeWin = int_win.document.getElementById("innerFrame").contentWindow;
        // synthesize mouse events with input source = mouse
        sendMouseEvent(int_win, "square1", "mousemove", {button:-1});
        sendMouseEvent(int_win, "square1", "mousedown");
        sendMouseEvent(int_win, "square1", "mouseup");
        sendMouseEvent(int_win, "square1", "mousemove", {button:-1,
                                                         offsetX:-1,
                                                         offsetY:-1});
        sendMouseEvent(iframeWin, "square2", "mousemove", {button:-1});
        sendMouseEvent(iframeWin, "square2", "mousedown");
        sendMouseEvent(iframeWin, "square2", "mouseup");
        sendMouseEvent(iframeWin, "square2", "mousemove", {button:-1,
                                                           offsetX:-1,
                                                           offsetY:-1});
        // synthesize mouse events with input source = pen
        let inputPen = MouseEvent.MOZ_SOURCE_PEN;
        sendMouseEvent(int_win, "square1", "mousemove", {button:-1,
                                                         inputSource: inputPen});
        sendMouseEvent(int_win, "square1", "mousedown", {inputSource:inputPen});
        sendMouseEvent(int_win, "square1", "mouseup", {inputSource:inputPen});
        sendMouseEvent(int_win, "square1", "mousemove", {button:-1,
                                                         offsetX:-1,
                                                         offsetY:-1,
                                                         inputSource:inputPen});
        sendMouseEvent(iframeWin, "square2", "mousemove", {button:-1,
                                                           inputSource:inputPen});
        sendMouseEvent(iframeWin, "square2", "mousedown", {inputSource:inputPen});
        sendMouseEvent(iframeWin, "square2", "mouseup", {inputSource:inputPen});
        sendMouseEvent(iframeWin, "square2", "mousemove", {button:-1,
                                                           offsetX:-1,
                                                           offsetY:-1,
                                                           inputSource:inputPen});
      }
    </script>
  </head>
  <body>
  </body>
</html>
