<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=968148
-->
<head>
  <title>Test for Bug 968148</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
  <style>
  .test {
    width: 20px;
    height: 20px;
    border: 1px solid black;
    -moz-user-select: none;
  }
  </style>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=968148">Mozilla Bug 968148</a>
<p id="display"></p>
<div id="content" style="display: none">
</div>
<pre id="test">
<script type="application/javascript">

/**
 * Test for Bug 968148, test orignally copied from test_bug582771.html.
 * Mouse functionality converted to pointer and all steps duplicated in order to run them in parallel for two different pointer Id's
**/

function ok(condition, msg) {
  parent.ok(condition, msg);
}

function is(a, b, msg) {
  parent.is(a, b, msg);
}

var test1d1;
var test1d2;
var test2d1;
var test2d2;
var test1d1pointermovecount = 0;
var test1d2pointermovecount = 0;
var test2d1pointermovecount = 0;
var test2d2pointermovecount = 0;

var test1d1pointerlostcapture = 0;
var test1d2pointerlostcapture = 0;
var test2d1pointerlostcapture = 0;
var test2d2pointerlostcapture = 0;
var test1d1pointergotcapture = 0;
var test1d2pointergotcapture = 0;
var test2d1pointergotcapture = 0;
var test2d2pointergotcapture = 0;
var test1PointerId = 1;
var test2PointerId = 2;
var rx = 1;
var ry = 1;
var angle = 0;
var force = 1;
var modifiers = 0;
var utils = SpecialPowers.getDOMWindowUtils(window);

function log(s) {
  document.getElementById("l").textContent += s + "\n";
}

function test1d1IncreasePointerMoveCount(e) {
  log(e.type + ", " + e.target.id);
  is(e.target, test1d1, "check target is 1d1.");
  ++test1d1pointermovecount;
}

function test1d2IncreasePointerMoveCount(e) {
  log(e.type + ", " + e.target.id);
  is(e.target, test1d2, "check target is 1d2.");
  ++test1d2pointermovecount;
}

function test2d1IncreasePointerMoveCount(e) {
  log(e.type + ", " + e.target.id);
  is(e.target, test2d1, "check target is 2d1.");
  ++test2d1pointermovecount;
}

function test2d2IncreasePointerMoveCount(e) {
  log(e.type + ", " + e.target.id);
  is(e.target, test2d2, "check target is 2d2.");
  ++test2d2pointermovecount;
}

function test1d1CapturePointer(e) {
  log(e.type + ", " + e.target.id);
  test1d1.setPointerCapture(e.pointerId);
}

function test1d2CapturePointer(e) {
  log(e.type + ", " + e.target.id);
  test1d2.setPointerCapture(e.pointerId);
}

function test2d1CapturePointer(e) {
  log(e.type + ", " + e.target.id);
  test2d1.setPointerCapture(e.pointerId);
}

function test2d2CapturePointer(e) {
  log(e.type + ", " + e.target.id);
  test2d2.setPointerCapture(e.pointerId);
}

function test1d1PointerGotCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test1d1pointergotcapture;
}

function test1d1PointerLostCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test1d1pointerlostcapture;
}

function test2d1PointerGotCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test2d1pointergotcapture;
}

function test2d1PointerLostCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test2d1pointerlostcapture;
}

function test1d2PointerGotCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test1d2pointergotcapture;
}

function test1d2PointerLostCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test1d2pointerlostcapture;
}

function test2d2PointerGotCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test2d2pointergotcapture;
}

function test2d2PointerLostCapture(e) {
  log(e.type + ", " + e.target.id);
  ++test2d2pointerlostcapture;
}

function runTests() {
  test1d1 = document.getElementById("test1d1");
  test1d2 = document.getElementById("test1d2");
  test2d1 = document.getElementById("test2d1");
  test2d2 = document.getElementById("test2d2");

  var rect1d1 = test1d1.getBoundingClientRect();
  var rect1d2 = test1d2.getBoundingClientRect();
  var rect2d1 = test2d1.getBoundingClientRect();
  var rect2d2 = test2d2.getBoundingClientRect();

  var left1d1 = rect1d1.left + 5;
  var top1d1 = rect1d1.top + 5;
  var left1d2 = rect1d2.left + 5;
  var top1d2 = rect1d2.top + 5;
  var left2d1 = rect2d1.left + 5;
  var top2d1 = rect2d1.top + 5;
  var left2d2 = rect2d2.left + 5;
  var top2d2 = rect2d2.top + 5;

  test1d1.addEventListener("pointermove", test1d1IncreasePointerMoveCount, true);
  test1d2.addEventListener("pointermove", test1d2IncreasePointerMoveCount, true);
  test2d1.addEventListener("pointermove", test2d1IncreasePointerMoveCount, true);
  test2d2.addEventListener("pointermove", test2d2IncreasePointerMoveCount, true);

  test1d1.addEventListener("gotpointercapture", test1d1PointerGotCapture, true);
  test1d1.addEventListener("lostpointercapture", test1d1PointerLostCapture, true);

  test2d1.addEventListener("gotpointercapture", test2d1PointerGotCapture, true);
  test2d1.addEventListener("lostpointercapture", test2d1PointerLostCapture, true);

  test1d2.addEventListener("gotpointercapture", test1d2PointerGotCapture, true);
  test1d2.addEventListener("lostpointercapture", test1d2PointerLostCapture, true);

  test2d2.addEventListener("gotpointercapture", test2d2PointerGotCapture, true);
  test2d2.addEventListener("lostpointercapture", test2d2PointerLostCapture, true);

  document.body.offsetLeft;

  // This shouldn't enable capturing, since we're not in a right kind of
  // event listener.
  utils.sendTouchEvent('touchstart', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  utils.sendTouchEvent('touchmove', [test1PointerId, test2PointerId],
                       [left1d2, left2d2], [top1d2, top2d2], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  utils.sendTouchEvent('touchend', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  // Implicitly pointer capture. pointermove should be dispatched to 1d1, 2d1
  is(test1d1pointermovecount, 1, "1d1 should have got pointermove");
  is(test2d1pointermovecount, 1, "2d1 should have got pointermove");
  is(test1d2pointermovecount, 0, "1d2 shouldn't have got pointermove");
  is(test2d2pointermovecount, 0, "2d2 shouldn't have got pointermove");

  // Explicitly capture pointer to 1d1, 2d1
  test1d1.addEventListener("pointerdown", test1d1CapturePointer, {capture: true, once: true});
  test2d1.addEventListener("pointerdown", test2d1CapturePointer, {capture: true, once: true});

  utils.sendTouchEvent('touchstart', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  utils.sendTouchEvent('touchmove', [test1PointerId, test2PointerId],
                       [left1d2, left2d2], [top1d2, top2d2], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  // Explicitly capture pointer to 1d1, 2d1. pointermove, gotpointercapture
  // should be dispatched to 1d1, 2d1
  is(test1d1pointermovecount, 2, "1d1 should have got pointermove");
  is(test1d1pointergotcapture, 2, "1d1 should have got pointergotcapture");
  is(test1d2pointermovecount, 0, "1d2 shouldn't have got pointermove");
  is(test1d2pointergotcapture, 0, "1d2 shouldn't have got pointergotcapture");

  is(test2d1pointermovecount, 2, "2d1 should have got pointermove");
  is(test2d1pointergotcapture, 2, "2d1 should have got pointergotcapture");
  is(test2d2pointermovecount, 0, "2d2 shouldn't have got pointermove");
  is(test2d2pointergotcapture, 0, "2d2 shouldn't have got pointergotcapture");

  utils.sendTouchEvent('touchend', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  // Explicitly capture pointer to 1d1, 2d1 when pointerdown
  test1d1.addEventListener("pointerdown", test1d1CapturePointer, {capture: true, once: true});
  test2d1.addEventListener("pointerdown", test2d1CapturePointer, {capture: true, once: true});

  // Explicitly capture pointer to 1d2, 2d2 when pointermove
  test1d1.addEventListener("pointermove", test1d2CapturePointer, {capture: true, once: true});
  test2d1.addEventListener("pointermove", test2d2CapturePointer, {capture: true, once: true});

  utils.sendTouchEvent('touchstart', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  // This should send pointer event to test1d1, test2d1.
  utils.sendTouchEvent('touchmove', [test1PointerId, test2PointerId],
                       [left1d1 + 5, left2d1 + 5], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  // This should send pointer event to test1d2, test2d2.
  utils.sendTouchEvent('touchmove', [test1PointerId, test2PointerId],
                       [left1d1 + 5, left2d1 + 5], [top1d1 + 5, top2d1 + 5], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  is(test1d1pointermovecount, 3, "1d1 shouldn't have got pointermove");
  is(test1d1pointergotcapture, 3, "1d1 should have got pointergotcapture");
  is(test1d2pointermovecount, 1, "1d2 should have got pointermove");
  is(test1d2pointergotcapture, 1, "1d2 should have got pointergotcapture");

  is(test2d1pointermovecount, 3, "2d1 shouldn't have got pointermove");
  is(test2d1pointergotcapture, 3, "2d1 should have got pointergotcapture");
  is(test2d2pointermovecount, 1, "2d2 should have got pointermove");
  is(test2d2pointergotcapture, 1, "2d2 should have got pointergotcapture");

  utils.sendTouchEvent('touchend', [test1PointerId, test2PointerId],
                       [left1d1, left2d1], [top1d1, top2d1], [rx, rx], [ry, ry],
                       [angle, angle], [force, force], [0, 0], [0, 0],
                       [0, 0], modifiers);

  finishTest();
}

function finishTest() {
  // Let window.onerror have a chance to fire
  setTimeout(function() {
    setTimeout(function() {
      window.parent.postMessage("finishTest", "*");
    }, 0);
  }, 0);
}

window.onload = function () {
  SpecialPowers.pushPrefEnv({
    "set": [
      ["dom.w3c_pointer_events.implicit_capture", true]
    ]
  }, runTests);
}

SimpleTest.waitForExplicitFinish();

</script>
</pre>
<div class="test" id="test1d1">&nbsp;</div><br><div class="test" id="test1d2">&nbsp;</div>
<div class="test" id="test2d1">&nbsp;</div><br><div class="test" id="test2d2">&nbsp;</div>
<pre id="l"></pre>
</body>
</html>
