<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=1511231
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 1511231</title>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/gfx/layers/apz/test/mochitest/apz_test_native_event_utils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1303957">Mozilla Bug 1511231</a>
<p id="display"></p>
<div id="target0" style="margin: 50px; width: 50px; height: 50px; background: green"></div>
<script type="text/javascript">
/** Test for Bug 1511231 **/
SimpleTest.waitForExplicitFinish();

if (!window.opener) {
  // the utils function in apz can't not be used in remote iframe, so run the
  // test in a new tab.
  info("run tests in a new tab");
  window.open("test_getCoalescedEvents_touch.html");
} else {
  function runTests() {
    let target0 = window.document.getElementById("target0");
    let utils = SpecialPowers.getDOMWindowUtils(window);
    utils.advanceTimeAndRefresh(0);

    SimpleTest.executeSoon(async () => {
      // Flush all pending touch events before synthesizing events.

      let eventCount = 0;
      target0.addEventListener("pointermove", function handler(ev) {
        eventCount++;
        if (eventCount == 1) {
          // Ignore the first pointermove event since the first touch event won't
          // be coalesced.
          is(ev.getCoalescedEvents().length, 1,
             "Coalesced events should be 1 for the first touchmove event");
          return;
        }

        let length = ev.getCoalescedEvents().length;
        ok(length >= 1, "Coalesced events should >= 1, got " + length);

        let rect = target0.getBoundingClientRect();
        let prevOffsetX = 0;
        let prevOffsetY = 0;

        for (let i = 0; i < length; ++i) {
          let coalescedEvent = ev.getCoalescedEvents()[i];
          opener.isnot(coalescedEvent.timeStamp, 0, "getCoalescedEvents()[" + i + "].timeStamp");
          opener.is(coalescedEvent.type, "pointermove", "getCoalescedEvents()[" + i + "].type");
          opener.is(coalescedEvent.pointerId, ev.pointerId, "getCoalescedEvents()[" + i + "].pointerId");
          opener.is(coalescedEvent.pointerType, ev.pointerType, "getCoalescedEvents()[" + i + "].pointerType");
          opener.is(coalescedEvent.isPrimary, ev.isPrimary, "getCoalescedEvents()[" + i + "].isPrimary");
          opener.is(coalescedEvent.target, ev.target, "getCoalescedEvents()[" + i + "].target");
          opener.is(coalescedEvent.currentTarget, null, "getCoalescedEvents()[" + i + "].currentTarget");
          opener.is(coalescedEvent.eventPhase, Event.NONE, "getCoalescedEvents()[" + i + "].eventPhase");
          opener.is(coalescedEvent.cancelable, false, "getCoalescedEvents()[" + i + "].cancelable");
          opener.is(coalescedEvent.bubbles, false, "getCoalescedEvents()[" + i + "].bubbles");

          opener.ok(coalescedEvent.offsetX >= prevOffsetX, "getCoalescedEvents()[" + i + "].offsetX = " + coalescedEvent.offsetX);
          opener.ok(coalescedEvent.offsetX >= 10 && coalescedEvent.offsetX <= 40, "expected offsetX");

          opener.ok(coalescedEvent.offsetY >= prevOffsetY, "getCoalescedEvents()[" + i + "].offsetY = " + coalescedEvent.offsetY);
          opener.ok(coalescedEvent.offsetY >= 10 && coalescedEvent.offsetY <= 40, "expected offsetY");

          prevOffsetX = coalescedEvent.offsetX;
          prevOffsetY = coalescedEvent.offsetY;

          let x = rect.left + prevOffsetX;
          let y = rect.top + prevOffsetY;
          // coordinates may change slightly due to rounding
          opener.ok((coalescedEvent.clientX <= x+2) && (coalescedEvent.clientX >= x-2), "getCoalescedEvents()[" + i + "].clientX");
          opener.ok((coalescedEvent.clientY <= y+2) && (coalescedEvent.clientY >= y-2), "getCoalescedEvents()[" + i + "].clientY");
        }

        target0.removeEventListener("pointermove", handler);
      });

      target0.addEventListener("pointerup", (ev) => {
        utils.restoreNormalRefresh();
        opener.SimpleTest.finish();
        window.close();
      }, { once: true });

      let positions = [];
      for (let i = 10; i <= 40; i+=5) {
        positions.push([{ x: i, y: i }]);
      }

      await synthesizeNativeTouchSequences(target0, positions);
    });
  }

  SimpleTest.waitForFocus(() => {
    SpecialPowers.pushPrefEnv({"set": [
      ["dom.events.coalesce.touchmove", true],
      ["dom.events.compress.touchmove", false],
    ]}, runTests);
  });
}

</script>
</body>
</html>
