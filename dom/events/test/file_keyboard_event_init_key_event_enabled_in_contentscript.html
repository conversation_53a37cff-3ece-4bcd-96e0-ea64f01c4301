<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Storing type of `KeyboardEvent.initKeyEvent`</title>
<script>
document.documentElement.setAttribute(
  "data-initKeyEvent-before",
  typeof window.KeyboardEvent.prototype.initKeyEvent
);
window.addEventListener("load", () => {
  document.documentElement.setAttribute(
    "data-initKeyEvent-after",
    typeof window.KeyboardEvent.prototype.initKeyEvent
  );
}, { capture: false, once: true });
</script>
</head>
<body>
</body>
</html>
