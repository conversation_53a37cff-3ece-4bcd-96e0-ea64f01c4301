<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=659071
-->
<head>
  <title>Test for Bug 659071</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=659071">Mozilla Bug 659071</a>
<p id="display"></p>
<div id="content" style="display: none">
  
</div>
<pre id="test">
<script class="testbody" type="text/javascript">

/** Test for Bug 659071 **/

SimpleTest.waitForExplicitFinish();
SimpleTest.requestFlakyTimeout("untriaged");

var subWin = window.open("window_bug659071.html", "_blank",
                         "width=500,height=500");

function finish()
{
  subWin.close();
  SimpleTest.finish();
}

</script>
</pre>
</body>
</html>
