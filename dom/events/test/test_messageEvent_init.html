<html><head>
<title>Test for bug 1308956</title>
<link rel="stylesheet" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script src="/tests/SimpleTest/EventUtils.js"></script>

<body>
  <script>

var a = new MessageEvent("message")
ok(!!a, "We have a MessageEvent");
is(a.ports.length, 0, "By default MessageEvent.ports is an empty array");

a.initMessageEvent("message", true, false, {}, window.location.href, "", null, []);
ok(Array.isArray(a.ports), "After InitMessageEvent() we have an array");
is(a.ports.length, 0, "Length is 0");

var mc = new MessageChannel();
a.initMessageEvent("message", true, false, {}, window.location.href, "", null, [mc.port1]);
ok(Array.isArray(a.ports), "After InitMessageEvent() we have an array");
is(a.ports.length, 1, "Length is 1");

  </script>
</body>
</html>
