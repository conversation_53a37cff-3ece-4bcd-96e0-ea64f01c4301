<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=450876
-->
<head>
  <title>Test for Bug 450876 - Crash [@ nsEventStateManager::GetNextTabbableMapArea] with img usemap and tabindex</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="/tests/SimpleTest/EventUtils.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css" />
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=450876">Mozilla Bug 450876</a>
<p id="display"><a href="#" id="a">link to focus from</a><img usemap="#a" tabindex="1"></p>
<div id="content" style="display: none">
  
</div>

<pre id="test">
<script class="testbody" type="text/javascript">

/** Test for Bug 450876 **/

function setTabFocus() {
  // Override tab focus behavior on Mac */
  SpecialPowers.pushPrefEnv({ set: [[ "accessibility.tabfocus", 7 ]] }, doTest);
}

function doTest() {
  is(document.activeElement, document.body, "body element should be focused");
  document.getElementById('a').focus();
  is(document.activeElement, document.getElementById('a'), "link should have focus");
  is(document.hasFocus(), true, "document should be focused");
  synthesizeKey("KEY_Tab");
  is(document.activeElement, document.body, "body element should be focused");
  is(document.hasFocus(), false, "document should not be focused");

  SimpleTest.finish();
}

SimpleTest.waitForExplicitFinish();
addLoadEvent(setTabFocus);

</script>
</pre>
</body>
</html>

