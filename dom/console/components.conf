# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{96cf7855-dfa9-4c6d-8276-f9705b4890f2}',
        'contract_ids': ['@mozilla.org/consoleAPI-storage;1'],
        'esModule': 'resource://gre/modules/ConsoleAPIStorage.sys.mjs',
        'constructor': 'ConsoleAPIStorageService',
    },
]
