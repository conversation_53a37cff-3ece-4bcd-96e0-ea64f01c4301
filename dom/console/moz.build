# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: Core & HTML")

XPIDL_SOURCES += [
    "nsIConsoleAPIStorage.idl",
]

XPIDL_MODULE = "dom"

EXPORTS += [
    "nsIConsoleReportCollector.h",
]

EXPORTS.mozilla += [
    "ConsoleReportCollector.h",
]

EXPORTS.mozilla.dom += [
    "Console.h",
    "ConsoleInstance.h",
    "ConsoleUtils.h",
]

UNIFIED_SOURCES += [
    "Console.cpp",
    "ConsoleInstance.cpp",
    "ConsoleReportCollector.cpp",
    "ConsoleUtils.cpp",
]

EXTRA_JS_MODULES += [
    "ConsoleAPIStorage.sys.mjs",
]

XPCOM_MANIFESTS += [
    "components.conf",
]

LOCAL_INCLUDES += [
    "/docshell/base",
    "/dom/base",
    "/js/xpconnect/src",
]

MOCHITEST_MANIFESTS += ["tests/mochitest.toml"]
MOCHITEST_CHROME_MANIFESTS += ["tests/chrome.toml"]
XPCSHELL_TESTS_MANIFESTS += ["tests/xpcshell/xpcshell.toml"]
BROWSER_CHROME_MANIFESTS += ["tests/browser.toml"]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"
