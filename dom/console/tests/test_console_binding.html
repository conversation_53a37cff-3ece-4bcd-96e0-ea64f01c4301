<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Test Console binding</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="head.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
  <script type="application/javascript">

function consoleListener() {
  addConsoleStorageListener(this);
}

var order = 0;
consoleListener.prototype  = {
  observe(obj) {
    ok(!obj.chromeContext, "Thils is not a chrome context");
    if (order + 1 == parseInt(obj.arguments[0])) {
      ok(true, "Message received: " + obj.arguments[0]);
      order++;
    }

    if (order == 3) {
      removeConsoleStorageListener(this);
      SimpleTest.finish();
    }
  },
};

var cl = new consoleListener();
SimpleTest.waitForExplicitFinish();

[1, 2, 3].forEach(console.log);

  </script>
</body>
</html>
