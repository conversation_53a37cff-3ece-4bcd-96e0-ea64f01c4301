<?xml version="1.0"?>
<!--
  Any copyright is dedicated to the Public Domain.
  http://creativecommons.org/publicdomain/zero/1.0/
-->
<window title="Console + MJS"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        onload="test();">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>
  <script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"/>
  <script src="head.js"/>

  <script type="application/javascript">
  <![CDATA[

const MJS = "chrome://mochitests/content/chrome/dom/console/tests/console.sys.mjs";

let dumpCalled = 0;
function dumpFunction(msg) {
  ok(msg.includes("_PREFIX_"), "we have a prefix");
  dump("Message received: " + msg); // Just for debugging
  dumpCalled++;
}

function promiseConsoleListenerCalled() {
  return new Promise(resolve => {
    let consoleListener = {
      count: 0,

      observe(aSubject) {
        var obj = aSubject.wrappedJSObject;
        ok(obj.chromeContext, "MJS is always a chrome context");

        if (obj.innerID == MJS) {
          is(obj.ID, "jsm", "ID and InnerID are correctly set.");
          is(obj.arguments[0], "Hello world!", "Message matches");
          is(obj.consoleID, "", "No consoleID for console API");
          is(obj.prefix, "", "prefix is empty by default");

          // We want to see 2 messages from this innerID, the first is generated
          // by console.log, the second one from createInstance().log();
          ++this.count;
        } else if (obj.innerID == "CUSTOM INNER") {
          is(obj.ID, "jsm", "ID and InnerID are correctly set.");
          is(obj.arguments[0], "Hello world!", "Message matches");
          is(obj.consoleID, "wow", "consoleID is set by consoleInstance");
          is(obj.prefix, "_PREFIX_", "prefix is set by consoleInstance");
          // We expect to see 2 messages from this innerID.
          ++this.count;
        } else if (obj.innerID == "LEVEL") {
          // Nothing special... just we don't want to see 'invisible' messages.
          is(obj.ID, "jsm", "ID and InnerID are correctly set.");
          is(obj.arguments[0], "Hello world!", "Message matches");
          is(obj.prefix, "", "prefix is empty by default");
          // We expect to see 2 messages from this innerID.
          ++this.count;
        } else if (obj.innerID == "NO PREF") {
          // Nothing special... just we don't want to see 'invisible' messages.
          is(obj.ID, "jsm", "ID and InnerID are correctly set.");
          is(obj.arguments[0], "Hello world!", "Message matches");
          is(obj.prefix, "", "prefix is empty by default");
          // We expect to see 2 messages from this innerID.
          ++this.count;
        }

        if (this.count == 8) {
          is(dumpCalled, 2, "Dump has been called!");
          removeConsoleStorageListener(consoleListener);
          resolve();
        }
      }
    }
    addConsoleStorageListener(consoleListener);
  });
}

async function test() {
  SimpleTest.waitForExplicitFinish();

  let consolePromise = promiseConsoleListenerCalled();
  let { ConsoleTest } = ChromeUtils.importESModule(MJS);
  await SpecialPowers.pushPrefEnv({set: [["pref.test.console", "log"]]})
  ConsoleTest.go(dumpFunction);

  // ConsoleTest.go() has already tested the console without this preference.
  // This test checks that the addition of the preference is tracked and
  // correctly applied.
  await SpecialPowers.pushPrefEnv({
    set: [["pref.test.console.notset", "Log"]],
  });
  ConsoleTest.go2();

  await consolePromise;
  SimpleTest.finish();
}

  ]]>
  </script>

  <body xmlns="http://www.w3.org/1999/xhtml">
  </body>
</window>
