<?xml version="1.0"?>
<?xml-stylesheet type="text/css" href="chrome://global/skin"?>
<?xml-stylesheet type="text/css" href="chrome://mochikit/content/tests/SimpleTest/test.css"?>
<window title="Test for URL API"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"/>

  <!-- test results are displayed in the html:body -->
  <body xmlns="http://www.w3.org/1999/xhtml">
    <iframe id="iframe" />
  </body>

  <!-- test code goes here -->
  <script type="application/javascript"><![CDATA[

  ok("console" in window, "Console exists");
  window.console.log(42);
  ok("table" in console, "Console has the 'table' method.");
  window.console = 42;
  is(window.console, 42, "Console is replacable");

  var frame = document.getElementById("iframe");
  ok(frame, "Frame must exist");
  frame.src="http://mochi.test:8888/tests/dom/console/test/file_empty.html";
  frame.onload = function() {
    ok("console" in frame.contentWindow, "Console exists in the iframe");
    frame.contentWindow.console.log(42);
    frame.contentWindow.console = 42;
    is(frame.contentWindow.console, 42, "Console is replacable in the iframe");
    SimpleTest.finish();
  }

  SimpleTest.waitForExplicitFinish();
  ]]></script>
</window>
