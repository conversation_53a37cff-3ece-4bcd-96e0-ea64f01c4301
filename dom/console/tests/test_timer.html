<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <title>Test for timeStart/timeLog/timeEnd in console</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <script src="head.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
  <script type="application/javascript">

SimpleTest.waitForExplicitFinish();

var reduceTimePrecisionPrevPrefValue = SpecialPowers.getBoolPref("privacy.reduceTimerPrecision");
SpecialPowers.setBoolPref("privacy.reduceTimerPrecision", false);

function ConsoleListener() {
  addConsoleStorageListener(this);
}

ConsoleListener.prototype = {
  observe(aSubject) {
    let obj = aSubject.wrappedJSObject;
    if (obj.arguments[0] != "test_bug1463614") {
      return;
    }

    if (!this._cb || !this._cb(obj)) {
      return;
    }

    this._cb = null;
    this._resolve();
  },

  shutdown() {
    removeConsoleStorageListener(this);
  },

  waitFor(cb) {
    return new Promise(resolve => {
      this._cb = SpecialPowers.wrapCallback(cb);
      this._resolve = resolve;
    });
  },
};

let listener = new ConsoleListener();

// Timer creation:
async function runTest() {
  let cl = listener.waitFor(obj => {
    return ("timer" in obj) &&
           ("name" in obj.timer) &&
           obj.timer.name == "test_bug1463614";
  });

  console.time("test_bug1463614");
  await cl;
  ok(true, "Console.time received!");

  // Timer check:
  cl = listener.waitFor(obj => {
    return ("timer" in obj) &&
           ("name" in obj.timer) &&
           obj.timer.name == "test_bug1463614" &&
           ("duration" in obj.timer) &&
           obj.timer.duration >= 0 &&
           obj.arguments[1] == 1 &&
           obj.arguments[2] == 2 &&
           obj.arguments[3] == 3 &&
           obj.arguments[4] == 4;
  });
  console.timeLog("test_bug1463614", 1, 2, 3, 4);
  await cl;
  ok(true, "Console.timeLog received!");

  // Time deleted:
  cl = listener.waitFor(obj => {
    return ("timer" in obj) &&
           ("name" in obj.timer) &&
           obj.timer.name == "test_bug1463614" &&
           ("duration" in obj.timer) &&
           obj.timer.duration >= 0;
  });
  console.timeEnd("test_bug1463614");
  await cl;
  ok(true, "Console.timeEnd received!");

  // Here an error:
  cl = listener.waitFor(obj => {
    return ("timer" in obj) &&
           ("name" in obj.timer) &&
           obj.timer.name == "test_bug1463614" &&
           ("error" in obj.timer);
  });
  console.timeLog("test_bug1463614");
  await cl;
  ok(true, "Console.time with error received!");
}

runTest().then(() => {
  listener.shutdown();

  SpecialPowers.setBoolPref("privacy.reduceTimerPrecision", reduceTimePrecisionPrevPrefValue);
  SimpleTest.finish();
});

  </script>
</body>
</html>
