<!DOCTYPE HTML>
<html>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=659625
-->
<head>
  <meta charset="utf-8">
  <title>Test for Bug 659625</title>
  <script src="/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css"/>
</head>
<body>
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=659625">Mozilla Bug 659625</a>
<script type="application/javascript">
  const { Cc, Ci } = SpecialPowers;
  let consoleStorage = Cc["@mozilla.org/consoleAPI-storage;1"];
  let storage = consoleStorage.getService(Ci.nsIConsoleAPIStorage);

  let clearAndCheckStorage = () => {
    console.clear();
    is(storage.getEvents().length, 1,
      "Only one event remains in consoleAPIStorage");
    is(storage.getEvents()[0].level, "clear",
      "Remaining event has level 'clear'");
  };

  storage.clearEvents();
  is(storage.getEvents().length, 0,
    "Console is empty when test is starting");
  clearAndCheckStorage();

  console.log("log");
  console.debug("debug");
  console.warn("warn");
  console.error("error");
  console.exception("exception");
  is(storage.getEvents().length, 6,
    "5 new console events have been registered for logging variants");
  clearAndCheckStorage();

  console.trace();
  is(storage.getEvents().length, 2,
    "1 new console event registered for trace");
  clearAndCheckStorage();

  console.dir({});
  is(storage.getEvents().length, 2,
    "1 new console event registered for dir");
  clearAndCheckStorage();

  console.count("count-label");
  console.count("count-label");
  is(storage.getEvents().length, 3,
    "2 new console events registered for 2 count calls");
  clearAndCheckStorage();

  // For bug 1346326.
  console.count("default");
  console.count();
  console.count(undefined);
  let events = storage.getEvents();
  // Drop the event from the previous "clear".
  events.splice(0, 1);
  is(events.length, 3,
    "3 new console events registered for 3 'default' count calls");
  for (let i = 0; i < events.length; ++i) {
      is(events[i].counter.count, i + 1, "check counter for event " + i);
      is(events[i].counter.label, "default", "check label for event " + i);
  }
  clearAndCheckStorage();

  console.group("group-label");
  console.log("group-log");
  is(storage.getEvents().length, 3,
    "2 new console events registered for group + log");
  clearAndCheckStorage();

  console.groupCollapsed("group-collapsed");
  console.log("group-collapsed-log");
  is(storage.getEvents().length, 3,
    "2 new console events registered for groupCollapsed + log");
  clearAndCheckStorage();

  console.group("closed-group-label");
  console.log("group-log");
  console.groupEnd();
  is(storage.getEvents().length, 4,
    "3 new console events registered for group/groupEnd");
  clearAndCheckStorage();

  console.time("time-label");
  console.timeEnd();
  is(storage.getEvents().length, 3,
    "2 new console events registered for time/timeEnd");
  clearAndCheckStorage();

  console.timeStamp("timestamp-label");
  is(storage.getEvents().length, 2,
    "1 new console event registered for timeStamp");
  clearAndCheckStorage();

  // Check that console.clear() clears previous clear messages
  clearAndCheckStorage();

</script>
</body>
</html>
