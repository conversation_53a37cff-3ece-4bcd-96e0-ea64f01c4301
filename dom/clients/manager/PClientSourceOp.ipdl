/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

include protocol PClientSource;
include ClientIPCTypes;

namespace mozilla {
namespace dom {

[ManualDealloc, ChildImpl=virtual, ParentImpl=virtual]
protocol PClientSourceOp
{
  manager PClientSource;

parent:
  async __delete__(ClientOpResult aResult);
};

} // namespace dom
} // namespace mozilla
