/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */

include protocol PBackground;
include protocol PClientHandle;
include protocol PClient<PERSON>anagerOp;
include protocol PClient<PERSON><PERSON>gateOp;
include protocol PClientSource;
include ClientIPCTypes;

namespace mozilla {
namespace dom {

[ChildImpl=virtual, ParentImpl=virtual]
sync protocol PC<PERSON><PERSON>anager
{
  manager PBackground;

  manages PClientHandle;
  manages PClientManagerOp;
  manages PClientNavigateOp;
  manages PClientSource;

parent:
  async Teardown();

  async PClientHandle(IPCClientInfo aClientInfo);
  async PClientManagerOp(ClientOpConstructorArgs aArgs);
  async PClientSource(ClientSourceConstructorArgs aArgs);

  async ExpectFutureClientSource(IPCClientInfo aClientInfo);
  async ForgetFutureClientSource(IPCClientInfo aClientInfo);

child:
  async PClientNavigateOp(ClientNavigateOpConstructorArgs aArgs);

  async __delete__();
};

} // namespace dom
} // namespace mozilla
