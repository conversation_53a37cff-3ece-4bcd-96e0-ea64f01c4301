# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

EXPORTS.mozilla.dom += [
    "ClientChannelHelper.h",
    "ClientHandle.h",
    "ClientHandleParent.h",
    "ClientInfo.h",
    "ClientIPCUtils.h",
    "ClientManager.h",
    "ClientManagerActors.h",
    "ClientManagerService.h",
    "ClientOpenWindowUtils.h",
    "ClientOpPromise.h",
    "ClientSource.h",
    "ClientState.h",
    "ClientThing.h",
]

UNIFIED_SOURCES += [
    "ClientChannelHelper.cpp",
    "ClientHandle.cpp",
    "ClientHandleChild.cpp",
    "ClientHandleOpChild.cpp",
    "ClientHandleOpParent.cpp",
    "ClientHandleParent.cpp",
    "ClientInfo.cpp",
    "ClientManager.cpp",
    "ClientManagerActors.cpp",
    "ClientManagerChild.cpp",
    "ClientManagerOpChild.cpp",
    "ClientManagerOpParent.cpp",
    "ClientManagerParent.cpp",
    "ClientManagerService.cpp",
    "ClientNavigateOpChild.cpp",
    "ClientNavigateOpParent.cpp",
    "ClientOpenWindowUtils.cpp",
    "ClientPrincipalUtils.cpp",
    "ClientSource.cpp",
    "ClientSourceChild.cpp",
    "ClientSourceOpChild.cpp",
    "ClientSourceOpParent.cpp",
    "ClientSourceParent.cpp",
    "ClientState.cpp",
    "ClientValidation.cpp",
]

IPDL_SOURCES += [
    "ClientIPCTypes.ipdlh",
    "PClientHandle.ipdl",
    "PClientHandleOp.ipdl",
    "PClientManager.ipdl",
    "PClientManagerOp.ipdl",
    "PClientNavigateOp.ipdl",
    "PClientSource.ipdl",
    "PClientSourceOp.ipdl",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"

MOCHITEST_MANIFESTS += []

BROWSER_CHROME_MANIFESTS += []

XPCSHELL_TESTS_MANIFESTS += []
