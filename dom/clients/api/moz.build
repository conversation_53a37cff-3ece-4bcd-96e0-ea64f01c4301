# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

EXPORTS.mozilla.dom += [
    "Client.h",
    "Clients.h",
]

UNIFIED_SOURCES += [
    "Client.cpp",
    "Clients.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"

MOCHITEST_MANIFESTS += []

BROWSER_CHROME_MANIFESTS += []

XPCSHELL_TESTS_MANIFESTS += []
