/* -*- Mode: IDL; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/.
 */

interface nsIDOMProcessChild;
interface nsIDOMProcessParent;
interface Principal;

/**
 * An optimized QueryInterface method, generated by generateQI.
 *
 * For JS callers, this behaves like a normal QueryInterface function. When
 * called with a supported interface, it returns its `this` object. When
 * called with an unsupported interface, it throws NS_ERROR_NO_INTERFACE.
 *
 * C++ callers use a fast path, and never call the JSAPI or WebIDL methods of
 * this object.
 */
[ChromeOnly, Exposed=Window]
interface MozQueryInterface {
  [Throws]
  legacycaller any (any aIID);
};

/**
 * Options for a marker created with the addProfilerMarker method.
 * All fields are optional.
 */
dictionary ProfilerMarkerOptions {
  // A timestamp to use as the start time of the marker.
  // If no start time is provided, the marker will have no duration.
  // In window and ChromeWorker contexts, use a timestamp from
  // `performance.now()`.
  // In JS modules, use `Cu.now()` to get a timestamp.
  DOMHighResTimeStamp startTime = 0;

  // If captureStack is true, a profiler stack will be captured and associated
  // with the marker.
  boolean captureStack = false;

  // Markers are created by default in the JavaScript category, but this can be
  // overridden.
  // Examples of correct values: "JavaScript", "Test", "Other", ...
  // See ProfilingCategoryList.h for the complete list of valid values.
  // Using an unrecognized value will set the category to "Other".
  ByteString category = "JavaScript";

  // Inner window ID to use for the marker. If the global object is a window,
  // the inner window id of the marker will be set automatically.
  // If a marker that relates to a specific window is added from a JS module,
  // setting the inner window id will allow the profiler to show which window
  // the marker applies to.
  unsigned long long innerWindowId = 0;
};

dictionary InteractionData {
  unsigned long interactionCount = 0;
  unsigned long interactionTimeInMilliseconds = 0;
  unsigned long scrollingDistanceInPixels = 0;
};

/**
 * Confidence value of credit card fields. This is used by the native
 * Fathom Credit Card model to return the score to JS.
 */
dictionary FormAutofillConfidences {
  double ccNumber = 0;
  double ccName = 0;
};

/**
 * A collection of static utility methods that are only exposed to system code.
 * This is exposed in all the system globals where we can expose stuff by
 * default, so should only include methods that are **thread-safe**.
 */
[ChromeOnly, Exposed=(Window,Worker)]
namespace ChromeUtils {
  /**
   * Get the |NodeId| for the given JS Object.
   * |NodeId| is the identifier of |JS::ubi::Node|.
   */
  NodeId getObjectNodeId(object obj);

  /**
   * Serialize a snapshot of the heap graph, as seen by |JS::ubi::Node| and
   * restricted by |boundaries|, and write it to the provided file path.
   *
   * @param boundaries        The portion of the heap graph to write.
   *
   * @returns                 The path to the file the heap snapshot was written
   *                          to. This is guaranteed to be within the temp
   *                          directory and its file name will match the regexp
   *                          `\d+(\-\d+)?\.fxsnapshot`.
   */
  [Throws]
  DOMString saveHeapSnapshot(optional HeapSnapshotBoundaries boundaries = {});

  /**
   * This is the same as saveHeapSnapshot, but with a different return value.
   *
   * @returns                 The snapshot ID of the file. This is the file name
   *                          without the temp directory or the trailing
   *                          `.fxsnapshot`.
   */
  [Throws]
  DOMString saveHeapSnapshotGetId(optional HeapSnapshotBoundaries boundaries = {});

  /**
   * Deserialize a core dump into a HeapSnapshot.
   *
   * @param filePath          The file path to read the heap snapshot from.
   */
  [Throws, NewObject]
  HeapSnapshot readHeapSnapshot(DOMString filePath);

  /**
   * Efficient way to know if DevTools are active in the current process.
   *
   * This doesn't help know what particular context is being debugged,
   * but can help strip off code entirely when DevTools aren't used at all.
   *
   * BrowsingContext.isWatchedByDevTools is a more precise way to know
   * when one precise tab is being debugged.
   */
  boolean isDevToolsOpened();

  /**
   * API exposed to DevTools JS code in order to know when devtools are being active in the current process.
   *
   * This API counts the number of calls to these methods, allowing to track many DevTools instances.
   */
  undefined notifyDevToolsOpened();
  undefined notifyDevToolsClosed();


  /**
   * Return the keys in a weak map.  This operation is
   * non-deterministic because it is affected by the scheduling of the
   * garbage collector and the cycle collector.
   *
   * @param aMap weak map or other JavaScript value
   * @returns If aMap is a weak map object, return the keys of the weak
   *          map as an array.  Otherwise, return undefined.
   */
  [Throws, NewObject]
  any nondeterministicGetWeakMapKeys(any map);

  /**
   * Return the keys in a weak set.  This operation is
   * non-deterministic because it is affected by the scheduling of the
   * garbage collector and the cycle collector.
   *
   * @param aSet weak set or other JavaScript value
   * @returns If aSet is a weak set object, return the keys of the weak
   *          set as an array.  Otherwise, return undefined.
   */
  [Throws, NewObject]
  any nondeterministicGetWeakSetKeys(any aSet);

  /**
   * Converts a buffer to a Base64 URL-encoded string per RFC 4648.
   *
   * @param source The buffer to encode.
   * @param options Additional encoding options.
   * @returns The encoded string.
   */
  [Throws]
  ByteString base64URLEncode(BufferSource source,
                             Base64URLEncodeOptions options);

  /**
   * Decodes a Base64 URL-encoded string per RFC 4648.
   *
   * @param string The string to decode.
   * @param options Additional decoding options.
   * @returns The decoded buffer.
   */
  [Throws, NewObject]
  ArrayBuffer base64URLDecode(ByteString string,
                              Base64URLDecodeOptions options);

  /**
   * Cause the current process to fatally crash unless the given condition is
   * true. This is similar to MOZ_RELEASE_ASSERT in C++ code.
   *
   * WARNING: This message is included publicly in the crash report, and must
   * not contain private information.
   *
   * Crash report will be augmented with the current JS stack information.
   */
  undefined releaseAssert(boolean condition,
                          optional DOMString message = "<no message>");

#ifdef NIGHTLY_BUILD

  /**
   * If the chrome code has thrown a JavaScript Dev Error
   * in the current JSRuntime. the first such error, or `undefined`
   * otherwise.
   *
   * A JavaScript Dev Error is an exception thrown by JavaScript
   * code that matches both conditions:
   * - it was thrown by chrome code;
   * - it is either a `ReferenceError`, a `TypeError` or a `SyntaxError`.
   *
   * Such errors are stored regardless of whether they have been
   * caught.
   *
   * This mechanism is designed to help ensure that the code of
   * Firefox is free from Dev Errors, even if they are accidentally
   * caught by clients.
   *
   * The object returned is not an exception. It has fields:
   * - DOMString stack
   * - DOMString filename
   * - DOMString lineNumber
   * - DOMString message
   */
  [Throws]
  readonly attribute any recentJSDevError;

  /**
   * Reset `recentJSDevError` to `undefined` for the current JSRuntime.
   */
  undefined clearRecentJSDevError();
#endif // NIGHTLY_BUILD

  /**
   * Clears the stylesheet cache by site. This includes associated
   * state-partitioned cache.
   */
  undefined clearStyleSheetCacheBySite(UTF8String schemelessSite, optional OriginAttributesPatternDictionary pattern = {});

  /**
   * Clears the stylesheet cache by principal.
   */
  undefined clearStyleSheetCacheByPrincipal(Principal principal);

  /**
   * Clears the entire stylesheet cache.
   */
  undefined clearStyleSheetCache();

  /**
   * Clears the Messaging Layer Security state by schemeless site.
   * This includes associated state-partitioned cache.
   */
  [Throws]
  undefined clearMessagingLayerSecurityStateBySite(UTF8String schemelessSite, optional OriginAttributesPatternDictionary pattern = {});

  /**
   * Clears the Messaging Layer Security state by principal.
   */
  [Throws]
  undefined clearMessagingLayerSecurityStateByPrincipal(Principal principal);

  /**
   * Clears all Messaging Layer Security related state across domains
   */
  [Throws]
  undefined clearMessagingLayerSecurityState();

  /**
   * Clears the JavaScript cache by schemeless site. This includes associated
   * state-partitioned cache.
   */
  undefined clearScriptCacheBySite(UTF8String schemelessSite, optional OriginAttributesPatternDictionary pattern = {});

  /**
   * Clears the JavaScript cache by principal.
   */
  undefined clearScriptCacheByPrincipal(Principal principal);

  /**
   * Clears the entire JavaScript cache.
   */
  undefined clearScriptCache();

  /**
   * If the profiler is currently running and recording the current thread,
   * add a marker for the current thread. No-op otherwise.
   *
   * @param name              The name of the marker.
   * @param options           Either a timestamp to use as the start time of the
   *                          marker, or a ProfilerMarkerOptions object that can
   *                          contain startTime, captureStack or category fields.
   *                          If this parameter is omitted, the marker will have
   *                           no duration.
   *                          In window and ChromeWorker contexts, use a
   *                          timestamp from `performance.now()`.
   *                          In JS modules, use `Cu.now()` to get a timestamp.
   * @param text              Text to associate with the marker.
   */
  undefined addProfilerMarker(UTF8String name,
                              optional (ProfilerMarkerOptions or DOMHighResTimeStamp) options = {},
                              optional UTF8String text);

  /**
   * Return the symbolic name of any given XPCOM error code (nsresult):
   * "NS_OK", "NS_ERROR_FAILURE",...
   */
  UTF8String getXPCOMErrorName(unsigned long aErrorCode);

  /**
   * Return a fractional number representing the current time (in milliseconds from the Epoch).
   * Should be JS_Now()/1000 so that it can be compared to Date.now in Javascript.
   */
  double dateNow();

  /**
   * Defines a getter on a specified object that will be created upon first
   * use.
   *
   * @param aTarget
   *        The object to define the lazy getter on.
   * @param aName
   *        The name of the getter to define on aTarget.
   * @param aLambda
   *        A function that returns what the getter should return.  This will
   *        only ever be called once.
   */
  [Throws]
  undefined defineLazyGetter(object aTarget, any aName, object aLambda);


#ifdef XP_UNIX
  /**
   * Return platform-specific libc constants, such as errno values.
   */
  LibcConstants getLibcConstants();
#endif

#ifdef MOZ_WMF_CDM
  /**
   * Returns the information about all Media Foundation based content decryption
   * modules, which would include key system names and their capabilities.
   */
  [NewObject]
  Promise<sequence<CDMInformation>> getWMFContentDecryptionModuleInformation();
#endif

  /**
   * Returns the information about the GMP based content decryption
   * modules, which would include key system names and their capabilities.
   */
  [NewObject]
  Promise<sequence<CDMInformation>> getGMPContentDecryptionModuleInformation();

  /**
   * Synchronously loads and evaluates the JS module source located at
   * 'aResourceURI'.
   *
   * @param aResourceURI A resource:// URI string to load the module from.
   * @param aOption An option to specify where to load the module into.
   * @returns the module's namespace object.
   *
   * The implementation maintains a hash of aResourceURI->global obj.
   * Subsequent invocations of import with 'aResourceURI' pointing to
   * the same file will not cause the module to be re-evaluated.
   *
   * In worker threads, aOption is required and only { global: "current" } and
   * { global: "contextual" } are supported.
   *
   * In DevTools distinct global, aOptions.global is reuiqred.
   */
  [Throws]
  object importESModule(DOMString aResourceURI,
                        optional ImportESModuleOptionsDictionary aOptions = {});

  /**
   * Defines properties on the given target which lazily imports a ES module
   * when accessed.
   *
   * @param aTarget The target object on which to define the property.
   * @param aModules An object with a property for each module property to be
   *                 imported, where the property name is the name of the
   *                 imported symbol and the value is the module URI.
   * @param aOptions An option to specify where to load the module into.
   *
   * In worker threads, aOptions is required and only { global: "current" } and
   * { global: "contextual" } are supported.
   *
   * In DevTools distinct global, aOptions.global is required.
   */
  [Throws]
  undefined defineESModuleGetters(object aTarget, object aModules,
                                  optional ImportESModuleOptionsDictionary aOptions = {});

  /**
   * IF YOU ADD NEW METHODS HERE, MAKE SURE THEY ARE THREAD-SAFE.
   */
};

/**
 * Additional ChromeUtils methods that are _not_ thread-safe, and hence not
 * exposed in workers.
 */
[Exposed=Window]
partial namespace ChromeUtils {
  /**
   * A helper that converts OriginAttributesDictionary to a opaque suffix string.
   *
   * @param originAttrs       The originAttributes from the caller.
   */
  ByteString
  originAttributesToSuffix(optional OriginAttributesDictionary originAttrs = {});

  /**
   * Returns true if the members of |originAttrs| match the provided members
   * of |pattern|.
   *
   * @param originAttrs       The originAttributes under consideration.
   * @param pattern           The pattern to use for matching.
   */
  boolean
  originAttributesMatchPattern(optional OriginAttributesDictionary originAttrs = {},
                               optional OriginAttributesPatternDictionary pattern = {});

  /**
   * Returns an OriginAttributesDictionary with values from the |origin| suffix
   * and unspecified attributes added and assigned default values.
   *
   * @param origin            The origin URI to create from.
   * @returns                 An OriginAttributesDictionary with values from
   *                          the origin suffix and unspecified attributes
   *                          added and assigned default values.
   */
  [Throws]
  OriginAttributesDictionary
  createOriginAttributesFromOrigin(DOMString origin);

  /**
   * Returns an OriginAttributesDictionary with values from the origin |suffix|
   * and unspecified attributes added and assigned default values.
   *
   * @param suffix            The origin suffix to create from.
   * @returns                 An OriginAttributesDictionary with values from
   *                          the origin suffix and unspecified attributes
   *                          added and assigned default values.
   */
  [Throws]
  OriginAttributesDictionary
  CreateOriginAttributesFromOriginSuffix(DOMString suffix);

  /**
   * Returns an OriginAttributesDictionary that is a copy of |originAttrs| with
   * unspecified attributes added and assigned default values.
   *
   * @param originAttrs       The origin attributes to copy.
   * @returns                 An OriginAttributesDictionary copy of |originAttrs|
   *                          with unspecified attributes added and assigned
   *                          default values.
   */
  OriginAttributesDictionary
  fillNonDefaultOriginAttributes(optional OriginAttributesDictionary originAttrs = {});

  /**
   * Returns true if the 2 OriginAttributes are equal.
   */
  boolean
  isOriginAttributesEqual(optional OriginAttributesDictionary aA = {},
                          optional OriginAttributesDictionary aB = {});

  /**
   * Returns the base domain portion of a given partitionKey.
   * Returns the empty string for an empty partitionKey.
   * Throws for invalid partition keys.
   */
  [Throws]
  DOMString
  getBaseDomainFromPartitionKey(DOMString partitionKey);

  /**
   * Returns the partitionKey for a given subresourceURL given its top-level URL
   * and whether or not it is in a foreign context.
   *
   * The function will treat the topLevelURL as a first party and construct the
   * partitionKey according to the scheme, site and port in the URL. It will also
   * include information about the subresource and whether or not this is a foreign
   * request in the partition key.
   *
   * Throws for invalid urls, if the Third Party Service is unavailable, or if the
   * combination of inputs is impossible.
   */
  [Throws]
  DOMString
  getPartitionKeyFromURL(DOMString topLevelUrl, DOMString subresourceUrl, optional boolean foreignContext);

  /**
   * Loads and compiles the script at the given URL and returns an object
   * which may be used to execute it repeatedly, in different globals, without
   * re-parsing.
   */
  [NewObject]
  Promise<PrecompiledScript>
  compileScript(DOMString url, optional CompileScriptOptionsDictionary options = {});

  /**
   * Returns an optimized QueryInterface method which, when called from
   * JavaScript, acts as an ordinary QueryInterface function call, and when
   * called from XPConnect, circumvents JSAPI entirely.
   *
   * The list of interfaces may include a mix of JS ID objects and interface
   * name strings.
   *
   * nsISupports is implicitly supported, and must not be included in the
   * interface list.
   */
  [Affects=Nothing, NewObject]
  MozQueryInterface generateQI(sequence<any> interfaces);

  /**
   * Waive Xray on a given value. Identity op for primitives.
   */
  [Throws]
  any waiveXrays(any val);

  /**
   * Strip off Xray waivers on a given value. Identity op for primitives.
   */
  [Throws]
  any unwaiveXrays(any val);

  /**
   * Gets the name of the JSClass of the object.
   *
   * if |unwrap| is true, all wrappers are unwrapped first. Unless you're
   * specifically trying to detect whether the object is a proxy, this is
   * probably what you want.
   */
  DOMString getClassName(object obj, optional boolean unwrap = true);

  /**
   * Returns whether the object is a DOM object.
   *
   * if |unwrap| is true, all wrappers are unwrapped first. Unless you're
   * specifically trying to detect whether the object is a proxy, this is
   * probably what you want.
   */
  boolean isDOMObject(object obj, optional boolean unwrap = true);

  /**
   * Returns whether |str| follows the Date Time String Format.
   */
  boolean isISOStyleDate(UTF8String str);

  /**
   * Clones the properties of the given object into a new object in the given
   * target compartment (or the caller compartment if no target is provided).
   * Property values themeselves are not cloned.
   *
   * Ignores non-enumerable properties, properties on prototypes, and properties
   * with getters or setters.
   */
  [Throws]
  object shallowClone(object obj, optional object? target = null);

  /**
   * Dispatches the given callback to the main thread when it would be
   * otherwise idle. Similar to Window.requestIdleCallback, but not bound to a
   * particular DOM windw.
   */
  [Throws]
  undefined idleDispatch(IdleRequestCallback callback,
                         optional IdleRequestOptions options = {});

  /**
   * Synchronously loads and evaluates the js file located at
   * 'aResourceURI' with a new, fully privileged global object.
   *
   * If `aTargetObj` is specified all properties exported by the module are
   * copied to that object. This is deprecated and should not be used in
   * new code.
   *
   * @param aResourceURI A resource:// URI string to load the module from.
   * @param aTargetObj the object to install the exported properties on or null.
   * @returns the module code's global object.
   *
   * The implementation maintains a hash of aResourceURI->global obj.
   * Subsequent invocations of import with 'aResourceURI' pointing to
   * the same file will not cause the module to be re-evaluated, but
   * the symbols in EXPORTED_SYMBOLS will be exported into the
   * specified target object and the global object returned as above.
   *
   * TODO: Remove this once m-c, c-c, and out-of-tree code migrations finish
   *       (bug 1881888).
   */
  [Throws]
  object import(UTF8String aResourceURI, optional object aTargetObj);

  /**
   * Defines a property on the given target which lazily imports a JavaScript
   * module when accessed.
   *
   * The first time the property is accessed, the module at the given URL is
   * imported, and the property is replaced with the module's exported symbol
   * of the same name.
   *
   * Some points to note when using this utility:
   *
   * - The cached module export is always stored on the `this` object that was
   *   used to access the getter. This means that if you define the lazy
   *   getter on a prototype, the module will be re-imported every time the
   *   property is accessed on a new instance.
   *
   * - The getter property may be overwritten by simple assignment, but as
   *   with imports, the new property value is always defined on the `this`
   *   object that the setter is called with.
   *
   * - If the module import fails, the getter will throw an error, and the
   *   property will not be replaced. Each subsequent attempt to access the
   *   getter will attempt to re-import the object, which will likely continue
   *   to result in errors.
   *
   * @param target The target object on which to define the property.
   * @param id The name of the property to define, and of the symbol to
   *           import.
   * @param resourceURI The resource URI of the module, as passed to
   *                    ChromeUtils.import.
   */
  [Throws]
  undefined defineModuleGetter(object target, DOMString id, DOMString resourceURI);

  /**
   * Returns the scripted location of the first ancestor stack frame with a
   * principal which is subsumed by the given principal. If no such frame
   * exists on the call stack, returns null.
   */
  object? getCallerLocation(Principal principal);

  /**
   * Creates a JS Error object with the given message and stack.
   *
   * If a stack object is provided, the error object is created in the global
   * that it belongs to.
   */
  [Throws]
  object createError(DOMString message, optional object? stack = null);

  /**
   * Set the collection of specific detailed performance timing information.
   * Selecting 0 for the mask will end existing collection. All metrics that
   * are chosen will be cleared after updating the mask.
   *
   * @param aCollectionMask A bitmask where each bit corresponds to a metric
   *        to be collected as listed in PerfStats::Metric.
   */
  undefined setPerfStatsCollectionMask(unsigned long long aCollectionMask);

  /**
   * Collect results of detailed performance timing information.
   * The output is a JSON string containing performance timings.
   */
  [NewObject]
  Promise<DOMString> collectPerfStats();

  /**
  * Returns a Promise containing all processes info
  */
  [NewObject]
  Promise<ParentProcInfoDictionary> requestProcInfo();

  /**
   * For testing purpose.
   */
  [ChromeOnly]
  boolean vsyncEnabled();

  [ChromeOnly, Throws]
  boolean hasReportingHeaderForOrigin(DOMString aOrigin);

  [ChromeOnly]
  PopupBlockerState getPopupControlState();

  /**
   * Milliseconds from the last iframe loading an external protocol.
   */
  [ChromeOnly]
  double lastExternalProtocolIframeAllowed();

  /**
   * For testing purpose we need to reset this value.
   */
  [ChromeOnly]
  undefined resetLastExternalProtocolIframeAllowed();

  /**
   * For webdriver consistency purposes, we need to be able to end a wheel
   * transaction from the browser chrome.
   */
  [ChromeOnly]
  undefined endWheelTransaction();

  /**
   * Register a new toplevel window global actor. This method may only be
   * called in the parent process. |name| must be globally unique.
   *
   * See JSWindowActor.webidl for WindowActorOptions fields documentation.
   */
  [ChromeOnly, Throws]
  undefined registerWindowActor(UTF8String aName, optional WindowActorOptions aOptions = {});

  [ChromeOnly]
  undefined unregisterWindowActor(UTF8String aName);

  /**
   * Register a new toplevel content global actor. This method may only be
   * called in the parent process. |name| must be globally unique.
   *
   * See JSProcessActor.webidl for ProcessActorOptions fields documentation.
   */
  [ChromeOnly, Throws]
  undefined registerProcessActor(UTF8String aName, optional ProcessActorOptions aOptions = {});

  [ChromeOnly]
  undefined unregisterProcessActor(UTF8String aName);

  [ChromeOnly]
  // aError should a nsresult.
  boolean isClassifierBlockingErrorCode(unsigned long aError);

  /**
   * If leak detection is enabled, print a note to the leak log that this
   * process will intentionally crash. This should be called only on child
   * processes for testing purpose.
   */
  [ChromeOnly, Throws]
  undefined privateNoteIntentionalCrash();

  /**
   * nsIDOMProcessChild for the current process.
   */
  [ChromeOnly]
  readonly attribute nsIDOMProcessChild? domProcessChild;

  /**
   * nsIDOMProcessParent for all processes.
   *
   * The first is for the parent process and all the next are for the content
   * processes.
   */
  [Throws, ChromeOnly]
  sequence<nsIDOMProcessParent> getAllDOMProcesses();

  /**
   * Returns a record of user interaction data. Currently only typing,
   * but will include scrolling and potentially other metrics.
   *
   * Valid keys: "Typing"
   */
  [Throws, ChromeOnly]
  record<DOMString, InteractionData> consumeInteractionData();

  /**
   * Returns a record of user scrolling interactions collected from content processes.
   *
   * Valid keys: "Scrolling"
   */
  [NewObject]
  Promise<InteractionData> collectScrollingData();

  [Throws]
  sequence<FormAutofillConfidences> getFormAutofillConfidences(sequence<Element> elements);

  /**
   * Returns whether the background of the element is dark.
   */
  boolean isDarkBackground(Element element);

  /**
   * Starts the JSOracle process for ORB JavaScript validation, if it hasn't started already.
   */
  undefined ensureJSOracleStarted();

  /**
   * The number of currently alive utility processes.
   */
  [ChromeOnly]
  readonly attribute unsigned long aliveUtilityProcesses;

  /**
   * Get a list of all possible Utility process Actor Names ; mostly useful to
   * perform testing and ensure about:processes display is sound and misses no
   * actor name.
   */
  [ChromeOnly]
  sequence<UTF8String> getAllPossibleUtilityActorNames();

  boolean shouldResistFingerprinting(JSRFPTarget target,
                                     unsigned long long? overriddenFingerprintingSettings);
};

/*
 * This type is a WebIDL representation of mozilla::ProcType.
 * These must match the similar ones in E10SUtils.sys.mjs, RemoteTypes.h,
 * ProcInfo.h and ChromeUtils.cpp
 */
enum WebIDLProcType {
 "web",
 "webIsolated",
 "file",
 "extension",
 "privilegedabout",
 "privilegedmozilla",
 "withCoopCoep",
 "webServiceWorker",
 "browser",
 "ipdlUnitTest",
 "gmpPlugin",
 "gpu",
 "vr",
 "rdd",
 "socket",
 "inference",
#ifdef MOZ_ENABLE_FORKSERVER
 "forkServer",
#endif
 "utility",
 "preallocated",
 "unknown",
};

/**
 * These dictionaries hold information about Firefox running processes and
 * threads.
 *
 * See widget/ProcInfo.h for fields documentation.
 */
dictionary ThreadInfoDictionary {
  long long tid = 0;
  DOMString name = "";
  unsigned long long cpuCycleCount = 0;
  unsigned long long cpuTime = 0;
};

dictionary WindowInfoDictionary {
  // Window ID, as known to the parent process.
  unsigned long long outerWindowId = 0;

  // URI of the document loaded in the window.
  URI? documentURI = null;

  // Title of the document loaded in the window.
  // Commonly empty for subframes.
  DOMString documentTitle = "";

  // `true` if this window is the root for the process.
  boolean isProcessRoot = false;

  // `true` if this is loaded in the same process as the parent, `false` otherwise.
  boolean isInProcess = false;
};

/*
 * Add new entry to WebIDLUtilityActorName here and update accordingly
 * UtilityActorNameToWebIDL in dom/base/ChromeUtils.cpp as well as
 * UtilityActorName in toolkit/components/processtools/ProcInfo.h
 */
enum WebIDLUtilityActorName {
  "unknown",
  "audioDecoder_Generic",
  "audioDecoder_AppleMedia",
  "audioDecoder_WMF",
  "mfMediaEngineCDM",
  "jSOracle",
  "windowsUtils",
  "windowsFileDialog",
};

dictionary UtilityActorsDictionary {
  WebIDLUtilityActorName actorName = "unknown";
};

/**
 * Information on a child process.
 *
 * # Limitation
 *
 * If we lose a race with a process or thread attempting to close the
 * target process, not all information is available.
 *
 * Missing information will generally have its default value.
 */
dictionary ChildProcInfoDictionary {
  // --- System info

  // The cross-process descriptor for this process.
  long long pid = 0;

  // The best end-user measure for "memory used" that we can obtain without
  // triggering expensive computations. The value is in bytes.
  // On Mac and Linux this matches the values shown by the system monitors.
  // On Windows this will return the Commit Size.
  unsigned long long memory = 0;

  // Total CPU time spent by the process, in ns.
  unsigned long long cpuTime = 0;

  // Total CPU cycles used by this process.
  // On Windows where the resolution of CPU timings is 16ms, this can
  // be used to determine if a process is idle or slightly active.
  unsigned long long cpuCycleCount = 0;

  // Thread information for this process.
  sequence<ThreadInfoDictionary> threads = [];

  // --- Firefox info

  // Internal-to-Firefox process identifier.
  unsigned long long childID = 0;

  // The origin of the process, e.g. the subset of domain names
  // that this subset serves.
  UTF8String origin = "";

  // Type of this child process.
  WebIDLProcType type = "web";

  // The windows implemented by this process.
  sequence<WindowInfoDictionary> windows = [];

  // The utility process list of actors if any
  sequence<UtilityActorsDictionary> utilityActors = [];
};

/**
 * Information on the parent process.
 */
dictionary ParentProcInfoDictionary {
  // --- System info

  // The cross-process descriptor for this process.
  long long pid = 0;

  // The best end-user measure for "memory used" that we can obtain without
  // triggering expensive computations. The value is in bytes.
  // On Mac and Linux this matches the values shown by the system monitors.
  // On Windows this will return the Commit Size.
  unsigned long long memory = 0;

  // Total CPU time spent by the process, in ns.
  unsigned long long cpuTime = 0;

  // Total CPU cycles used by this process.
  // On Windows where the resolution of CPU timings is 16ms, this can
  // be used to determine if a process is idle or slightly active.
  unsigned long long cpuCycleCount = 0;

  // Thread information for this process.
  sequence<ThreadInfoDictionary> threads = [];

  // Information on children processes.
  sequence<ChildProcInfoDictionary> children = [];

  // --- Firefox info
  // Type of this parent process.
  // As of this writing, this is always `browser`.
  WebIDLProcType type = "browser";
};

/**
 * Used by principals and the script security manager to represent origin
 * attributes. The first dictionary is designed to contain the full set of
 * OriginAttributes, the second is used for pattern-matching (i.e. does this
 * OriginAttributesDictionary match the non-empty attributes in this pattern).
 *
 * IMPORTANT: If you add any members here, you need to do the following:
 * (1) Add them to both dictionaries.
 * (2) Update the methods on mozilla::OriginAttributes, including equality,
 *     serialization, deserialization, and inheritance.
 * (3) Update the methods on mozilla::OriginAttributesPattern, including matching.
 */
[GenerateInitFromJSON, GenerateEqualityOperator]
dictionary OriginAttributesDictionary {
  unsigned long userContextId = 0;
  unsigned long privateBrowsingId = 0;
  DOMString firstPartyDomain = "";
  DOMString geckoViewSessionContextId = "";
  DOMString partitionKey = "";
};

[GenerateInitFromJSON, GenerateToJSON]
dictionary OriginAttributesPatternDictionary {
  unsigned long userContextId;
  unsigned long privateBrowsingId;
  DOMString firstPartyDomain;
  DOMString geckoViewSessionContextId;
  // partitionKey takes precedence over partitionKeyPattern.
  DOMString partitionKey;
  PartitionKeyPatternDictionary partitionKeyPattern;
};

dictionary PartitionKeyPatternDictionary {
  DOMString scheme;
  DOMString baseDomain;
  long port;
  boolean foreignByAncestorContext;
};

dictionary CompileScriptOptionsDictionary {
  /**
   * The character set from which to decode the script.
   */
  DOMString charset = "utf-8";

  /**
   * The filename to associate with the script. Defaults to the source's URL.
   */
  DOMString filename;

  /**
   * If true, certain parts of the script may be parsed lazily, the first time
   * they are used, rather than eagerly parsed at load time.
   */
  boolean lazilyParse = false;

  /**
   * If true, the script will be compiled so that its last expression will be
   * returned as the value of its execution. This makes certain types of
   * optimization impossible, and disables the JIT in many circumstances, so
   * should not be used when not absolutely necessary.
   */
  boolean hasReturnValue = false;
};

/**
 * Where the modules are loaded into with importESModule and
 * defineESModuleGetters.
 */
enum ImportESModuleTargetGlobal {
  /**
   * Load into the shared system global.
   * This is the default value.
   */
  "shared",

  /**
   * Load into a distinct system global for DevTools, so that the DevTools can
   * load a distinct set of modules and do not interfere with its debuggee.
   */
  "devtools",

  /**
   * If the current global is DevTools' distinct system global, load into the
   * DevTools' distinct system global.
   * If the current thread is worker thread, load into the current global.
   * Otherwise load into the shared system global.
   *
   * This is a temporary workaround until DevTools modules are ESMified.
   */
  "contextual",

  /**
   * Load into current global.
   *
   * This can be used for any global.  If this is used for shared global or
   * devtools global, this has the same effect as "shared" or "devtools".
   */
  "current",
};

dictionary ImportESModuleOptionsDictionary {
  // This field is required for importESModule and defineESModuleGetters in
  // DevTools distinct global.
  ImportESModuleTargetGlobal global;
};

/**
 * A JS object whose properties specify what portion of the heap graph to
 * write. The recognized properties are:
 *
 * * globals: [ global, ... ]
 *   Dump only nodes that either:
 *   - belong in the compartment of one of the given globals;
 *   - belong to no compartment, but do belong to a Zone that contains one of
 *     the given globals;
 *   - are referred to directly by one of the last two kinds of nodes; or
 *   - is the fictional root node, described below.
 *
 * * debugger: Debugger object
 *   Like "globals", but use the Debugger's debuggees as the globals.
 *
 * * runtime: true
 *   Dump the entire heap graph, starting with the JSRuntime's roots.
 *
 * One, and only one, of these properties must exist on the boundaries object.
 *
 * The root of the dumped graph is a fictional node whose ubi::Node type name is
 * "CoreDumpRoot". If we are dumping the entire ubi::Node graph, this root node
 * has an edge for each of the JSRuntime's roots. If we are dumping a selected
 * set of globals, the root has an edge to each global, and an edge for each
 * incoming JS reference to the selected Zones.
 */
dictionary HeapSnapshotBoundaries {
  sequence<object> globals;
  object           debugger;
  boolean          runtime;
};

dictionary Base64URLEncodeOptions {
  /** Specifies whether the output should be padded with "=" characters. */
  required boolean pad;
};

enum Base64URLDecodePadding {
  /**
   * Fails decoding if the input is unpadded. RFC 4648, section 3.2 requires
   * padding, unless the referring specification prohibits it.
   */
  "require",

  /** Tolerates padded and unpadded input. */
  "ignore",

  /**
   * Fails decoding if the input is padded. This follows the strict base64url
   * variant used in JWS (RFC 7515, Appendix C) and HTTP Encrypted
   * Content-Encoding (draft-ietf-httpbis-encryption-encoding-01).
   */
  "reject"
};

dictionary Base64URLDecodeOptions {
  /** Specifies the padding mode for decoding the input. */
  required Base64URLDecodePadding padding;
};

// Keep this in sync with PopupBlocker::PopupControlState!
enum PopupBlockerState {
  "openAllowed",
  "openControlled",
  "openBlocked",
  "openAbused",
  "openOverridden",
};

// Subset of RFPTargets.inc with JS callers.
// New values need to be handled in ChromeUtils::ShouldResistFingerprinting.
enum JSRFPTarget {
  "RoundWindowSize",
  "SiteSpecificZoom",
  "CSSPrefersColorScheme",
};

#ifdef XP_UNIX
dictionary LibcConstants {
  long EPERM;
  long EINTR;
  long EACCES;
  long EAGAIN;
  long EINVAL;
  long ENOSYS;

  long F_SETFD;
  long F_SETFL;

  long FD_CLOEXEC;

  long AT_EACCESS;

  long O_CREAT;
  long O_NONBLOCK;
  long O_WRONLY;

  long POLLIN;
  long POLLOUT;
  long POLLERR;
  long POLLHUP;
  long POLLNVAL;

  long WNOHANG;

#ifdef XP_LINUX
  long PR_CAPBSET_READ;
#endif
};
#endif

dictionary CDMInformation {
  required DOMString keySystemName;
  required DOMString capabilities;
  required boolean clearlead;
  required boolean isHDCP22Compatible;
  required boolean isHardwareDecryption;
};
