# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: Core & HTML")

with Files("ChannelWrapper.webidl"):
    BUG_COMPONENT = ("WebExtensions", "Request Handling")

with Files("Flex.webidl"):
    BUG_COMPONENT = ("Core", "CSS Parsing and Computation")

with Files("HeapSnapshot.webidl"):
    BUG_COMPONENT = ("DevTools", "Memory")

with Files("InspectorUtils.webidl"):
    BUG_COMPONENT = ("DevTools", "Inspector")

with Files("MatchGlob.webidl"):
    BUG_COMPONENT = ("WebExtensions", "General")

with Files("MatchPattern.webidl"):
    BUG_COMPONENT = ("WebExtensions", "General")

with Files("WebExtension*.webidl"):
    BUG_COMPONENT = ("WebExtensions", "General")

with Files("IOUtils.webidl"):
    BUG_COMPONENT = ("Toolkit", "IOUtils and PathUtils")

with Files("PathUtils.webidl"):
    BUG_COMPONENT = ("Toolkit", "IOUtils and PathUtils")

with Files("WidevineCDMManifest.webidl"):
    BUG_COMPONENT = ("Core", "Audio/Video: Playback")

PREPROCESSED_WEBIDL_FILES = [
    "ChromeUtils.webidl",
    "IOUtils.webidl",
]

WEBIDL_FILES = [
    "BrowserSessionStore.webidl",
    "BrowsingContext.webidl",
    "ChannelWrapper.webidl",
    "ChildSHistory.webidl",
    "ChromeNodeList.webidl",
    "ClonedErrorHolder.webidl",
    "CommandEvent.webidl",
    "ConsoleInstance.webidl",
    "CSSCustomPropertyRegisteredEvent.webidl",
    "DebuggerNotification.webidl",
    "DebuggerNotificationObserver.webidl",
    "DebuggerUtils.webidl",
    "DocumentL10n.webidl",
    "DOMCollectedFrames.webidl",
    "DominatorTree.webidl",
    "Flex.webidl",
    "Fluent.webidl",
    "FrameLoader.webidl",
    "Grid.webidl",
    "HeapSnapshot.webidl",
    "ImageText.webidl",
    "InspectorUtils.webidl",
    "IteratorResult.webidl",
    "JSActor.webidl",
    "JSProcessActor.webidl",
    "JSWindowActor.webidl",
    "L10nOverlays.webidl",
    "L10nRegistry.webidl",
    "LoadURIOptions.webidl",
    "MatchGlob.webidl",
    "MatchPattern.webidl",
    "MediaController.webidl",
    "MessageManager.webidl",
    "MozDocumentObserver.webidl",
    "MozSharedMap.webidl",
    "MozStorageAsyncStatementParams.webidl",
    "MozStorageStatementParams.webidl",
    "MozStorageStatementRow.webidl",
    "NetDashboard.webidl",
    "PathUtils.webidl",
    "PermissionSetParameters.webidl",
    "PrecompiledScript.webidl",
    "PromiseDebugging.webidl",
    "SessionStoreUtils.webidl",
    "StripOnShareRule.webidl",
    "StructuredCloneHolder.webidl",
    "StyleSheetApplicableStateChangeEvent.webidl",
    "StyleSheetRemovedEvent.webidl",
    "TelemetryStopwatch.webidl",
    "TreeColumn.webidl",
    "TreeColumns.webidl",
    "TreeContentView.webidl",
    "TreeView.webidl",
    "UserInteraction.webidl",
    "WebExtensionContentScript.webidl",
    "WebExtensionPolicy.webidl",
    "WidevineCDMManifest.webidl",
    "WindowGlobalActors.webidl",
    "WindowRoot.webidl",
    "XULCommandEvent.webidl",
    "XULElement.webidl",
    "XULFrameElement.webidl",
    "XULMenuElement.webidl",
    "XULPopupElement.webidl",
    "XULResizerElement.webidl",
    "XULTextElement.webidl",
    "XULTreeElement.webidl",
]

GENERATED_EVENTS_WEBIDL_FILES = [
    "CSSCustomPropertyRegisteredEvent.webidl",
    "PluginCrashedEvent.webidl",
    "PopupPositionedEvent.webidl",
    "StyleSheetApplicableStateChangeEvent.webidl",
    "StyleSheetRemovedEvent.webidl",
]

if CONFIG["MOZ_BUILD_APP"] != "mobile/android":
    WEBIDL_FILES += [
        "UniFFI.webidl",
    ]

if CONFIG["MOZ_PLACES"]:
    WEBIDL_FILES += [
        "PlacesEvent.webidl",
        "PlacesObservers.webidl",
    ]

if CONFIG["MOZ_WIDGET_TOOLKIT"] == "windows":
    WEBIDL_FILES += [
        "WindowsJumpListShortcutDescription.webidl",
    ]

if CONFIG["MOZ_WEBRTC"]:
    WEBIDL_FILES += [
        "WebrtcGlobalInformation.webidl",
    ]
