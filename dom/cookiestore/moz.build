# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Networking: Cookies")

EXPORTS.mozilla.dom += [
    "CookieChangeEvent.h",
    "CookieStore.h",
    "CookieStoreChild.h",
    "CookieStoreParent.h",
]

UNIFIED_SOURCES += [
    "CookieChangeEvent.cpp",
    "CookieStore.cpp",
    "CookieStoreChild.cpp",
    "CookieStoreNotificationWatcher.cpp",
    "CookieStoreNotificationWatcherWrapper.cpp",
    "CookieStoreNotifier.cpp",
    "CookieStoreParent.cpp",
]

LOCAL_INCLUDES += [
    "../base",
    "../events",
]

IPDL_SOURCES += [
    "PCookieStore.ipdl",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"
