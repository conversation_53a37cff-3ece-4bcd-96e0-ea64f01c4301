# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "DOM: UI Events & Focus Handling")

EXPORTS += [
    "nsBaseCommandController.h",
    "nsCommandManager.h",
    "nsCommandParams.h",
    "nsControllerCommandTable.h",
]

XPIDL_SOURCES += [
    "nsICommandManager.idl",
    "nsICommandParams.idl",
    "nsIControllerCommand.idl",
    "nsIControllerCommandTable.idl",
    "nsIControllerContext.idl",
]

XPIDL_MODULE = "commandhandler"

UNIFIED_SOURCES += [
    "nsBaseCommandController.cpp",
    "nsCommandManager.cpp",
    "nsCommandParams.cpp",
    "nsControllerCommandTable.cpp",
]

LOCAL_INCLUDES += [
    "/dom/base",
]

FINAL_LIBRARY = "xul"
