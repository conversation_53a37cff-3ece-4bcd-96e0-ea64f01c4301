/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*-
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"
#include "nsICommandParams.idl"

/**
 * nsIControllerCommand
 *
 * A generic command interface. You can register an nsIControllerCommand
 * with the nsIControllerCommandTable.
 */

[scriptable, uuid(0eae9a46-1dd2-11b2-aca0-9176f05fe9db)]
interface nsIControllerCommand : nsISupports
{

  /**
   * Returns true if the command is currently enabled. An nsIControllerCommand
   * can implement more than one commands; say, a group of related commands
   * (e.g. delete left/delete right). Because of this, the command name is
   * passed to each method.
   *
   * @param aCommandName  the name of the command for which we want the enabled
   *                      state.
   * @param aCommandContext    a cookie held by the nsIController<PERSON>ommandTable,
   *                  allowing the command to get some context information.
   *                  The contents of this cookie are implementation-defined.
   */
  boolean isCommandEnabled(in string aCommandName, in nsISupports aCommandContext);

  void getCommandStateParams(in string aCommandName, in nsICommandParams aParams, in nsISupports aCommandContext);

  /**
   * Execute the name command.
   *
   * @param aCommandName  the name of the command to execute.
   *
   * @param aCommandContext    a cookie held by the nsIControllerCommandTable,
   *                  allowing the command to get some context information.
   *                  The contents of this cookie are implementation-defined.
   */
  [can_run_script]
  void    doCommand(in string aCommandName, in nsISupports aCommandContext);

  [can_run_script]
  void    doCommandParams(in string aCommandName, in nsICommandParams aParams, in nsISupports aCommandContext);

};
