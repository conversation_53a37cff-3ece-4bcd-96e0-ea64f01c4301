<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level1/core/hc_namednodemapsetnameditemwithnewvalue</title>
<link href="activity-home.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['hc_namednodemapsetnameditemwithnewvalue'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "hc_staff");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'hc_namednodemapsetnameditemwithnewvalue';



window.doc = window;  
SimpleTest.waitForExplicitFinish();
addLoadEvent(setUpPage);


/**
* 
    If the "setNamedItem(arg)" method does not replace an 
   existing node with the same name then it returns null. 
   
   Retrieve the third employee and create a NamedNodeMap 
   object from the attributes of the last child.
   Once the list is created the "setNamedItem(arg)" method
   is invoked with arg=newAttr, where newAttr is a
   newly created Attr Node and whose node name
   already exists in the map.  The "setNamedItem(arg)"
   method should add the new node and return null.
   This test uses the "createAttribute(name)" method from
   the document interface.

* <AUTHOR> Arnold
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-core#ID-1025163788
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-core#ID-349467F9
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=243
*/
function hc_namednodemapsetnameditemwithnewvalue() {
   var success;
    if(checkInitialization(builder, "hc_namednodemapsetnameditemwithnewvalue") != null) return;
    var doc;
      var elementList;
      var newAttribute;
      var testAddress;
      var attributes;
      var newNode;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "hc_staff");
      elementList = doc.getElementsByTagName("acronym");
      testAddress = elementList.item(2);
      newAttribute = doc.createAttribute("lang");
      attributes = testAddress.attributes;

      newNode = attributes.setNamedItem(newAttribute);
      assertNull("prevValueNull",newNode);
    
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level1/core/hc_namednodemapsetnameditemwithnewvalue</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
<iframe name="doc" src="files/hc_staff.html"></iframe>
<br>
</body>
</html>
