<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level1/core/elementinuseattributeerr</title>
<link href="activity-home.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['elementinuseattributeerr'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staff");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'elementinuseattributeerr';



window.doc = window;  
SimpleTest.waitForExplicitFinish();
addLoadEvent(setUpPage);


/**
* 
    The "setAttributeNode(newAttr)" method raises an 
   "INUSE_ATTRIBUTE_ERR DOMException if the "newAttr" 
   is already an attribute of another element.
   
   Retrieve the last child of the second employee and append
   a newly created element.  The "createAttribute(name)"
   and "setAttributeNode(newAttr)" methods are invoked
   to create and add a new attribute to the newly created
   Element.  The "setAttributeNode(newAttr)" method is
   once again called to add the new attribute causing an
   exception to be raised since the attribute is already
   an attribute of another element.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-core#xpointer(id('ID-258A00AF')/constant[@name='INUSE_ATTRIBUTE_ERR'])
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-core#ID-887236154
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-core#xpointer(id('ID-887236154')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='INUSE_ATTRIBUTE_ERR'])
*/
function elementinuseattributeerr() {
   var success;
    if(checkInitialization(builder, "elementinuseattributeerr") != null) return;
    var doc;
      var newAttribute;
      var addressElementList;
      var testAddress;
      var newElement;
      var appendedChild;
      var setAttr1;
      var setAttr2;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staff");
      addressElementList = doc.getElementsByTagName("address");
      testAddress = addressElementList.item(1);
      newElement = doc.createElement("newElement");
      appendedChild = testAddress.appendChild(newElement);
      newAttribute = doc.createAttribute("newAttribute");
      setAttr1 = newElement.setAttributeNode(newAttribute);
      
	{
		success = false;
		try {
            setAttr2 = testAddress.setAttributeNode(newAttribute);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 10);
		}
		assertTrue("throw_INUSE_ATTRIBUTE_ERR",success);
	}

}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level1/core/elementinuseattributeerr</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
