<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['documentimportnode02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
      var docImportedRef = null;
      if (typeof(this.docImported) != 'undefined') {
        docImportedRef = this.docImported;
      }
      docsLoaded += preload(docImportedRef, "docImported", "staff");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'documentimportnode02';


/**
* 
	The importNode method imports a node from another document to this document. 
	The returned node has no parent; (parentNode is null). The source node is not 
	altered or removed from the original document but a new copy of the source node
	is created.
	
	Using the method importNode with deep=false, import the attribute, "emp:zone" of the 
	element node which is retreived by its elementId="CANADA", into the another document.
	Check the parentNode, nodeName, nodeType and nodeValue of the imported node to 
	verify if it has been imported correctly.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function documentimportnode02() {
   var success;
    if(checkInitialization(builder, "documentimportnode02") != null) return;
    var doc;
      var docImported;
      var element;
      var attr;
      var importedAttr;
      var nodeName;
      var nodeType;
      var nodeValue;
      var addresses;
      var attrsParent;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      
      var docImportedRef = null;
      if (typeof(this.docImported) != 'undefined') {
        docImportedRef = this.docImported;
      }
      docImported = load(docImportedRef, "docImported", "staff");
      addresses = doc.getElementsByTagNameNS("http://www.nist.gov","address");
      element = addresses.item(1);
      attr = element.getAttributeNodeNS("http://www.nist.gov","zone");
      importedAttr = docImported.importNode(attr,false);
      nodeName = importedAttr.nodeName;

      nodeType = importedAttr.nodeType;

      nodeValue = importedAttr.nodeValue;

      attrsParent = importedAttr.parentNode;

      assertNull("documentimportnode02_parentNull",attrsParent);
    assertEquals("documentimportnode02_nodeName","emp:zone",nodeName);
       assertEquals("documentimportnode02_nodeType",2,nodeType);
       assertEquals("documentimportnode02_nodeValue","CANADA",nodeValue);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
