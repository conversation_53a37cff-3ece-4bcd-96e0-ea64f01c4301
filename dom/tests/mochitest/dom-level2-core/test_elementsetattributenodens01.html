<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementsetattributenodens01</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['elementsetattributenodens01'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staff");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'elementsetattributenodens01';


/**
* 
      Testing Element.setAttributeNodeNS: If an attribute with that local name 
      and that namespace URI is already present in the element, it is replaced
      by the new one.

      Create a new element and two new attribute nodes (in the same namespace
      and same localNames).  
      Add the two new attribute nodes to the element node using the 
      setAttributeNodeNS method.  Check that only one attribute is added, check
      the value of this attribute.
    
* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-ElSetAtNodeNS
*/
function elementsetattributenodens01() {
   var success;
    if(checkInitialization(builder, "elementsetattributenodens01") != null) return;
    var doc;
      var element;
      var attribute1;
      var attribute2;
      var attrNode;
      var attrName;
      var attrNS;
      var attrValue;
      var attributes;
      var newAttribute;
      var length;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staff");
      element = doc.createElementNS("http://www.w3.org/DOM/Test/Level2","new:element");
      attribute1 = doc.createAttributeNS("http://www.w3.org/DOM/Test/att1","p1:att");
      attribute2 = doc.createAttributeNS("http://www.w3.org/DOM/Test/att1","p2:att");
      attribute2.value = "value2";

      newAttribute = element.setAttributeNodeNS(attribute1);
      newAttribute = element.setAttributeNodeNS(attribute2);
      attrNode = element.getAttributeNodeNS("http://www.w3.org/DOM/Test/att1","att");
      attrName = attrNode.nodeName;

      attrNS = attrNode.namespaceURI;

      assertEquals("elementsetattributenodens01_attrName","p2:att",attrName);
       assertEquals("elementsetattributenodens01_attrNS","http://www.w3.org/DOM/Test/att1",attrNS);
       attributes = element.attributes;

      length = attributes.length;

      assertEquals("length",1,length);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementsetattributenodens01</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
