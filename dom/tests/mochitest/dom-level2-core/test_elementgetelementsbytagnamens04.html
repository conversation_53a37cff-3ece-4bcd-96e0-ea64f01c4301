<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementgetelementsbytagnamens04</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['elementgetelementsbytagnamens04'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'elementgetelementsbytagnamens04';


/**
* 
      Returns a NodeList of all the Elements with a given local name and namespace URI in the 
      order in which they are encountered in a preorder traversal of the Document tree. 
      Create a new element node ('root') and append three newly created child nodes (all have 
      local name 'child' and defined in different namespaces). 
      Test 1: invoke getElementsByTagNameNS to retrieve one of the children.
      Test 2: invoke getElementsByTagNameNS with the value of namespace equals to '*', and 
      verify that the node list has length of 3. 
    
* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-getElBTNNS
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=259
*/
function elementgetelementsbytagnamens04() {
   var success;
    if(checkInitialization(builder, "elementgetelementsbytagnamens04") != null) return;
    var doc;
      var element;
      var child1;
      var child2;
      var child3;
      var appendedChild;
      var elementList;
      var nullNS = null;

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      element = doc.createElementNS("http://www.w3.org/DOM","root");
      child1 = doc.createElementNS("http://www.w3.org/DOM/Level1","dom:child");
      child2 = doc.createElementNS(nullNS,"child");
      child3 = doc.createElementNS("http://www.w3.org/DOM/Level2","dom:child");
      appendedChild = element.appendChild(child1);
      appendedChild = element.appendChild(child2);
      appendedChild = element.appendChild(child3);
      elementList = element.getElementsByTagNameNS(nullNS,"child");
      assertSize("elementgetelementsbytagnamens04_1",1,elementList);
elementList = element.getElementsByTagNameNS("*","child");
      assertSize("elementgetelementsbytagnamens04_2",3,elementList);

}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementgetelementsbytagnamens04</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
