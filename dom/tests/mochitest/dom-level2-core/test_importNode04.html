<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode04</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['importNode04'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staff");
        
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      docsLoaded += preload(aNewDocRef, "aNewDoc", "staff");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'importNode04';


/**
* 
    The "importNode(importedNode,deep)" method for a 
   Document should import the given importedNode into that Document.
   The importedNode is of type Document_Fragment.
   
   Create a DocumentFragment in a different document.
   Create a Comment child node for the Document Fragment.
   Invoke method importNode(importedNode,deep) on this document
   with importedNode being the newly created DocumentFragment.
   Method should return a node of type DocumentFragment whose child has
   comment value "descendant1".

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function importNode04() {
   var success;
    if(checkInitialization(builder, "importNode04") != null) return;
    var doc;
      var aNewDoc;
      var docFrag;
      var comment;
      var aNode;
      var children;
      var child;
      var childValue;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staff");
      
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      aNewDoc = load(aNewDocRef, "aNewDoc", "staff");
      docFrag = aNewDoc.createDocumentFragment();
      comment = aNewDoc.createComment("descendant1");
      aNode = docFrag.appendChild(comment);
      aNode = doc.importNode(docFrag,true);
      children = aNode.childNodes;

      assertSize("throw_Size",1,children);
child = aNode.firstChild;

      childValue = child.nodeValue;

      assertEquals("descendant1","descendant1",childValue);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode04</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
