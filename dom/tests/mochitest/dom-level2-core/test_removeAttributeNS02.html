<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/removeAttributeNS02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['removeAttributeNS02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);
       setImplementationAttribute("validating", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'removeAttributeNS02';


/**
* 
    The "removeAttributeNS(namespaceURI,localName)" removes an attribute by 
   local name and namespace URI.  If the removed attribute has a
   default value it is immediately replaced.  The replacing attribute has the same
   namespace URI and local name, as well as the original prefix.
   
   Retrieve the attribute named "emp:local" from emp:address
   node, then remove the "emp:local" 
   attribute by invoking the "removeAttributeNS(namespaceURI,localName)" method.
   The "emp:local" attribute has a default value defined in the
   DTD file, that value should immediately replace the old
   value.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-ElRemAtNS
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=238
*/
function removeAttributeNS02() {
   var success;
    if(checkInitialization(builder, "removeAttributeNS02") != null) return;
    var doc;
      var elementList;
      var testAddr;
      var addrAttr;
      var attr;
      var namespaceURI;
      var localName;
      var prefix;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      elementList = doc.getElementsByTagName("emp:address");
      testAddr = elementList.item(0);
      testAddr.removeAttributeNS("http://www.nist.gov","local1");
      elementList = doc.getElementsByTagName("emp:address");
      testAddr = elementList.item(0);
      addrAttr = testAddr.getAttributeNodeNS("http://www.nist.gov","local1");
      attr = testAddr.getAttributeNS("http://www.nist.gov","local1");
      namespaceURI = addrAttr.namespaceURI;

      localName = addrAttr.localName;

      prefix = testAddr.prefix;

      assertEquals("attr","FALSE",attr);
       assertEquals("uri","http://www.nist.gov",namespaceURI);
       assertEquals("lname","local1",localName);
       assertEquals("prefix","emp",prefix);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/removeAttributeNS02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
