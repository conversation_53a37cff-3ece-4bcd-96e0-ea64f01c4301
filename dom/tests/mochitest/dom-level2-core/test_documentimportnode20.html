<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode20</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['documentimportnode20'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);
       setImplementationAttribute("expandEntityReferences", false);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'documentimportnode20';


/**
* 
	The importNode method imports a node from another document to this document. 
	The returned node has no parent; (parentNode is null). The source node is not 
	altered or removed from the original document but a new copy of the source node
	is created.
	
	Using the method importNode with deep=true, import a entity node ent4 
	from this document to a new document object.  The replacement text of this entity is an element
	node, a cdata node and a pi.  Verify if the nodes have been 
	imported correctly by checking the nodeNames of the imported element node, the data for the
	cdata nodes and the PItarget and PIData for the pi nodes.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function documentimportnode20() {
   var success;
    if(checkInitialization(builder, "documentimportnode20") != null) return;
    var doc;
      var docImp;
      var domImpl;
      var docType;
      var docTypeNull = null;

      var nodeMap;
      var entity4;
      var entityImp4;
      var element;
      var cdata;
      var pi;
      var childList;
      var elemchildList;
      var ent4Name;
      var ent4ImpName;
      var cdataVal;
      var piTargetVal;
      var piDataVal;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      domImpl = doc.implementation;
docType = doc.doctype;

      docImp = domImpl.createDocument("http://www.w3.org/DOM/Test","a:b",docTypeNull);
      nodeMap = docType.entities;

      entity4 = nodeMap.getNamedItem("ent4");
      entityImp4 = docImp.importNode(entity4,true);
      childList = entityImp4.childNodes;

      element = childList.item(0);
      elemchildList = element.childNodes;

      cdata = elemchildList.item(0);
      pi = childList.item(1);
      ent4Name = entity4.nodeName;

      ent4ImpName = entityImp4.nodeName;

      cdataVal = cdata.data;

      piTargetVal = pi.target;

      piDataVal = pi.data;

      assertEquals("documentimportnode20_Ent4NodeName",ent4Name,ent4ImpName);
       assertEquals("documentimportnode20_Cdata","Element data",cdataVal);
       assertEquals("documentimportnode20_PITarget","PItarget",piTargetVal);
       assertEquals("documentimportnode20_PIData","PIdata",piDataVal);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode20</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
