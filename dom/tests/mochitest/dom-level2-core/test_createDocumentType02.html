<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/createDocumentType02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['createDocumentType02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'createDocumentType02';


/**
* 
    The "createDocumentType(qualifiedName,publicId,systemId)" method for a 
   DOMImplementation should raise INVALID_CHARACTER_ERR DOMException if
   qualifiedName contains an illegal character.
   
   Invoke method createDocumentType(qualifiedName,publicId,systemId) on
   this domimplementation with qualifiedName containing an illegal character
   from illegalChars[]. Method should raise INVALID_CHARACTER_ERR
   DOMException for all characters in illegalChars[].

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Level-2-Core-DOM-createDocType
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#xpointer(id('Level-2-Core-DOM-createDocType')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='INVALID_CHARACTER_ERR'])
*/
function createDocumentType02() {
   var success;
    if(checkInitialization(builder, "createDocumentType02") != null) return;
    var publicId = "http://www.localhost.com/";
      var systemId = "myDoc.dtd";
      var qualifiedName;
      var doc;
      var docType = null;

      var domImpl;
      illegalQNames = new Array();
      illegalQNames[0] = "edi:{";
      illegalQNames[1] = "edi:}";
      illegalQNames[2] = "edi:~";
      illegalQNames[3] = "edi:'";
      illegalQNames[4] = "edi:!";
      illegalQNames[5] = "edi:@";
      illegalQNames[6] = "edi:#";
      illegalQNames[7] = "edi:$";
      illegalQNames[8] = "edi:%";
      illegalQNames[9] = "edi:^";
      illegalQNames[10] = "edi:&";
      illegalQNames[11] = "edi:*";
      illegalQNames[12] = "edi:(";
      illegalQNames[13] = "edi:)";
      illegalQNames[14] = "edi:+";
      illegalQNames[15] = "edi:=";
      illegalQNames[16] = "edi:[";
      illegalQNames[17] = "edi:]";
      illegalQNames[18] = "edi:\\";
      illegalQNames[19] = "edi:/";
      illegalQNames[20] = "edi:;";
      illegalQNames[21] = "edi:`";
      illegalQNames[22] = "edi:<";
      illegalQNames[23] = "edi:>";
      illegalQNames[24] = "edi:,";
      illegalQNames[25] = "edi:a ";
      illegalQNames[26] = "edi:\"";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      for(var indexN65690 = 0;indexN65690 < illegalQNames.length; indexN65690++) {
      qualifiedName = illegalQNames[indexN65690];
      domImpl = doc.implementation;

	{
		success = false;
		try {
            docType = domImpl.createDocumentType(qualifiedName,publicId,systemId);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 5);
		}
		assertTrue("throw_INVALID_CHARACTER_ERR",success);
	}

	}
   
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/createDocumentType02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
