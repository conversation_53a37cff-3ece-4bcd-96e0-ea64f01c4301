<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/nodegetnamespaceuri03</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['nodegetnamespaceuri03'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staff");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'nodegetnamespaceuri03';


/**
* 
	The method getNamespaceURI returns the namespace URI of this node, or null if it is unspecified
	For nodes of any type other than ELEMENT_NODE and ATTRIBUTE_NODE and nodes created with 
	a DOM Level 1 method, such as createElement from the Document interface, this is always null.
  
	Ceate two new element nodes and atribute nodes, with and without namespace prefixes.
	Retreive their namespaceURI's using getNamespaceURI and verrify if it is correct.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-NodeNSname
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=259
*/
function nodegetnamespaceuri03() {
   var success;
    if(checkInitialization(builder, "nodegetnamespaceuri03") != null) return;
    var doc;
      var element;
      var elementNS;
      var attr;
      var attrNS;
      var elemNSURI;
      var elemNSURINull;
      var attrNSURI;
      var attrNSURINull;
      var nullNS = null;

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staff");
      element = doc.createElementNS(nullNS,"elem");
      elementNS = doc.createElementNS("http://www.w3.org/DOM/Test/elem","qual:qelem");
      attr = doc.createAttributeNS(nullNS,"attr");
      attrNS = doc.createAttributeNS("http://www.w3.org/DOM/Test/attr","qual:qattr");
      elemNSURI = elementNS.namespaceURI;

      elemNSURINull = element.namespaceURI;

      attrNSURI = attrNS.namespaceURI;

      attrNSURINull = attr.namespaceURI;

      assertEquals("nodegetnamespaceuri03_elemNSURI","http://www.w3.org/DOM/Test/elem",elemNSURI);
       assertNull("nodegetnamespaceuri03_1",elemNSURINull);
    assertEquals("nodegetnamespaceuri03_attrNSURI","http://www.w3.org/DOM/Test/attr",attrNSURI);
       assertNull("nodegetnamespaceuri03_2",attrNSURINull);
    
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/nodegetnamespaceuri03</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
