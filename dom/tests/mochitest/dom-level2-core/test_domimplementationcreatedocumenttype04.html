<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/domimplementationcreatedocumenttype04</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['domimplementationcreatedocumenttype04'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'domimplementationcreatedocumenttype04';


/**
* 
	The method createDocumentType should raise a INVALID_CHARACTER_ERR if the qualifiedName 
	contains an illegal characters.
	
	Invoke createDocument on this DOMImplementation with qualifiedNames having illegal characters. 
	Check if an INVALID_CHARACTER_ERR is raised in each case.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Level-2-Core-DOM-createDocType
*/
function domimplementationcreatedocumenttype04() {
   var success;
    if(checkInitialization(builder, "domimplementationcreatedocumenttype04") != null) return;
    var doc;
      var domImpl;
      var newDocType;
      var publicId = "http://www.w3.org/DOM/Test/dom2.dtd";
      var systemId = "dom2.dtd";
      var qualifiedName;
      qualifiedNames = new Array();
      qualifiedNames[0] = "{";
      qualifiedNames[1] = "}";
      qualifiedNames[2] = "'";
      qualifiedNames[3] = "~";
      qualifiedNames[4] = "`";
      qualifiedNames[5] = "@";
      qualifiedNames[6] = "#";
      qualifiedNames[7] = "$";
      qualifiedNames[8] = "%";
      qualifiedNames[9] = "^";
      qualifiedNames[10] = "&";
      qualifiedNames[11] = "*";
      qualifiedNames[12] = "(";
      qualifiedNames[13] = ")";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      domImpl = doc.implementation;
for(var indexN65651 = 0;indexN65651 < qualifiedNames.length; indexN65651++) {
      qualifiedName = qualifiedNames[indexN65651];
      
	{
		success = false;
		try {
            newDocType = domImpl.createDocumentType(qualifiedName,publicId,systemId);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 5);
		}
		assertTrue("domimplementationcreatedocumenttype04",success);
	}

	}
   
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/domimplementationcreatedocumenttype04</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
