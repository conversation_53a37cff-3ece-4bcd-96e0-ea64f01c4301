<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/createDocument05</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['createDocument05'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'createDocument05';


/**
* 
    The "createDocument(namespaceURI,qualifiedName,doctype)" method for a 
   DOMImplementation should raise INVALID_CHARACTER_ERR DOMException
   if parameter qualifiedName contains an illegal character.
   
   Invoke method createDocument(namespaceURI,qualifiedName,doctype) on
   this domimplementation with namespaceURI equals "http://www.ecommerce.org/schema",
   doctype is null and qualifiedName contains an illegal character from
   illegalChars[].  Method should raise INVALID_CHARACTER_ERR DOMException
   for all characters in illegalChars[].

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#
*/
function createDocument05() {
   var success;
    if(checkInitialization(builder, "createDocument05") != null) return;
    var namespaceURI = "http://www.ecommerce.org/schema";
      var qualifiedName;
      var doc;
      var docType = null;

      var domImpl;
      var aNewDoc;
      var charact;
      illegalQNames = new Array();
      illegalQNames[0] = "namespaceURI:{";
      illegalQNames[1] = "namespaceURI:}";
      illegalQNames[2] = "namespaceURI:~";
      illegalQNames[3] = "namespaceURI:'";
      illegalQNames[4] = "namespaceURI:!";
      illegalQNames[5] = "namespaceURI:@";
      illegalQNames[6] = "namespaceURI:#";
      illegalQNames[7] = "namespaceURI:$";
      illegalQNames[8] = "namespaceURI:%";
      illegalQNames[9] = "namespaceURI:^";
      illegalQNames[10] = "namespaceURI:&";
      illegalQNames[11] = "namespaceURI:*";
      illegalQNames[12] = "namespaceURI:(";
      illegalQNames[13] = "namespaceURI:)";
      illegalQNames[14] = "namespaceURI:+";
      illegalQNames[15] = "namespaceURI:=";
      illegalQNames[16] = "namespaceURI:[";
      illegalQNames[17] = "namespaceURI:]";
      illegalQNames[18] = "namespaceURI:\\";
      illegalQNames[19] = "namespaceURI:/";
      illegalQNames[20] = "namespaceURI:;";
      illegalQNames[21] = "namespaceURI:`";
      illegalQNames[22] = "namespaceURI:<";
      illegalQNames[23] = "namespaceURI:>";
      illegalQNames[24] = "namespaceURI:,";
      illegalQNames[25] = "namespaceURI:a ";
      illegalQNames[26] = "namespaceURI:\"";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      for(var indexN65690 = 0;indexN65690 < illegalQNames.length; indexN65690++) {
      qualifiedName = illegalQNames[indexN65690];
      domImpl = doc.implementation;

	{
		success = false;
		try {
            aNewDoc = domImpl.createDocument(namespaceURI,qualifiedName,docType);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 5);
		}
		assertTrue("throw_INVALID_CHARACTER_ERR",success);
	}

	}
   
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/createDocument05</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
