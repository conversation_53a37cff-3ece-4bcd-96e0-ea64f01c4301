<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/getNamedItemNS02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['getNamedItemNS02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'getNamedItemNS02';


/**
* 
    The "getNamedItemNS(namespaceURI,localName)" method for a 
   NamedNodeMap should return null
   if parameters do not identify any node in this map.
   
   Retrieve a list of elements with tag name "address".
   Access the second element from the list and get its attributes.
   Try to retrieve an attribute node with local name "domest"
   and namespace uri "http://www.usa.com" with
   method getNamedItemNS(namespaceURI,localName).
   This should return null because "domest" does not match any local names in this map.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-getNamedItemNS
*/
function getNamedItemNS02() {
   var success;
    if(checkInitialization(builder, "getNamedItemNS02") != null) return;
    var namespaceURI = "http://www.usa.com";
      var localName = "domest";
      var doc;
      var elementList;
      var testEmployee;
      var attributes;
      var newAttr;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      elementList = doc.getElementsByTagName("address");
      testEmployee = elementList.item(1);
      attributes = testEmployee.attributes;

      newAttr = attributes.getNamedItemNS(namespaceURI,localName);
      assertNull("throw_Null",newAttr);
    
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/getNamedItemNS02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
