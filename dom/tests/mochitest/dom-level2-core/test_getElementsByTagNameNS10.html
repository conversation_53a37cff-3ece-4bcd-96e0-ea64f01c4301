<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/getElementsByTagNameNS10</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['getElementsByTagNameNS10'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'getElementsByTagNameNS10';


/**
* 
    The "getElementsByTagNameNS(namespaceURI,localName)" method returns a NodeList
   of all descendant Elements with a given local name and namespace URI in the
   order in which they are encountered in a preorder traversal of this Element tree.
   
   Create a NodeList of all the descendant elements of the document element
   using the "http://www.nist.gov" as the namespaceURI and the special value "*" as the 
   localName.
   The method should return a NodeList of elements that have "http://www.nist.gov
   as a namespace URI.

   Derived from getElementsByTagNameNS03

* <AUTHOR> Arnold
* <AUTHOR> Arnold
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-1938918D
*/
function getElementsByTagNameNS10() {
   var success;
    if(checkInitialization(builder, "getElementsByTagNameNS10") != null) return;
    var doc;
      var docElem;
      var elementList;
      var child;
      var childName;
      var result = new Array();

      expectedResult = new Array();
      expectedResult[0] = "employee";
      expectedResult[1] = "employeeId";
      expectedResult[2] = "name";
      expectedResult[3] = "position";
      expectedResult[4] = "salary";
      expectedResult[5] = "gender";
      expectedResult[6] = "address";
      expectedResult[7] = "emp:employee";
      expectedResult[8] = "emp:employeeId";
      expectedResult[9] = "emp:position";
      expectedResult[10] = "emp:salary";
      expectedResult[11] = "emp:gender";
      expectedResult[12] = "emp:address";
      expectedResult[13] = "address";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      docElem = doc.documentElement;

      elementList = docElem.getElementsByTagNameNS("http://www.nist.gov","*");
      for(var indexN65662 = 0;indexN65662 < elementList.length; indexN65662++) {
      child = elementList.item(indexN65662);
      childName = child.nodeName;

      result[result.length] = childName;

	}
   assertEqualsList("nodeNames",expectedResult,result);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/getElementsByTagNameNS10</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
