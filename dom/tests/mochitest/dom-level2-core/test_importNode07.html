<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode07</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['importNode07'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);
       setImplementationAttribute("validating", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      docsLoaded += preload(aNewDocRef, "aNewDoc", "staff");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'importNode07';


/**
* 
    The "importNode(importedNode,deep)" method for a 
   Document should import the given importedNode into that Document.
   The importedNode is of type Element.
   If this document defines default attributes for this element name (importedNode),
   those default attributes are assigned.
   
   Create an element whose name is "emp:employee" in a different document.
   Invoke method importNode(importedNode,deep) on this document which
   defines default attribute for the element name "emp:employee".
   Method should return an the imported element with an assigned default attribute.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=238
*/
function importNode07() {
   var success;
    if(checkInitialization(builder, "importNode07") != null) return;
    var doc;
      var aNewDoc;
      var element;
      var aNode;
      var attributes;
      var name;
      var attr;
      var lname;
      var namespaceURI = "http://www.nist.gov";
      var qualifiedName = "emp:employee";
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      aNewDoc = load(aNewDocRef, "aNewDoc", "staff");
      element = aNewDoc.createElementNS(namespaceURI,qualifiedName);
      aNode = doc.importNode(element,false);
      attributes = aNode.attributes;

      assertSize("throw_Size",1,attributes);
name = aNode.nodeName;

      assertEquals("nodeName","emp:employee",name);
       attr = attributes.item(0);
      lname = attr.localName;

      assertEquals("lname","defaultAttr",lname);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode07</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
