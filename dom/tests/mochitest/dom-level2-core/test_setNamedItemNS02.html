<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/setNamedItemNS02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['setNamedItemNS02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
      var anotherDocRef = null;
      if (typeof(this.anotherDoc) != 'undefined') {
        anotherDocRef = this.anotherDoc;
      }
      docsLoaded += preload(anotherDocRef, "anotherDoc", "staffNS");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'setNamedItemNS02';


/**
* 
    The "setNamedItemNS(arg)" method for a 
   NamedNodeMap should raise WRONG_DOCUMENT_ERR DOMException if arg was
   created from a different document than the one that created this map.
   
   Create an attr node in a different document with qualifiedName equals
   "dmstc:domestic" and namespaceURI is "http://www.usa.com".
   Access the namednodemap of the first "address" element in this document.
   Invoke method setNamedItemNS(arg) with arg being the attr node from above.
   Method should raise WRONG_DOCUMENT_ERR DOMException.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#xpointer(id('ID-258A00AF')/constant[@name='WRONG_DOCUMENT_ERR'])
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-setNamedItemNS
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#xpointer(id('ID-setNamedItemNS')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='WRONG_DOCUMENT_ERR'])
*/
function setNamedItemNS02() {
   var success;
    if(checkInitialization(builder, "setNamedItemNS02") != null) return;
    var namespaceURI = "http://www.usa.com";
      var qualifiedName = "dmstc:domestic";
      var doc;
      var anotherDoc;
      var arg;
      var elementList;
      var testAddress;
      var attributes;
      var setNode;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      
      var anotherDocRef = null;
      if (typeof(this.anotherDoc) != 'undefined') {
        anotherDocRef = this.anotherDoc;
      }
      anotherDoc = load(anotherDocRef, "anotherDoc", "staffNS");
      arg = anotherDoc.createAttributeNS(namespaceURI,qualifiedName);
      arg.nodeValue = "Maybe";

      elementList = doc.getElementsByTagName("address");
      testAddress = elementList.item(0);
      attributes = testAddress.attributes;

      
	{
		success = false;
		try {
            setNode = attributes.setNamedItemNS(arg);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 4);
		}
		assertTrue("throw_WRONG_DOCUMENT_ERR",success);
	}

}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/setNamedItemNS02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
