<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/setAttributeNS09</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['setAttributeNS09'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'setAttributeNS09';


/**
* 
    The "setAttributeNS(namespaceURI,qualifiedName,value)" method adds a new attribute.
   If an attribute with the same local name and namespace URI is already present
   on the element, its prefix is changed to be the prefix part of the "qualifiedName",
   and its vale is changed to be the "value" paramter.
   null value if no previously existing Attr node with the
   same name was replaced.
   
   Add a new attribute to the "emp:address" element.
   Check to see if the new attribute has been successfully added to the document
   by getting the attributes value, namespace URI, local Name and prefix.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-ElSetAttrNS
*/
function setAttributeNS09() {
   var success;
    if(checkInitialization(builder, "setAttributeNS09") != null) return;
    var localName = "newAttr";
      var namespaceURI = "http://www.newattr.com";
      var qualifiedName = "emp:newAttr";
      var doc;
      var elementList;
      var testAddr;
      var addrAttr;
      var resultAttr;
      var resultNamespaceURI;
      var resultLocalName;
      var resultPrefix;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      elementList = doc.getElementsByTagName("emp:address");
      testAddr = elementList.item(0);
      assertNotNull("empAddrNotNull",testAddr);
testAddr.setAttributeNS(namespaceURI,qualifiedName,"newValue");
      addrAttr = testAddr.getAttributeNodeNS(namespaceURI,localName);
      resultAttr = testAddr.getAttributeNS(namespaceURI,localName);
      assertEquals("attrValue","newValue",resultAttr);
       resultNamespaceURI = addrAttr.namespaceURI;

      assertEquals("nsuri","http://www.newattr.com",resultNamespaceURI);
       resultLocalName = addrAttr.localName;

      assertEquals("lname","newAttr",resultLocalName);
       resultPrefix = addrAttr.prefix;

      assertEquals("prefix","emp",resultPrefix);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/setAttributeNS09</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
