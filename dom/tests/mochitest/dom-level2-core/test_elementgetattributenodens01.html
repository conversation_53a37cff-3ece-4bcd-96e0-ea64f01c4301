<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementgetattributenodens01</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['elementgetattributenodens01'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'elementgetattributenodens01';


/**
* 
      The method getAttributeNodeNS retrieves an Attr node by local name and namespace URI.
      Create a new element node and add 2 new attribute nodes to it that have the same 
      local name but different namespaceURIs and prefixes.  
      Retrieve an attribute using namespace and localname and check its value, name and 
      namespaceURI.
    
* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-ElGetAtNodeNS
*/
function elementgetattributenodens01() {
   var success;
    if(checkInitialization(builder, "elementgetattributenodens01") != null) return;
    var doc;
      var element;
      var attribute1;
      var attribute2;
      var newAttribute1;
      var newAttribute2;
      var attribute;
      var attrValue;
      var attrName;
      var attNodeName;
      var attrLocalName;
      var attrNS;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      element = doc.createElementNS("namespaceURI","root");
      attribute1 = doc.createAttributeNS("http://www.w3.org/DOM/Level2","l2:att");
      newAttribute1 = element.setAttributeNodeNS(attribute1);
      attribute2 = doc.createAttributeNS("http://www.w3.org/DOM/Level1","att");
      newAttribute2 = element.setAttributeNodeNS(attribute2);
      attribute = element.getAttributeNodeNS("http://www.w3.org/DOM/Level2","att");
      attrValue = attribute.nodeValue;

      attrName = attribute.name;

      attNodeName = attribute.nodeName;

      attrLocalName = attribute.localName;

      attrNS = attribute.namespaceURI;

      assertEquals("elementgetattributenodens01_attrValue","",attrValue);
       assertEquals("elementgetattributenodens01_attrName","l2:att",attrName);
       assertEquals("elementgetattributenodens01_attrNodeName","l2:att",attNodeName);
       assertEquals("elementgetattributenodens01_attrLocalName","att",attrLocalName);
       assertEquals("elementgetattributenodens01_attrNs","http://www.w3.org/DOM/Level2",attrNS);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/elementgetattributenodens01</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
