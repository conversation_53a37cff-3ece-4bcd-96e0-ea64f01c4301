<!ELEMENT employeeId (#PCDATA)>
<!ELEMENT name (#PCDATA)>
<!ELEMENT position (#PCDATA)>
<!ELEMENT salary (#PCDATA)>
<!ELEMENT address (#PCDATA)>
<!ELEMENT gender ( #PCDATA)>
<!ELEMENT employee (employeeId, name, position, salary, gender, address) >
<!ATTLIST employee xmlns CDATA #IMPLIED>
<!ELEMENT staff (employee)+>
<!ELEMENT svg (rect, script, employee+)>
<!ATTLIST svg 
      xmlns CDATA #FIXED "http://www.w3.org/2000/svg"
      name CDATA #IMPLIED>
<!ELEMENT rect EMPTY>
<!ATTLIST rect 
      x CDATA #REQUIRED
      y CDATA #REQUIRED
      width CDATA #REQUIRED
      height CDATA #REQUIRED>
<!ELEMENT script (#PCDATA)>
<!ATTLIST script type CDATA #IMPLIED>      
<!ENTITY svgunit SYSTEM "svgunit.js">
<!ENTITY svgtest SYSTEM "internalSubset01.js">

