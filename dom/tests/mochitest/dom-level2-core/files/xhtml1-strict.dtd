<!--

Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, Institut National de
Recherche en Informatique et en Automatique, Keio University).  All 
Rights Reserved.  This program is distributed under the W3C's Software
Intellectual Property License.  This program is distributed in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR 
PURPOSE.  

See W3C License http://www.w3.org/Consortium/Legal/ for more details.

-->

<!--

This is a radically simplified DTD for use in the DOM Test Suites
due to a XML non-conformance of one implementation in processing
parameter entities.  When that non-conformance is resolved,
this DTD can be replaced by the normal DTD for XHTML.

-->


<!ELEMENT html (head, body)>
<!ATTLIST html xmlns CDATA #IMPLIED>
<!ELEMENT head (meta,title,script*)>
<!ELEMENT meta EMPTY>
<!ATTLIST meta 
	http-equiv CDATA #IMPLIED
	content CDATA #IMPLIED>
<!ELEMENT title (#PCDATA)>
<!ELEMENT body (p*)>
<!ATTLIST body onload CDATA #IMPLIED>
<!ELEMENT p (#PCDATA|em|strong|code|sup|var|acronym|abbr)*>
<!ATTLIST p 
  xmlns:dmstc CDATA #IMPLIED
  xmlns:nm CDATA #IMPLIED
  xmlns:emp2 CDATA #IMPLIED
  id ID #IMPLIED  
>
<!ELEMENT em (#PCDATA)>
<!ELEMENT span (#PCDATA)>
<!ELEMENT strong (#PCDATA)>
<!ELEMENT code (#PCDATA)>
<!ELEMENT sup (#PCDATA)>
<!ELEMENT var (#PCDATA|span)*>
<!ELEMENT acronym (#PCDATA)>
<!ATTLIST acronym
    title CDATA #IMPLIED
    class CDATA #IMPLIED
    id ID #IMPLIED
>
<!ELEMENT abbr (#PCDATA)>
<!ATTLIST abbr
    title CDATA #IMPLIED
    class CDATA #IMPLIED
    id ID #IMPLIED
>
<!ELEMENT script (#PCDATA)>
<!ATTLIST script 
   type CDATA #IMPLIED
   src CDATA #IMPLIED
   charset CDATA #IMPLIED>          
