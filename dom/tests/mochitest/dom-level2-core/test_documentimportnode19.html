<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode19</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['documentimportnode19'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'documentimportnode19';


/**
* 
	The importNode method imports a node from another document to this document. 
	The returned node has no parent; (parentNode is null). The source node is not 
	altered or removed from the original document but a new copy of the source node
	is created.
	
	Using the method importNode with deep=true/false, import a entity nodes ent2 and ent6
	from this document to a new document object.  Verify if the nodes have been 
	imported correctly by checking the nodeNames of the imported nodes and public and system ids.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function documentimportnode19() {
   var success;
    if(checkInitialization(builder, "documentimportnode19") != null) return;
    var doc;
      var docTypeNull = null;

      var docImp;
      var domImpl;
      var docType;
      var nodeMap;
      var entity2;
      var entity6;
      var entityImp2;
      var entityImp6;
      var nodeName;
      var systemId;
      var notationName;
      var nodeNameImp;
      var systemIdImp;
      var notationNameImp;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      domImpl = doc.implementation;
docType = doc.doctype;

      docImp = domImpl.createDocument("http://www.w3.org/DOM/Test","a:b",docTypeNull);
      nodeMap = docType.entities;

      assertNotNull("entitiesNotNull",nodeMap);
entity2 = nodeMap.getNamedItem("ent2");
      entity6 = nodeMap.getNamedItem("ent6");
      entityImp2 = docImp.importNode(entity2,false);
      entityImp6 = docImp.importNode(entity6,true);
      nodeName = entity2.nodeName;

      nodeNameImp = entityImp2.nodeName;

      assertEquals("documentimportnode19_Ent2NodeName",nodeName,nodeNameImp);
       nodeName = entity6.nodeName;

      nodeNameImp = entityImp6.nodeName;

      assertEquals("documentimportnode19_Ent6NodeName",nodeName,nodeNameImp);
       systemId = entity2.systemId;

      systemIdImp = entityImp2.systemId;

      assertEquals("documentimportnode19_Ent2SystemId",systemId,systemIdImp);
       systemId = entity6.systemId;

      systemIdImp = entityImp6.systemId;

      assertEquals("documentimportnode19_Ent6SystemId",systemId,systemIdImp);
       notationName = entity2.notationName;

      notationNameImp = entityImp2.notationName;

      assertEquals("documentimportnode19_Ent2NotationName",notationName,notationNameImp);
       notationName = entity6.notationName;

      notationNameImp = entityImp6.notationName;

      assertEquals("documentimportnode19_Ent6NotationName",notationName,notationNameImp);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode19</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
