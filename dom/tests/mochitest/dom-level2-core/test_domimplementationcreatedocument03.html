<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/domimplementationcreatedocument03</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['domimplementationcreatedocument03'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'domimplementationcreatedocument03';


/**
* 
    The createDocument method with valid arguments, should create a DOM Document of 
    the specified type.  
    
    Call the createDocument on this DOMImplementation with 
    createDocument ("http://www.w3.org/DOMTest/L2",see the array below for valid QNames,null).  
    Check if the returned Document object is is empty with no Document Element.	

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Level-2-Core-DOM-createDocument
*/
function domimplementationcreatedocument03() {
   var success;
    if(checkInitialization(builder, "domimplementationcreatedocument03") != null) return;
    var doc;
      var domImpl;
      var newDoc;
      var docType = null;

      var namespaceURI = "http://www.w3.org/DOMTest/L2";
      var qualifiedName;
      qualifiedNames = new Array();
      qualifiedNames[0] = "_:_";
      qualifiedNames[1] = "_:h0";
      qualifiedNames[2] = "_:test";
      qualifiedNames[3] = "l_:_";
      qualifiedNames[4] = "ns:_0";
      qualifiedNames[5] = "ns:a0";
      qualifiedNames[6] = "ns0:test";
      qualifiedNames[7] = "a.b:c";
      qualifiedNames[8] = "a-b:c";
      qualifiedNames[9] = "a-b:c";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      domImpl = doc.implementation;
for(var indexN65643 = 0;indexN65643 < qualifiedNames.length; indexN65643++) {
      qualifiedName = qualifiedNames[indexN65643];
      newDoc = domImpl.createDocument(namespaceURI,qualifiedName,docType);
      assertNotNull("domimplementationcreatedocument03",newDoc);

	}
   
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/domimplementationcreatedocument03</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
