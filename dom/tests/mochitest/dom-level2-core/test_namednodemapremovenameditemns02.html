<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/namednodemapremovenameditemns02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['namednodemapremovenameditemns02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);
       setImplementationAttribute("validating", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'namednodemapremovenameditemns02';


/**
* 
   The method removeNamedItemNS removes a node specified by local name and namespace 
   A removed attribute may be known to have a default value when this map contains the 
   attributes attached to an element, as returned by the attributes attribute of the Node 
   interface. If so, an attribute immediately appears containing the default value as well 
   as the corresponding namespace URI, local name, and prefix when applicable.
    
	Retreive a default attribute node.  Remove it from the NodeMap.  Check if a new one immediately
	appears containing the default value.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-D58B193
* @see http://www.w3.org/Bugs/Public/show_bug.cgi?id=259
*/
function namednodemapremovenameditemns02() {
   var success;
    if(checkInitialization(builder, "namednodemapremovenameditemns02") != null) return;
    var doc;
      var attributes;
      var element;
      var attribute;
      var elementList;
      var attrValue;
      var nullNS = null;

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      elementList = doc.getElementsByTagNameNS("http://www.nist.gov","employee");
      element = elementList.item(1);
      attributes = element.attributes;

      attribute = attributes.removeNamedItemNS(nullNS,"defaultAttr");
      attribute = attributes.getNamedItemNS(nullNS,"defaultAttr");
      attrValue = attribute.nodeValue;

      assertNotNull("namednodemapremovenameditemns02",attribute);
assertEquals("namednodemapremovenameditemns02_attrValue","defaultVal",attrValue);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/namednodemapremovenameditemns02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
