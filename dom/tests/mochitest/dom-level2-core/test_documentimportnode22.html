<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode22</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['documentimportnode22'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();
       setImplementationAttribute("namespaceAware", true);

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'documentimportnode22';


/**
* 
	The importNode method imports a node from another document to this document. 
	The returned node has no parent; (parentNode is null). The source node is not 
	altered or removed from the original document but a new copy of the source node
	is created.
	
	Using the method importNode with deep=true/false, import two notaiton nodes into the 
	same and different documnet objects.  In each case check if valid public and systemids 
	are returned if any and if none, check if a null value was returned.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function documentimportnode22() {
   var success;
    if(checkInitialization(builder, "documentimportnode22") != null) return;
    var doc;
      var docTypeNull = null;

      var docImp;
      var domImpl;
      var docType;
      var nodeMap;
      var notation1;
      var notation2;
      var notationImp1;
      var notationImp2;
      var notationImpNew1;
      var notationImpNew2;
      var publicId1;
      var publicId1Imp;
      var publicId1NewImp;
      var publicId2Imp;
      var publicId2NewImp;
      var systemId1Imp;
      var systemId1NewImp;
      var systemId2;
      var systemId2Imp;
      var systemId2NewImp;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      domImpl = doc.implementation;
docType = doc.doctype;

      docImp = domImpl.createDocument("http://www.w3.org/DOM/Test","a:b",docTypeNull);
      nodeMap = docType.notations;

      assertNotNull("notationsNotNull",nodeMap);
notation1 = nodeMap.getNamedItem("notation1");
      notation2 = nodeMap.getNamedItem("notation2");
      notationImp1 = doc.importNode(notation1,true);
      notationImp2 = doc.importNode(notation2,false);
      notationImpNew1 = docImp.importNode(notation1,false);
      notationImpNew2 = docImp.importNode(notation2,true);
      publicId1 = notation1.publicId;

      publicId1Imp = notation1.publicId;

      publicId1NewImp = notation1.publicId;

      systemId1Imp = notation1.systemId;

      systemId1NewImp = notation1.systemId;

      publicId2Imp = notation2.publicId;

      publicId2NewImp = notation2.publicId;

      systemId2 = notation2.systemId;

      systemId2Imp = notation2.systemId;

      systemId2NewImp = notation2.systemId;

      assertEquals("documentimportnode22_N1PID",publicId1,publicId1Imp);
       assertEquals("documentimportnode22_N1NPID",publicId1,publicId1NewImp);
       assertNull("documentimportnode22_N1SID",systemId1Imp);
    assertNull("documentimportnode22_N1NSID",systemId1NewImp);
    assertEquals("documentimportnode22_N2SID",systemId2,systemId2Imp);
       assertEquals("documentimportnode22_N2NSID",systemId2,systemId2NewImp);
       assertNull("documentimportnode22_N2PID",publicId2Imp);
    assertNull("documentimportnode22_N2NPID",publicId2Imp);
    
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentimportnode22</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
