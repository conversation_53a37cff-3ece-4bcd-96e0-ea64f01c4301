<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentcreateattributeNS02</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['documentcreateattributeNS02'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'documentcreateattributeNS02';


/**
* 
	The method createAttributeNS creates an attribute of the given qualified name and namespace URI
	
	Invoke the createAttributeNS method on this Document object with a valid values for 
	namespaceURI, and a qualifiedName as below.  This should return a valid Attr node.

* <AUTHOR>
* <AUTHOR> Delima
* @see http://www.w3.org/TR/DOM-Level-2-Core/core
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-DocCrAttrNS
*/
function documentcreateattributeNS02() {
   var success;
    if(checkInitialization(builder, "documentcreateattributeNS02") != null) return;
    var doc;
      var attribute1;
      var attribute2;
      var name;
      var nodeName;
      var nodeValue;
      var prefix;
      var namespaceURI;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      attribute1 = doc.createAttributeNS("http://www.w3.org/XML/1998/namespace","xml:xml");
      name = attribute1.name;

      nodeName = attribute1.nodeName;

      nodeValue = attribute1.nodeValue;

      prefix = attribute1.prefix;

      namespaceURI = attribute1.namespaceURI;

      assertEquals("documentcreateattributeNS02_att1_name","xml:xml",name);
       assertEquals("documentcreateattributeNS02_att1_nodeName","xml:xml",nodeName);
       assertEquals("documentcreateattributeNS02_att1_nodeValue","",nodeValue);
       assertEquals("documentcreateattributeNS02_att1_prefix","xml",prefix);
       assertEquals("documentcreateattributeNS02_att1_namespaceURI","http://www.w3.org/XML/1998/namespace",namespaceURI);
       attribute2 = doc.createAttributeNS("http://www.w3.org/2000/xmlns/","xmlns");
      name = attribute2.name;

      nodeName = attribute2.nodeName;

      nodeValue = attribute2.nodeValue;

      prefix = attribute2.prefix;

      namespaceURI = attribute2.namespaceURI;

      assertEquals("documentcreateattributeNS02_att2_name","xmlns",name);
       assertEquals("documentcreateattributeNS02_att2_nodeName","xmlns",nodeName);
       assertEquals("documentcreateattributeNS02_att2_nodeValue","",nodeValue);
       assertEquals("documentcreateattributeNS02_att2_namespaceURI","http://www.w3.org/2000/xmlns/",namespaceURI);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/documentcreateattributeNS02</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
