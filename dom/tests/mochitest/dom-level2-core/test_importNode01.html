<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode01</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['importNode01'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      docsLoaded += preload(aNewDocRef, "aNewDoc", "staffNS");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'importNode01';


/**
* 
    The "importNode(importedNode,deep)" method for a 
   Document should import the given importedNode into that Document.
   The importedNode is of type Attr. 
   The ownerElement is set to null. Specified flag is set to true.
   Children is imported.
   
   Create a new attribute whose name is "elem:attr1" in a different document.
   Create a child Text node with value "importedText" for the attribute node above.
   Invoke method importNode(importedNode,deep) on this document with
   importedNode being the newly created attribute.
   Method should return a node whose name matches "elem:attr1" and a child node
   whose value equals "importedText".
   The returned node should belong to this document whose systemId is "staff.dtd"

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function importNode01() {
   var success;
    if(checkInitialization(builder, "importNode01") != null) return;
    var doc;
      var aNewDoc;
      var newAttr;
      var importedChild;
      var aNode;
      var ownerDocument;
      var attrOwnerElement;
      var docType;
      var system;
      var specified;
      var childList;
      var nodeName;
      var child;
      var childValue;
      var result = new Array();

      expectedResult = new Array();
      expectedResult[0] = "elem:attr1";
      expectedResult[1] = "importedText";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      aNewDoc = load(aNewDocRef, "aNewDoc", "staffNS");
      newAttr = aNewDoc.createAttribute("elem:attr1");
      importedChild = aNewDoc.createTextNode("importedText");
      aNode = newAttr.appendChild(importedChild);
      aNode = doc.importNode(newAttr,false);
      ownerDocument = aNode.ownerDocument;

      docType = ownerDocument.doctype;

      system = docType.systemId;

      assertNotNull("aNode",aNode);
assertURIEquals("systemId",null,null,null,"staffNS.dtd",null,null,null,null,system);
attrOwnerElement = aNode.ownerElement;

      assertNull("ownerElement",attrOwnerElement);
    specified = aNode.specified;

      assertTrue("specified",specified);
childList = aNode.childNodes;

      assertSize("childList",1,childList);
nodeName = aNode.nodeName;

      assertEquals("nodeName","elem:attr1",nodeName);
       child = aNode.firstChild;

      childValue = child.nodeValue;

      assertEquals("childValue","importedText",childValue);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode01</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
