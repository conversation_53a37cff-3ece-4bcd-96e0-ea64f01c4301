<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/createAttributeNS03</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['createAttributeNS03'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 1) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'createAttributeNS03';


/**
* 
    The "createAttributeNS(namespaceURI,qualifiedName)" method for a 
   Document should raise INVALID_CHARACTER_ERR DOMException
   if qualifiedName contains an illegal character.
   
   Invoke method createAttributeNS(namespaceURI,qualifiedName) on this document
   with qualifiedName containing an illegal character from illegalChars[].
   Method should raise INVALID_CHARACTER_ERR DOMException for all
   characters in illegalChars[].

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-DocCrAttrNS
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#xpointer(id('ID-DocCrAttrNS')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='INVALID_CHARACTER_ERR'])
*/
function createAttributeNS03() {
   var success;
    if(checkInitialization(builder, "createAttributeNS03") != null) return;
    var namespaceURI = "http://www.wedding.com/";
      var qualifiedName;
      var doc;
      var newAttr;
      illegalQNames = new Array();
      illegalQNames[0] = "person:{";
      illegalQNames[1] = "person:}";
      illegalQNames[2] = "person:~";
      illegalQNames[3] = "person:'";
      illegalQNames[4] = "person:!";
      illegalQNames[5] = "person:@";
      illegalQNames[6] = "person:#";
      illegalQNames[7] = "person:$";
      illegalQNames[8] = "person:%";
      illegalQNames[9] = "person:^";
      illegalQNames[10] = "person:&";
      illegalQNames[11] = "person:*";
      illegalQNames[12] = "person:(";
      illegalQNames[13] = "person:)";
      illegalQNames[14] = "person:+";
      illegalQNames[15] = "person:=";
      illegalQNames[16] = "person:[";
      illegalQNames[17] = "person:]";
      illegalQNames[18] = "person:\\";
      illegalQNames[19] = "person:/";
      illegalQNames[20] = "person:;";
      illegalQNames[21] = "person:`";
      illegalQNames[22] = "person:<";
      illegalQNames[23] = "person:>";
      illegalQNames[24] = "person:,";
      illegalQNames[25] = "person:a ";
      illegalQNames[26] = "person:\"";

      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      for(var indexN65680 = 0;indexN65680 < illegalQNames.length; indexN65680++) {
      qualifiedName = illegalQNames[indexN65680];
      
	{
		success = false;
		try {
            newAttr = doc.createAttributeNS(namespaceURI,qualifiedName);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 5);
		}
		assertTrue("throw_INVALID_CHARACTER_ERR",success);
	}

	}
   
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/createAttributeNS03</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
