<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN""http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode05</title>
<link rel="stylesheet" type="text/css" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js"></script>
<script type="text/javascript" src="DOMTestCase.js"></script>
<script type="text/javascript" src="exclusions.js"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['importNode05'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "staffNS");
        
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      docsLoaded += preload(aNewDocRef, "aNewDoc", "staffNS");
        
       if (docsLoaded == 2) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}

//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
  if (++docsLoaded == 2) {
    setUpPageStatus = 'complete';
    runJSUnitTests();
    markTodos();
    SimpleTest.finish();
  }
}

var docName = 'importNode05';


/**
* 
    The "importNode(importedNode,deep)" method for a 
   Document should import the given importedNode into that Document.
   The importedNode is of type Element.
   
   Retrieve element "emp:address" from staffNS.xml document.
   Invoke method importNode(importedNode,deep) on this document
   with importedNode being the element from above and deep is false.
   Method should return an element node whose name matches "emp:address" 
   and whose children are not imported. The returned node should 
   belong to this document whose systemId is "staff.dtd"

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#Core-Document-importNode
*/
function importNode05() {
   var success;
    if(checkInitialization(builder, "importNode05") != null) return;
    var doc;
      var aNewDoc;
      var element;
      var aNode;
      var hasChild;
      var ownerDocument;
      var docType;
      var system;
      var name;
      var addresses;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "staffNS");
      
      var aNewDocRef = null;
      if (typeof(this.aNewDoc) != 'undefined') {
        aNewDocRef = this.aNewDoc;
      }
      aNewDoc = load(aNewDocRef, "aNewDoc", "staffNS");
      addresses = aNewDoc.getElementsByTagName("emp:address");
      element = addresses.item(0);
      assertNotNull("empAddressNotNull",element);
aNode = doc.importNode(element,false);
      hasChild = aNode.hasChildNodes();
      assertFalse("hasChild",hasChild);
ownerDocument = aNode.ownerDocument;

      docType = ownerDocument.doctype;

      system = docType.systemId;

      assertURIEquals("dtdSystemId",null,null,null,"staffNS.dtd",null,null,null,null,system);
name = aNode.nodeName;

      assertEquals("nodeName","emp:address",name);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/core/importNode05</h2>
<p></p>
<p>
Copyright (c) 2001-2004 World Wide Web Consortium, 
(Massachusetts Institute of Technology, European Research Consortium 
for Informatics and Mathematics, Keio University). All 
Rights Reserved. This work is distributed under the <a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231">W3C(r) Software License</a> in the 
hope that it will be useful, but WITHOUT ANY WARRANTY; without even 
the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
</p>
</body>
</html>
