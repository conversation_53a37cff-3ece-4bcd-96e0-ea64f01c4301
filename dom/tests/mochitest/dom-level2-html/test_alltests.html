<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/html/alltests</title>
<link type="text/css" rel="stylesheet" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js" type="text/javascript"></script>
<script src="DOMTestCase.js" type="text/javascript"></script>
<script src="DOMTestSuite.js" type="text/javascript"></script>
<script type="text/javascript">
            
function suite() {
  var newsuite = new top.jsUnitTestSuite();
  var suiteBuilder = createConfiguredBuilder();
  var isHTML = (suiteBuilder.contentType == "text/html") || (suiteBuilder.contentType == "application/xhtml+xml");
    newsuite.addTestPage("../level2/html/HTMLAnchorElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLAnchorElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLAppletElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLAreaElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLBRElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLBaseElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLBaseElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLBodyElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLButtonElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection01.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection02.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection03.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection04.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection05.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection06.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection07.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection08.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection09.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection10.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection11.html");  
  newsuite.addTestPage("../level2/html/HTMLCollection12.html");  
  newsuite.addTestPage("../level2/html/HTMLDirectoryElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLDivElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLDlistElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument01.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument02.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument03.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument04.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument05.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument07.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument08.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument09.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument10.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument11.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument12.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument13.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument14.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument15.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument16.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument17.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument18.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument19.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument20.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument21.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument22.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument23.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument24.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument25.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument26.html");  
  newsuite.addTestPage("../level2/html/HTMLDocument27.html");  
  newsuite.addTestPage("../level2/html/HTMLElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLElement100.html");  
  newsuite.addTestPage("../level2/html/HTMLElement101.html");  
  newsuite.addTestPage("../level2/html/HTMLElement102.html");  
  newsuite.addTestPage("../level2/html/HTMLElement103.html");  
  newsuite.addTestPage("../level2/html/HTMLElement104.html");  
  newsuite.addTestPage("../level2/html/HTMLElement105.html");  
  newsuite.addTestPage("../level2/html/HTMLElement106.html");  
  newsuite.addTestPage("../level2/html/HTMLElement107.html");  
  newsuite.addTestPage("../level2/html/HTMLElement108.html");  
  newsuite.addTestPage("../level2/html/HTMLElement109.html");  
  newsuite.addTestPage("../level2/html/HTMLElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLElement110.html");  
  newsuite.addTestPage("../level2/html/HTMLElement111.html");  
  newsuite.addTestPage("../level2/html/HTMLElement112.html");  
  newsuite.addTestPage("../level2/html/HTMLElement113.html");  
  newsuite.addTestPage("../level2/html/HTMLElement114.html");  
  newsuite.addTestPage("../level2/html/HTMLElement115.html");  
  newsuite.addTestPage("../level2/html/HTMLElement116.html");  
  newsuite.addTestPage("../level2/html/HTMLElement117.html");  
  newsuite.addTestPage("../level2/html/HTMLElement118.html");  
  newsuite.addTestPage("../level2/html/HTMLElement119.html");  
  newsuite.addTestPage("../level2/html/HTMLElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLElement120.html");  
  newsuite.addTestPage("../level2/html/HTMLElement121.html");  
  newsuite.addTestPage("../level2/html/HTMLElement122.html");  
  newsuite.addTestPage("../level2/html/HTMLElement123.html");  
  newsuite.addTestPage("../level2/html/HTMLElement124.html");  
  newsuite.addTestPage("../level2/html/HTMLElement125.html");  
  newsuite.addTestPage("../level2/html/HTMLElement126.html");  
  newsuite.addTestPage("../level2/html/HTMLElement127.html");  
  newsuite.addTestPage("../level2/html/HTMLElement128.html");  
  newsuite.addTestPage("../level2/html/HTMLElement129.html");  
  newsuite.addTestPage("../level2/html/HTMLElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLElement130.html");  
  newsuite.addTestPage("../level2/html/HTMLElement131.html");  
  newsuite.addTestPage("../level2/html/HTMLElement132.html");  
  newsuite.addTestPage("../level2/html/HTMLElement133.html");  
  newsuite.addTestPage("../level2/html/HTMLElement134.html");  
  newsuite.addTestPage("../level2/html/HTMLElement135.html");  
  newsuite.addTestPage("../level2/html/HTMLElement136.html");  
  newsuite.addTestPage("../level2/html/HTMLElement137.html");  
  newsuite.addTestPage("../level2/html/HTMLElement138.html");  
  newsuite.addTestPage("../level2/html/HTMLElement139.html");  
  newsuite.addTestPage("../level2/html/HTMLElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLElement140.html");  
  newsuite.addTestPage("../level2/html/HTMLElement141.html");  
  newsuite.addTestPage("../level2/html/HTMLElement142.html");  
  newsuite.addTestPage("../level2/html/HTMLElement143.html");  
  newsuite.addTestPage("../level2/html/HTMLElement144.html");  
  newsuite.addTestPage("../level2/html/HTMLElement145.html");  
  newsuite.addTestPage("../level2/html/HTMLElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLElement22.html");  
  newsuite.addTestPage("../level2/html/HTMLElement23.html");  
  newsuite.addTestPage("../level2/html/HTMLElement24.html");  
  newsuite.addTestPage("../level2/html/HTMLElement25.html");  
  newsuite.addTestPage("../level2/html/HTMLElement26.html");  
  newsuite.addTestPage("../level2/html/HTMLElement27.html");  
  newsuite.addTestPage("../level2/html/HTMLElement28.html");  
  newsuite.addTestPage("../level2/html/HTMLElement29.html");  
  newsuite.addTestPage("../level2/html/HTMLElement30.html");  
  newsuite.addTestPage("../level2/html/HTMLElement31.html");  
  newsuite.addTestPage("../level2/html/HTMLElement32.html");  
  newsuite.addTestPage("../level2/html/HTMLElement33.html");  
  newsuite.addTestPage("../level2/html/HTMLElement34.html");  
  newsuite.addTestPage("../level2/html/HTMLElement35.html");  
  newsuite.addTestPage("../level2/html/HTMLElement36.html");  
  newsuite.addTestPage("../level2/html/HTMLElement37.html");  
  newsuite.addTestPage("../level2/html/HTMLElement38.html");  
  newsuite.addTestPage("../level2/html/HTMLElement39.html");  
  newsuite.addTestPage("../level2/html/HTMLElement40.html");  
  newsuite.addTestPage("../level2/html/HTMLElement41.html");  
  newsuite.addTestPage("../level2/html/HTMLElement42.html");  
  newsuite.addTestPage("../level2/html/HTMLElement43.html");  
  newsuite.addTestPage("../level2/html/HTMLElement44.html");  
  newsuite.addTestPage("../level2/html/HTMLElement45.html");  
  newsuite.addTestPage("../level2/html/HTMLElement46.html");  
  newsuite.addTestPage("../level2/html/HTMLElement47.html");  
  newsuite.addTestPage("../level2/html/HTMLElement48.html");  
  newsuite.addTestPage("../level2/html/HTMLElement49.html");  
  newsuite.addTestPage("../level2/html/HTMLElement50.html");  
  newsuite.addTestPage("../level2/html/HTMLElement51.html");  
  newsuite.addTestPage("../level2/html/HTMLElement52.html");  
  newsuite.addTestPage("../level2/html/HTMLElement53.html");  
  newsuite.addTestPage("../level2/html/HTMLElement54.html");  
  newsuite.addTestPage("../level2/html/HTMLElement55.html");  
  newsuite.addTestPage("../level2/html/HTMLElement56.html");  
  newsuite.addTestPage("../level2/html/HTMLElement57.html");  
  newsuite.addTestPage("../level2/html/HTMLElement58.html");  
  newsuite.addTestPage("../level2/html/HTMLElement59.html");  
  newsuite.addTestPage("../level2/html/HTMLElement60.html");  
  newsuite.addTestPage("../level2/html/HTMLElement61.html");  
  newsuite.addTestPage("../level2/html/HTMLElement62.html");  
  newsuite.addTestPage("../level2/html/HTMLElement63.html");  
  newsuite.addTestPage("../level2/html/HTMLElement64.html");  
  newsuite.addTestPage("../level2/html/HTMLElement65.html");  
  newsuite.addTestPage("../level2/html/HTMLElement66.html");  
  newsuite.addTestPage("../level2/html/HTMLElement67.html");  
  newsuite.addTestPage("../level2/html/HTMLElement68.html");  
  newsuite.addTestPage("../level2/html/HTMLElement69.html");  
  newsuite.addTestPage("../level2/html/HTMLElement70.html");  
  newsuite.addTestPage("../level2/html/HTMLElement71.html");  
  newsuite.addTestPage("../level2/html/HTMLElement72.html");  
  newsuite.addTestPage("../level2/html/HTMLElement73.html");  
  newsuite.addTestPage("../level2/html/HTMLElement74.html");  
  newsuite.addTestPage("../level2/html/HTMLElement75.html");  
  newsuite.addTestPage("../level2/html/HTMLElement76.html");  
  newsuite.addTestPage("../level2/html/HTMLElement77.html");  
  newsuite.addTestPage("../level2/html/HTMLElement78.html");  
  newsuite.addTestPage("../level2/html/HTMLElement79.html");  
  newsuite.addTestPage("../level2/html/HTMLElement80.html");  
  newsuite.addTestPage("../level2/html/HTMLElement81.html");  
  newsuite.addTestPage("../level2/html/HTMLElement82.html");  
  newsuite.addTestPage("../level2/html/HTMLElement83.html");  
  newsuite.addTestPage("../level2/html/HTMLElement84.html");  
  newsuite.addTestPage("../level2/html/HTMLElement85.html");  
  newsuite.addTestPage("../level2/html/HTMLElement86.html");  
  newsuite.addTestPage("../level2/html/HTMLElement87.html");  
  newsuite.addTestPage("../level2/html/HTMLElement88.html");  
  newsuite.addTestPage("../level2/html/HTMLElement89.html");  
  newsuite.addTestPage("../level2/html/HTMLElement90.html");  
  newsuite.addTestPage("../level2/html/HTMLElement91.html");  
  newsuite.addTestPage("../level2/html/HTMLElement92.html");  
  newsuite.addTestPage("../level2/html/HTMLElement93.html");  
  newsuite.addTestPage("../level2/html/HTMLElement94.html");  
  newsuite.addTestPage("../level2/html/HTMLElement95.html");  
  newsuite.addTestPage("../level2/html/HTMLElement96.html");  
  newsuite.addTestPage("../level2/html/HTMLElement97.html");  
  newsuite.addTestPage("../level2/html/HTMLElement98.html");  
  newsuite.addTestPage("../level2/html/HTMLElement99.html");  
  newsuite.addTestPage("../level2/html/HTMLFieldSetElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLFieldSetElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLFontElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLFontElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLFontElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLFormElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameSetElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLFrameSetElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLHRElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLHRElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLHRElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLHRElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLHeadingElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLHtmlElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLIFrameElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLImageElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLInputElement22.html");  
  newsuite.addTestPage("../level2/html/HTMLIsIndexElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLIsIndexElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLIsIndexElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLLIElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLLIElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLLabelElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLLabelElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLLabelElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLLabelElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLLegendElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLLegendElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLLegendElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLLegendElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLLinkElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLMapElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLMapElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLMenuElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLMetaElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLMetaElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLMetaElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLMetaElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLModElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLModElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLModElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLModElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLOListElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLOListElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLOListElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLObjectElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLOptGroupElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLOptGroupElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection01.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection02.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection03.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection04.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection05.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection06.html");  
  newsuite.addTestPage("../level2/html/HTMLOptionsCollection07.html");  
  newsuite.addTestPage("../level2/html/HTMLParagraphElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLParamElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLParamElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLParamElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLParamElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLPreElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLQuoteElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLQuoteElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLScriptElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLSelectElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLStyleElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLStyleElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLStyleElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCaptionElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement22.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement23.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement24.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement25.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement26.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement27.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement28.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement29.html");  
  newsuite.addTestPage("../level2/html/HTMLTableCellElement30.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTableColElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement22.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement23.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement24.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement25.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement26.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement27.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement28.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement29.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement30.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement31.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement32.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement33.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement34.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement35.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement36.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement37.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement38.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement39.html");  
  newsuite.addTestPage("../level2/html/HTMLTableElement40.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLTableRowElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement16.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement17.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement18.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement19.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement20.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement21.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement22.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement23.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement24.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement25.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement26.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement27.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement28.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement29.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement30.html");  
  newsuite.addTestPage("../level2/html/HTMLTableSectionElement31.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement02.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement03.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement04.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement05.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement06.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement07.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement08.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement09.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement10.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement11.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement12.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement13.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement14.html");  
  newsuite.addTestPage("../level2/html/HTMLTextAreaElement15.html");  
  newsuite.addTestPage("../level2/html/HTMLTitleElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLUListElement01.html");  
  newsuite.addTestPage("../level2/html/HTMLUListElement02.html");  
  newsuite.addTestPage("../level2/html/anchor01.html");  
  newsuite.addTestPage("../level2/html/anchor02.html");  
  newsuite.addTestPage("../level2/html/anchor03.html");  
  newsuite.addTestPage("../level2/html/anchor04.html");  
  newsuite.addTestPage("../level2/html/anchor05.html");  
  newsuite.addTestPage("../level2/html/anchor06.html");  
  newsuite.addTestPage("../level2/html/area01.html");  
  newsuite.addTestPage("../level2/html/area02.html");  
  newsuite.addTestPage("../level2/html/area03.html");  
  newsuite.addTestPage("../level2/html/area04.html");  
  newsuite.addTestPage("../level2/html/body01.html");  
  newsuite.addTestPage("../level2/html/button01.html");  
  newsuite.addTestPage("../level2/html/button02.html");  
  newsuite.addTestPage("../level2/html/button03.html");  
  newsuite.addTestPage("../level2/html/button04.html");  
  newsuite.addTestPage("../level2/html/button05.html");  
  newsuite.addTestPage("../level2/html/button06.html");  
  newsuite.addTestPage("../level2/html/button07.html");  
  newsuite.addTestPage("../level2/html/button08.html");  
  newsuite.addTestPage("../level2/html/button09.html");  
  newsuite.addTestPage("../level2/html/dlist01.html");  
  newsuite.addTestPage("../level2/html/doc01.html");  
  newsuite.addTestPage("../level2/html/hasFeature01.html");  
  newsuite.addTestPage("../level2/html/hasFeature02.html");  
  newsuite.addTestPage("../level2/html/hasFeature03.html");  
  newsuite.addTestPage("../level2/html/hasFeature04.html");  
  newsuite.addTestPage("../level2/html/hasFeature05.html");  
  newsuite.addTestPage("../level2/html/hasFeature06.html");  
  newsuite.addTestPage("../level2/html/object01.html");  
  newsuite.addTestPage("../level2/html/object02.html");  
  newsuite.addTestPage("../level2/html/object03.html");  
  newsuite.addTestPage("../level2/html/object04.html");  
  newsuite.addTestPage("../level2/html/object05.html");  
  newsuite.addTestPage("../level2/html/object06.html");  
  newsuite.addTestPage("../level2/html/object07.html");  
  newsuite.addTestPage("../level2/html/object08.html");  
  newsuite.addTestPage("../level2/html/object09.html");  
  newsuite.addTestPage("../level2/html/object10.html");  
  newsuite.addTestPage("../level2/html/object11.html");  
  newsuite.addTestPage("../level2/html/object12.html");  
  newsuite.addTestPage("../level2/html/object13.html");  
  newsuite.addTestPage("../level2/html/object14.html");  
  newsuite.addTestPage("../level2/html/object15.html");  
  newsuite.addTestPage("../level2/html/table01.html");  
  newsuite.addTestPage("../level2/html/table02.html");  
  newsuite.addTestPage("../level2/html/table03.html");  
  newsuite.addTestPage("../level2/html/table04.html");  
  newsuite.addTestPage("../level2/html/table06.html");  
  newsuite.addTestPage("../level2/html/table07.html");  
  newsuite.addTestPage("../level2/html/table08.html");  
  newsuite.addTestPage("../level2/html/table09.html");  
  newsuite.addTestPage("../level2/html/table10.html");  
  newsuite.addTestPage("../level2/html/table12.html");  
  newsuite.addTestPage("../level2/html/table15.html");  
  newsuite.addTestPage("../level2/html/table17.html");  
  newsuite.addTestPage("../level2/html/table18.html");  
  newsuite.addTestPage("../level2/html/table19.html");  
  newsuite.addTestPage("../level2/html/table20.html");  
  newsuite.addTestPage("../level2/html/table21.html");  
  newsuite.addTestPage("../level2/html/table22.html");  
  newsuite.addTestPage("../level2/html/table23.html");  
  newsuite.addTestPage("../level2/html/table24.html");  
  newsuite.addTestPage("../level2/html/table25.html");  
  newsuite.addTestPage("../level2/html/table26.html");  
  newsuite.addTestPage("../level2/html/table27.html");  
  newsuite.addTestPage("../level2/html/table28.html");  
  newsuite.addTestPage("../level2/html/table29.html");  
  newsuite.addTestPage("../level2/html/table30.html");  
  newsuite.addTestPage("../level2/html/table31.html");  
  newsuite.addTestPage("../level2/html/table32.html");  
  newsuite.addTestPage("../level2/html/table33.html");  
  newsuite.addTestPage("../level2/html/table34.html");  
  newsuite.addTestPage("../level2/html/table35.html");  
  newsuite.addTestPage("../level2/html/table36.html");  
  newsuite.addTestPage("../level2/html/table37.html");  
  newsuite.addTestPage("../level2/html/table38.html");  
  newsuite.addTestPage("../level2/html/table39.html");  
  newsuite.addTestPage("../level2/html/table40.html");  
  newsuite.addTestPage("../level2/html/table41.html");  
  newsuite.addTestPage("../level2/html/table42.html");  
  newsuite.addTestPage("../level2/html/table43.html");  
  newsuite.addTestPage("../level2/html/table44.html");  
  newsuite.addTestPage("../level2/html/table45.html");  
  newsuite.addTestPage("../level2/html/table46.html");  
  newsuite.addTestPage("../level2/html/table47.html");  
  newsuite.addTestPage("../level2/html/table48.html");  
  newsuite.addTestPage("../level2/html/table49.html");  
  newsuite.addTestPage("../level2/html/table50.html");  
  newsuite.addTestPage("../level2/html/table51.html");  
  newsuite.addTestPage("../level2/html/table52.html");  
  newsuite.addTestPage("../level2/html/table53.html");  

  return newsuite;
}
</script>
</head>
<body onload="onImplementationChange()">
<h1>DOM Level 2 HTML Test Suite</h1>
<form onsubmit="fixTestPagePath()" action="../../jsunit/testRunner.html" id="configuration">
<table summary="Configuration" border="1" width="100%">
<tr>
<td>
<table summary="Tests" width="100%">
<tr>
<td>Test: </td><td><select size="1" name="testpage"><option value="alltests.html" selected>alltests</option><option value="anchor01.html">anchor01</option>




<option value="anchor02.html">anchor02</option>




<option value="anchor03.html">anchor03</option>




<option value="anchor04.html">anchor04</option>




<option value="anchor05.html">anchor05</option>




<option value="anchor06.html">anchor06</option>




<option value="area01.html">area01</option>




<option value="area02.html">area02</option>




<option value="area03.html">area03</option>




<option value="area04.html">area04</option>




<option value="body01.html">body01</option>




<option value="button01.html">button01</option>




<option value="button02.html">button02</option>




<option value="button03.html">button03</option>




<option value="button04.html">button04</option>




<option value="button05.html">button05</option>




<option value="button06.html">button06</option>




<option value="button07.html">button07</option>




<option value="button08.html">button08</option>




<option value="button09.html">button09</option>




<option value="dlist01.html">dlist01</option>




<option value="doc01.html">doc01</option>




<option value="hasFeature01.html">hasFeature01</option>




<option value="HTMLAnchorElement01.html">HTMLAnchorElement01</option>




<option value="HTMLAnchorElement02.html">HTMLAnchorElement02</option>




<option value="HTMLAnchorElement03.html">HTMLAnchorElement03</option>




<option value="HTMLAnchorElement04.html">HTMLAnchorElement04</option>




<option value="HTMLAnchorElement05.html">HTMLAnchorElement05</option>




<option value="HTMLAnchorElement06.html">HTMLAnchorElement06</option>




<option value="HTMLAnchorElement07.html">HTMLAnchorElement07</option>




<option value="HTMLAnchorElement08.html">HTMLAnchorElement08</option>




<option value="HTMLAnchorElement09.html">HTMLAnchorElement09</option>




<option value="HTMLAnchorElement10.html">HTMLAnchorElement10</option>




<option value="HTMLAnchorElement11.html">HTMLAnchorElement11</option>




<option value="HTMLAnchorElement12.html">HTMLAnchorElement12</option>




<option value="HTMLAnchorElement13.html">HTMLAnchorElement13</option>




<option value="HTMLAnchorElement14.html">HTMLAnchorElement14</option>




<option value="HTMLAppletElement01.html">HTMLAppletElement01</option>




<option value="HTMLAppletElement02.html">HTMLAppletElement02</option>




<option value="HTMLAppletElement03.html">HTMLAppletElement03</option>




<option value="HTMLAppletElement04.html">HTMLAppletElement04</option>




<option value="HTMLAppletElement05.html">HTMLAppletElement05</option>




<option value="HTMLAppletElement06.html">HTMLAppletElement06</option>




<option value="HTMLAppletElement08.html">HTMLAppletElement08</option>




<option value="HTMLAppletElement10.html">HTMLAppletElement10</option>




<option value="HTMLAppletElement11.html">HTMLAppletElement11</option>




<option value="HTMLAreaElement01.html">HTMLAreaElement01</option>




<option value="HTMLAreaElement02.html">HTMLAreaElement02</option>




<option value="HTMLAreaElement03.html">HTMLAreaElement03</option>




<option value="HTMLAreaElement04.html">HTMLAreaElement04</option>




<option value="HTMLAreaElement05.html">HTMLAreaElement05</option>




<option value="HTMLAreaElement06.html">HTMLAreaElement06</option>




<option value="HTMLAreaElement07.html">HTMLAreaElement07</option>




<option value="HTMLAreaElement08.html">HTMLAreaElement08</option>




<option value="HTMLBaseElement01.html">HTMLBaseElement01</option>




<option value="HTMLBaseElement02.html">HTMLBaseElement02</option>




<option value="HTMLBodyElement01.html">HTMLBodyElement01</option>




<option value="HTMLBodyElement02.html">HTMLBodyElement02</option>




<option value="HTMLBodyElement03.html">HTMLBodyElement03</option>




<option value="HTMLBodyElement04.html">HTMLBodyElement04</option>




<option value="HTMLBodyElement05.html">HTMLBodyElement05</option>




<option value="HTMLBodyElement06.html">HTMLBodyElement06</option>




<option value="HTMLBRElement01.html">HTMLBRElement01</option>




<option value="HTMLButtonElement01.html">HTMLButtonElement01</option>




<option value="HTMLButtonElement02.html">HTMLButtonElement02</option>




<option value="HTMLButtonElement03.html">HTMLButtonElement03</option>




<option value="HTMLButtonElement04.html">HTMLButtonElement04</option>




<option value="HTMLButtonElement05.html">HTMLButtonElement05</option>




<option value="HTMLButtonElement06.html">HTMLButtonElement06</option>




<option value="HTMLButtonElement07.html">HTMLButtonElement07</option>




<option value="HTMLButtonElement08.html">HTMLButtonElement08</option>




<option value="HTMLCollection01.html">HTMLCollection01</option>




<option value="HTMLCollection02.html">HTMLCollection02</option>




<option value="HTMLCollection03.html">HTMLCollection03</option>




<option value="HTMLCollection04.html">HTMLCollection04</option>




<option value="HTMLCollection05.html">HTMLCollection05</option>




<option value="HTMLCollection06.html">HTMLCollection06</option>




<option value="HTMLCollection07.html">HTMLCollection07</option>




<option value="HTMLCollection08.html">HTMLCollection08</option>




<option value="HTMLCollection09.html">HTMLCollection09</option>




<option value="HTMLCollection10.html">HTMLCollection10</option>




<option value="HTMLCollection11.html">HTMLCollection11</option>




<option value="HTMLCollection12.html">HTMLCollection12</option>




<option value="HTMLDirectoryElement01.html">HTMLDirectoryElement01</option>




<option value="HTMLDivElement01.html">HTMLDivElement01</option>




<option value="HTMLDlistElement01.html">HTMLDlistElement01</option>




<option value="HTMLDocument01.html">HTMLDocument01</option>




<option value="HTMLDocument02.html">HTMLDocument02</option>




<option value="HTMLDocument03.html">HTMLDocument03</option>




<option value="HTMLDocument04.html">HTMLDocument04</option>




<option value="HTMLDocument05.html">HTMLDocument05</option>




<option value="HTMLDocument07.html">HTMLDocument07</option>




<option value="HTMLDocument08.html">HTMLDocument08</option>




<option value="HTMLDocument09.html">HTMLDocument09</option>




<option value="HTMLDocument10.html">HTMLDocument10</option>




<option value="HTMLDocument11.html">HTMLDocument11</option>




<option value="HTMLDocument12.html">HTMLDocument12</option>




<option value="HTMLDocument13.html">HTMLDocument13</option>




<option value="HTMLDocument14.html">HTMLDocument14</option>




<option value="HTMLDocument15.html">HTMLDocument15</option>




<option value="HTMLDocument16.html">HTMLDocument16</option>




<option value="HTMLDocument17.html">HTMLDocument17</option>




<option value="HTMLDocument18.html">HTMLDocument18</option>




<option value="HTMLDocument19.html">HTMLDocument19</option>




<option value="HTMLDocument20.html">HTMLDocument20</option>




<option value="HTMLDocument21.html">HTMLDocument21</option>




<option value="HTMLElement01.html">HTMLElement01</option>




<option value="HTMLElement02.html">HTMLElement02</option>




<option value="HTMLElement03.html">HTMLElement03</option>




<option value="HTMLElement04.html">HTMLElement04</option>




<option value="HTMLElement05.html">HTMLElement05</option>




<option value="HTMLElement06.html">HTMLElement06</option>




<option value="HTMLElement07.html">HTMLElement07</option>




<option value="HTMLElement08.html">HTMLElement08</option>




<option value="HTMLElement09.html">HTMLElement09</option>




<option value="HTMLElement10.html">HTMLElement10</option>




<option value="HTMLElement100.html">HTMLElement100</option>




<option value="HTMLElement101.html">HTMLElement101</option>




<option value="HTMLElement102.html">HTMLElement102</option>




<option value="HTMLElement103.html">HTMLElement103</option>




<option value="HTMLElement104.html">HTMLElement104</option>




<option value="HTMLElement105.html">HTMLElement105</option>




<option value="HTMLElement106.html">HTMLElement106</option>




<option value="HTMLElement107.html">HTMLElement107</option>




<option value="HTMLElement108.html">HTMLElement108</option>




<option value="HTMLElement109.html">HTMLElement109</option>




<option value="HTMLElement11.html">HTMLElement11</option>




<option value="HTMLElement110.html">HTMLElement110</option>




<option value="HTMLElement111.html">HTMLElement111</option>




<option value="HTMLElement112.html">HTMLElement112</option>




<option value="HTMLElement113.html">HTMLElement113</option>




<option value="HTMLElement114.html">HTMLElement114</option>




<option value="HTMLElement115.html">HTMLElement115</option>




<option value="HTMLElement116.html">HTMLElement116</option>




<option value="HTMLElement117.html">HTMLElement117</option>




<option value="HTMLElement118.html">HTMLElement118</option>




<option value="HTMLElement119.html">HTMLElement119</option>




<option value="HTMLElement12.html">HTMLElement12</option>




<option value="HTMLElement120.html">HTMLElement120</option>




<option value="HTMLElement121.html">HTMLElement121</option>




<option value="HTMLElement122.html">HTMLElement122</option>




<option value="HTMLElement123.html">HTMLElement123</option>




<option value="HTMLElement124.html">HTMLElement124</option>




<option value="HTMLElement125.html">HTMLElement125</option>




<option value="HTMLElement126.html">HTMLElement126</option>




<option value="HTMLElement127.html">HTMLElement127</option>




<option value="HTMLElement128.html">HTMLElement128</option>




<option value="HTMLElement129.html">HTMLElement129</option>




<option value="HTMLElement13.html">HTMLElement13</option>




<option value="HTMLElement130.html">HTMLElement130</option>




<option value="HTMLElement131.html">HTMLElement131</option>




<option value="HTMLElement132.html">HTMLElement132</option>




<option value="HTMLElement133.html">HTMLElement133</option>




<option value="HTMLElement134.html">HTMLElement134</option>




<option value="HTMLElement135.html">HTMLElement135</option>




<option value="HTMLElement136.html">HTMLElement136</option>




<option value="HTMLElement137.html">HTMLElement137</option>




<option value="HTMLElement138.html">HTMLElement138</option>




<option value="HTMLElement139.html">HTMLElement139</option>




<option value="HTMLElement14.html">HTMLElement14</option>




<option value="HTMLElement140.html">HTMLElement140</option>




<option value="HTMLElement141.html">HTMLElement141</option>




<option value="HTMLElement142.html">HTMLElement142</option>




<option value="HTMLElement143.html">HTMLElement143</option>




<option value="HTMLElement144.html">HTMLElement144</option>




<option value="HTMLElement145.html">HTMLElement145</option>




<option value="HTMLElement15.html">HTMLElement15</option>




<option value="HTMLElement16.html">HTMLElement16</option>




<option value="HTMLElement17.html">HTMLElement17</option>




<option value="HTMLElement18.html">HTMLElement18</option>




<option value="HTMLElement19.html">HTMLElement19</option>




<option value="HTMLElement20.html">HTMLElement20</option>




<option value="HTMLElement21.html">HTMLElement21</option>




<option value="HTMLElement22.html">HTMLElement22</option>




<option value="HTMLElement23.html">HTMLElement23</option>




<option value="HTMLElement24.html">HTMLElement24</option>




<option value="HTMLElement25.html">HTMLElement25</option>




<option value="HTMLElement26.html">HTMLElement26</option>




<option value="HTMLElement27.html">HTMLElement27</option>




<option value="HTMLElement28.html">HTMLElement28</option>




<option value="HTMLElement29.html">HTMLElement29</option>




<option value="HTMLElement30.html">HTMLElement30</option>




<option value="HTMLElement31.html">HTMLElement31</option>




<option value="HTMLElement32.html">HTMLElement32</option>




<option value="HTMLElement33.html">HTMLElement33</option>




<option value="HTMLElement34.html">HTMLElement34</option>




<option value="HTMLElement35.html">HTMLElement35</option>




<option value="HTMLElement36.html">HTMLElement36</option>




<option value="HTMLElement37.html">HTMLElement37</option>




<option value="HTMLElement38.html">HTMLElement38</option>




<option value="HTMLElement39.html">HTMLElement39</option>




<option value="HTMLElement40.html">HTMLElement40</option>




<option value="HTMLElement41.html">HTMLElement41</option>




<option value="HTMLElement42.html">HTMLElement42</option>




<option value="HTMLElement43.html">HTMLElement43</option>




<option value="HTMLElement44.html">HTMLElement44</option>




<option value="HTMLElement45.html">HTMLElement45</option>




<option value="HTMLElement46.html">HTMLElement46</option>




<option value="HTMLElement47.html">HTMLElement47</option>




<option value="HTMLElement48.html">HTMLElement48</option>




<option value="HTMLElement49.html">HTMLElement49</option>




<option value="HTMLElement50.html">HTMLElement50</option>




<option value="HTMLElement51.html">HTMLElement51</option>




<option value="HTMLElement52.html">HTMLElement52</option>




<option value="HTMLElement53.html">HTMLElement53</option>




<option value="HTMLElement54.html">HTMLElement54</option>




<option value="HTMLElement55.html">HTMLElement55</option>




<option value="HTMLElement56.html">HTMLElement56</option>




<option value="HTMLElement57.html">HTMLElement57</option>




<option value="HTMLElement58.html">HTMLElement58</option>




<option value="HTMLElement59.html">HTMLElement59</option>




<option value="HTMLElement60.html">HTMLElement60</option>




<option value="HTMLElement61.html">HTMLElement61</option>




<option value="HTMLElement62.html">HTMLElement62</option>




<option value="HTMLElement63.html">HTMLElement63</option>




<option value="HTMLElement64.html">HTMLElement64</option>




<option value="HTMLElement65.html">HTMLElement65</option>




<option value="HTMLElement66.html">HTMLElement66</option>




<option value="HTMLElement67.html">HTMLElement67</option>




<option value="HTMLElement68.html">HTMLElement68</option>




<option value="HTMLElement69.html">HTMLElement69</option>




<option value="HTMLElement70.html">HTMLElement70</option>




<option value="HTMLElement71.html">HTMLElement71</option>




<option value="HTMLElement72.html">HTMLElement72</option>




<option value="HTMLElement73.html">HTMLElement73</option>




<option value="HTMLElement74.html">HTMLElement74</option>




<option value="HTMLElement75.html">HTMLElement75</option>




<option value="HTMLElement76.html">HTMLElement76</option>




<option value="HTMLElement77.html">HTMLElement77</option>




<option value="HTMLElement78.html">HTMLElement78</option>




<option value="HTMLElement79.html">HTMLElement79</option>




<option value="HTMLElement80.html">HTMLElement80</option>




<option value="HTMLElement81.html">HTMLElement81</option>




<option value="HTMLElement82.html">HTMLElement82</option>




<option value="HTMLElement83.html">HTMLElement83</option>




<option value="HTMLElement84.html">HTMLElement84</option>




<option value="HTMLElement85.html">HTMLElement85</option>




<option value="HTMLElement86.html">HTMLElement86</option>




<option value="HTMLElement87.html">HTMLElement87</option>




<option value="HTMLElement88.html">HTMLElement88</option>




<option value="HTMLElement89.html">HTMLElement89</option>




<option value="HTMLElement90.html">HTMLElement90</option>




<option value="HTMLElement91.html">HTMLElement91</option>




<option value="HTMLElement92.html">HTMLElement92</option>




<option value="HTMLElement93.html">HTMLElement93</option>




<option value="HTMLElement94.html">HTMLElement94</option>




<option value="HTMLElement95.html">HTMLElement95</option>




<option value="HTMLElement96.html">HTMLElement96</option>




<option value="HTMLElement97.html">HTMLElement97</option>




<option value="HTMLElement98.html">HTMLElement98</option>




<option value="HTMLElement99.html">HTMLElement99</option>




<option value="HTMLFieldSetElement01.html">HTMLFieldSetElement01</option>




<option value="HTMLFieldSetElement02.html">HTMLFieldSetElement02</option>




<option value="HTMLFontElement01.html">HTMLFontElement01</option>




<option value="HTMLFontElement02.html">HTMLFontElement02</option>




<option value="HTMLFontElement03.html">HTMLFontElement03</option>




<option value="HTMLFormElement01.html">HTMLFormElement01</option>




<option value="HTMLFormElement02.html">HTMLFormElement02</option>




<option value="HTMLFormElement03.html">HTMLFormElement03</option>




<option value="HTMLFormElement04.html">HTMLFormElement04</option>




<option value="HTMLFormElement05.html">HTMLFormElement05</option>




<option value="HTMLFormElement06.html">HTMLFormElement06</option>




<option value="HTMLFormElement07.html">HTMLFormElement07</option>




<option value="HTMLFormElement08.html">HTMLFormElement08</option>




<option value="HTMLFormElement09.html">HTMLFormElement09</option>




<option value="HTMLFormElement10.html">HTMLFormElement10</option>




<option value="HTMLFrameElement01.html">HTMLFrameElement01</option>




<option value="HTMLFrameElement02.html">HTMLFrameElement02</option>




<option value="HTMLFrameElement03.html">HTMLFrameElement03</option>




<option value="HTMLFrameElement04.html">HTMLFrameElement04</option>




<option value="HTMLFrameElement05.html">HTMLFrameElement05</option>




<option value="HTMLFrameElement06.html">HTMLFrameElement06</option>




<option value="HTMLFrameElement07.html">HTMLFrameElement07</option>




<option value="HTMLFrameElement08.html">HTMLFrameElement08</option>




<option value="HTMLFrameSetElement01.html">HTMLFrameSetElement01</option>




<option value="HTMLFrameSetElement02.html">HTMLFrameSetElement02</option>




<option value="HTMLHeadElement01.html">HTMLHeadElement01</option>




<option value="HTMLHeadingElement01.html">HTMLHeadingElement01</option>




<option value="HTMLHeadingElement02.html">HTMLHeadingElement02</option>




<option value="HTMLHeadingElement03.html">HTMLHeadingElement03</option>




<option value="HTMLHeadingElement04.html">HTMLHeadingElement04</option>




<option value="HTMLHeadingElement05.html">HTMLHeadingElement05</option>




<option value="HTMLHeadingElement06.html">HTMLHeadingElement06</option>




<option value="HTMLHRElement01.html">HTMLHRElement01</option>




<option value="HTMLHRElement02.html">HTMLHRElement02</option>




<option value="HTMLHRElement03.html">HTMLHRElement03</option>




<option value="HTMLHRElement04.html">HTMLHRElement04</option>




<option value="HTMLHtmlElement01.html">HTMLHtmlElement01</option>




<option value="HTMLIFrameElement01.html">HTMLIFrameElement01</option>




<option value="HTMLIFrameElement02.html">HTMLIFrameElement02</option>




<option value="HTMLIFrameElement03.html">HTMLIFrameElement03</option>




<option value="HTMLIFrameElement04.html">HTMLIFrameElement04</option>




<option value="HTMLIFrameElement05.html">HTMLIFrameElement05</option>




<option value="HTMLIFrameElement06.html">HTMLIFrameElement06</option>




<option value="HTMLIFrameElement07.html">HTMLIFrameElement07</option>




<option value="HTMLIFrameElement08.html">HTMLIFrameElement08</option>




<option value="HTMLIFrameElement09.html">HTMLIFrameElement09</option>




<option value="HTMLIFrameElement10.html">HTMLIFrameElement10</option>




<option value="HTMLImageElement01.html">HTMLImageElement01</option>




<option value="HTMLImageElement02.html">HTMLImageElement02</option>




<option value="HTMLImageElement03.html">HTMLImageElement03</option>




<option value="HTMLImageElement04.html">HTMLImageElement04</option>




<option value="HTMLImageElement07.html">HTMLImageElement07</option>




<option value="HTMLImageElement08.html">HTMLImageElement08</option>




<option value="HTMLImageElement09.html">HTMLImageElement09</option>




<option value="HTMLImageElement10.html">HTMLImageElement10</option>




<option value="HTMLInputElement01.html">HTMLInputElement01</option>




<option value="HTMLInputElement02.html">HTMLInputElement02</option>




<option value="HTMLInputElement03.html">HTMLInputElement03</option>




<option value="HTMLInputElement04.html">HTMLInputElement04</option>




<option value="HTMLInputElement05.html">HTMLInputElement05</option>




<option value="HTMLInputElement06.html">HTMLInputElement06</option>




<option value="HTMLInputElement07.html">HTMLInputElement07</option>




<option value="HTMLInputElement08.html">HTMLInputElement08</option>




<option value="HTMLInputElement09.html">HTMLInputElement09</option>




<option value="HTMLInputElement10.html">HTMLInputElement10</option>




<option value="HTMLInputElement11.html">HTMLInputElement11</option>




<option value="HTMLInputElement12.html">HTMLInputElement12</option>




<option value="HTMLInputElement14.html">HTMLInputElement14</option>




<option value="HTMLInputElement15.html">HTMLInputElement15</option>




<option value="HTMLInputElement16.html">HTMLInputElement16</option>




<option value="HTMLInputElement17.html">HTMLInputElement17</option>




<option value="HTMLInputElement18.html">HTMLInputElement18</option>




<option value="HTMLInputElement19.html">HTMLInputElement19</option>




<option value="HTMLInputElement20.html">HTMLInputElement20</option>




<option value="HTMLInputElement21.html">HTMLInputElement21</option>




<option value="HTMLInputElement22.html">HTMLInputElement22</option>




<option value="HTMLIsIndexElement01.html">HTMLIsIndexElement01</option>




<option value="HTMLIsIndexElement02.html">HTMLIsIndexElement02</option>




<option value="HTMLIsIndexElement03.html">HTMLIsIndexElement03</option>




<option value="HTMLLabelElement01.html">HTMLLabelElement01</option>




<option value="HTMLLabelElement02.html">HTMLLabelElement02</option>




<option value="HTMLLabelElement03.html">HTMLLabelElement03</option>




<option value="HTMLLabelElement04.html">HTMLLabelElement04</option>




<option value="HTMLLegendElement01.html">HTMLLegendElement01</option>




<option value="HTMLLegendElement02.html">HTMLLegendElement02</option>




<option value="HTMLLegendElement03.html">HTMLLegendElement03</option>




<option value="HTMLLegendElement04.html">HTMLLegendElement04</option>




<option value="HTMLLIElement01.html">HTMLLIElement01</option>




<option value="HTMLLIElement02.html">HTMLLIElement02</option>




<option value="HTMLLinkElement01.html">HTMLLinkElement01</option>




<option value="HTMLLinkElement02.html">HTMLLinkElement02</option>




<option value="HTMLLinkElement03.html">HTMLLinkElement03</option>




<option value="HTMLLinkElement04.html">HTMLLinkElement04</option>




<option value="HTMLLinkElement05.html">HTMLLinkElement05</option>




<option value="HTMLLinkElement06.html">HTMLLinkElement06</option>




<option value="HTMLLinkElement07.html">HTMLLinkElement07</option>




<option value="HTMLLinkElement08.html">HTMLLinkElement08</option>




<option value="HTMLLinkElement09.html">HTMLLinkElement09</option>




<option value="HTMLMapElement01.html">HTMLMapElement01</option>




<option value="HTMLMapElement02.html">HTMLMapElement02</option>




<option value="HTMLMenuElement01.html">HTMLMenuElement01</option>




<option value="HTMLMetaElement01.html">HTMLMetaElement01</option>




<option value="HTMLMetaElement02.html">HTMLMetaElement02</option>




<option value="HTMLMetaElement03.html">HTMLMetaElement03</option>




<option value="HTMLMetaElement04.html">HTMLMetaElement04</option>




<option value="HTMLModElement01.html">HTMLModElement01</option>




<option value="HTMLModElement02.html">HTMLModElement02</option>




<option value="HTMLModElement03.html">HTMLModElement03</option>




<option value="HTMLModElement04.html">HTMLModElement04</option>




<option value="HTMLObjectElement01.html">HTMLObjectElement01</option>




<option value="HTMLObjectElement02.html">HTMLObjectElement02</option>




<option value="HTMLObjectElement03.html">HTMLObjectElement03</option>




<option value="HTMLObjectElement04.html">HTMLObjectElement04</option>




<option value="HTMLObjectElement05.html">HTMLObjectElement05</option>




<option value="HTMLObjectElement06.html">HTMLObjectElement06</option>




<option value="HTMLObjectElement07.html">HTMLObjectElement07</option>




<option value="HTMLObjectElement08.html">HTMLObjectElement08</option>




<option value="HTMLObjectElement09.html">HTMLObjectElement09</option>




<option value="HTMLObjectElement10.html">HTMLObjectElement10</option>




<option value="HTMLObjectElement12.html">HTMLObjectElement12</option>




<option value="HTMLObjectElement13.html">HTMLObjectElement13</option>




<option value="HTMLObjectElement14.html">HTMLObjectElement14</option>




<option value="HTMLObjectElement15.html">HTMLObjectElement15</option>




<option value="HTMLObjectElement17.html">HTMLObjectElement17</option>




<option value="HTMLObjectElement18.html">HTMLObjectElement18</option>




<option value="HTMLObjectElement19.html">HTMLObjectElement19</option>




<option value="HTMLOListElement01.html">HTMLOListElement01</option>




<option value="HTMLOListElement02.html">HTMLOListElement02</option>




<option value="HTMLOListElement03.html">HTMLOListElement03</option>




<option value="HTMLOptGroupElement01.html">HTMLOptGroupElement01</option>




<option value="HTMLOptGroupElement02.html">HTMLOptGroupElement02</option>




<option value="HTMLOptionElement01.html">HTMLOptionElement01</option>




<option value="HTMLOptionElement02.html">HTMLOptionElement02</option>




<option value="HTMLOptionElement03.html">HTMLOptionElement03</option>




<option value="HTMLOptionElement04.html">HTMLOptionElement04</option>




<option value="HTMLOptionElement05.html">HTMLOptionElement05</option>




<option value="HTMLOptionElement06.html">HTMLOptionElement06</option>




<option value="HTMLOptionElement07.html">HTMLOptionElement07</option>




<option value="HTMLOptionElement08.html">HTMLOptionElement08</option>




<option value="HTMLOptionElement09.html">HTMLOptionElement09</option>




<option value="HTMLParagraphElement01.html">HTMLParagraphElement01</option>




<option value="HTMLParamElement01.html">HTMLParamElement01</option>




<option value="HTMLParamElement02.html">HTMLParamElement02</option>




<option value="HTMLParamElement03.html">HTMLParamElement03</option>




<option value="HTMLParamElement04.html">HTMLParamElement04</option>




<option value="HTMLPreElement01.html">HTMLPreElement01</option>




<option value="HTMLQuoteElement01.html">HTMLQuoteElement01</option>




<option value="HTMLQuoteElement02.html">HTMLQuoteElement02</option>




<option value="HTMLScriptElement01.html">HTMLScriptElement01</option>




<option value="HTMLScriptElement02.html">HTMLScriptElement02</option>




<option value="HTMLScriptElement03.html">HTMLScriptElement03</option>




<option value="HTMLScriptElement04.html">HTMLScriptElement04</option>




<option value="HTMLScriptElement05.html">HTMLScriptElement05</option>




<option value="HTMLScriptElement06.html">HTMLScriptElement06</option>




<option value="HTMLScriptElement07.html">HTMLScriptElement07</option>




<option value="HTMLSelectElement01.html">HTMLSelectElement01</option>




<option value="HTMLSelectElement02.html">HTMLSelectElement02</option>




<option value="HTMLSelectElement03.html">HTMLSelectElement03</option>




<option value="HTMLSelectElement04.html">HTMLSelectElement04</option>




<option value="HTMLSelectElement05.html">HTMLSelectElement05</option>




<option value="HTMLSelectElement06.html">HTMLSelectElement06</option>




<option value="HTMLSelectElement07.html">HTMLSelectElement07</option>




<option value="HTMLSelectElement08.html">HTMLSelectElement08</option>




<option value="HTMLSelectElement09.html">HTMLSelectElement09</option>




<option value="HTMLSelectElement10.html">HTMLSelectElement10</option>




<option value="HTMLSelectElement11.html">HTMLSelectElement11</option>




<option value="HTMLSelectElement12.html">HTMLSelectElement12</option>




<option value="HTMLSelectElement13.html">HTMLSelectElement13</option>




<option value="HTMLSelectElement14.html">HTMLSelectElement14</option>




<option value="HTMLSelectElement15.html">HTMLSelectElement15</option>




<option value="HTMLSelectElement16.html">HTMLSelectElement16</option>




<option value="HTMLSelectElement17.html">HTMLSelectElement17</option>




<option value="HTMLSelectElement18.html">HTMLSelectElement18</option>




<option value="HTMLSelectElement19.html">HTMLSelectElement19</option>




<option value="HTMLStyleElement01.html">HTMLStyleElement01</option>




<option value="HTMLStyleElement02.html">HTMLStyleElement02</option>




<option value="HTMLStyleElement03.html">HTMLStyleElement03</option>




<option value="HTMLTableCaptionElement01.html">HTMLTableCaptionElement01</option>




<option value="HTMLTableCellElement01.html">HTMLTableCellElement01</option>




<option value="HTMLTableCellElement02.html">HTMLTableCellElement02</option>




<option value="HTMLTableCellElement03.html">HTMLTableCellElement03</option>




<option value="HTMLTableCellElement04.html">HTMLTableCellElement04</option>




<option value="HTMLTableCellElement05.html">HTMLTableCellElement05</option>




<option value="HTMLTableCellElement06.html">HTMLTableCellElement06</option>




<option value="HTMLTableCellElement07.html">HTMLTableCellElement07</option>




<option value="HTMLTableCellElement08.html">HTMLTableCellElement08</option>




<option value="HTMLTableCellElement09.html">HTMLTableCellElement09</option>




<option value="HTMLTableCellElement10.html">HTMLTableCellElement10</option>




<option value="HTMLTableCellElement11.html">HTMLTableCellElement11</option>




<option value="HTMLTableCellElement12.html">HTMLTableCellElement12</option>




<option value="HTMLTableCellElement13.html">HTMLTableCellElement13</option>




<option value="HTMLTableCellElement14.html">HTMLTableCellElement14</option>




<option value="HTMLTableCellElement15.html">HTMLTableCellElement15</option>




<option value="HTMLTableCellElement16.html">HTMLTableCellElement16</option>




<option value="HTMLTableCellElement17.html">HTMLTableCellElement17</option>




<option value="HTMLTableCellElement18.html">HTMLTableCellElement18</option>




<option value="HTMLTableCellElement19.html">HTMLTableCellElement19</option>




<option value="HTMLTableCellElement20.html">HTMLTableCellElement20</option>




<option value="HTMLTableCellElement21.html">HTMLTableCellElement21</option>




<option value="HTMLTableCellElement22.html">HTMLTableCellElement22</option>




<option value="HTMLTableCellElement23.html">HTMLTableCellElement23</option>




<option value="HTMLTableCellElement24.html">HTMLTableCellElement24</option>




<option value="HTMLTableCellElement25.html">HTMLTableCellElement25</option>




<option value="HTMLTableCellElement26.html">HTMLTableCellElement26</option>




<option value="HTMLTableCellElement27.html">HTMLTableCellElement27</option>




<option value="HTMLTableCellElement28.html">HTMLTableCellElement28</option>




<option value="HTMLTableCellElement29.html">HTMLTableCellElement29</option>




<option value="HTMLTableCellElement30.html">HTMLTableCellElement30</option>




<option value="HTMLTableColElement01.html">HTMLTableColElement01</option>




<option value="HTMLTableColElement02.html">HTMLTableColElement02</option>




<option value="HTMLTableColElement03.html">HTMLTableColElement03</option>




<option value="HTMLTableColElement04.html">HTMLTableColElement04</option>




<option value="HTMLTableColElement05.html">HTMLTableColElement05</option>




<option value="HTMLTableColElement06.html">HTMLTableColElement06</option>




<option value="HTMLTableColElement07.html">HTMLTableColElement07</option>




<option value="HTMLTableColElement08.html">HTMLTableColElement08</option>




<option value="HTMLTableColElement09.html">HTMLTableColElement09</option>




<option value="HTMLTableColElement10.html">HTMLTableColElement10</option>




<option value="HTMLTableColElement11.html">HTMLTableColElement11</option>




<option value="HTMLTableColElement12.html">HTMLTableColElement12</option>




<option value="HTMLTableElement01.html">HTMLTableElement01</option>




<option value="HTMLTableElement02.html">HTMLTableElement02</option>




<option value="HTMLTableElement03.html">HTMLTableElement03</option>




<option value="HTMLTableElement04.html">HTMLTableElement04</option>




<option value="HTMLTableElement05.html">HTMLTableElement05</option>




<option value="HTMLTableElement06.html">HTMLTableElement06</option>




<option value="HTMLTableElement07.html">HTMLTableElement07</option>




<option value="HTMLTableElement08.html">HTMLTableElement08</option>




<option value="HTMLTableElement09.html">HTMLTableElement09</option>




<option value="HTMLTableElement10.html">HTMLTableElement10</option>




<option value="HTMLTableElement11.html">HTMLTableElement11</option>




<option value="HTMLTableElement12.html">HTMLTableElement12</option>




<option value="HTMLTableElement13.html">HTMLTableElement13</option>




<option value="HTMLTableElement14.html">HTMLTableElement14</option>




<option value="HTMLTableElement15.html">HTMLTableElement15</option>




<option value="HTMLTableElement16.html">HTMLTableElement16</option>




<option value="HTMLTableElement17.html">HTMLTableElement17</option>




<option value="HTMLTableElement18.html">HTMLTableElement18</option>




<option value="HTMLTableElement19.html">HTMLTableElement19</option>




<option value="HTMLTableElement20.html">HTMLTableElement20</option>




<option value="HTMLTableElement21.html">HTMLTableElement21</option>




<option value="HTMLTableElement22.html">HTMLTableElement22</option>




<option value="HTMLTableElement23.html">HTMLTableElement23</option>




<option value="HTMLTableElement24.html">HTMLTableElement24</option>




<option value="HTMLTableElement25.html">HTMLTableElement25</option>




<option value="HTMLTableElement26.html">HTMLTableElement26</option>




<option value="HTMLTableElement27.html">HTMLTableElement27</option>




<option value="HTMLTableElement28.html">HTMLTableElement28</option>




<option value="HTMLTableElement29.html">HTMLTableElement29</option>




<option value="HTMLTableElement30.html">HTMLTableElement30</option>




<option value="HTMLTableElement31.html">HTMLTableElement31</option>




<option value="HTMLTableElement32.html">HTMLTableElement32</option>




<option value="HTMLTableElement33.html">HTMLTableElement33</option>




<option value="HTMLTableRowElement01.html">HTMLTableRowElement01</option>




<option value="HTMLTableRowElement02.html">HTMLTableRowElement02</option>




<option value="HTMLTableRowElement03.html">HTMLTableRowElement03</option>




<option value="HTMLTableRowElement04.html">HTMLTableRowElement04</option>




<option value="HTMLTableRowElement05.html">HTMLTableRowElement05</option>




<option value="HTMLTableRowElement06.html">HTMLTableRowElement06</option>




<option value="HTMLTableRowElement07.html">HTMLTableRowElement07</option>




<option value="HTMLTableRowElement08.html">HTMLTableRowElement08</option>




<option value="HTMLTableRowElement09.html">HTMLTableRowElement09</option>




<option value="HTMLTableRowElement10.html">HTMLTableRowElement10</option>




<option value="HTMLTableRowElement11.html">HTMLTableRowElement11</option>




<option value="HTMLTableRowElement12.html">HTMLTableRowElement12</option>




<option value="HTMLTableRowElement13.html">HTMLTableRowElement13</option>




<option value="HTMLTableRowElement14.html">HTMLTableRowElement14</option>




<option value="HTMLTableSectionElement01.html">HTMLTableSectionElement01</option>




<option value="HTMLTableSectionElement02.html">HTMLTableSectionElement02</option>




<option value="HTMLTableSectionElement03.html">HTMLTableSectionElement03</option>




<option value="HTMLTableSectionElement04.html">HTMLTableSectionElement04</option>




<option value="HTMLTableSectionElement05.html">HTMLTableSectionElement05</option>




<option value="HTMLTableSectionElement06.html">HTMLTableSectionElement06</option>




<option value="HTMLTableSectionElement07.html">HTMLTableSectionElement07</option>




<option value="HTMLTableSectionElement08.html">HTMLTableSectionElement08</option>




<option value="HTMLTableSectionElement09.html">HTMLTableSectionElement09</option>




<option value="HTMLTableSectionElement10.html">HTMLTableSectionElement10</option>




<option value="HTMLTableSectionElement11.html">HTMLTableSectionElement11</option>




<option value="HTMLTableSectionElement12.html">HTMLTableSectionElement12</option>




<option value="HTMLTableSectionElement13.html">HTMLTableSectionElement13</option>




<option value="HTMLTableSectionElement14.html">HTMLTableSectionElement14</option>




<option value="HTMLTableSectionElement15.html">HTMLTableSectionElement15</option>




<option value="HTMLTableSectionElement16.html">HTMLTableSectionElement16</option>




<option value="HTMLTableSectionElement17.html">HTMLTableSectionElement17</option>




<option value="HTMLTableSectionElement18.html">HTMLTableSectionElement18</option>




<option value="HTMLTableSectionElement19.html">HTMLTableSectionElement19</option>




<option value="HTMLTableSectionElement20.html">HTMLTableSectionElement20</option>




<option value="HTMLTableSectionElement21.html">HTMLTableSectionElement21</option>




<option value="HTMLTableSectionElement22.html">HTMLTableSectionElement22</option>




<option value="HTMLTableSectionElement23.html">HTMLTableSectionElement23</option>




<option value="HTMLTableSectionElement24.html">HTMLTableSectionElement24</option>




<option value="HTMLTextAreaElement01.html">HTMLTextAreaElement01</option>




<option value="HTMLTextAreaElement02.html">HTMLTextAreaElement02</option>




<option value="HTMLTextAreaElement03.html">HTMLTextAreaElement03</option>




<option value="HTMLTextAreaElement04.html">HTMLTextAreaElement04</option>




<option value="HTMLTextAreaElement05.html">HTMLTextAreaElement05</option>




<option value="HTMLTextAreaElement06.html">HTMLTextAreaElement06</option>




<option value="HTMLTextAreaElement07.html">HTMLTextAreaElement07</option>




<option value="HTMLTextAreaElement08.html">HTMLTextAreaElement08</option>




<option value="HTMLTextAreaElement09.html">HTMLTextAreaElement09</option>




<option value="HTMLTextAreaElement10.html">HTMLTextAreaElement10</option>




<option value="HTMLTextAreaElement11.html">HTMLTextAreaElement11</option>




<option value="HTMLTextAreaElement12.html">HTMLTextAreaElement12</option>




<option value="HTMLTextAreaElement13.html">HTMLTextAreaElement13</option>




<option value="HTMLTextAreaElement14.html">HTMLTextAreaElement14</option>




<option value="HTMLTextAreaElement15.html">HTMLTextAreaElement15</option>




<option value="HTMLTitleElement01.html">HTMLTitleElement01</option>




<option value="HTMLUListElement01.html">HTMLUListElement01</option>




<option value="HTMLUListElement02.html">HTMLUListElement02</option>




<option value="object01.html">object01</option>




<option value="object02.html">object02</option>




<option value="object03.html">object03</option>




<option value="object04.html">object04</option>




<option value="object05.html">object05</option>




<option value="object06.html">object06</option>




<option value="object07.html">object07</option>




<option value="object09.html">object09</option>




<option value="object10.html">object10</option>




<option value="object11.html">object11</option>




<option value="object12.html">object12</option>




<option value="object14.html">object14</option>




<option value="object15.html">object15</option>




<option value="table01.html">table01</option>




<option value="table02.html">table02</option>




<option value="table03.html">table03</option>




<option value="table04.html">table04</option>




<option value="table06.html">table06</option>




<option value="table07.html">table07</option>




<option value="table08.html">table08</option>




<option value="table09.html">table09</option>




<option value="table10.html">table10</option>




<option value="table12.html">table12</option>




<option value="table15.html">table15</option>




<option value="table17.html">table17</option>




<option value="table18.html">table18</option>




<option value="table19.html">table19</option>




<option value="table20.html">table20</option>




<option value="table21.html">table21</option>




<option value="table22.html">table22</option>




<option value="table23.html">table23</option>




<option value="table24.html">table24</option>




<option value="table25.html">table25</option>




<option value="table26.html">table26</option>




<option value="table27.html">table27</option>




<option value="table28.html">table28</option>




<option value="table29.html">table29</option>




<option value="table30.html">table30</option>




<option value="table31.html">table31</option>




<option value="table32.html">table32</option>




<option value="table33.html">table33</option>




<option value="table34.html">table34</option>




<option value="table35.html">table35</option>




<option value="table36.html">table36</option>




<option value="table37.html">table37</option>




<option value="table38.html">table38</option>




<option value="table39.html">table39</option>




<option value="table40.html">table40</option>




<option value="table41.html">table41</option>




<option value="table42.html">table42</option>




<option value="table43.html">table43</option>




<option value="table44.html">table44</option>




<option value="table45.html">table45</option>




<option value="table46.html">table46</option>




<option value="table47.html">table47</option>




<option value="table48.html">table48</option>




<option value="table49.html">table49</option>




<option value="table50.html">table50</option>




<option value="table51.html">table51</option>




<option value="table52.html">table52</option>




<option value="table53.html">table53</option>




<option value="hasFeature02.html">hasFeature02</option>
<option value="hasFeature03.html">hasFeature03</option>
<option value="hasFeature04.html">hasFeature04</option>
<option value="hasFeature05.html">hasFeature05</option>
<option value="hasFeature06.html">hasFeature06</option>
<option value="HTMLAppletElement07.html">HTMLAppletElement07</option>
<option value="HTMLAppletElement09.html">HTMLAppletElement09</option>
<option value="HTMLBodyElement07.html">HTMLBodyElement07</option>
<option value="HTMLBodyElement08.html">HTMLBodyElement08</option>
<option value="HTMLBodyElement09.html">HTMLBodyElement09</option>
<option value="HTMLBodyElement10.html">HTMLBodyElement10</option>
<option value="HTMLBodyElement11.html">HTMLBodyElement11</option>
<option value="HTMLBodyElement12.html">HTMLBodyElement12</option>
<option value="HTMLDocument22.html">HTMLDocument22</option>
<option value="HTMLDocument23.html">HTMLDocument23</option>
<option value="HTMLDocument24.html">HTMLDocument24</option>
<option value="HTMLDocument25.html">HTMLDocument25</option>
<option value="HTMLDocument26.html">HTMLDocument26</option>
<option value="HTMLDocument27.html">HTMLDocument27</option>
<option value="HTMLFrameElement09.html">HTMLFrameElement09</option>
<option value="HTMLIFrameElement11.html">HTMLIFrameElement11</option>
<option value="HTMLImageElement05.html">HTMLImageElement05</option>
<option value="HTMLImageElement06.html">HTMLImageElement06</option>
<option value="HTMLImageElement11.html">HTMLImageElement11</option>
<option value="HTMLImageElement12.html">HTMLImageElement12</option>
<option value="HTMLInputElement13.html">HTMLInputElement13</option>
<option value="HTMLObjectElement11.html">HTMLObjectElement11</option>
<option value="HTMLObjectElement16.html">HTMLObjectElement16</option>
<option value="HTMLObjectElement20.html">HTMLObjectElement20</option>
<option value="HTMLOptionsCollection01.html">HTMLOptionsCollection01</option>
<option value="HTMLOptionsCollection02.html">HTMLOptionsCollection02</option>
<option value="HTMLOptionsCollection03.html">HTMLOptionsCollection03</option>
<option value="HTMLOptionsCollection04.html">HTMLOptionsCollection04</option>
<option value="HTMLOptionsCollection05.html">HTMLOptionsCollection05</option>
<option value="HTMLOptionsCollection06.html">HTMLOptionsCollection06</option>
<option value="HTMLOptionsCollection07.html">HTMLOptionsCollection07</option>
<option value="HTMLSelectElement20.html">HTMLSelectElement20</option>
<option value="HTMLTableElement34.html">HTMLTableElement34</option>
<option value="HTMLTableElement35.html">HTMLTableElement35</option>
<option value="HTMLTableElement36.html">HTMLTableElement36</option>
<option value="HTMLTableElement37.html">HTMLTableElement37</option>
<option value="HTMLTableElement38.html">HTMLTableElement38</option>
<option value="HTMLTableElement39.html">HTMLTableElement39</option>
<option value="HTMLTableElement40.html">HTMLTableElement40</option>
<option value="HTMLTableRowElement15.html">HTMLTableRowElement15</option>
<option value="HTMLTableRowElement16.html">HTMLTableRowElement16</option>
<option value="HTMLTableRowElement17.html">HTMLTableRowElement17</option>
<option value="HTMLTableRowElement18.html">HTMLTableRowElement18</option>
<option value="HTMLTableRowElement19.html">HTMLTableRowElement19</option>
<option value="HTMLTableRowElement20.html">HTMLTableRowElement20</option>
<option value="HTMLTableRowElement21.html">HTMLTableRowElement21</option>
<option value="HTMLTableSectionElement25.html">HTMLTableSectionElement25</option>
<option value="HTMLTableSectionElement26.html">HTMLTableSectionElement26</option>
<option value="HTMLTableSectionElement27.html">HTMLTableSectionElement27</option>
<option value="HTMLTableSectionElement28.html">HTMLTableSectionElement28</option>
<option value="HTMLTableSectionElement29.html">HTMLTableSectionElement29</option>
<option value="HTMLTableSectionElement30.html">HTMLTableSectionElement30</option>
<option value="HTMLTableSectionElement31.html">HTMLTableSectionElement31</option>
<option value="object08.html">object08</option>
<option value="object13.html">object13</option>
</select></td><td align="right"><input value="Load JSUnit" type="submit"></td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<table summary="Configuration" width="100%" border="1">
<tr>
<th>Implementation</th><th>Configuration</th><th>Content Type</th>
</tr>
<tr>
<td valign="top"><input onclick="onImplementationChange()" checked value="iframe" id="iframeImpl" name="implementation" type="radio"> iframe<br>
<input onclick="onImplementationChange()" value="dom3ls" id="dom3lsImpl" name="implementation" type="radio"> DOM 3 Load/Save<br> 
                            	Features: <input disabled name="dom3lsFeatures" type="text">
<br>
<input onclick="onImplementationChange()" value="mozillaXML" id="mozillaXMLImpl" name="implementation" type="radio"> Mozilla XML<br>
<input onclick="onImplementationChange()" value="msxml3" id="msxml3Impl" name="implementation" type="radio"> MSXML 3.0<br>
<input onclick="onImplementationChange()" value="msxml4" id="msxml4Impl" name="implementation" type="radio"> MSXML 4.0<br>
<input onclick="onImplementationChange()" value="svgplugin" id="svgpluginImpl" name="implementation" type="radio"> SVG Plugin<br>
</td><td valign="top"><input disabled checked type="checkbox" name="asynchronous" onclick="setAsynchronous(true)"> Asynchronous<br>
<input onclick="setImplementationAttribute('expandEntityReferences', true)" disabled value="true" name="expandEntityReferences" type="checkbox"> Expanding Entities<br>
<input onclick="setImplementationAttribute('ignoringElementContentWhitespace', true)" disabled value="true" name="ignoringElementContentWhitespace" type="checkbox"> Ignoring whitespace<br>
<input onclick="setImplementationAttribute('validating', true)" disabled value="true" name="validating" type="checkbox"> Validating<br>
<input onclick="setImplementationAttribute('coalescing', true)" disabled value="true" name="coalescing" type="checkbox"> Coalescing<br>
<input onclick="setImplementationAttribute('namespaceAware', true)" disabled value="true" name="namespaceAware" type="checkbox"> Namespace aware<br>
<input onclick="setImplementationAttribute('ignoringComments', true)" checked disabled value="true" name="ignoringComments" type="checkbox"> Ignoring comments<br>
<input checked value="true" name="skipIncompatibleTests" type="checkbox"> Skip incompatible tests<br>
<input value="true" checked name="autorun" type="checkbox"> Autorun<br>
<input onclick="updateTestSubmission()" name="submitCheckbox" type="checkbox"> Submit results<br>
                            Acceptor: http://<input size="35" disabled value="" name="submitresults" type="text">
<br>
							Result ID: <input disabled value="" name="resultid" type="text">
<br>
</td><td valign="top"><input onclick="setContentType('text/xml')" value="text/xml" id="contentTypeXML" name="contentType" type="radio">XML<br>
<input onclick="setContentType('text/html')" checked value="text/html" id="contentTypeHTML" name="contentType" type="radio">HTML<br>
<input onclick="setContentType('application/xhtml+xml')" value="application/xhtml+xml" id="contentTypeXHTML" name="contentType" type="radio">XHTML<br>
</td>
</tr>
</table>
</td>
</tr>
</table>
</form>
<p>
			Copyright (c) 2001-2004 World Wide Web Consortium,
			(Massachusetts Institute of Technology, Institut National de
			Recherche en Informatique et en Automatique, Keio University). All
			Rights Reserved. This program is distributed under the W3C's Software
			Intellectual Property License. This program is distributed in the
			hope that it will be useful, but WITHOUT ANY WARRANTY; without even
			the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
			PURPOSE.
			</p>
<p>See W3C License <a href="http://www.w3.org/Consortium/Legal/">http://www.w3.org/Consortium/Legal/</a> 
 for more details.</p>
</body>
</html>
