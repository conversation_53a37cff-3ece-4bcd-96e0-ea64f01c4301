<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/html/HTMLTextAreaElement08</title>
<link type="text/css" rel="stylesheet" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js" type="text/javascript"></script>
<script src="DOMTestCase.js" type="text/javascript"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['HTMLTextAreaElement08'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "textarea");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}



//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
    if (++docsLoaded == 1) {
        setUpPageStatus = 'complete';
        runJSUnitTests();
        SimpleTest.finish();
    }
}


/**
* 
    The readOnly attribute specifies this control is read-only.

    Retrieve the readOnly attribute from the 3rd TEXTAREA element and
    examine its value.

* <AUTHOR>
* <AUTHOR> Brady
* @see http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-html#ID-39131423
*/
function HTMLTextAreaElement08() {
   var success;
    if(checkInitialization(builder, "HTMLTextAreaElement08") != null) return;
    var nodeList;
      var testNode;
      var vreadonly;
      var doc;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "textarea");
      nodeList = doc.getElementsByTagName("textarea");
      assertSize("Asize",3,nodeList);
testNode = nodeList.item(2);
      vreadonly = testNode.readOnly;

      assertTrue("readOnlyLink",vreadonly);

}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/html/HTMLTextAreaElement08</h2>
<p>&lt;test name='HTMLTextAreaElement08' schemaLocation='http://www.w3.org/2001/DOM-Test-Suite/Level-1 dom1.xsd'&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;title&gt;HTMLTextAreaElement08&lt;/title&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;creator&gt;NIST&lt;/creator&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;description&gt;
    The readOnly attribute specifies this control is read-only.

    Retrieve the readOnly attribute from the 3rd TEXTAREA element and
    examine its value.
&lt;/description&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;contributor&gt;Mary Brady&lt;/contributor&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;date qualifier='created'&gt;2002-02-22&lt;/date&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;subject resource='<a href="http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-html#ID-39131423">http://www.w3.org/TR/1998/REC-DOM-Level-1-19981001/level-one-html#ID-39131423</a>'/&gt;
<br>&lt;/metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='nodeList' type='NodeList'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='testNode' type='Node'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='vreadonly' type='boolean'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='doc' type='Document'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;load var='doc' href='textarea' willBeModified='false'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;getElementsByTagName interface='Document' obj='doc' var='nodeList' tagname='"textarea"'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;assertSize collection='nodeList' size='3' <a id="Asize">id='Asize'</a>/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;item interface='NodeList' obj='nodeList' var='testNode' index='2'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;readOnly interface='HTMLTextAreaElement' obj='testNode' var='vreadonly'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;assertTrue actual='vreadonly' <a id="readOnlyLink">id='readOnlyLink'</a>/&gt;<br>&lt;/test&gt;<br>
</p>
<p>
			Copyright (c) 2001-2004 World Wide Web Consortium,
			(Massachusetts Institute of Technology, Institut National de
			Recherche en Informatique et en Automatique, Keio University). All
			Rights Reserved. This program is distributed under the W3C's Software
			Intellectual Property License. This program is distributed in the
			hope that it will be useful, but WITHOUT ANY WARRANTY; without even
			the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
			PURPOSE.
			</p>
<p>See W3C License <a href="http://www.w3.org/Consortium/Legal/">http://www.w3.org/Consortium/Legal/</a> 
 for more details.</p>
<iframe name="doc" src="files/textarea.html"></iframe>
<br>
</body>
</html>
