<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/html/hasFeature04</title>
<link type="text/css" rel="stylesheet" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js" type="text/javascript"></script>
<script src="DOMTestCase.js" type="text/javascript"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['hasFeature04'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
       if (docsLoaded == 0) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
   runJSUnitTests();
   SimpleTest.finish();
}



//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
    if (++docsLoaded == 0) {
        setUpPageStatus = 'complete';
    }
}


/**
* 
hasFeature("xhTmL", "2.0") should return true if hasFeature("XML", "2.0") returns true.

* <AUTHOR> Arnold
* @see http://www.w3.org/TR/DOM-Level-2-Core/core#ID-5CED94D7
*/
function hasFeature04() {
   var success;
    if(checkInitialization(builder, "hasFeature04") != null) return;
    var doc;
      var domImpl;
      var version = "2.0";
      var state;
      var hasXML;
      domImpl = getImplementation();
hasXML = domImpl.hasFeature("XML",version);
state = domImpl.hasFeature("xhTmL",version);
assertEquals("hasXHTML",hasXML,state);
       
}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/html/hasFeature04</h2>
<p>&lt;test name='hasFeature04' schemaLocation='http://www.w3.org/2001/DOM-Test-Suite/Level-2 dom2.xsd'&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;title&gt;hasFeature04&lt;/title&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;creator&gt;Curt Arnold&lt;/creator&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;description&gt;
hasFeature("xhTmL", "2.0") should return true if hasFeature("XML", "2.0") returns true.
&lt;/description&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;date qualifier='created'&gt;2004-03-18&lt;/date&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;subject resource='<a href="http://www.w3.org/TR/DOM-Level-2-Core/core#ID-5CED94D7">http://www.w3.org/TR/DOM-Level-2-Core/core#ID-5CED94D7</a>'/&gt;
<br>&lt;/metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='doc' type='Document'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='domImpl' type='DOMImplementation'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='version' type='DOMString' value='"2.0"'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='state' type='boolean'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='hasXML' type='boolean'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;implementation var='domImpl'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;hasFeature obj='domImpl' var='hasXML' feature='"XML"' version='version'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;hasFeature obj='domImpl' var='state' feature='"xhTmL"' version='version'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;assertEquals actual='state' expected='hasXML' ignoreCase='false' <a id="hasXHTML">id='hasXHTML'</a>/&gt;<br>&lt;/test&gt;<br>
</p>
<p>
			Copyright (c) 2001-2004 World Wide Web Consortium,
			(Massachusetts Institute of Technology, Institut National de
			Recherche en Informatique et en Automatique, Keio University). All
			Rights Reserved. This program is distributed under the W3C's Software
			Intellectual Property License. This program is distributed in the
			hope that it will be useful, but WITHOUT ANY WARRANTY; without even
			the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
			PURPOSE.
			</p>
<p>See W3C License <a href="http://www.w3.org/Consortium/Legal/">http://www.w3.org/Consortium/Legal/</a> 
 for more details.</p>
</body>
</html>
