<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html
   PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
   "xhtml1-transitional.dtd">
<html xmlns='http://www.w3.org/1999/xhtml'>
<head>
<title>NIST DOM HTML Test - TABLECELL</title>
</head>
<body onload="parent.loadComplete()">
<table summary="Table 1">
<tr>
<th id="header-1">Employee Id</th>
<th id="header-2" abbr="hd1" axis="center" align="center" bgcolor="#00FFFF" char=":" charoff="1" colspan="1" height="50" nowrap="nowrap" rowspan="1" scope="col" headers="header-1" valign="middle" width="170">Employee Name</th>
<th>Position</th>
<th>Salary</th>
</tr>
<tr>
<td id="header-3">EMP0001</td>
<td id="header-4" abbr="hd2" axis="center" align="center" bgcolor="#FF0000" char=":" charoff="1" colspan="1" height="50" nowrap="nowrap" rowspan="1" scope="col" headers="header-3" valign="middle" width="175">Margaret Martin</td>
<td>Accountant</td>
<td>56,000</td>
</tr>
</table>
</body>
</html>

