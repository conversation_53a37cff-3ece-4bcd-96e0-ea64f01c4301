<!--
   Extensible HTML version 1.0 Frameset DTD

   This is the same as HTML 4 Frameset except for
   changes due to the differences between XML and SGML.

   Namespace = http://www.w3.org/1999/xhtml

   For further information, see: http://www.w3.org/TR/xhtml1

   Copyright (c) 1998-2002 W3C (MIT, INRIA, Keio),
   All Rights Reserved. 

   This DTD module is identified by the PUBLIC and SYSTEM identifiers:

   PUBLIC "-//W3C//DTD XHTML 1.0 Frameset//EN"
   SYSTEM "http://www.w3.org/TR/xhtml1/DTD/xhtml1-frameset.dtd"

   $Revision: 1.1 $
   $Date: 2008/03/29 19:06:30 $

-->

<!--================ Character mnemonic entities =========================-->

<!ENTITY % HTMLlat1 PUBLIC
   "-//W3C//ENTITIES Latin 1 for XHTML//EN"
   "xhtml-lat1.ent">
%HTMLlat1;

<!ENTITY % HTMLsymbol PUBLIC
   "-//W3C//ENTITIES Symbols for XHTML//EN"
   "xhtml-symbol.ent">
%HTMLsymbol;

<!ENTITY % HTMLspecial PUBLIC
   "-//W3C//ENTITIES Special for XHTML//EN"
   "xhtml-special.ent">
%HTMLspecial;

<!--================== Imported Names ====================================-->

<!ENTITY % ContentType "CDATA">
    <!-- media type, as per [RFC2045] -->

<!ENTITY % ContentTypes "CDATA">
    <!-- comma-separated list of media types, as per [RFC2045] -->

<!ENTITY % Charset "CDATA">
    <!-- a character encoding, as per [RFC2045] -->

<!ENTITY % Charsets "CDATA">
    <!-- a space separated list of character encodings, as per [RFC2045] -->

<!ENTITY % LanguageCode "NMTOKEN">
    <!-- a language code, as per [RFC3066] -->

<!ENTITY % Character "CDATA">
    <!-- a single character, as per section 2.2 of [XML] -->

<!ENTITY % Number "CDATA">
    <!-- one or more digits -->

<!ENTITY % LinkTypes "CDATA">
    <!-- space-separated list of link types -->

<!ENTITY % MediaDesc "CDATA">
    <!-- single or comma-separated list of media descriptors -->

<!ENTITY % URI "CDATA">
    <!-- a Uniform Resource Identifier, see [RFC2396] -->

<!ENTITY % UriList "CDATA">
    <!-- a space separated list of Uniform Resource Identifiers -->

<!ENTITY % Datetime "CDATA">
    <!-- date and time information. ISO date format -->

<!ENTITY % Script "CDATA">
    <!-- script expression -->

<!ENTITY % StyleSheet "CDATA">
    <!-- style sheet data -->

<!ENTITY % Text "CDATA">
    <!-- used for titles etc. -->

<!ENTITY % FrameTarget "NMTOKEN">
    <!-- render in this frame -->

<!ENTITY % Length "CDATA">
    <!-- nn for pixels or nn% for percentage length -->

<!ENTITY % MultiLength "CDATA">
    <!-- pixel, percentage, or relative -->

<!ENTITY % MultiLengths "CDATA">
    <!-- comma-separated list of MultiLength -->

<!ENTITY % Pixels "CDATA">
    <!-- integer representing length in pixels -->

<!-- these are used for image maps -->

<!ENTITY % Shape "(rect|circle|poly|default)">

<!ENTITY % Coords "CDATA">
    <!-- comma separated list of lengths -->

<!-- used for object, applet, img, input and iframe -->
<!ENTITY % ImgAlign "(top|middle|bottom|left|right)">

<!-- a color using sRGB: #RRGGBB as Hex values -->
<!ENTITY % Color "CDATA">

<!-- There are also 16 widely known color names with their sRGB values:

    Black  = #000000    Green  = #008000
    Silver = #C0C0C0    Lime   = #00FF00
    Gray   = #808080    Olive  = #808000
    White  = #FFFFFF    Yellow = #FFFF00
    Maroon = #800000    Navy   = #000080
    Red    = #FF0000    Blue   = #0000FF
    Purple = #800080    Teal   = #008080
    Fuchsia= #FF00FF    Aqua   = #00FFFF
-->

<!--=================== Generic Attributes ===============================-->

<!-- core attributes common to most elements
  id       document-wide unique id
  class    space separated list of classes
  style    associated style info
  title    advisory title/amplification
-->
<!ENTITY % coreattrs
 "id          ID             #IMPLIED
  class       CDATA          #IMPLIED
  style       %StyleSheet;   #IMPLIED
  title       %Text;         #IMPLIED"
  >

<!-- internationalization attributes
  lang        language code (backwards compatible)
  xml:lang    language code (as per XML 1.0 spec)
  dir         direction for weak/neutral text
-->
<!ENTITY % i18n
 "lang        %LanguageCode; #IMPLIED
  xml:lang    %LanguageCode; #IMPLIED
  dir         (ltr|rtl)      #IMPLIED"
  >

<!-- attributes for common UI events
  onclick     a pointer button was clicked
  ondblclick  a pointer button was double clicked
  onmousedown a pointer button was pressed down
  onmouseup   a pointer button was released
  onmousemove a pointer was moved onto the element
  onmouseout  a pointer was moved away from the element
  onkeypress  a key was pressed and released
  onkeydown   a key was pressed down
  onkeyup     a key was released
-->
<!ENTITY % events
 "onclick     %Script;       #IMPLIED
  ondblclick  %Script;       #IMPLIED
  onmousedown %Script;       #IMPLIED
  onmouseup   %Script;       #IMPLIED
  onmouseover %Script;       #IMPLIED
  onmousemove %Script;       #IMPLIED
  onmouseout  %Script;       #IMPLIED
  onkeypress  %Script;       #IMPLIED
  onkeydown   %Script;       #IMPLIED
  onkeyup     %Script;       #IMPLIED"
  >

<!-- attributes for elements that can get the focus
  accesskey   accessibility key character
  tabindex    position in tabbing order
  onfocus     the element got the focus
  onblur      the element lost the focus
-->
<!ENTITY % focus
 "accesskey   %Character;    #IMPLIED
  tabindex    %Number;       #IMPLIED
  onfocus     %Script;       #IMPLIED
  onblur      %Script;       #IMPLIED"
  >

<!ENTITY % attrs "%coreattrs; %i18n; %events;">

<!-- text alignment for p, div, h1-h6. The default is
     align="left" for ltr headings, "right" for rtl -->

<!ENTITY % TextAlign "align (left|center|right|justify) #IMPLIED">

<!--=================== Text Elements ====================================-->

<!ENTITY % special.extra
   "object | applet | img | map | iframe">
	
<!ENTITY % special.basic
	"br | span | bdo">

<!ENTITY % special
   "%special.basic; | %special.extra;">

<!ENTITY % fontstyle.extra "big | small | font | basefont">

<!ENTITY % fontstyle.basic "tt | i | b | u
                      | s | strike ">

<!ENTITY % fontstyle "%fontstyle.basic; | %fontstyle.extra;">

<!ENTITY % phrase.extra "sub | sup">
<!ENTITY % phrase.basic "em | strong | dfn | code | q |
                   samp | kbd | var | cite | abbr | acronym">

<!ENTITY % phrase "%phrase.basic; | %phrase.extra;">

<!ENTITY % inline.forms "input | select | textarea | label | button">

<!-- these can occur at block or inline level -->
<!ENTITY % misc.inline "ins | del | script">

<!-- these can only occur at block level -->
<!ENTITY % misc "noscript | %misc.inline;">


<!ENTITY % inline "a | %special; | %fontstyle; | %phrase; | %inline.forms;">

<!-- %Inline; covers inline or "text-level" elements -->
<!ENTITY % Inline "(#PCDATA | %inline; | %misc.inline;)*">

<!--================== Block level elements ==============================-->

<!ENTITY % heading "h1|h2|h3|h4|h5|h6">
<!ENTITY % lists "ul | ol | dl | menu | dir">
<!ENTITY % blocktext "pre | hr | blockquote | address | center">

<!ENTITY % block
    "p | %heading; | div | %lists; | %blocktext; | isindex | fieldset | table">

<!-- %Flow; mixes block and inline and is used for list items etc. -->
<!ENTITY % Flow "(#PCDATA | %block; | form | %inline; | %misc;)*">

<!--================== Content models for exclusions =====================-->

<!-- a elements use %Inline; excluding a -->

<!ENTITY % a.content
   "(#PCDATA | %special; | %fontstyle; | %phrase; | %inline.forms; | %misc.inline;)*">

<!-- pre uses %Inline excluding img, object, applet, big, small,
     sub, sup, font, or basefont -->

<!ENTITY % pre.content
   "(#PCDATA | a | %special.basic; | %fontstyle.basic; | %phrase.basic; |
	   %inline.forms; | %misc.inline;)*">


<!-- form uses %Flow; excluding form -->

<!ENTITY % form.content "(#PCDATA | %block; | %inline; | %misc;)*">

<!-- button uses %Flow; but excludes a, form, form controls, iframe -->

<!ENTITY % button.content
   "(#PCDATA | p | %heading; | div | %lists; | %blocktext; |
      table | br | span | bdo | object | applet | img | map |
      %fontstyle; | %phrase; | %misc;)*">

<!--================ Document Structure ==================================-->

<!-- the namespace URI designates the document profile -->

<!ELEMENT html (head, frameset)>
<!ATTLIST html
  %i18n;
  id          ID             #IMPLIED
  xmlns       %URI;          #FIXED 'http://www.w3.org/1999/xhtml'
  >

<!--================ Document Head =======================================-->

<!ENTITY % head.misc "(script|style|meta|link|object|isindex)*">

<!-- content model is %head.misc; combined with a single
     title and an optional base element in any order -->

<!ELEMENT head (%head.misc;,
     ((title, %head.misc;, (base, %head.misc;)?) |
      (base, %head.misc;, (title, %head.misc;))))>

<!ATTLIST head
  %i18n;
  id          ID             #IMPLIED
  profile     %URI;          #IMPLIED
  >

<!-- The title element is not considered part of the flow of text.
       It should be displayed, for example as the page header or
       window title. Exactly one title is required per document.
    -->
<!ELEMENT title (#PCDATA)>
<!ATTLIST title 
  %i18n;
  id          ID             #IMPLIED
  >

<!-- document base URI -->

<!ELEMENT base EMPTY>
<!ATTLIST base
  id          ID             #IMPLIED
  href        %URI;          #IMPLIED
  target      %FrameTarget;  #IMPLIED
  >

<!-- generic metainformation -->
<!ELEMENT meta EMPTY>
<!ATTLIST meta
  %i18n;
  id          ID             #IMPLIED
  http-equiv  CDATA          #IMPLIED
  name        CDATA          #IMPLIED
  content     CDATA          #REQUIRED
  scheme      CDATA          #IMPLIED
  >

<!--
  Relationship values can be used in principle:

   a) for document specific toolbars/menus when used
      with the link element in document head e.g.
        start, contents, previous, next, index, end, help
   b) to link to a separate style sheet (rel="stylesheet")
   c) to make a link to a script (rel="script")
   d) by stylesheets to control how collections of
      html nodes are rendered into printed documents
   e) to make a link to a printable version of this document
      e.g. a PostScript or PDF version (rel="alternate" media="print")
-->

<!ELEMENT link EMPTY>
<!ATTLIST link
  %attrs;
  charset     %Charset;      #IMPLIED
  href        %URI;          #IMPLIED
  hreflang    %LanguageCode; #IMPLIED
  type        %ContentType;  #IMPLIED
  rel         %LinkTypes;    #IMPLIED
  rev         %LinkTypes;    #IMPLIED
  media       %MediaDesc;    #IMPLIED
  target      %FrameTarget;  #IMPLIED
  >

<!-- style info, which may include CDATA sections -->
<!ELEMENT style (#PCDATA)>
<!ATTLIST style
  %i18n;
  id          ID             #IMPLIED
  type        %ContentType;  #REQUIRED
  media       %MediaDesc;    #IMPLIED
  title       %Text;         #IMPLIED
  xml:space   (preserve)     #FIXED 'preserve'
  >

<!-- script statements, which may include CDATA sections -->
<!ELEMENT script (#PCDATA)>
<!ATTLIST script
  id          ID             #IMPLIED
  charset     %Charset;      #IMPLIED
  type        %ContentType;  #REQUIRED
  language    CDATA          #IMPLIED
  src         %URI;          #IMPLIED
  defer       (defer)        #IMPLIED
  xml:space   (preserve)     #FIXED 'preserve'
  >

<!-- alternate content container for non script-based rendering -->

<!ELEMENT noscript %Flow;>
<!ATTLIST noscript
  %attrs;
  >

<!--======================= Frames =======================================-->

<!-- only one noframes element permitted per document -->

<!ELEMENT frameset (frameset|frame|noframes)*>
<!ATTLIST frameset
  %coreattrs;
  rows        %MultiLengths; #IMPLIED
  cols        %MultiLengths; #IMPLIED
  onload      %Script;       #IMPLIED
  onunload    %Script;       #IMPLIED
  >

<!-- reserved frame names start with "_" otherwise starts with letter -->

<!-- tiled window within frameset -->

<!ELEMENT frame EMPTY>
<!ATTLIST frame
  %coreattrs;
  longdesc    %URI;          #IMPLIED
  name        NMTOKEN        #IMPLIED
  src         %URI;          #IMPLIED
  frameborder (1|0)          "1"
  marginwidth %Pixels;       #IMPLIED
  marginheight %Pixels;      #IMPLIED
  noresize    (noresize)     #IMPLIED
  scrolling   (yes|no|auto)  "auto"
  >

<!-- inline subwindow -->

<!ELEMENT iframe %Flow;>
<!ATTLIST iframe
  %coreattrs;
  longdesc    %URI;          #IMPLIED
  name        NMTOKEN        #IMPLIED
  src         %URI;          #IMPLIED
  frameborder (1|0)          "1"
  marginwidth %Pixels;       #IMPLIED
  marginheight %Pixels;      #IMPLIED
  scrolling   (yes|no|auto)  "auto"
  align       %ImgAlign;     #IMPLIED
  height      %Length;       #IMPLIED
  width       %Length;       #IMPLIED
  >

<!-- alternate content container for non frame-based rendering -->

<!ELEMENT noframes (body)>
<!ATTLIST noframes
  %attrs;
  >

<!--=================== Document Body ====================================-->

<!ELEMENT body %Flow;>
<!ATTLIST body
  %attrs;
  onload      %Script;       #IMPLIED
  onunload    %Script;       #IMPLIED
  background  %URI;          #IMPLIED
  bgcolor     %Color;        #IMPLIED
  text        %Color;        #IMPLIED
  link        %Color;        #IMPLIED
  vlink       %Color;        #IMPLIED
  alink       %Color;        #IMPLIED
  >

<!ELEMENT div %Flow;>  <!-- generic language/style container -->
<!ATTLIST div
  %attrs;
  %TextAlign;
  >

<!--=================== Paragraphs =======================================-->

<!ELEMENT p %Inline;>
<!ATTLIST p
  %attrs;
  %TextAlign;
  >

<!--=================== Headings =========================================-->

<!--
  There are six levels of headings from h1 (the most important)
  to h6 (the least important).
-->

<!ELEMENT h1  %Inline;>
<!ATTLIST h1
  %attrs;
  %TextAlign;
  >

<!ELEMENT h2 %Inline;>
<!ATTLIST h2
  %attrs;
  %TextAlign;
  >

<!ELEMENT h3 %Inline;>
<!ATTLIST h3
  %attrs;
  %TextAlign;
  >

<!ELEMENT h4 %Inline;>
<!ATTLIST h4
  %attrs;
  %TextAlign;
  >

<!ELEMENT h5 %Inline;>
<!ATTLIST h5
  %attrs;
  %TextAlign;
  >

<!ELEMENT h6 %Inline;>
<!ATTLIST h6
  %attrs;
  %TextAlign;
  >

<!--=================== Lists ============================================-->

<!-- Unordered list bullet styles -->

<!ENTITY % ULStyle "(disc|square|circle)">

<!-- Unordered list -->

<!ELEMENT ul (li)+>
<!ATTLIST ul
  %attrs;
  type        %ULStyle;     #IMPLIED
  compact     (compact)     #IMPLIED
  >

<!-- Ordered list numbering style

    1   arabic numbers      1, 2, 3, ...
    a   lower alpha         a, b, c, ...
    A   upper alpha         A, B, C, ...
    i   lower roman         i, ii, iii, ...
    I   upper roman         I, II, III, ...

    The style is applied to the sequence number which by default
    is reset to 1 for the first list item in an ordered list.
-->
<!ENTITY % OLStyle "CDATA">

<!-- Ordered (numbered) list -->

<!ELEMENT ol (li)+>
<!ATTLIST ol
  %attrs;
  type        %OLStyle;      #IMPLIED
  compact     (compact)      #IMPLIED
  start       %Number;       #IMPLIED
  >

<!-- single column list (DEPRECATED) --> 
<!ELEMENT menu (li)+>
<!ATTLIST menu
  %attrs;
  compact     (compact)     #IMPLIED
  >

<!-- multiple column list (DEPRECATED) --> 
<!ELEMENT dir (li)+>
<!ATTLIST dir
  %attrs;
  compact     (compact)     #IMPLIED
  >

<!-- LIStyle is constrained to: "(%ULStyle;|%OLStyle;)" -->
<!ENTITY % LIStyle "CDATA">

<!-- list item -->

<!ELEMENT li %Flow;>
<!ATTLIST li
  %attrs;
  type        %LIStyle;      #IMPLIED
  value       %Number;       #IMPLIED
  >

<!-- definition lists - dt for term, dd for its definition -->

<!ELEMENT dl (dt|dd)+>
<!ATTLIST dl
  %attrs;
  compact     (compact)      #IMPLIED
  >

<!ELEMENT dt %Inline;>
<!ATTLIST dt
  %attrs;
  >

<!ELEMENT dd %Flow;>
<!ATTLIST dd
  %attrs;
  >

<!--=================== Address ==========================================-->

<!-- information on author -->

<!ELEMENT address (#PCDATA | %inline; | %misc.inline; | p)*>
<!ATTLIST address
  %attrs;
  >

<!--=================== Horizontal Rule ==================================-->

<!ELEMENT hr EMPTY>
<!ATTLIST hr
  %attrs;
  align       (left|center|right) #IMPLIED
  noshade     (noshade)      #IMPLIED
  size        %Pixels;       #IMPLIED
  width       %Length;       #IMPLIED
  >

<!--=================== Preformatted Text ================================-->

<!-- content is %Inline; excluding 
        "img|object|applet|big|small|sub|sup|font|basefont" -->

<!ELEMENT pre %pre.content;>
<!ATTLIST pre
  %attrs;
  width       %Number;      #IMPLIED
  xml:space   (preserve)    #FIXED 'preserve'
  >

<!--=================== Block-like Quotes ================================-->

<!ELEMENT blockquote %Flow;>
<!ATTLIST blockquote
  %attrs;
  cite        %URI;          #IMPLIED
  >

<!--=================== Text alignment ===================================-->

<!-- center content -->
<!ELEMENT center %Flow;>
<!ATTLIST center
  %attrs;
  >

<!--=================== Inserted/Deleted Text ============================-->


<!--
  ins/del are allowed in block and inline content, but its
  inappropriate to include block content within an ins element
  occurring in inline content.
-->
<!ELEMENT ins %Flow;>
<!ATTLIST ins
  %attrs;
  cite        %URI;          #IMPLIED
  datetime    %Datetime;     #IMPLIED
  >

<!ELEMENT del %Flow;>
<!ATTLIST del
  %attrs;
  cite        %URI;          #IMPLIED
  datetime    %Datetime;     #IMPLIED
  >

<!--================== The Anchor Element ================================-->

<!-- content is %Inline; except that anchors shouldn't be nested -->

<!ELEMENT a %a.content;>
<!ATTLIST a
  %attrs;
  %focus;
  charset     %Charset;      #IMPLIED
  type        %ContentType;  #IMPLIED
  name        NMTOKEN        #IMPLIED
  href        %URI;          #IMPLIED
  hreflang    %LanguageCode; #IMPLIED
  rel         %LinkTypes;    #IMPLIED
  rev         %LinkTypes;    #IMPLIED
  shape       %Shape;        "rect"
  coords      %Coords;       #IMPLIED
  target      %FrameTarget;  #IMPLIED
  >

<!--===================== Inline Elements ================================-->

<!ELEMENT span %Inline;> <!-- generic language/style container -->
<!ATTLIST span
  %attrs;
  >

<!ELEMENT bdo %Inline;>  <!-- I18N BiDi over-ride -->
<!ATTLIST bdo
  %coreattrs;
  %events;
  lang        %LanguageCode; #IMPLIED
  xml:lang    %LanguageCode; #IMPLIED
  dir         (ltr|rtl)      #REQUIRED
  >

<!ELEMENT br EMPTY>   <!-- forced line break -->
<!ATTLIST br
  %coreattrs;
  clear       (left|all|right|none) "none"
  >

<!ELEMENT em %Inline;>   <!-- emphasis -->
<!ATTLIST em %attrs;>

<!ELEMENT strong %Inline;>   <!-- strong emphasis -->
<!ATTLIST strong %attrs;>

<!ELEMENT dfn %Inline;>   <!-- definitional -->
<!ATTLIST dfn %attrs;>

<!ELEMENT code %Inline;>   <!-- program code -->
<!ATTLIST code %attrs;>

<!ELEMENT samp %Inline;>   <!-- sample -->
<!ATTLIST samp %attrs;>

<!ELEMENT kbd %Inline;>  <!-- something user would type -->
<!ATTLIST kbd %attrs;>

<!ELEMENT var %Inline;>   <!-- variable -->
<!ATTLIST var %attrs;>

<!ELEMENT cite %Inline;>   <!-- citation -->
<!ATTLIST cite %attrs;>

<!ELEMENT abbr %Inline;>   <!-- abbreviation -->
<!ATTLIST abbr %attrs;>

<!ELEMENT acronym %Inline;>   <!-- acronym -->
<!ATTLIST acronym %attrs;>

<!ELEMENT q %Inline;>   <!-- inlined quote -->
<!ATTLIST q
  %attrs;
  cite        %URI;          #IMPLIED
  >

<!ELEMENT sub %Inline;> <!-- subscript -->
<!ATTLIST sub %attrs;>

<!ELEMENT sup %Inline;> <!-- superscript -->
<!ATTLIST sup %attrs;>

<!ELEMENT tt %Inline;>   <!-- fixed pitch font -->
<!ATTLIST tt %attrs;>

<!ELEMENT i %Inline;>   <!-- italic font -->
<!ATTLIST i %attrs;>

<!ELEMENT b %Inline;>   <!-- bold font -->
<!ATTLIST b %attrs;>

<!ELEMENT big %Inline;>   <!-- bigger font -->
<!ATTLIST big %attrs;>

<!ELEMENT small %Inline;>   <!-- smaller font -->
<!ATTLIST small %attrs;>

<!ELEMENT u %Inline;>   <!-- underline -->
<!ATTLIST u %attrs;>

<!ELEMENT s %Inline;>   <!-- strike-through -->
<!ATTLIST s %attrs;>

<!ELEMENT strike %Inline;>   <!-- strike-through -->
<!ATTLIST strike %attrs;>

<!ELEMENT basefont EMPTY>  <!-- base font size -->
<!ATTLIST basefont
  id          ID             #IMPLIED
  size        CDATA          #REQUIRED
  color       %Color;        #IMPLIED
  face        CDATA          #IMPLIED
  >

<!ELEMENT font %Inline;> <!-- local change to font -->
<!ATTLIST font
  %coreattrs;
  %i18n;
  size        CDATA          #IMPLIED
  color       %Color;        #IMPLIED
  face        CDATA          #IMPLIED
  >

<!--==================== Object ======================================-->
<!--
  object is used to embed objects as part of HTML pages.
  param elements should precede other content. Parameters
  can also be expressed as attribute/value pairs on the
  object element itself when brevity is desired.
-->

<!ELEMENT object (#PCDATA | param | %block; | form |%inline; | %misc;)*>
<!ATTLIST object
  %attrs;
  declare     (declare)      #IMPLIED
  classid     %URI;          #IMPLIED
  codebase    %URI;          #IMPLIED
  data        %URI;          #IMPLIED
  type        %ContentType;  #IMPLIED
  codetype    %ContentType;  #IMPLIED
  archive     %UriList;      #IMPLIED
  standby     %Text;         #IMPLIED
  height      %Length;       #IMPLIED
  width       %Length;       #IMPLIED
  usemap      %URI;          #IMPLIED
  name        NMTOKEN        #IMPLIED
  tabindex    %Number;       #IMPLIED
  align       %ImgAlign;     #IMPLIED
  border      %Pixels;       #IMPLIED
  hspace      %Pixels;       #IMPLIED
  vspace      %Pixels;       #IMPLIED
  >

<!--
  param is used to supply a named property value.
  In XML it would seem natural to follow RDF and support an
  abbreviated syntax where the param elements are replaced
  by attribute value pairs on the object start tag.
-->
<!ELEMENT param EMPTY>
<!ATTLIST param
  id          ID             #IMPLIED
  name        CDATA          #REQUIRED
  value       CDATA          #IMPLIED
  valuetype   (data|ref|object) "data"
  type        %ContentType;  #IMPLIED
  >

<!--=================== Java applet ==================================-->
<!--
  One of code or object attributes must be present.
  Place param elements before other content.
-->
<!ELEMENT applet (#PCDATA | param | %block; | form | %inline; | %misc;)*>
<!ATTLIST applet
  %coreattrs;
  codebase    %URI;          #IMPLIED
  archive     CDATA          #IMPLIED
  code        CDATA          #IMPLIED
  object      CDATA          #IMPLIED
  alt         %Text;         #IMPLIED
  name        NMTOKEN        #IMPLIED
  width       %Length;       #REQUIRED
  height      %Length;       #REQUIRED
  align       %ImgAlign;     #IMPLIED
  hspace      %Pixels;       #IMPLIED
  vspace      %Pixels;       #IMPLIED
  >

<!--=================== Images ===========================================-->

<!--
   To avoid accessibility problems for people who aren't
   able to see the image, you should provide a text
   description using the alt and longdesc attributes.
   In addition, avoid the use of server-side image maps.
-->

<!ELEMENT img EMPTY>
<!ATTLIST img
  %attrs;
  src         %URI;          #REQUIRED
  alt         %Text;         #REQUIRED
  name        NMTOKEN        #IMPLIED
  longdesc    %URI;          #IMPLIED
  height      %Length;       #IMPLIED
  width       %Length;       #IMPLIED
  usemap      %URI;          #IMPLIED
  ismap       (ismap)        #IMPLIED
  align       %ImgAlign;     #IMPLIED
  border      %Pixels;       #IMPLIED
  hspace      %Pixels;       #IMPLIED
  vspace      %Pixels;       #IMPLIED
  >

<!-- usemap points to a map element which may be in this document
  or an external document, although the latter is not widely supported -->

<!--================== Client-side image maps ============================-->

<!-- These can be placed in the same document or grouped in a
     separate document although this isn't yet widely supported -->

<!ELEMENT map ((%block; | form | %misc;)+ | area+)>
<!ATTLIST map
  %i18n;
  %events;
  id          ID             #REQUIRED
  class       CDATA          #IMPLIED
  style       %StyleSheet;   #IMPLIED
  title       %Text;         #IMPLIED
  name        NMTOKEN        #IMPLIED
  >

<!ELEMENT area EMPTY>
<!ATTLIST area
  %attrs;
  %focus;
  shape       %Shape;        "rect"
  coords      %Coords;       #IMPLIED
  href        %URI;          #IMPLIED
  nohref      (nohref)       #IMPLIED
  alt         %Text;         #REQUIRED
  target      %FrameTarget;  #IMPLIED
  >

<!--================ Forms ===============================================-->

<!ELEMENT form %form.content;>   <!-- forms shouldn't be nested -->

<!ATTLIST form
  %attrs;
  action      %URI;          #REQUIRED
  method      (get|post)     "get"
  name        NMTOKEN        #IMPLIED
  enctype     %ContentType;  "application/x-www-form-urlencoded"
  onsubmit    %Script;       #IMPLIED
  onreset     %Script;       #IMPLIED
  accept      %ContentTypes; #IMPLIED
  accept-charset %Charsets;  #IMPLIED
  target      %FrameTarget;  #IMPLIED
  >

<!--
  Each label must not contain more than ONE field
  Label elements shouldn't be nested.
-->
<!ELEMENT label %Inline;>
<!ATTLIST label
  %attrs;
  for         IDREF          #IMPLIED
  accesskey   %Character;    #IMPLIED
  onfocus     %Script;       #IMPLIED
  onblur      %Script;       #IMPLIED
  >

<!ENTITY % InputType
  "(text | password | checkbox |
    radio | submit | reset |
    file | hidden | image | button)"
   >

<!-- the name attribute is required for all but submit & reset -->

<!ELEMENT input EMPTY>     <!-- form control -->
<!ATTLIST input
  %attrs;
  %focus;
  type        %InputType;    "text"
  name        CDATA          #IMPLIED
  value       CDATA          #IMPLIED
  checked     (checked)      #IMPLIED
  disabled    (disabled)     #IMPLIED
  readonly    (readonly)     #IMPLIED
  size        CDATA          #IMPLIED
  maxlength   %Number;       #IMPLIED
  src         %URI;          #IMPLIED
  alt         CDATA          #IMPLIED
  usemap      %URI;          #IMPLIED
  onselect    %Script;       #IMPLIED
  onchange    %Script;       #IMPLIED
  accept      %ContentTypes; #IMPLIED
  align       %ImgAlign;     #IMPLIED
  >

<!ELEMENT select (optgroup|option)+>  <!-- option selector -->
<!ATTLIST select
  %attrs;
  name        CDATA          #IMPLIED
  size        %Number;       #IMPLIED
  multiple    (multiple)     #IMPLIED
  disabled    (disabled)     #IMPLIED
  tabindex    %Number;       #IMPLIED
  onfocus     %Script;       #IMPLIED
  onblur      %Script;       #IMPLIED
  onchange    %Script;       #IMPLIED
  >

<!ELEMENT optgroup (option)+>   <!-- option group -->
<!ATTLIST optgroup
  %attrs;
  disabled    (disabled)     #IMPLIED
  label       %Text;         #REQUIRED
  >

<!ELEMENT option (#PCDATA)>     <!-- selectable choice -->
<!ATTLIST option
  %attrs;
  selected    (selected)     #IMPLIED
  disabled    (disabled)     #IMPLIED
  label       %Text;         #IMPLIED
  value       CDATA          #IMPLIED
  >

<!ELEMENT textarea (#PCDATA)>     <!-- multi-line text field -->
<!ATTLIST textarea
  %attrs;
  %focus;
  name        CDATA          #IMPLIED
  rows        %Number;       #REQUIRED
  cols        %Number;       #REQUIRED
  disabled    (disabled)     #IMPLIED
  readonly    (readonly)     #IMPLIED
  onselect    %Script;       #IMPLIED
  onchange    %Script;       #IMPLIED
  >

<!--
  The fieldset element is used to group form fields.
  Only one legend element should occur in the content
  and if present should only be preceded by whitespace.
-->
<!ELEMENT fieldset (#PCDATA | legend | %block; | form | %inline; | %misc;)*>
<!ATTLIST fieldset
  %attrs;
  >

<!ENTITY % LAlign "(top|bottom|left|right)">

<!ELEMENT legend %Inline;>     <!-- fieldset label -->
<!ATTLIST legend
  %attrs;
  accesskey   %Character;    #IMPLIED
  align       %LAlign;       #IMPLIED
  >

<!--
 Content is %Flow; excluding a, form, form controls, iframe
--> 
<!ELEMENT button %button.content;>  <!-- push button -->
<!ATTLIST button
  %attrs;
  %focus;
  name        CDATA          #IMPLIED
  value       CDATA          #IMPLIED
  type        (button|submit|reset) "submit"
  disabled    (disabled)     #IMPLIED
  >

<!-- single-line text input control (DEPRECATED) -->
<!ELEMENT isindex EMPTY>
<!ATTLIST isindex
  %coreattrs;
  %i18n;
  prompt      %Text;         #IMPLIED
  >

<!--======================= Tables =======================================-->

<!-- Derived from IETF HTML table standard, see [RFC1942] -->

<!--
 The border attribute sets the thickness of the frame around the
 table. The default units are screen pixels.

 The frame attribute specifies which parts of the frame around
 the table should be rendered. The values are not the same as
 CALS to avoid a name clash with the valign attribute.
-->
<!ENTITY % TFrame "(void|above|below|hsides|lhs|rhs|vsides|box|border)">

<!--
 The rules attribute defines which rules to draw between cells:

 If rules is absent then assume:
     "none" if border is absent or border="0" otherwise "all"
-->

<!ENTITY % TRules "(none | groups | rows | cols | all)">
  
<!-- horizontal placement of table relative to document -->
<!ENTITY % TAlign "(left|center|right)">

<!-- horizontal alignment attributes for cell contents

  char        alignment char, e.g. char=":"
  charoff     offset for alignment char
-->
<!ENTITY % cellhalign
  "align      (left|center|right|justify|char) #IMPLIED
   char       %Character;    #IMPLIED
   charoff    %Length;       #IMPLIED"
  >

<!-- vertical alignment attributes for cell contents -->
<!ENTITY % cellvalign
  "valign     (top|middle|bottom|baseline) #IMPLIED"
  >

<!ELEMENT table
     (caption?, (col*|colgroup*), thead?, tfoot?, (tbody+|tr+))>
<!ELEMENT caption  %Inline;>
<!ELEMENT thead    (tr)+>
<!ELEMENT tfoot    (tr)+>
<!ELEMENT tbody    (tr)+>
<!ELEMENT colgroup (col)*>
<!ELEMENT col      EMPTY>
<!ELEMENT tr       (th|td)+>
<!ELEMENT th       %Flow;>
<!ELEMENT td       %Flow;>

<!ATTLIST table
  %attrs;
  summary     %Text;         #IMPLIED
  width       %Length;       #IMPLIED
  border      %Pixels;       #IMPLIED
  frame       %TFrame;       #IMPLIED
  rules       %TRules;       #IMPLIED
  cellspacing %Length;       #IMPLIED
  cellpadding %Length;       #IMPLIED
  align       %TAlign;       #IMPLIED
  bgcolor     %Color;        #IMPLIED
  >

<!ENTITY % CAlign "(top|bottom|left|right)">

<!ATTLIST caption
  %attrs;
  align       %CAlign;       #IMPLIED
  >

<!--
colgroup groups a set of col elements. It allows you to group
several semantically related columns together.
-->
<!ATTLIST colgroup
  %attrs;
  span        %Number;       "1"
  width       %MultiLength;  #IMPLIED
  %cellhalign;
  %cellvalign;
  >

<!--
 col elements define the alignment properties for cells in
 one or more columns.

 The width attribute specifies the width of the columns, e.g.

     width=64        width in screen pixels
     width=0.5*      relative width of 0.5

 The span attribute causes the attributes of one
 col element to apply to more than one column.
-->
<!ATTLIST col
  %attrs;
  span        %Number;       "1"
  width       %MultiLength;  #IMPLIED
  %cellhalign;
  %cellvalign;
  >

<!--
    Use thead to duplicate headers when breaking table
    across page boundaries, or for static headers when
    tbody sections are rendered in scrolling panel.

    Use tfoot to duplicate footers when breaking table
    across page boundaries, or for static footers when
    tbody sections are rendered in scrolling panel.

    Use multiple tbody sections when rules are needed
    between groups of table rows.
-->
<!ATTLIST thead
  %attrs;
  %cellhalign;
  %cellvalign;
  >

<!ATTLIST tfoot
  %attrs;
  %cellhalign;
  %cellvalign;
  >

<!ATTLIST tbody
  %attrs;
  %cellhalign;
  %cellvalign;
  >

<!ATTLIST tr
  %attrs;
  %cellhalign;
  %cellvalign;
  bgcolor     %Color;        #IMPLIED
  >

<!-- Scope is simpler than headers attribute for common tables -->
<!ENTITY % Scope "(row|col|rowgroup|colgroup)">

<!-- th is for headers, td for data and for cells acting as both -->

<!ATTLIST th
  %attrs;
  abbr        %Text;         #IMPLIED
  axis        CDATA          #IMPLIED
  headers     IDREFS         #IMPLIED
  scope       %Scope;        #IMPLIED
  rowspan     %Number;       "1"
  colspan     %Number;       "1"
  %cellhalign;
  %cellvalign;
  nowrap      (nowrap)       #IMPLIED
  bgcolor     %Color;        #IMPLIED
  width       %Pixels;       #IMPLIED
  height      %Pixels;       #IMPLIED
  >

<!ATTLIST td
  %attrs;
  abbr        %Text;         #IMPLIED
  axis        CDATA          #IMPLIED
  headers     IDREFS         #IMPLIED
  scope       %Scope;        #IMPLIED
  rowspan     %Number;       "1"
  colspan     %Number;       "1"
  %cellhalign;
  %cellvalign;
  nowrap      (nowrap)       #IMPLIED
  bgcolor     %Color;        #IMPLIED
  width       %Pixels;       #IMPLIED
  height      %Pixels;       #IMPLIED
  >

