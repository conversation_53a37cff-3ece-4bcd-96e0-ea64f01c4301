<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html
   PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
   "xhtml1-transitional.dtd">
<html xmlns='http://www.w3.org/1999/xhtml'>
<head>
<title>NIST DOM HTML Test - BR</title>
</head>
<body onload="parent.loadComplete()">
<table id="table-1" border="4" frame="border" cellpadding="2" cellspacing="2" summary="HTML Control Table" rules="all">
<caption>Table Caption</caption>
<thead align="center" valign="middle">
<tr align="center" valign="middle" char="*" charoff="1">
<th id="header-1">Employee Id</th>
<th id="header-2" abbr="maiden" axis="center" align="center" colspan="1" rowspan="1" scope="col" headers="header-1" valign="middle">Employee Name</th>
<th>Position</th>
<th>Salary</th>
<th>Gender</th>
<th>Address</th>
</tr>
</thead>
<tfoot align="center" valign="middle">
<tr>
<th>next page ...</th>
<th>next page ...</th>
<th>next page ...</th>
<th>next page ...</th>
<th>next page ...</th>
<th>next page ...</th>
</tr>
</tfoot>
<tbody align="center" valign="middle">
<tr>
<td axis="center" id="Table-3" abbr="maiden2" colspan="1" rowspan="1" scope="col" headers="header-2" valign="middle">EMP0001</td>
<td headers="header-2">Margaret Martin</td>
<td>Accountant</td>
<td>56,000</td>
<td>Female</td>
<td>1230 North Ave. Dallas, Texas 98551</td>
</tr>
<tr>
<td>EMP0002</td>
<td>Martha Raynolds</td>
<td>Secretary</td>
<td>35,000</td>
<td>Female</td>
<td>1900 Dallas Road Dallas, Texas 98554</td>
</tr>
</tbody>
</table>
<form id="form1" action="./files/getData.pl" method="post">
<p>
<select id="selectId" dir="ltr" tabindex="7" name="select1" multiple="multiple" size="1">
<option selected="selected" value="EMP1">EMP10001</option>
<option>EMP10002</option>
<option>EMP10003</option>
<option>EMP10004</option>
<option>EMP10005</option>
</select>
</p>
</form>
<p>
<select name="select2">
<option>EMP20001</option>
<option>EMP20002</option>
<option>EMP20003</option>
<option>EMP20004</option>
<option>EMP20005</option>
</select>
</p>
<p>
<select name="select3" disabled="disabled" tabindex="1">
<option>EMP30001</option>
<option>EMP30002</option>
<option>EMP30003</option>
<option>EMP30004</option>
<option>EMP30005</option>
</select>
</p>
</body>
</html>

