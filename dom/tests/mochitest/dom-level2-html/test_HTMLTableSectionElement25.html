<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
<title>http://www.w3.org/2001/DOM-Test-Suite/level2/html/HTMLTableSectionElement25</title>
<link type="text/css" rel="stylesheet" href="/tests/SimpleTest/test.css">
<script src="/tests/SimpleTest/SimpleTest.js" type="text/javascript"></script>
<script src="DOMTestCase.js" type="text/javascript"></script>
<script type="text/javascript">
// expose test function names
function exposeTestFunctionNames()
{
return ['HTMLTableSectionElement25'];
}

var docsLoaded = -1000000;
var builder = null;

//
//   This function is called by the testing framework before
//      running the test suite.
//
//   If there are no configuration exceptions, asynchronous
//        document loading is started.  Otherwise, the status
//        is set to complete and the exception is immediately
//        raised when entering the body of the test.
//
function setUpPage() {
   setUpPageStatus = 'running';
   try {
     //
     //   creates test document builder, may throw exception
     //
     builder = createConfiguredBuilder();

      docsLoaded = 0;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      docsLoaded += preload(docRef, "doc", "tablesection");
        
       if (docsLoaded == 1) {
          setUpPage = 'complete';
       }
    } catch(ex) {
    	catchInitializationError(builder, ex);
        setUpPage = 'complete';
    }
}



//
//   This method is called on the completion of 
//      each asychronous load started in setUpTests.
//
//   When every synchronous loaded document has completed,
//      the page status is changed which allows the
//      body of the test to be executed.
function loadComplete() {
    if (++docsLoaded == 1) {
        setUpPageStatus = 'complete';
        runJSUnitTests();
        SimpleTest.finish();
    }
}


/**
* 
    The insertRow() method throws a INDEX_SIZE_ERR DOMException
    if the specified index is greater than the number of rows. 
 
    Retrieve the first THEAD element which has one row.  Try
    to insert a new row using an index of two.  This should throw
    a INDEX_SIZE_ERR DOMException since there is only one row.

* <AUTHOR>
* <AUTHOR> Rivello
* @see http://www.w3.org/TR/DOM-Level-2-HTML/html#ID-93995626
* @see http://www.w3.org/TR/DOM-Level-2-HTML/html#xpointer(id('ID-93995626')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='INDEX_SIZE_ERR'])
*/
function HTMLTableSectionElement25() {
   var success;
    if(checkInitialization(builder, "HTMLTableSectionElement25") != null) return;
    var nodeList;
      var testNode;
      var newRow;
      var doc;
      
      var docRef = null;
      if (typeof(this.doc) != 'undefined') {
        docRef = this.doc;
      }
      doc = load(docRef, "doc", "tablesection");
      nodeList = doc.getElementsByTagName("thead");
      assertSize("Asize",1,nodeList);
testNode = nodeList.item(0);
      
	{
		success = false;
		try {
            newRow = testNode.insertRow(2);
        }
		catch(ex) {
      success = (typeof(ex.code) != 'undefined' && ex.code == 1);
		}
		assertTrue("HTMLTableSectionElement25",success);
	}

}

</script>
</head>
<body>
<h2>Test http://www.w3.org/2001/DOM-Test-Suite/level2/html/HTMLTableSectionElement25</h2>
<p>&lt;test name='HTMLTableSectionElement25' schemaLocation='http://www.w3.org/2001/DOM-Test-Suite/Level-2 dom2.xsd'&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;title&gt;HTMLTableSectionElement25&lt;/title&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;creator&gt;NIST&lt;/creator&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;description&gt;
    The insertRow() method throws a INDEX_SIZE_ERR DOMException
    if the specified index is greater than the number of rows. 
 
    Retrieve the first THEAD element which has one row.  Try
    to insert a new row using an index of two.  This should throw
    a INDEX_SIZE_ERR DOMException since there is only one row.
&lt;/description&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;contributor&gt;Rick Rivello&lt;/contributor&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;date qualifier='created'&gt;2002-05-02&lt;/date&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;subject resource='<a href="http://www.w3.org/TR/DOM-Level-2-HTML/html#ID-93995626">http://www.w3.org/TR/DOM-Level-2-HTML/html#ID-93995626</a>'/&gt;
<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;subject resource='<a href="http://www.w3.org/TR/DOM-Level-2-HTML/html#ID-93995626">http://www.w3.org/TR/DOM-Level-2-HTML/html#xpointer(id('ID-93995626')/raises/exception[@name='DOMException']/descr/p[substring-before(.,':')='INDEX_SIZE_ERR'])</a>'/&gt;
<br>&lt;/metadata&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='nodeList' type='NodeList'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='testNode' type='Node'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='newRow' type='HTMLElement'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;var name='doc' type='Document'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;load var='doc' href='tablesection' willBeModified='true'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;getElementsByTagName interface='Document' obj='doc' var='nodeList' tagname='"thead"'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;assertSize collection='nodeList' size='1' <a id="Asize">id='Asize'</a>/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;item interface='NodeList' obj='nodeList' var='testNode' index='0'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;assertDOMException <a id="HTMLTableSectionElement25">id='HTMLTableSectionElement25'</a>&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;INDEX_SIZE_ERR&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;insertRow interface='HTMLTableSectionElement' obj='testNode' var='newRow' index='2'/&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/INDEX_SIZE_ERR&gt;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/assertDOMException&gt;<br>&lt;/test&gt;<br>
</p>
<p>
			Copyright (c) 2001-2004 World Wide Web Consortium,
			(Massachusetts Institute of Technology, Institut National de
			Recherche en Informatique et en Automatique, Keio University). All
			Rights Reserved. This program is distributed under the W3C's Software
			Intellectual Property License. This program is distributed in the
			hope that it will be useful, but WITHOUT ANY WARRANTY; without even
			the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
			PURPOSE.
			</p>
<p>See W3C License <a href="http://www.w3.org/Consortium/Legal/">http://www.w3.org/Consortium/Legal/</a> 
 for more details.</p>
<iframe name="doc" src="files/tablesection.html"></iframe>
<br>
</body>
</html>
