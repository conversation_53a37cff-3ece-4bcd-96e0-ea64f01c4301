# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("DevTools", "Debugger")

EXPORTS.mozilla.dom += [
    "CallbackDebuggerNotification.h",
    "DebuggerNotification.h",
    "DebuggerNotificationManager.h",
    "DebuggerNotificationObserver.h",
    "EventCallbackDebuggerNotification.h",
]

UNIFIED_SOURCES += [
    "CallbackDebuggerNotification.cpp",
    "DebuggerNotification.cpp",
    "DebuggerNotificationManager.cpp",
    "DebuggerNotificationObserver.cpp",
    "EventCallbackDebuggerNotification.cpp",
]

FINAL_LIBRARY = "xul"
