// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2022 Igalia, S.L. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal.plaindatetime.prototype.withcalendar
description: A number is not allowed to be a calendar
features: [Temporal]
---*/

const instance = new Temporal.PlainDateTime(1976, 11, 18, 15, 23, 30, 123, 456, 789, "iso8601");

const numbers = [
  1,
  -19761118,
  19761118,
  **********,
];

for (const arg of numbers) {
  assert.throws(
    TypeError,
    () => instance.withCalendar(arg),
    "A number is not a valid ISO string for Calendar"
  );
}

reportCompare(0, 0);
