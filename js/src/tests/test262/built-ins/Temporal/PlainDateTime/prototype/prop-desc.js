// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2021 Igalia, S.L. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal-plaindatetime-prototype
description: The "prototype" property of Temporal.PlainDateTime
includes: [propertyHelper.js]
features: [Temporal]
---*/

assert.sameValue(typeof Temporal.PlainDateTime.prototype, "object");
assert.notSameValue(Temporal.PlainDateTime.prototype, null);

verifyProperty(Temporal.PlainDateTime, "prototype", {
  writable: false,
  enumerable: false,
  configurable: false,
});

reportCompare(0, 0);
