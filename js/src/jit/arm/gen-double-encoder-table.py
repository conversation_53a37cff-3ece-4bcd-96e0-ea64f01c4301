#!/usr/bin/env python
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
"""Generate tables of immediately-encodable VFP doubles.

DOES NOT get automatically run during the build process.  If you need to
modify this file (which is unlikely), you must re-run this script:

python gen-double-encode-table.py > $(topsrcdir)/path/to/DoubleEntryTable.tbl
"""

import operator


def rep(bit, count):
    return reduce(operator.ior, [bit << c for c in range(count)])


def encodeDouble(value):
    """Generate an ARM ARM 'VFP modified immediate constant' with format:
    aBbbbbbb bbcdefgh 000...

    We will return the top 32 bits of the double; the rest are 0."""
    assert (0 <= value) and (value <= 255)
    a = value >> 7
    b = (value >> 6) & 1
    B = int(b == 0)
    cdefgh = value & 0x3F
    return (a << 31) | (B << 30) | (rep(b, 8) << 22) | cdefgh << 16


print("/* THIS FILE IS AUTOMATICALLY GENERATED BY gen-double-encode-table.py.  */")
for i in range(256):
    print("  { 0x%08x, { %d, %d, 0 } }," % (encodeDouble(i), i & 0xF, i >> 4))
