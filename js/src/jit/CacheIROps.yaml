# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# [SMDOC] CacheIR Opcodes
# =======================
# This file defines all CacheIR opcodes and their arguments.
#
# Each op has the following attributes:
#
# name
# ====
# Opcode name. Convention is to use a name ending in *Result for ops that store
# to the IC's output register.
#
# shared
# ======
# If true, Baseline and Ion use the same CacheIRCompiler code for this op.
# If false, the op must be implemented in both BaselineCacheIRCompiler and
# IonCacheIRCompiler.
#
# transpile
# =========
# Whether this op can be transpiled to MIR by WarpCacheIRTranspiler.
#
# cost_estimate
# =========
# Score of an individual CacheIR Opcode's contribution to the overall score for
# each stub. This score is based off of the cost of the masm calls made by the op's
# implementation. The higher the score the more costly the op is.
#
# How to decide the cost estimate for a CacheIROp:
# 0 points - Generates no code
# 1 point - 1-5 simple masm ops, no callVM or callWithABI
# 2 points - 5-20 masm ops, no callVM or callWithABI
# 3 points - 20+ masm ops, no callVM or callWithABI
# 4 points - callWithABI
# 5 points - callVM
# 6 points - more than one callWithABI or callVM
#
# In the case of the op not being shared, default to counting the Baseline
# implementation.
#
# If the cost estimate is different based off of what branch of a conditional
# is taken, assign the score of the branch with the highest cost.
#
# Note:
# Currently, the scoring is tentative. It is in place to provide an
# estimate for the cost of each op. The scoring will be refined.
#
# custom_writer (optional)
# ========================
# If true, the generated CacheIRWriter method will be private and has a trailing
# '_'. This is useful for ops that need custom CacheIRWriter logic on top of the
# generated code.
#
# args
# ====
# List of arguments encoded in the bytecode stream. There are three argument
# kinds:
#
# - Id (ObjId, ValId, ...): refers to either an IC input or a value defined by
#   a previous CacheIR instruction. This is encoded as integer in the bytecode
#   stream.
#
# - Field (ObjectField, StringField, ...): specific value is stored in the stub
#   data and the bytecode stream stores the offset of this field. This means the
#   CacheIR is not specialized for particular values and code can be shared.
#
# - Immediate (BoolImm, Int32Imm, JSOpImm, ...): a value baked directly into
#   the bytecode stream. This is useful for bits of state that need to be
#   available to all CacheIR compilers/transpilers.
#
# If there's an argument named 'result', the generated CacheIRWriter method will
# return a new OperandId of this type.

- name: ReturnFromIC
  shared: false
  transpile: true
  cost_estimate: 1
  args:

- name: GuardToObject
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardIsNullOrUndefined
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

- name: GuardIsNull
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

- name: GuardIsUndefined
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

- name: GuardIsNotUninitializedLexical
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: ValId

- name: GuardToBoolean
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardToString
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardToSymbol
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardToBigInt
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardIsNumber
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardToInt32
  shared: true
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    input: ValId

- name: GuardToNonGCThing
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

# If the Value is a boolean, convert it to int32.
- name: GuardBooleanToInt32
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId
    result: Int32Id

- name: GuardToInt32Index
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId
    result: Int32Id

- name: Int32ToIntPtr
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id
    result: IntPtrId

- name: GuardNumberToIntPtrIndex
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: NumberId
    supportOOB: BoolImm
    result: IntPtrId

- name: GuardToInt32ModUint32
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: ValId
    result: Int32Id

- name: GuardToUint8Clamped
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: ValId
    result: Int32Id

# Note: this doesn't accept doubles to avoid ambiguity about whether it includes
# int32 values. Use GuardIsNumber instead.
- name: GuardNonDoubleType
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId
    type: ValueTypeImm

- name: GuardShape
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    shape: WeakShapeField

- name: GuardMultipleShapes
  shared: true
  transpile: true
  cost_estimate: 2
  custom_writer: true
  args:
    obj: ObjId
    shapes: ObjectField

- name: GuardProto
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    proto: WeakObjectField

- name: GuardNullProto
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

# Guard per GuardClassKind.
- name: GuardClass
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    kind: GuardClassKindImm

# Guard per GuardClassKind.
- name: GuardEitherClass
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    kind1: GuardClassKindImm
    kind2: GuardClassKindImm

# Guard on a realm fuse.
- name: GuardFuse
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fuseWord: RealmFuseIndexImm

# Guard on an arbitrary JSClass.
- name: GuardAnyClass
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    clasp: RawPointerField

- name: GuardGlobalGeneration
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    expected: RawInt32Field
    generationAddr: RawPointerField

- name: HasClassResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    clasp: RawPointerField

- name: CallRegExpMatcherResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    regexp: ObjId
    input: StringId
    lastIndex: Int32Id
    stub: JitCodeField

- name: CallRegExpSearcherResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    regexp: ObjId
    input: StringId
    lastIndex: Int32Id
    stub: JitCodeField

- name: RegExpSearcherLastLimitResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:

- name: RegExpHasCaptureGroupsResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    regexp: ObjId
    input: StringId

- name: RegExpBuiltinExecMatchResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    regexp: ObjId
    input: StringId
    stub: JitCodeField

- name: RegExpBuiltinExecTestResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    regexp: ObjId
    input: StringId
    stub: JitCodeField

- name: RegExpFlagResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    regexp: ObjId
    flagsMask: Int32Imm

- name: CallSubstringKernelResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    begin: Int32Id
    length: Int32Id

- name: StringReplaceStringResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    pattern: StringId
    replacement: StringId

- name: StringSplitStringResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    separator: StringId

- name: RegExpPrototypeOptimizableResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    proto: ObjId

- name: RegExpInstanceOptimizableResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    regexp: ObjId
    proto: ObjId

- name: GetFirstDollarIndexResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

# Add a reference to a global in the compartment to keep it alive.
- name: GuardCompartment
  shared: false
  transpile: false
  cost_estimate: 2
  args:
    obj: ObjId
    global: ObjectField
    compartment: RawPointerField

- name: GuardIsExtensible
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsNativeObject
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsProxy
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsNotProxy
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsNotArrayBufferMaybeShared
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsTypedArray
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsFixedLengthTypedArray
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardIsResizableTypedArray
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardHasProxyHandler
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    handler: RawPointerField

- name: GuardIsNotDOMProxy
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardSpecificObject
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    expected: WeakObjectField

- name: GuardObjectIdentity
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj1: ObjId
    obj2: ObjId

- name: GuardSpecificFunction
  shared: false
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    fun: ObjId
    expected: WeakObjectField
    nargsAndFlags: RawInt32Field

- name: GuardFunctionScript
  shared: false
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    obj: ObjId
    expected: WeakBaseScriptField
    nargsAndFlags: RawInt32Field

- name: GuardSpecificAtom
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    expected: AtomField

- name: GuardSpecificSymbol
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    sym: SymbolId
    expected: SymbolField

- name: GuardSpecificInt32
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    num: Int32Id
    expected: Int32Imm

- name: GuardNoDenseElements
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: GuardStringToIndex
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    result: Int32Id

- name: GuardStringToInt32
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    result: Int32Id

- name: GuardStringToNumber
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    result: NumberId

- name: StringToAtom
  shared: true
  transpile: true
  custom_writer: true
  cost_estimate: 4
  args:
    str: StringId

- name: BooleanToNumber
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    boolean: BooleanId
    result: NumberId

- name: GuardHasGetterSetter
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    id: IdField
    getterSetter: WeakGetterSetterField

- name: GuardInt32IsNonNegative
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    index: Int32Id

- name: GuardIndexIsValidUpdateOrAdd
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    index: Int32Id

- name: GuardIndexIsNotDenseElement
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    index: Int32Id

- name: GuardTagNotEqual
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: ValueTagId
    rhs: ValueTagId

- name: GuardXrayExpandoShapeAndDefaultProto
  shared: true
  transpile: false
  cost_estimate: 2
  args:
    obj: ObjId
    shapeWrapper: ObjectField

- name: GuardXrayNoExpando
  shared: true
  transpile: false
  cost_estimate: 2
  args:
    obj: ObjId

# Guard obj[slot] == expected.
- name: GuardDynamicSlotIsSpecificObject
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    expected: ObjId
    slot: RawInt32Field

# Guard obj[slot] is not an object.
- name: GuardDynamicSlotIsNotObject
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    slot: RawInt32Field

- name: GuardFixedSlotValue
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    offset: RawInt32Field
    val: ValueField

- name: GuardDynamicSlotValue
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    offset: RawInt32Field
    val: ValueField

- name: LoadScriptedProxyHandler
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ObjId
    obj: ObjId

- name: IdToStringOrSymbol
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    result: ValId
    id: ValId

- name: LoadFixedSlot
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ValId
    obj: ObjId
    offset: RawInt32Field

- name: LoadDynamicSlot
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ValId
    obj: ObjId
    slot: RawInt32Field

- name: GuardNoAllocationMetadataBuilder
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    builderAddr: RawPointerField

- name: GuardFunctionHasJitEntry
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fun: ObjId

- name: GuardFunctionHasNoJitEntry
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fun: ObjId

- name: GuardFunctionIsNonBuiltinCtor
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fun: ObjId

- name: GuardFunctionIsConstructor
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fun: ObjId

- name: GuardNotClassConstructor
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    fun: ObjId

- name: GuardArrayIsPacked
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    array: ObjId

- name: GuardArgumentsObjectFlags
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    flags: ByteImm

- name: LoadObject
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ObjId
    obj: ObjectField

# This is just LoadObject with extra information for the purpose of optimizing
# out shape guards if we're just storing to slots of the receiver object.
- name: LoadProtoObject
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ObjId
    protoObj: ObjectField
    receiverObj: ObjId

- name: LoadProto
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ObjId

- name: LoadEnclosingEnvironment
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ObjId

- name: LoadWrapperTarget
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ObjId
    fallible: BoolImm

- name: LoadValueTag
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: ValId
    result: ValueTagId

- name: LoadArgumentFixedSlot
  shared: false
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    result: ValId
    slotIndex: ByteImm

- name: LoadArgumentDynamicSlot
  shared: false
  transpile: true
  cost_estimate: 1
  custom_writer: true
  args:
    result: ValId
    argc: Int32Id
    slotIndex: ByteImm

- name: TruncateDoubleToUInt32
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId
    result: Int32Id

- name: DoubleToUint8Clamped
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: NumberId
    result: Int32Id

- name: MegamorphicLoadSlotResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    name: IdField

- name: MegamorphicLoadSlotByValueResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    id: ValId

- name: MegamorphicLoadSlotPermissiveResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    name: IdField

- name: MegamorphicLoadSlotByValuePermissiveResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    id: ValId

- name: MegamorphicStoreSlot
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    name: IdField
    rhs: ValId
    strict: BoolImm

- name: MegamorphicSetElement
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: ValId
    rhs: ValId
    strict: BoolImm

- name: MegamorphicHasPropResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    id: ValId
    hasOwn: BoolImm

# For the case where we have an object with a small number of properties,
# we can do better than a megamorphic hasprop if we just run through those
# IDs and check for atom pointer equality.
- name: SmallObjectVariableKeyHasOwnResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    # We don't actually need the object, because we only care about the shape,
    # which was guarded before this
    id: StringId
    # We include the prop names to speed up and simplify the Baseline case,
    # but only the shape is used from Ion to achieve an unrolled loop with
    # comparisons to known atoms
    propNames: ObjectField
    shape: ShapeField

- name: ObjectToIteratorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    enumeratorsAddr: RawPointerField

- name: ValueToIteratorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    val: ValId

# See CacheIR.cpp 'DOM proxies' comment.
- name: LoadDOMExpandoValue
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ValId

- name: LoadDOMExpandoValueGuardGeneration
  shared: false
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    expandoAndGeneration: RawPointerField
    generation: RawInt64Field
    result: ValId

- name: LoadDOMExpandoValueIgnoreGeneration
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ValId

- name: GuardDOMExpandoMissingOrGuardShape
  shared: false
  transpile: true
  cost_estimate: 2
  args:
    expando: ValId
    shape: ShapeField

- name: StoreFixedSlot
  shared: false
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId

- name: StoreDynamicSlot
  shared: false
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId

- name: AddAndStoreFixedSlot
  shared: false
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId
    newShape: ShapeField

- name: AddAndStoreDynamicSlot
  shared: false
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId
    newShape: ShapeField

- name: AllocateAndStoreDynamicSlot
  shared: false
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId
    newShape: ShapeField
    numNewSlots: RawInt32Field

- name: AddSlotAndCallAddPropHook
  shared: true
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    rhs: ValId
    newShape: ShapeField

- name: StoreDenseElement
  shared: true
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    index: Int32Id
    rhs: ValId

- name: StoreDenseElementHole
  shared: true
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    index: Int32Id
    rhs: ValId
    handleAdd: BoolImm

- name: ArrayPush
  shared: true
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId
    rhs: ValId

- name: ArrayJoinResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    sep: StringId

- name: ObjectKeysResult
  shared: true
  transpile: true
  cost_estimate: 6
  args:
    obj: ObjId

- name: PackedArrayPopResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    array: ObjId

- name: PackedArrayShiftResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    array: ObjId

- name: PackedArraySliceResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    array: ObjId
    begin: Int32Id
    end: Int32Id

- name: ArgumentsSliceResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    args: ObjId
    begin: Int32Id
    end: Int32Id

- name: IsArrayResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    input: ValId

- name: StoreFixedSlotUndefinedResult
  shared: true
  transpile: true
  args:
    obj: ObjId
    offset: RawInt32Field
    rhs: ValId

- name: IsObjectResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

- name: IsPackedArrayResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: IsCallableResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: ValId

- name: IsConstructorResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId

- name: IsCrossRealmArrayConstructorResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: IsTypedArrayResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    isPossiblyWrapped: BoolImm

- name: IsTypedArrayConstructorResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ArrayBufferViewByteOffsetInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: ArrayBufferViewByteOffsetDoubleResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: ResizableTypedArrayByteOffsetMaybeOutOfBoundsInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableTypedArrayByteOffsetMaybeOutOfBoundsDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: TypedArrayByteLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: TypedArrayByteLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableTypedArrayByteLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableTypedArrayByteLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableTypedArrayLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableTypedArrayLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: TypedArrayElementSizeResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableDataViewByteLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: ResizableDataViewByteLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: GrowableSharedArrayBufferByteLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: GrowableSharedArrayBufferByteLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: GuardHasAttachedArrayBuffer
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: GuardResizableArrayBufferViewInBounds
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: GuardResizableArrayBufferViewInBoundsOrDetached
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: NewArrayIteratorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: NewStringIteratorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: NewRegExpStringIteratorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: ObjectCreateResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: NewArrayFromLengthResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    length: Int32Id
    site: AllocSiteField

- name: NewTypedArrayFromLengthResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    length: Int32Id

- name: NewTypedArrayFromArrayBufferResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    buffer: ObjId
    byteOffset: ValId
    length: ValId

- name: NewTypedArrayFromArrayResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    array: ObjId

- name: NewMapObjectResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: NewSetObjectResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField

- name: NewMapObjectFromIterableResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    iterable: ValId

- name: NewSetObjectFromIterableResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    iterable: ValId

- name: NewStringObjectResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    templateObject: ObjectField
    str: StringId

- name: StringFromCharCodeResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    code: Int32Id

- name: StringFromCodePointResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    code: Int32Id

- name: StringIncludesResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    searchStr: StringId

- name: StringIndexOfResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    searchStr: StringId

- name: StringLastIndexOfResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    searchStr: StringId

- name: StringStartsWithResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    searchStr: StringId

- name: StringEndsWithResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    searchStr: StringId

- name: StringToLowerCaseResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

- name: StringToUpperCaseResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

- name: StringTrimResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

- name: StringTrimStartResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

- name: StringTrimEndResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId

- name: MathAbsInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: Int32Id

- name: MathAbsNumberResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: MathClz32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: MathSignInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: MathSignNumberResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: NumberId

- name: MathSignNumberToInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: NumberId

- name: MathImulResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: MathSqrtNumberResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: MathFRoundNumberResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: MathF16RoundNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId

# Because Baseline stub code is shared by all realms in the Zone, this
# instruction loads a pointer to the RNG from a stub field.
- name: MathRandomResult
  shared: false
  transpile: true
  cost_estimate: 3
  args:
    rng: RawPointerField

- name: MathHypot2NumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    first: NumberId
    second: NumberId

- name: MathHypot3NumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    first: NumberId
    second: NumberId
    third: NumberId

- name: MathHypot4NumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    first: NumberId
    second: NumberId
    third: NumberId
    fourth: NumberId

- name: MathAtan2NumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    lhs: NumberId
    rhs: NumberId

- name: MathFloorNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId

- name: MathCeilNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId

- name: MathTruncNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId

- name: MathFloorToInt32Result
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    input: NumberId

- name: MathCeilToInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: MathTruncToInt32Result
  shared: true
  transpile: true
  args:
    input: NumberId

- name: MathRoundToInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: Int32MinMax
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    isMax: BoolImm
    first: Int32Id
    second: Int32Id
    result: Int32Id

- name: NumberMinMax
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    isMax: BoolImm
    first: NumberId
    second: NumberId
    result: NumberId

- name: Int32MinMaxArrayResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    array: ObjId
    isMax: BoolImm

- name: NumberMinMaxArrayResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    array: ObjId
    isMax: BoolImm

- name: MathFunctionNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId
    fun: UnaryMathFunctionImm

- name: NumberParseIntResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    radix: Int32Id

- name: DoubleParseIntResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    num: NumberId

- name: ObjectToStringResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId

- name: ReflectGetPrototypeOfResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId

- name: StoreTypedArrayElement
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    obj: ObjId
    elementType: ScalarTypeImm
    index: IntPtrId
    rhs: RawId
    handleOOB: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsCompareExchangeResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    expected: RawId
    replacement: RawId
    elementType: ScalarTypeImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsExchangeResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsAddResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    forEffect: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsSubResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    forEffect: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsAndResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    forEffect: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsOrResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    forEffect: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsXorResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    forEffect: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsLoadResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: IntPtrId
    elementType: ScalarTypeImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsStoreResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: IntPtrId
    value: RawId
    elementType: ScalarTypeImm
    viewKind: ArrayBufferViewKindImm

- name: AtomicsIsLockFreeResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    value: Int32Id

- name: AtomicsPauseResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:

- name: CallNativeSetter
  shared: false
  transpile: true
  cost_estimate: 5
  custom_writer: true
  args:
    receiver: ObjId
    setter: ObjectField
    rhs: ValId
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallScriptedSetter
  shared: false
  transpile: true
  cost_estimate: 3
  custom_writer: true
  args:
    receiver: ObjId
    setter: ObjectField
    rhs: ValId
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallInlinedSetter
  shared: false
  transpile: true
  cost_estimate: 3
  custom_writer: true
  args:
    receiver: ObjId
    setter: ObjectField
    rhs: ValId
    icScript: RawPointerField
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallDOMSetter
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    jitInfo: RawPointerField
    rhs: ValId

- name: CallSetArrayLength
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    strict: BoolImm
    rhs: ValId

- name: ProxySet
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: IdField
    rhs: ValId
    strict: BoolImm

- name: ProxySetByValue
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: ValId
    rhs: ValId
    strict: BoolImm

- name: CallAddOrUpdateSparseElementHelper
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: Int32Id
    rhs: ValId
    strict: BoolImm

- name: CallInt32ToString
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: Int32Id
    result: StringId

- name: CallNumberToString
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: NumberId
    result: StringId

- name: Int32ToStringWithBaseResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    input: Int32Id
    base: Int32Id

- name: BooleanToString
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: BooleanId
    result: StringId

- name: CallScriptedFunction
  shared: false
  transpile: true
  cost_estimate: 3
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    flags: CallFlagsImm
    argcFixed: UInt32Imm

- name: CallBoundScriptedFunction
  shared: false
  transpile: true
  cost_estimate: 3
  args:
    callee: ObjId
    target: ObjId
    argc: Int32Id
    flags: CallFlagsImm
    numBoundArgs: UInt32Imm

- name: CallWasmFunction
  shared: false
  transpile: true
  cost_estimate: 3
  args:
    callee: ObjId
    argc: Int32Id
    flags: CallFlagsImm
    argcFixed: UInt32Imm
    funcType: RawPointerField
    instance: ObjectField

- name: GuardWasmArg
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    arg: ValId
    type: WasmValTypeImm

- name: CallNativeFunction
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    flags: CallFlagsImm
    argcFixed: UInt32Imm
#ifdef JS_SIMULATOR
    target: RawPointerField
#else
    ignoresReturnValue: BoolImm
#endif

- name: CallDOMFunction
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    thisObj: ObjId
    flags: CallFlagsImm
    argcFixed: UInt32Imm
#ifdef JS_SIMULATOR
    target: RawPointerField
#endif

- name: CallDOMFunctionWithAllocSite
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    thisObj: ObjId
    flags: CallFlagsImm
    argcFixed: UInt32Imm
    site: AllocSiteField
#ifdef JS_SIMULATOR
    target: RawPointerField
#endif

- name: CallClassHook
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    flags: CallFlagsImm
    argcFixed: UInt32Imm
    target: RawPointerField

- name: CallInlinedFunction
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    callee: ObjId
    argc: Int32Id
    icScript: RawPointerField
    flags: CallFlagsImm
    argcFixed: UInt32Imm

#ifdef JS_PUNBOX64
- name: CallScriptedProxyGetResult
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    target: ValId
    receiver: ObjId
    handler: ObjId
    trap: ObjId
    property: IdField
    nargsAndFlags: UInt32Imm

- name: CallScriptedProxyGetByValueResult
  shared: false
  transpile: true
  cost_estimate: 4
  custom_writer: true
  args:
    target: ValId
    receiver: ObjId
    handler: ObjId
    property: ValId
    trap: ObjId
    nargsAndFlags: UInt32Imm
  #endif

# Meta ops generate no code, but contain data for the Warp Transpiler.
- name: MetaScriptedThisShape
  shared: true
  transpile: true
  cost_estimate: 0
  custom_writer: true
  args:
    thisShape: ShapeField

- name: BindFunctionResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    target: ObjId
    argc: UInt32Imm
    templateObject: ObjectField

- name: SpecializedBindFunctionResult
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    target: ObjId
    argc: UInt32Imm
    templateObject: ObjectField

- name: LoadFixedSlotResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    offset: RawInt32Field

- name: LoadFixedSlotTypedResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    offset: RawInt32Field
    type: ValueTypeImm

- name: LoadDynamicSlotResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    offset: RawInt32Field

- name: LoadDenseElementResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: LoadDenseElementHoleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: CallGetSparseElementResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    index: Int32Id

- name: LoadDenseElementExistsResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    index: Int32Id

- name: LoadTypedArrayElementExistsResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: IntPtrId
    viewKind: ArrayBufferViewKindImm

- name: LoadDenseElementHoleExistsResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: LoadTypedArrayElementResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: IntPtrId
    elementType: ScalarTypeImm
    handleOOB: BoolImm
    forceDoubleForUint32: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: LoadDataViewValueResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    offset: IntPtrId
    littleEndian: BooleanId
    elementType: ScalarTypeImm
    forceDoubleForUint32: BoolImm
    viewKind: ArrayBufferViewKindImm

- name: StoreDataViewValueResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    offset: IntPtrId
    value: RawId
    littleEndian: BooleanId
    elementType: ScalarTypeImm
    viewKind: ArrayBufferViewKindImm

- name: LoadInt32ArrayLengthResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadInt32ArrayLength
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: Int32Id

- name: LoadArgumentsObjectArgResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: LoadArgumentsObjectArgHoleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: LoadArgumentsObjectArgExistsResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId
    index: Int32Id

- name: LoadArgumentsObjectLengthResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadArgumentsObjectLength
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: Int32Id

- name: LoadFunctionLengthResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: LoadFunctionNameResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    obj: ObjId

- name: LoadBoundFunctionNumArgs
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: Int32Id

- name: LoadBoundFunctionTarget
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId
    result: ObjId

- name: GuardBoundFunctionIsConstructor
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadArrayBufferByteLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadArrayBufferByteLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadArrayBufferViewLengthInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadArrayBufferViewLengthDoubleResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LinearizeForCharAccess
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    index: Int32Id
    result: StringId

- name: LinearizeForCodePointAccess
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    str: StringId
    index: Int32Id
    result: StringId

- name: ToRelativeStringIndex
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    index: Int32Id
    str: StringId
    result: Int32Id

- name: LoadStringCharResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    index: Int32Id
    handleOOB: BoolImm

- name: LoadStringAtResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    str: StringId
    index: Int32Id
    handleOOB: BoolImm

- name: LoadStringCharCodeResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    str: StringId
    index: Int32Id
    handleOOB: BoolImm

- name: LoadStringCodePointResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    str: StringId
    index: Int32Id
    handleOOB: BoolImm

- name: LoadStringLengthResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    str: StringId

- name: FrameIsConstructingResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:

- name: LoadObjectResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    obj: ObjId

- name: LoadStringResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    str: StringId

- name: LoadSymbolResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    sym: SymbolId

- name: LoadInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: Int32Id

- name: LoadDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    val: NumberId

- name: LoadBigIntResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: BigIntId

- name: CallScriptedGetterResult
  shared: false
  transpile: true
  cost_estimate: 5
  custom_writer: true
  args:
    receiver: ValId
    getter: ObjectField
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallInlinedGetterResult
  shared: false
  transpile: true
  cost_estimate: 5
  custom_writer: true
  args:
    receiver: ValId
    getter: ObjectField
    icScript: RawPointerField
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallNativeGetterResult
  shared: false
  transpile: true
  cost_estimate: 5
  custom_writer: true
  args:
    receiver: ValId
    getter: ObjectField
    sameRealm: BoolImm
    nargsAndFlags: RawInt32Field

- name: CallDOMGetterResult
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    jitInfo: RawPointerField

- name: ProxyGetResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: IdField

- name: ProxyGetByValueResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: ValId

- name: ProxyHasPropResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    id: ValId
    hasOwn: BoolImm

- name: CallObjectHasSparseElementResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    index: Int32Id

- name: CallNativeGetElementResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    index: Int32Id

- name: CallNativeGetElementSuperResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    index: Int32Id
    receiver: ValId

- name: GetNextMapSetEntryForIteratorResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    iter: ObjId
    resultArr: ObjId
    isMap: BoolImm

- name: LoadUndefinedResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:

- name: LoadBooleanResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: BoolImm

- name: LoadInt32Constant
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: RawInt32Field
    result: Int32Id

- name: LoadDoubleConstant
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: DoubleField
    result: NumberId

- name: LoadBooleanConstant
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: BoolImm
    result: BooleanId

- name: LoadUndefined
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    result: ValId

- name: LoadConstantString
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    str: StringField
    result: StringId

- name: LoadConstantStringResult
  shared: false
  transpile: true
  cost_estimate: 1
  args:
    str: StringField

- name: LoadInstanceOfObjectResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    lhs: ValId
    proto: ObjId

- name: LoadTypeOfObjectResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId

- name: LoadTypeOfEqObjectResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    operand: TypeofEqOperandImm

- name: DoubleAddResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: NumberId
    rhs: NumberId

- name: DoubleSubResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: NumberId
    rhs: NumberId

- name: DoubleMulResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: NumberId
    rhs: NumberId

- name: DoubleDivResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: NumberId
    rhs: NumberId

- name: DoubleModResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    lhs: NumberId
    rhs: NumberId

- name: DoublePowResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    lhs: NumberId
    rhs: NumberId

- name: Int32AddResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32SubResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32MulResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32DivResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32ModResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32PowResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: BigIntAddResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntSubResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntMulResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntDivResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntModResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntPowResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntToIntPtr
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: BigIntId
    result: IntPtrId

- name: IntPtrToBigIntResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    input: IntPtrId

- name: BigIntPtrAdd
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrSub
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrMul
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrDiv
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrMod
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrPow
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: Int32BitOrResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32BitXorResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32BitAndResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32LeftShiftResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32RightShiftResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: Int32Id
    rhs: Int32Id

- name: Int32URightShiftResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    lhs: Int32Id
    rhs: Int32Id
    forceDouble: BoolImm

- name: Int32NotResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: BigIntBitOrResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntBitXorResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntBitAndResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntLeftShiftResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntRightShiftResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: BigIntId
    rhs: BigIntId

- name: BigIntNotResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    input: BigIntId

- name: BigIntPtrBitOr
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrBitXor
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrBitAnd
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrLeftShift
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrRightShift
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    lhs: IntPtrId
    rhs: IntPtrId
    result: IntPtrId

- name: BigIntPtrNot
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: IntPtrId
    result: IntPtrId

- name: Int32NegationResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: DoubleNegationResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: BigIntNegationResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    input: BigIntId

- name: BigIntPtrNegation
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: IntPtrId
    result: IntPtrId

- name: Int32IncResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: Int32DecResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: Int32Id

- name: DoubleIncResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: DoubleDecResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: NumberId

- name: BigIntIncResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    input: BigIntId

- name: BigIntDecResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    input: BigIntId

- name: BigIntPtrInc
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: IntPtrId
    result: IntPtrId

- name: BigIntPtrDec
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: IntPtrId
    result: IntPtrId

- name: LoadInt32TruthyResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: ValId

- name: LoadDoubleTruthyResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    input: NumberId

- name: LoadStringTruthyResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    str: StringId

- name: LoadObjectTruthyResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId

- name: LoadBigIntTruthyResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    bigInt: BigIntId

- name: LoadValueTruthyResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    input: ValId

- name: LoadValueResult
  shared: false
  transpile: false
  cost_estimate: 1
  args:
    val: ValueField

- name: LoadOperandResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    input: ValId

- name: NewPlainObjectResult
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    numFixedSlots: UInt32Imm
    numDynamicSlots: UInt32Imm
    allocKind: AllocKindImm
    shape: ShapeField
    site: AllocSiteField

- name: NewArrayObjectResult
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    arrayLength: UInt32Imm
    shape: ShapeField
    site: AllocSiteField

- name: NewFunctionCloneResult
  shared: false
  transpile: false
  cost_estimate: 4
  args:
    canonical: ObjectField
    allocKind: AllocKindImm

- name: ConcatStringsResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    lhs: StringId
    rhs: StringId
    stub: JitCodeField

- name: CallStringObjectConcatResult
  shared: false
  transpile: false
  cost_estimate: 5
  args:
    lhs: ValId
    rhs: ValId

- name: CallIsSuspendedGeneratorResult
  shared: true
  transpile: false
  cost_estimate: 2
  args:
    val: ValId

- name: CompareStringResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    op: JSOpImm
    lhs: StringId
    rhs: StringId

- name: CompareObjectResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    op: JSOpImm
    lhs: ObjId
    rhs: ObjId

- name: CompareSymbolResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    op: JSOpImm
    lhs: SymbolId
    rhs: SymbolId

- name: CompareInt32Result
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    op: JSOpImm
    lhs: Int32Id
    rhs: Int32Id

- name: CompareDoubleResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    op: JSOpImm
    lhs: NumberId
    rhs: NumberId

- name: CompareBigIntResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    op: JSOpImm
    lhs: BigIntId
    rhs: BigIntId

- name: CompareBigIntInt32Result
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    op: JSOpImm
    lhs: BigIntId
    rhs: Int32Id

- name: CompareBigIntNumberResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    op: JSOpImm
    lhs: BigIntId
    rhs: NumberId

- name: CompareBigIntStringResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    op: JSOpImm
    lhs: BigIntId
    rhs: StringId

- name: CompareNullUndefinedResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    op: JSOpImm
    isUndefined: BoolImm
    input: ValId

- name: CompareDoubleSameValueResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    lhs: NumberId
    rhs: NumberId

- name: SameValueResult
  shared: false
  transpile: true
  cost_estimate: 4
  args:
    lhs: ValId
    rhs: ValId

- name: IndirectTruncateInt32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: Int32Id

- name: BigIntAsIntNResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    bits: Int32Id
    bigInt: BigIntId

- name: BigIntAsUintNResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    bits: Int32Id
    bigInt: BigIntId

- name: SetHasResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    set: ObjId
    val: ValId

- name: SetHasNonGCThingResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    set: ObjId
    val: ValId

- name: SetHasStringResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    set: ObjId
    str: StringId

- name: SetHasSymbolResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    set: ObjId
    sym: SymbolId

- name: SetHasBigIntResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    set: ObjId
    bigInt: BigIntId

- name: SetHasObjectResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    set: ObjId
    obj: ObjId

- name: SetDeleteResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    set: ObjId
    val: ValId

- name: SetAddResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    set: ObjId
    key: ValId

- name: SetSizeResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    set: ObjId

- name: MapHasResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    val: ValId

- name: MapHasNonGCThingResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    val: ValId

- name: MapHasStringResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    str: StringId

- name: MapHasSymbolResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    sym: SymbolId

- name: MapHasBigIntResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    bigInt: BigIntId

- name: MapHasObjectResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    obj: ObjId

- name: MapGetResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    val: ValId

- name: MapGetNonGCThingResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    val: ValId

- name: MapGetStringResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    str: StringId

- name: MapGetSymbolResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    sym: SymbolId

- name: MapGetBigIntResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    bigInt: BigIntId

- name: MapGetObjectResult
  shared: true
  transpile: true
  cost_estimate: 3
  args:
    map: ObjId
    obj: ObjId

- name: MapDeleteResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    val: ValId

- name: MapSetResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    map: ObjId
    key: ValId
    val: ValId

- name: MapSizeResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    map: ObjId

- name: DateFillLocalTimeSlots
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    date: ObjId

- name: DateHoursFromSecondsIntoYearResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    secondsIntoYear: ValId

- name: DateMinutesFromSecondsIntoYearResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    secondsIntoYear: ValId

- name: DateSecondsFromSecondsIntoYearResult
  shared: true
  transpile: true
  cost_estimate: 2
  args:
    secondsIntoYear: ValId

- name: ArrayFromArgumentsObjectResult
  shared: true
  transpile: true
  cost_estimate: 5
  args:
    obj: ObjId
    shape: ShapeField

- name: CloseIterScriptedResult
  shared: false
  transpile: true
  cost_estimate: 5
  args:
    iter: ObjId
    callee: ObjId
    kind: CompletionKindImm
    targetNargs: UInt32Imm

- name: CallPrintString
  shared: true
  transpile: false
  cost_estimate: 1
  args:
    str: StaticStringImm

- name: Breakpoint
  shared: true
  transpile: false
  cost_estimate: 1
  args:

- name: WrapResult
  shared: true
  transpile: false
  cost_estimate: 4
  args:

- name: Bailout
  shared: true
  transpile: true
  cost_estimate: 0
  args:

- name: AssertFloat32Result
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: ValId
    mustBeFloat32: BoolImm

- name: AssertRecoveredOnBailoutResult
  shared: true
  transpile: true
  cost_estimate: 1
  args:
    val: ValId
    mustBeRecovered: BoolImm

- name: AssertPropertyLookup
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    obj: ObjId
    id: IdField
    slot: RawInt32Field

#ifdef FUZZING_JS_FUZZILLI
- name: FuzzilliHashResult
  shared: true
  transpile: true
  cost_estimate: 4
  args:
    val: ValId
#endif
