# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

FINAL_LIBRARY = "js"

# Includes should be relative to parent path
LOCAL_INCLUDES += ["!..", ".."]

include("../js-config.mozbuild")
include("../js-cxxflags.mozbuild")

UNIFIED_SOURCES += [
    "AliasAnalysis.cpp",
    "AlignmentMaskAnalysis.cpp",
    "BacktrackingAllocator.cpp",
    "Bailouts.cpp",
    "BaselineBailouts.cpp",
    "BaselineCacheIRCompiler.cpp",
    "BaselineCodeGen.cpp",
    "BaselineDebugModeOSR.cpp",
    "BaselineFrame.cpp",
    "BaselineFrameInfo.cpp",
    "BaselineIC.cpp",
    "BaselineJIT.cpp",
    "BitSet.cpp",
    "BranchHinting.cpp",
    "BytecodeAnalysis.cpp",
    "CacheIR.cpp",
    "CacheIRAOT.cpp",
    "CacheIRCompiler.cpp",
    "CacheIRHealth.cpp",
    "CacheIRSpewer.cpp",
    "CodeGenerator.cpp",
    "CompileWrappers.cpp",
    "Disassemble.cpp",
    "DominatorTree.cpp",
    "EdgeCaseAnalysis.cpp",
    "EffectiveAddressAnalysis.cpp",
    "ExecutableAllocator.cpp",
    "FlushICache.cpp",
    "FoldLinearArithConstants.cpp",
    "InlinableNatives.cpp",
    "InstructionReordering.cpp",
    "InterpreterEntryTrampoline.cpp",
    "Ion.cpp",
    "IonAnalysis.cpp",
    "IonCacheIRCompiler.cpp",
    "IonCompileTask.cpp",
    "IonIC.cpp",
    "IonOptimizationLevels.cpp",
    "Jit.cpp",
    "JitcodeMap.cpp",
    "JitContext.cpp",
    "JitFrames.cpp",
    "JitHints.cpp",
    "JitOptions.cpp",
    "JitScript.cpp",
    "JitSpewer.cpp",
    "JSJitFrameIter.cpp",
    "JSONSpewer.cpp",
    "KnownClass.cpp",
    "Label.cpp",
    "LICM.cpp",
    "Linker.cpp",
    "LIR.cpp",
    "Lowering.cpp",
    "MacroAssembler.cpp",
    "MIR-wasm.cpp",
    "MIR.cpp",
    "MIRGraph.cpp",
    "MoveResolver.cpp",
    "PerfSpewer.cpp",
    "ProcessExecutableMemory.cpp",
    "RangeAnalysis.cpp",
    "ReciprocalMulConstants.cpp",
    "Recover.cpp",
    "RegisterAllocator.cpp",
    "RematerializedFrame.cpp",
    "SafepointIndex.cpp",
    "Safepoints.cpp",
    "ScalarReplacement.cpp",
    "shared/Assembler-shared.cpp",
    "shared/AtomicOperations-shared-jit.cpp",
    "shared/CodeGenerator-shared.cpp",
    "shared/Disassembler-shared.cpp",
    "shared/Lowering-shared.cpp",
    "ShuffleAnalysis.cpp",
    "Sink.cpp",
    "Snapshots.cpp",
    "Trampoline.cpp",
    "TrampolineNatives.cpp",
    "TrialInlining.cpp",
    "TypePolicy.cpp",
    "ValueNumbering.cpp",
    "VMFunctions.cpp",
    "WarpBuilder.cpp",
    "WarpBuilderShared.cpp",
    "WarpCacheIRTranspiler.cpp",
    "WarpOracle.cpp",
    "WarpSnapshot.cpp",
    "WasmBCE.cpp",
    "XrayJitInfo.cpp",
]

if CONFIG["JS_CODEGEN_NONE"]:
    UNIFIED_SOURCES += ["none/Trampoline-none.cpp"]
elif CONFIG["JS_CODEGEN_X86"] or CONFIG["JS_CODEGEN_X64"]:
    UNIFIED_SOURCES += [
        "x86-shared/Architecture-x86-shared.cpp",
        "x86-shared/Assembler-x86-shared.cpp",
        "x86-shared/AssemblerBuffer-x86-shared.cpp",
        "x86-shared/CodeGenerator-x86-shared.cpp",
        "x86-shared/Lowering-x86-shared.cpp",
        "x86-shared/MacroAssembler-x86-shared-SIMD.cpp",
        "x86-shared/MacroAssembler-x86-shared.cpp",
        "x86-shared/MoveEmitter-x86-shared.cpp",
    ]
    if CONFIG["JS_CODEGEN_X64"]:
        UNIFIED_SOURCES += [
            "x64/Assembler-x64.cpp",
            "x64/CodeGenerator-x64.cpp",
            "x64/Lowering-x64.cpp",
            "x64/MacroAssembler-x64.cpp",
            "x64/Trampoline-x64.cpp",
        ]
    else:
        UNIFIED_SOURCES += [
            "x86/Assembler-x86.cpp",
            "x86/CodeGenerator-x86.cpp",
            "x86/Lowering-x86.cpp",
            "x86/MacroAssembler-x86.cpp",
            "x86/Trampoline-x86.cpp",
        ]
elif CONFIG["JS_CODEGEN_ARM"]:
    UNIFIED_SOURCES += [
        "arm/Architecture-arm.cpp",
        "arm/Assembler-arm.cpp",
        "arm/CodeGenerator-arm.cpp",
        "arm/disasm/Constants-arm.cpp",
        "arm/disasm/Disasm-arm.cpp",
        "arm/Lowering-arm.cpp",
        "arm/MacroAssembler-arm.cpp",
        "arm/MoveEmitter-arm.cpp",
        "arm/Trampoline-arm.cpp",
    ]
    if CONFIG["JS_SIMULATOR_ARM"]:
        UNIFIED_SOURCES += ["arm/Simulator-arm.cpp"]
    elif CONFIG["OS_ARCH"] == "Darwin":
        SOURCES += [
            "arm/llvm-compiler-rt/arm/aeabi_idivmod.S",
            "arm/llvm-compiler-rt/arm/aeabi_uidivmod.S",
        ]
elif CONFIG["JS_CODEGEN_ARM64"]:
    UNIFIED_SOURCES += [
        "arm64/Architecture-arm64.cpp",
        "arm64/Assembler-arm64.cpp",
        "arm64/CodeGenerator-arm64.cpp",
        "arm64/Lowering-arm64.cpp",
        "arm64/MacroAssembler-arm64.cpp",
        "arm64/MoveEmitter-arm64.cpp",
        "arm64/Trampoline-arm64.cpp",
        "arm64/vixl/Assembler-vixl.cpp",
        "arm64/vixl/Cpu-Features-vixl.cpp",
        "arm64/vixl/Cpu-vixl.cpp",
        "arm64/vixl/Decoder-vixl.cpp",
        "arm64/vixl/Instructions-vixl.cpp",
        "arm64/vixl/MacroAssembler-vixl.cpp",
        "arm64/vixl/MozAssembler-vixl.cpp",
        "arm64/vixl/MozCpu-vixl.cpp",
        "arm64/vixl/MozInstructions-vixl.cpp",
        "arm64/vixl/Utils-vixl.cpp",
    ]
    vixl_werror_sources = [
        "arm64/vixl/Disasm-vixl.cpp",
        "arm64/vixl/Instrument-vixl.cpp",
    ]
    SOURCES += vixl_werror_sources
    if CONFIG["CC_TYPE"] == "clang-cl":
        for f in vixl_werror_sources:
            SOURCES[f].flags += ["-Wno-c++11-narrowing"]
    if CONFIG["JS_SIMULATOR_ARM64"]:
        UNIFIED_SOURCES += [
            "arm64/vixl/Debugger-vixl.cpp",
            "arm64/vixl/Logic-vixl.cpp",
            "arm64/vixl/MozSimulator-vixl.cpp",
            "arm64/vixl/Simulator-vixl.cpp",
        ]
elif CONFIG["JS_CODEGEN_MIPS32"] or CONFIG["JS_CODEGEN_MIPS64"]:
    UNIFIED_SOURCES += [
        "mips-shared/Architecture-mips-shared.cpp",
        "mips-shared/Assembler-mips-shared.cpp",
        "mips-shared/CodeGenerator-mips-shared.cpp",
        "mips-shared/Lowering-mips-shared.cpp",
        "mips-shared/MacroAssembler-mips-shared.cpp",
        "mips-shared/MoveEmitter-mips-shared.cpp",
    ]
    if CONFIG["JS_CODEGEN_MIPS32"]:
        UNIFIED_SOURCES += [
            "mips32/Architecture-mips32.cpp",
            "mips32/Assembler-mips32.cpp",
            "mips32/CodeGenerator-mips32.cpp",
            "mips32/Lowering-mips32.cpp",
            "mips32/MacroAssembler-mips32.cpp",
            "mips32/MoveEmitter-mips32.cpp",
            "mips32/Trampoline-mips32.cpp",
        ]
        if CONFIG["JS_SIMULATOR_MIPS32"]:
            UNIFIED_SOURCES += ["mips32/Simulator-mips32.cpp"]
    elif CONFIG["JS_CODEGEN_MIPS64"]:
        UNIFIED_SOURCES += [
            "mips64/Architecture-mips64.cpp",
            "mips64/Assembler-mips64.cpp",
            "mips64/CodeGenerator-mips64.cpp",
            "mips64/Lowering-mips64.cpp",
            "mips64/MacroAssembler-mips64.cpp",
            "mips64/MoveEmitter-mips64.cpp",
            "mips64/Trampoline-mips64.cpp",
        ]
        if CONFIG["JS_SIMULATOR_MIPS64"]:
            UNIFIED_SOURCES += ["mips64/Simulator-mips64.cpp"]
elif CONFIG["JS_CODEGEN_LOONG64"]:
    UNIFIED_SOURCES += [
        "loong64/Architecture-loong64.cpp",
        "loong64/Assembler-loong64.cpp",
        "loong64/CodeGenerator-loong64.cpp",
        "loong64/Lowering-loong64.cpp",
        "loong64/MacroAssembler-loong64.cpp",
        "loong64/MoveEmitter-loong64.cpp",
        "loong64/Trampoline-loong64.cpp",
    ]
    if CONFIG["JS_SIMULATOR_LOONG64"]:
        UNIFIED_SOURCES += ["loong64/Simulator-loong64.cpp"]
elif CONFIG["JS_CODEGEN_RISCV64"]:
    UNIFIED_SOURCES += [
        "riscv64/Architecture-riscv64.cpp",
        "riscv64/Assembler-riscv64.cpp",
        "riscv64/AssemblerMatInt.cpp",
        "riscv64/CodeGenerator-riscv64.cpp",
        "riscv64/constant/Base-constant-riscv.cpp",
        "riscv64/disasm/Disasm-riscv64.cpp",
        "riscv64/extension/base-assembler-riscv.cc",
        "riscv64/extension/base-riscv-i.cc",
        "riscv64/extension/extension-riscv-a.cc",
        "riscv64/extension/extension-riscv-c.cc",
        "riscv64/extension/extension-riscv-d.cc",
        "riscv64/extension/extension-riscv-f.cc",
        "riscv64/extension/extension-riscv-m.cc",
        "riscv64/extension/extension-riscv-v.cc",
        "riscv64/extension/extension-riscv-zicsr.cc",
        "riscv64/extension/extension-riscv-zifencei.cc",
        "riscv64/Lowering-riscv64.cpp",
        "riscv64/MacroAssembler-riscv64.cpp",
        "riscv64/MoveEmitter-riscv64.cpp",
        "riscv64/Trampoline-riscv64.cpp",
    ]
    if CONFIG["JS_SIMULATOR_RISCV64"]:
        UNIFIED_SOURCES += ["riscv64/Simulator-riscv64.cpp"]
elif CONFIG["JS_CODEGEN_WASM32"]:
    UNIFIED_SOURCES += [
        "wasm32/CodeGenerator-wasm32.cpp",
        "wasm32/MacroAssembler-wasm32.cpp",
        "wasm32/Trampoline-wasm32.cpp",
    ]

# Generate jit/ABIFunctionTypeGenerated.h from jit/ABIFunctionType.yaml
GeneratedFile(
    "ABIFunctionTypeGenerated.h",
    script="GenerateABIFunctionType.py",
    inputs=["ABIFunctionType.yaml"],
)

# Generate jit/MIROpsGenerated.h from jit/MIROps.yaml
GeneratedFile(
    "MIROpsGenerated.h",
    script="GenerateMIRFiles.py",
    entry_point="generate_mir_header",
    inputs=["MIROps.yaml"],
)

# Generate jit/LIROpsGenerated.h from jit/LIR.h, jit/shared/LIR-shared.h, and
# platform-specific LIR files.
GeneratedFile(
    "LIROpsGenerated.h",
    script="GenerateLIRFiles.py",
    entry_point="generate_lir_header",
    inputs=["LIROps.yaml", "MIROps.yaml"],
)

# Generate jit/CacheIROpsGenerated.h from jit/CacheIROps.yaml
GeneratedFile(
    "CacheIROpsGenerated.h",
    script="GenerateCacheIRFiles.py",
    entry_point="generate_cacheirops_header",
    inputs=["CacheIROps.yaml"],
)

GeneratedFile(
    "AtomicOperationsGenerated.h",
    script="GenerateAtomicOperations.py",
    entry_point="generate_atomics_header",
    inputs=[],
)

if CONFIG["ENABLE_JS_AOT_ICS"]:
    GeneratedFile(
        "CacheIRAOTGenerated.h",
        script="GenerateCacheIRFiles.py",
        entry_point="generate_aot_ics_header",
        inputs=["../ics/"],
        force=True,  # depends on list of files in js/src/ics/; always rebuild
    )

if CONFIG["FUZZING_INTERFACES"] or CONFIG["FUZZING_JS_FUZZILLI"]:
    include("/tools/fuzzing/libfuzzer-config.mozbuild")
