# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# [SMDOC] LIR Opcodes
# =======================
# This file defines all LIR opcodes as well as LIR opcode class
# definitions. It is parsed by GenerateLIRFiles.py at build time to
# create LIROpsGenerated.h. Each opcode consists of a
# name and a set of attributes that are described below. Unless
# marked as required, attributes are optional.
#
# name [required]
# ====
# Opcode name.
# Possible values:
#   - opcode string: used as the name for LIR opcode.
#
# gen_boilerplate
# ===============
# Used to decide to generate LIR boilerplate.
#   - true (default): auto generate boilerplate for this LIR opcode
#   - false: do not generate boilerplate for this LIR opcode
#
# result_type
# ===========
# Specifies the result type that is produced by this LIR instruction.
# The result type can be any of the following: WordSized, BoxedValue,
# or Int64.
#   - attribute not specified (default): there is no result produced
#     by this LIR instruction
#   - result type: sets result type for this LIR instruction
#
# operands
# ========
# A list of operands to the LIR node. Each operand will be
# passed into and set in the instruction's constructor. A simple getter
# will also be auto generated for the operand. Each operand in the
# following list is defined by its name and an type.
# The type can be WordSized, BoxedValue, or Int64.
#
#   For example:
#     operands:
#       lhs: BoxedValue
#       rhs: WordSized
#
#   Will result in:
#     explicit LInstanceOfV(const LBoxAllocation& lhs, const LAllocation& rhs)
#         : LInstructionHelper(classOpcode) {
#       setBoxOperand(lhsIndex, lhs);
#       setOperand(rhsIndex, rhs);
#     }
#     const LAllocation* rhs() { return getOperand(0); }
#
#     static const size_t lhsIndex = 0;
#     static const size_t rhsIndex = BOX_PIECES;
#
#   - attribute not specified (default): no code generated
#   - list of operand names with their types: operand getters and setters
#     are generated and passed into the constructor
#
# arguments
# =========
# A list of non-LIR node arguments to the LIR op class constructor
# that are passed along with the operands. The arguments require
# both a name and a full type signature for each item in the list.
#
# For example:
#   offset: size_t
#   type: MIRType
#
# For each argument a private variable declaration will be autogenerated
# in the LIR op class, as well as simple accessor for that variable. The
# above arguments list will result in the following declarations and
# accessors:
#
#   size_t offset_;
#   MIRType type_;
#
#   size_t offset() const { return offset_; }
#   MIRType type() const { return type_; }
#
#   - attribute not specified (default): no code generated
#   - argument list: argument names and their full type signature
#
# num_temps
# ========
# Specifies the number of temporary virtual registers, LDefinitions, used by
# this LIR op.
#   - attribute not specified (default): number of temps is set to 0
#   - number of LDefinition temps: sets number of temps max 15
#
# call_instruction
# ================
# Used to define call instructions.
#   - attribute not specified (default): no code generated
#   - true: generates a call to setIsCall in the op's constructor
#
# mir_op
# ======
# If a LIR instruction corresponds one-to-one with a particular MIR
# instruction, this will generate a method that returns that MIR
# instruction.
#   - attribute not specified (default): no code generated
#   - true: generates a method to return MIR instruction
#   - mir string: returns this specified MIR instruction
#

- name: Phi
  gen_boilerplate: false

- name: Box
  gen_boilerplate: false

- name: OsiPoint
  gen_boilerplate: false

- name: MoveGroup
  gen_boilerplate: false

# Constant 32-bit integer.
- name: Integer
  result_type: WordSized
  arguments:
    i32: int32_t

# Constant 64-bit integer.
- name: Integer64
  result_type: Int64
  arguments:
    i64: int64_t

# Constant pointer.
- name: Pointer
  result_type: WordSized
  arguments:
    gcptr: gc::Cell*

# Constant double.
- name: Double
  result_type: WordSized
  arguments:
    value: double

# Constant float32.
- name: Float32
  result_type: WordSized
  arguments:
    value: float

- name: Value
  gen_boilerplate: false

- name: NurseryObject
  result_type: WordSized
  mir_op: true

# Formal argument for a function, returning a box. Formal arguments are
# initially read from the stack.
- name: Parameter
  result_type: BoxedValue

# Stack offset for a word-sized immutable input value to a frame.
- name: Callee
  result_type: WordSized

- name: IsConstructing
  result_type: WordSized

- name: Goto
  gen_boilerplate: false

- name: NewArray
  gen_boilerplate: false

- name: NewArrayDynamicLength
  result_type: WordSized
  operands:
    length: WordSized
  num_temps: 1
  mir_op: true

- name: NewIterator
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewTypedArray
  result_type: WordSized
  num_temps: 2
  mir_op: true

- name: NewTypedArrayDynamicLength
  result_type: WordSized
  operands:
    length: WordSized
  num_temps: 1
  mir_op: true

- name: NewTypedArrayFromArray
  result_type: WordSized
  operands:
    array: WordSized
  call_instruction: true
  mir_op: true

- name: NewTypedArrayFromArrayBuffer
  result_type: WordSized
  operands:
    arrayBuffer: WordSized
    byteOffset: BoxedValue
    length: BoxedValue
  call_instruction: true
  mir_op: true

- name: BindFunction
  result_type: WordSized
  operands:
    target: WordSized
  call_instruction: true
  num_temps: 2
  mir_op: true

- name: NewBoundFunction
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewObject
  gen_boilerplate: false

- name: NewPlainObject
  result_type: WordSized
  num_temps: 3
  mir_op: true

- name: NewArrayObject
  result_type: WordSized
  num_temps: 2
  mir_op: true

# Allocates a new NamedLambdaObject.
#
# This instruction generates two possible instruction sets:
#   (1) An inline allocation of the call object is attempted.
#   (2) Otherwise, a callVM create a new object.
#
- name: NewNamedLambdaObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

# Allocates a new CallObject.
#
# This instruction generates two possible instruction sets:
#   (1) If the call object is extensible, this is a callVM to create the
#       call object.
#   (2) Otherwise, an inline allocation of the call object is attempted.
#
- name: NewCallObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewMapObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewSetObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewMapObjectFromIterable
  operands:
    iterable: BoxedValue
  result_type: WordSized
  num_temps: 2
  call_instruction: true
  mir_op: true

- name: NewSetObjectFromIterable
  operands:
    iterable: BoxedValue
  result_type: WordSized
  num_temps: 2
  call_instruction: true
  mir_op: true

- name: NewStringObject
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1
  mir_op: true

- name: InitElemGetterSetter
  operands:
    object: WordSized
    id: BoxedValue
    value: WordSized
  call_instruction: true
  mir_op: true

# Takes in an Object and a Value.
- name: MutateProto
  operands:
    object: WordSized
    value: BoxedValue
  call_instruction: true

- name: InitPropGetterSetter
  operands:
    object: WordSized
    value: WordSized
  call_instruction: true
  mir_op: true

- name: CheckOverRecursed
  mir_op: true

- name: WasmTrap
  mir_op: true

- name: WasmTrapIfNull
  operands:
    ref: WordSized
  mir_op: true

- name: WasmRefIsSubtypeOfConcrete
  mir_op: true
  operands:
    ref: WordSized
    superSTV: WordSized
  result_type: WordSized
  num_temps: 2

- name: WasmRefIsSubtypeOfAbstract
  mir_op: true
  operands:
    ref: WordSized
  result_type: WordSized
  num_temps: 1

- name: WasmRefIsSubtypeOfConcreteAndBranch
  gen_boilerplate: false

- name: WasmRefIsSubtypeOfAbstractAndBranch
  gen_boilerplate: false

- name: WasmNewStructObject
  mir_op: true
  operands:
    instance: WordSized
    typeDefData: WordSized
  result_type: WordSized
  num_temps: 2

- name: WasmNewArrayObject
  mir_op: true
  operands:
    instance: WordSized
    numElements: WordSized
    typeDefData: WordSized
  result_type: WordSized
  num_temps: 2

- name: WasmReinterpret
  gen_boilerplate: false

- name: WasmReinterpretFromI64
  gen_boilerplate: false

- name: WasmReinterpretToI64
  gen_boilerplate: false

- name: Rotate
  gen_boilerplate: false

- name: RotateI64
  gen_boilerplate: false

- name: InterruptCheck
  mir_op: true

- name: WasmStackSwitchToMain
  result_type: WordSized
  operands:
    suspender: WordSized
    fn: WordSized
    data: WordSized
  call_instruction: true

- name: WasmStackSwitchToSuspendable
  operands:
    suspender: WordSized
    fn: WordSized
    data: WordSized
  call_instruction: true

- name: WasmStackContinueOnSuspendable
  operands:
    suspender: WordSized
    result: WordSized
  call_instruction: true

- name: WasmInterruptCheck
  operands:
    instance: WordSized
  mir_op: true

- name: TypeOfV
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: TypeOf

- name: TypeOfO
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: TypeOf

- name: TypeOfName
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: TypeOfIsNonPrimitiveV
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: TypeOfIs

- name: TypeOfIsNonPrimitiveO
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: TypeOfIs

- name: TypeOfIsPrimitive
  result_type: WordSized
  operands:
    input: BoxedValue
  mir_op: TypeOfIs

- name: ToAsyncIter
  result_type: WordSized
  operands:
    iterator: WordSized
    nextMethod: BoxedValue
  call_instruction: true

- name: ToPropertyKeyCache
  result_type: BoxedValue
  operands:
    input: BoxedValue
  mir_op: true

# Allocate an object for |new| on the caller-side,
# when there is no templateObject or prototype known
- name: CreateThis
  result_type: BoxedValue
  operands:
    callee: WordSized
    newTarget: WordSized
  call_instruction: true
  mir_op: true

# Allocate a new arguments object for the frame.
- name: CreateArgumentsObject
  result_type: WordSized
  operands:
    callObject: WordSized
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: CreateInlinedArgumentsObject
  gen_boilerplate: false

- name: GetInlinedArgument
  gen_boilerplate: false

- name: GetInlinedArgumentHole
  gen_boilerplate: false

# Get argument from arguments object.
- name: GetArgumentsObjectArg
  result_type: BoxedValue
  operands:
    argsObject: WordSized
  num_temps: 1
  mir_op: true

# Set argument on arguments object.
- name: SetArgumentsObjectArg
  operands:
    argsObject: WordSized
    value: BoxedValue
  num_temps: 1
  mir_op: true

# Load an element from an arguments object.
- name: LoadArgumentsObjectArg
  result_type: BoxedValue
  operands:
    argsObject: WordSized
    index: WordSized
  num_temps: 1

# Load an element from an arguments object. Handles out-of-bounds accesses.
- name: LoadArgumentsObjectArgHole
  result_type: BoxedValue
  operands:
    argsObject: WordSized
    index: WordSized
  num_temps: 1

# Return true if the element exists in the arguments object.
- name: InArgumentsObjectArg
  result_type: WordSized
  operands:
    argsObject: WordSized
    index: WordSized
  num_temps: 1

# Return |arguments.length| unless it has been overridden.
- name: ArgumentsObjectLength
  result_type: WordSized
  operands:
    argsObject: WordSized

# Create an array from an arguments object.
- name: ArrayFromArgumentsObject
  result_type: WordSized
  operands:
    argsObject: WordSized
  call_instruction: true
  mir_op: true

# Guard that the given flags are not set on the arguments object.
- name: GuardArgumentsObjectFlags
  operands:
    argsObject: WordSized
  num_temps: 1
  mir_op: true

- name: BoundFunctionNumArgs
  result_type: WordSized
  operands:
    object: WordSized

- name: GuardBoundFunctionIsConstructor
  operands:
    object: WordSized

# If the Value is an Object, return unbox(Value).
# Otherwise, return the other Object.
- name: ReturnFromCtor
  result_type: WordSized
  operands:
    value: BoxedValue
    object: WordSized

- name: BoxNonStrictThis
  result_type: WordSized
  operands:
    value: BoxedValue
  mir_op: true

- name: ImplicitThis
  result_type: BoxedValue
  operands:
    env: WordSized
  mir_op: true

# Writes a typed argument for a function call to the frame's argument vector.
- name: StackArgT
  operands:
    arg: WordSized
  arguments:
    # Index into frame-scope argument vector.
    argslot: uint32_t
    type: MIRType

# Writes a typed argument for a function call to the frame's argument vector.
- name: StackArgV
  operands:
    value: BoxedValue
  arguments:
    # Index into frame-scope argument vector.
    argslot: uint32_t

- name: CallGeneric
  gen_boilerplate: false

- name: CallKnown
  gen_boilerplate: false

- name: CallNative
  gen_boilerplate: false

- name: CallDOMNative
  gen_boilerplate: false

- name: CallClassHook
  gen_boilerplate: false

- name: Bail

- name: Unreachable
  gen_boilerplate: false

- name: EncodeSnapshot

- name: UnreachableResultV
  gen_boilerplate: false

- name: UnreachableResultT
  result_type: WordSized

- name: GetDOMProperty
  gen_boilerplate: false

- name: GetDOMMemberV
  gen_boilerplate: false

- name: GetDOMMemberT
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: GetDOMMember

- name: SetDOMProperty
  gen_boilerplate: false

- name: LoadDOMExpandoValue
  result_type: BoxedValue
  operands:
    proxy: WordSized
  mir_op: true

- name: LoadDOMExpandoValueGuardGeneration
  result_type: BoxedValue
  operands:
    proxy: WordSized
  mir_op: true

- name: LoadDOMExpandoValueIgnoreGeneration
  result_type: BoxedValue
  operands:
    proxy: WordSized
  mir_op: true

- name: GuardDOMExpandoMissingOrGuardShape
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: true

- name: ApplyArgsGeneric
  gen_boilerplate: false

- name: ApplyArgsObj
  gen_boilerplate: false

- name: ApplyArrayGeneric
  gen_boilerplate: false

- name: ConstructArgsGeneric
  gen_boilerplate: false

- name: ConstructArrayGeneric
  gen_boilerplate: false

- name: ApplyArgsNative
  gen_boilerplate: false

- name: ApplyArgsObjNative
  gen_boilerplate: false

- name: ApplyArrayNative
  gen_boilerplate: false

- name: ConstructArgsNative
  gen_boilerplate: false

- name: ConstructArrayNative
  gen_boilerplate: false

- name: TestIAndBranch
  gen_boilerplate: false

- name: TestIPtrAndBranch
  gen_boilerplate: false

- name: TestI64AndBranch
  gen_boilerplate: false

- name: TestDAndBranch
  gen_boilerplate: false

- name: TestFAndBranch
  gen_boilerplate: false

- name: TestBIAndBranch
  gen_boilerplate: false

- name: TestOAndBranch
  gen_boilerplate: false

- name: TestVAndBranch
  gen_boilerplate: false

- name: Compare
  gen_boilerplate: false

- name: CompareI64
  gen_boilerplate: false

- name: CompareI64AndBranch
  gen_boilerplate: false

- name: CompareAndBranch
  gen_boilerplate: false

- name: CompareD
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  mir_op: Compare

- name: CompareF
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  mir_op: Compare

- name: CompareDAndBranch
  gen_boilerplate: false

- name: CompareFAndBranch
  gen_boilerplate: false

- name: CompareS
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  mir_op: Compare

- name: CompareSInline
  result_type: WordSized
  operands:
    input: WordSized
  arguments:
    constant: JSLinearString*
  mir_op: Compare

- name: CompareSSingle
  result_type: WordSized
  operands:
    input: WordSized
  arguments:
    jsop: JSOp
    constant: JSLinearString*
  num_temps: 1
  mir_op: Compare

- name: CompareBigInt
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  num_temps: 3
  mir_op: Compare

- name: CompareBigIntInt32
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  num_temps: 2
  mir_op: Compare

- name: CompareBigIntDouble
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  call_instruction: true
  mir_op: Compare

- name: CompareBigIntString
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  call_instruction: true
  mir_op: Compare

- name: CompareBigIntInt32AndBranch
  gen_boilerplate: false

- name: BitAndAndBranch
  gen_boilerplate: false

- name: BitAnd64AndBranch
  gen_boilerplate: false

# Takes a value and tests whether it is null, undefined, or is an object that
# emulates |undefined|, as determined by the JSCLASS_EMULATES_UNDEFINED class
# flag on unwrapped objects.  See also js::EmulatesUndefined.
- name: IsNullOrLikeUndefinedV
  result_type: WordSized
  operands:
    value: BoxedValue
  num_temps: 1
  mir_op: Compare

# Takes an object pointer and tests whether it is an object that emulates
# |undefined|, as above.
- name: IsNullOrLikeUndefinedT
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: Compare

# Takes a value and tests whether it is null.
- name: IsNull
  result_type: WordSized
  operands:
    value: BoxedValue
  mir_op: Compare

# Takes a value and tests whether it is undefined.
- name: IsUndefined
  result_type: WordSized
  operands:
    value: BoxedValue
  mir_op: Compare

- name: IsNullOrLikeUndefinedAndBranchV
  gen_boilerplate: false

- name: IsNullOrLikeUndefinedAndBranchT
  gen_boilerplate: false

- name: IsNullAndBranch
  gen_boilerplate: false

- name: IsUndefinedAndBranch
  gen_boilerplate: false

- name: SameValueDouble
  result_type: WordSized
  operands:
    left: WordSized
    right: WordSized
  num_temps: 1

- name: SameValue
  result_type: WordSized
  operands:
    lhs: BoxedValue
    rhs: BoxedValue

# Not operation on an integer.
- name: NotI
  result_type: WordSized
  operands:
    input: WordSized

# Not operation on an intptr.
- name: NotIPtr
  result_type: WordSized
  operands:
    input: WordSized

# Not operation on an int64.
- name: NotI64
  result_type: WordSized
  operands:
    inputI64: Int64

# Not operation on a double.
- name: NotD
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: Not

# Not operation on a float32.
- name: NotF
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: Not

# Not operation on a BigInt.
- name: NotBI
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: Not

# Boolean complement operation on an object.
- name: NotO
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: Not

# Boolean complement operation on a value.
- name: NotV
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 2
  mir_op: Not

- name: BitNotI
  gen_boilerplate: false

- name: BitNotI64
  gen_boilerplate: false

- name: BitOpI
  gen_boilerplate: false

- name: BitOpI64
  gen_boilerplate: false

- name: ShiftI
  gen_boilerplate: false

- name: ShiftI64
  gen_boilerplate: false

- name: SignExtendInt32
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: true

- name: SignExtendIntPtr
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: true

- name: SignExtendInt64
  gen_boilerplate: false

- name: UrshD
  gen_boilerplate: false

- name: Return
  gen_boilerplate: false

- name: Throw
  operands:
    value: BoxedValue
  call_instruction: true

- name: ThrowWithStack
  operands:
    value: BoxedValue
    stack: BoxedValue
  call_instruction: true

- name: MinMaxI
  gen_boilerplate: false

- name: MinMaxD
  gen_boilerplate: false

- name: MinMaxF
  gen_boilerplate: false

- name: MinMaxArrayI
  gen_boilerplate: false

- name: MinMaxArrayD
  gen_boilerplate: false

# Negative of integer
- name: NegI
  result_type: WordSized
  operands:
    num: WordSized

# Negative of an int64
- name: NegI64
  result_type: Int64
  operands:
    num: Int64

# Negative of double
- name: NegD
  result_type: WordSized
  operands:
    num: WordSized

# Negative of float32
- name: NegF
  result_type: WordSized
  operands:
    num: WordSized

# Absolute value of an integer.
- name: AbsI
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: Abs

# Absolute value of a double.
- name: AbsD
  result_type: WordSized
  operands:
    num: WordSized

# Absolute value of a float32.
- name: AbsF
  result_type: WordSized
  operands:
    num: WordSized

- name: CopySignD
  gen_boilerplate: false

- name: CopySignF
  gen_boilerplate: false

# Count leading zeroes on an int32.
- name: ClzI
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: Clz

# Count leading zeroes on an int64.
- name: ClzI64
  result_type: Int64
  operands:
    num: Int64
  mir_op: Clz

# Count trailing zeroes on an int32.
- name: CtzI
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: Ctz

# Count trailing zeroes on an int64.
- name: CtzI64
  result_type: Int64
  operands:
    num: Int64
  mir_op: Ctz

# Count population on an int32.
- name: PopcntI
  result_type: WordSized
  operands:
    num: WordSized
  num_temps: 1
  mir_op: Popcnt

# Count population on an int64.
- name: PopcntI64
  result_type: Int64
  operands:
    num: Int64
  num_temps: 1
  mir_op: Popcnt

- name: SqrtD
  result_type: WordSized
  operands:
    num: WordSized

- name: SqrtF
  result_type: WordSized
  operands:
    num: WordSized

- name: Atan2D
  gen_boilerplate: false

- name: Hypot
  gen_boilerplate: false

# Double raised to an integer power.
- name: PowI
  result_type: WordSized
  operands:
    value: WordSized
    power: WordSized
  call_instruction: true

# Integer raised to an integer power.
- name: PowII
  result_type: WordSized
  operands:
    value: WordSized
    power: WordSized
  num_temps: 2
  mir_op: Pow

# Double raised to a double power.
- name: PowD
  result_type: WordSized
  operands:
    value: WordSized
    power: WordSized
  call_instruction: true

# Constant of a power of two raised to an integer power.
- name: PowOfTwoI
  result_type: WordSized
  operands:
    power: WordSized
  arguments:
    base: uint32_t

# Sign value of an integer.
- name: SignI
  result_type: WordSized
  operands:
    num: WordSized

# Sign value of an integer.
- name: SignD
  result_type: WordSized
  operands:
    num: WordSized

# Sign value of a double with expected int32 result.
- name: SignDI
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: MathFunctionD
  gen_boilerplate: false

- name: MathFunctionF
  gen_boilerplate: false

- name: AddI
  gen_boilerplate: false

- name: AddI64
  gen_boilerplate: false

- name: SubI
  gen_boilerplate: false

- name: SubI64
  gen_boilerplate: false

- name: MulI64
  gen_boilerplate: false

- name: MathD
  gen_boilerplate: false

- name: MathF
  gen_boilerplate: false

- name: ModD
  gen_boilerplate: false

- name: ModPowTwoD
  gen_boilerplate: false

- name: WasmBuiltinModD
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
    instance: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntAdd
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntSub
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntMul
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntDiv
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntMod
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntPow
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntBitAnd
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntBitOr
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntBitXor
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntLsh
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntRsh
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntIncrement
  result_type: WordSized
  operands:
    input: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntDecrement
  result_type: WordSized
  operands:
    input: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntNegate
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1
  mir_op: true

- name: BigIntBitNot
  result_type: WordSized
  operands:
    input: WordSized
  call_instruction: true
  mir_op: true

- name: BigIntToIntPtr
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: IntPtrToBigInt
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1
  mir_op: true

- name: BigIntPtrAdd
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrSub
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrMul
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrDiv
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 2
  mir_op: true

- name: BigIntPtrDivPowTwo
  result_type: WordSized
  operands:
    lhs: WordSized
  arguments:
    shift: int32_t
    negativeDivisor: bool

- name: BigIntPtrMod
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 2
  mir_op: true

- name: BigIntPtrModPowTwo
  result_type: WordSized
  operands:
    lhs: WordSized
  arguments:
    shift: int32_t
  num_temps: 1

- name: BigIntPtrPow
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 2
  mir_op: true

- name: BigIntPtrBitAnd
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrBitOr
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrBitXor
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: true

- name: BigIntPtrLsh
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 2
  mir_op: true

- name: BigIntPtrRsh
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 2
  mir_op: true

- name: BigIntPtrBitNot
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: Int32ToStringWithBase
  result_type: WordSized
  operands:
    input: WordSized
    base: WordSized
  num_temps: 2
  mir_op: true

- name: NumberParseInt
  result_type: BoxedValue
  operands:
    string: WordSized
    radix: WordSized
  num_temps: 1
  call_instruction: true
  mir_op: true

- name: DoubleParseInt
  result_type: WordSized
  operands:
    number: WordSized
  num_temps: 1
  mir_op: true

# Adds two string, returning a string.
- name: Concat
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  num_temps: 5

# Linearize a string.
- name: LinearizeString
  result_type: WordSized
  operands:
    str: WordSized
  mir_op: true

# Linearize a string before a character access.
- name: LinearizeForCharAccess
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  mir_op: true

# Linearize a string before a code point access.
- name: LinearizeForCodePointAccess
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  num_temps: 1
  mir_op: true

# Compute the relative string index.
- name: ToRelativeStringIndex
  result_type: WordSized
  operands:
    index: WordSized
    length: WordSized
  mir_op: true

# Get uint16 character code from a string.
- name: CharCodeAt
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  num_temps: 2

# Get uint16 character code from a string. Return -1 on out-of-bounds.
- name: CharCodeAtOrNegative
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  num_temps: 2

# Get uint32 code point from a string.
- name: CodePointAt
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  num_temps: 2

# Get uint32 code point from a string. Return -1 on out-of-bounds.
- name: CodePointAtOrNegative
  result_type: WordSized
  operands:
    str: WordSized
    index: WordSized
  num_temps: 2

# Box the input if non-negative. Otherwise return NaN.
- name: NegativeToNaN
  result_type: BoxedValue
  operands:
    input: WordSized

# Box the input if non-negative. Otherwise return undefined.
- name: NegativeToUndefined
  result_type: BoxedValue
  operands:
    input: WordSized

# Convert uint16 character code to a string.
- name: FromCharCode
  result_type: WordSized
  operands:
    code: WordSized

# Convert non-negative input as a uint16 character code to a string. If the
# input is negative, return the empty string.
- name: FromCharCodeEmptyIfNegative
  result_type: WordSized
  operands:
    code: WordSized

# Convert non-negative input as a uint16 character code to a string. If the
# input is negative, return the undefined value.
- name: FromCharCodeUndefinedIfNegative
  result_type: BoxedValue
  operands:
    code: WordSized

# Convert uint32 code point to a string.
- name: FromCodePoint
  result_type: WordSized
  operands:
    codePoint: WordSized
  num_temps: 2

# Test if a string includes the search string.
- name: StringIncludes
  result_type: WordSized
  operands:
    string: WordSized
    searchString: WordSized
  call_instruction: true

# Test if a string includes the constant search string.
- name: StringIncludesSIMD
  result_type: WordSized
  operands:
    string: WordSized
  arguments:
    searchString: JSLinearString*
  num_temps: 3

# Search for the first index of the search string.
- name: StringIndexOf
  result_type: WordSized
  operands:
    string: WordSized
    searchString: WordSized
  call_instruction: true

# Search for the first index of the constant search string.
- name: StringIndexOfSIMD
  result_type: WordSized
  operands:
    string: WordSized
  arguments:
    searchString: JSLinearString*
  num_temps: 3

# Search for the last index of the search string.
- name: StringLastIndexOf
  result_type: WordSized
  operands:
    string: WordSized
    searchString: WordSized
  call_instruction: true

# Test if a string starts with the search string
- name: StringStartsWith
  result_type: WordSized
  operands:
    string: WordSized
    searchString: WordSized
  call_instruction: true

# Test if a string starts with the constant search string
- name: StringStartsWithInline
  result_type: WordSized
  operands:
    string: WordSized
  arguments:
    searchString: JSLinearString*
  num_temps: 1

# Test if a string ends with the search string
- name: StringEndsWith
  result_type: WordSized
  operands:
    string: WordSized
    searchString: WordSized
  call_instruction: true

# Test if a string ends with the constant search string
- name: StringEndsWithInline
  result_type: WordSized
  operands:
    string: WordSized
  arguments:
    searchString: JSLinearString*
  num_temps: 1

# Calls the ToLowerCase case conversion function. Inlines the case conversion
# when possible.
- name: StringToLowerCase
  result_type: WordSized
  operands:
    string: WordSized
  num_temps: 5
  mir_op: StringConvertCase

# Calls the ToLowerCase case conversion function. Inlines the case conversion
# when possible.
- name: CharCodeToLowerCase
  result_type: WordSized
  operands:
    code: WordSized
  num_temps: 1
  mir_op: CharCodeConvertCase

# Calls the ToUpperCase case conversion function.
- name: StringToUpperCase
  result_type: WordSized
  operands:
    string: WordSized
  call_instruction: true
  mir_op: StringConvertCase

# Calls the ToUpperCase case conversion function. Inlines the case conversion
# when possible.
- name: CharCodeToUpperCase
  result_type: WordSized
  operands:
    code: WordSized
  num_temps: 1
  mir_op: CharCodeConvertCase

# Index of first non-whitespace character.
- name: StringTrimStartIndex
  result_type: WordSized
  operands:
    string: WordSized

# Index of last non-whitespace character.
- name: StringTrimEndIndex
  result_type: WordSized
  operands:
    string: WordSized
    start: WordSized

- name: StringSplit
  result_type: WordSized
  operands:
    string: WordSized
    separator: WordSized
  call_instruction: true
  mir_op: true

- name: Substr
  result_type: WordSized
  operands:
    string: WordSized
    begin: WordSized
    length: WordSized
  num_temps: 3
  mir_op: true

- name: Int32ToDouble
  result_type: WordSized
  operands:
    input: WordSized

- name: Float32ToDouble
  result_type: WordSized
  operands:
    input: WordSized

- name: DoubleToFloat32
  result_type: WordSized
  operands:
    input: WordSized

- name: Int32ToFloat32
  result_type: WordSized
  operands:
    input: WordSized

- name: DoubleToFloat16
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: DoubleToFloat32ToFloat16
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 2

- name: Float32ToFloat16
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: Int32ToFloat16
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

# Convert a value to a double.
- name: ValueToDouble
  result_type: WordSized
  operands:
    input: BoxedValue
  mir_op: ToDouble

# Convert a value to a float32.
- name: ValueToFloat32
  result_type: WordSized
  operands:
    input: BoxedValue
  mir_op: ToFloat32

# Convert a value to a float16.
- name: ValueToFloat16
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: ToFloat16

- name: ValueToInt32
  gen_boilerplate: false

# Convert a value to a BigInt.
- name: ValueToBigInt
  result_type: WordSized
  operands:
    input: BoxedValue
  mir_op: ToBigInt

# Convert a double to an int32.
#   Input: floating-point Register
#   Output: 32-bit integer
#   Bailout: if the double cannot be converted to an integer.
- name: DoubleToInt32
  result_type: WordSized
  operands:
    in: WordSized
  mir_op: ToNumberInt32

# Convert a float32 to an int32.
#   Input: floating-point Register
#   Output: 32-bit integer
#   Bailout: if the float32 cannot be converted to an integer.
- name: Float32ToInt32
  result_type: WordSized
  operands:
    in: WordSized
  mir_op: ToNumberInt32

# Convert a double to a truncated int32.
#   Input: floating-point Register
#   Output: 32-bit integer
- name: TruncateDToInt32
  result_type: WordSized
  operands:
    in: WordSized
  num_temps: 1
  mir_op: TruncateToInt32

# Convert a double to a truncated int32 with instance offset because we need it
# for the slow ool path.
- name: WasmBuiltinTruncateDToInt32
  result_type: WordSized
  operands:
    in: WordSized
    instance: WordSized
  num_temps: 1
  mir_op: WasmBuiltinTruncateToInt32

# Convert a float32 to a truncated int32.
#   Input: floating-point Register
#   Output: 32-bit integer
- name: TruncateFToInt32
  result_type: WordSized
  operands:
    in: WordSized
  num_temps: 1
  mir_op: TruncateToInt32

# Convert a float32 to a truncated int32 with instance offset because we need
# it for the slow ool path.
- name: WasmBuiltinTruncateFToInt32
  result_type: WordSized
  operands:
    in: WordSized
    instance: WordSized
  num_temps: 1
  mir_op: WasmBuiltinTruncateToInt32

- name: WasmTruncateToInt32
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: WrapInt64ToInt32
  result_type: WordSized
  operands:
    input: Int64
  mir_op: true

- name: ExtendInt32ToInt64
  result_type: Int64
  operands:
    input: WordSized
  mir_op: true

# Convert a boolean value to a string.
- name: BooleanToString
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: ToString

# Convert an integer hosted on one definition to a string with a function call.
- name: IntToString
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: ToString

# Convert a double hosted on one definition to a string with a function call.
- name: DoubleToString
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1
  mir_op: ToString

# Convert a primitive to a string with a function call.
- name: ValueToString
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: ToString

- name: PowHalfD
  gen_boilerplate: false

- name: NaNToZero
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1
  mir_op: true

- name: OsrEntry
  gen_boilerplate: false

# Materialize a Value stored in an interpreter frame for OSR.
- name: OsrValue
  result_type: BoxedValue
  operands:
    entry: WordSized
  mir_op: true

# Materialize a JSObject env chain stored in an interpreter frame for OSR.
- name: OsrEnvironmentChain
  result_type: WordSized
  operands:
    entry: WordSized
  mir_op: true

# Materialize a JSObject env chain stored in an interpreter frame for OSR.
- name: OsrReturnValue
  result_type: BoxedValue
  operands:
    entry: WordSized
  mir_op: true

- name: OsrArgumentsObject
  result_type: WordSized
  operands:
    entry: WordSized
  mir_op: true

- name: RegExp
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: RegExpMatcher
  result_type: BoxedValue
  operands:
    regexp: WordSized
    string: WordSized
    lastIndex: WordSized
  call_instruction: true
  mir_op: true

- name: RegExpSearcher
  result_type: WordSized
  operands:
    regexp: WordSized
    string: WordSized
    lastIndex: WordSized
  call_instruction: true
  mir_op: true

- name: RegExpSearcherLastLimit
  result_type: WordSized
  operands:
  num_temps: 1

- name: RegExpExecMatch
  result_type: BoxedValue
  operands:
    regexp: WordSized
    string: WordSized
  call_instruction: true
  num_temps: 0
  mir_op: true

- name: RegExpExecTest
  result_type: WordSized
  operands:
    regexp: WordSized
    string: WordSized
  call_instruction: true
  num_temps: 0
  mir_op: true

- name: RegExpHasCaptureGroups
  result_type: WordSized
  operands:
    regexp: WordSized
    input: WordSized
  mir_op: true

- name: RegExpPrototypeOptimizable
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 1
  mir_op: true

- name: RegExpInstanceOptimizable
  result_type: WordSized
  operands:
    object: WordSized
    proto: WordSized
  num_temps: 1
  mir_op: true

- name: GetFirstDollarIndex
  result_type: WordSized
  operands:
    str: WordSized
  num_temps: 3

- name: StringReplace
  result_type: WordSized
  operands:
    string: WordSized
    pattern: WordSized
    replacement: WordSized
  call_instruction: true
  mir_op: true

- name: BinaryValueCache
  result_type: BoxedValue
  operands:
    lhs: BoxedValue
    rhs: BoxedValue
  # Takes two temps: these are intended to be FloatReg0 and FloatReg1
  # to allow the actual cache code to safely clobber those values without
  # save and restore.
  num_temps: 2
  mir_op: BinaryCache

- name: BinaryBoolCache
  result_type: WordSized
  operands:
    lhs: BoxedValue
    rhs: BoxedValue
  # Takes two temps: these are intendend to be FloatReg0 and FloatReg1
  # To allow the actual cache code to safely clobber those values without
  # save and restore.
  num_temps: 2
  mir_op: BinaryCache

- name: UnaryCache
  result_type: BoxedValue
  operands:
    input: BoxedValue
  mir_op_cast: true

- name: ModuleMetadata
  result_type: WordSized
  call_instruction: true
  mir_op: true

- name: DynamicImport
  result_type: WordSized
  operands:
    specifier: BoxedValue
    options: BoxedValue
  call_instruction: true
  mir_op: true

- name: Lambda
  result_type: WordSized
  operands:
    environmentChain: WordSized
  num_temps: 1
  mir_op: true

- name: FunctionWithProto
  result_type: WordSized
  operands:
    envChain: WordSized
    prototype: WordSized
  call_instruction: true
  mir_op: true

- name: SetFunName
  result_type: WordSized
  operands:
    fun: WordSized
    name: BoxedValue
  call_instruction: true
  mir_op: true

- name: KeepAliveObject
  operands:
    object: WordSized

- name: DebugEnterGCUnsafeRegion
  num_temps: 1

- name: DebugLeaveGCUnsafeRegion
  num_temps: 1

# Load the "slots" member out of a JSObject.
#   Input: JSObject pointer
#   Output: slots pointer
- name: Slots
  result_type: WordSized
  operands:
    object: WordSized

# Load the "elements" member out of a JSObject.
#   Input: JSObject pointer
#   Output: elements pointer
- name: Elements
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

# Load the initialized length from an elements header.
- name: InitializedLength
  result_type: WordSized
  operands:
    elements: WordSized

# Store to the initialized length in an elements header. Note the input is an
# *index*, one less than the desired initialized length.
- name: SetInitializedLength
  operands:
    elements: WordSized
    index: WordSized

# Load the length from an elements header.
- name: ArrayLength
  result_type: WordSized
  operands:
    elements: WordSized

# Store to the length in an elements header. Note the input is an *index*,
# one less than the desired length.
- name: SetArrayLength
  operands:
    elements: WordSized
    index: WordSized

# Load the "length" property of a function.
- name: FunctionLength
  result_type: WordSized
  operands:
    function: WordSized

# Load the "name" property of a function.
- name: FunctionName
  result_type: WordSized
  operands:
    function: WordSized

- name: GetNextEntryForIterator
  result_type: WordSized
  operands:
    iter: WordSized
    result: WordSized
  num_temps: 3
  mir_op: true

- name: ArrayBufferByteLength
  result_type: WordSized
  operands:
    object: WordSized

- name: ArrayBufferViewLength
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

# Read the byteOffset of an array buffer view.
- name: ArrayBufferViewByteOffset
  result_type: WordSized
  operands:
    object: WordSized

# Load an array buffer view's elements vector.
- name: ArrayBufferViewElements
  result_type: WordSized
  operands:
    object: WordSized

# Return the element size of a typed array.
- name: TypedArrayElementSize
  result_type: WordSized
  operands:
    object: WordSized

# Read the length of a resizable typed array.
- name: ResizableTypedArrayLength
  result_type: WordSized
  operands:
    object: WordSized
  arguments:
    synchronization: js::jit::Synchronization
  num_temps: 1

# Read the possibly out-of-bounds byteOffset of a resizable typed array.
- name: ResizableTypedArrayByteOffsetMaybeOutOfBounds
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 1

# Read the byte length of a resizable data view.
- name: ResizableDataViewByteLength
  result_type: WordSized
  operands:
    object: WordSized
  arguments:
    synchronization: js::jit::Synchronization
  num_temps: 1

# Read the byte length of a growable shared array buffer.
- name: GrowableSharedArrayBufferByteLength
  result_type: WordSized
  operands:
    object: WordSized

# Guard a resizable array buffer view is in-bounds.
- name: GuardResizableArrayBufferViewInBounds
  operands:
    object: WordSized
  num_temps: 1

# Guard a resizable array buffer view is in-bounds.
- name: GuardResizableArrayBufferViewInBoundsOrDetached
  operands:
    object: WordSized
  num_temps: 1

- name: GuardHasAttachedArrayBuffer
  operands:
    object: WordSized
  num_temps: 1

# Double to IntPtr, eligible for accessing into a TypedArray or DataView. If
# the index isn't exactly representable as an IntPtr, depending on the
# supportOOB flag on the MIR instruction, either bail out or produce an IntPtr
# which is equivalent to an OOB access.
- name: GuardNumberToIntPtrIndex
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

# Bailout if index >= length.
- name: BoundsCheck
  operands:
    index: WordSized
    length: WordSized
  mir_op: true

- name: BoundsCheckRange
  gen_boilerplate: false

# Bailout if index < minimum.
- name: BoundsCheckLower
  operands:
    index: WordSized
  mir_op: true

- name: SpectreMaskIndex
  result_type: WordSized
  operands:
    index: WordSized
    length: WordSized
  mir_op: true

- name: LoadElementV
  gen_boilerplate: false

- name: InArray
  result_type: WordSized
  operands:
    elements: WordSized
    index: WordSized
    initLength: WordSized
  mir_op: true

- name: GuardElementNotHole
  operands:
    elements: WordSized
    index: WordSized

- name: LoadElementHole
  gen_boilerplate: false

- name: StoreElementV
  gen_boilerplate: false

- name: StoreElementT
  gen_boilerplate: false

- name: StoreHoleValueElement
  operands:
    elements: WordSized
    index: WordSized

# Like LStoreElementV, but supports indexes >= initialized length.
- name: StoreElementHoleV
  operands:
    object: WordSized
    elements: WordSized
    index: WordSized
    value: BoxedValue
  num_temps: 1
  mir_op: StoreElementHole

# Like LStoreElementT, but supports indexes >= initialized length.
- name: StoreElementHoleT
  operands:
    object: WordSized
    elements: WordSized
    index: WordSized
    value: WordSized
  num_temps: 1
  mir_op: StoreElementHole

- name: ArrayPopShift
  gen_boilerplate: false

- name: ArrayPush
  result_type: WordSized
  operands:
    object: WordSized
    value: BoxedValue
  num_temps: 2
  mir_op: true

- name: ArraySlice
  result_type: WordSized
  operands:
    object: WordSized
    begin: WordSized
    end: WordSized
  num_temps: 2
  call_instruction: true
  mir_op: true

- name: ArgumentsSlice
  result_type: WordSized
  operands:
    object: WordSized
    begin: WordSized
    end: WordSized
  num_temps: 2
  call_instruction: true
  mir_op: true

- name: FrameArgumentsSlice
  result_type: WordSized
  operands:
    begin: WordSized
    count: WordSized
  num_temps: 1
  mir_op: true

- name: InlineArgumentsSlice
  gen_boilerplate: false

- name: NormalizeSliceTerm
  result_type: WordSized
  operands:
    value: WordSized
    length: WordSized
  mir_op: true

- name: ArrayJoin
  result_type: WordSized
  operands:
    array: WordSized
    separator: WordSized
  num_temps: 1
  call_instruction: true
  mir_op: true

- name: ObjectKeys
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 0
  call_instruction: true

- name: ObjectKeysLength
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 0
  call_instruction: true

- name: LoadUnboxedScalar
  result_type: WordSized
  operands:
    elements: WordSized
    index: WordSized
  num_temps: 2
  mir_op: true

- name: LoadUnboxedInt64
  gen_boilerplate: false

- name: LoadDataViewElement
  gen_boilerplate: false

- name: LoadDataViewElement64
  gen_boilerplate: false

- name: LoadTypedArrayElementHole
  result_type: BoxedValue
  operands:
    elements: WordSized
    index: WordSized
    length: WordSized
  num_temps: 1
  mir_op: true

- name: LoadTypedArrayElementHoleBigInt
  gen_boilerplate: false

- name: StoreUnboxedScalar
  operands:
    elements: WordSized
    index: WordSized
    value: WordSized
  num_temps: 1
  mir_op: true

- name: StoreUnboxedInt64
  gen_boilerplate: false

- name: StoreDataViewElement
  gen_boilerplate: false

- name: StoreDataViewElement64
  gen_boilerplate: false

- name: StoreTypedArrayElementHole
  operands:
    elements: WordSized
    length: WordSized
    index: WordSized
    value: WordSized
  num_temps: 1
  mir_op: true

- name: StoreTypedArrayElementHoleInt64
  gen_boilerplate: false

- name: AtomicIsLockFree
  result_type: WordSized
  operands:
    value: WordSized

- name: CompareExchangeTypedArrayElement
  gen_boilerplate: false

- name: AtomicExchangeTypedArrayElement
  gen_boilerplate: false

- name: AtomicTypedArrayElementBinop
  gen_boilerplate: false

- name: AtomicTypedArrayElementBinopForEffect
  gen_boilerplate: false

- name: AtomicLoad64
  gen_boilerplate: false

- name: AtomicStore64
  gen_boilerplate: false

- name: CompareExchangeTypedArrayElement64
  gen_boilerplate: false

- name: AtomicExchangeTypedArrayElement64
  gen_boilerplate: false

- name: AtomicTypedArrayElementBinop64
  gen_boilerplate: false

- name: AtomicTypedArrayElementBinopForEffect64
  gen_boilerplate: false

- name: EffectiveAddress
  result_type: WordSized
  operands:
    base: WordSized
    index: WordSized
  mir_op: true

- name: ClampIToUint8
  result_type: WordSized
  operands:
    input: WordSized

- name: ClampDToUint8
  result_type: WordSized
  operands:
    in: WordSized
  num_temps: 1

- name: ClampVToUint8
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: ClampToUint8

- name: LoadScriptedProxyHandler
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

#ifdef JS_PUNBOX64
- name: CheckScriptedProxyGetResult
  operands:
    target: BoxedValue
    id: BoxedValue
    value: BoxedValue
  num_temps: 2
  mir_op: true
#endif

- name: IdToStringOrSymbol
  result_type: BoxedValue
  operands:
    id: BoxedValue
  num_temps: 1
  mir_op: true

# Load a boxed value from an object's fixed slot.
- name: LoadFixedSlotV
  result_type: BoxedValue
  operands:
    object: WordSized
  mir_op: LoadFixedSlot

# Load a boxed value from an object's fixed slot.
# If it is a non-atom string, atomize it and update the slot.
- name: LoadFixedSlotAndAtomize
  result_type: BoxedValue
  operands:
    object: WordSized
  num_temps: 1
  mir_op: LoadFixedSlot

# Load a typed value from an object's fixed slot.
- name: LoadFixedSlotT
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: LoadFixedSlot

- name: LoadFixedSlotAndUnbox
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

- name: LoadDynamicSlotAndUnbox
  result_type: WordSized
  operands:
    slots: WordSized
  mir_op: true

- name: LoadElementAndUnbox
  result_type: WordSized
  operands:
    elements: WordSized
    index: WordSized
  mir_op: true

- name: LoadFixedSlotUnboxAndAtomize
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: LoadFixedSlotAndUnbox

- name: LoadDynamicSlotUnboxAndAtomize
  result_type: WordSized
  operands:
    slots: WordSized
  mir_op: LoadDynamicSlotAndUnbox

- name: AddAndStoreSlot
  operands:
    object: WordSized
    value: BoxedValue
  num_temps: 1
  mir_op: true

- name: AllocateAndStoreSlot
  operands:
    object: WordSized
    value: BoxedValue
  num_temps: 2
  call_instruction: true
  mir_op: true

- name: AddSlotAndCallAddPropHook
  operands:
    object: WordSized
    value: BoxedValue
  call_instruction: true
  mir_op: true

# Store a boxed value to an object's fixed slot.
- name: StoreFixedSlotV
  operands:
    obj: WordSized
    value: BoxedValue
  mir_op: StoreFixedSlot

# Store a typed value to an object's fixed slot.
- name: StoreFixedSlotT
  operands:
    obj: WordSized
    value: WordSized
  mir_op: StoreFixedSlot

# Note, Name ICs always return a Value. There are no V/T variants.
- name: GetNameCache
  result_type: BoxedValue
  operands:
    envObj: WordSized
  num_temps: 1
  mir_op: true

- name: CallGetIntrinsicValue
  result_type: BoxedValue
  call_instruction: true
  mir_op: true

- name: GetPropSuperCache
  result_type: BoxedValue
  operands:
    obj: WordSized
    receiver: BoxedValue
    id: BoxedValue
  mir_op: true

# Patchable jump to stubs generated for a GetProperty cache, which loads a
# boxed value.
- name: GetPropertyCache
  result_type: BoxedValue
  operands:
    value: BoxedValue
    id: BoxedValue
  mir_op: true

- name: BindNameCache
  result_type: WordSized
  operands:
    environmentChain: WordSized
  num_temps: 1
  mir_op: true

- name: CallBindVar
  result_type: WordSized
  operands:
    environmentChain: WordSized
  mir_op: true

# Load a value from an object's dslots or a slots vector.
- name: LoadDynamicSlotV
  result_type: BoxedValue
  operands:
    in: WordSized
  mir_op: LoadDynamicSlot

# Load a value from an object's dslots or a slots vector.
# If it is a non-atom string, atomize it and update the slot.
- name: LoadDynamicSlotAndAtomize
  result_type: BoxedValue
  operands:
    in: WordSized
  num_temps: 1
  mir_op: LoadDynamicSlot

# Store a value to an object's dslots or a slots vector.
- name: StoreDynamicSlotV
  operands:
    slots: WordSized
    value: BoxedValue
  mir_op: StoreDynamicSlot

# Store a typed value to an object's dslots or a slots vector. This has a
# few advantages over LStoreDynamicSlotV:
# 1) We can bypass storing the type tag if the slot has the same type as
#    the value.
# 2) Better Register allocation: we can store constants and FP regs directly
#    without requiring a second Register for the value.
- name: StoreDynamicSlotT
  operands:
    slots: WordSized
    value: WordSized
  mir_op: StoreDynamicSlot

# Read length field of a JSString*.
- name: StringLength
  result_type: WordSized
  operands:
    string: WordSized

# Take the floor of a double precision number and converts it to an int32.
# Implements Math.floor().
- name: Floor
  result_type: WordSized
  operands:
    num: WordSized

# Take the floor of a single precision number and converts it to an int32.
# Implements Math.floor().
- name: FloorF
  result_type: WordSized
  operands:
    num: WordSized

# Take the ceiling of a double precision number and converts it to an int32.
# Implements Math.ceil().
- name: Ceil
  result_type: WordSized
  operands:
    num: WordSized

# Take the ceiling of a single precision number and converts it to an int32.
# Implements Math.ceil().
- name: CeilF
  result_type: WordSized
  operands:
    string: WordSized

# Round a double precision number and converts it to an int32.
# Implements Math.round().
- name: Round
  result_type: WordSized
  operands:
    num: WordSized
  num_temps: 1
  mir_op: true

# Round a single precision number and converts it to an int32.
# Implements Math.round().
- name: RoundF
  result_type: WordSized
  operands:
    num: WordSized
  num_temps: 1
  mir_op: Round

# Truncates a double precision number and converts it to an int32.
# Implements Math.trunc().
- name: Trunc
  result_type: WordSized
  operands:
    num: WordSized

# Truncates a single precision number and converts it to an int32.
# Implements Math.trunc().
- name: TruncF
  result_type: WordSized
  operands:
    num: WordSized

# Rounds a double precision number accordingly to mir()->roundingMode(),
# and keeps a double output.
- name: NearbyInt
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: true

# Rounds a single precision number accordingly to mir()->roundingMode(),
# and keeps a single output.
- name: NearbyIntF
  result_type: WordSized
  operands:
    num: WordSized
  mir_op: NearbyInt

# Load a function's call environment.
- name: FunctionEnvironment
  result_type: WordSized
  operands:
    function: WordSized

- name: HomeObject
  result_type: WordSized
  operands:
    function: WordSized

- name: HomeObjectSuperBase
  result_type: BoxedValue
  operands:
    homeObject: WordSized

- name: NewLexicalEnvironmentObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewClassBodyEnvironmentObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: NewVarEnvironmentObject
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: MegamorphicSetElement
  operands:
    object: WordSized
    index: BoxedValue
    value: BoxedValue
  # On x86 we do not have enough registers to use 3 temps for this *and* take
  # five words worth of operands. Since it's 32-bit, though, we get two
  # registers from pushing `value`, which doesn't get used until the end
  # anyway. This is somewhat klunky, but oh well.
#ifdef JS_CODEGEN_X86
  num_temps: 1
#else
  num_temps: 3
#endif
  call_instruction: true
  mir_op: true

- name: CallDeleteProperty
  result_type: WordSized
  operands:
    value: BoxedValue
  call_instruction: true
  mir_op: DeleteProperty

- name: CallDeleteElement
  result_type: WordSized
  operands:
    value: BoxedValue
    index: BoxedValue
  call_instruction: true
  mir_op: DeleteElement

- name: ObjectToIterator
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 3
  mir_op: true

- name: ValueToIterator
  result_type: WordSized
  operands:
    value: BoxedValue
  call_instruction: true
  mir_op: ValueToIterator

- name: IteratorHasIndicesAndBranch
  gen_boilerplate: false

- name: LoadSlotByIteratorIndex
  result_type: BoxedValue
  operands:
    object: WordSized
    iterator: WordSized

  num_temps: 2

- name: StoreSlotByIteratorIndex
  operands:
    object: WordSized
    iterator: WordSized
    value: BoxedValue
  num_temps: 2
  mir_op: true

# Patchable jump to stubs generated for a SetProperty cache.
- name: SetPropertyCache
  operands:
    object: WordSized
    id: BoxedValue
    value: BoxedValue
  # Takes an additional temp: this is intendend to be FloatReg0 to allow the
  # actual cache code to safely clobber that value without save and restore.
  num_temps: 2
  mir_op: true

- name: GetIteratorCache
  result_type: WordSized
  operands:
    value: BoxedValue
  num_temps: 2
  mir_op: true

- name: OptimizeSpreadCallCache
  result_type: BoxedValue
  operands:
    value: BoxedValue
  num_temps: 1
  mir_op: true

- name: IteratorMore
  result_type: BoxedValue
  operands:
    iterator: WordSized
  num_temps: 1
  mir_op: true

- name: IsNoIterAndBranch
  gen_boilerplate: false

- name: IteratorEnd
  operands:
    object: WordSized
  num_temps: 3
  mir_op: true

- name: CloseIterCache
  operands:
    iter: WordSized
  num_temps: 1
  mir_op: true

- name: OptimizeGetIteratorCache
  result_type: WordSized
  operands:
    value: BoxedValue
  num_temps: 1
  mir_op: true

# Read the number of actual arguments.
- name: ArgumentsLength
  result_type: WordSized

# Load a value from the actual arguments.
- name: GetFrameArgument
  result_type: BoxedValue
  operands:
    index: WordSized

# Load a value from the actual arguments.
# Returns undefined if |index| is larger-or-equals to |length|. Bails out if
# |index| is negative.
- name: GetFrameArgumentHole
  result_type: BoxedValue
  operands:
    index: WordSized
    length: WordSized
  num_temps: 1

# Create the rest parameter.
- name: Rest
  result_type: WordSized
  operands:
    numActuals: WordSized
  num_temps: 4
  call_instruction: true
  mir_op: true

- name: Int32ToIntPtr
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: NonNegativeIntPtrToInt32
  result_type: WordSized
  operands:
    input: WordSized

- name: IntPtrToDouble
  result_type: WordSized
  operands:
    input: WordSized

- name: AdjustDataViewLength
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

# Convert a Boolean to an Int64, following ToBigInt.
- name: BooleanToInt64
  result_type: Int64
  operands:
    input: WordSized
  mir_op: ToInt64

# Convert a String to an Int64, following ToBigInt.
- name: StringToInt64
  result_type: Int64
  operands:
    input: WordSized
  mir_op: ToInt64

# Simulate ToBigInt on a Value and produce a matching Int64.
- name: ValueToInt64
  result_type: Int64
  operands:
    input: BoxedValue
  num_temps: 1
  mir_op: ToInt64

# Truncate a BigInt to an unboxed int64.
- name: TruncateBigIntToInt64
  result_type: Int64
  operands:
    input: WordSized
  mir_op: true

# Create a new BigInt* from an unboxed int64.
- name: Int64ToBigInt
  gen_boilerplate: false

# Create a new BigInt* from an unboxed uint64.
- name: Uint64ToBigInt
  result_type: WordSized
  operands:
    input: Int64
  num_temps: 1
  mir_op: Int64ToBigInt

# Return intptr from an unboxed int64. Bails if the input exceeds intptr limits.
- name: Int64ToIntPtr
  result_type: WordSized
  operands:
    input: Int64
  mir_op: true

# Return an int64 from an intptr.
- name: IntPtrToInt64
  result_type: Int64
  operands:
    input: WordSized

# Generational write barrier used when writing an object to another object.
- name: PostWriteBarrierO
  operands:
    object: WordSized
    value: WordSized
  num_temps: 1
  mir_op: PostWriteBarrier

# Generational write barrier used when writing a string to an object.
- name: PostWriteBarrierS
  operands:
    object: WordSized
    value: WordSized
  num_temps: 1
  mir_op: PostWriteBarrier

# Generational write barrier used when writing a BigInt to an object.
- name: PostWriteBarrierBI
  operands:
    object: WordSized
    value: WordSized
  num_temps: 1
  mir_op: PostWriteBarrier

# Generational write barrier used when writing a value to another object.
- name: PostWriteBarrierV
  operands:
    object: WordSized
    value: BoxedValue
  num_temps: 1
  mir_op: PostWriteBarrier

# Generational write barrier used when writing an object to another object's
# elements.
- name: PostWriteElementBarrierO
  operands:
    object: WordSized
    value: WordSized
    index: WordSized
  num_temps: 1
  mir_op: PostWriteElementBarrier

# Generational write barrier used when writing a string to an object's
# elements.
- name: PostWriteElementBarrierS
  operands:
    object: WordSized
    value: WordSized
    index: WordSized
  num_temps: 1
  mir_op: PostWriteElementBarrier

# Generational write barrier used when writing a BigInt to an object's
# elements.
- name: PostWriteElementBarrierBI
  operands:
    object: WordSized
    value: WordSized
    index: WordSized
  num_temps: 1
  mir_op: PostWriteElementBarrier

# Generational write barrier used when writing a value to another object's
# elements.
- name: PostWriteElementBarrierV
  operands:
    object: WordSized
    index: WordSized
    value: BoxedValue
  num_temps: 1
  mir_op: PostWriteElementBarrier

# Assert in debug mode that a post write barrier can be elided.
- name: AssertCanElidePostWriteBarrier
  operands:
    object: WordSized
    value: BoxedValue
  num_temps: 1

# Guard against an object's identity.
- name: GuardObjectIdentity
  operands:
    input: WordSized
    expected: WordSized
  mir_op: true

# Guard against an function's identity.
- name: GuardSpecificFunction
  operands:
    input: WordSized
    expected: WordSized

- name: GuardSpecificAtom
  operands:
    str: WordSized
  num_temps: 1
  mir_op: true

- name: GuardSpecificSymbol
  operands:
    symbol: WordSized
  mir_op: true

- name: GuardSpecificInt32
  operands:
    num: WordSized
  mir_op: true

- name: GuardStringToIndex
  result_type: WordSized
  operands:
    string: WordSized

- name: GuardStringToInt32
  result_type: WordSized
  operands:
    string: WordSized
  num_temps: 1

- name: GuardStringToDouble
  result_type: WordSized
  operands:
    string: WordSized
  num_temps: 2

- name: GuardShape
  result_type: WordSized
  operands:
    in: WordSized
  num_temps: 1
  mir_op: true

- name: GuardMultipleShapes
  result_type: WordSized
  operands:
    object: WordSized
    shapeList: WordSized
  num_temps: 4
  mir_op: true

- name: GuardProto
  operands:
    object: WordSized
    expected: WordSized
  num_temps: 1

- name: GuardNullProto
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsNativeObject
  operands:
    object: WordSized
  num_temps: 1

- name: GuardGlobalGeneration
  mir_op: true
  num_temps: 1

- name: GuardFuse
  mir_op: true
  num_temps: 1

- name: GuardIsProxy
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsNotProxy
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsNotDOMProxy
  operands:
    proxy: WordSized
  num_temps: 1

- name: ProxyGet
  result_type: BoxedValue
  operands:
    proxy: WordSized
  num_temps: 1
  call_instruction: true
  mir_op: true

- name: ProxyGetByValue
  result_type: BoxedValue
  operands:
    proxy: WordSized
    id: BoxedValue
  call_instruction: true

- name: ProxyHasProp
  result_type: BoxedValue
  operands:
    proxy: WordSized
    id: BoxedValue
  call_instruction: true
  mir_op: true

- name: ProxySet
  operands:
    proxy: WordSized
    rhs: BoxedValue
  num_temps: 1
  call_instruction: true
  mir_op: true

- name: ProxySetByValue
  operands:
    proxy: WordSized
    id: BoxedValue
    rhs: BoxedValue
  call_instruction: true
  mir_op: true

- name: CallSetArrayLength
  operands:
    obj: WordSized
    rhs: BoxedValue
  call_instruction: true
  mir_op: true

- name: MegamorphicLoadSlot
  result_type: BoxedValue
  operands:
    object: WordSized
  num_temps: 4
  call_instruction: true
  mir_op: true

- name: MegamorphicLoadSlotByValue
  result_type: BoxedValue
  operands:
    object: WordSized
    id: BoxedValue
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: MegamorphicLoadSlotPermissive
  result_type: BoxedValue
  operands:
    object: WordSized
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: MegamorphicLoadSlotByValuePermissive
  result_type: BoxedValue
  operands:
    object: WordSized
    id: BoxedValue
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: MegamorphicStoreSlot
  operands:
    object: WordSized
    rhs: BoxedValue
#ifdef JS_CODEGEN_X86
  num_temps: 1
#else
  num_temps: 3
#endif
  call_instruction: true
  mir_op: true

- name: MegamorphicHasProp
  result_type: WordSized
  operands:
    object: WordSized
    id: BoxedValue
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: SmallObjectVariableKeyHasProp
  result_type: WordSized
  operands:
    id: WordSized
  num_temps: 0
  mir_op: true

- name: GuardIsNotArrayBufferMaybeShared
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsTypedArray
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsFixedLengthTypedArray
  operands:
    object: WordSized
  num_temps: 1

- name: GuardIsResizableTypedArray
  operands:
    object: WordSized
  num_temps: 1

- name: GuardHasProxyHandler
  operands:
    object: WordSized
  mir_op: true

- name: GuardNoDenseElements
  operands:
    in: WordSized
  num_temps: 1

- name: InCache
  result_type: WordSized
  operands:
    lhs: BoxedValue
    rhs: WordSized
  num_temps: 1
  mir_op: true

- name: HasOwnCache
  result_type: WordSized
  operands:
    value: BoxedValue
    id: BoxedValue
  mir_op: true

- name: CheckPrivateFieldCache
  result_type: WordSized
  operands:
    value: BoxedValue
    id: BoxedValue
  mir_op: true

- name: NewPrivateName
  result_type: WordSized
  call_instruction: true
  mir_op: true

- name: InstanceOfO
  result_type: WordSized
  operands:
    lhs: WordSized
    rhs: WordSized
  mir_op: InstanceOf

- name: InstanceOfV
  result_type: WordSized
  operands:
    lhs: BoxedValue
    rhs: WordSized
  mir_op: InstanceOf

- name: InstanceOfCache
  gen_boilerplate: false

- name: IsCallableO
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: IsCallable

- name: IsCallableV
  result_type: WordSized
  operands:
    object: BoxedValue
  num_temps: 1
  mir_op: IsCallable

- name: IsConstructor
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

- name: IsCrossRealmArrayConstructor
  result_type: WordSized
  operands:
    object: WordSized

- name: IsArrayO
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: IsArray

- name: IsArrayV
  result_type: WordSized
  operands:
    value: BoxedValue
  num_temps: 1
  mir_op: IsArray

- name: IsTypedArray
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

- name: IsObject
  result_type: WordSized
  operands:
    object: BoxedValue
  mir_op: true

- name: IsObjectAndBranch
  gen_boilerplate: false

- name: IsNullOrUndefined
  result_type: WordSized
  operands:
    input: BoxedValue
  mir_op: true

- name: IsNullOrUndefinedAndBranch
  gen_boilerplate: false

- name: HasClass
  result_type: WordSized
  operands:
    lhs: WordSized
  mir_op: true

- name: GuardToClass
  result_type: WordSized
  operands:
    lhs: WordSized
  num_temps: 1
  mir_op: true

- name: GuardToEitherClass
  result_type: WordSized
  operands:
    lhs: WordSized
  num_temps: 1
  mir_op: true

- name: GuardToFunction
  result_type: WordSized
  operands:
    lhs: WordSized
  num_temps: 1
  mir_op: true

- name: ObjectClassToString
  result_type: WordSized
  operands:
    lhs: WordSized
  num_temps: 1
  call_instruction: true
  mir_op: true

- name: WasmSelect
  gen_boilerplate: false

- name: WasmSelectI64
  gen_boilerplate: false

- name: WasmCompareAndSelect
  gen_boilerplate: false

- name: WasmAddOffset
  result_type: WordSized
  operands:
    base: WordSized
  mir_op: true

- name: WasmAddOffset64
  result_type: Int64
  operands:
    base: Int64
  mir_op: WasmAddOffset

- name: WasmBoundsCheck
  result_type: WordSized
  operands:
    ptr: WordSized
    boundsCheckLimit: WordSized
  mir_op: true

- name: WasmBoundsCheck64
  gen_boilerplate: false

- name: WasmBoundsCheckRange32
  result_type: WordSized
  operands:
    index: WordSized
    length: WordSized
    limit: WordSized
  mir_op: true
  num_temps: 1

- name: WasmExtendU32Index
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: WasmWrapU32Index
  result_type: WordSized
  operands:
    input: WordSized
  mir_op: true

- name: WasmClampTable64Address
  result_type: WordSized
  operands:
    address: Int64
  mir_op: true

- name: WasmAlignmentCheck
  operands:
    ptr: WordSized
  mir_op: true

- name: WasmAlignmentCheck64
  operands:
    ptr: Int64
  mir_op: WasmAlignmentCheck

- name: WasmLoadInstance
  result_type: WordSized
  operands:
    instance: WordSized
  mir_op: true

- name: WasmLoadInstance64
  result_type: Int64
  operands:
    instance: WordSized
  mir_op: WasmLoadInstance

- name: WasmHeapReg
  result_type: WordSized
  mir_op: true

- name: WasmLoad
  gen_boilerplate: false

- name: WasmLoadI64
  gen_boilerplate: false

- name: WasmStore
  gen_boilerplate: false

- name: WasmStoreI64
  gen_boilerplate: false

- name: AsmJSLoadHeap
  result_type: WordSized
  operands:
    ptr: WordSized
    boundsCheckLimit: WordSized
    memoryBase: WordSized
  mir_op: true

- name: AsmJSStoreHeap
  result_type: WordSized
  operands:
    ptr: WordSized
    value: WordSized
    boundsCheckLimit: WordSized
    memoryBase: WordSized
  mir_op: true

- name: WasmCompareExchangeHeap
  gen_boilerplate: false

- name: WasmFence

- name: WasmAtomicExchangeHeap
  gen_boilerplate: false

- name: WasmAtomicBinopHeap
  gen_boilerplate: false

- name: WasmAtomicBinopHeapForEffect
  gen_boilerplate: false

- name: WasmLoadSlot
  result_type: WordSized
  operands:
    containerRef: WordSized
  arguments:
    offset: size_t
    type: MIRType
    wideningOp: MWideningOp
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmLoadElement
  result_type: WordSized
  operands:
    base: WordSized
    index: WordSized
  arguments:
    type: MIRType
    wideningOp: MWideningOp
    scale: Scale
    maybeTrap: wasm::MaybeTrapSiteDesc
  num_temps: 1

- name: WasmLoadSlotI64
  result_type: Int64
  operands:
    containerRef: WordSized
  arguments:
    offset: size_t
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmLoadElementI64
  result_type: Int64
  operands:
    base: WordSized
    index: WordSized
  arguments:
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmStoreSlot
  operands:
    value: WordSized
    containerRef: WordSized
  arguments:
    offset: size_t
    type: MIRType
    narrowingOp: MNarrowingOp
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmStoreSlotI64
  operands:
    value: Int64
    containerRef: WordSized
  arguments:
    offset: size_t
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmStoreElement
  operands:
    base: WordSized
    index: WordSized
    value: WordSized
  arguments:
    type: MIRType
    narrowingOp: MNarrowingOp
    scale: Scale
    maybeTrap: wasm::MaybeTrapSiteDesc
  num_temps: 1

- name: WasmStoreElementI64
  operands:
    base: WordSized
    index: WordSized
    value: Int64
  arguments:
    maybeTrap: wasm::MaybeTrapSiteDesc

- name: WasmStoreElementRef
  operands:
    instance: WordSized
    base: WordSized
    index: WordSized
    value: WordSized
  arguments:
    maybeTrap: wasm::MaybeTrapSiteDesc
    preBarrierKind: WasmPreBarrierKind
  num_temps: 2

- name: WasmLoadTableElement
  result_type: WordSized
  operands:
    elements: WordSized
    index: WordSized

- name: WasmDerivedPointer
  gen_boilerplate: false

- name: WasmDerivedIndexPointer
  gen_boilerplate: false

- name: WasmStoreRef
  operands:
    instance: WordSized
    valueBase: WordSized
    value: WordSized
  arguments:
    offset: uint32_t
    maybeTrap: wasm::MaybeTrapSiteDesc
    preBarrierKind: WasmPreBarrierKind
  num_temps: 1
  mir_op: true

# Generational write barrier used when writing an object to another object.
- name: WasmPostWriteBarrierImmediate
  operands:
    instance: WordSized
    object: WordSized
    valueBase: WordSized
    value: WordSized
  arguments:
    valueOffset: uint32_t
  num_temps: 1
  mir_op: true

# Ditto, but with a scaled index instead of a constant offset.
- name: WasmPostWriteBarrierIndex
  operands:
    instance: WordSized
    object: WordSized
    valueBase: WordSized
    index: WordSized
    value: WordSized
  arguments:
    elemSize: uint32_t
  num_temps: 1
  mir_op: true

- name: WasmParameter
  result_type: WordSized

- name: WasmParameterI64
  gen_boilerplate: false

- name: WasmReturn
  operands:
    rval: WordSized
    instance: WordSized

- name: WasmReturnI64
  operands:
    rval: Int64
    instance: WordSized

- name: WasmReturnVoid
  operands:
    rval: WordSized

- name: WasmStackArg
  operands:
    arg: WordSized
  mir_op: true

- name: WasmStackArgI64
  operands:
    arg: Int64
  mir_op: WasmStackArg

- name: WasmNullConstant
  result_type: WordSized

- name: WasmCallIndirectAdjunctSafepoint
  gen_boilerplate: false

- name: WasmCall
  gen_boilerplate: false

- name: WasmCallLandingPrePad
  mir_op: true

- name: WasmRegisterResult
  gen_boilerplate: false

- name: WasmRegisterPairResult
  gen_boilerplate: false

- name: WasmStackResultArea
  result_type: WordSized
  num_temps: 1
  mir_op: true

- name: WasmStackResult
  gen_boilerplate: false

- name: WasmStackResult64
  gen_boilerplate: false

- name: AssertRangeI
  gen_boilerplate: false

- name: AssertRangeD
  gen_boilerplate: false

- name: AssertRangeF
  gen_boilerplate: false

- name: AssertRangeV
  gen_boilerplate: false

- name: AssertClass
  operands:
    input: WordSized
  num_temps: 1
  mir_op: true

- name: AssertShape
  operands:
    input: WordSized
  mir_op: true

- name: GuardValue
  operands:
    input: BoxedValue
  mir_op: true

- name: GuardNullOrUndefined
  operands:
    input: BoxedValue
  mir_op: true

- name: GuardIsNotObject
  operands:
    input: BoxedValue
  mir_op: true

- name: GuardFunctionFlags
  operands:
    function: WordSized
  mir_op: true

- name: GuardFunctionIsNonBuiltinCtor
  operands:
    function: WordSized
  num_temps: 1

- name: GuardFunctionKind
  operands:
    function: WordSized
  num_temps: 1
  mir_op: true

- name: GuardFunctionScript
  operands:
    function: WordSized
  mir_op: true

- name: IncrementWarmUpCounter
  num_temps: 1
  mir_op: true

- name: LexicalCheck
  operands:
    input: BoxedValue
  mir_op: true

- name: ThrowRuntimeLexicalError
  call_instruction: true
  mir_op: true

- name: ThrowMsg
  call_instruction: true
  mir_op: true

- name: GlobalDeclInstantiation
  mir_op: true

- name: MemoryBarrier
  gen_boilerplate: false

- name: Debugger
  num_temps: 1
  call_instruction: true

- name: NewTarget
  result_type: BoxedValue

- name: Random
  gen_boilerplate: false

- name: CheckReturn
  result_type: BoxedValue
  operands:
    returnValue: BoxedValue
    thisValue: BoxedValue

- name: CheckIsObj
  result_type: WordSized
  operands:
    value: BoxedValue
  mir_op: true

- name: CheckObjCoercible
  operands:
    value: BoxedValue

- name: CheckClassHeritage
  operands:
    heritage: BoxedValue
  num_temps: 2

- name: CheckThis
  operands:
    value: BoxedValue

- name: CheckThisReinit
  operands:
    thisValue: BoxedValue

- name: Generator
  result_type: WordSized
  operands:
    callee: WordSized
    environmentChain: WordSized
    argsObject: WordSized
  call_instruction: true
  mir_op: true

- name: AsyncResolve
  result_type: WordSized
  operands:
    generator: WordSized
    value: BoxedValue
  call_instruction: true
  mir_op: true

- name: AsyncReject
  result_type: WordSized
  operands:
    generator: WordSized
    reason: BoxedValue
    stack: BoxedValue
  call_instruction: true
  mir_op: true

- name: AsyncAwait
  result_type: WordSized
  operands:
    value: BoxedValue
    generator: WordSized
  call_instruction: true
  mir_op: true

- name: CanSkipAwait
  result_type: WordSized
  operands:
    value: BoxedValue
  call_instruction: true
  mir_op: true

- name: MaybeExtractAwaitValue
  result_type: BoxedValue
  operands:
    value: BoxedValue
    canSkip: WordSized
  call_instruction: true
  mir_op: true

- name: DebugCheckSelfHosted
  operands:
    value: BoxedValue
  call_instruction: true

- name: IsPackedArray
  result_type: WordSized
  operands:
    object: WordSized
  num_temps: 1

- name: GuardArrayIsPacked
  operands:
    array: WordSized
  num_temps: 2
  mir_op: true

- name: GetPrototypeOf
  result_type: BoxedValue
  operands:
    target: WordSized

- name: ObjectWithProto
  result_type: WordSized
  operands:
    prototype: BoxedValue
  call_instruction: true

- name: ObjectStaticProto
  result_type: WordSized
  operands:
    object: WordSized

- name: BuiltinObject
  result_type: WordSized
  call_instruction: true
  mir_op: true

- name: SuperFunction
  result_type: BoxedValue
  operands:
    callee: WordSized
  num_temps: 1

- name: InitHomeObject
  result_type: WordSized
  operands:
    function: WordSized
    homeObject: BoxedValue

- name: IsTypedArrayConstructor
  result_type: WordSized
  operands:
    object: WordSized

- name: LoadValueTag
  result_type: WordSized
  operands:
    value: BoxedValue

- name: GuardTagNotEqual
  operands:
    lhs: WordSized
    rhs: WordSized

- name: LoadWrapperTarget
  result_type: WordSized
  operands:
    object: WordSized
  mir_op: true

- name: GuardHasGetterSetter
  operands:
    object: WordSized
  num_temps: 3
  call_instruction: true
  mir_op: true

- name: GuardIsExtensible
  operands:
    object: WordSized
  num_temps: 1

- name: GuardInt32IsNonNegative
  operands:
    index: WordSized

- name: GuardInt32Range
  operands:
    input: WordSized
  mir_op: true

- name: GuardIndexIsNotDenseElement
  operands:
    object: WordSized
    index: WordSized
  num_temps: 2

- name: GuardIndexIsValidUpdateOrAdd
  operands:
    object: WordSized
    index: WordSized
  num_temps: 2

- name: CallAddOrUpdateSparseElement
  operands:
    object: WordSized
    index: WordSized
    value: BoxedValue
  call_instruction: true
  mir_op: true

- name: CallGetSparseElement
  result_type: BoxedValue
  operands:
    object: WordSized
    index: WordSized
  call_instruction: true

- name: CallNativeGetElement
  result_type: BoxedValue
  operands:
    object: WordSized
    index: WordSized
  call_instruction: true

- name: CallNativeGetElementSuper
  result_type: BoxedValue
  operands:
    object: WordSized
    index: WordSized
    receiver: BoxedValue
  call_instruction: true

- name: CallObjectHasSparseElement
  result_type: WordSized
  operands:
    object: WordSized
    index: WordSized
  num_temps: 2
  call_instruction: true

- name: BigIntAsIntN
  result_type: WordSized
  operands:
    bits: WordSized
    input: WordSized
  call_instruction: true

- name: GuardNonGCThing
  operands:
    input: BoxedValue

- name: ToHashableNonGCThing
  result_type: BoxedValue
  operands:
    input: BoxedValue
  num_temps: 1

- name: ToHashableString
  result_type: WordSized
  operands:
    input: WordSized

- name: ToHashableValue
  result_type: BoxedValue
  operands:
    input: BoxedValue
  num_temps: 1

- name: HashNonGCThing
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1

- name: HashString
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: HashSymbol
  result_type: WordSized
  operands:
    input: WordSized

- name: HashBigInt
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 3

- name: HashObject
  result_type: WordSized
  operands:
    setObject: WordSized
    input: BoxedValue
  num_temps: 4

- name: HashValue
  result_type: WordSized
  operands:
    setObject: WordSized
    input: BoxedValue
  num_temps: 4

- name: SetObjectHasNonBigInt
  result_type: WordSized
  operands:
    setObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 2

- name: SetObjectHasBigInt
  result_type: WordSized
  operands:
    setObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 4

- name: SetObjectHasValue
  result_type: WordSized
  operands:
    setObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 4

- name: SetObjectHasValueVMCall
  result_type: WordSized
  call_instruction: true
  operands:
    setObject: WordSized
    input: BoxedValue

- name: SetObjectSize
  result_type: WordSized
  operands:
    setObject: WordSized

- name: MapObjectHasNonBigInt
  result_type: WordSized
  operands:
    mapObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 2

- name: MapObjectHasBigInt
  result_type: WordSized
  operands:
    mapObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 4

- name: MapObjectHasValue
  result_type: WordSized
  operands:
    mapObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 4

- name: MapObjectHasValueVMCall
  result_type: WordSized
  call_instruction: true
  operands:
    mapObject: WordSized
    input: BoxedValue

- name: MapObjectGetNonBigInt
  result_type: BoxedValue
  operands:
    mapObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 2

- name: MapObjectGetBigInt
  result_type: BoxedValue
  operands:
    mapObject: WordSized
    input: BoxedValue
    hash: WordSized
  num_temps: 4

- name: MapObjectGetValueVMCall
  result_type: BoxedValue
  call_instruction: true
  operands:
    mapObject: WordSized
    input: BoxedValue

- name: MapObjectSize
  result_type: WordSized
  operands:
    mapObject: WordSized

- name: BigIntAsUintN
  result_type: WordSized
  operands:
    bits: WordSized
    input: WordSized
  call_instruction: true

- name: IonToWasmCall
  gen_boilerplate: false

- name: IonToWasmCallV
  gen_boilerplate: false

- name: IonToWasmCallI64
  gen_boilerplate: false

- name: WasmAnyRefFromJSValue
  result_type: WordSized
  operands:
    input: BoxedValue
  num_temps: 1

- name: WasmAnyRefFromJSObject
  result_type: WordSized
  operands:
    input: WordSized

- name: WasmAnyRefFromJSString
  result_type: WordSized
  operands:
    input: WordSized

- name: WasmAnyRefIsJSString
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: WasmTrapIfAnyRefIsNotJSString
  mir_op: true
  operands:
    input: WordSized
  num_temps: 1

- name: WasmAnyRefJSStringLength
  mir_op: true
  result_type: WordSized
  operands:
    input: WordSized
  num_temps: 1

- name: WasmNewI31Ref
  mir_op: true
  result_type: WordSized
  operands:
    value: WordSized

- name: WasmI31RefGet
  mir_op: true
  result_type: WordSized
  operands:
    value: WordSized

# Constant Simd128
- name: Simd128
  result_type: WordSized
  arguments:
    simd128: SimdConstant

- name: WasmTernarySimd128
  gen_boilerplate: false

- name: WasmBinarySimd128
  gen_boilerplate: false

- name: WasmBinarySimd128WithConstant
  gen_boilerplate: false

- name: WasmVariableShiftSimd128
  gen_boilerplate: false

- name: WasmConstantShiftSimd128
  gen_boilerplate: false

- name: WasmSignReplicationSimd128
  gen_boilerplate: false

- name: WasmShuffleSimd128
  gen_boilerplate: false

- name: WasmPermuteSimd128
  gen_boilerplate: false

- name: WasmReplaceLaneSimd128
  gen_boilerplate: false

- name: WasmReplaceInt64LaneSimd128
  gen_boilerplate: false

- name: WasmScalarToSimd128
  gen_boilerplate: false

- name: WasmInt64ToSimd128
  gen_boilerplate: false

- name: WasmUnarySimd128
  gen_boilerplate: false

- name: WasmReduceSimd128
  gen_boilerplate: false

- name: WasmReduceAndBranchSimd128
  gen_boilerplate: false

- name: WasmReduceSimd128ToInt64
  gen_boilerplate: false

- name: WasmLoadLaneSimd128
  gen_boilerplate: false

- name: WasmStoreLaneSimd128
  gen_boilerplate: false

- name: Unbox
  gen_boilerplate: false

- name: UnboxFloatingPoint
  gen_boilerplate: false

- name: WasmUint32ToDouble
  gen_boilerplate: false

- name: WasmUint32ToFloat32
  gen_boilerplate: false

- name: DivI
  gen_boilerplate: false

- name: ModI
  gen_boilerplate: false

- name: DivPowTwoI
  gen_boilerplate: false

- name: ModPowTwoI
  gen_boilerplate: false

- name: TableSwitch
  gen_boilerplate: false

- name: TableSwitchV
  gen_boilerplate: false

- name: MulI
  gen_boilerplate: false

#ifdef JS_CODEGEN_X86
- name: BoxFloatingPoint
  gen_boilerplate: false

- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: DivOrModConstantI
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: UDivOrModConstant
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false

- name: WasmAtomicLoadI64
  gen_boilerplate: false

- name: WasmAtomicStoreI64
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_X64
- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: DivOrModConstantI
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: UDivOrModConstant
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_ARM
- name: BoxFloatingPoint
  gen_boilerplate: false

- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: SoftDivI
  gen_boilerplate: false

- name: SoftModI
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: UDiv
  gen_boilerplate: false

- name: UMod
  gen_boilerplate: false

- name: SoftUDivOrMod
  gen_boilerplate: false

- name: Int64ToFloatingPointCall
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: WasmAtomicLoadI64
  gen_boilerplate: false

- name: WasmAtomicStoreI64
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_ARM64
- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: DivConstantI
  gen_boilerplate: false

- name: UDivConstantI
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: UDiv
  gen_boilerplate: false

- name: UMod
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_MIPS32
- name: BoxFloatingPoint
  gen_boilerplate: false

- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false

- name: WasmUnalignedLoad
  gen_boilerplate: false

- name: WasmUnalignedLoadI64
  gen_boilerplate: false

- name: WasmUnalignedStore
  gen_boilerplate: false

- name: WasmUnalignedStoreI64
  gen_boilerplate: false

- name: WasmAtomicLoadI64
  gen_boilerplate: false

- name: WasmAtomicStoreI64
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_MIPS64
- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false

- name: WasmUnalignedLoad
  gen_boilerplate: false

- name: WasmUnalignedLoadI64
  gen_boilerplate: false

- name: WasmUnalignedStore
  gen_boilerplate: false

- name: WasmUnalignedStoreI64
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_LOONG64
- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef JS_CODEGEN_RISCV64
- name: DivOrModI64
  gen_boilerplate: false

- name: UDivOrMod
  gen_boilerplate: false

- name: UDivOrModI64
  gen_boilerplate: false

- name: ModMaskI
  gen_boilerplate: false

- name: WasmTruncateToInt64
  gen_boilerplate: false

- name: Int64ToFloatingPoint
  gen_boilerplate: false

- name: WasmCompareExchangeI64
  gen_boilerplate: false

- name: WasmAtomicBinopI64
  gen_boilerplate: false

- name: WasmAtomicExchangeI64
  gen_boilerplate: false
#endif

#ifdef FUZZING_JS_FUZZILLI
- name: FuzzilliHashT
  gen_boilerplate: false

- name: FuzzilliHashV
  gen_boilerplate: false

- name: FuzzilliHashStore
  gen_boilerplate: false
#endif

#ifdef ENABLE_EXPLICIT_RESOURCE_MANAGEMENT
- name: AddDisposableResource
  operands:
    environment: WordSized
    resource: BoxedValue
    method: BoxedValue
    needsClosure: WordSized
  arguments:
    hint: uint8_t
  call_instruction: true

- name: TakeDisposeCapability
  operands:
    environment: WordSized
  result_type: BoxedValue

- name: CreateSuppressedError
  operands:
    error: BoxedValue
    suppressed: BoxedValue
  result_type: WordSized
  call_instruction: true
#endif
