# The definitions in here are processed by GenerateABIFunctionType.py to
# generate the ABIFunctionType enum and simulator code for handling these
# function types.
#
# The name field is optional and will be autogenerated if omitted. New
# additions should generally use the autogenerated name.

# VM functions that take 0-9 non-double arguments
# and return a non-double value.
- name: General0
  ret: General
  args: []
- name: General1
  ret: General
  args: [General]
- name: General2
  ret: General
  args: [General, General]
- name: General3
  ret: General
  args: [General, General, General]
- name: General4
  ret: General
  args: [General, General, General, General]
- name: General5
  ret: General
  args: [General, General, General, General, General]
- name: General6
  ret: General
  args: [General, General, General, General, General, General]
- name: General7
  ret: General
  args: [General, General, General, General, General, General, General]
- name: General8
  ret: General
  args: [General, General, General, General, General, General, General, General]

# int64 f(double)
- name: Int64_Double
  ret: Int64
  args: [Float64]

# double f()
- name: Double_None
  ret: Float64
  args: []

# int f(double)
- name: Int_Double
  ret: General
  args: [Float64]

# int f(float32)
- name: Int_Float32
  ret: General
  args: [Float32]

# int32_t f(float32)
- ret: Int32
  args: [Float32]

# float f(float)
- ret: Float32
  args: [Float32]

# float f(double)
- ret: Float32
  args: [Float64]

# float f(int)
- ret: Float32
  args: [General]

# float f(int32_t)
- ret: Float32
  args: [Int32]

# float f(int, int)
- name: Float32_IntInt
  ret: Float32
  args: [General, General]

# double f(double)
- name: Double_Double
  ret: Float64
  args: [Float64]

# double f(int)
- name: Double_Int
  ret: Float64
  args: [General]

# double f(int, int)
- name: Double_IntInt
  ret: Float64
  args: [General, General]

# double f(double, int)
- name: Double_DoubleInt
  ret: Float64
  args: [Float64, General]

# double f(double, double)
- name: Double_DoubleDouble
  ret: Float64
  args: [Float64, Float64]

# float f(float, float)
- ret: Float32
  args: [Float32, Float32]

# double f(int, double)
- name: Double_IntDouble
  ret: Float64
  args: [General, Float64]

# int f(int, double)
- name: Int_IntDouble
  ret: General
  args: [General, Float64]

# int f(double, int)
- name: Int_DoubleInt
  ret: General
  args: [Float64, General]

# double f(double, double, double)
- name: Double_DoubleDoubleDouble
  ret: Float64
  args: [Float64, Float64, Float64]

# double f(double, double, double, double)
- name: Double_DoubleDoubleDoubleDouble
  ret: Float64
  args: [Float64, Float64, Float64, Float64]

# int f(double, int, int)
- name: Int_DoubleIntInt
  ret: General
  args: [Float64, General, General]

# int f(int, double, int, int)
- name: Int_IntDoubleIntInt
  ret: General
  args: [General, Float64, General, General]

- name: Int_GeneralGeneralGeneralInt64
  ret: General
  args: [General, General, General, Int64]

- name: Int_GeneralGeneralInt64Int64
  ret: General
  args: [General, General, Int64, Int64]

# int32_t f(...) variants
- ret: General
  args: [General, Int32]

- ret: General
  args: [General, Int32, General]

- ret: General
  args: [General, Int32, Int32]

- ret: General
  args: [General, Int32, Int32, General, Int32]

- name: General_GeneralGeneralInt32Int32
  ret: General
  args: [General, General, Int32, Int32]

# int32_t f(...) variants
- ret: Int32
  args: [General]

- ret: Int32
  args: [General, General]

- ret: Int32
  args: [General, General, General]

- ret: Int32
  args: [General, General, General, General]

- ret: Int32
  args: [General, General, General, Int32]

- ret: Int32
  args: [General, General, Int32]

- ret: Int32
  args: [General, General, Int32, General]

- ret: Int32
  args: [General, General, Int32, General, Int32, Int32, Int32]

- ret: Int32
  args: [General, General, Int32, Int32]

- ret: Int32
  args: [General, General, Int32, Int32, Int32, Int32]

- ret: Int32
  args: [General, General, Int32, Int32, Int32, General, Int32]

- ret: Int32
  args: [General, Int32]

- ret: Int32
  args: [General, Int32, Float32, Float32, Float32, Float32, Int32, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Float32, Float32, Int32, Float32, Float32, Int32, Float32, Int32, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Float32, Float32, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, General, Int32]

- ret: Int32
  args: [General, Int32, General, Int32, Int32]

- ret: Int32
  args: [General, Int32, Int32]

- ret: Int32
  args: [General, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Int32, Int32]

- ret: Int32
  args: [General, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Int32, Int32, Int32]

- ret:  Int32
  args: [General, Int32, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Int32, Int32, Int32, Int32]

- ret: Int32
  args: [General, Int32, Int32, Int32, Int32, Int32, Int32, General]

- ret: Int32
  args: [General, Int32, Int32, Int64, Int32]

- ret: Int32
  args: [General, Int32, Int64, Int64, Int32]

- ret: Int32
  args: [General, Int64, Int32, Int32]

- ret: Int32
  args: [General, Int64, Int32, Int32, Int32, Int32]

- ret: Int32
  args: [General, Int64, Int32, Int64, General]

- ret: Int32
  args: [General, Int64, Int32, Int64, Int32]

- ret: Int32
  args: [General, Int64, Int64, General]

- ret: Int32
  args: [General, Int64, Int64, Int64]

- ret: Int32
  args: [General, Int64, Int64, Int64, General]

- ret: Int32
  args: [General, Int64, Int64, Int64, General, General]

- ret: Int32
  args: [General, Int64, Int64, Int64, Int32]

- ret: Int32
  args: [General, Int64, Int64, Int64, Int32, Int32]

# Functions that return Int64 are tricky because SpiderMonkey's ReturnRegI64
# does not match the ABI int64 return register on x86.  Wasm only!
- ret: Int64
  args: [General]

- ret: Int64
  args: [General, General]

- ret: Int64
  args: [Int32, Int32, Int32, Int32]

- ret: Int64
  args: [General, Int32]

- ret: Int64
  args: [General, Int64]

- ret: Int64
  args: [General, Int64, Int32]

# void
- ret: Void
  args: [General, Int32, General, Int32, Int32]

- ret: Void
  args: [General, Int32, General, Int32, Int32, Int32]
