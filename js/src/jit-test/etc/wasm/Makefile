.PHONY: update

warning = '\# Wasm Spec Tests\n\nThese tests are autogenerated using a tool, do not edit.\n\nSee `jit-test/etc/wasm/` for more information.'

update:
	(cd ./generate-spectests && RUST_BACKTRACE=1 RUST_LOG=info cargo run --release)
	rm -r ../../tests/wasm/spec || echo "No tests to delete."
	cp -R generate-spectests/tests/js ../../tests/wasm/spec
	echo $(warning) > ../../tests/wasm/spec/README.md
	[ -f ./spec-tests.patch ] && (cd ../../tests/wasm/spec && patch -u -p7 < ../../../etc/wasm/spec-tests.patch)
