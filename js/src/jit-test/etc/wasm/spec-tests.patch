# HG changeset patch
# User <PERSON> <<EMAIL>>
# Date 1685551062 18000
#      Wed May 31 11:37:42 2023 -0500
# Node ID 551e7bae9a6bb2634680b8129af7634ceeccc648
# Parent  1b28ba88d59584fcf977974a625a179b5bdbdabf
Spec test patches rollup.

1. Bug 1737225 - Disable some tests on arm.  r=yury

Disable a partial-oob test on arm/arm64 because some hardware will
perform byte-at-a-time writes at the end of the heap, and we have
not fixed that yet.

diff --git a/js/src/jit-test/tests/wasm/spec/memory64/align64.wast.js b/js/src/jit-test/tests/wasm/spec/memory64/align64.wast.js
--- a/js/src/jit-test/tests/wasm/spec/memory64/align64.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/memory64/align64.wast.js
@@ -1076,8 +1076,15 @@ let $24 = instantiate(`(module
   )
 )`);

-// ./test/core/align64.wast:864
-assert_trap(() => invoke($24, `store`, [65532n, -1n]), `out of bounds memory access`);
+// Bug 1737225 - do not observe the partial store caused by bug 1666747 on
+// some native platforms.
+if (!partialOobWriteMayWritePartialData()) {
+    // ./test/core/align64.wast:864
+    assert_trap(
+        () => invoke($24, `store`, [65532n, -1n]),
+        `out of bounds memory access`,
+    );

-// ./test/core/align64.wast:866
-assert_return(() => invoke($24, `load`, [65532n]), [value("i32", 0)]);
+    // ./test/core/align64.wast:866
+    assert_return(() => invoke($24, `load`, [65532n]), [value("i32", 0)]);
+}
diff --git a/js/src/jit-test/tests/wasm/spec/memory64/harness/harness.js b/js/src/jit-test/tests/wasm/spec/memory64/harness/harness.js
--- a/js/src/jit-test/tests/wasm/spec/memory64/harness/harness.js
+++ b/js/src/jit-test/tests/wasm/spec/memory64/harness/harness.js
@@ -19,6 +19,15 @@ if (!wasmIsSupported()) {
   quit();
 }

+function partialOobWriteMayWritePartialData() {
+    let arm_native = getBuildConfiguration("arm") && !getBuildConfiguration("arm-simulator");
+    let arm64_native = getBuildConfiguration("arm64") && !getBuildConfiguration("arm64-simulator");
+    return arm_native || arm64_native;
+}
+
+let native_arm = getBuildConfiguration("arm") && !getBuildConfiguration("arm-simulator");
+let native_arm64 = getBuildConfiguration("arm64") && !getBuildConfiguration("arm64-simulator");
+
 function bytes(type, bytes) {
   var typedBuffer = new Uint8Array(bytes);
   return wasmGlobalFromArrayBuffer(type, typedBuffer.buffer);
diff --git a/js/src/jit-test/tests/wasm/spec/memory64/memory_trap64.wast.js b/js/src/jit-test/tests/wasm/spec/memory64/memory_trap64.wast.js
--- a/js/src/jit-test/tests/wasm/spec/memory64/memory_trap64.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/memory64/memory_trap64.wast.js
@@ -617,8 +617,16 @@ assert_trap(() => invoke($1, `i64.load32
 // ./test/core/memory_trap64.wast:265
 assert_trap(() => invoke($1, `i64.load32_u`, [-4n]), `out of bounds memory access`);

-// ./test/core/memory_trap64.wast:268
-assert_return(() => invoke($1, `i64.load`, [65528n]), [value("i64", 7523094288207667809n)]);
+// Bug 1737225 - do not observe the partial store caused by bug 1666747 on
+// some native platforms.
+if (!partialOobWriteMayWritePartialData()) {
+    // ./test/core/memory_trap64.wast:268
+    assert_return(() => invoke($1, `i64.load`, [65528n]), [
+        value("i64", 7523094288207667809n),
+    ]);

-// ./test/core/memory_trap64.wast:269
-assert_return(() => invoke($1, `i64.load`, [0n]), [value("i64", 7523094288207667809n)]);
+    // ./test/core/memory_trap64.wast:269
+    assert_return(() => invoke($1, `i64.load`, [0n]), [
+        value("i64", 7523094288207667809n),
+    ]);
+}
diff --git a/js/src/jit-test/tests/wasm/spec/memory64/memory.wast.js b/js/src/jit-test/tests/wasm/spec/memory64/memory.wast.js
--- a/js/src/jit-test/tests/wasm/spec/memory64/memory.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/memory64/memory.wast.js
@@ -138,24 +138,6 @@ assert_invalid(
   `memory size must be at most 65536 pages (4GiB)`,
 );
 
-// ./test/core/memory.wast:76
-assert_invalid(
-  () => instantiate(`(memory 0x1_0000_0000) `),
-  `memory size must be at most 65536 pages (4GiB)`,
-);
-
-// ./test/core/memory.wast:80
-assert_invalid(
-  () => instantiate(`(memory 0x1_0000_0000 0x1_0000_0000) `),
-  `memory size must be at most 65536 pages (4GiB)`,
-);
-
-// ./test/core/memory.wast:84
-assert_invalid(
-  () => instantiate(`(memory 0 0x1_0000_0000) `),
-  `memory size must be at most 65536 pages (4GiB)`,
-);
-
 // ./test/core/memory.wast:89
 let $9 = instantiate(`(module
   (memory 1)
diff --git a/js/src/jit-test/tests/wasm/spec/memory64/simd_address.wast.js b/js/src/jit-test/tests/wasm/spec/memory64/simd_address.wast.js
--- a/js/src/jit-test/tests/wasm/spec/memory64/simd_address.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/memory64/simd_address.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/ref_null.wast.js b/js/src/jit-test/tests/wasm/spec/spec/ref_null.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/ref_null.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/ref_null.wast.js
@@ -37,8 +37,9 @@ assert_return(() => invoke($0, `anyref`, []), [value('anyref', null)]);
 // ./test/core/ref_null.wast:17
 assert_return(() => invoke($0, `funcref`, []), [value('anyfunc', null)]);
 
-// ./test/core/ref_null.wast:18
-assert_return(() => invoke($0, `exnref`, []), [value('exnref', null)]);
+// exnref values are not exportable to JS
+// // ./test/core/ref_null.wast:18
+// assert_return(() => invoke($0, `exnref`, []), [value('exnref', null)]);
 
 // ./test/core/ref_null.wast:19
 assert_return(() => invoke($0, `externref`, []), [value('externref', null)]);
@@ -115,23 +116,24 @@ assert_return(() => invoke($1, `nullfuncref`, []), [value('nullfuncref', null)])
 // ./test/core/ref_null.wast:66
 assert_return(() => invoke($1, `nullfuncref`, []), [null]);

-// ./test/core/ref_null.wast:67
-assert_return(() => invoke($1, `exnref`, []), [value('exnref', null)]);
-
-// ./test/core/ref_null.wast:68
-assert_return(() => invoke($1, `exnref`, []), [value('nullexnref', null)]);
-
-// ./test/core/ref_null.wast:69
-assert_return(() => invoke($1, `exnref`, []), [null]);
-
-// ./test/core/ref_null.wast:70
-assert_return(() => invoke($1, `nullexnref`, []), [value('exnref', null)]);
-
-// ./test/core/ref_null.wast:71
-assert_return(() => invoke($1, `nullexnref`, []), [value('nullexnref', null)]);
-
-// ./test/core/ref_null.wast:72
-assert_return(() => invoke($1, `nullexnref`, []), [null]);
+// exnref values are not exportable to JS
+// // ./test/core/ref_null.wast:67
+// assert_return(() => invoke($1, `exnref`, []), [value('exnref', null)]);
+//
+// // ./test/core/ref_null.wast:68
+// assert_return(() => invoke($1, `exnref`, []), [value('nullexnref', null)]);
+//
+// // ./test/core/ref_null.wast:69
+// assert_return(() => invoke($1, `exnref`, []), [null]);
+//
+// // ./test/core/ref_null.wast:70
+// assert_return(() => invoke($1, `nullexnref`, []), [value('exnref', null)]);
+//
+// // ./test/core/ref_null.wast:71
+// assert_return(() => invoke($1, `nullexnref`, []), [value('nullexnref', null)]);
+//
+// // ./test/core/ref_null.wast:72
+// assert_return(() => invoke($1, `nullexnref`, []), [null]);
 
 // ./test/core/ref_null.wast:73
 assert_return(() => invoke($1, `externref`, []), [value('externref', null)]);
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_address.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_address.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_address.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_address.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_bit_shift.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_bit_shift.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_bit_shift.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_bit_shift.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_bitwise.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_bitwise.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_bitwise.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_bitwise.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_boolean.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_boolean.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_boolean.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_boolean.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_const.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_const.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_const.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_const.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_conversions.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_conversions.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_conversions.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_conversions.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_pmin_pmax.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_pmin_pmax.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_pmin_pmax.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_pmin_pmax.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_rounding.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_rounding.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_rounding.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f32x4_rounding.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_pmin_pmax.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_pmin_pmax.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_pmin_pmax.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_pmin_pmax.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_rounding.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_rounding.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_rounding.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_f64x2_rounding.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_arith2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extadd_pairwise_i8x16.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extadd_pairwise_i8x16.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extadd_pairwise_i8x16.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extadd_pairwise_i8x16.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extmul_i8x16.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extmul_i8x16.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extmul_i8x16.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_extmul_i8x16.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_q15mulr_sat_s.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_q15mulr_sat_s.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_q15mulr_sat_s.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_q15mulr_sat_s.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_sat_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_sat_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_sat_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i16x8_sat_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_arith2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_dot_i16x8.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_dot_i16x8.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_dot_i16x8.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_dot_i16x8.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extadd_pairwise_i16x8.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extadd_pairwise_i16x8.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extadd_pairwise_i16x8.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extadd_pairwise_i16x8.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extmul_i16x8.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extmul_i16x8.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extmul_i16x8.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_extmul_i16x8.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f32x4.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f32x4.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f32x4.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f32x4.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f64x2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f64x2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f64x2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i32x4_trunc_sat_f64x2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_arith2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_extmul_i32x4.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_extmul_i32x4.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_extmul_i32x4.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i64x2_extmul_i32x4.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith2.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith2.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith2.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_arith2.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_cmp.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_cmp.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_cmp.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_cmp.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_sat_arith.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_sat_arith.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_sat_arith.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_i8x16_sat_arith.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_int_to_int_extend.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_int_to_int_extend.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_int_to_int_extend.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_int_to_int_extend.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_linking.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_linking.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_linking.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_linking.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load16_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load16_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load16_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load16_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load32_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load32_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load32_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load32_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load64_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load64_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load64_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load64_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load8_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load8_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load8_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load8_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load_extend.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load_extend.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load_extend.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load_extend.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load_splat.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load_splat.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load_splat.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load_splat.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_load_zero.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_load_zero.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_load_zero.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_load_zero.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_memory-multi.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_memory-multi.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_memory-multi.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_memory-multi.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_splat.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_splat.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_splat.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_splat.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_store.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_store.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_store.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_store.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_store16_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_store16_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_store16_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_store16_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_store32_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_store32_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_store32_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_store32_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_store64_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_store64_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_store64_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_store64_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/simd_store8_lane.wast.js b/js/src/jit-test/tests/wasm/spec/spec/simd_store8_lane.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/simd_store8_lane.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/simd_store8_lane.wast.js
@@ -1,3 +1,5 @@
+// |jit-test| skip-if: !wasmSimdEnabled()
+
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/i8x16_relaxed_swizzle.wast.js b/js/src/jit-test/tests/wasm/spec/spec/i8x16_relaxed_swizzle.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/i8x16_relaxed_swizzle.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/i8x16_relaxed_swizzle.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/i16x8_relaxed_q15mulr_s.wast.js b/js/src/jit-test/tests/wasm/spec/spec/i16x8_relaxed_q15mulr_s.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/i16x8_relaxed_q15mulr_s.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/i16x8_relaxed_q15mulr_s.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/i32x4_relaxed_trunc.wast.js b/js/src/jit-test/tests/wasm/spec/spec/i32x4_relaxed_trunc.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/i32x4_relaxed_trunc.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/i32x4_relaxed_trunc.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
@@ -92,6 +92,7 @@ assert_return(
     either(
       i32x4([0x0, 0x0, 0xffffff00, 0xffffffff]),
       i32x4([0x0, 0xffffffff, 0xffffff00, 0xffffffff]),
+      i32x4([0x0, 0xffffffff, 0xffffff00, 0x0]),
     ),
   ],
 );
@@ -122,6 +123,7 @@ assert_return(
     either(
       i32x4([0x0, 0x0, 0x0, 0x0]),
       i32x4([0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff]),
+      i32x4([0x80000000, 0x80000000, 0x80000000, 0x80000000]),
     ),
   ],
 );
@@ -173,6 +175,7 @@ assert_return(
     either(
       i32x4([0x0, 0xffffffff, 0x0, 0x0]),
       i32x4([0xffffffff, 0xffffffff, 0x0, 0x0]),
+      i32x4([0xfffffffe, 0x0, 0x0, 0x0]),
     ),
   ],
 );
diff --git a/js/src/jit-test/tests/wasm/spec/spec/relaxed_dot_product.wast.js b/js/src/jit-test/tests/wasm/spec/spec/relaxed_dot_product.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/relaxed_dot_product.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/relaxed_dot_product.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/relaxed_laneselect.wast.js b/js/src/jit-test/tests/wasm/spec/spec/relaxed_laneselect.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/relaxed_laneselect.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/relaxed_laneselect.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/relaxed_madd_nmadd.wast.js b/js/src/jit-test/tests/wasm/spec/spec/relaxed_madd_nmadd.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/relaxed_madd_nmadd.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/relaxed_madd_nmadd.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/js/src/jit-test/tests/wasm/spec/spec/relaxed_min_max.wast.js b/js/src/jit-test/tests/wasm/spec/spec/relaxed_min_max.wast.js
--- a/js/src/jit-test/tests/wasm/spec/spec/relaxed_min_max.wast.js
+++ b/js/src/jit-test/tests/wasm/spec/spec/relaxed_min_max.wast.js
@@ -1,3 +1,4 @@
+// |jit-test| --setpref=wasm_relaxed_simd=true; skip-if: !wasmRelaxedSimdEnabled()
 /* Copyright 2021 Mozilla Foundation
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
