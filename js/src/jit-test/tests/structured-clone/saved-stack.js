// The following binary data was created with:
// JS_STRUCTURED_CLONE_VERSION = 8
//
// ```
// function f() {
//  return saveStack();
// }
// function g() {
//  return f();
// }
//
// let stack = g();
// print(valueToSource(serialize(stack, undefined, {scope: "DifferentProcess"}).clonebuffer))
// ```

function checkStack(stack) {
    print(stack.toString());

    assertEq(stack.functionDisplayName, "f");
    assertEq(stack.parent.functionDisplayName, "g");
    assertEq(stack.parent.parent.functionDisplayName, null);
    assertEq(stack.parent.parent.parent, null);
}

var clonebuffer = serialize("dummy");
clonebuffer.clonebuffer = "\x02\x00\x00\x00\x00\x00\xF1\xFF\x18\x00\xFF\xFF\x16\x00\xFF\xFF \x00\x00\x80\x04\x00\xFF\xFF/home/<USER>/Desktop/saved-stack.js\x11\x00\x00\x00\x03\x00\xFF\xFF\t\x00\x00\x00\x03\x00\xFF\xFF\x01\x00\x00\x80\x04\x00\xFF\xFFf\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xFF\x18\x00\xFF\xFF\x16\x00\xFF\xFF \x00\x00\x80\x04\x00\xFF\xFF/home/<USER>/Desktop/saved-stack.js\x14\x00\x00\x00\x03\x00\xFF\xFF\t\x00\x00\x00\x03\x00\xFF\xFF\x01\x00\x00\x80\x04\x00\xFF\xFFg\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xFF\x18\x00\xFF\xFF\x16\x00\xFF\xFF \x00\x00\x80\x04\x00\xFF\xFF/home/<USER>/Desktop/saved-stack.js\x17\x00\x00\x00\x03\x00\xFF\xFF\r\x00\x00\x00\x03\x00\xFF\xFF\x00\x00\x00\x00\x00\x00\xFF\xFF\x00\x00\x00\x00\x00\x00\xFF\xFF\x00\x00\x00\x00\x00\x00\xFF\xFF\x00\x00\x00\x00\x13\x00\xFF\xFF\x00\x00\x00\x00\x13\x00\xFF\xFF\x00\x00\x00\x00\x13\x00\xFF\xFF";
var stack = deserialize(clonebuffer);
checkStack(stack);

function f() {
 return saveStack();
}
function g() {
 return f();
}
stack = deserialize(serialize(g()));
checkStack(stack);
