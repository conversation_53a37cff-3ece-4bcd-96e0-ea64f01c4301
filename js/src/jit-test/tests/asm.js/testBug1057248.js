// |jit-test| skip-if: !this.SharedArrayBuffer || !isAsmJSCompilationAvailable()

Random = {
    weighted: function(wa) {
        var a = [];
        for (var i = 0; i < wa.length; ++i) {
            for (var j = 0; j < wa[i]; ++j) {}
        }
    }
}
s = (function() {
    Random.weighted([{
            n() {}
        }, {
            n() {}
        }, {
            n() {}
        }, {
            w: 5,
            n() {}
        }
    ])
})()
var builtinObjectNames = [];
(function() {
    function exploreDeeper(a, a) {
        var s = Object.getOwnPropertyNames(a)
        for (var j = 0; j < s.length; ++j) {
            if (typeof h == "" && n != "") {}
        }
        builtinObjectNames.push()
    }
    exploreDeeper(Math, "")
})(this, false)
r = autoExpr(Random.weighted([]));
r = autoExpr(Random.weighted([]));
(Random.weighted([]));
r = (Random.weighted([]))
v = autoExpr(Random.weighted([]), true);
r = autoExpr(Random.weighted([{
        n() {}
    }
]), true)
function autoExpr() {}
function makeStatement() {}
s = Random.weighted([{
        n() {}
    }
])
a = Random.weighted([{}, {}, {}, {}, {}, {
        n() {}
    }
])
a = Random.weighted([{
        n() {}
    }, {}, {}
])
var recursiveFunctions = [{
        text: "(function(){{}})"
    }, {
        text: "(function(){if(0){}(1)})",
        n() {}
    }, {
        text: "(function(){t:[]()})",
        n() {}
    }, {
        text: "(function(){g()})",
        n() {}
    }
];
(function s() {
    for (var i = 0; i < recursiveFunctions.length; ++i) {
        a = recursiveFunctions[i];
        var text = a.text
        a = eval(text.replace(/@/, ""))
    }
    function g() {}
    s = Random.weighted([{
            w: 1,
            n() {}
        }, {
            n() {}
        }, //
        {
            n() {}
        }, //
        {
            n() {}
        }, {
            w: 0,
            n() {}
        }, {
            n() {}
        }, {
            n() {}
        }, {
            w: 1,
            n() {}
        }, {
            w: makeStatement
        }, {
            w: 5
        }
    ])
    t = function() {}
    function c() {}
})()
function testMathyFunction(f, inputs) {
    var r = [];
    for (var j = 0; j < inputs.length; ++j) {
        for (var k = 0; k < inputs.length; ++k) {
            try {
                f(inputs[j])
            } catch (e) {}
        }
    }
    JSON.stringify([])
}
mathy2 = (function(stdlib, foreign, heap) {
    "use asm";
    var Float32ArrayView = new stdlib.Float32Array(heap)
        function f(i0) {
            i0 = i0 | 0;
            (Float32ArrayView[-i0 >> 2]) = 1.
        }
    return f
})(this, {
}, new SharedArrayBuffer(4096))
testMathyFunction(mathy2, [-0])
mathy5 = (function(y) {
    ((function() {})(mathy2(y)()))
})
testMathyFunction(mathy5, [Math.PI])
