var buffer = new ArrayBuffer(4096);

function testmod (glob, env, b) {
    'use asm';
    var f32=new glob.Float32Array(b)
    function fn1() {
	f32[0 >> 2]=2.0
	f32[4 >> 2]=2.125
	f32[8 >> 2]=2.25
	f32[12 >> 2]=2.375
	f32[16 >> 2]=2.5
	f32[20 >> 2]=2.625
	f32[24 >> 2]=2.75
	f32[28 >> 2]=2.875
	f32[32 >> 2]=3.0
	f32[36 >> 2]=3.125
	f32[40 >> 2]=3.25
	f32[44 >> 2]=3.375
	f32[48 >> 2]=3.5
	f32[52 >> 2]=3.625
	f32[56 >> 2]=3.75
	f32[60 >> 2]=3.875
	f32[64 >> 2]=4.0
	f32[68 >> 2]=4.25
	f32[72 >> 2]=4.5
	f32[76 >> 2]=4.75
	f32[80 >> 2]=5.0
	f32[84 >> 2]=5.25
	f32[88 >> 2]=5.5
	f32[92 >> 2]=5.75
	f32[96 >> 2]=6.0
	f32[100 >> 2]=6.25
	f32[104 >> 2]=6.5
	f32[108 >> 2]=6.75
	f32[112 >> 2]=7.0
	f32[116 >> 2]=7.25
	f32[120 >> 2]=7.5
	f32[124 >> 2]=7.75
	f32[128 >> 2]=8.0
	f32[132 >> 2]=8.5
	f32[136 >> 2]=9.0
	f32[140 >> 2]=9.5
	f32[144 >> 2]=10.0
	f32[148 >> 2]=10.5
	f32[152 >> 2]=11.0
	f32[156 >> 2]=11.5
	f32[160 >> 2]=12.0
	f32[164 >> 2]=12.5
	f32[168 >> 2]=13.0
	f32[172 >> 2]=13.5
	f32[176 >> 2]=14.0
	f32[180 >> 2]=14.5
	f32[184 >> 2]=15.0
	f32[188 >> 2]=15.5
	f32[192 >> 2]=16.0
	f32[196 >> 2]=17.0
	f32[200 >> 2]=18.0
	f32[204 >> 2]=19.0
	f32[208 >> 2]=20.0
	f32[212 >> 2]=21.0
	f32[216 >> 2]=22.0
	f32[220 >> 2]=23.0
	f32[224 >> 2]=24.0
	f32[228 >> 2]=25.0
	f32[232 >> 2]=26.0
	f32[236 >> 2]=27.0
	f32[240 >> 2]=28.0
	f32[244 >> 2]=29.0
	f32[248 >> 2]=30.0
	f32[252 >> 2]=31.0
	f32[256 >> 2]=0.125
	f32[260 >> 2]=0.1328125
	f32[264 >> 2]=0.140625
	f32[268 >> 2]=0.1484375
	f32[272 >> 2]=0.15625
	f32[276 >> 2]=0.1640625
	f32[280 >> 2]=0.171875
	f32[284 >> 2]=0.1796875
	f32[288 >> 2]=0.1875
	f32[292 >> 2]=0.1953125
	f32[296 >> 2]=0.203125
	f32[300 >> 2]=0.2109375
	f32[304 >> 2]=0.21875
	f32[308 >> 2]=0.2265625
	f32[312 >> 2]=0.234375
	f32[316 >> 2]=0.2421875
	f32[320 >> 2]=0.25
	f32[324 >> 2]=0.265625
	f32[328 >> 2]=0.28125
	f32[332 >> 2]=0.296875
	f32[336 >> 2]=0.3125
	f32[340 >> 2]=0.328125
	f32[344 >> 2]=0.34375
	f32[348 >> 2]=0.359375
	f32[352 >> 2]=0.375
	f32[356 >> 2]=0.390625
	f32[360 >> 2]=0.40625
	f32[364 >> 2]=0.421875
	f32[368 >> 2]=0.4375
	f32[368 >> 2]=0.4375
	f32[372 >> 2]=0.453125
	f32[376 >> 2]=0.46875
	f32[380 >> 2]=0.484375
	f32[384 >> 2]=0.5
	f32[388 >> 2]=0.53125
	f32[392 >> 2]=0.5625
	f32[396 >> 2]=0.59375
	f32[400 >> 2]=0.625
	f32[404 >> 2]=0.65625
	f32[408 >> 2]=0.6875
	f32[412 >> 2]=0.71875
	f32[416 >> 2]=0.75
	f32[420 >> 2]=0.78125
	f32[424 >> 2]=0.8125
	f32[428 >> 2]=0.84375
	f32[432 >> 2]=0.875
	f32[436 >> 2]=0.90625
	f32[440 >> 2]=0.9375
	f32[444 >> 2]=0.96875
	f32[448 >> 2]=1.0
	f32[452 >> 2]=1.0625
	f32[456 >> 2]=1.125
	f32[460 >> 2]=1.1875
	f32[464 >> 2]=1.25
	f32[468 >> 2]=1.3125
	f32[472 >> 2]=1.375
	f32[476 >> 2]=1.4375
	f32[480 >> 2]=1.5
	f32[484 >> 2]=1.5625
	f32[488 >> 2]=1.625
	f32[492 >> 2]=1.6875
	f32[496 >> 2]=1.75
	f32[500 >> 2]=1.8125
	f32[504 >> 2]=1.875
	f32[508 >> 2]=1.9375
	f32[512 >> 2]=-2.0
	f32[516 >> 2]=-2.125
	f32[520 >> 2]=-2.25
	f32[524 >> 2]=-2.375
	f32[528 >> 2]=-2.5
	f32[532 >> 2]=-2.625
	f32[536 >> 2]=-2.75
	f32[540 >> 2]=-2.875
	f32[544 >> 2]=-3.0
	f32[548 >> 2]=-3.125
	f32[552 >> 2]=-3.25
	f32[556 >> 2]=-3.375
	f32[560 >> 2]=-3.5
	f32[564 >> 2]=-3.625
	f32[568 >> 2]=-3.75
	f32[572 >> 2]=-3.875
	f32[576 >> 2]=-4.0
	f32[580 >> 2]=-4.25
	f32[584 >> 2]=-4.5
	f32[588 >> 2]=-4.75
	f32[592 >> 2]=-5.0
	f32[596 >> 2]=-5.25
	f32[600 >> 2]=-5.5
	f32[604 >> 2]=-5.75
	f32[608 >> 2]=-6.0
	f32[612 >> 2]=-6.25
	f32[616 >> 2]=-6.5
	f32[620 >> 2]=-6.75
	f32[624 >> 2]=-7.0
	f32[628 >> 2]=-7.25
	f32[632 >> 2]=-7.5
	f32[636 >> 2]=-7.75
	f32[640 >> 2]=-8.0
	f32[644 >> 2]=-8.5
	f32[648 >> 2]=-9.0
	f32[652 >> 2]=-9.5
	f32[656 >> 2]=-10.0
	f32[660 >> 2]=-10.5
	f32[664 >> 2]=-11.0
	f32[668 >> 2]=-11.5
	f32[672 >> 2]=-12.0
	f32[676 >> 2]=-12.5
	f32[680 >> 2]=-13.0
	f32[684 >> 2]=-13.5
	f32[688 >> 2]=-14.0
	f32[692 >> 2]=-14.5
	f32[696 >> 2]=-15.0
	f32[700 >> 2]=-15.5
	f32[704 >> 2]=-16.0
	f32[708 >> 2]=-17.0
	f32[712 >> 2]=-18.0
	f32[716 >> 2]=-19.0
	f32[720 >> 2]=-20.0
	f32[724 >> 2]=-21.0
	f32[728 >> 2]=-22.0
	f32[732 >> 2]=-23.0
	f32[736 >> 2]=-24.0
	f32[740 >> 2]=-25.0
	f32[744 >> 2]=-26.0
	f32[748 >> 2]=-27.0
	f32[752 >> 2]=-28.0
	f32[756 >> 2]=-29.0
	f32[760 >> 2]=-30.0
	f32[764 >> 2]=-31.0
	f32[768 >> 2]=-0.125
	f32[768 >> 2]=-0.125
	f32[772 >> 2]=-0.1328125
	f32[776 >> 2]=-0.140625
	f32[780 >> 2]=-0.1484375
	f32[784 >> 2]=-0.15625
	f32[788 >> 2]=-0.1640625
	f32[792 >> 2]=-0.171875
	f32[796 >> 2]=-0.1796875
	f32[800 >> 2]=-0.1875
	f32[804 >> 2]=-0.1953125
	f32[808 >> 2]=-0.203125
	f32[812 >> 2]=-0.2109375
	f32[816 >> 2]=-0.21875
	f32[820 >> 2]=-0.2265625
	f32[824 >> 2]=-0.234375
	f32[828 >> 2]=-0.2421875
	f32[832 >> 2]=-0.25
	f32[836 >> 2]=-0.265625
	f32[840 >> 2]=-0.28125
	f32[844 >> 2]=-0.296875
	f32[848 >> 2]=-0.3125
	f32[852 >> 2]=-0.328125
	f32[856 >> 2]=-0.34375
	f32[860 >> 2]=-0.359375
	f32[864 >> 2]=-0.375
	f32[868 >> 2]=-0.390625
	f32[872 >> 2]=-0.40625
	f32[876 >> 2]=-0.421875
	f32[880 >> 2]=-0.4375
	f32[884 >> 2]=-0.453125
	f32[888 >> 2]=-0.46875
	f32[892 >> 2]=-0.484375
	f32[896 >> 2]=-0.5
	f32[900 >> 2]=-0.53125
	f32[904 >> 2]=-0.5625
	f32[908 >> 2]=-0.59375
	f32[912 >> 2]=-0.625
	f32[916 >> 2]=-0.65625
	f32[920 >> 2]=-0.6875
	f32[924 >> 2]=-0.71875
	f32[928 >> 2]=-0.75
	f32[932 >> 2]=-0.78125
	f32[936 >> 2]=-0.8125
	f32[940 >> 2]=-0.84375
	f32[944 >> 2]=-0.875
	f32[948 >> 2]=-0.90625
	f32[952 >> 2]=-0.9375
	f32[956 >> 2]=-0.96875
	f32[960 >> 2]=-1.0
	f32[964 >> 2]=-1.0625
	f32[968 >> 2]=-1.125
	f32[972 >> 2]=-1.1875
	f32[976 >> 2]=-1.25
	f32[980 >> 2]=-1.3125
	f32[984 >> 2]=-1.375
	f32[988 >> 2]=-1.4375
	f32[992 >> 2]=-1.5
	f32[996 >> 2]=-1.5625
	f32[1000 >> 2]=-1.625
	f32[1004 >> 2]=-1.6875
	f32[1008 >> 2]=-1.75
	f32[1012 >> 2]=-1.8125
	f32[1016 >> 2]=-1.875
	f32[1020 >> 2]=-1.9375

	// Some cases that should not be encoded as an immediate on the ARM.

	// All the low zero bits set.
	f32[1024 >> 2]=2.1249998
	f32[1028 >> 2]=2.2499998
	f32[1032 >> 2]=2.3749998
	f32[1036 >> 2]=2.4999998
	f32[1040 >> 2]=2.6249998
	f32[1044 >> 2]=2.7499998
	f32[1048 >> 2]=2.8749998
	f32[1052 >> 2]=2.9999998
	f32[1056 >> 2]=3.1249998
	f32[1060 >> 2]=3.2499998
	f32[1064 >> 2]=3.3749998
	f32[1068 >> 2]=3.4999998
	f32[1072 >> 2]=3.6249998
	f32[1076 >> 2]=3.7499998
	f32[1080 >> 2]=3.8749998
	f32[1084 >> 2]=3.9999998
	f32[1088 >> 2]=4.2499995
	f32[1092 >> 2]=4.4999995
	f32[1096 >> 2]=4.7499995
	f32[1100 >> 2]=4.9999995
	f32[1104 >> 2]=5.2499995
	f32[1108 >> 2]=5.4999995
	f32[1112 >> 2]=5.7499995
	f32[1116 >> 2]=5.9999995
	f32[1120 >> 2]=6.2499995
	f32[1124 >> 2]=6.4999995
	f32[1128 >> 2]=6.7499995
	f32[1132 >> 2]=6.9999995
	f32[1136 >> 2]=7.2499995
	f32[1140 >> 2]=7.4999995
	f32[1144 >> 2]=7.7499995
	f32[1148 >> 2]=7.9999995
	f32[1152 >> 2]=8.499999
	f32[1156 >> 2]=8.999999
	f32[1160 >> 2]=9.499999
	f32[1164 >> 2]=9.999999
	f32[1168 >> 2]=10.499999
	f32[1172 >> 2]=10.999999
	f32[1176 >> 2]=11.499999
	f32[1180 >> 2]=11.999999
	f32[1184 >> 2]=12.499999
	f32[1188 >> 2]=12.999999
	f32[1192 >> 2]=13.499999
	f32[1196 >> 2]=13.999999
	f32[1200 >> 2]=14.499999
	f32[1204 >> 2]=14.999999
	f32[1208 >> 2]=15.499999
	f32[1212 >> 2]=15.999999
	f32[1216 >> 2]=16.999998
	f32[1220 >> 2]=17.999998
	f32[1224 >> 2]=18.999998
	f32[1228 >> 2]=19.999998
	f32[1232 >> 2]=20.999998
	f32[1236 >> 2]=21.999998
	f32[1240 >> 2]=22.999998
	f32[1244 >> 2]=23.999998
	f32[1248 >> 2]=24.999998
	f32[1252 >> 2]=25.999998
	f32[1256 >> 2]=26.999998
	f32[1260 >> 2]=27.999998
	f32[1264 >> 2]=28.999998
	f32[1268 >> 2]=29.999998
	f32[1272 >> 2]=30.999998
	f32[1276 >> 2]=31.999998
	f32[1280 >> 2]=0.13281249
	f32[1284 >> 2]=0.14062499
	f32[1288 >> 2]=0.14843749
	f32[1292 >> 2]=0.15624999
	f32[1296 >> 2]=0.16406249
	f32[1300 >> 2]=0.17187499
	f32[1304 >> 2]=0.17968749
	f32[1308 >> 2]=0.18749999
	f32[1312 >> 2]=0.19531249
	f32[1316 >> 2]=0.20312499
	f32[1320 >> 2]=0.21093749
	f32[1324 >> 2]=0.21874999
	f32[1328 >> 2]=0.22656249
	f32[1332 >> 2]=0.23437499
	f32[1336 >> 2]=0.24218749
	f32[1340 >> 2]=0.24999999
	f32[1344 >> 2]=0.26562497
	f32[1348 >> 2]=0.28124997
	f32[1352 >> 2]=0.29687497
	f32[1356 >> 2]=0.31249997
	f32[1360 >> 2]=0.32812497
	f32[1364 >> 2]=0.34374997
	f32[1368 >> 2]=0.35937497
	f32[1372 >> 2]=0.37499997
	f32[1376 >> 2]=0.39062497
	f32[1380 >> 2]=0.40624997
	f32[1384 >> 2]=0.42187497
	f32[1388 >> 2]=0.43749997
	f32[1392 >> 2]=0.45312497
	f32[1396 >> 2]=0.46874997
	f32[1400 >> 2]=0.48437497
	f32[1404 >> 2]=0.49999997
	f32[1408 >> 2]=0.53124994
	f32[1412 >> 2]=0.56249994
	f32[1416 >> 2]=0.59374994
	f32[1420 >> 2]=0.62499994
	f32[1424 >> 2]=0.65624994
	f32[1428 >> 2]=0.68749994
	f32[1432 >> 2]=0.71874994
	f32[1436 >> 2]=0.74999994
	f32[1440 >> 2]=0.78124994
	f32[1444 >> 2]=0.81249994
	f32[1448 >> 2]=0.84374994
	f32[1452 >> 2]=0.87499994
	f32[1456 >> 2]=0.90624994
	f32[1460 >> 2]=0.93749994
	f32[1464 >> 2]=0.96874994
	f32[1468 >> 2]=0.99999994
	f32[1472 >> 2]=1.0624999
	f32[1476 >> 2]=1.1249999
	f32[1480 >> 2]=1.1874999
	f32[1484 >> 2]=1.2499999
	f32[1488 >> 2]=1.3124999
	f32[1488 >> 2]=1.3124999
	f32[1492 >> 2]=1.3749999
	f32[1496 >> 2]=1.4374999
	f32[1500 >> 2]=1.4999999
	f32[1504 >> 2]=1.5624999
	f32[1508 >> 2]=1.6249999
	f32[1512 >> 2]=1.6874999
	f32[1516 >> 2]=1.7499999
	f32[1520 >> 2]=1.8124999
	f32[1524 >> 2]=1.8749999
	f32[1528 >> 2]=1.9374999
	f32[1532 >> 2]=1.9999999
	f32[1536 >> 2]=-2.1249998
	f32[1540 >> 2]=-2.2499998
	f32[1544 >> 2]=-2.3749998
	f32[1548 >> 2]=-2.4999998
	f32[1552 >> 2]=-2.6249998
	f32[1556 >> 2]=-2.7499998
	f32[1560 >> 2]=-2.8749998
	f32[1564 >> 2]=-2.9999998
	f32[1568 >> 2]=-3.1249998
	f32[1572 >> 2]=-3.2499998
	f32[1576 >> 2]=-3.3749998
	f32[1580 >> 2]=-3.4999998
	f32[1584 >> 2]=-3.6249998
	f32[1588 >> 2]=-3.7499998
	f32[1592 >> 2]=-3.8749998
	f32[1596 >> 2]=-3.9999998
	f32[1600 >> 2]=-4.2499995
	f32[1604 >> 2]=-4.4999995
	f32[1608 >> 2]=-4.7499995
	f32[1612 >> 2]=-4.9999995
	f32[1616 >> 2]=-5.2499995
	f32[1620 >> 2]=-5.4999995
	f32[1624 >> 2]=-5.7499995
	f32[1628 >> 2]=-5.9999995
	f32[1632 >> 2]=-6.2499995
	f32[1636 >> 2]=-6.4999995
	f32[1640 >> 2]=-6.7499995
	f32[1644 >> 2]=-6.9999995
	f32[1648 >> 2]=-7.2499995
	f32[1652 >> 2]=-7.4999995
	f32[1656 >> 2]=-7.7499995
	f32[1660 >> 2]=-7.9999995
	f32[1664 >> 2]=-8.499999
	f32[1668 >> 2]=-8.999999
	f32[1672 >> 2]=-9.499999
	f32[1676 >> 2]=-9.999999
	f32[1680 >> 2]=-10.499999
	f32[1684 >> 2]=-10.999999
	f32[1688 >> 2]=-11.499999
	f32[1692 >> 2]=-11.999999
	f32[1696 >> 2]=-12.499999
	f32[1700 >> 2]=-12.999999
	f32[1704 >> 2]=-13.499999
	f32[1708 >> 2]=-13.999999
	f32[1712 >> 2]=-14.499999
	f32[1716 >> 2]=-14.999999
	f32[1720 >> 2]=-15.499999
	f32[1724 >> 2]=-15.999999
	f32[1728 >> 2]=-16.999998
	f32[1732 >> 2]=-17.999998
	f32[1736 >> 2]=-18.999998
	f32[1740 >> 2]=-19.999998
	f32[1744 >> 2]=-20.999998
	f32[1748 >> 2]=-21.999998
	f32[1752 >> 2]=-22.999998
	f32[1756 >> 2]=-23.999998
	f32[1760 >> 2]=-24.999998
	f32[1764 >> 2]=-25.999998
	f32[1768 >> 2]=-26.999998
	f32[1772 >> 2]=-27.999998
	f32[1776 >> 2]=-28.999998
	f32[1780 >> 2]=-29.999998
	f32[1784 >> 2]=-30.999998
	f32[1788 >> 2]=-31.999998
	f32[1792 >> 2]=-0.13281249
	f32[1796 >> 2]=-0.14062499
	f32[1800 >> 2]=-0.14843749
	f32[1804 >> 2]=-0.15624999
	f32[1808 >> 2]=-0.16406249
	f32[1812 >> 2]=-0.17187499
	f32[1816 >> 2]=-0.17968749
	f32[1820 >> 2]=-0.18749999
	f32[1824 >> 2]=-0.19531249
	f32[1828 >> 2]=-0.20312499
	f32[1832 >> 2]=-0.21093749
	f32[1836 >> 2]=-0.21874999
	f32[1840 >> 2]=-0.22656249
	f32[1844 >> 2]=-0.23437499
	f32[1848 >> 2]=-0.24218749
	f32[1852 >> 2]=-0.24999999
	f32[1856 >> 2]=-0.26562497
	f32[1860 >> 2]=-0.28124997
	f32[1864 >> 2]=-0.29687497
	f32[1868 >> 2]=-0.31249997
	f32[1872 >> 2]=-0.32812497
	f32[1876 >> 2]=-0.34374997
	f32[1880 >> 2]=-0.35937497
	f32[1884 >> 2]=-0.37499997
	f32[1888 >> 2]=-0.39062497
	f32[1888 >> 2]=-0.39062497
	f32[1892 >> 2]=-0.40624997
	f32[1896 >> 2]=-0.42187497
	f32[1900 >> 2]=-0.43749997
	f32[1904 >> 2]=-0.45312497
	f32[1908 >> 2]=-0.46874997
	f32[1912 >> 2]=-0.48437497
	f32[1916 >> 2]=-0.49999997
	f32[1920 >> 2]=-0.53124994
	f32[1924 >> 2]=-0.56249994
	f32[1928 >> 2]=-0.59374994
	f32[1932 >> 2]=-0.62499994
	f32[1936 >> 2]=-0.65624994
	f32[1940 >> 2]=-0.68749994
	f32[1944 >> 2]=-0.71874994
	f32[1948 >> 2]=-0.74999994
	f32[1952 >> 2]=-0.78124994
	f32[1956 >> 2]=-0.81249994
	f32[1960 >> 2]=-0.84374994
	f32[1964 >> 2]=-0.87499994
	f32[1968 >> 2]=-0.90624994
	f32[1972 >> 2]=-0.93749994
	f32[1976 >> 2]=-0.96874994
	f32[1980 >> 2]=-0.99999994
	f32[1984 >> 2]=-1.0624999
	f32[1988 >> 2]=-1.1249999
	f32[1992 >> 2]=-1.1874999
	f32[1996 >> 2]=-1.2499999
	f32[2000 >> 2]=-1.3124999
	f32[2004 >> 2]=-1.3749999
	f32[2008 >> 2]=-1.4374999
	f32[2012 >> 2]=-1.4999999
	f32[2016 >> 2]=-1.5624999
	f32[2020 >> 2]=-1.6249999
	f32[2024 >> 2]=-1.6874999
	f32[2028 >> 2]=-1.7499999
	f32[2032 >> 2]=-1.8124999
	f32[2036 >> 2]=-1.8749999
	f32[2040 >> 2]=-1.9374999
	f32[2044 >> 2]=-1.9999999

	// Just the lowest zero bit set.
	f32[2048 >> 2]=2.0000002
	f32[2052 >> 2]=2.1250002
	f32[2056 >> 2]=2.2500002
	f32[2060 >> 2]=2.3750002
	f32[2064 >> 2]=2.5000002
	f32[2068 >> 2]=2.6250002
	f32[2072 >> 2]=2.7500002
	f32[2076 >> 2]=2.8750002
	f32[2080 >> 2]=3.0000002
	f32[2084 >> 2]=3.1250002
	f32[2088 >> 2]=3.2500002
	f32[2092 >> 2]=3.3750002
	f32[2096 >> 2]=3.5000002
	f32[2100 >> 2]=3.6250002
	f32[2104 >> 2]=3.7500002
	f32[2108 >> 2]=3.8750002
	f32[2112 >> 2]=4.0000005
	f32[2116 >> 2]=4.2500005
	f32[2120 >> 2]=4.5000005
	f32[2124 >> 2]=4.7500005
	f32[2128 >> 2]=5.0000005
	f32[2132 >> 2]=5.2500005
	f32[2136 >> 2]=5.5000005
	f32[2140 >> 2]=5.7500005
	f32[2140 >> 2]=5.7500005
	f32[2144 >> 2]=6.0000005
	f32[2148 >> 2]=6.2500005
	f32[2152 >> 2]=6.5000005
	f32[2156 >> 2]=6.7500005
	f32[2160 >> 2]=7.0000005
	f32[2164 >> 2]=7.2500005
	f32[2168 >> 2]=7.5000005
	f32[2172 >> 2]=7.7500005
	f32[2176 >> 2]=8.000001
	f32[2180 >> 2]=8.500001
	f32[2184 >> 2]=9.000001
	f32[2188 >> 2]=9.500001
	f32[2192 >> 2]=10.000001
	f32[2196 >> 2]=10.500001
	f32[2200 >> 2]=11.000001
	f32[2204 >> 2]=11.500001
	f32[2208 >> 2]=12.000001
	f32[2212 >> 2]=12.500001
	f32[2216 >> 2]=13.000001
	f32[2220 >> 2]=13.500001
	f32[2224 >> 2]=14.000001
	f32[2228 >> 2]=14.500001
	f32[2232 >> 2]=15.000001
	f32[2236 >> 2]=15.500001
	f32[2240 >> 2]=16.000002
	f32[2244 >> 2]=17.000002
	f32[2248 >> 2]=18.000002
	f32[2252 >> 2]=19.000002
	f32[2256 >> 2]=20.000002
	f32[2260 >> 2]=21.000002
	f32[2264 >> 2]=22.000002
	f32[2268 >> 2]=23.000002
	f32[2272 >> 2]=24.000002
	f32[2276 >> 2]=25.000002
	f32[2280 >> 2]=26.000002
	f32[2284 >> 2]=27.000002
	f32[2288 >> 2]=28.000002
	f32[2292 >> 2]=29.000002
	f32[2296 >> 2]=30.000002
	f32[2300 >> 2]=31.000002
	f32[2304 >> 2]=0.12500001
	f32[2308 >> 2]=0.13281251
	f32[2312 >> 2]=0.14062501
	f32[2316 >> 2]=0.14843751
	f32[2320 >> 2]=0.15625001
	f32[2324 >> 2]=0.16406251
	f32[2328 >> 2]=0.17187501
	f32[2332 >> 2]=0.17968751
	f32[2336 >> 2]=0.18750001
	f32[2340 >> 2]=0.19531251
	f32[2344 >> 2]=0.20312501
	f32[2348 >> 2]=0.21093751
	f32[2352 >> 2]=0.21875001
	f32[2356 >> 2]=0.22656251
	f32[2360 >> 2]=0.23437501
	f32[2364 >> 2]=0.24218751
	f32[2368 >> 2]=0.25000003
	f32[2372 >> 2]=0.26562503
	f32[2376 >> 2]=0.28125003
	f32[2380 >> 2]=0.29687503
	f32[2384 >> 2]=0.31250003
	f32[2388 >> 2]=0.32812503
	f32[2392 >> 2]=0.34375003
	f32[2396 >> 2]=0.35937503
	f32[2400 >> 2]=0.37500003
	f32[2404 >> 2]=0.39062503
	f32[2408 >> 2]=0.40625003
	f32[2412 >> 2]=0.42187503
	f32[2416 >> 2]=0.43750003
	f32[2420 >> 2]=0.45312503
	f32[2424 >> 2]=0.46875003
	f32[2428 >> 2]=0.48437503
	f32[2432 >> 2]=0.50000006
	f32[2436 >> 2]=0.53125006
	f32[2440 >> 2]=0.56250006
	f32[2444 >> 2]=0.59375006
	f32[2448 >> 2]=0.62500006
	f32[2452 >> 2]=0.65625006
	f32[2456 >> 2]=0.68750006
	f32[2460 >> 2]=0.71875006
	f32[2464 >> 2]=0.75000006
	f32[2468 >> 2]=0.78125006
	f32[2472 >> 2]=0.81250006
	f32[2476 >> 2]=0.84375006
	f32[2480 >> 2]=0.87500006
	f32[2484 >> 2]=0.90625006
	f32[2488 >> 2]=0.93750006
	f32[2492 >> 2]=0.96875006
	f32[2496 >> 2]=1.0000001
	f32[2500 >> 2]=1.0625001
	f32[2504 >> 2]=1.1250001
	f32[2508 >> 2]=1.1875001
	f32[2512 >> 2]=1.2500001
	f32[2516 >> 2]=1.3125001
	f32[2520 >> 2]=1.3750001
	f32[2524 >> 2]=1.4375001
	f32[2528 >> 2]=1.5000001
	f32[2532 >> 2]=1.5625001
	f32[2536 >> 2]=1.6250001
	f32[2540 >> 2]=1.6875001
	f32[2540 >> 2]=1.6875001
	f32[2544 >> 2]=1.7500001
	f32[2548 >> 2]=1.8125001
	f32[2552 >> 2]=1.8750001
	f32[2556 >> 2]=1.9375001
	f32[2560 >> 2]=-2.0000002
	f32[2564 >> 2]=-2.1250002
	f32[2568 >> 2]=-2.2500002
	f32[2572 >> 2]=-2.3750002
	f32[2576 >> 2]=-2.5000002
	f32[2580 >> 2]=-2.6250002
	f32[2584 >> 2]=-2.7500002
	f32[2588 >> 2]=-2.8750002
	f32[2592 >> 2]=-3.0000002
	f32[2596 >> 2]=-3.1250002
	f32[2600 >> 2]=-3.2500002
	f32[2604 >> 2]=-3.3750002
	f32[2608 >> 2]=-3.5000002
	f32[2612 >> 2]=-3.6250002
	f32[2616 >> 2]=-3.7500002
	f32[2620 >> 2]=-3.8750002
	f32[2624 >> 2]=-4.0000005
	f32[2628 >> 2]=-4.2500005
	f32[2632 >> 2]=-4.5000005
	f32[2636 >> 2]=-4.7500005
	f32[2640 >> 2]=-5.0000005
	f32[2644 >> 2]=-5.2500005
	f32[2648 >> 2]=-5.5000005
	f32[2652 >> 2]=-5.7500005
	f32[2656 >> 2]=-6.0000005
	f32[2660 >> 2]=-6.2500005
	f32[2664 >> 2]=-6.5000005
	f32[2668 >> 2]=-6.7500005
	f32[2672 >> 2]=-7.0000005
	f32[2676 >> 2]=-7.2500005
	f32[2680 >> 2]=-7.5000005
	f32[2684 >> 2]=-7.7500005
	f32[2688 >> 2]=-8.000001
	f32[2692 >> 2]=-8.500001
	f32[2696 >> 2]=-9.000001
	f32[2700 >> 2]=-9.500001
	f32[2704 >> 2]=-10.000001
	f32[2708 >> 2]=-10.500001
	f32[2712 >> 2]=-11.000001
	f32[2716 >> 2]=-11.500001
	f32[2720 >> 2]=-12.000001
	f32[2724 >> 2]=-12.500001
	f32[2728 >> 2]=-13.000001
	f32[2732 >> 2]=-13.500001
	f32[2736 >> 2]=-14.000001
	f32[2740 >> 2]=-14.500001
	f32[2744 >> 2]=-15.000001
	f32[2748 >> 2]=-15.500001
	f32[2752 >> 2]=-16.000002
	f32[2756 >> 2]=-17.000002
	f32[2760 >> 2]=-18.000002
	f32[2764 >> 2]=-19.000002
	f32[2768 >> 2]=-20.000002
	f32[2772 >> 2]=-21.000002
	f32[2776 >> 2]=-22.000002
	f32[2780 >> 2]=-23.000002
	f32[2784 >> 2]=-24.000002
	f32[2788 >> 2]=-25.000002
	f32[2792 >> 2]=-26.000002
	f32[2796 >> 2]=-27.000002
	f32[2800 >> 2]=-28.000002
	f32[2804 >> 2]=-29.000002
	f32[2808 >> 2]=-30.000002
	f32[2812 >> 2]=-31.000002
	f32[2816 >> 2]=-0.12500001
	f32[2820 >> 2]=-0.13281251
	f32[2824 >> 2]=-0.14062501
	f32[2828 >> 2]=-0.14843751
	f32[2832 >> 2]=-0.15625001
	f32[2836 >> 2]=-0.16406251
	f32[2840 >> 2]=-0.17187501
	f32[2844 >> 2]=-0.17968751
	f32[2848 >> 2]=-0.18750001
	f32[2852 >> 2]=-0.19531251
	f32[2856 >> 2]=-0.20312501
	f32[2860 >> 2]=-0.21093751
	f32[2864 >> 2]=-0.21875001
	f32[2868 >> 2]=-0.22656251
	f32[2872 >> 2]=-0.23437501
	f32[2876 >> 2]=-0.24218751
	f32[2880 >> 2]=-0.25000003
	f32[2884 >> 2]=-0.26562503
	f32[2888 >> 2]=-0.28125003
	f32[2892 >> 2]=-0.29687503
	f32[2896 >> 2]=-0.31250003
	f32[2900 >> 2]=-0.32812503
	f32[2904 >> 2]=-0.34375003
	f32[2908 >> 2]=-0.35937503
	f32[2912 >> 2]=-0.37500003
	f32[2916 >> 2]=-0.39062503
	f32[2920 >> 2]=-0.40625003
	f32[2924 >> 2]=-0.42187503
	f32[2928 >> 2]=-0.43750003
	f32[2932 >> 2]=-0.45312503
	f32[2936 >> 2]=-0.46875003
	f32[2940 >> 2]=-0.48437503
	f32[2940 >> 2]=-0.48437503
	f32[2944 >> 2]=-0.50000006
	f32[2948 >> 2]=-0.53125006
	f32[2952 >> 2]=-0.56250006
	f32[2956 >> 2]=-0.59375006
	f32[2960 >> 2]=-0.62500006
	f32[2964 >> 2]=-0.65625006
	f32[2968 >> 2]=-0.68750006
	f32[2972 >> 2]=-0.71875006
	f32[2976 >> 2]=-0.75000006
	f32[2980 >> 2]=-0.78125006
	f32[2984 >> 2]=-0.81250006
	f32[2988 >> 2]=-0.84375006
	f32[2992 >> 2]=-0.87500006
	f32[2996 >> 2]=-0.90625006
	f32[3000 >> 2]=-0.93750006
	f32[3004 >> 2]=-0.96875006
	f32[3008 >> 2]=-1.0000001
	f32[3012 >> 2]=-1.0625001
	f32[3016 >> 2]=-1.1250001
	f32[3020 >> 2]=-1.1875001
	f32[3024 >> 2]=-1.2500001
	f32[3028 >> 2]=-1.3125001
	f32[3032 >> 2]=-1.3750001
	f32[3036 >> 2]=-1.4375001
	f32[3040 >> 2]=-1.5000001
	f32[3044 >> 2]=-1.5625001
	f32[3048 >> 2]=-1.6250001
	f32[3052 >> 2]=-1.6875001
	f32[3056 >> 2]=-1.7500001
	f32[3060 >> 2]=-1.8125001
	f32[3064 >> 2]=-1.8750001
	f32[3068 >> 2]=-1.9375001

	// Just the highest zero bit set.
	f32[3073 >> 2]=2.0625
	f32[3073 >> 2]=2.0625
	f32[3077 >> 2]=2.1875
	f32[3081 >> 2]=2.3125
	f32[3085 >> 2]=2.4375
	f32[3089 >> 2]=2.5625
	f32[3093 >> 2]=2.6875
	f32[3097 >> 2]=2.8125
	f32[3101 >> 2]=2.9375
	f32[3105 >> 2]=3.0625
	f32[3109 >> 2]=3.1875
	f32[3113 >> 2]=3.3125
	f32[3117 >> 2]=3.4375
	f32[3121 >> 2]=3.5625
	f32[3125 >> 2]=3.6875
	f32[3129 >> 2]=3.8125
	f32[3133 >> 2]=3.9375
	f32[3137 >> 2]=4.125
	f32[3141 >> 2]=4.375
	f32[3145 >> 2]=4.625
	f32[3149 >> 2]=4.875
	f32[3153 >> 2]=5.125
	f32[3157 >> 2]=5.375
	f32[3161 >> 2]=5.625
	f32[3165 >> 2]=5.875
	f32[3169 >> 2]=6.125
	f32[3173 >> 2]=6.375
	f32[3177 >> 2]=6.625
	f32[3181 >> 2]=6.875
	f32[3185 >> 2]=7.125
	f32[3189 >> 2]=7.375
	f32[3193 >> 2]=7.625
	f32[3197 >> 2]=7.875
	f32[3201 >> 2]=8.25
	f32[3205 >> 2]=8.75
	f32[3209 >> 2]=9.25
	f32[3213 >> 2]=9.75
	f32[3217 >> 2]=10.25
	f32[3221 >> 2]=10.75
	f32[3225 >> 2]=11.25
	f32[3229 >> 2]=11.75
	f32[3233 >> 2]=12.25
	f32[3237 >> 2]=12.75
	f32[3241 >> 2]=13.25
	f32[3245 >> 2]=13.75
	f32[3249 >> 2]=14.25
	f32[3253 >> 2]=14.75
	f32[3257 >> 2]=15.25
	f32[3261 >> 2]=15.75
	f32[3265 >> 2]=16.5
	f32[3269 >> 2]=17.5
	f32[3273 >> 2]=18.5
	f32[3277 >> 2]=19.5
	f32[3281 >> 2]=20.5
	f32[3285 >> 2]=21.5
	f32[3289 >> 2]=22.5
	f32[3293 >> 2]=23.5
	f32[3297 >> 2]=24.5
	f32[3301 >> 2]=25.5
	f32[3305 >> 2]=26.5
	f32[3309 >> 2]=27.5
	f32[3313 >> 2]=28.5
	f32[3317 >> 2]=29.5
	f32[3321 >> 2]=30.5
	f32[3325 >> 2]=31.5
	f32[3329 >> 2]=0.12890625
	f32[3333 >> 2]=0.13671875
	f32[3337 >> 2]=0.14453125
	f32[3341 >> 2]=0.15234375
	f32[3345 >> 2]=0.16015625
	f32[3349 >> 2]=0.16796875
	f32[3353 >> 2]=0.17578125
	f32[3357 >> 2]=0.18359375
	f32[3361 >> 2]=0.19140625
	f32[3365 >> 2]=0.19921875
	f32[3369 >> 2]=0.20703125
	f32[3373 >> 2]=0.21484375
	f32[3377 >> 2]=0.22265625
	f32[3381 >> 2]=0.23046875
	f32[3385 >> 2]=0.23828125
	f32[3389 >> 2]=0.24609375
	f32[3393 >> 2]=0.2578125
	f32[3397 >> 2]=0.2734375
	f32[3401 >> 2]=0.2890625
	f32[3405 >> 2]=0.3046875
	f32[3409 >> 2]=0.3203125
	f32[3413 >> 2]=0.3359375
	f32[3417 >> 2]=0.3515625
	f32[3421 >> 2]=0.3671875
	f32[3425 >> 2]=0.3828125
	f32[3429 >> 2]=0.3984375
	f32[3433 >> 2]=0.4140625
	f32[3437 >> 2]=0.4296875
	f32[3441 >> 2]=0.4453125
	f32[3445 >> 2]=0.4609375
	f32[3449 >> 2]=0.4765625
	f32[3453 >> 2]=0.4921875
	f32[3457 >> 2]=0.515625
	f32[3461 >> 2]=0.546875
	f32[3465 >> 2]=0.578125
	f32[3469 >> 2]=0.609375
	f32[3473 >> 2]=0.640625
	f32[3473 >> 2]=0.640625
	f32[3477 >> 2]=0.671875
	f32[3481 >> 2]=0.703125
	f32[3485 >> 2]=0.734375
	f32[3489 >> 2]=0.765625
	f32[3493 >> 2]=0.796875
	f32[3497 >> 2]=0.828125
	f32[3501 >> 2]=0.859375
	f32[3505 >> 2]=0.890625
	f32[3509 >> 2]=0.921875
	f32[3513 >> 2]=0.953125
	f32[3517 >> 2]=0.984375
	f32[3521 >> 2]=1.03125
	f32[3525 >> 2]=1.09375
	f32[3529 >> 2]=1.15625
	f32[3533 >> 2]=1.21875
	f32[3537 >> 2]=1.28125
	f32[3541 >> 2]=1.34375
	f32[3545 >> 2]=1.40625
	f32[3549 >> 2]=1.46875
	f32[3553 >> 2]=1.53125
	f32[3557 >> 2]=1.59375
	f32[3561 >> 2]=1.65625
	f32[3565 >> 2]=1.71875
	f32[3569 >> 2]=1.78125
	f32[3573 >> 2]=1.84375
	f32[3577 >> 2]=1.90625
	f32[3581 >> 2]=1.96875
	f32[3585 >> 2]=-2.0625
	f32[3589 >> 2]=-2.1875
	f32[3593 >> 2]=-2.3125
	f32[3597 >> 2]=-2.4375
	f32[3601 >> 2]=-2.5625
	f32[3605 >> 2]=-2.6875
	f32[3609 >> 2]=-2.8125
	f32[3613 >> 2]=-2.9375
	f32[3617 >> 2]=-3.0625
	f32[3621 >> 2]=-3.1875
	f32[3625 >> 2]=-3.3125
	f32[3629 >> 2]=-3.4375
	f32[3633 >> 2]=-3.5625
	f32[3637 >> 2]=-3.6875
	f32[3641 >> 2]=-3.8125
	f32[3645 >> 2]=-3.9375
	f32[3649 >> 2]=-4.125
	f32[3653 >> 2]=-4.375
	f32[3657 >> 2]=-4.625
	f32[3661 >> 2]=-4.875
	f32[3665 >> 2]=-5.125
	f32[3669 >> 2]=-5.375
	f32[3673 >> 2]=-5.625
	f32[3677 >> 2]=-5.875
	f32[3681 >> 2]=-6.125
	f32[3685 >> 2]=-6.375
	f32[3689 >> 2]=-6.625
	f32[3693 >> 2]=-6.875
	f32[3697 >> 2]=-7.125
	f32[3701 >> 2]=-7.375
	f32[3705 >> 2]=-7.625
	f32[3709 >> 2]=-7.875
	f32[3713 >> 2]=-8.25
	f32[3717 >> 2]=-8.75
	f32[3721 >> 2]=-9.25
	f32[3725 >> 2]=-9.75
	f32[3729 >> 2]=-10.25
	f32[3733 >> 2]=-10.75
	f32[3737 >> 2]=-11.25
	f32[3741 >> 2]=-11.75
	f32[3745 >> 2]=-12.25
	f32[3749 >> 2]=-12.75
	f32[3753 >> 2]=-13.25
	f32[3757 >> 2]=-13.75
	f32[3761 >> 2]=-14.25
	f32[3765 >> 2]=-14.75
	f32[3769 >> 2]=-15.25
	f32[3773 >> 2]=-15.75
	f32[3777 >> 2]=-16.5
	f32[3781 >> 2]=-17.5
	f32[3785 >> 2]=-18.5
	f32[3789 >> 2]=-19.5
	f32[3793 >> 2]=-20.5
	f32[3797 >> 2]=-21.5
	f32[3801 >> 2]=-22.5
	f32[3805 >> 2]=-23.5
	f32[3809 >> 2]=-24.5
	f32[3813 >> 2]=-25.5
	f32[3817 >> 2]=-26.5
	f32[3821 >> 2]=-27.5
	f32[3825 >> 2]=-28.5
	f32[3829 >> 2]=-29.5
	f32[3833 >> 2]=-30.5
	f32[3837 >> 2]=-31.5
	f32[3841 >> 2]=-0.12890625
	f32[3845 >> 2]=-0.13671875
	f32[3849 >> 2]=-0.14453125
	f32[3853 >> 2]=-0.15234375
	f32[3857 >> 2]=-0.16015625
	f32[3861 >> 2]=-0.16796875
	f32[3865 >> 2]=-0.17578125
	f32[3869 >> 2]=-0.18359375
	f32[3873 >> 2]=-0.19140625
	f32[3873 >> 2]=-0.19140625
	f32[3877 >> 2]=-0.19921875
	f32[3881 >> 2]=-0.20703125
	f32[3885 >> 2]=-0.21484375
	f32[3889 >> 2]=-0.22265625
	f32[3893 >> 2]=-0.23046875
	f32[3897 >> 2]=-0.23828125
	f32[3901 >> 2]=-0.24609375
	f32[3905 >> 2]=-0.2578125
	f32[3909 >> 2]=-0.2734375
	f32[3913 >> 2]=-0.2890625
	f32[3917 >> 2]=-0.3046875
	f32[3921 >> 2]=-0.3203125
	f32[3925 >> 2]=-0.3359375
	f32[3929 >> 2]=-0.3515625
	f32[3933 >> 2]=-0.3671875
	f32[3937 >> 2]=-0.3828125
	f32[3941 >> 2]=-0.3984375
	f32[3945 >> 2]=-0.4140625
	f32[3949 >> 2]=-0.4296875
	f32[3953 >> 2]=-0.4453125
	f32[3957 >> 2]=-0.4609375
	f32[3961 >> 2]=-0.4765625
	f32[3965 >> 2]=-0.4921875
	f32[3969 >> 2]=-0.515625
	f32[3973 >> 2]=-0.546875
	f32[3977 >> 2]=-0.578125
	f32[3981 >> 2]=-0.609375
	f32[3985 >> 2]=-0.640625
	f32[3989 >> 2]=-0.671875
	f32[3993 >> 2]=-0.703125
	f32[3997 >> 2]=-0.734375
	f32[4001 >> 2]=-0.765625
	f32[4005 >> 2]=-0.796875
	f32[4009 >> 2]=-0.828125
	f32[4013 >> 2]=-0.859375
	f32[4017 >> 2]=-0.890625
	f32[4021 >> 2]=-0.921875
	f32[4025 >> 2]=-0.953125
	f32[4029 >> 2]=-0.984375
	f32[4033 >> 2]=-1.03125
	f32[4037 >> 2]=-1.09375
	f32[4041 >> 2]=-1.15625
	f32[4045 >> 2]=-1.21875
	f32[4049 >> 2]=-1.28125
	f32[4053 >> 2]=-1.34375
	f32[4057 >> 2]=-1.40625
	f32[4061 >> 2]=-1.46875
	f32[4065 >> 2]=-1.53125
	f32[4069 >> 2]=-1.59375
	f32[4073 >> 2]=-1.65625
	f32[4077 >> 2]=-1.71875
	f32[4081 >> 2]=-1.78125
	f32[4085 >> 2]=-1.84375
	f32[4089 >> 2]=-1.90625
	f32[4093 >> 2]=-1.96875
    };

    return {
	fn1: fn1
    };
};

var asm = testmod(this, {}, buffer);

asm.fn1()

var f32=new Float32Array(buffer);
assertEq(f32[0 >> 2], 2.0)
assertEq(f32[4 >> 2], 2.125)
assertEq(f32[8 >> 2], 2.25)
assertEq(f32[12 >> 2], 2.375)
assertEq(f32[16 >> 2], 2.5)
assertEq(f32[20 >> 2], 2.625)
assertEq(f32[24 >> 2], 2.75)
assertEq(f32[28 >> 2], 2.875)
assertEq(f32[32 >> 2], 3.0)
assertEq(f32[36 >> 2], 3.125)
assertEq(f32[40 >> 2], 3.25)
assertEq(f32[44 >> 2], 3.375)
assertEq(f32[48 >> 2], 3.5)
assertEq(f32[52 >> 2], 3.625)
assertEq(f32[56 >> 2], 3.75)
assertEq(f32[60 >> 2], 3.875)
assertEq(f32[64 >> 2], 4.0)
assertEq(f32[68 >> 2], 4.25)
assertEq(f32[72 >> 2], 4.5)
assertEq(f32[76 >> 2], 4.75)
assertEq(f32[80 >> 2], 5.0)
assertEq(f32[84 >> 2], 5.25)
assertEq(f32[88 >> 2], 5.5)
assertEq(f32[92 >> 2], 5.75)
assertEq(f32[96 >> 2], 6.0)
assertEq(f32[100 >> 2], 6.25)
assertEq(f32[104 >> 2], 6.5)
assertEq(f32[108 >> 2], 6.75)
assertEq(f32[112 >> 2], 7.0)
assertEq(f32[116 >> 2], 7.25)
assertEq(f32[120 >> 2], 7.5)
assertEq(f32[124 >> 2], 7.75)
assertEq(f32[128 >> 2], 8.0)
assertEq(f32[132 >> 2], 8.5)
assertEq(f32[136 >> 2], 9.0)
assertEq(f32[140 >> 2], 9.5)
assertEq(f32[144 >> 2], 10.0)
assertEq(f32[148 >> 2], 10.5)
assertEq(f32[152 >> 2], 11.0)
assertEq(f32[156 >> 2], 11.5)
assertEq(f32[160 >> 2], 12.0)
assertEq(f32[164 >> 2], 12.5)
assertEq(f32[168 >> 2], 13.0)
assertEq(f32[172 >> 2], 13.5)
assertEq(f32[176 >> 2], 14.0)
assertEq(f32[180 >> 2], 14.5)
assertEq(f32[184 >> 2], 15.0)
assertEq(f32[188 >> 2], 15.5)
assertEq(f32[192 >> 2], 16.0)
assertEq(f32[196 >> 2], 17.0)
assertEq(f32[200 >> 2], 18.0)
assertEq(f32[204 >> 2], 19.0)
assertEq(f32[208 >> 2], 20.0)
assertEq(f32[212 >> 2], 21.0)
assertEq(f32[216 >> 2], 22.0)
assertEq(f32[220 >> 2], 23.0)
assertEq(f32[224 >> 2], 24.0)
assertEq(f32[228 >> 2], 25.0)
assertEq(f32[232 >> 2], 26.0)
assertEq(f32[236 >> 2], 27.0)
assertEq(f32[240 >> 2], 28.0)
assertEq(f32[244 >> 2], 29.0)
assertEq(f32[248 >> 2], 30.0)
assertEq(f32[252 >> 2], 31.0)
assertEq(f32[256 >> 2], 0.125)
assertEq(f32[260 >> 2], 0.1328125)
assertEq(f32[264 >> 2], 0.140625)
assertEq(f32[268 >> 2], 0.1484375)
assertEq(f32[272 >> 2], 0.15625)
assertEq(f32[276 >> 2], 0.1640625)
assertEq(f32[280 >> 2], 0.171875)
assertEq(f32[284 >> 2], 0.1796875)
assertEq(f32[288 >> 2], 0.1875)
assertEq(f32[292 >> 2], 0.1953125)
assertEq(f32[296 >> 2], 0.203125)
assertEq(f32[300 >> 2], 0.2109375)
assertEq(f32[304 >> 2], 0.21875)
assertEq(f32[308 >> 2], 0.2265625)
assertEq(f32[312 >> 2], 0.234375)
assertEq(f32[316 >> 2], 0.2421875)
assertEq(f32[320 >> 2], 0.25)
assertEq(f32[324 >> 2], 0.265625)
assertEq(f32[328 >> 2], 0.28125)
assertEq(f32[332 >> 2], 0.296875)
assertEq(f32[336 >> 2], 0.3125)
assertEq(f32[340 >> 2], 0.328125)
assertEq(f32[344 >> 2], 0.34375)
assertEq(f32[348 >> 2], 0.359375)
assertEq(f32[348 >> 2], 0.359375)
assertEq(f32[352 >> 2], 0.375)
assertEq(f32[356 >> 2], 0.390625)
assertEq(f32[360 >> 2], 0.40625)
assertEq(f32[364 >> 2], 0.421875)
assertEq(f32[368 >> 2], 0.4375)
assertEq(f32[372 >> 2], 0.453125)
assertEq(f32[376 >> 2], 0.46875)
assertEq(f32[380 >> 2], 0.484375)
assertEq(f32[384 >> 2], 0.5)
assertEq(f32[388 >> 2], 0.53125)
assertEq(f32[392 >> 2], 0.5625)
assertEq(f32[396 >> 2], 0.59375)
assertEq(f32[400 >> 2], 0.625)
assertEq(f32[404 >> 2], 0.65625)
assertEq(f32[408 >> 2], 0.6875)
assertEq(f32[412 >> 2], 0.71875)
assertEq(f32[416 >> 2], 0.75)
assertEq(f32[420 >> 2], 0.78125)
assertEq(f32[424 >> 2], 0.8125)
assertEq(f32[428 >> 2], 0.84375)
assertEq(f32[432 >> 2], 0.875)
assertEq(f32[436 >> 2], 0.90625)
assertEq(f32[440 >> 2], 0.9375)
assertEq(f32[444 >> 2], 0.96875)
assertEq(f32[448 >> 2], 1.0)
assertEq(f32[452 >> 2], 1.0625)
assertEq(f32[456 >> 2], 1.125)
assertEq(f32[460 >> 2], 1.1875)
assertEq(f32[464 >> 2], 1.25)
assertEq(f32[468 >> 2], 1.3125)
assertEq(f32[472 >> 2], 1.375)
assertEq(f32[476 >> 2], 1.4375)
assertEq(f32[480 >> 2], 1.5)
assertEq(f32[484 >> 2], 1.5625)
assertEq(f32[488 >> 2], 1.625)
assertEq(f32[492 >> 2], 1.6875)
assertEq(f32[496 >> 2], 1.75)
assertEq(f32[500 >> 2], 1.8125)
assertEq(f32[504 >> 2], 1.875)
assertEq(f32[508 >> 2], 1.9375)
assertEq(f32[512 >> 2], -2.0)
assertEq(f32[516 >> 2], -2.125)
assertEq(f32[520 >> 2], -2.25)
assertEq(f32[524 >> 2], -2.375)
assertEq(f32[528 >> 2], -2.5)
assertEq(f32[532 >> 2], -2.625)
assertEq(f32[536 >> 2], -2.75)
assertEq(f32[540 >> 2], -2.875)
assertEq(f32[544 >> 2], -3.0)
assertEq(f32[548 >> 2], -3.125)
assertEq(f32[552 >> 2], -3.25)
assertEq(f32[556 >> 2], -3.375)
assertEq(f32[560 >> 2], -3.5)
assertEq(f32[564 >> 2], -3.625)
assertEq(f32[568 >> 2], -3.75)
assertEq(f32[572 >> 2], -3.875)
assertEq(f32[576 >> 2], -4.0)
assertEq(f32[580 >> 2], -4.25)
assertEq(f32[584 >> 2], -4.5)
assertEq(f32[588 >> 2], -4.75)
assertEq(f32[592 >> 2], -5.0)
assertEq(f32[596 >> 2], -5.25)
assertEq(f32[600 >> 2], -5.5)
assertEq(f32[604 >> 2], -5.75)
assertEq(f32[608 >> 2], -6.0)
assertEq(f32[612 >> 2], -6.25)
assertEq(f32[616 >> 2], -6.5)
assertEq(f32[620 >> 2], -6.75)
assertEq(f32[624 >> 2], -7.0)
assertEq(f32[628 >> 2], -7.25)
assertEq(f32[632 >> 2], -7.5)
assertEq(f32[636 >> 2], -7.75)
assertEq(f32[640 >> 2], -8.0)
assertEq(f32[644 >> 2], -8.5)
assertEq(f32[648 >> 2], -9.0)
assertEq(f32[652 >> 2], -9.5)
assertEq(f32[656 >> 2], -10.0)
assertEq(f32[660 >> 2], -10.5)
assertEq(f32[664 >> 2], -11.0)
assertEq(f32[668 >> 2], -11.5)
assertEq(f32[672 >> 2], -12.0)
assertEq(f32[676 >> 2], -12.5)
assertEq(f32[680 >> 2], -13.0)
assertEq(f32[684 >> 2], -13.5)
assertEq(f32[688 >> 2], -14.0)
assertEq(f32[692 >> 2], -14.5)
assertEq(f32[696 >> 2], -15.0)
assertEq(f32[700 >> 2], -15.5)
assertEq(f32[704 >> 2], -16.0)
assertEq(f32[708 >> 2], -17.0)
assertEq(f32[712 >> 2], -18.0)
assertEq(f32[716 >> 2], -19.0)
assertEq(f32[720 >> 2], -20.0)
assertEq(f32[724 >> 2], -21.0)
assertEq(f32[728 >> 2], -22.0)
assertEq(f32[732 >> 2], -23.0)
assertEq(f32[736 >> 2], -24.0)
assertEq(f32[740 >> 2], -25.0)
assertEq(f32[744 >> 2], -26.0)
assertEq(f32[748 >> 2], -27.0)
assertEq(f32[748 >> 2], -27.0)
assertEq(f32[752 >> 2], -28.0)
assertEq(f32[756 >> 2], -29.0)
assertEq(f32[760 >> 2], -30.0)
assertEq(f32[764 >> 2], -31.0)
assertEq(f32[768 >> 2], -0.125)
assertEq(f32[772 >> 2], -0.1328125)
assertEq(f32[776 >> 2], -0.140625)
assertEq(f32[780 >> 2], -0.1484375)
assertEq(f32[784 >> 2], -0.15625)
assertEq(f32[788 >> 2], -0.1640625)
assertEq(f32[792 >> 2], -0.171875)
assertEq(f32[796 >> 2], -0.1796875)
assertEq(f32[800 >> 2], -0.1875)
assertEq(f32[804 >> 2], -0.1953125)
assertEq(f32[808 >> 2], -0.203125)
assertEq(f32[812 >> 2], -0.2109375)
assertEq(f32[816 >> 2], -0.21875)
assertEq(f32[820 >> 2], -0.2265625)
assertEq(f32[824 >> 2], -0.234375)
assertEq(f32[828 >> 2], -0.2421875)
assertEq(f32[832 >> 2], -0.25)
assertEq(f32[836 >> 2], -0.265625)
assertEq(f32[840 >> 2], -0.28125)
assertEq(f32[844 >> 2], -0.296875)
assertEq(f32[848 >> 2], -0.3125)
assertEq(f32[852 >> 2], -0.328125)
assertEq(f32[856 >> 2], -0.34375)
assertEq(f32[860 >> 2], -0.359375)
assertEq(f32[864 >> 2], -0.375)
assertEq(f32[868 >> 2], -0.390625)
assertEq(f32[872 >> 2], -0.40625)
assertEq(f32[876 >> 2], -0.421875)
assertEq(f32[880 >> 2], -0.4375)
assertEq(f32[884 >> 2], -0.453125)
assertEq(f32[888 >> 2], -0.46875)
assertEq(f32[892 >> 2], -0.484375)
assertEq(f32[896 >> 2], -0.5)
assertEq(f32[900 >> 2], -0.53125)
assertEq(f32[904 >> 2], -0.5625)
assertEq(f32[908 >> 2], -0.59375)
assertEq(f32[912 >> 2], -0.625)
assertEq(f32[916 >> 2], -0.65625)
assertEq(f32[920 >> 2], -0.6875)
assertEq(f32[924 >> 2], -0.71875)
assertEq(f32[928 >> 2], -0.75)
assertEq(f32[932 >> 2], -0.78125)
assertEq(f32[936 >> 2], -0.8125)
assertEq(f32[940 >> 2], -0.84375)
assertEq(f32[944 >> 2], -0.875)
assertEq(f32[948 >> 2], -0.90625)
assertEq(f32[952 >> 2], -0.9375)
assertEq(f32[956 >> 2], -0.96875)
assertEq(f32[960 >> 2], -1.0)
assertEq(f32[964 >> 2], -1.0625)
assertEq(f32[968 >> 2], -1.125)
assertEq(f32[972 >> 2], -1.1875)
assertEq(f32[976 >> 2], -1.25)
assertEq(f32[980 >> 2], -1.3125)
assertEq(f32[984 >> 2], -1.375)
assertEq(f32[988 >> 2], -1.4375)
assertEq(f32[992 >> 2], -1.5)
assertEq(f32[996 >> 2], -1.5625)
assertEq(f32[1000 >> 2], -1.625)
assertEq(f32[1004 >> 2], -1.6875)
assertEq(f32[1008 >> 2], -1.75)
assertEq(f32[1012 >> 2], -1.8125)
assertEq(f32[1016 >> 2], -1.875)
assertEq(f32[1020 >> 2], -1.9375)

assertEq(f32[1024 >> 2], 2.124999761581421)
assertEq(f32[1028 >> 2], 2.249999761581421)
assertEq(f32[1032 >> 2], 2.374999761581421)
assertEq(f32[1036 >> 2], 2.499999761581421)
assertEq(f32[1040 >> 2], 2.624999761581421)
assertEq(f32[1044 >> 2], 2.749999761581421)
assertEq(f32[1048 >> 2], 2.874999761581421)
assertEq(f32[1052 >> 2], 2.999999761581421)
assertEq(f32[1056 >> 2], 3.124999761581421)
assertEq(f32[1060 >> 2], 3.249999761581421)
assertEq(f32[1064 >> 2], 3.374999761581421)
assertEq(f32[1068 >> 2], 3.499999761581421)
assertEq(f32[1072 >> 2], 3.624999761581421)
assertEq(f32[1076 >> 2], 3.749999761581421)
assertEq(f32[1080 >> 2], 3.874999761581421)
assertEq(f32[1084 >> 2], 3.999999761581421)
assertEq(f32[1088 >> 2], 4.249999523162842)
assertEq(f32[1092 >> 2], 4.499999523162842)
assertEq(f32[1096 >> 2], 4.749999523162842)
assertEq(f32[1100 >> 2], 4.999999523162842)
assertEq(f32[1104 >> 2], 5.249999523162842)
assertEq(f32[1108 >> 2], 5.499999523162842)
assertEq(f32[1112 >> 2], 5.749999523162842)
assertEq(f32[1116 >> 2], 5.999999523162842)
assertEq(f32[1120 >> 2], 6.249999523162842)
assertEq(f32[1124 >> 2], 6.499999523162842)
assertEq(f32[1128 >> 2], 6.749999523162842)
assertEq(f32[1132 >> 2], 6.999999523162842)
assertEq(f32[1136 >> 2], 7.249999523162842)
assertEq(f32[1140 >> 2], 7.499999523162842)
assertEq(f32[1144 >> 2], 7.749999523162842)
assertEq(f32[1148 >> 2], 7.999999523162842)
assertEq(f32[1152 >> 2], 8.499999046325684)
assertEq(f32[1156 >> 2], 8.999999046325684)
assertEq(f32[1160 >> 2], 9.499999046325684)
assertEq(f32[1164 >> 2], 9.999999046325684)
assertEq(f32[1168 >> 2], 10.499999046325684)
assertEq(f32[1172 >> 2], 10.999999046325684)
assertEq(f32[1176 >> 2], 11.499999046325684)
assertEq(f32[1180 >> 2], 11.999999046325684)
assertEq(f32[1184 >> 2], 12.499999046325684)
assertEq(f32[1184 >> 2], 12.499999046325684)
assertEq(f32[1188 >> 2], 12.999999046325684)
assertEq(f32[1192 >> 2], 13.499999046325684)
assertEq(f32[1196 >> 2], 13.999999046325684)
assertEq(f32[1200 >> 2], 14.499999046325684)
assertEq(f32[1204 >> 2], 14.999999046325684)
assertEq(f32[1208 >> 2], 15.499999046325684)
assertEq(f32[1212 >> 2], 15.999999046325684)
assertEq(f32[1216 >> 2], 16.999998092651367)
assertEq(f32[1220 >> 2], 17.999998092651367)
assertEq(f32[1224 >> 2], 18.999998092651367)
assertEq(f32[1228 >> 2], 19.999998092651367)
assertEq(f32[1232 >> 2], 20.999998092651367)
assertEq(f32[1236 >> 2], 21.999998092651367)
assertEq(f32[1240 >> 2], 22.999998092651367)
assertEq(f32[1244 >> 2], 23.999998092651367)
assertEq(f32[1248 >> 2], 24.999998092651367)
assertEq(f32[1252 >> 2], 25.999998092651367)
assertEq(f32[1256 >> 2], 26.999998092651367)
assertEq(f32[1260 >> 2], 27.999998092651367)
assertEq(f32[1264 >> 2], 28.999998092651367)
assertEq(f32[1268 >> 2], 29.999998092651367)
assertEq(f32[1272 >> 2], 30.999998092651367)
assertEq(f32[1276 >> 2], 31.999998092651367)
assertEq(f32[1280 >> 2], 0.1328124850988388)
assertEq(f32[1284 >> 2], 0.1406249850988388)
assertEq(f32[1288 >> 2], 0.1484374850988388)
assertEq(f32[1292 >> 2], 0.1562499850988388)
assertEq(f32[1296 >> 2], 0.1640624850988388)
assertEq(f32[1300 >> 2], 0.1718749850988388)
assertEq(f32[1304 >> 2], 0.1796874850988388)
assertEq(f32[1308 >> 2], 0.1874999850988388)
assertEq(f32[1312 >> 2], 0.1953124850988388)
assertEq(f32[1316 >> 2], 0.2031249850988388)
assertEq(f32[1320 >> 2], 0.2109374850988388)
assertEq(f32[1324 >> 2], 0.2187499850988388)
assertEq(f32[1328 >> 2], 0.2265624850988388)
assertEq(f32[1332 >> 2], 0.2343749850988388)
assertEq(f32[1336 >> 2], 0.2421874850988388)
assertEq(f32[1340 >> 2], 0.2499999850988388)
assertEq(f32[1344 >> 2], 0.2656249701976776)
assertEq(f32[1348 >> 2], 0.2812499701976776)
assertEq(f32[1352 >> 2], 0.2968749701976776)
assertEq(f32[1356 >> 2], 0.3124999701976776)
assertEq(f32[1360 >> 2], 0.3281249701976776)
assertEq(f32[1364 >> 2], 0.3437499701976776)
assertEq(f32[1368 >> 2], 0.3593749701976776)
assertEq(f32[1372 >> 2], 0.3749999701976776)
assertEq(f32[1376 >> 2], 0.3906249701976776)
assertEq(f32[1380 >> 2], 0.4062499701976776)
assertEq(f32[1384 >> 2], 0.4218749701976776)
assertEq(f32[1388 >> 2], 0.4374999701976776)
assertEq(f32[1392 >> 2], 0.4531249701976776)
assertEq(f32[1396 >> 2], 0.4687499701976776)
assertEq(f32[1400 >> 2], 0.4843749701976776)
assertEq(f32[1404 >> 2], 0.4999999701976776)
assertEq(f32[1408 >> 2], 0.5312499403953552)
assertEq(f32[1412 >> 2], 0.5624999403953552)
assertEq(f32[1416 >> 2], 0.5937499403953552)
assertEq(f32[1420 >> 2], 0.6249999403953552)
assertEq(f32[1424 >> 2], 0.6562499403953552)
assertEq(f32[1428 >> 2], 0.6874999403953552)
assertEq(f32[1432 >> 2], 0.7187499403953552)
assertEq(f32[1436 >> 2], 0.7499999403953552)
assertEq(f32[1440 >> 2], 0.7812499403953552)
assertEq(f32[1444 >> 2], 0.8124999403953552)
assertEq(f32[1448 >> 2], 0.8437499403953552)
assertEq(f32[1452 >> 2], 0.8749999403953552)
assertEq(f32[1456 >> 2], 0.9062499403953552)
assertEq(f32[1460 >> 2], 0.9374999403953552)
assertEq(f32[1464 >> 2], 0.9687499403953552)
assertEq(f32[1468 >> 2], 0.9999999403953552)
assertEq(f32[1472 >> 2], 1.0624998807907104)
assertEq(f32[1476 >> 2], 1.1249998807907104)
assertEq(f32[1480 >> 2], 1.1874998807907104)
assertEq(f32[1484 >> 2], 1.2499998807907104)
assertEq(f32[1488 >> 2], 1.3124998807907104)
assertEq(f32[1492 >> 2], 1.3749998807907104)
assertEq(f32[1496 >> 2], 1.4374998807907104)
assertEq(f32[1500 >> 2], 1.4999998807907104)
assertEq(f32[1504 >> 2], 1.5624998807907104)
assertEq(f32[1508 >> 2], 1.6249998807907104)
assertEq(f32[1512 >> 2], 1.6874998807907104)
assertEq(f32[1516 >> 2], 1.7499998807907104)
assertEq(f32[1520 >> 2], 1.8124998807907104)
assertEq(f32[1524 >> 2], 1.8749998807907104)
assertEq(f32[1528 >> 2], 1.9374998807907104)
assertEq(f32[1532 >> 2], 1.9999998807907104)
assertEq(f32[1536 >> 2], -2.124999761581421)
assertEq(f32[1540 >> 2], -2.249999761581421)
assertEq(f32[1544 >> 2], -2.374999761581421)
assertEq(f32[1548 >> 2], -2.499999761581421)
assertEq(f32[1552 >> 2], -2.624999761581421)
assertEq(f32[1556 >> 2], -2.749999761581421)
assertEq(f32[1560 >> 2], -2.874999761581421)
assertEq(f32[1564 >> 2], -2.999999761581421)
assertEq(f32[1568 >> 2], -3.124999761581421)
assertEq(f32[1572 >> 2], -3.249999761581421)
assertEq(f32[1576 >> 2], -3.374999761581421)
assertEq(f32[1580 >> 2], -3.499999761581421)
assertEq(f32[1584 >> 2], -3.624999761581421)
assertEq(f32[1584 >> 2], -3.624999761581421)
assertEq(f32[1588 >> 2], -3.749999761581421)
assertEq(f32[1592 >> 2], -3.874999761581421)
assertEq(f32[1596 >> 2], -3.999999761581421)
assertEq(f32[1600 >> 2], -4.249999523162842)
assertEq(f32[1604 >> 2], -4.499999523162842)
assertEq(f32[1608 >> 2], -4.749999523162842)
assertEq(f32[1612 >> 2], -4.999999523162842)
assertEq(f32[1616 >> 2], -5.249999523162842)
assertEq(f32[1620 >> 2], -5.499999523162842)
assertEq(f32[1624 >> 2], -5.749999523162842)
assertEq(f32[1628 >> 2], -5.999999523162842)
assertEq(f32[1632 >> 2], -6.249999523162842)
assertEq(f32[1636 >> 2], -6.499999523162842)
assertEq(f32[1640 >> 2], -6.749999523162842)
assertEq(f32[1644 >> 2], -6.999999523162842)
assertEq(f32[1648 >> 2], -7.249999523162842)
assertEq(f32[1652 >> 2], -7.499999523162842)
assertEq(f32[1656 >> 2], -7.749999523162842)
assertEq(f32[1660 >> 2], -7.999999523162842)
assertEq(f32[1664 >> 2], -8.499999046325684)
assertEq(f32[1668 >> 2], -8.999999046325684)
assertEq(f32[1672 >> 2], -9.499999046325684)
assertEq(f32[1676 >> 2], -9.999999046325684)
assertEq(f32[1680 >> 2], -10.499999046325684)
assertEq(f32[1684 >> 2], -10.999999046325684)
assertEq(f32[1688 >> 2], -11.499999046325684)
assertEq(f32[1692 >> 2], -11.999999046325684)
assertEq(f32[1696 >> 2], -12.499999046325684)
assertEq(f32[1700 >> 2], -12.999999046325684)
assertEq(f32[1704 >> 2], -13.499999046325684)
assertEq(f32[1708 >> 2], -13.999999046325684)
assertEq(f32[1712 >> 2], -14.499999046325684)
assertEq(f32[1716 >> 2], -14.999999046325684)
assertEq(f32[1720 >> 2], -15.499999046325684)
assertEq(f32[1724 >> 2], -15.999999046325684)
assertEq(f32[1728 >> 2], -16.999998092651367)
assertEq(f32[1732 >> 2], -17.999998092651367)
assertEq(f32[1736 >> 2], -18.999998092651367)
assertEq(f32[1740 >> 2], -19.999998092651367)
assertEq(f32[1744 >> 2], -20.999998092651367)
assertEq(f32[1748 >> 2], -21.999998092651367)
assertEq(f32[1752 >> 2], -22.999998092651367)
assertEq(f32[1756 >> 2], -23.999998092651367)
assertEq(f32[1760 >> 2], -24.999998092651367)
assertEq(f32[1764 >> 2], -25.999998092651367)
assertEq(f32[1768 >> 2], -26.999998092651367)
assertEq(f32[1772 >> 2], -27.999998092651367)
assertEq(f32[1776 >> 2], -28.999998092651367)
assertEq(f32[1780 >> 2], -29.999998092651367)
assertEq(f32[1784 >> 2], -30.999998092651367)
assertEq(f32[1788 >> 2], -31.999998092651367)
assertEq(f32[1792 >> 2], -0.1328124850988388)
assertEq(f32[1796 >> 2], -0.1406249850988388)
assertEq(f32[1800 >> 2], -0.1484374850988388)
assertEq(f32[1804 >> 2], -0.1562499850988388)
assertEq(f32[1808 >> 2], -0.1640624850988388)
assertEq(f32[1812 >> 2], -0.1718749850988388)
assertEq(f32[1816 >> 2], -0.1796874850988388)
assertEq(f32[1820 >> 2], -0.1874999850988388)
assertEq(f32[1824 >> 2], -0.1953124850988388)
assertEq(f32[1828 >> 2], -0.2031249850988388)
assertEq(f32[1832 >> 2], -0.2109374850988388)
assertEq(f32[1836 >> 2], -0.2187499850988388)
assertEq(f32[1840 >> 2], -0.2265624850988388)
assertEq(f32[1844 >> 2], -0.2343749850988388)
assertEq(f32[1848 >> 2], -0.2421874850988388)
assertEq(f32[1852 >> 2], -0.2499999850988388)
assertEq(f32[1856 >> 2], -0.2656249701976776)
assertEq(f32[1860 >> 2], -0.2812499701976776)
assertEq(f32[1864 >> 2], -0.2968749701976776)
assertEq(f32[1868 >> 2], -0.3124999701976776)
assertEq(f32[1872 >> 2], -0.3281249701976776)
assertEq(f32[1876 >> 2], -0.3437499701976776)
assertEq(f32[1880 >> 2], -0.3593749701976776)
assertEq(f32[1884 >> 2], -0.3749999701976776)
assertEq(f32[1888 >> 2], -0.3906249701976776)
assertEq(f32[1892 >> 2], -0.4062499701976776)
assertEq(f32[1896 >> 2], -0.4218749701976776)
assertEq(f32[1900 >> 2], -0.4374999701976776)
assertEq(f32[1904 >> 2], -0.4531249701976776)
assertEq(f32[1908 >> 2], -0.4687499701976776)
assertEq(f32[1912 >> 2], -0.4843749701976776)
assertEq(f32[1916 >> 2], -0.4999999701976776)
assertEq(f32[1920 >> 2], -0.5312499403953552)
assertEq(f32[1924 >> 2], -0.5624999403953552)
assertEq(f32[1928 >> 2], -0.5937499403953552)
assertEq(f32[1932 >> 2], -0.6249999403953552)
assertEq(f32[1936 >> 2], -0.6562499403953552)
assertEq(f32[1940 >> 2], -0.6874999403953552)
assertEq(f32[1944 >> 2], -0.7187499403953552)
assertEq(f32[1948 >> 2], -0.7499999403953552)
assertEq(f32[1952 >> 2], -0.7812499403953552)
assertEq(f32[1956 >> 2], -0.8124999403953552)
assertEq(f32[1960 >> 2], -0.8437499403953552)
assertEq(f32[1964 >> 2], -0.8749999403953552)
assertEq(f32[1968 >> 2], -0.9062499403953552)
assertEq(f32[1972 >> 2], -0.9374999403953552)
assertEq(f32[1976 >> 2], -0.9687499403953552)
assertEq(f32[1980 >> 2], -0.9999999403953552)
assertEq(f32[1984 >> 2], -1.0624998807907104)
assertEq(f32[1984 >> 2], -1.0624998807907104)
assertEq(f32[1988 >> 2], -1.1249998807907104)
assertEq(f32[1992 >> 2], -1.1874998807907104)
assertEq(f32[1996 >> 2], -1.2499998807907104)
assertEq(f32[2000 >> 2], -1.3124998807907104)
assertEq(f32[2004 >> 2], -1.3749998807907104)
assertEq(f32[2008 >> 2], -1.4374998807907104)
assertEq(f32[2012 >> 2], -1.4999998807907104)
assertEq(f32[2016 >> 2], -1.5624998807907104)
assertEq(f32[2020 >> 2], -1.6249998807907104)
assertEq(f32[2024 >> 2], -1.6874998807907104)
assertEq(f32[2028 >> 2], -1.7499998807907104)
assertEq(f32[2032 >> 2], -1.8124998807907104)
assertEq(f32[2036 >> 2], -1.8749998807907104)
assertEq(f32[2040 >> 2], -1.9374998807907104)
assertEq(f32[2044 >> 2], -1.9999998807907104)

assertEq(f32[2048 >> 2], 2.000000238418579)
assertEq(f32[2052 >> 2], 2.125000238418579)
assertEq(f32[2056 >> 2], 2.250000238418579)
assertEq(f32[2060 >> 2], 2.375000238418579)
assertEq(f32[2064 >> 2], 2.500000238418579)
assertEq(f32[2068 >> 2], 2.625000238418579)
assertEq(f32[2072 >> 2], 2.750000238418579)
assertEq(f32[2076 >> 2], 2.875000238418579)
assertEq(f32[2080 >> 2], 3.000000238418579)
assertEq(f32[2084 >> 2], 3.125000238418579)
assertEq(f32[2088 >> 2], 3.250000238418579)
assertEq(f32[2092 >> 2], 3.375000238418579)
assertEq(f32[2096 >> 2], 3.500000238418579)
assertEq(f32[2100 >> 2], 3.625000238418579)
assertEq(f32[2104 >> 2], 3.750000238418579)
assertEq(f32[2108 >> 2], 3.875000238418579)
assertEq(f32[2112 >> 2], 4.000000476837158)
assertEq(f32[2116 >> 2], 4.250000476837158)
assertEq(f32[2120 >> 2], 4.500000476837158)
assertEq(f32[2124 >> 2], 4.750000476837158)
assertEq(f32[2128 >> 2], 5.000000476837158)
assertEq(f32[2132 >> 2], 5.250000476837158)
assertEq(f32[2136 >> 2], 5.500000476837158)
assertEq(f32[2140 >> 2], 5.750000476837158)
assertEq(f32[2144 >> 2], 6.000000476837158)
assertEq(f32[2148 >> 2], 6.250000476837158)
assertEq(f32[2152 >> 2], 6.500000476837158)
assertEq(f32[2156 >> 2], 6.750000476837158)
assertEq(f32[2160 >> 2], 7.000000476837158)
assertEq(f32[2164 >> 2], 7.250000476837158)
assertEq(f32[2168 >> 2], 7.500000476837158)
assertEq(f32[2172 >> 2], 7.750000476837158)
assertEq(f32[2176 >> 2], 8.000000953674316)
assertEq(f32[2180 >> 2], 8.500000953674316)
assertEq(f32[2184 >> 2], 9.000000953674316)
assertEq(f32[2188 >> 2], 9.500000953674316)
assertEq(f32[2192 >> 2], 10.000000953674316)
assertEq(f32[2196 >> 2], 10.500000953674316)
assertEq(f32[2200 >> 2], 11.000000953674316)
assertEq(f32[2204 >> 2], 11.500000953674316)
assertEq(f32[2208 >> 2], 12.000000953674316)
assertEq(f32[2212 >> 2], 12.500000953674316)
assertEq(f32[2216 >> 2], 13.000000953674316)
assertEq(f32[2220 >> 2], 13.500000953674316)
assertEq(f32[2224 >> 2], 14.000000953674316)
assertEq(f32[2228 >> 2], 14.500000953674316)
assertEq(f32[2228 >> 2], 14.500000953674316)
assertEq(f32[2232 >> 2], 15.000000953674316)
assertEq(f32[2236 >> 2], 15.500000953674316)
assertEq(f32[2240 >> 2], 16.000001907348633)
assertEq(f32[2244 >> 2], 17.000001907348633)
assertEq(f32[2248 >> 2], 18.000001907348633)
assertEq(f32[2252 >> 2], 19.000001907348633)
assertEq(f32[2256 >> 2], 20.000001907348633)
assertEq(f32[2260 >> 2], 21.000001907348633)
assertEq(f32[2264 >> 2], 22.000001907348633)
assertEq(f32[2268 >> 2], 23.000001907348633)
assertEq(f32[2272 >> 2], 24.000001907348633)
assertEq(f32[2276 >> 2], 25.000001907348633)
assertEq(f32[2280 >> 2], 26.000001907348633)
assertEq(f32[2284 >> 2], 27.000001907348633)
assertEq(f32[2288 >> 2], 28.000001907348633)
assertEq(f32[2292 >> 2], 29.000001907348633)
assertEq(f32[2296 >> 2], 30.000001907348633)
assertEq(f32[2300 >> 2], 31.000001907348633)
assertEq(f32[2304 >> 2], 0.1250000149011612)
assertEq(f32[2308 >> 2], 0.1328125149011612)
assertEq(f32[2312 >> 2], 0.1406250149011612)
assertEq(f32[2316 >> 2], 0.1484375149011612)
assertEq(f32[2320 >> 2], 0.1562500149011612)
assertEq(f32[2324 >> 2], 0.1640625149011612)
assertEq(f32[2328 >> 2], 0.1718750149011612)
assertEq(f32[2332 >> 2], 0.1796875149011612)
assertEq(f32[2336 >> 2], 0.1875000149011612)
assertEq(f32[2340 >> 2], 0.1953125149011612)
assertEq(f32[2344 >> 2], 0.2031250149011612)
assertEq(f32[2348 >> 2], 0.2109375149011612)
assertEq(f32[2352 >> 2], 0.2187500149011612)
assertEq(f32[2356 >> 2], 0.2265625149011612)
assertEq(f32[2360 >> 2], 0.2343750149011612)
assertEq(f32[2364 >> 2], 0.2421875149011612)
assertEq(f32[2368 >> 2], 0.2500000298023224)
assertEq(f32[2372 >> 2], 0.2656250298023224)
assertEq(f32[2376 >> 2], 0.2812500298023224)
assertEq(f32[2380 >> 2], 0.2968750298023224)
assertEq(f32[2384 >> 2], 0.3125000298023224)
assertEq(f32[2388 >> 2], 0.3281250298023224)
assertEq(f32[2392 >> 2], 0.3437500298023224)
assertEq(f32[2396 >> 2], 0.3593750298023224)
assertEq(f32[2400 >> 2], 0.3750000298023224)
assertEq(f32[2404 >> 2], 0.3906250298023224)
assertEq(f32[2408 >> 2], 0.4062500298023224)
assertEq(f32[2412 >> 2], 0.4218750298023224)
assertEq(f32[2416 >> 2], 0.4375000298023224)
assertEq(f32[2420 >> 2], 0.4531250298023224)
assertEq(f32[2424 >> 2], 0.4687500298023224)
assertEq(f32[2428 >> 2], 0.4843750298023224)
assertEq(f32[2432 >> 2], 0.5000000596046448)
assertEq(f32[2436 >> 2], 0.5312500596046448)
assertEq(f32[2440 >> 2], 0.5625000596046448)
assertEq(f32[2444 >> 2], 0.5937500596046448)
assertEq(f32[2448 >> 2], 0.6250000596046448)
assertEq(f32[2452 >> 2], 0.6562500596046448)
assertEq(f32[2456 >> 2], 0.6875000596046448)
assertEq(f32[2460 >> 2], 0.7187500596046448)
assertEq(f32[2464 >> 2], 0.7500000596046448)
assertEq(f32[2468 >> 2], 0.7812500596046448)
assertEq(f32[2472 >> 2], 0.8125000596046448)
assertEq(f32[2476 >> 2], 0.8437500596046448)
assertEq(f32[2480 >> 2], 0.8750000596046448)
assertEq(f32[2484 >> 2], 0.9062500596046448)
assertEq(f32[2488 >> 2], 0.9375000596046448)
assertEq(f32[2492 >> 2], 0.9687500596046448)
assertEq(f32[2496 >> 2], 1.0000001192092896)
assertEq(f32[2500 >> 2], 1.0625001192092896)
assertEq(f32[2504 >> 2], 1.1250001192092896)
assertEq(f32[2508 >> 2], 1.1875001192092896)
assertEq(f32[2512 >> 2], 1.2500001192092896)
assertEq(f32[2516 >> 2], 1.3125001192092896)
assertEq(f32[2520 >> 2], 1.3750001192092896)
assertEq(f32[2524 >> 2], 1.4375001192092896)
assertEq(f32[2528 >> 2], 1.5000001192092896)
assertEq(f32[2532 >> 2], 1.5625001192092896)
assertEq(f32[2536 >> 2], 1.6250001192092896)
assertEq(f32[2540 >> 2], 1.6875001192092896)
assertEq(f32[2544 >> 2], 1.7500001192092896)
assertEq(f32[2548 >> 2], 1.8125001192092896)
assertEq(f32[2552 >> 2], 1.8750001192092896)
assertEq(f32[2556 >> 2], 1.9375001192092896)
assertEq(f32[2560 >> 2], -2.000000238418579)
assertEq(f32[2564 >> 2], -2.125000238418579)
assertEq(f32[2568 >> 2], -2.250000238418579)
assertEq(f32[2572 >> 2], -2.375000238418579)
assertEq(f32[2576 >> 2], -2.500000238418579)
assertEq(f32[2580 >> 2], -2.625000238418579)
assertEq(f32[2584 >> 2], -2.750000238418579)
assertEq(f32[2588 >> 2], -2.875000238418579)
assertEq(f32[2592 >> 2], -3.000000238418579)
assertEq(f32[2596 >> 2], -3.125000238418579)
assertEq(f32[2600 >> 2], -3.250000238418579)
assertEq(f32[2604 >> 2], -3.375000238418579)
assertEq(f32[2608 >> 2], -3.500000238418579)
assertEq(f32[2612 >> 2], -3.625000238418579)
assertEq(f32[2616 >> 2], -3.750000238418579)
assertEq(f32[2620 >> 2], -3.875000238418579)
assertEq(f32[2624 >> 2], -4.000000476837158)
assertEq(f32[2628 >> 2], -4.250000476837158)
assertEq(f32[2628 >> 2], -4.250000476837158)
assertEq(f32[2632 >> 2], -4.500000476837158)
assertEq(f32[2636 >> 2], -4.750000476837158)
assertEq(f32[2640 >> 2], -5.000000476837158)
assertEq(f32[2644 >> 2], -5.250000476837158)
assertEq(f32[2648 >> 2], -5.500000476837158)
assertEq(f32[2652 >> 2], -5.750000476837158)
assertEq(f32[2656 >> 2], -6.000000476837158)
assertEq(f32[2660 >> 2], -6.250000476837158)
assertEq(f32[2664 >> 2], -6.500000476837158)
assertEq(f32[2668 >> 2], -6.750000476837158)
assertEq(f32[2672 >> 2], -7.000000476837158)
assertEq(f32[2676 >> 2], -7.250000476837158)
assertEq(f32[2680 >> 2], -7.500000476837158)
assertEq(f32[2684 >> 2], -7.750000476837158)
assertEq(f32[2688 >> 2], -8.000000953674316)
assertEq(f32[2692 >> 2], -8.500000953674316)
assertEq(f32[2696 >> 2], -9.000000953674316)
assertEq(f32[2700 >> 2], -9.500000953674316)
assertEq(f32[2704 >> 2], -10.000000953674316)
assertEq(f32[2708 >> 2], -10.500000953674316)
assertEq(f32[2712 >> 2], -11.000000953674316)
assertEq(f32[2716 >> 2], -11.500000953674316)
assertEq(f32[2720 >> 2], -12.000000953674316)
assertEq(f32[2724 >> 2], -12.500000953674316)
assertEq(f32[2728 >> 2], -13.000000953674316)
assertEq(f32[2732 >> 2], -13.500000953674316)
assertEq(f32[2736 >> 2], -14.000000953674316)
assertEq(f32[2740 >> 2], -14.500000953674316)
assertEq(f32[2744 >> 2], -15.000000953674316)
assertEq(f32[2748 >> 2], -15.500000953674316)
assertEq(f32[2752 >> 2], -16.000001907348633)
assertEq(f32[2756 >> 2], -17.000001907348633)
assertEq(f32[2760 >> 2], -18.000001907348633)
assertEq(f32[2764 >> 2], -19.000001907348633)
assertEq(f32[2768 >> 2], -20.000001907348633)
assertEq(f32[2772 >> 2], -21.000001907348633)
assertEq(f32[2776 >> 2], -22.000001907348633)
assertEq(f32[2780 >> 2], -23.000001907348633)
assertEq(f32[2784 >> 2], -24.000001907348633)
assertEq(f32[2788 >> 2], -25.000001907348633)
assertEq(f32[2792 >> 2], -26.000001907348633)
assertEq(f32[2796 >> 2], -27.000001907348633)
assertEq(f32[2800 >> 2], -28.000001907348633)
assertEq(f32[2804 >> 2], -29.000001907348633)
assertEq(f32[2808 >> 2], -30.000001907348633)
assertEq(f32[2812 >> 2], -31.000001907348633)
assertEq(f32[2816 >> 2], -0.1250000149011612)
assertEq(f32[2820 >> 2], -0.1328125149011612)
assertEq(f32[2824 >> 2], -0.1406250149011612)
assertEq(f32[2828 >> 2], -0.1484375149011612)
assertEq(f32[2832 >> 2], -0.1562500149011612)
assertEq(f32[2836 >> 2], -0.1640625149011612)
assertEq(f32[2840 >> 2], -0.1718750149011612)
assertEq(f32[2844 >> 2], -0.1796875149011612)
assertEq(f32[2848 >> 2], -0.1875000149011612)
assertEq(f32[2852 >> 2], -0.1953125149011612)
assertEq(f32[2856 >> 2], -0.2031250149011612)
assertEq(f32[2860 >> 2], -0.2109375149011612)
assertEq(f32[2864 >> 2], -0.2187500149011612)
assertEq(f32[2868 >> 2], -0.2265625149011612)
assertEq(f32[2872 >> 2], -0.2343750149011612)
assertEq(f32[2876 >> 2], -0.2421875149011612)
assertEq(f32[2880 >> 2], -0.2500000298023224)
assertEq(f32[2884 >> 2], -0.2656250298023224)
assertEq(f32[2888 >> 2], -0.2812500298023224)
assertEq(f32[2892 >> 2], -0.2968750298023224)
assertEq(f32[2896 >> 2], -0.3125000298023224)
assertEq(f32[2900 >> 2], -0.3281250298023224)
assertEq(f32[2904 >> 2], -0.3437500298023224)
assertEq(f32[2908 >> 2], -0.3593750298023224)
assertEq(f32[2912 >> 2], -0.3750000298023224)
assertEq(f32[2916 >> 2], -0.3906250298023224)
assertEq(f32[2920 >> 2], -0.4062500298023224)
assertEq(f32[2924 >> 2], -0.4218750298023224)
assertEq(f32[2928 >> 2], -0.4375000298023224)
assertEq(f32[2932 >> 2], -0.4531250298023224)
assertEq(f32[2936 >> 2], -0.4687500298023224)
assertEq(f32[2940 >> 2], -0.4843750298023224)
assertEq(f32[2944 >> 2], -0.5000000596046448)
assertEq(f32[2948 >> 2], -0.5312500596046448)
assertEq(f32[2952 >> 2], -0.5625000596046448)
assertEq(f32[2956 >> 2], -0.5937500596046448)
assertEq(f32[2960 >> 2], -0.6250000596046448)
assertEq(f32[2964 >> 2], -0.6562500596046448)
assertEq(f32[2968 >> 2], -0.6875000596046448)
assertEq(f32[2972 >> 2], -0.7187500596046448)
assertEq(f32[2976 >> 2], -0.7500000596046448)
assertEq(f32[2980 >> 2], -0.7812500596046448)
assertEq(f32[2984 >> 2], -0.8125000596046448)
assertEq(f32[2988 >> 2], -0.8437500596046448)
assertEq(f32[2992 >> 2], -0.8750000596046448)
assertEq(f32[2996 >> 2], -0.9062500596046448)
assertEq(f32[3000 >> 2], -0.9375000596046448)
assertEq(f32[3004 >> 2], -0.9687500596046448)
assertEq(f32[3008 >> 2], -1.0000001192092896)
assertEq(f32[3012 >> 2], -1.0625001192092896)
assertEq(f32[3016 >> 2], -1.1250001192092896)
assertEq(f32[3020 >> 2], -1.1875001192092896)
assertEq(f32[3024 >> 2], -1.2500001192092896)
assertEq(f32[3028 >> 2], -1.3125001192092896)
assertEq(f32[3028 >> 2], -1.3125001192092896)
assertEq(f32[3032 >> 2], -1.3750001192092896)
assertEq(f32[3036 >> 2], -1.4375001192092896)
assertEq(f32[3040 >> 2], -1.5000001192092896)
assertEq(f32[3044 >> 2], -1.5625001192092896)
assertEq(f32[3048 >> 2], -1.6250001192092896)
assertEq(f32[3052 >> 2], -1.6875001192092896)
assertEq(f32[3056 >> 2], -1.7500001192092896)
assertEq(f32[3060 >> 2], -1.8125001192092896)
assertEq(f32[3064 >> 2], -1.8750001192092896)
assertEq(f32[3068 >> 2], -1.9375001192092896)

assertEq(f32[3072 >> 2], 2.0625)
assertEq(f32[3076 >> 2], 2.1875)
assertEq(f32[3080 >> 2], 2.3125)
assertEq(f32[3084 >> 2], 2.4375)
assertEq(f32[3088 >> 2], 2.5625)
assertEq(f32[3092 >> 2], 2.6875)
assertEq(f32[3096 >> 2], 2.8125)
assertEq(f32[3100 >> 2], 2.9375)
assertEq(f32[3104 >> 2], 3.0625)
assertEq(f32[3108 >> 2], 3.1875)
assertEq(f32[3112 >> 2], 3.3125)
assertEq(f32[3116 >> 2], 3.4375)
assertEq(f32[3120 >> 2], 3.5625)
assertEq(f32[3124 >> 2], 3.6875)
assertEq(f32[3128 >> 2], 3.8125)
assertEq(f32[3132 >> 2], 3.9375)
assertEq(f32[3136 >> 2], 4.125)
assertEq(f32[3140 >> 2], 4.375)
assertEq(f32[3144 >> 2], 4.625)
assertEq(f32[3148 >> 2], 4.875)
assertEq(f32[3152 >> 2], 5.125)
assertEq(f32[3156 >> 2], 5.375)
assertEq(f32[3160 >> 2], 5.625)
assertEq(f32[3164 >> 2], 5.875)
assertEq(f32[3168 >> 2], 6.125)
assertEq(f32[3172 >> 2], 6.375)
assertEq(f32[3176 >> 2], 6.625)
assertEq(f32[3180 >> 2], 6.875)
assertEq(f32[3184 >> 2], 7.125)
assertEq(f32[3188 >> 2], 7.375)
assertEq(f32[3192 >> 2], 7.625)
assertEq(f32[3196 >> 2], 7.875)
assertEq(f32[3200 >> 2], 8.25)
assertEq(f32[3204 >> 2], 8.75)
assertEq(f32[3208 >> 2], 9.25)
assertEq(f32[3212 >> 2], 9.75)
assertEq(f32[3216 >> 2], 10.25)
assertEq(f32[3220 >> 2], 10.75)
assertEq(f32[3224 >> 2], 11.25)
assertEq(f32[3224 >> 2], 11.25)
assertEq(f32[3228 >> 2], 11.75)
assertEq(f32[3232 >> 2], 12.25)
assertEq(f32[3236 >> 2], 12.75)
assertEq(f32[3240 >> 2], 13.25)
assertEq(f32[3244 >> 2], 13.75)
assertEq(f32[3248 >> 2], 14.25)
assertEq(f32[3252 >> 2], 14.75)
assertEq(f32[3256 >> 2], 15.25)
assertEq(f32[3260 >> 2], 15.75)
assertEq(f32[3264 >> 2], 16.5)
assertEq(f32[3268 >> 2], 17.5)
assertEq(f32[3272 >> 2], 18.5)
assertEq(f32[3276 >> 2], 19.5)
assertEq(f32[3280 >> 2], 20.5)
assertEq(f32[3284 >> 2], 21.5)
assertEq(f32[3288 >> 2], 22.5)
assertEq(f32[3292 >> 2], 23.5)
assertEq(f32[3296 >> 2], 24.5)
assertEq(f32[3300 >> 2], 25.5)
assertEq(f32[3304 >> 2], 26.5)
assertEq(f32[3308 >> 2], 27.5)
assertEq(f32[3312 >> 2], 28.5)
assertEq(f32[3316 >> 2], 29.5)
assertEq(f32[3320 >> 2], 30.5)
assertEq(f32[3324 >> 2], 31.5)
assertEq(f32[3328 >> 2], 0.12890625)
assertEq(f32[3332 >> 2], 0.13671875)
assertEq(f32[3336 >> 2], 0.14453125)
assertEq(f32[3340 >> 2], 0.15234375)
assertEq(f32[3344 >> 2], 0.16015625)
assertEq(f32[3348 >> 2], 0.16796875)
assertEq(f32[3352 >> 2], 0.17578125)
assertEq(f32[3356 >> 2], 0.18359375)
assertEq(f32[3360 >> 2], 0.19140625)
assertEq(f32[3364 >> 2], 0.19921875)
assertEq(f32[3368 >> 2], 0.20703125)
assertEq(f32[3372 >> 2], 0.21484375)
assertEq(f32[3376 >> 2], 0.22265625)
assertEq(f32[3380 >> 2], 0.23046875)
assertEq(f32[3384 >> 2], 0.23828125)
assertEq(f32[3388 >> 2], 0.24609375)
assertEq(f32[3392 >> 2], 0.2578125)
assertEq(f32[3396 >> 2], 0.2734375)
assertEq(f32[3400 >> 2], 0.2890625)
assertEq(f32[3404 >> 2], 0.3046875)
assertEq(f32[3408 >> 2], 0.3203125)
assertEq(f32[3412 >> 2], 0.3359375)
assertEq(f32[3416 >> 2], 0.3515625)
assertEq(f32[3420 >> 2], 0.3671875)
assertEq(f32[3424 >> 2], 0.3828125)
assertEq(f32[3428 >> 2], 0.3984375)
assertEq(f32[3432 >> 2], 0.4140625)
assertEq(f32[3436 >> 2], 0.4296875)
assertEq(f32[3440 >> 2], 0.4453125)
assertEq(f32[3444 >> 2], 0.4609375)
assertEq(f32[3448 >> 2], 0.4765625)
assertEq(f32[3452 >> 2], 0.4921875)
assertEq(f32[3456 >> 2], 0.515625)
assertEq(f32[3460 >> 2], 0.546875)
assertEq(f32[3464 >> 2], 0.578125)
assertEq(f32[3468 >> 2], 0.609375)
assertEq(f32[3472 >> 2], 0.640625)
assertEq(f32[3476 >> 2], 0.671875)
assertEq(f32[3480 >> 2], 0.703125)
assertEq(f32[3484 >> 2], 0.734375)
assertEq(f32[3488 >> 2], 0.765625)
assertEq(f32[3492 >> 2], 0.796875)
assertEq(f32[3496 >> 2], 0.828125)
assertEq(f32[3500 >> 2], 0.859375)
assertEq(f32[3504 >> 2], 0.890625)
assertEq(f32[3508 >> 2], 0.921875)
assertEq(f32[3512 >> 2], 0.953125)
assertEq(f32[3516 >> 2], 0.984375)
assertEq(f32[3520 >> 2], 1.03125)
assertEq(f32[3524 >> 2], 1.09375)
assertEq(f32[3528 >> 2], 1.15625)
assertEq(f32[3532 >> 2], 1.21875)
assertEq(f32[3536 >> 2], 1.28125)
assertEq(f32[3540 >> 2], 1.34375)
assertEq(f32[3544 >> 2], 1.40625)
assertEq(f32[3548 >> 2], 1.46875)
assertEq(f32[3552 >> 2], 1.53125)
assertEq(f32[3556 >> 2], 1.59375)
assertEq(f32[3560 >> 2], 1.65625)
assertEq(f32[3564 >> 2], 1.71875)
assertEq(f32[3568 >> 2], 1.78125)
assertEq(f32[3572 >> 2], 1.84375)
assertEq(f32[3576 >> 2], 1.90625)
assertEq(f32[3580 >> 2], 1.96875)
assertEq(f32[3584 >> 2], -2.0625)
assertEq(f32[3588 >> 2], -2.1875)
assertEq(f32[3592 >> 2], -2.3125)
assertEq(f32[3596 >> 2], -2.4375)
assertEq(f32[3600 >> 2], -2.5625)
assertEq(f32[3604 >> 2], -2.6875)
assertEq(f32[3608 >> 2], -2.8125)
assertEq(f32[3612 >> 2], -2.9375)
assertEq(f32[3616 >> 2], -3.0625)
assertEq(f32[3620 >> 2], -3.1875)
assertEq(f32[3624 >> 2], -3.3125)
assertEq(f32[3624 >> 2], -3.3125)
assertEq(f32[3628 >> 2], -3.4375)
assertEq(f32[3632 >> 2], -3.5625)
assertEq(f32[3636 >> 2], -3.6875)
assertEq(f32[3640 >> 2], -3.8125)
assertEq(f32[3644 >> 2], -3.9375)
assertEq(f32[3648 >> 2], -4.125)
assertEq(f32[3652 >> 2], -4.375)
assertEq(f32[3656 >> 2], -4.625)
assertEq(f32[3660 >> 2], -4.875)
assertEq(f32[3664 >> 2], -5.125)
assertEq(f32[3668 >> 2], -5.375)
assertEq(f32[3672 >> 2], -5.625)
assertEq(f32[3676 >> 2], -5.875)
assertEq(f32[3680 >> 2], -6.125)
assertEq(f32[3684 >> 2], -6.375)
assertEq(f32[3688 >> 2], -6.625)
assertEq(f32[3692 >> 2], -6.875)
assertEq(f32[3696 >> 2], -7.125)
assertEq(f32[3700 >> 2], -7.375)
assertEq(f32[3704 >> 2], -7.625)
assertEq(f32[3708 >> 2], -7.875)
assertEq(f32[3712 >> 2], -8.25)
assertEq(f32[3716 >> 2], -8.75)
assertEq(f32[3720 >> 2], -9.25)
assertEq(f32[3724 >> 2], -9.75)
assertEq(f32[3728 >> 2], -10.25)
assertEq(f32[3732 >> 2], -10.75)
assertEq(f32[3736 >> 2], -11.25)
assertEq(f32[3740 >> 2], -11.75)
assertEq(f32[3744 >> 2], -12.25)
assertEq(f32[3748 >> 2], -12.75)
assertEq(f32[3752 >> 2], -13.25)
assertEq(f32[3756 >> 2], -13.75)
assertEq(f32[3760 >> 2], -14.25)
assertEq(f32[3764 >> 2], -14.75)
assertEq(f32[3768 >> 2], -15.25)
assertEq(f32[3772 >> 2], -15.75)
assertEq(f32[3776 >> 2], -16.5)
assertEq(f32[3780 >> 2], -17.5)
assertEq(f32[3784 >> 2], -18.5)
assertEq(f32[3788 >> 2], -19.5)
assertEq(f32[3792 >> 2], -20.5)
assertEq(f32[3796 >> 2], -21.5)
assertEq(f32[3800 >> 2], -22.5)
assertEq(f32[3804 >> 2], -23.5)
assertEq(f32[3808 >> 2], -24.5)
assertEq(f32[3812 >> 2], -25.5)
assertEq(f32[3816 >> 2], -26.5)
assertEq(f32[3820 >> 2], -27.5)
assertEq(f32[3824 >> 2], -28.5)
assertEq(f32[3828 >> 2], -29.5)
assertEq(f32[3832 >> 2], -30.5)
assertEq(f32[3836 >> 2], -31.5)
assertEq(f32[3840 >> 2], -0.12890625)
assertEq(f32[3844 >> 2], -0.13671875)
assertEq(f32[3848 >> 2], -0.14453125)
assertEq(f32[3852 >> 2], -0.15234375)
assertEq(f32[3856 >> 2], -0.16015625)
assertEq(f32[3860 >> 2], -0.16796875)
assertEq(f32[3864 >> 2], -0.17578125)
assertEq(f32[3868 >> 2], -0.18359375)
assertEq(f32[3872 >> 2], -0.19140625)
assertEq(f32[3876 >> 2], -0.19921875)
assertEq(f32[3880 >> 2], -0.20703125)
assertEq(f32[3884 >> 2], -0.21484375)
assertEq(f32[3888 >> 2], -0.22265625)
assertEq(f32[3892 >> 2], -0.23046875)
assertEq(f32[3896 >> 2], -0.23828125)
assertEq(f32[3900 >> 2], -0.24609375)
assertEq(f32[3904 >> 2], -0.2578125)
assertEq(f32[3908 >> 2], -0.2734375)
assertEq(f32[3912 >> 2], -0.2890625)
assertEq(f32[3916 >> 2], -0.3046875)
assertEq(f32[3920 >> 2], -0.3203125)
assertEq(f32[3924 >> 2], -0.3359375)
assertEq(f32[3928 >> 2], -0.3515625)
assertEq(f32[3932 >> 2], -0.3671875)
assertEq(f32[3936 >> 2], -0.3828125)
assertEq(f32[3940 >> 2], -0.3984375)
assertEq(f32[3944 >> 2], -0.4140625)
assertEq(f32[3948 >> 2], -0.4296875)
assertEq(f32[3952 >> 2], -0.4453125)
assertEq(f32[3956 >> 2], -0.4609375)
assertEq(f32[3960 >> 2], -0.4765625)
assertEq(f32[3964 >> 2], -0.4921875)
assertEq(f32[3968 >> 2], -0.515625)
assertEq(f32[3972 >> 2], -0.546875)
assertEq(f32[3976 >> 2], -0.578125)
assertEq(f32[3980 >> 2], -0.609375)
assertEq(f32[3984 >> 2], -0.640625)
assertEq(f32[3988 >> 2], -0.671875)
assertEq(f32[3992 >> 2], -0.703125)
assertEq(f32[3996 >> 2], -0.734375)
assertEq(f32[4000 >> 2], -0.765625)
assertEq(f32[4004 >> 2], -0.796875)
assertEq(f32[4008 >> 2], -0.828125)
assertEq(f32[4012 >> 2], -0.859375)
assertEq(f32[4016 >> 2], -0.890625)
assertEq(f32[4020 >> 2], -0.921875)
assertEq(f32[4024 >> 2], -0.953125)
assertEq(f32[4024 >> 2], -0.953125)
assertEq(f32[4028 >> 2], -0.984375)
assertEq(f32[4032 >> 2], -1.03125)
assertEq(f32[4036 >> 2], -1.09375)
assertEq(f32[4040 >> 2], -1.15625)
assertEq(f32[4044 >> 2], -1.21875)
assertEq(f32[4048 >> 2], -1.28125)
assertEq(f32[4052 >> 2], -1.34375)
assertEq(f32[4056 >> 2], -1.40625)
assertEq(f32[4060 >> 2], -1.46875)
assertEq(f32[4064 >> 2], -1.53125)
assertEq(f32[4068 >> 2], -1.59375)
assertEq(f32[4072 >> 2], -1.65625)
assertEq(f32[4076 >> 2], -1.71875)
assertEq(f32[4080 >> 2], -1.78125)
assertEq(f32[4084 >> 2], -1.84375)
assertEq(f32[4088 >> 2], -1.90625)
assertEq(f32[4092 >> 2], -1.96875)
