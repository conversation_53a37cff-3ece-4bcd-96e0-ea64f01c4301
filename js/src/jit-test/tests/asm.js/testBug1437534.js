let __v_11662 = `(function module() { "use asm";function foo(`;
const __v_11663 =
551;
for (let __v_11665 = 0; __v_11665 < __v_11663; ++__v_11665) {
    __v_11662 += `arg${__v_11665},`;
}
try {
  __v_11662 += `arg${__v_11663}){`;
} catch (e) {}
for (let __v_11666 = 0; __v_11666 <= __v_11663; ++__v_11666) {
    __v_11662 += `arg${__v_11666}=+arg${__v_11666};`;
}
  __v_11662 += "return 10;}function bar(){return foo(";
for (let __v_11667 = 0; __v_11667 < __v_11663; ++__v_11667) {
    __v_11662 += "0.0,";
}
  __v_11662 += "1.0)|0;}";
  __v_11662 += "return bar})()()";
const __v_11664 = eval(__v_11662);