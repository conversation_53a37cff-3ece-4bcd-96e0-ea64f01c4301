try {
    s.e
} catch (e) {}
o = o = s2 = /x/
for (let e in []);
x = s2
schedulegc(21)
eval("x.e=x.t")
try {
    (function() {
        this.eval("\
            (function(stdlib,fgn,heap) {\
                \"use asm\";\
                var Vie = new stdlib.Float64Array(heap);\
                var Iew = new stdlib.Int8Array(heap);\
                function f(){\
                    ent\
                }\
            })()\
        ")
    })()
} catch (e) {}
