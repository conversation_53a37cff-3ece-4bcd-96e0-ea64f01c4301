// The Great Computer Language Shootout
//  http://shootout.alioth.debian.org
//
//  Contributed by <PERSON>

var result = [];

function pad(n,width) {
  var s = n.toString();
  while (s.length < width) s = ' ' + s;
  return s;
}

function primes(isPrime, n) {
  var i, count = 0, m = 10000<<n, size = m+31>>5;

  for (i=0; i<size; i++) isPrime[i] = 0xffffffff;

  for (i=2; i<m; i++)
    if (isPrime[i>>5] & 1<<(i&31)) {
      for (var j=i+i; j<m; j+=i)
        result.push(isPrime[j>>5] &= ~(1<<(j&31)));
      count++;
    }
}

function sieve() {
    for (var i = 4; i <= 4; i++) {
        var isPrime = new Array((10000<<i)+31>>5);
        primes(isPrime, i);
    }
}

sieve();

var ret = 0;
for (var i = 0; i < result.length; ++i)
  ret += result[i];

assertEq(ret, -211235557404919)
