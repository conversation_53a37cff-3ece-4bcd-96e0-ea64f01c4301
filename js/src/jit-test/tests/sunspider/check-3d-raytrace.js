/*
 * Copyright (C) 2007 Apple Inc.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY APPLE COMPUTER, INC. ``AS IS'' AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTAB<PERSON>ITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL APPLE COMPUTER, INC. OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 
 */

function createVector(x,y,z) {
    return new Array(x,y,z);
}

function sqrLengthVector(self) {
    return self[0] * self[0] + self[1] * self[1] + self[2] * self[2];
}

function lengthVector(self) {
    return Math.sqrt(self[0] * self[0] + self[1] * self[1] + self[2] * self[2]);
}

function addVector(self, v) {
    self[0] += v[0];
    self[1] += v[1];
    self[2] += v[2];
    return self;
}

function subVector(self, v) {
    self[0] -= v[0];
    self[1] -= v[1];
    self[2] -= v[2];
    return self;
}

function scaleVector(self, scale) {
    self[0] *= scale;
    self[1] *= scale;
    self[2] *= scale;
    return self;
}

function normaliseVector(self) {
    var len = Math.sqrt(self[0] * self[0] + self[1] * self[1] + self[2] * self[2]);
    self[0] /= len;
    self[1] /= len;
    self[2] /= len;
    return self;
}

function add(v1, v2) {
    return new Array(v1[0] + v2[0], v1[1] + v2[1], v1[2] + v2[2]);
}

function sub(v1, v2) {
    return new Array(v1[0] - v2[0], v1[1] - v2[1], v1[2] - v2[2]);
}

function scalev(v1, v2) {
    return new Array(v1[0] * v2[0], v1[1] * v2[1], v1[2] * v2[2]);
}

function dot(v1, v2) {
    return v1[0] * v2[0] + v1[1] * v2[1] + v1[2] * v2[2];
}

function scale(v, scale) {
    return [v[0] * scale, v[1] * scale, v[2] * scale];
}

function cross(v1, v2) {
    return [v1[1] * v2[2] - v1[2] * v2[1], 
            v1[2] * v2[0] - v1[0] * v2[2],
            v1[0] * v2[1] - v1[1] * v2[0]];

}

function normalise(v) {
    var len = lengthVector(v);
    return [v[0] / len, v[1] / len, v[2] / len];
}

function transformMatrix(self, v) {
    var vals = self;
    var x  = vals[0] * v[0] + vals[1] * v[1] + vals[2] * v[2] + vals[3];
    var y  = vals[4] * v[0] + vals[5] * v[1] + vals[6] * v[2] + vals[7];
    var z  = vals[8] * v[0] + vals[9] * v[1] + vals[10] * v[2] + vals[11];
    return [x, y, z];
}

function invertMatrix(self) {
    var temp = new Array(16);
    var tx = -self[3];
    var ty = -self[7];
    var tz = -self[11];
    for (h = 0; h < 3; h++) 
        for (v = 0; v < 3; v++) 
            temp[h + v * 4] = self[v + h * 4];
    for (i = 0; i < 11; i++)
        self[i] = temp[i];
    self[3] = tx * self[0] + ty * self[1] + tz * self[2];
    self[7] = tx * self[4] + ty * self[5] + tz * self[6];
    self[11] = tx * self[8] + ty * self[9] + tz * self[10];
    return self;
}


// Triangle intersection using barycentric coord method
function Triangle(p1, p2, p3) {
    var edge1 = sub(p3, p1);
    var edge2 = sub(p2, p1);
    var normal = cross(edge1, edge2);
    if (Math.abs(normal[0]) > Math.abs(normal[1]))
        if (Math.abs(normal[0]) > Math.abs(normal[2]))
            this.axis = 0; 
        else 
            this.axis = 2;
    else
        if (Math.abs(normal[1]) > Math.abs(normal[2])) 
            this.axis = 1;
        else 
            this.axis = 2;
    var u = (this.axis + 1) % 3;
    var v = (this.axis + 2) % 3;
    var u1 = edge1[u];
    var v1 = edge1[v];
    
    var u2 = edge2[u];
    var v2 = edge2[v];
    this.normal = normalise(normal);
    this.nu = normal[u] / normal[this.axis];
    this.nv = normal[v] / normal[this.axis];
    this.nd = dot(normal, p1) / normal[this.axis];
    var det = u1 * v2 - v1 * u2;
    this.eu = p1[u];
    this.ev = p1[v]; 
    this.nu1 = u1 / det;
    this.nv1 = -v1 / det;
    this.nu2 = v2 / det;
    this.nv2 = -u2 / det; 
    this.material = [0.7, 0.7, 0.7];
}

Triangle.prototype.intersect = function(orig, dir, near, far) {
    var u = (this.axis + 1) % 3;
    var v = (this.axis + 2) % 3;
    var d = dir[this.axis] + this.nu * dir[u] + this.nv * dir[v];
    var t = (this.nd - orig[this.axis] - this.nu * orig[u] - this.nv * orig[v]) / d;
    if (t < near || t > far)
        return null;
    var Pu = orig[u] + t * dir[u] - this.eu;
    var Pv = orig[v] + t * dir[v] - this.ev;
    var a2 = Pv * this.nu1 + Pu * this.nv1;
    if (a2 < 0) 
        return null;
    var a3 = Pu * this.nu2 + Pv * this.nv2;
    if (a3 < 0) 
        return null;

    if ((a2 + a3) > 1) 
        return null;
    return t;
}

function Scene(a_triangles) {
    this.triangles = a_triangles;
    this.lights = [];
    this.ambient = [0,0,0];
    this.background = [0.8,0.8,1];
}
var zero = new Array(0,0,0);

Scene.prototype.intersect = function(origin, dir, near, far) {
    var closest = null;
    for (i = 0; i < this.triangles.length; i++) {
        var triangle = this.triangles[i];   
        var d = triangle.intersect(origin, dir, near, far);
        if (d == null || d > far || d < near)
            continue;
        far = d;
        closest = triangle;
    }
    
    if (!closest)
        return [this.background[0],this.background[1],this.background[2]];
        
    var normal = closest.normal;
    var hit = add(origin, scale(dir, far)); 
    if (dot(dir, normal) > 0)
        normal = [-normal[0], -normal[1], -normal[2]];
    
    var colour = null;
    if (closest.shader) {
        colour = closest.shader(closest, hit, dir);
    } else {
        colour = closest.material;
    }
    
    // do reflection
    var reflected = null;
    if (colour.reflection > 0.001) {
        var reflection = addVector(scale(normal, -2*dot(dir, normal)), dir);
        reflected = this.intersect(hit, reflection, 0.0001, 1000000);
        if (colour.reflection >= 0.999999)
            return reflected;
    }
    
    var l = [this.ambient[0], this.ambient[1], this.ambient[2]];
    for (var i = 0; i < this.lights.length; i++) {
        var light = this.lights[i];
        var toLight = sub(light, hit);
        var distance = lengthVector(toLight);
        scaleVector(toLight, 1.0/distance);
        distance -= 0.0001;
        if (this.blocked(hit, toLight, distance))
            continue;
        var nl = dot(normal, toLight);
        if (nl > 0)
            addVector(l, scale(light.colour, nl));
    }
    l = scalev(l, colour);
    if (reflected) {
        l = addVector(scaleVector(l, 1 - colour.reflection), scaleVector(reflected, colour.reflection));
    }
    return l;
}

Scene.prototype.blocked = function(O, D, far) {
    var near = 0.0001;
    var closest = null;
    for (i = 0; i < this.triangles.length; i++) {
        var triangle = this.triangles[i];   
        var d = triangle.intersect(O, D, near, far);
        if (d == null || d > far || d < near)
            continue;
        return true;
    }
    
    return false;
}


// this camera code is from notes i made ages ago, it is from *somewhere* -- i cannot remember where
// that somewhere is
function Camera(origin, lookat, up) {
    var zaxis = normaliseVector(subVector(lookat, origin));
    var xaxis = normaliseVector(cross(up, zaxis));
    var yaxis = normaliseVector(cross(xaxis, subVector([0,0,0], zaxis)));
    var m = new Array(16);
    m[0] = xaxis[0]; m[1] = xaxis[1]; m[2] = xaxis[2];
    m[4] = yaxis[0]; m[5] = yaxis[1]; m[6] = yaxis[2];
    m[8] = zaxis[0]; m[9] = zaxis[1]; m[10] = zaxis[2];
    invertMatrix(m);
    m[3] = 0; m[7] = 0; m[11] = 0;
    this.origin = origin;
    this.directions = new Array(4);
    this.directions[0] = normalise([-0.7,  0.7, 1]);
    this.directions[1] = normalise([ 0.7,  0.7, 1]);
    this.directions[2] = normalise([ 0.7, -0.7, 1]);
    this.directions[3] = normalise([-0.7, -0.7, 1]);
    this.directions[0] = transformMatrix(m, this.directions[0]);
    this.directions[1] = transformMatrix(m, this.directions[1]);
    this.directions[2] = transformMatrix(m, this.directions[2]);
    this.directions[3] = transformMatrix(m, this.directions[3]);
}

Camera.prototype.generateRayPair = function(y) {
    rays = new Array(new Object(), new Object());
    rays[0].origin = this.origin;
    rays[1].origin = this.origin;
    rays[0].dir = addVector(scale(this.directions[0], y), scale(this.directions[3], 1 - y));
    rays[1].dir = addVector(scale(this.directions[1], y), scale(this.directions[2], 1 - y));
    return rays;
}

function renderRows(camera, scene, pixels, width, height, starty, stopy) {
    for (var y = starty; y < stopy; y++) {
        var rays = camera.generateRayPair(y / height);
        for (var x = 0; x < width; x++) {
            var xp = x / width;
            var origin = addVector(scale(rays[0].origin, xp), scale(rays[1].origin, 1 - xp));
            var dir = normaliseVector(addVector(scale(rays[0].dir, xp), scale(rays[1].dir, 1 - xp)));
            var l = scene.intersect(origin, dir);
            pixels[y][x] = l;
        }
    }
}

Camera.prototype.render = function(scene, pixels, width, height) {
    var cam = this;
    var row = 0;
    renderRows(cam, scene, pixels, width, height, 0, height);
}



function raytraceScene()
{
    var numTriangles = 2 * 6;
    var triangles = new Array();//numTriangles);
    var tfl = createVector(-10,  10, -10);
    var tfr = createVector( 10,  10, -10);
    var tbl = createVector(-10,  10,  10);
    var tbr = createVector( 10,  10,  10);
    var bfl = createVector(-10, -10, -10);
    var bfr = createVector( 10, -10, -10);
    var bbl = createVector(-10, -10,  10);
    var bbr = createVector( 10, -10,  10);
    
    // cube!!!
    // front
    var i = 0;
    
    triangles[i++] = new Triangle(tfl, tfr, bfr);
    triangles[i++] = new Triangle(tfl, bfr, bfl);
    // back
    triangles[i++] = new Triangle(tbl, tbr, bbr);
    triangles[i++] = new Triangle(tbl, bbr, bbl);
    //        triangles[i-1].material = [0.7,0.2,0.2];
    //            triangles[i-1].material.reflection = 0.8;
    // left
    triangles[i++] = new Triangle(tbl, tfl, bbl);
    //            triangles[i-1].reflection = 0.6;
    triangles[i++] = new Triangle(tfl, bfl, bbl);
    //            triangles[i-1].reflection = 0.6;
    // right
    triangles[i++] = new Triangle(tbr, tfr, bbr);
    triangles[i++] = new Triangle(tfr, bfr, bbr);
    // top
    triangles[i++] = new Triangle(tbl, tbr, tfr);
    triangles[i++] = new Triangle(tbl, tfr, tfl);
    // bottom
    triangles[i++] = new Triangle(bbl, bbr, bfr);
    triangles[i++] = new Triangle(bbl, bfr, bfl);
    
    //Floor!!!!
    var green = createVector(0.0, 0.4, 0.0);
    var grey = createVector(0.4, 0.4, 0.4);
    grey.reflection = 1.0;
    var floorShader = function(tri, pos, view) {
        var x = ((pos[0]/32) % 2 + 2) % 2;
        var z = ((pos[2]/32 + 0.3) % 2 + 2) % 2;
        if (x < 1 != z < 1) {
            //in the real world we use the fresnel term...
            //    var angle = 1-dot(view, tri.normal);
            //   angle *= angle;
            //  angle *= angle;
            // angle *= angle;
            //grey.reflection = angle;
            return grey;
        } else 
            return green;
    }
    var ffl = createVector(-1000, -30, -1000);
    var ffr = createVector( 1000, -30, -1000);
    var fbl = createVector(-1000, -30,  1000);
    var fbr = createVector( 1000, -30,  1000);
    triangles[i++] = new Triangle(fbl, fbr, ffr);
    triangles[i-1].shader = floorShader;
    triangles[i++] = new Triangle(fbl, ffr, ffl);
    triangles[i-1].shader = floorShader;
    
    var _scene = new Scene(triangles);
    _scene.lights[0] = createVector(20, 38, -22);
    _scene.lights[0].colour = createVector(0.7, 0.3, 0.3);
    _scene.lights[1] = createVector(-23, 40, 17);
    _scene.lights[1].colour = createVector(0.7, 0.3, 0.3);
    _scene.lights[2] = createVector(23, 20, 17);
    _scene.lights[2].colour = createVector(0.7, 0.7, 0.7);
    _scene.ambient = createVector(0.1, 0.1, 0.1);
    //  _scene.background = createVector(0.7, 0.7, 1.0);
    
    var size = 30;
    var pixels = new Array();
    for (var y = 0; y < size; y++) {
        pixels[y] = new Array();
        for (var x = 0; x < size; x++) {
            pixels[y][x] = 0;
        }
    }

    var _camera = new Camera(createVector(-40, 40, 40), createVector(0, 0, 0), createVector(0, 1, 0));
    _camera.render(_scene, pixels, size, size);

    return pixels;
}

function arrayToCanvasCommands(pixels)
{
    var s = '<canvas id="renderCanvas" width="30px" height="30px"></canvas><scr' + 'ipt>\nvar pixels = [';
    var size = 30;
    for (var y = 0; y < size; y++) {
        s += "[";
        for (var x = 0; x < size; x++) {
            s += "[" + pixels[y][x] + "],";
        }
        s+= "],";
    }
    s += '];\n    var canvas = document.getElementById("renderCanvas").getContext("2d");\n\
\n\
\n\
    var size = 30;\n\
    canvas.fillStyle = "red";\n\
    canvas.fillRect(0, 0, size, size);\n\
    canvas.scale(1, -1);\n\
    canvas.translate(0, -size);\n\
\n\
    if (!canvas.setFillColor)\n\
        canvas.setFillColor = function(r, g, b, a) {\n\
            this.fillStyle = "rgb("+[Math.floor(r * 255), Math.floor(g * 255), Math.floor(b * 255)]+")";\n\
    }\n\
\n\
for (var y = 0; y < size; y++) {\n\
  for (var x = 0; x < size; x++) {\n\
    var l = pixels[y][x];\n\
    canvas.setFillColor(l[0], l[1], l[2], 1);\n\
    canvas.fillRect(x, y, 1, 1);\n\
  }\n\
}</scr' + 'ipt>';

    return s;
}

testOutput = arrayToCanvasCommands(raytraceScene());
expected = '<canvas id="renderCanvas" width="30px" height="30px"></canvas><script>\nvar pixels = [[[0,0.22646733835486615,0],[0,0.22917348499592718,0],[0,0.23178836719862694,0],[0,0.23429286876882874,0],[0,0.23666708243914814,0],[0,0.2388906159889881,0],[0,0.3260187640505792,0],[0,0.33121005205394954,0],[0,0.3363076586511704,0],[0,0.3412818000213254,0],[0,0.34610095331648705,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.369829536245317,0],[0,0.3725822614817006,0],[0,0.37489560357280544,0],[0,0.37673658797290227,0],[0,0.3780753374916205,0],[0,0.378886188721004,0],[0,0.3791488586269958,0],[0,0.3788495731470844,0],[0,0.3779820527845238,0],[0,0.37654824729910663,0],[0,0.4585834760044105,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0,0.22925665044954321,0],[0,0.2320573979410493,0],[0,0.23474822091583247,0],[0,0.2373069549209832,0],[0,0.2397107002896524,0],[0,0.15436982463108695,0],[0,0.15568628300351414,0],[0,0.33780762567168454,0],[0,0.3431766295062631,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.24744701364085558,0.14604872013179526,0.14604872013179526],[0,0.3743786742105677,0],[0,0.37742123153478285,0],[0,0.3799794006700716,0],[0,0.38201209682126785,0],[0,0.38348180518082586,0],[0,0.384356168843629,0],[0,0.3846096564538848,0],[0,0.3842251672467923,0],[0,0.3831954061706588,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0,0.23215413887706876,0],[0,0.2350440502458203,0],[0,0.23780113739316563,0],[0,0.24039973450409946,0],[0,0.24281359296637,0],[0,0.15528082901621987,0],[0,0.15653052853852803,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.24335890550700673,0.1442966737887172,0.1442966737887172],[0.21191595684264103,0.13082112436113186,0.13082112436113186],[0.27664813175852776,0.2248217713585563,0.2248217713585563],[0,0.3823836235518836,0],[0,0.3852234408034573,0],[0,0.38747642030616,0],[0,0.3890951276817348,0],[0,0.39003853152190077,0],[0,0.39027440447223904,0],[0,0.3897816153712006,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.23811367607569112,0],[0,0.240922314629212,0],[0,0.2435404294800615,0],[0,0.24593811382698388,0],[0,0.1559883317159253,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.233189785862315,0.13993847965527784,0.13993847965527784],[0.2095470195339134,0.1298058655145343,0.1298058655145343],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.2414541261336147,0.19927150247084813,0.19927150247084813],[0.30463716829842996,0.25698429422662805,0.25698429422662805],[0,0.39057010876231657,0],[0,0.39307456071571556,0],[0,0.394860705064173,0],[0,0.3958762994996104,0],[0,0.3960806578453934,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.24123643784061885,0],[0,0.24407545031211067,0],[0,0.24668523203085055,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.20428988349740462,0.1275528072131734,0.1275528072131734],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.2553534506258493,0.21540752629099336,0.21540752629099336],[0.8,0.8,1],[0,0.39871352471166227,0],[0,0.40068391900131317,0],[0,0.4017699848209471,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.24436322334505386,0],[0,0.24745253188899904,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0,0.40943101981787544,0],[0,0.41179341435345673,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.24398601063610253,0],[0,0.24734388131046534,0],[0,0.2504039497369661,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.8,0.8,1],[0,0.41015054936419404,0],[0,0.4139256751539831,0],[0,0.5176011801301246,0],[0,0.5175400296826781,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.23925831942915582,0],[0,0.2431514340750372,0],[0,0.24679679895694717,0],[0,0.25013656179204347,0],[0,0.25311244537612027,0],[0,0.2556680399787405,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.06999999999999999,0.06999999999999999,0.06999999999999999],[0.8,0.8,1],[0,0.4078259849771481,0],[0,0.4131357292874544,0],[0,0.5218814714518779,0],[0,0.5233124012306586,0],[0,0.522962771547786,0],[0,0.5207522057325761,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.47311372545546515,0],[0,0.4614041416827006,0],],[[0,0.21490764011046362,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.2331842261176049,0],[0,0.23755980367223836,0],[0,0.24175353358196602,0],[0,0.24570333061205787,0],[0,0.24934343472275186,0],[0,0.252606535195386,0],[0,0.25542647135913205,0],[0,0.25774138056580276,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.04000000000000001,0],[0,0.2913399551219817,0],[0,0.40821549181429595,0],[0,0.5226526471916983,0],[0,0.5257809891986108,0],[0,0.5270304637173788,0],[0,0.5262436797403963,0],[0,0.5233412343635394,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.48095949311801045,0],[0,0.46869626187306984,0],[0,0.4558873544509206,0],],[[0,0.21129537920439745,0],[0,0.2160838834157171,0],[0,0.22090682198375836,0],[0.8,0.8,1],[0,0.23049540839949767,0],[0,0.23516114626695328,0],[0,0.23966144813312718,0],[0,0.24392754707957162,0],[0,0.24788516106908107,0],[0,0.25145680804980497,0],[0,0.2545649373510444,0],[0,0.2571357591990073,0],[0,0.2591035093142245,0],[0,0.15255606913369724,0],[0,0.15301134862115395,0],[0.19736821241316202,0.12458637674849803,0.12458637674849803],[0,0.40504494009802183,0],[0,0.4123372862951718,0],[0,0.4183003766375901,0],[0,0.5268338036458257,0],[0,0.5277169309488912,0],[0,0.5263102439245335,0],[0,0.5225497158196737,0],[0,0.5164937589802646,0],[0.8,0.8,1],[0,0.49832248210805585,0],[0,0.4868414893043067,0],[0,0.47425805574715646,0],[0,0.46093994347307254,0],[0,0.4472184699099014,0],],[[0,0.20695133260602822,0],[0,0.21189973891969208,0],[0,0.21691233850171843,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.23191357418198488,0],[0,0.23671451069678634,0],[0,0.24129830018648707,0],[0,0.24558190818576656,0],[0,0.24947677854650704,0],[0,0.2528923625850763,0],[0,0.1528305739035691,0],[0,0.1542326299051252,0],[0.8,0.8,1],[0.19785925315493239,0.12479682278068532,0.12479682278068532],[0.2081375194488818,0.12920179404952076,0.12920179404952076],[0.2240887656214228,0.18514359742568504,0.18514359742568504],[0,0.4135020430625767,0],[0,0.4192949763868133,0],[0,0.42324582463394744,0],[0,0.524175088930771,0],[0,0.5219574826556216,0],[0,0.5172050501700545,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.4895533585416516,0],[0,0.4770140400123619,0],[0,0.4635030619052592,0],[0,0.44941365075166806,0],[0,0.43508467272477774,0],],[[0,0.20179362237895174,0],[0,0.20685458951965674,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.23760464753632676,0],[0,0.24220528986272394,0],[0,0.2464227921634767,0],[0,0.2501524694953926,0],[0,0.15461153696054686,0],[0.18550977568907606,0.11950418958103261,0.11950418958103261],[0.1973204644052136,0.12456591331652012,0.12456591331652012],[0.2088287796802108,0.12949804843437607,0.12949804843437607],[0.21976471724250635,0.134184878818217,0.134184878818217],[0.23568458491329167,0.19345433251780333,0.19345433251780333],[0.23236149715622312,0.19397984285078584,0.19397984285078584],[0.22707008733584053,0.19247800386744748,0.19247800386744748],[0,0.41921793505330557,0],[0,0.42018199893505187,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.4624620672233052,0],[0,0.4484080186022647,0],[0,0.43394937018864516,0],[0,0.4194117002954933,0],],[[0,0.195751560483372,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.4179362030887152,0],[0.8,0.8,1],[0.18284994643317204,0.11836426275707375,0.11836426275707375],[0.19559717882822145,0.12382736235495205,0.12382736235495205],[0.20829587479733996,0.1292696606274314,0.1292696606274314],[0.22064479825066774,0.13456205639314334,0.13456205639314334],[0.2323223325492669,0.13956671394968584,0.13956671394968584],[0.24724443467900797,0.20148878770701636,0.20148878770701636],[0.24548491352821342,0.20390487818440434,0.20390487818440434],[0.24239083545814452,0.20490715724849212,0.20490715724849212],[0.23580267356471662,0.2022853173521094,0.2022853173521094],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.42907468966717865,0],[0,0.4147140942539032,0],[0.8,0.8,1],],[[0.8,0.8,1],[0,0.19385093619429258,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.40369350610660937,0],[0,0.4158360873491173,0],[0.19261301561109398,0.12254843526189743,0.12254843526189743],[0.20636000368544563,0.1284400015794767,0.1284400015794767],[0.22006309132254379,0.13431275342394736,0.13431275342394736],[0.23336548922639505,0.14001378109702645,0.14001378109702645],[0.24588206119264946,0.1453780262254212,0.1453780262254212],[0.2585904923823373,0.20905557193769056,0.20905557193769056],[0.2583880059856603,0.21340384806404408,0.21340384806404408],[0.2579257731612357,0.21738553706566144,0.21738553706566144],[0.25469196497831764,0.21843261169087974,0.21843261169087974],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.4196956387047621,0],[0,0.40600233966188104,0],[0.8,0.8,1],[0.8,0.8,1],],[[0,0.18086678377768206,0],[0,0.29820732165845826,0],[0,0.3078671511008362,0],[0,0.31797632104786633,0],[0,0.3285185113204014,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.3740646417282967,0],[0,0.38583856211873363,0],[0,0.39747405235976174,0],[0.2029477686069114,0.12697761511724776,0.12697761511724776],[0.21781735307317274,0.1333502941742169,0.1333502941742169],[0.2326566051026994,0.13970997361544257,0.13970997361544257],[0.2470420370437997,0.145875158733057,0.145875158733057],[0.9125534455486627,0.5106731929990184,0.5106731929990184],[0.26957187383368114,0.21599310805975738,0.21599310805975738],[0.2707166126603939,0.2221190287629908,0.2221190287629908],[0.2730659500911373,0.22930539474084957,0.22930539474084957],[0.27432898752113144,0.23520013372187343,0.23520013372187343],[0,0.370913970040732,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.41728826192115065,0],[0,0.4053708259838642,0],[0,0.39282677897301016,0],[0,0.37998757881530154,0],[0,0.3671261088322657,0],[0.8,0.8,1],],[[0,0.2734507658091365,0],[0,0.28211354268150884,0],[0,0.29120495530846624,0],[0,0.3007197072254542,0],[0,0.31064069882287787,0],[0,0.32093517914620384,0],[0,0.3315502317284035,0],[0,0.34240777559380986,0],[0,0.35339949423457706,0],[0,0.3643824114196257,0],[0,0.3751761555363945,0],[0.21384735597314658,0.13164886684563426,0.13164886684563426],[0.2299739346154931,0.1385602576923542,0.1385602576923542],[0.24609657496992726,0.1454699607013974,0.1454699607013974],[0.9085781768104235,0.508698320758555,0.508698320758555],[0.9154549906081679,0.5170357163338555,0.5170357163338555],[0.9205088622142807,0.5252164494222175,0.5252164494222175],[0.28215745535360465,0.22973861951523428,0.22973861951523428],[0.2871003088909314,0.23996159648606105,0.23996159648606105],[0.29363706106359355,0.2515193872815144,0.2515193872815144],[0,0.41902122210815307,0],[0,0.41310298499553366,0],[0,0.40544274823125576,0],[0,0.396335311262737,0],[0,0.3861118369914827,0],[0,0.37510764377615097,0],[0,0.3636351145776063,0],[0,0.35196551120236436,0],[0,0.3403202703614798,0],[0,0.32887008288221503,0],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.28175542273932297,0],[0,0.29085225965171946,0],[0,0.3002718747152153,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.3493460616221397,0],[0.22528651843925174,0.1365513650453936,0.1365513650453936],[0.884020287542635,0.49068080880618953,0.49068080880618953],[0.8974534722142382,0.5016001274737849,0.5016001274737849],[0.9089511260291274,0.5124192313516149,0.5124192313516149],[0.918724776273931,0.5233733004162796,0.5233733004162796],[0.9271769325614658,0.5348099989058438,0.5348099989058438],[0.9348913571357613,0.5471866228466732,0.5471866228466732],[0.9425953513779031,0.5610467455399599,0.5610467455399599],[0.31111289810529186,0.2659003418574735,0.2659003418574735],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.36238100335464446,0],[0,0.353165370752559,0],[0,0.3433460721377995,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.269553510147819,0],[0,0.2779247384646558,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.862863795871991,0.4769708653359615,0.4769708653359615],[0.8809280461363547,0.48977541774660666,0.48977541774660666],[0.8970168973464191,0.5025218831442017,0.5025218831442017],[0.911183718592768,0.5154210174684783,0.5154210174684783],[0.923749369873305,0.5288572912288372,0.5288572912288372],[0.9353543113230034,0.5434323975814477,0.5434323975814477],[0.9469976115499875,0.5600089248350288,0.5600089248350288],[0.9600573266066551,0.5797476044207045,0.5797476044207045],[0.976264515309917,0.6041064704498702,0.6041064704498702],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.31964250752889856,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0,0.22050019052313752,0],[0,0.22681553981028935,0],[0,0.23339203717763343,0],[0,0.240214496679792,0],[0,0.24725746972595297,0],[0,0.25448250911433834,0],[0,0.26183515020613984,0],[0,0.26924183890499,0],[0,0.27660721846944913,0],[0,0.2838124066288337,0],[0,0.290715098224911,0],[0,0.2971524154784058,0],[0,0.3029472623689724,0],[0,0.3079184123129743,0],[0.9166547694971252,0.5166323224598249,0.5166323224598249],[0.9306991201883348,0.5311982918579957,0.5311982918579957],[0.942986803591991,0.5467760342781277,0.5467760342781277],[0,0.3165578521241011,0],[0,0.31548839579667104,0],[0,0.3131368632708015,0],[0.8,0.8,1],[0,0.30500137342777356,0],[0,0.29951030523776406,0],[0,0.29329773299429385,0],[0,0.28653936257609475,0],[0,0.27940222592749825,0],[0.8,0.8,1],[0,0.26457440761040923,0],[0,0.2571216942588568,0],[0,0.24976474642245705,0],],[[0.8,0.8,1],[0,0.20714814874471174,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.23670584867979186,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.2653456430194066,0],[0,0.2698801253354915,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.2797697633705014,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.2652596644585568,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.23044738434960232,0],[0.8,0.8,1],],[[0.8,0.8,1],[0,0.1874620478256095,0],[0,0.19215316230371715,0],[0,0.19696169224108015,0],[0,0.20186087426658073,0],[0,0.20681514030842463,0],[0,0.2117788084075138,0],[0,0.21669500719585458,0],[0,0.22149508562026893,0],[0,0.22609882383824634,0],[0,0.23041578943560334,0],[0,0.23434813805418903,0],[0,0.23779500435424242,0],[0,0.240658357082728,0],[0.8,0.8,1],[0,0.24429776275232565,0],[0,0.24495325891611666,0],[0,0.24479455500778916,0],[0,0.24382873003864916,0],[0.8,0.8,1],[0,0.23963973129383997,0],[0,0.23655408649128906,0],[0,0.23292473874860314,0],[0,0.2288489260033022,0],[0.8,0.8,1],[0,0.21974417921637143,0],[0,0.21489400490094954,0],[0,0.20994923454342304,0],[0,0.20497430079270745,0],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.1797204574749079,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.19516287408734248,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.2074862652007326,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.20573701126457444,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.19259287153244908,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.17697513874785395,0],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.1614935034375082,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.17936757268974335,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.17026872079690972,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0,0.12879641411423945,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.1358377385211167,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.1546643297719027,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.1559650385862031,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.14211514071040432,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.13513052327523242,0],],[[0,0.11212384156112787,0],[0.8,0.8,1],[0,0.11564325792725064,0],[0,0.1174011949544484,0],[0.8,0.8,1],[0,0.12084571295779206,0],[0,0.12249918688067803,0],[0,0.1240816293005488,0],[0,0.12557222890553357,0],[0.8,0.8,1],[0,0.12819024296879525,0],[0,0.12927368617900967,0],[0.8,0.8,1],[0,0.13088813449481448,0],[0,0.13138603838942955,0],[0.8,0.8,1],[0,0.13170953912018962,0],[0.8,0.8,1],[0,0.13112114069722186,0],[0,0.13049815823500216,0],[0.8,0.8,1],[0,0.12866048872503766,0],[0,0.1274822855623644,0],[0.8,0.8,1],[0,0.1247126669400825,0],[0,0.12316534788101306,0],[0,0.12153830315457151,0],[0,0.11985151859147432,0],[0.8,0.8,1],[0,0.11637011656642021,0],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.104782462250846,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.1094376687357348,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0,0.0830756783895465,0],[0.8,0.8,1],[0,0.08474229354589341,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.08702004690763979,0],[0.8,0.8,1],[0,0.08829471061229066,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.0896722795161435,0],[0.8,0.8,1],[0,0.0901512080809384,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.09012955842093213,0],[0.8,0.8,1],[0,0.08962319641070081,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.08821080590025861,0],[0.8,0.8,1],[0,0.08691857775431859,0],[0.8,0.8,1],[0.8,0.8,1],[0,0.08462346811430875,0],[0.8,0.8,1],[0,0.08295071578903558,0],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.06935458357760912,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.07223735845162484,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0,0.07191875490542338,0],[0,0.07163456254931609,0],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],[[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],[0.8,0.8,1],],];\n    var canvas = document.getElementById("renderCanvas").getContext("2d");\n\n\n    var size = 30;\n    canvas.fillStyle = "red";\n    canvas.fillRect(0, 0, size, size);\n    canvas.scale(1, -1);\n    canvas.translate(0, -size);\n\n    if (!canvas.setFillColor)\n        canvas.setFillColor = function(r, g, b, a) {\n            this.fillStyle = "rgb("+[Math.floor(r * 255), Math.floor(g * 255), Math.floor(b * 255)]+")";\n    }\n\nfor (var y = 0; y < size; y++) {\n  for (var x = 0; x < size; x++) {\n    var l = pixels[y][x];\n    canvas.setFillColor(l[0], l[1], l[2], 1);\n    canvas.fillRect(x, y, 1, 1);\n  }\n}</script>';
assertEq(testOutput, expected)
