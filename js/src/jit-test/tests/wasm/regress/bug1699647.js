// |jit-test| skip-if: !wasmSimdEnabled() || !getBuildConfiguration("x86")

const module = new Uint8Array([
    0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,
    5,4,1,1,0,0,7,10,1,6,109,101,109,111,114,121,2,0,10,49,
    1,47,0,65,16,253,1,1,1,253,253,1,253,131,1,65,158,232,
    68,253,1,1,1,253,100,65,16,253,1,1,1,253,11,0,0,253,1,1,
    1,65,158,232,68,253,1,1,0,0,11,0,14,4,110,97,109,101,1,
    7,1,0,4,109,97,105,110]);
new WebAssembly.Module(module)
