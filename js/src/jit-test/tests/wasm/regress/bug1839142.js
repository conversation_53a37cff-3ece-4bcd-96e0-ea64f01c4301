// |jit-test| skip-if: !wasmSimdEnabled()
var wasm_code = new Uint8Array([0,97,115,109,1,0,0,0,1,152,128,128,128,0,4,80,0,95,1,126,0,80,0,94,124,1,80,0,96,3,127,127,127,1,127,96,0,0,3,130,128,128,128,0,1,2,4,133,128,128,128,0,1,112,1,1,1,5,132,128,128,128,0,1,1,16,32,13,131,128,128,128,0,1,0,3,6,204,131,128,128,0,62,100,107,0,66,197,129,131,134,140,152,176,224,64,251,0,0,11,127,0,65,196,129,131,134,124,11,100,107,0,66,192,129,131,134,204,132,137,146,36,251,0,0,11,124,1,68,0,0,0,0,0,0,0,0,11,124,1,68,0,0,0,0,0,0,0,0,11,124,1,68,0,0,0,0,0,0,0,0,11,124,1,68,0,0,0,0,0,0,0,0,11,100,107,0,66,192,129,131,134,140,216,53,251,0,0,11,100,107,1,66,210,164,201,146,165,202,148,169,210,0,66,210,164,201,146,165,202,212,156,218,0,66,192,129,131,134,140,152,176,224,64,66,192,129,131,134,140,152,176,224,64,126,125,66,192,129,131,128,130,152,176,224,64,125,66,192,129,131,190,130,152,176,224,36,125,66,164,200,0,125,125,66,0,125,66,0,125,66,0,125,251,0,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,127,0,65,0,11,7,136,128,128,128,0,1,4,109,97,105,110,0,0,9,139,128,128,128,0,1,6,0,65,0,11,112,1,210,0,11,10,141,133,128,128,0,1,138,5,0,65,238,235,177,226,126,253,15,253,83,32,0,65,235,146,246,155,122,65,244,231,246,248,124,253,15,253,164,1,65,230,152,157,154,7,253,15,253,164,1,118,65,167,184,218,133,127,253,15,253,164,1,118,118,66,149,131,127,66,164,128,218,132,206,227,209,231,254,0,65,230,133,189,200,126,65,252,208,237,164,5,254,32,0,132,245,241,222,13,27,254,71,2,211,226,246,158,7,66,243,213,226,237,209,166,141,199,0,68,76,189,205,180,194,110,195,89,36,3,131,253,18,253,127,253,127,253,127,253,127,253,164,1,65,138,173,198,47,65,138,248,237,203,120,65,205,162,146,252,5,65,190,148,192,156,5,254,53,0,200,229,139,195,9,65,167,139,216,173,5,65,215,146,221,45,254,53,0,169,255,135,252,1,254,53,0,193,209,131,217,7,40,2,134,242,184,197,3,65,228,191,145,146,6,65,142,162,226,169,4,254,53,0,168,178,151,189,15,113,109,71,109,107,254,46,0,191,232,145,230,9,67,66,84,34,11,67,88,147,220,200,91,68,233,240,20,66,52,37,190,38,182,187,182,187,182,187,182,187,182,187,57,3,168,169,148,198,10,65,226,162,208,167,7,65,221,226,226,242,120,107,65,140,215,139,233,5,65,141,151,153,19,107,107,65,188,134,175,165,5,65,183,219,200,136,121,107,65,250,197,157,214,123,65,139,168,173,167,126,107,107,107,42,1,249,156,171,169,13,187,182,187,182,187,182,187,182,187,182,65,191,253,243,170,122,253,15,65,203,195,202,169,122,253,15,65,179,204,244,234,123,253,15,253,119,65,166,184,138,186,122,253,15,65,129,140,243,163,6,253,15,253,119,65,229,139,254,233,121,253,15,65,183,191,195,183,122,253,15,253,119,253,119,65,151,211,231,151,122,253,15,253,119,253,119,253,119,65,192,156,192,215,3,65,178,193,209,198,7,107,65,240,157,246,199,6,65,221,225,148,169,1,107,65,145,183,142,141,127,65,188,218,139,244,7,107,107,65,236,243,250,169,127,65,146,241,174,181,120,107,65,139,147,232,229,124,65,255,203,253,217,3,107,107,65,250,197,224,140,2,65,202,242,215,181,3,107,65,135,244,246,28,65,140,170,229,200,123,107,107,107,65,154,217,196,153,1,65,137,128,243,231,123,107,107,107,107,65,227,146,143,180,126,40,1,245,130,139,196,13,40,2,244,172,225,238,10,40,0,216,160,178,215,11,40,1,197,193,230,178,3,40,2,195,241,223,254,2,65,158,240,247,204,124,40,2,140,190,218,180,14,40,2,215,128,167,146,8,40,0,141,143,157,196,10,40,0,147,146,185,143,13,40,1,195,168,134,179,5,107,107,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,69,253,15,253,119,253,31,0,91,254,46,0,203,243,148,239,8,11]);
var wasm_module = new WebAssembly.Module(wasm_code);
var wasm_instance = new WebAssembly.Instance(wasm_module);
var f = wasm_instance.exports.main;
assertErrorMessage(f, WebAssembly.RuntimeError, /index out of bounds/);
