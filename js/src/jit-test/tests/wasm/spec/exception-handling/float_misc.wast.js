/* Copyright 2021 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// ./test/core/float_misc.wast

// ./test/core/float_misc.wast:17
let $0 = instantiate(`(module
  (func (export "f32.add") (param $$x f32) (param $$y f32) (result f32) (f32.add (local.get $$x) (local.get $$y)))
  (func (export "f32.sub") (param $$x f32) (param $$y f32) (result f32) (f32.sub (local.get $$x) (local.get $$y)))
  (func (export "f32.mul") (param $$x f32) (param $$y f32) (result f32) (f32.mul (local.get $$x) (local.get $$y)))
  (func (export "f32.div") (param $$x f32) (param $$y f32) (result f32) (f32.div (local.get $$x) (local.get $$y)))
  (func (export "f32.sqrt") (param $$x f32) (result f32) (f32.sqrt (local.get $$x)))
  (func (export "f32.abs") (param $$x f32) (result f32) (f32.abs (local.get $$x)))
  (func (export "f32.neg") (param $$x f32) (result f32) (f32.neg (local.get $$x)))
  (func (export "f32.copysign") (param $$x f32) (param $$y f32) (result f32) (f32.copysign (local.get $$x) (local.get $$y)))
  (func (export "f32.ceil") (param $$x f32) (result f32) (f32.ceil (local.get $$x)))
  (func (export "f32.floor") (param $$x f32) (result f32) (f32.floor (local.get $$x)))
  (func (export "f32.trunc") (param $$x f32) (result f32) (f32.trunc (local.get $$x)))
  (func (export "f32.nearest") (param $$x f32) (result f32) (f32.nearest (local.get $$x)))
  (func (export "f32.min") (param $$x f32) (param $$y f32) (result f32) (f32.min (local.get $$x) (local.get $$y)))
  (func (export "f32.max") (param $$x f32) (param $$y f32) (result f32) (f32.max (local.get $$x) (local.get $$y)))

  (func (export "f64.add") (param $$x f64) (param $$y f64) (result f64) (f64.add (local.get $$x) (local.get $$y)))
  (func (export "f64.sub") (param $$x f64) (param $$y f64) (result f64) (f64.sub (local.get $$x) (local.get $$y)))
  (func (export "f64.mul") (param $$x f64) (param $$y f64) (result f64) (f64.mul (local.get $$x) (local.get $$y)))
  (func (export "f64.div") (param $$x f64) (param $$y f64) (result f64) (f64.div (local.get $$x) (local.get $$y)))
  (func (export "f64.sqrt") (param $$x f64) (result f64) (f64.sqrt (local.get $$x)))
  (func (export "f64.abs") (param $$x f64) (result f64) (f64.abs (local.get $$x)))
  (func (export "f64.neg") (param $$x f64) (result f64) (f64.neg (local.get $$x)))
  (func (export "f64.copysign") (param $$x f64) (param $$y f64) (result f64) (f64.copysign (local.get $$x) (local.get $$y)))
  (func (export "f64.ceil") (param $$x f64) (result f64) (f64.ceil (local.get $$x)))
  (func (export "f64.floor") (param $$x f64) (result f64) (f64.floor (local.get $$x)))
  (func (export "f64.trunc") (param $$x f64) (result f64) (f64.trunc (local.get $$x)))
  (func (export "f64.nearest") (param $$x f64) (result f64) (f64.nearest (local.get $$x)))
  (func (export "f64.min") (param $$x f64) (param $$y f64) (result f64) (f64.min (local.get $$x) (local.get $$y)))
  (func (export "f64.max") (param $$x f64) (param $$y f64) (result f64) (f64.max (local.get $$x) (local.get $$y)))
)`);

// ./test/core/float_misc.wast:50
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 1.1234568), value("f32", 0.00000000012345)]),
  [value("f32", 1.1234568)],
);

// ./test/core/float_misc.wast:51
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 1.123456789),
    value("f64", 0.00000000012345),
  ]),
  [value("f64", 1.12345678912345)],
);

// ./test/core/float_misc.wast:55
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 1), value("f32", 0.000000059604645)]),
  [value("f32", 1)],
);

// ./test/core/float_misc.wast:56
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 1), value("f32", 0.00000005960465)]),
  [value("f32", 1.0000001)],
);

// ./test/core/float_misc.wast:57
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 1),
    value("f64", 0.00000000000000011102230246251565),
  ]),
  [value("f64", 1)],
);

// ./test/core/float_misc.wast:58
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 1),
    value("f64", 0.00000000000000011102230246251568),
  ]),
  [value("f64", 1.0000000000000002)],
);

// ./test/core/float_misc.wast:61
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.000000000000000000000000000000000000000000001),
    value("f32", 0.000000000000000000000000000000000000011754942),
  ]),
  [value("f32", 0.000000000000000000000000000000000000011754944)],
);

// ./test/core/float_misc.wast:62
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002225073858507201),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
  ],
);

// ./test/core/float_misc.wast:67
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 2147483600), value("f32", 1024.25)]),
  [value("f32", 2147484700)],
);

// ./test/core/float_misc.wast:68
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 9223372036854776000),
    value("f64", 1024.25),
  ]),
  [value("f64", 9223372036854778000)],
);

// ./test/core/float_misc.wast:72
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003645561009778199),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000292),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000036455610097781983),
  ],
);

// ./test/core/float_misc.wast:75
assert_return(
  () => invoke($0, `f64.add`, [value("f64", 9007199254740992), value("f64", 1.00001)]),
  [value("f64", 9007199254740994)],
);

// ./test/core/float_misc.wast:78
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 9007199254740994),
    value("f64", 0.9999847412109375),
  ]),
  [value("f64", 9007199254740994)],
);

// ./test/core/float_misc.wast:81
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 8388608), value("f32", 0.5)]),
  [value("f32", 8388608)],
);

// ./test/core/float_misc.wast:82
assert_return(
  () => invoke($0, `f32.add`, [value("f32", 8388609), value("f32", 0.5)]),
  [value("f32", 8388610)],
);

// ./test/core/float_misc.wast:83
assert_return(
  () => invoke($0, `f64.add`, [value("f64", 4503599627370496), value("f64", 0.5)]),
  [value("f64", 4503599627370496)],
);

// ./test/core/float_misc.wast:84
assert_return(
  () => invoke($0, `f64.add`, [value("f64", 4503599627370497), value("f64", 0.5)]),
  [value("f64", 4503599627370498)],
);

// ./test/core/float_misc.wast:87
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -6207600000000000000000000000000),
    value("f32", 0.000000000000000000000000000002309799),
  ]),
  [value("f32", -6207600000000000000000000000000)],
);

// ./test/core/float_misc.wast:88
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 209865800000000000000),
    value("f32", -5270152500000000),
  ]),
  [value("f32", 209860530000000000000)],
);

// ./test/core/float_misc.wast:89
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.0000000000000000000000001963492),
    value("f32", 0.000000000000000000000000000000000000046220067),
  ]),
  [value("f32", 0.0000000000000000000000001963492)],
);

// ./test/core/float_misc.wast:90
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 640905000000),
    value("f32", -64449550000000000),
  ]),
  [value("f32", -64448910000000000)],
);

// ./test/core/float_misc.wast:91
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.0000601966),
    value("f32", 120372790000000000000000000000000),
  ]),
  [value("f32", 120372790000000000000000000000000)],
);

// ./test/core/float_misc.wast:92
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009218993827002741),
    value("f64", -1283078243878048500000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -1283078243878048500000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:93
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -96503407870148960000000),
    value("f64", 0.00000000000000000000000000000000000000000000000000000004670208988478548),
  ]),
  [value("f64", -96503407870148960000000)],
);

// ./test/core/float_misc.wast:94
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.0000000000000000000000000000000000000000000028559147675434106),
    value("f64", -0.00026124280570653086),
  ]),
  [value("f64", -0.00026124280570653086)],
);

// ./test/core/float_misc.wast:95
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 417909928165296700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 79335564741512700000),
  ]),
  [
    value("f64", 417909928165296700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:96
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 8265442868747023000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 43603327839006250000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 43603327839006250000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:99
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 5238404000000000000000),
    value("f32", -1570182.5),
  ]),
  [value("f32", 5238404000000000000000)],
);

// ./test/core/float_misc.wast:100
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.00000000000004258938),
    value("f32", -0.0000000000000000000000057092353),
  ]),
  [value("f32", 0.00000000000004258938)],
);

// ./test/core/float_misc.wast:101
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.00000000000027251026),
    value("f32", 83711560000000000000000000000000000000),
  ]),
  [value("f32", 83711560000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:102
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.0000000000000884536),
    value("f32", -0.000000000000000000000000000000015165626),
  ]),
  [value("f32", -0.0000000000000884536)],
);

// ./test/core/float_misc.wast:103
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.0010521035),
    value("f32", -0.000000000000000000000000000000007582135),
  ]),
  [value("f32", 0.0010521035)],
);

// ./test/core/float_misc.wast:104
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 1511135228188924600000000000000000000000000000000000000),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002760218100603169),
  ]),
  [value("f64", 1511135228188924600000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:105
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 62386719760360280000000000000000000000000000000),
    value("f64", -0.0000000000000000008592185488839212),
  ]),
  [value("f64", 62386719760360280000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:106
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004195022848436354),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000029225342022551453),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004195022848436354),
  ],
);

// ./test/core/float_misc.wast:107
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -215220546714824520000000000000000000000000000),
    value("f64", -1112220412047137200000000000000000000000000),
  ]),
  [value("f64", -216332767126871650000000000000000000000000000)],
);

// ./test/core/float_misc.wast:108
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -13.6911535055856),
    value("f64", 2066117898924419800000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 2066117898924419800000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:111
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.000000000000000000000000000000000006456021),
    value("f32", 0.00000000000020219949),
  ]),
  [value("f32", 0.00000000000020219949)],
);

// ./test/core/float_misc.wast:112
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.000026823169),
    value("f32", 0.000000011196016),
  ]),
  [value("f32", -0.000026811973)],
);

// ./test/core/float_misc.wast:113
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -128526170000),
    value("f32", 0.0000000000000000000000000000000027356305),
  ]),
  [value("f32", -128526170000)],
);

// ./test/core/float_misc.wast:114
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.000000000000000000000000000000000004158973),
    value("f32", -1573528700),
  ]),
  [value("f32", -1573528700)],
);

// ./test/core/float_misc.wast:115
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.0000000000000000000000000000000000009338769),
    value("f32", 78647514000000000000000000000),
  ]),
  [value("f32", 78647514000000000000000000000)],
);

// ./test/core/float_misc.wast:116
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021986596650683218),
    value("f64", -235447594845461340000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -235447594845461340000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:117
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -314175619593595700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -30114098514611660000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -314175649707694230000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:118
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013722858367681836),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000011571842749688977),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000011571842749688977),
  ],
);

// ./test/core/float_misc.wast:119
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009828583756551075),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016862581574752944),
  ]),
  [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009828583756551075),
  ],
);

// ./test/core/float_misc.wast:120
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -672584203522163500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 8374007930974482000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -672584203522163500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:123
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -210896605327889950000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 581483233421196300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 581483022524591100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:124
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 102315792666821480000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 450204300797494900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 102315792667271680000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:125
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -130529978570956560000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 154899434220186570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 154899434220186450000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:126
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 47629997434721684000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 455586451058259700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 455586451058259700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:127
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003958952516558414),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000023092460710062946),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000230924607140219),
  ],
);

// ./test/core/float_misc.wast:130
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -43780558475415996000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -49680759347383435000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -49680759347383435000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:131
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 21174311168546080000000000000000000000000000000000000000000),
    value("f64", -26385928474612128000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -26385928474612128000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:132
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -9508489561700635000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007858068235728165),
  ]),
  [
    value("f64", -9508489561700635000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:133
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005079144928553737),
    value("f64", -354021720742499800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -354021720742499800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:134
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000004165382103988111),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010865942283516648),
  ]),
  [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000004165382103988111),
  ],
);

// ./test/core/float_misc.wast:137
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 97215650000000000000000000000000000),
    value("f32", 305590870000000000000000000000000000000),
  ]),
  [value("f32", 305688080000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:138
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 270465630000000000000000000000000000000),
    value("f32", -230236850000000000000000000000000),
  ]),
  [value("f32", 270465400000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:139
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 357209300000000000000000000000000000),
    value("f32", -236494050000000000000000000000000000000),
  ]),
  [value("f32", -236136840000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:140
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -1484234100000000000000000000000000000),
    value("f32", -328991400000000000000000000000000000000),
  ]),
  [value("f32", -330475620000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:141
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -219885600000000000000000000000000000000),
    value("f32", -81560930000000000000000000000000000000),
  ]),
  [value("f32", -301446520000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:142
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 90390204939547630000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 22943337422040356000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 90390204939570580000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:143
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 165916059736246050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 12577349331444160000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 165916059748823400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:144
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -136351292561394300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 60507030603873580000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -136290785530790440000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:145
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -34377613258227424000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 169947152758793490000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 169947118381180220000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:146
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 92273427008645570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -39269416451018680000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 92273426969376150000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:149
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.000000000000000000000000000000000000008313455),
    value("f32", 0.000000000000000000000000000000000000000000873),
  ]),
  [value("f32", 0.000000000000000000000000000000000000008314328)],
);

// ./test/core/float_misc.wast:150
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.000000000000000000000000000000000000000000052),
    value("f32", -0.000000000000000000000000000000000000000000003),
  ]),
  [value("f32", 0.000000000000000000000000000000000000000000049)],
);

// ./test/core/float_misc.wast:151
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.000000000000000000000000000000000000000000011),
    value("f32", 0.000000000000000000000000000000000000005186284),
  ]),
  [value("f32", 0.000000000000000000000000000000000000005186273)],
);

// ./test/core/float_misc.wast:152
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", -0.000000000000000000000000000000000000000000028),
    value("f32", 0.00000000000000000000000000000000000023675283),
  ]),
  [value("f32", 0.0000000000000000000000000000000000002367528)],
);

// ./test/core/float_misc.wast:153
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 0.000000000000000000000000000000000000000000635),
    value("f32", -0.00000000000000000000000000000000000000003327),
  ]),
  [value("f32", -0.000000000000000000000000000000000000000032635)],
);

// ./test/core/float_misc.wast:154
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000028461489375936755),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005130160608603642),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002284011671009967),
  ],
);

// ./test/core/float_misc.wast:155
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000047404811354775),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008895417776504167),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004154936641026667),
  ],
);

// ./test/core/float_misc.wast:156
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009330082001250494),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000029863980609419717),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003919406261067021),
  ],
);

// ./test/core/float_misc.wast:157
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000014418693884494008),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016324914377759187),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001906220493265178),
  ],
);

// ./test/core/float_misc.wast:158
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000043203619362281506),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002521511966399844),
  ]),
  [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000017988499698283067),
  ],
);

// ./test/core/float_misc.wast:162
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 340282330000000000000000000000000000000),
    value("f32", 20282410000000000000000000000000),
  ]),
  [value("f32", 340282350000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:163
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 179769313486231550000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 19958403095347200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:166
assert_return(() => invoke($0, `f32.add`, [value("f32", 2), value("f32", 2)]), [value("f32", 4)]);

// ./test/core/float_misc.wast:167
assert_return(() => invoke($0, `f64.add`, [value("f64", 2), value("f64", 2)]), [value("f64", 4)]);

// ./test/core/float_misc.wast:170
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 340282350000000000000000000000000000000),
    value("f32", 10141204000000000000000000000000),
  ]),
  [value("f32", 340282350000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:171
assert_return(
  () => invoke($0, `f32.add`, [
    value("f32", 340282350000000000000000000000000000000),
    value("f32", 10141205000000000000000000000000),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:172
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 9979201547673598000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:173
assert_return(
  () => invoke($0, `f64.add`, [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 9979201547673600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", Infinity)],
);

// ./test/core/float_misc.wast:177
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 65536), value("f32", 0.000000000007275958)]),
  [value("f32", 65536)],
);

// ./test/core/float_misc.wast:178
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 65536),
    value("f64", 0.000000000007275957614183426),
  ]),
  [value("f64", 65535.99999999999)],
);

// ./test/core/float_misc.wast:182
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1), value("f32", 0.000000029802322)]),
  [value("f32", 1)],
);

// ./test/core/float_misc.wast:183
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1), value("f32", 0.000000029802326)]),
  [value("f32", 0.99999994)],
);

// ./test/core/float_misc.wast:184
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 1),
    value("f64", 0.00000000000000005551115123125783),
  ]),
  [value("f64", 1)],
);

// ./test/core/float_misc.wast:185
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 1),
    value("f64", 0.00000000000000005551115123125784),
  ]),
  [value("f64", 0.9999999999999999)],
);

// ./test/core/float_misc.wast:188
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 0.00000000000000000000000000000002379208),
    value("f32", -722129800000000000000000000000000000),
  ]),
  [value("f32", 722129800000000000000000000000000000)],
);

// ./test/core/float_misc.wast:189
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -842284000000000000000000000000000000),
    value("f32", -11118414000000),
  ]),
  [value("f32", -842284000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:190
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 1.4549444),
    value("f32", -0.00000000000000000000000033792615),
  ]),
  [value("f32", 1.4549444)],
);

// ./test/core/float_misc.wast:191
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 0.0000000000000000000000000000000000094808914),
    value("f32", 0.000000000000000000000018589502),
  ]),
  [value("f32", -0.000000000000000000000018589502)],
);

// ./test/core/float_misc.wast:192
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 0.000006181167),
    value("f32", -0.0000000000000000000000000000000093959864),
  ]),
  [value("f32", 0.000006181167)],
);

// ./test/core/float_misc.wast:193
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000775701650124413),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002524845082116609),
  ]),
  [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000775701650124413),
  ],
);

// ./test/core/float_misc.wast:194
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -20991871064832710000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -0.0000000000000000000000000000000000000000000000038165079778426864),
  ]),
  [
    value("f64", -20991871064832710000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:195
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000028592030964162332),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020889465194336087),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000028592030964162332),
  ],
);

// ./test/core/float_misc.wast:196
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000303879528930943),
    value("f64", -23204941114021897000000000000000000000000000000),
  ]),
  [value("f64", 23204941114021897000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:197
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.00000000000000000000000000000000000000000014953904039036317),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010592252695645683),
  ]),
  [value("f64", -0.00000000000000000000000000000000000000000014953904039036317)],
);

// ./test/core/float_misc.wast:200
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -448601660000000000000000000000000),
    value("f32", -8984148000000000000000000000000000),
  ]),
  [value("f32", 8535546400000000000000000000000000)],
);

// ./test/core/float_misc.wast:201
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -899427400000000000000000000000000),
    value("f32", 91.579384),
  ]),
  [value("f32", -899427400000000000000000000000000)],
);

// ./test/core/float_misc.wast:202
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -0.00000000000000000000000011975),
    value("f32", 0.000000063140405),
  ]),
  [value("f32", -0.000000063140405)],
);

// ./test/core/float_misc.wast:203
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -0.000000000000000000000011800487),
    value("f32", -0.00031558736),
  ]),
  [value("f32", 0.00031558736)],
);

// ./test/core/float_misc.wast:204
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -736483800000000000000000000000),
    value("f32", 0.0000000000000000030824513),
  ]),
  [value("f32", -736483800000000000000000000000)],
);

// ./test/core/float_misc.wast:205
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -9410469964196796000000000000000000000000000000000000000000000),
    value("f64", -17306275691385970000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 17306275691385970000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:206
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002877908564233173),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002339448785991429),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002877908564233173),
  ],
);

// ./test/core/float_misc.wast:207
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000009719219783531962),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001572015082308034),
  ]),
  [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000009719219783531962),
  ],
);

// ./test/core/float_misc.wast:208
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000034908896031751274),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000019928479721303208),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000019928479721303208),
  ],
);

// ./test/core/float_misc.wast:209
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -7538298763725556000000000000000000),
    value("f64", 4447012580193329000000000000000000000000000000000000),
  ]),
  [value("f64", -4447012580193329000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:212
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 75846976000000000000000000000),
    value("f32", 0.000046391753),
  ]),
  [value("f32", 75846976000000000000000000000)],
);

// ./test/core/float_misc.wast:213
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -567139.9),
    value("f32", -0.000000000030334842),
  ]),
  [value("f32", -567139.9)],
);

// ./test/core/float_misc.wast:214
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -0.000000000017412261),
    value("f32", -0.000000000000000017877793),
  ]),
  [value("f32", -0.000000000017412244)],
);

// ./test/core/float_misc.wast:215
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -0.000065645545),
    value("f32", 0.00014473806),
  ]),
  [value("f32", -0.00021038362)],
);

// ./test/core/float_misc.wast:216
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", -0.00000000016016115),
    value("f32", -0.000000000000000000000000000000085380075),
  ]),
  [value("f32", -0.00000000016016115)],
);

// ./test/core/float_misc.wast:217
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.000000000000000000000000000000000000000000000009358725267183177),
    value("f64", -31137147338685164000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 31137147338685164000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:218
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -4390767596767215000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -67890457158958560000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 67890457158958560000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:219
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000036288281010831153),
    value("f64", 3383199683245004400000000000000000000000000000000000000),
  ]),
  [value("f64", -3383199683245004400000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:220
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003645097751812619),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000031423490969686624),
  ]),
  [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000031423491006137603),
  ],
);

// ./test/core/float_misc.wast:221
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008021529638989887),
    value("f64", -0.00006774972769072139),
  ]),
  [value("f64", 0.00006774972769072139)],
);

// ./test/core/float_misc.wast:224
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.000000000000000000000005816988065793039),
    value("f64", 0.000000000000000000000000000000000025021499241540866),
  ]),
  [value("f64", 0.000000000000000000000005816988065768018)],
);

// ./test/core/float_misc.wast:225
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000043336683304809554),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016945582607476316),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000043336683135353726),
  ],
);

// ./test/core/float_misc.wast:226
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000006908052676315257),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000012001773734799856),
  ]),
  [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000012001773734799856),
  ],
);

// ./test/core/float_misc.wast:227
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.0000000000022044291547443813),
    value("f64", -0.0000000000000000000027947429925618632),
  ]),
  [value("f64", -0.000000000002204429151949638)],
);

// ./test/core/float_misc.wast:228
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.00000004016393569117761),
    value("f64", 0.17053881989395447),
  ]),
  [value("f64", -0.17053877973001877)],
);

// ./test/core/float_misc.wast:231
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010015106898667285),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004785375958943186),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000047853759589431757),
  ],
);

// ./test/core/float_misc.wast:232
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", -15618959953.641388),
    value("f64", 598234410620718900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -598234410620718900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:233
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 38832071540376680000000000000000000),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000042192279274320304),
  ]),
  [value("f64", 38832071540376680000000000000000000)],
);

// ./test/core/float_misc.wast:234
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010705986890807897),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000017466607734737216),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010705986890807897),
  ],
);

// ./test/core/float_misc.wast:235
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.00000000000000000949378346261834),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000014584885434950294),
  ]),
  [value("f64", 0.00000000000000000949378346261834)],
);

// ./test/core/float_misc.wast:239
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 23.140692), value("f32", 3.1415927)]),
  [value("f32", 19.9991)],
);

// ./test/core/float_misc.wast:240
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 23.14069263277927),
    value("f64", 3.141592653589793),
  ]),
  [value("f64", 19.999099979189477)],
);

// ./test/core/float_misc.wast:243
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 2999999), value("f32", 2999998)]),
  [value("f32", 1)],
);

// ./test/core/float_misc.wast:244
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1999999), value("f32", 1999995)]),
  [value("f32", 4)],
);

// ./test/core/float_misc.wast:245
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1999999), value("f32", 1999993)]),
  [value("f32", 6)],
);

// ./test/core/float_misc.wast:246
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 400002), value("f32", 400001)]),
  [value("f32", 1)],
);

// ./test/core/float_misc.wast:247
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 400002), value("f32", 400000)]),
  [value("f32", 2)],
);

// ./test/core/float_misc.wast:248
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 2999999999999999),
    value("f64", 2999999999999998),
  ]),
  [value("f64", 1)],
);

// ./test/core/float_misc.wast:249
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 1999999999999999),
    value("f64", 1999999999999995),
  ]),
  [value("f64", 4)],
);

// ./test/core/float_misc.wast:250
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 1999999999999999),
    value("f64", 1999999999999993),
  ]),
  [value("f64", 6)],
);

// ./test/core/float_misc.wast:251
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 400000000000002),
    value("f64", 400000000000001),
  ]),
  [value("f64", 1)],
);

// ./test/core/float_misc.wast:252
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 400000000000002),
    value("f64", 400000000000000),
  ]),
  [value("f64", 2)],
);

// ./test/core/float_misc.wast:255
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 0.000000000000000000000000000000000000011754944),
    value("f32", 0.000000000000000000000000000000000000011754942),
  ]),
  [value("f32", 0.000000000000000000000000000000000000000000001)],
);

// ./test/core/float_misc.wast:256
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002225073858507201),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005),
  ],
);

// ./test/core/float_misc.wast:259
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1.0000001), value("f32", 0.99999994)]),
  [value("f32", 0.00000017881393)],
);

// ./test/core/float_misc.wast:260
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1.0000001), value("f32", 1)]),
  [value("f32", 0.00000011920929)],
);

// ./test/core/float_misc.wast:261
assert_return(
  () => invoke($0, `f32.sub`, [value("f32", 1), value("f32", 0.99999994)]),
  [value("f32", 0.000000059604645)],
);

// ./test/core/float_misc.wast:262
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 1.0000000000000002),
    value("f64", 0.9999999999999999),
  ]),
  [value("f64", 0.00000000000000033306690738754696)],
);

// ./test/core/float_misc.wast:263
assert_return(
  () => invoke($0, `f64.sub`, [value("f64", 1.0000000000000002), value("f64", 1)]),
  [value("f64", 0.0000000000000002220446049250313)],
);

// ./test/core/float_misc.wast:264
assert_return(
  () => invoke($0, `f64.sub`, [value("f64", 1), value("f64", 0.9999999999999999)]),
  [value("f64", 0.00000000000000011102230246251565)],
);

// ./test/core/float_misc.wast:268
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 340282350000000000000000000000000000000),
    value("f32", 10141204000000000000000000000000),
  ]),
  [value("f32", 340282350000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:269
assert_return(
  () => invoke($0, `f32.sub`, [
    value("f32", 340282350000000000000000000000000000000),
    value("f32", 10141205000000000000000000000000),
  ]),
  [value("f32", 340282330000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:270
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 9979201547673598000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:271
assert_return(
  () => invoke($0, `f64.sub`, [
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 9979201547673600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 179769313486231550000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:274
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 1000000000000000),
    value("f32", 1000000000000000),
  ]),
  [value("f32", 999999940000000000000000000000)],
);

// ./test/core/float_misc.wast:275
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 100000000000000000000),
    value("f32", 100000000000000000000),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:276
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 10000000000000000000000000),
    value("f32", 10000000000000000000000000),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:277
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 1000000000000000),
    value("f64", 1000000000000000),
  ]),
  [value("f64", 1000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:278
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 100000000000000000000),
    value("f64", 100000000000000000000),
  ]),
  [value("f64", 10000000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:279
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 10000000000000000000000000),
    value("f64", 10000000000000000000000000),
  ]),
  [value("f64", 100000000000000030000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:284
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 1848874900), value("f32", 19954563000)]),
  [value("f32", 36893493000000000000)],
);

// ./test/core/float_misc.wast:285
assert_return(
  () => invoke($0, `f64.mul`, [value("f64", 1848874847), value("f64", 19954562207)]),
  [value("f64", 36893488147419110000)],
);

// ./test/core/float_misc.wast:289
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 77.1), value("f32", 850)]),
  [value("f32", 65535)],
);

// ./test/core/float_misc.wast:290
assert_return(
  () => invoke($0, `f64.mul`, [value("f64", 77.1), value("f64", 850)]),
  [value("f64", 65534.99999999999)],
);

// ./test/core/float_misc.wast:293
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -2493839400000000000),
    value("f32", 0.000000000021176054),
  ]),
  [value("f32", -52809680)],
);

// ./test/core/float_misc.wast:294
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -6777248400000000000000000000000),
    value("f32", -0.00000000000000000000000000000034758242),
  ]),
  [value("f32", 2.3556523)],
);

// ./test/core/float_misc.wast:295
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -8384397600000000000000000000),
    value("f32", -0.000000000000000000000000000011948991),
  ]),
  [value("f32", 0.10018509)],
);

// ./test/core/float_misc.wast:296
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -656765400000000000000000),
    value("f32", -0.000000000000000000000046889766),
  ]),
  [value("f32", 30.795576)],
);

// ./test/core/float_misc.wast:297
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 13328204000000000),
    value("f32", 45.567223),
  ]),
  [value("f32", 607329200000000000)],
);

// ./test/core/float_misc.wast:298
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -99426226093342430000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 583177241514245140000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", -Infinity)],
);

// ./test/core/float_misc.wast:299
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002748155824301909),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000002093035437779455),
  ]),
  [value("f64", 0)],
);

// ./test/core/float_misc.wast:300
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 464888257371302500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -159272886487254360000000000000000),
  ]),
  [
    value("f64", -74044094645556960000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:301
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008261927764172427),
    value("f64", 36684744190529535000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -3030867065492991300000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:302
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 253838958331769250000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007842892881810105),
  ]),
  [value("f64", 0.00000000000000000019908317594263248)],
);

// ./test/core/float_misc.wast:305
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -0.0000000000000000000000000020153333),
    value("f32", -5031353000000000000000000000),
  ]),
  [value("f32", 10.139854)],
);

// ./test/core/float_misc.wast:306
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 12286325000000000000000),
    value("f32", 749601.8),
  ]),
  [value("f32", 9209852000000000000000000000)],
);

// ./test/core/float_misc.wast:307
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -0.0000000002763514),
    value("f32", -35524714000000000000000),
  ]),
  [value("f32", 9817304000000)],
);

// ./test/core/float_misc.wast:308
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 218931220000000000000),
    value("f32", -40298.785),
  ]),
  [value("f32", -8822662000000000000000000)],
);

// ./test/core/float_misc.wast:309
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 1691996300),
    value("f32", -122103350000000000000),
  ]),
  [value("f32", -206598410000000000000000000000)],
);

// ./test/core/float_misc.wast:310
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007576316076452304),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004601355879514986),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003486132652344772),
  ],
);

// ./test/core/float_misc.wast:311
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000012228616081443885),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008055526185180067),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009850793705258527),
  ],
);

// ./test/core/float_misc.wast:312
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -2068651246039250800000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -366801071583254800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", Infinity)],
);

// ./test/core/float_misc.wast:313
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 1543238835610281000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007370621385787007),
  ]),
  [
    value("f64", 1137462916512617700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:314
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 2235876566242058700000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -760669005920257000000000000000000000000000000000000),
  ]),
  [
    value("f64", -1700762005003744000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:317
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -110087030000000),
    value("f32", -54038020000000000000000000000),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:318
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -0.19366351),
    value("f32", 0.0000000000000000000000000000029748954),
  ]),
  [value("f32", -0.0000000000000000000000000000005761287)],
);

// ./test/core/float_misc.wast:319
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -0.0000034300713),
    value("f32", 77991523000000000000000000000000),
  ]),
  [value("f32", -267516490000000000000000000)],
);

// ./test/core/float_misc.wast:320
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -99003850000000000),
    value("f32", 0.000000000000000000000000000020933774),
  ]),
  [value("f32", -0.0000000000020725242)],
);

// ./test/core/float_misc.wast:321
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -129919.07),
    value("f32", 0.0000000000000000000000000000000000018480999),
  ]),
  [value("f32", -0.00000000000000000000000000000024010342)],
);

// ./test/core/float_misc.wast:322
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006625572200844895),
    value("f64", -37374020681740010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 0.00000000000000000024762427246273877)],
);

// ./test/core/float_misc.wast:323
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 821076848561758000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000012976552328552289),
  ]),
  [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000010654746691124455),
  ],
);

// ./test/core/float_misc.wast:324
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -10223449294906041000000000000000000000000000000000000),
    value("f64", 1970855583334680500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -20148942123804574000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:325
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 2918243080119086000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -63633170941689700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", -Infinity)],
);

// ./test/core/float_misc.wast:326
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 3407037798802672000000000),
    value("f64", 1225791423971563000000),
  ]),
  [value("f64", 4176317714919266400000000000000000000000000000)],
);

// ./test/core/float_misc.wast:329
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000044091927284399547),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000011518840702296592),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005078878866462432),
  ],
);

// ./test/core/float_misc.wast:330
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.002980041826472432),
    value("f64", 63125412993218000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -188116371033135940000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:331
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -308344578081300100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010081049555008529),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000031084369716557833),
  ],
);

// ./test/core/float_misc.wast:332
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 349387501315677300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 2131316915930809900),
  ]),
  [
    value("f64", 744655491768901000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:333
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000012500108005100234),
    value("f64", 1035265704160467500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -12940933115981990000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:336
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008947461661755698),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020853844141312436),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018658897095462173),
  ],
);

// ./test/core/float_misc.wast:337
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.00000000000000001161813037330394),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018737038135583668),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021768935186877886),
  ],
);

// ./test/core/float_misc.wast:338
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021752326768352433),
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006631210068072052),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000014424424827029184),
  ],
);

// ./test/core/float_misc.wast:339
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007149518157441743),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000022770445062365393),
  ]),
  [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001627977104264113),
  ],
);

// ./test/core/float_misc.wast:340
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004817739302150786),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000025375023049719763),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000012225024583961697),
  ],
);

// ./test/core/float_misc.wast:343
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 46576441629501554000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007021344893525714),
  ]),
  [value("f64", 0.000000003270292605938992)],
);

// ./test/core/float_misc.wast:344
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.012451716278313712),
    value("f64", 0.000000000000000000000000000000000000000000001945309177849331),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000002422243795617958),
  ],
);

// ./test/core/float_misc.wast:345
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -3.8312314777598586),
    value("f64", 0.0000000000009039887741742674),
  ]),
  [value("f64", -0.0000000000034633902471580017)],
);

// ./test/core/float_misc.wast:346
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009843582638849689),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000003375405654777583),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000033226084502443684),
  ],
);

// ./test/core/float_misc.wast:347
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", -260544537094514460000000),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000032887528185809035),
  ]),
  [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000008568665807354412),
  ],
);

// ./test/core/float_misc.wast:350
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 0.00000000000000000000002646978),
    value("f32", 0.00000000000000000000002646978),
  ]),
  [value("f32", 0)],
);

// ./test/core/float_misc.wast:351
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 0.000000000000000000000026469783),
    value("f32", 0.000000000000000000000026469783),
  ]),
  [value("f32", 0.000000000000000000000000000000000000000000001)],
);

// ./test/core/float_misc.wast:352
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000015717277847026285),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000015717277847026285),
  ]),
  [value("f64", 0)],
);

// ./test/core/float_misc.wast:353
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000015717277847026288),
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000015717277847026288),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005),
  ],
);

// ./test/core/float_misc.wast:356
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 18446743000000000000),
    value("f32", 18446743000000000000),
  ]),
  [value("f32", 340282330000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:357
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 18446744000000000000),
    value("f32", 18446744000000000000),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:358
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 13407807929942596000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 13407807929942596000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 179769313486231550000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:359
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 13407807929942597000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 13407807929942597000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", Infinity)],
);

// ./test/core/float_misc.wast:362
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 1.0000001), value("f32", 1.0000001)]),
  [value("f32", 1.0000002)],
);

// ./test/core/float_misc.wast:363
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 0.99999994), value("f32", 0.99999994)]),
  [value("f32", 0.9999999)],
);

// ./test/core/float_misc.wast:364
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 1.0000000000000002),
    value("f64", 1.0000000000000002),
  ]),
  [value("f64", 1.0000000000000004)],
);

// ./test/core/float_misc.wast:365
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.9999999999999999),
    value("f64", 0.9999999999999999),
  ]),
  [value("f64", 0.9999999999999998)],
);

// ./test/core/float_misc.wast:368
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 1.0000001), value("f32", 0.99999994)]),
  [value("f32", 1)],
);

// ./test/core/float_misc.wast:369
assert_return(
  () => invoke($0, `f32.mul`, [value("f32", 1.0000002), value("f32", 0.9999999)]),
  [value("f32", 1.0000001)],
);

// ./test/core/float_misc.wast:370
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 1.0000000000000002),
    value("f64", 0.9999999999999999),
  ]),
  [value("f64", 1)],
);

// ./test/core/float_misc.wast:371
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 1.0000000000000004),
    value("f64", 0.9999999999999998),
  ]),
  [value("f64", 1.0000000000000002)],
);

// ./test/core/float_misc.wast:375
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", 0.000000000000000000000000000000000000011754944),
    value("f32", 0.00000011920929),
  ]),
  [value("f32", 0.000000000000000000000000000000000000000000001)],
);

// ./test/core/float_misc.wast:376
assert_return(
  () => invoke($0, `f64.mul`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
    value("f64", 0.0000000000000002220446049250313),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005),
  ],
);

// ./test/core/float_misc.wast:379
assert_return(
  () => invoke($0, `f32.mul`, [
    value("f32", -16.001465),
    value("f32", 0.000000000000000000000000000000000000000298465),
  ]),
  [value("f32", -0.000000000000000000000000000000000000004775883)],
);

// ./test/core/float_misc.wast:382
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 1.1234568), value("f32", 100)]),
  [value("f32", 0.011234568)],
);

// ./test/core/float_misc.wast:383
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 8391667), value("f32", 12582905)]),
  [value("f32", 0.6669102)],
);

// ./test/core/float_misc.wast:384
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 65536), value("f32", 0.000000000007275958)]),
  [value("f32", 9007199000000000)],
);

// ./test/core/float_misc.wast:385
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1.8622957),
    value("f32", 340282350000000000000000000000000000000),
  ]),
  [value("f32", 0.000000000000000000000000000000000000005472795)],
);

// ./test/core/float_misc.wast:386
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 4), value("f32", 3)]),
  [value("f32", 1.3333334)],
);

// ./test/core/float_misc.wast:387
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 1.123456789), value("f64", 100)]),
  [value("f64", 0.01123456789)],
);

// ./test/core/float_misc.wast:388
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 8391667), value("f64", 12582905)]),
  [value("f64", 0.6669101451532854)],
);

// ./test/core/float_misc.wast:389
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 65536),
    value("f64", 0.000000000007275957614183426),
  ]),
  [value("f64", 9007199254740992)],
);

// ./test/core/float_misc.wast:390
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1.8622957468032837),
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001035936395755283),
  ],
);

// ./test/core/float_misc.wast:391
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 4), value("f64", 3)]),
  [value("f64", 1.3333333333333333)],
);

// ./test/core/float_misc.wast:395
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 4195835), value("f32", 3145727)]),
  [value("f32", 1.3338205)],
);

// ./test/core/float_misc.wast:396
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 4195835), value("f64", 3145727)]),
  [value("f64", 1.333820449136241)],
);

// ./test/core/float_misc.wast:399
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.000000000000005029633),
    value("f32", 336324380000000000000000000000000000000),
  ]),
  [value("f32", 0)],
);

// ./test/core/float_misc.wast:400
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.000000000000000000000000008921987),
    value("f32", 354097530000000000000),
  ]),
  [value("f32", 0)],
);

// ./test/core/float_misc.wast:401
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -104167.47),
    value("f32", 0.0000000000000000000000015866623),
  ]),
  [value("f32", -65651950000000000000000000000)],
);

// ./test/core/float_misc.wast:402
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -0.000000000000000000000024938657),
    value("f32", -0.00000000000000000000000000000000000036230088),
  ]),
  [value("f32", 68834107000000)],
);

// ./test/core/float_misc.wast:403
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -4142204200000),
    value("f32", 0.0000000000000000000000011954948),
  ]),
  [value("f32", -3464845000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:404
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 193901163824483840000000000000000000000000000),
    value("f64", 25290742357348314000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 0.000000000000000000000007666883046955921)],
);

// ./test/core/float_misc.wast:405
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006600332149752304),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003007915153468629),
  ]),
  [
    value("f64", 219432125342399270000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:406
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -934827517366190300000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 4809309529035847000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000019437873809582001),
  ],
);

// ./test/core/float_misc.wast:407
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -17598339088417535000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 199386072580682850000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -88262629684409150000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:408
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -4566268877844991000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 31282495822334530000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", -145968816036246260000000000)],
);

// ./test/core/float_misc.wast:411
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -1039406400000000000000),
    value("f32", -0.000000000000000000000000012965966),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:412
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.000000000000026831563),
    value("f32", 31241038000000),
  ]),
  [value("f32", 0.0000000000000000000000000008588563)],
);

// ./test/core/float_misc.wast:413
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1.2734247),
    value("f32", -692783700000000000000000000),
  ]),
  [value("f32", -0.0000000000000000000000000018381274)],
);

// ./test/core/float_misc.wast:414
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.00000000000000068988827),
    value("f32", 0.000000000000000000000000000000000000003762676),
  ]),
  [value("f32", 183350460000000000000000)],
);

// ./test/core/float_misc.wast:415
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1819916200000000000000000000),
    value("f32", 205067030000000000000000000),
  ]),
  [value("f32", 8.874739)],
);

// ./test/core/float_misc.wast:416
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021137715924428077),
    value("f64", -16733261612910253000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", -0)],
);

// ./test/core/float_misc.wast:417
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008116644948016275),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006517571349002277),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000012453480772801648),
  ],
);

// ./test/core/float_misc.wast:418
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009335476912259029),
    value("f64", -39099281466396.5),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000023876338802497726),
  ],
);

// ./test/core/float_misc.wast:419
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -1686856985488590200000000),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013535993861076857),
  ]),
  [
    value("f64", -12462010568276012000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:420
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -173388773324941200000000000000000000000000000000000000000000000000000000),
    value("f64", -70026160475217470),
  ]),
  [value("f64", 2476057121342590000000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:423
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 93506190),
    value("f32", 0.0000000000000000000000000000000000028760885),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:424
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -200575400000000000000000),
    value("f32", 246697220),
  ]),
  [value("f32", -813042800000000)],
);

// ./test/core/float_misc.wast:425
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 384712200000),
    value("f32", -107037850000000000000000000000),
  ]),
  [value("f32", -0.00000000000000000359417)],
);

// ./test/core/float_misc.wast:426
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -4156665000000000000000000000000000),
    value("f32", -901.4192),
  ]),
  [value("f32", 4611245300000000000000000000000)],
);

// ./test/core/float_misc.wast:427
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -6702387000000000000000000000),
    value("f32", -14000.255),
  ]),
  [value("f32", 478733200000000000000000)],
);

// ./test/core/float_misc.wast:428
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010085269598907525),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018780374032850215),
  ]),
  [value("f64", -53701111496.85621)],
);

// ./test/core/float_misc.wast:429
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -32571664562951100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005885738519211168),
  ]),
  [value("f64", Infinity)],
);

// ./test/core/float_misc.wast:430
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000031640946861233317),
    value("f64", 0.000000000000000000045854510556516254),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006900291046010721),
  ],
);

// ./test/core/float_misc.wast:431
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -526842242946656600000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000014816907071451201),
  ]),
  [
    value("f64", 355568298030134360000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:432
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 4039956270017490000000000000000000000000000000000000000),
    value("f64", -47097881971884274000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", -0.0000000000857778757955442)],
);

// ./test/core/float_misc.wast:435
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -203959560468347600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -74740887394612260000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 2728888665604071000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:436
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -304261712294687660000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", -2655679232658824300000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 114570204320220420000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:437
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 49235240512480730000000000000000000000000000000000000000),
    value("f64", -366340828310036700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013439736089369927),
  ],
);

// ./test/core/float_misc.wast:438
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 289260843556341600000000000000000000000000000000000000000000000000),
    value("f64", 517194875837335500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000005592879146144478),
  ],
);

// ./test/core/float_misc.wast:439
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -421542582344268600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 1428505854670649100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -295093352936560340000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:442
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1.8622957433108482),
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010359363938125513),
  ],
);

// ./test/core/float_misc.wast:443
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008566632480779937),
    value("f64", 5381.2699796556235),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001591935084685746),
  ],
);

// ./test/core/float_misc.wast:444
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.00000000000000000000000000000000000000000008196220919495565),
    value("f64", -10406557086484777000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007876015911295176),
  ],
);

// ./test/core/float_misc.wast:445
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007052801866447111),
    value("f64", -13767429405781133000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005122816800851397),
  ],
);

// ./test/core/float_misc.wast:446
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022655621734165475),
    value("f64", 133219932963494700000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000017006180103974106),
  ],
);

// ./test/core/float_misc.wast:447
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004196304106554003),
    value("f64", -9789327.297653636),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000042866113053139),
  ],
);

// ./test/core/float_misc.wast:450
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1038860800000000000000000000),
    value("f32", 6211079500000),
  ]),
  [value("f32", 167259300000000)],
);

// ./test/core/float_misc.wast:451
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1869033000000000000000000000),
    value("f32", -112355730000000000000000000000000),
  ]),
  [value("f32", -0.00001663496)],
);

// ./test/core/float_misc.wast:452
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 3290747200000000000000000),
    value("f32", 0.9064788),
  ]),
  [value("f32", 3630252700000000000000000)],
);

// ./test/core/float_misc.wast:453
assert_return(
  () => invoke($0, `f32.div`, [value("f32", -908946.56), value("f32", -17034289000)]),
  [value("f32", 0.000053359818)],
);

// ./test/core/float_misc.wast:454
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", -0.00000000000024092477),
    value("f32", -89840810000000000),
  ]),
  [value("f32", 0.0000000000000000000000000000026816852)],
);

// ./test/core/float_misc.wast:455
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 3910973045785834000),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008392730733897136),
  ]),
  [
    value("f64", -46599529638070336000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:456
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.0000000000000000000000000000000000000008379351966732404),
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021077277802048832),
  ]),
  [
    value("f64", -3975538039318286000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:457
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 4561142017854715000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
    value("f64", 1500578067736849100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 3039589952.6465592)],
);

// ./test/core/float_misc.wast:458
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -6236072401827852000000000000000000000000000000000000000),
    value("f64", 83170632504609900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007497925907299316),
  ],
);

// ./test/core/float_misc.wast:459
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", -0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009757271330468098),
    value("f64", -0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000035613812243480865),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000002739743575824061),
  ],
);

// ./test/core/float_misc.wast:462
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.00000000000000001046256872449641),
    value("f64", 1.8150892711657447),
  ]),
  [value("f64", 0.000000000000000005764217160391678)],
);

// ./test/core/float_misc.wast:463
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.00000000000000000000000000000022038268106596436),
    value("f64", -0.0000000000002859803943943555),
  ]),
  [value("f64", -0.0000000000000000007706216418530616)],
);

// ./test/core/float_misc.wast:464
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.0000000000007596539988437179),
    value("f64", 0.00000000000000000000000000000000021055358831337124),
  ]),
  [value("f64", 3607889112357986600000)],
);

// ./test/core/float_misc.wast:465
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1120696114500866900000000000),
    value("f64", 159713233802866500000000000000),
  ]),
  [value("f64", 0.007016927074960728)],
);

// ./test/core/float_misc.wast:466
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.0006342142502301953),
    value("f64", -6391950865520085),
  ]),
  [value("f64", -0.00000000000000000009922076429769178)],
);

// ./test/core/float_misc.wast:469
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.000000000000000000000000000000000000011754944),
    value("f32", 0.000000000000000000000000000000000000011754942),
  ]),
  [value("f32", 1.0000001)],
);

// ./test/core/float_misc.wast:470
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.000000000000000000000000000000000000011754942),
    value("f32", 0.000000000000000000000000000000000000011754944),
  ]),
  [value("f32", 0.9999999)],
);

// ./test/core/float_misc.wast:471
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002225073858507201),
  ]),
  [value("f64", 1.0000000000000002)],
);

// ./test/core/float_misc.wast:472
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002225073858507201),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
  ]),
  [value("f64", 0.9999999999999998)],
);

// ./test/core/float_misc.wast:475
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.00000023841856),
    value("f32", 340282350000000000000000000000000000000),
  ]),
  [value("f32", 0)],
);

// ./test/core/float_misc.wast:476
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 0.00000023841858),
    value("f32", 340282350000000000000000000000000000000),
  ]),
  [value("f32", 0.000000000000000000000000000000000000000000001)],
);

// ./test/core/float_misc.wast:477
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.00000000000000044408920985006257),
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 0)],
);

// ./test/core/float_misc.wast:478
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.0000000000000004440892098500626),
    value("f64", 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005),
  ],
);

// ./test/core/float_misc.wast:481
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1),
    value("f32", 0.000000000000000000000000000000000000002938736),
  ]),
  [value("f32", Infinity)],
);

// ./test/core/float_misc.wast:482
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1),
    value("f32", 0.000000000000000000000000000000000000002938737),
  ]),
  [value("f32", 340282200000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:483
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1),
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005562684646268003),
  ]),
  [value("f64", Infinity)],
);

// ./test/core/float_misc.wast:484
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1),
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000556268464626801),
  ]),
  [
    value("f64", 179769313486231430000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:487
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1),
    value("f32", 85070600000000000000000000000000000000),
  ]),
  [value("f32", 0.000000000000000000000000000000000000011754942)],
);

// ./test/core/float_misc.wast:488
assert_return(
  () => invoke($0, `f32.div`, [
    value("f32", 1),
    value("f32", 85070590000000000000000000000000000000),
  ]),
  [value("f32", 0.000000000000000000000000000000000000011754944)],
);

// ./test/core/float_misc.wast:489
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1),
    value("f64", 44942328371557910000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002225073858507201),
  ],
);

// ./test/core/float_misc.wast:490
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1),
    value("f64", 44942328371557900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014),
  ],
);

// ./test/core/float_misc.wast:500
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 1), value("f32", 3)]),
  [value("f32", 0.33333334)],
);

// ./test/core/float_misc.wast:501
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 3), value("f32", 9)]),
  [value("f32", 0.33333334)],
);

// ./test/core/float_misc.wast:502
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 9), value("f32", 27)]),
  [value("f32", 0.33333334)],
);

// ./test/core/float_misc.wast:503
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 1), value("f64", 3)]),
  [value("f64", 0.3333333333333333)],
);

// ./test/core/float_misc.wast:504
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 3), value("f64", 9)]),
  [value("f64", 0.3333333333333333)],
);

// ./test/core/float_misc.wast:505
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 9), value("f64", 27)]),
  [value("f64", 0.3333333333333333)],
);

// ./test/core/float_misc.wast:508
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 1.0000001), value("f32", 0.99999994)]),
  [value("f32", 1.0000002)],
);

// ./test/core/float_misc.wast:509
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 0.99999994), value("f32", 1.0000001)]),
  [value("f32", 0.9999998)],
);

// ./test/core/float_misc.wast:510
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 1), value("f32", 0.99999994)]),
  [value("f32", 1.0000001)],
);

// ./test/core/float_misc.wast:511
assert_return(
  () => invoke($0, `f32.div`, [value("f32", 1), value("f32", 1.0000001)]),
  [value("f32", 0.9999999)],
);

// ./test/core/float_misc.wast:512
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 1.0000000000000002),
    value("f64", 0.9999999999999999),
  ]),
  [value("f64", 1.0000000000000004)],
);

// ./test/core/float_misc.wast:513
assert_return(
  () => invoke($0, `f64.div`, [
    value("f64", 0.9999999999999999),
    value("f64", 1.0000000000000002),
  ]),
  [value("f64", 0.9999999999999997)],
);

// ./test/core/float_misc.wast:514
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 1), value("f64", 0.9999999999999999)]),
  [value("f64", 1.0000000000000002)],
);

// ./test/core/float_misc.wast:515
assert_return(
  () => invoke($0, `f64.div`, [value("f64", 1), value("f64", 1.0000000000000002)]),
  [value("f64", 0.9999999999999998)],
);

// ./test/core/float_misc.wast:519
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 171)]), [value("f32", 13.076696)]);

// ./test/core/float_misc.wast:520
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 0.000000160795)]),
  [value("f32", 0.00040099252)],
);

// ./test/core/float_misc.wast:521
assert_return(() => invoke($0, `f64.sqrt`, [value("f64", 171)]), [value("f64", 13.076696830622021)]);

// ./test/core/float_misc.wast:522
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.000000160795)]),
  [value("f64", 0.00040099251863345283)],
);

// ./test/core/float_misc.wast:525
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.00000000000000000000000000000000000000000000000004316357580352844),
  ]),
  [value("f64", 0.00000000000000000000000020775845543209175)],
);

// ./test/core/float_misc.wast:526
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 676253300479648500000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 822346216918183800000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:527
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 17485296624861996000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 4181542373916829400000000000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:528
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.000000000009593720960603523)]),
  [value("f64", 0.0000030973732355987585)],
);

// ./test/core/float_misc.wast:529
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006348452898717835),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000002519613640762773),
  ],
);

// ./test/core/float_misc.wast:533
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.9999999999999999)]),
  [value("f64", 0.9999999999999999)],
);

// ./test/core/float_misc.wast:536
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.12963942)]), [value("f32", 0.36005473)]);

// ./test/core/float_misc.wast:537
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 2345875800000000000000000000000)]),
  [value("f32", 1531625200000000)],
);

// ./test/core/float_misc.wast:538
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.078786574)]), [value("f32", 0.28068945)]);

// ./test/core/float_misc.wast:539
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 0.00000000000000000000051371026)]),
  [value("f32", 0.000000000022665177)],
);

// ./test/core/float_misc.wast:540
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.00090167153)]), [value("f32", 0.030027846)]);

// ./test/core/float_misc.wast:541
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009591922760825561),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009793836204892116),
  ],
);

// ./test/core/float_misc.wast:542
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 935787535216400500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 30590644570136150000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:543
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 147706699783365580000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [value("f64", 12153464517715332000000000000000000000000000000000000000000)],
);

// ./test/core/float_misc.wast:544
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 48800457180027890000000000000000)]),
  [value("f64", 6985732401117859)],
);

// ./test/core/float_misc.wast:545
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 7618977687174540000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 2760249569726357000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:548
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 154481010)]), [value("f32", 12429.039)]);

// ./test/core/float_misc.wast:549
assert_return(
  () => invoke($0, `f32.sqrt`, [
    value("f32", 0.00000000000000000000000000000000010471305),
  ]),
  [value("f32", 0.00000000000000001023294)],
);

// ./test/core/float_misc.wast:550
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.00003790637)]), [value("f32", 0.006156815)]);

// ./test/core/float_misc.wast:551
assert_return(
  () => invoke($0, `f32.sqrt`, [
    value("f32", 0.00000000000000000000000000000000000089607535),
  ]),
  [value("f32", 0.0000000000000000009466126)],
);

// ./test/core/float_misc.wast:552
assert_return(
  () => invoke($0, `f32.sqrt`, [
    value("f32", 0.0000000000000000000000000000000000001687712),
  ]),
  [value("f32", 0.00000000000000000041081773)],
);

// ./test/core/float_misc.wast:553
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 316996264378909500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 563024212959717700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:554
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040573669271847993),
  ]),
  [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020142906759414837),
  ],
);

// ./test/core/float_misc.wast:555
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.0000000015299861660588838)]),
  [value("f64", 0.00003911503759500793)],
);

// ./test/core/float_misc.wast:556
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000000000000000000002822766928951239),
  ]),
  [value("f64", 0.0000000000000000000000000000000000005312971794533864)],
);

// ./test/core/float_misc.wast:557
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 14375957727045067000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 119899782014168260000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:560
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 464023420000000000000000000000000000)]),
  [value("f32", 681192700000000000)],
);

// ./test/core/float_misc.wast:561
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 47536.133)]), [value("f32", 218.02783)]);

// ./test/core/float_misc.wast:562
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.812613)]), [value("f32", 0.9014505)]);

// ./test/core/float_misc.wast:563
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 0.000000000000000000000000009549605)]),
  [value("f32", 0.00000000000009772208)],
);

// ./test/core/float_misc.wast:564
assert_return(
  () => invoke($0, `f32.sqrt`, [value("f32", 0.000000000000000000000000000068856485)]),
  [value("f32", 0.000000000000008297981)],
);

// ./test/core/float_misc.wast:565
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 2349768917495332200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 1532895599020146000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:566
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000029262574743429683),
  ]),
  [
    value("f64", 0.0000000000000000000000000000000000000000000000000000000005409489323718985),
  ],
);

// ./test/core/float_misc.wast:567
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 377335087484490800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 19425114864126050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:568
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.000000000000035498432023945234)]),
  [value("f64", 0.00000018841027579180822)],
);

// ./test/core/float_misc.wast:569
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013747419336166767),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000011724938949165905),
  ],
);

// ./test/core/float_misc.wast:572
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", -0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000015535152663257847),
  ]),
  [`canonical_nan`],
);

// ./test/core/float_misc.wast:573
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 18763296348029700000000000000000)]),
  [value("f64", 4331662076851067)],
);

// ./test/core/float_misc.wast:574
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000274405777036165),
  ]),
  [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000523837548325972),
  ],
);

// ./test/core/float_misc.wast:575
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 0.000000000000000000000000000000000000000000000000000000000000000000000000000000000015613859952920445),
  ]),
  [value("f64", 0.0000000000000000000000000000000000000000039514377070783294)],
);

// ./test/core/float_misc.wast:576
assert_return(
  () => invoke($0, `f64.sqrt`, [
    value("f64", 619303768945071200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000),
  ]),
  [
    value("f64", 24885814612848646000000000000000000000000000000000000000000000000000000000000000000000),
  ],
);

// ./test/core/float_misc.wast:579
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 1.0000001)]), [value("f32", 1)]);

// ./test/core/float_misc.wast:580
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 1.0000002)]), [value("f32", 1.0000001)]);

// ./test/core/float_misc.wast:581
assert_return(() => invoke($0, `f64.sqrt`, [value("f64", 1.0000000000000002)]), [value("f64", 1)]);

// ./test/core/float_misc.wast:582
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 1.0000000000000004)]),
  [value("f64", 1.0000000000000002)],
);

// ./test/core/float_misc.wast:585
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.9999999)]), [value("f32", 0.99999994)]);

// ./test/core/float_misc.wast:586
assert_return(() => invoke($0, `f32.sqrt`, [value("f32", 0.9999998)]), [value("f32", 0.9999999)]);

// ./test/core/float_misc.wast:587
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.9999999999999998)]),
  [value("f64", 0.9999999999999999)],
);

// ./test/core/float_misc.wast:588
assert_return(
  () => invoke($0, `f64.sqrt`, [value("f64", 0.9999999999999997)]),
  [value("f64", 0.9999999999999998)],
);

// ./test/core/float_misc.wast:592
assert_return(
  () => invoke($0, `f32.abs`, [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])],
);

// ./test/core/float_misc.wast:593
assert_return(
  () => invoke($0, `f32.abs`, [bytes("f32", [0xe2, 0xf1, 0x80, 0xff])]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])],
);

// ./test/core/float_misc.wast:594
assert_return(
  () => invoke($0, `f64.abs`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f])],
);

// ./test/core/float_misc.wast:595
assert_return(
  () => invoke($0, `f64.abs`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f])],
);

// ./test/core/float_misc.wast:597
assert_return(
  () => invoke($0, `f32.neg`, [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0xff])],
);

// ./test/core/float_misc.wast:598
assert_return(
  () => invoke($0, `f32.neg`, [bytes("f32", [0xe2, 0xf1, 0x80, 0xff])]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])],
);

// ./test/core/float_misc.wast:599
assert_return(
  () => invoke($0, `f64.neg`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff])],
);

// ./test/core/float_misc.wast:600
assert_return(
  () => invoke($0, `f64.neg`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f])],
);

// ./test/core/float_misc.wast:602
assert_return(
  () => invoke($0, `f32.copysign`, [
    bytes("f32", [0xe2, 0xf1, 0x80, 0x7f]),
    bytes("f32", [0x0, 0x0, 0xc0, 0x7f]),
  ]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])],
);

// ./test/core/float_misc.wast:603
assert_return(
  () => invoke($0, `f32.copysign`, [
    bytes("f32", [0xe2, 0xf1, 0x80, 0x7f]),
    bytes("f32", [0x0, 0x0, 0xc0, 0xff]),
  ]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0xff])],
);

// ./test/core/float_misc.wast:604
assert_return(
  () => invoke($0, `f32.copysign`, [
    bytes("f32", [0xe2, 0xf1, 0x80, 0xff]),
    bytes("f32", [0x0, 0x0, 0xc0, 0x7f]),
  ]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0x7f])],
);

// ./test/core/float_misc.wast:605
assert_return(
  () => invoke($0, `f32.copysign`, [
    bytes("f32", [0xe2, 0xf1, 0x80, 0xff]),
    bytes("f32", [0x0, 0x0, 0xc0, 0xff]),
  ]),
  [bytes("f32", [0xe2, 0xf1, 0x80, 0xff])],
);

// ./test/core/float_misc.wast:606
assert_return(
  () => invoke($0, `f64.copysign`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f]),
    bytes("f64", [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8, 0x7f]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f])],
);

// ./test/core/float_misc.wast:607
assert_return(
  () => invoke($0, `f64.copysign`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f]),
    bytes("f64", [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8, 0xff]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff])],
);

// ./test/core/float_misc.wast:608
assert_return(
  () => invoke($0, `f64.copysign`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff]),
    bytes("f64", [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8, 0x7f]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0x7f])],
);

// ./test/core/float_misc.wast:609
assert_return(
  () => invoke($0, `f64.copysign`, [
    bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff]),
    bytes("f64", [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8, 0xff]),
  ]),
  [bytes("f64", [0x6b, 0x7a, 0xe2, 0xf1, 0x0, 0x0, 0xf0, 0xff])],
);

// ./test/core/float_misc.wast:612
assert_return(() => invoke($0, `f32.ceil`, [value("f32", 0.99999994)]), [value("f32", 1)]);

// ./test/core/float_misc.wast:613
assert_return(() => invoke($0, `f32.ceil`, [value("f32", 1.0000001)]), [value("f32", 2)]);

// ./test/core/float_misc.wast:614
assert_return(() => invoke($0, `f64.ceil`, [value("f64", 0.9999999999999999)]), [value("f64", 1)]);

// ./test/core/float_misc.wast:615
assert_return(() => invoke($0, `f64.ceil`, [value("f64", 1.0000000000000002)]), [value("f64", 2)]);

// ./test/core/float_misc.wast:618
assert_return(() => invoke($0, `f32.ceil`, [value("f32", 8388607.5)]), [value("f32", 8388608)]);

// ./test/core/float_misc.wast:619
assert_return(() => invoke($0, `f32.ceil`, [value("f32", -8388607.5)]), [value("f32", -8388607)]);

// ./test/core/float_misc.wast:620
assert_return(
  () => invoke($0, `f64.ceil`, [value("f64", 4503599627370495.5)]),
  [value("f64", 4503599627370496)],
);

// ./test/core/float_misc.wast:621
assert_return(
  () => invoke($0, `f64.ceil`, [value("f64", -4503599627370495.5)]),
  [value("f64", -4503599627370495)],
);

// ./test/core/float_misc.wast:625
assert_return(() => invoke($0, `f32.ceil`, [value("f32", 16777215)]), [value("f32", 16777215)]);

// ./test/core/float_misc.wast:626
assert_return(() => invoke($0, `f32.ceil`, [value("f32", -16777215)]), [value("f32", -16777215)]);

// ./test/core/float_misc.wast:627
assert_return(
  () => invoke($0, `f64.ceil`, [value("f64", 9007199254740991)]),
  [value("f64", 9007199254740991)],
);

// ./test/core/float_misc.wast:628
assert_return(
  () => invoke($0, `f64.ceil`, [value("f64", -9007199254740991)]),
  [value("f64", -9007199254740991)],
);

// ./test/core/float_misc.wast:631
assert_return(() => invoke($0, `f32.floor`, [value("f32", -0.99999994)]), [value("f32", -1)]);

// ./test/core/float_misc.wast:632
assert_return(() => invoke($0, `f32.floor`, [value("f32", -1.0000001)]), [value("f32", -2)]);

// ./test/core/float_misc.wast:633
assert_return(() => invoke($0, `f64.floor`, [value("f64", -0.9999999999999999)]), [value("f64", -1)]);

// ./test/core/float_misc.wast:634
assert_return(() => invoke($0, `f64.floor`, [value("f64", -1.0000000000000002)]), [value("f64", -2)]);

// ./test/core/float_misc.wast:637
assert_return(() => invoke($0, `f32.floor`, [value("f32", -8388607.5)]), [value("f32", -8388608)]);

// ./test/core/float_misc.wast:638
assert_return(() => invoke($0, `f32.floor`, [value("f32", 8388607.5)]), [value("f32", 8388607)]);

// ./test/core/float_misc.wast:639
assert_return(
  () => invoke($0, `f64.floor`, [value("f64", -4503599627370495.5)]),
  [value("f64", -4503599627370496)],
);

// ./test/core/float_misc.wast:640
assert_return(
  () => invoke($0, `f64.floor`, [value("f64", 4503599627370495.5)]),
  [value("f64", 4503599627370495)],
);

// ./test/core/float_misc.wast:644
assert_return(() => invoke($0, `f32.floor`, [value("f32", 88607)]), [value("f32", 88607)]);

// ./test/core/float_misc.wast:645
assert_return(() => invoke($0, `f64.floor`, [value("f64", 88607)]), [value("f64", 88607)]);

// ./test/core/float_misc.wast:648
assert_return(() => invoke($0, `f32.trunc`, [value("f32", -8388607.5)]), [value("f32", -8388607)]);

// ./test/core/float_misc.wast:649
assert_return(() => invoke($0, `f32.trunc`, [value("f32", 8388607.5)]), [value("f32", 8388607)]);

// ./test/core/float_misc.wast:650
assert_return(
  () => invoke($0, `f64.trunc`, [value("f64", -4503599627370495.5)]),
  [value("f64", -4503599627370495)],
);

// ./test/core/float_misc.wast:651
assert_return(
  () => invoke($0, `f64.trunc`, [value("f64", 4503599627370495.5)]),
  [value("f64", 4503599627370495)],
);

// ./test/core/float_misc.wast:656
assert_return(() => invoke($0, `f32.nearest`, [value("f32", 8388609)]), [value("f32", 8388609)]);

// ./test/core/float_misc.wast:657
assert_return(() => invoke($0, `f32.nearest`, [value("f32", 8388610)]), [value("f32", 8388610)]);

// ./test/core/float_misc.wast:658
assert_return(() => invoke($0, `f32.nearest`, [value("f32", 0.49999997)]), [value("f32", 0)]);

// ./test/core/float_misc.wast:659
assert_return(
  () => invoke($0, `f32.nearest`, [value("f32", 281474960000000)]),
  [value("f32", 281474960000000)],
);

// ./test/core/float_misc.wast:660
assert_return(
  () => invoke($0, `f64.nearest`, [value("f64", 4503599627370497)]),
  [value("f64", 4503599627370497)],
);

// ./test/core/float_misc.wast:661
assert_return(
  () => invoke($0, `f64.nearest`, [value("f64", 4503599627370498)]),
  [value("f64", 4503599627370498)],
);

// ./test/core/float_misc.wast:662
assert_return(() => invoke($0, `f64.nearest`, [value("f64", 0.49999999999999994)]), [value("f64", 0)]);

// ./test/core/float_misc.wast:663
assert_return(
  () => invoke($0, `f64.nearest`, [value("f64", 81129638414606670000000000000000)]),
  [value("f64", 81129638414606670000000000000000)],
);

// ./test/core/float_misc.wast:667
assert_return(() => invoke($0, `f32.nearest`, [value("f32", 4.5)]), [value("f32", 4)]);

// ./test/core/float_misc.wast:668
assert_return(() => invoke($0, `f32.nearest`, [value("f32", -4.5)]), [value("f32", -4)]);

// ./test/core/float_misc.wast:669
assert_return(() => invoke($0, `f32.nearest`, [value("f32", -3.5)]), [value("f32", -4)]);

// ./test/core/float_misc.wast:670
assert_return(() => invoke($0, `f64.nearest`, [value("f64", 4.5)]), [value("f64", 4)]);

// ./test/core/float_misc.wast:671
assert_return(() => invoke($0, `f64.nearest`, [value("f64", -4.5)]), [value("f64", -4)]);

// ./test/core/float_misc.wast:672
assert_return(() => invoke($0, `f64.nearest`, [value("f64", -3.5)]), [value("f64", -4)]);

// ./test/core/float_misc.wast:675
assert_return(() => invoke($0, `f32.nearest`, [value("f32", -8388607.5)]), [value("f32", -8388608)]);

// ./test/core/float_misc.wast:676
assert_return(() => invoke($0, `f32.nearest`, [value("f32", 8388607.5)]), [value("f32", 8388608)]);

// ./test/core/float_misc.wast:677
assert_return(
  () => invoke($0, `f64.nearest`, [value("f64", -4503599627370495.5)]),
  [value("f64", -4503599627370496)],
);

// ./test/core/float_misc.wast:678
assert_return(
  () => invoke($0, `f64.nearest`, [value("f64", 4503599627370495.5)]),
  [value("f64", 4503599627370496)],
);
