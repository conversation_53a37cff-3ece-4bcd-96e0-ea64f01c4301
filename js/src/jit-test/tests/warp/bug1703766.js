function bar() { return 0;}

function foo(x) {
    switch (x) {
    case 0: bar(); break;
    case 1: bar(); break;
    case 2: bar(); break;
    case 3: bar(); break;
    case 4: bar(); break;
    case 5: bar(); break;
    case 6: bar(); break;
    case 7: bar(); break;
    case 8: bar(); break;
    case 9: bar(); break;
    case 10: bar(); break;
    case 11: bar(); break;
    case 12: bar(); break;
    case 13: bar(); break;
    case 14: bar(); break;
    case 15: bar(); break;
    case 16: bar(); break;
    case 17: bar(); break;
    case 18: bar(); break;
    case 19: bar(); break;
    case 20: bar(); break;
    case 21: bar(); break;
    case 22: bar(); break;
    case 23: bar(); break;
    case 24: bar(); break;
    case 25: bar(); break;
    case 26: bar(); break;
    case 27: bar(); break;
    case 28: bar(); break;
    case 29: bar(); break;
    case 30: bar(); break;
    case 31: bar(); break;
    case 32: bar(); break;
    case 33: bar(); break;
    case 34: bar(); break;
    case 35: bar(); break;
    case 36: bar(); break;
    case 37: bar(); break;
    case 38: bar(); break;
    case 39: bar(); break;
    case 40: bar(); break;
    case 41: bar(); break;
    case 42: bar(); break;
    case 43: bar(); break;
    case 44: bar(); break;
    case 45: bar(); break;
    case 46: bar(); break;
    case 47: bar(); break;
    case 48: bar(); break;
    case 49: bar(); break;
    case 50: bar(); break;
    case 51: bar(); break;
    case 52: bar(); break;
    case 53: bar(); break;
    case 54: bar(); break;
    case 55: bar(); break;
    case 56: bar(); break;
    case 57: bar(); break;
    case 58: bar(); break;
    case 59: bar(); break;
    case 60: bar(); break;
    case 61: bar(); break;
    case 62: bar(); break;
    case 63: bar(); break;
    case 64: bar(); break;
    case 65: bar(); break;
    case 66: bar(); break;
    case 67: bar(); break;
    case 68: bar(); break;
    case 69: bar(); break;
    case 70: bar(); break;
    case 71: bar(); break;
    case 72: bar(); break;
    case 73: bar(); break;
    case 74: bar(); break;
    case 75: bar(); break;
    case 76: bar(); break;
    case 77: bar(); break;
    case 78: bar(); break;
    case 79: bar(); break;
    case 80: bar(); break;
    case 81: bar(); break;
    case 82: bar(); break;
    case 83: bar(); break;
    case 84: bar(); break;
    case 85: bar(); break;
    case 86: bar(); break;
    case 87: bar(); break;
    case 88: bar(); break;
    case 89: bar(); break;
    case 90: bar(); break;
    case 91: bar(); break;
    case 92: bar(); break;
    case 93: bar(); break;
    case 94: bar(); break;
    case 95: bar(); break;
    case 96: bar(); break;
    case 97: bar(); break;
    case 98: bar(); break;
    case 99: bar(); break;
    case 100: bar(); break;
    case 101: bar(); break;
    case 102: bar(); break;
    case 103: bar(); break;
    case 104: bar(); break;
    case 105: bar(); break;
    case 106: bar(); break;
    case 107: bar(); break;
    case 108: bar(); break;
    case 109: bar(); break;
    case 110: bar(); break;
    case 111: bar(); break;
    case 112: bar(); break;
    case 113: bar(); break;
    case 114: bar(); break;
    case 115: bar(); break;
    case 116: bar(); break;
    case 117: bar(); break;
    case 118: bar(); break;
    case 119: bar(); break;
    case 120: bar(); break;
    case 121: bar(); break;
    case 122: bar(); break;
    case 123: bar(); break;
    case 124: bar(); break;
    case 125: bar(); break;
    case 126: bar(); break;
    case 127: bar(); break;
    case 128: bar(); break;
    case 129: bar(); break;
    case 130: bar(); break;
    case 131: bar(); break;
    case 132: bar(); break;
    case 133: bar(); break;
    case 134: bar(); break;
    case 135: bar(); break;
    case 136: bar(); break;
    case 137: bar(); break;
    case 138: bar(); break;
    case 139: bar(); break;
    case 140: bar(); break;
    case 141: bar(); break;
    case 142: bar(); break;
    case 143: bar(); break;
    case 144: bar(); break;
    case 145: bar(); break;
    case 146: bar(); break;
    case 147: bar(); break;
    case 148: bar(); break;
    case 149: bar(); break;
    case 150: bar(); break;
    case 151: bar(); break;
    case 152: bar(); break;
    case 153: bar(); break;
    case 154: bar(); break;
    case 155: bar(); break;
    case 156: bar(); break;
    case 157: bar(); break;
    case 158: bar(); break;
    case 159: bar(); break;
    case 160: bar(); break;
    case 161: bar(); break;
    case 162: bar(); break;
    case 163: bar(); break;
    case 164: bar(); break;
    case 165: bar(); break;
    case 166: bar(); break;
    case 167: bar(); break;
    case 168: bar(); break;
    case 169: bar(); break;
    case 170: bar(); break;
    case 171: bar(); break;
    case 172: bar(); break;
    case 173: bar(); break;
    case 174: bar(); break;
    case 175: bar(); break;
    case 176: bar(); break;
    case 177: bar(); break;
    case 178: bar(); break;
    case 179: bar(); break;
    case 180: bar(); break;
    case 181: bar(); break;
    case 182: bar(); break;
    case 183: bar(); break;
    case 184: bar(); break;
    case 185: bar(); break;
    case 186: bar(); break;
    case 187: bar(); break;
    case 188: bar(); break;
    case 189: bar(); break;
    case 190: bar(); break;
    case 191: bar(); break;
    case 192: bar(); break;
    case 193: bar(); break;
    case 194: bar(); break;
    case 195: bar(); break;
    case 196: bar(); break;
    case 197: bar(); break;
    case 198: bar(); break;
    case 199: bar(); break;
    case 200: bar(); break;
    }
    return x;
}

for (var i = 0; i < 5000; i++) {
    foo(1)
}
