// Binary: cache/js-dbg-64-290712e55ade-linux
// Flags: -j
//
var o9 = Function.prototype;
var o13 = Array;
function f5(o) {
ox1 = new Proxy(o, {});
}
f5(o9);
f5(o13);
var o0 = [];
function f3(o) {
            var prop = Object.getOwnPropertyNames(ox1)[0];
            if (prop) { Object.defineProperty(ox1, prop, {configurable: true,enumerable: true,unused: 1 }); }
}(function() {
for(var o3 in o0) {
f3(f3);
}
})();
  for (var i = 0; i < 9; i++)
  {
    new Array(1, 2);
  }
