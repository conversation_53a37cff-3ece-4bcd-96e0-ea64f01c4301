const f = (x) => 1;
for (let v4 = 0; v4 < 100; v4++) {
    for (const v8 of "arguments") {
        v8 == "a";
        Symbol.for().description;
        Symbol.for();
        Symbol.for();
    }
    const v16 = Symbol.search;
    let v17 = v16.description;
    Symbol.for("arguments");
    const v19 = v16.description;
    v17 = 0;
    const v21 = Symbol.for(v19).description;
    Symbol.for(v21);
    Symbol.for(v21);
    const v25 = Symbol.for().description;
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for().description;
    Symbol.for();
    Symbol.for();
    Symbol.for().description;
    Symbol.for();
    Symbol.for(v25).description;
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for();
    const v42 = (1024)[v16];
    const v43 = Symbol();
    Symbol.for(v19).description;
    Symbol.for();
    Symbol.for();
    Symbol.for(v21).description;
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol.for();
    Symbol[v43] ||= v42;
}
f(0);
f(0);
f(0);
f(0);
