// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "irregexp/imported/regexp-bytecodes.h"

#include <cctype>


namespace v8 {
namespace internal {

void RegExpBytecodeDisassembleSingle(const uint8_t* code_base,
                                     const uint8_t* pc) {
  int bytecode = *reinterpret_cast<const int32_t*>(pc) & BYTECODE_MASK;
  PrintF("%s", RegExpBytecodeName(bytecode));

  // Args and the bytecode as hex.
  for (int i = 0; i < RegExpBytecodeLength(bytecode); i++) {
    PrintF(", %02x", pc[i]);
  }
  PrintF(" ");

  // Args as ascii.
  for (int i = 1; i < RegExpBytecodeLength(bytecode); i++) {
    unsigned char b = pc[i];
    PrintF("%c", std::isprint(b) ? b : '.');
  }
  PrintF("\n");
}

void RegExpBytecodeDisassemble(const uint8_t* code_base, int length,
                               const char* pattern) {
  PrintF("[generated bytecode for regexp pattern: '%s']\n", pattern);

  ptrdiff_t offset = 0;

  while (offset < length) {
    const uint8_t* const pc = code_base + offset;
    PrintF("%p  %4" V8PRIxPTRDIFF "  ", pc, offset);
    RegExpBytecodeDisassembleSingle(code_base, pc);
    offset += RegExpBytecodeLength(*pc);
  }
}

}  // namespace internal
}  // namespace v8
