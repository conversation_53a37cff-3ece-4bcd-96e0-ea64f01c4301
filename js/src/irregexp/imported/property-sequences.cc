// Copyright 2018 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifdef V8_INTL_SUPPORT

#include "irregexp/imported/property-sequences.h"

namespace v8 {
namespace internal {

/*
Generated from following Node.js source:

package.json

```
{
  "private": true,
  "dependencies": {
    "unicode-12.0.0": "^0.7.9"
  }
}
```

generate-unicode-sequence-property-data.js

```
const toHex = (symbol) => {
  return '0x' + symbol.codePointAt(0).toString(16)
                      .toUpperCase().padStart(6, '0');
};

const generateData = (property) => {
  const sequences =
      require(`unicode-12.0.0/Sequence_Property/${ property }/index.js`);
  const id = property.replace(/_/g, '') + 's';
  const buffer = [];
  for (const sequence of sequences) {
    const symbols = [...sequence];
    const codePoints = symbols.map(symbol => toHex(symbol));
    buffer.push('    ' + codePoints.join(', ') + ', 0,');
  }
  const output =
      `const base::uc32 UnicodePropertySequences::k${ id }[] = {\n` +
      `${ buffer.join('\n') }\n    0  // null-terminating the list\n};\n`;
  return output;
};

const properties = [
  'Emoji_Flag_Sequence',
  'Emoji_Tag_Sequence',
  'Emoji_ZWJ_Sequence',
];

for (const property of properties) {
  console.log(generateData(property));
}
```
*/

// clang-format off
const base::uc32 UnicodePropertySequences::kEmojiFlagSequences[] = {
    0x01F1E6, 0x01F1E8, 0,
    0x01F1FF, 0x01F1FC, 0,
    0x01F1E6, 0x01F1EA, 0,
    0x01F1E6, 0x01F1EB, 0,
    0x01F1E6, 0x01F1EC, 0,
    0x01F1E6, 0x01F1EE, 0,
    0x01F1E6, 0x01F1F1, 0,
    0x01F1E6, 0x01F1F2, 0,
    0x01F1E6, 0x01F1F4, 0,
    0x01F1E6, 0x01F1F6, 0,
    0x01F1E6, 0x01F1F7, 0,
    0x01F1E6, 0x01F1F8, 0,
    0x01F1E6, 0x01F1F9, 0,
    0x01F1E6, 0x01F1FA, 0,
    0x01F1E6, 0x01F1FC, 0,
    0x01F1E6, 0x01F1FD, 0,
    0x01F1E6, 0x01F1FF, 0,
    0x01F1E7, 0x01F1E6, 0,
    0x01F1E7, 0x01F1E7, 0,
    0x01F1E7, 0x01F1E9, 0,
    0x01F1E7, 0x01F1EA, 0,
    0x01F1E7, 0x01F1EB, 0,
    0x01F1E7, 0x01F1EC, 0,
    0x01F1E7, 0x01F1ED, 0,
    0x01F1E7, 0x01F1EE, 0,
    0x01F1E7, 0x01F1EF, 0,
    0x01F1E7, 0x01F1F1, 0,
    0x01F1E7, 0x01F1F2, 0,
    0x01F1E7, 0x01F1F3, 0,
    0x01F1E7, 0x01F1F4, 0,
    0x01F1E7, 0x01F1F6, 0,
    0x01F1E7, 0x01F1F7, 0,
    0x01F1E7, 0x01F1F8, 0,
    0x01F1E7, 0x01F1F9, 0,
    0x01F1E7, 0x01F1FB, 0,
    0x01F1E7, 0x01F1FC, 0,
    0x01F1E7, 0x01F1FE, 0,
    0x01F1E7, 0x01F1FF, 0,
    0x01F1E8, 0x01F1E6, 0,
    0x01F1E8, 0x01F1E8, 0,
    0x01F1E8, 0x01F1E9, 0,
    0x01F1E8, 0x01F1EB, 0,
    0x01F1E8, 0x01F1EC, 0,
    0x01F1E8, 0x01F1ED, 0,
    0x01F1E8, 0x01F1EE, 0,
    0x01F1E8, 0x01F1F0, 0,
    0x01F1E8, 0x01F1F1, 0,
    0x01F1E8, 0x01F1F2, 0,
    0x01F1E8, 0x01F1F3, 0,
    0x01F1E8, 0x01F1F4, 0,
    0x01F1E8, 0x01F1F5, 0,
    0x01F1E8, 0x01F1F7, 0,
    0x01F1E8, 0x01F1FA, 0,
    0x01F1E8, 0x01F1FB, 0,
    0x01F1E8, 0x01F1FC, 0,
    0x01F1E8, 0x01F1FD, 0,
    0x01F1E8, 0x01F1FE, 0,
    0x01F1E8, 0x01F1FF, 0,
    0x01F1E9, 0x01F1EA, 0,
    0x01F1E9, 0x01F1EC, 0,
    0x01F1E9, 0x01F1EF, 0,
    0x01F1E9, 0x01F1F0, 0,
    0x01F1E9, 0x01F1F2, 0,
    0x01F1E9, 0x01F1F4, 0,
    0x01F1E9, 0x01F1FF, 0,
    0x01F1EA, 0x01F1E6, 0,
    0x01F1EA, 0x01F1E8, 0,
    0x01F1EA, 0x01F1EA, 0,
    0x01F1EA, 0x01F1EC, 0,
    0x01F1EA, 0x01F1ED, 0,
    0x01F1EA, 0x01F1F7, 0,
    0x01F1EA, 0x01F1F8, 0,
    0x01F1EA, 0x01F1F9, 0,
    0x01F1EA, 0x01F1FA, 0,
    0x01F1EB, 0x01F1EE, 0,
    0x01F1EB, 0x01F1EF, 0,
    0x01F1EB, 0x01F1F0, 0,
    0x01F1EB, 0x01F1F2, 0,
    0x01F1EB, 0x01F1F4, 0,
    0x01F1EB, 0x01F1F7, 0,
    0x01F1EC, 0x01F1E6, 0,
    0x01F1EC, 0x01F1E7, 0,
    0x01F1EC, 0x01F1E9, 0,
    0x01F1EC, 0x01F1EA, 0,
    0x01F1EC, 0x01F1EB, 0,
    0x01F1EC, 0x01F1EC, 0,
    0x01F1EC, 0x01F1ED, 0,
    0x01F1EC, 0x01F1EE, 0,
    0x01F1EC, 0x01F1F1, 0,
    0x01F1EC, 0x01F1F2, 0,
    0x01F1EC, 0x01F1F3, 0,
    0x01F1EC, 0x01F1F5, 0,
    0x01F1EC, 0x01F1F6, 0,
    0x01F1EC, 0x01F1F7, 0,
    0x01F1EC, 0x01F1F8, 0,
    0x01F1EC, 0x01F1F9, 0,
    0x01F1EC, 0x01F1FA, 0,
    0x01F1EC, 0x01F1FC, 0,
    0x01F1EC, 0x01F1FE, 0,
    0x01F1ED, 0x01F1F0, 0,
    0x01F1ED, 0x01F1F2, 0,
    0x01F1ED, 0x01F1F3, 0,
    0x01F1ED, 0x01F1F7, 0,
    0x01F1ED, 0x01F1F9, 0,
    0x01F1ED, 0x01F1FA, 0,
    0x01F1EE, 0x01F1E8, 0,
    0x01F1EE, 0x01F1E9, 0,
    0x01F1EE, 0x01F1EA, 0,
    0x01F1EE, 0x01F1F1, 0,
    0x01F1EE, 0x01F1F2, 0,
    0x01F1EE, 0x01F1F3, 0,
    0x01F1EE, 0x01F1F4, 0,
    0x01F1EE, 0x01F1F6, 0,
    0x01F1EE, 0x01F1F7, 0,
    0x01F1EE, 0x01F1F8, 0,
    0x01F1EE, 0x01F1F9, 0,
    0x01F1EF, 0x01F1EA, 0,
    0x01F1EF, 0x01F1F2, 0,
    0x01F1EF, 0x01F1F4, 0,
    0x01F1EF, 0x01F1F5, 0,
    0x01F1F0, 0x01F1EA, 0,
    0x01F1F0, 0x01F1EC, 0,
    0x01F1F0, 0x01F1ED, 0,
    0x01F1F0, 0x01F1EE, 0,
    0x01F1F0, 0x01F1F2, 0,
    0x01F1F0, 0x01F1F3, 0,
    0x01F1F0, 0x01F1F5, 0,
    0x01F1F0, 0x01F1F7, 0,
    0x01F1F0, 0x01F1FC, 0,
    0x01F1E6, 0x01F1E9, 0,
    0x01F1F0, 0x01F1FF, 0,
    0x01F1F1, 0x01F1E6, 0,
    0x01F1F1, 0x01F1E7, 0,
    0x01F1F1, 0x01F1E8, 0,
    0x01F1F1, 0x01F1EE, 0,
    0x01F1F1, 0x01F1F0, 0,
    0x01F1F1, 0x01F1F7, 0,
    0x01F1F1, 0x01F1F8, 0,
    0x01F1F1, 0x01F1F9, 0,
    0x01F1F1, 0x01F1FA, 0,
    0x01F1F1, 0x01F1FB, 0,
    0x01F1F1, 0x01F1FE, 0,
    0x01F1F2, 0x01F1E6, 0,
    0x01F1F2, 0x01F1E8, 0,
    0x01F1F2, 0x01F1E9, 0,
    0x01F1F2, 0x01F1EA, 0,
    0x01F1F2, 0x01F1EB, 0,
    0x01F1F2, 0x01F1EC, 0,
    0x01F1F2, 0x01F1ED, 0,
    0x01F1F2, 0x01F1F0, 0,
    0x01F1F2, 0x01F1F1, 0,
    0x01F1F2, 0x01F1F2, 0,
    0x01F1F2, 0x01F1F3, 0,
    0x01F1F2, 0x01F1F4, 0,
    0x01F1F2, 0x01F1F5, 0,
    0x01F1F2, 0x01F1F6, 0,
    0x01F1F2, 0x01F1F7, 0,
    0x01F1F2, 0x01F1F8, 0,
    0x01F1F2, 0x01F1F9, 0,
    0x01F1F2, 0x01F1FA, 0,
    0x01F1F2, 0x01F1FB, 0,
    0x01F1F2, 0x01F1FC, 0,
    0x01F1F2, 0x01F1FD, 0,
    0x01F1F2, 0x01F1FE, 0,
    0x01F1F2, 0x01F1FF, 0,
    0x01F1F3, 0x01F1E6, 0,
    0x01F1F3, 0x01F1E8, 0,
    0x01F1F3, 0x01F1EA, 0,
    0x01F1F3, 0x01F1EB, 0,
    0x01F1F3, 0x01F1EC, 0,
    0x01F1F3, 0x01F1EE, 0,
    0x01F1F3, 0x01F1F1, 0,
    0x01F1F3, 0x01F1F4, 0,
    0x01F1F3, 0x01F1F5, 0,
    0x01F1F3, 0x01F1F7, 0,
    0x01F1F3, 0x01F1FA, 0,
    0x01F1F3, 0x01F1FF, 0,
    0x01F1F4, 0x01F1F2, 0,
    0x01F1F5, 0x01F1E6, 0,
    0x01F1F5, 0x01F1EA, 0,
    0x01F1F5, 0x01F1EB, 0,
    0x01F1F5, 0x01F1EC, 0,
    0x01F1F5, 0x01F1ED, 0,
    0x01F1F5, 0x01F1F0, 0,
    0x01F1F5, 0x01F1F1, 0,
    0x01F1F5, 0x01F1F2, 0,
    0x01F1F5, 0x01F1F3, 0,
    0x01F1F5, 0x01F1F7, 0,
    0x01F1F5, 0x01F1F8, 0,
    0x01F1F5, 0x01F1F9, 0,
    0x01F1F5, 0x01F1FC, 0,
    0x01F1F5, 0x01F1FE, 0,
    0x01F1F6, 0x01F1E6, 0,
    0x01F1F7, 0x01F1EA, 0,
    0x01F1F7, 0x01F1F4, 0,
    0x01F1F7, 0x01F1F8, 0,
    0x01F1F7, 0x01F1FA, 0,
    0x01F1F7, 0x01F1FC, 0,
    0x01F1F8, 0x01F1E6, 0,
    0x01F1F8, 0x01F1E7, 0,
    0x01F1F8, 0x01F1E8, 0,
    0x01F1F8, 0x01F1E9, 0,
    0x01F1F8, 0x01F1EA, 0,
    0x01F1F8, 0x01F1EC, 0,
    0x01F1F8, 0x01F1ED, 0,
    0x01F1F8, 0x01F1EE, 0,
    0x01F1F8, 0x01F1EF, 0,
    0x01F1F8, 0x01F1F0, 0,
    0x01F1F8, 0x01F1F1, 0,
    0x01F1F8, 0x01F1F2, 0,
    0x01F1F8, 0x01F1F3, 0,
    0x01F1F8, 0x01F1F4, 0,
    0x01F1F8, 0x01F1F7, 0,
    0x01F1F8, 0x01F1F8, 0,
    0x01F1F8, 0x01F1F9, 0,
    0x01F1F8, 0x01F1FB, 0,
    0x01F1F8, 0x01F1FD, 0,
    0x01F1F8, 0x01F1FE, 0,
    0x01F1F8, 0x01F1FF, 0,
    0x01F1F9, 0x01F1E6, 0,
    0x01F1F9, 0x01F1E8, 0,
    0x01F1F9, 0x01F1E9, 0,
    0x01F1F9, 0x01F1EB, 0,
    0x01F1F9, 0x01F1EC, 0,
    0x01F1F9, 0x01F1ED, 0,
    0x01F1F9, 0x01F1EF, 0,
    0x01F1F9, 0x01F1F0, 0,
    0x01F1F9, 0x01F1F1, 0,
    0x01F1F9, 0x01F1F2, 0,
    0x01F1F9, 0x01F1F3, 0,
    0x01F1F9, 0x01F1F4, 0,
    0x01F1F9, 0x01F1F7, 0,
    0x01F1F9, 0x01F1F9, 0,
    0x01F1F9, 0x01F1FB, 0,
    0x01F1F9, 0x01F1FC, 0,
    0x01F1F9, 0x01F1FF, 0,
    0x01F1FA, 0x01F1E6, 0,
    0x01F1FA, 0x01F1EC, 0,
    0x01F1FA, 0x01F1F2, 0,
    0x01F1FA, 0x01F1F3, 0,
    0x01F1FA, 0x01F1F8, 0,
    0x01F1FA, 0x01F1FE, 0,
    0x01F1FA, 0x01F1FF, 0,
    0x01F1FB, 0x01F1E6, 0,
    0x01F1FB, 0x01F1E8, 0,
    0x01F1FB, 0x01F1EA, 0,
    0x01F1FB, 0x01F1EC, 0,
    0x01F1FB, 0x01F1EE, 0,
    0x01F1FB, 0x01F1F3, 0,
    0x01F1FB, 0x01F1FA, 0,
    0x01F1FC, 0x01F1EB, 0,
    0x01F1FC, 0x01F1F8, 0,
    0x01F1FD, 0x01F1F0, 0,
    0x01F1FE, 0x01F1EA, 0,
    0x01F1FE, 0x01F1F9, 0,
    0x01F1FF, 0x01F1E6, 0,
    0x01F1FF, 0x01F1F2, 0,
    0x01F1F0, 0x01F1FE, 0,
    0  // null-terminating the list
};

const base::uc32 UnicodePropertySequences::kEmojiTagSequences[] = {
    0x01F3F4, 0x0E0067, 0x0E0062, 0x0E0065, 0x0E006E, 0x0E0067, 0x0E007F, 0,
    0x01F3F4, 0x0E0067, 0x0E0062, 0x0E0073, 0x0E0063, 0x0E0074, 0x0E007F, 0,
    0x01F3F4, 0x0E0067, 0x0E0062, 0x0E0077, 0x0E006C, 0x0E0073, 0x0E007F, 0,
    0  // null-terminating the list
};

const base::uc32 UnicodePropertySequences::kEmojiZWJSequences[] = {
    0x01F468, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F468, 0,
    0x01F441, 0x00FE0F, 0x00200D, 0x01F5E8, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F466, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F467, 0,
    0x01F468, 0x00200D, 0x01F467, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F467, 0x00200D, 0x01F467, 0,
    0x01F468, 0x00200D, 0x01F468, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F468, 0x00200D, 0x01F466, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F468, 0x00200D, 0x01F467, 0,
    0x01F468, 0x00200D, 0x01F468, 0x00200D, 0x01F467, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F468, 0x00200D, 0x01F467, 0x00200D, 0x01F467, 0,
    0x01F468, 0x00200D, 0x01F469, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F469, 0x00200D, 0x01F466, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0,
    0x01F468, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F466, 0,
    0x01F468, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F467, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FE, 0,
    0x01F469, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F468, 0,
    0x01F469, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F469, 0,
    0x01F469, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F48B, 0x00200D,
        0x01F468, 0,
    0x01F469, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F48B, 0x00200D,
        0x01F469, 0,
    0x01F469, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F466, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F467, 0,
    0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F467, 0,
    0x01F469, 0x00200D, 0x01F469, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F469, 0x00200D, 0x01F466, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0,
    0x01F469, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F466, 0,
    0x01F469, 0x00200D, 0x01F469, 0x00200D, 0x01F467, 0x00200D, 0x01F467, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FE, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FF, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FE, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FF, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FB, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FE, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FF, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FB, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FC, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FF, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FB, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FC, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FD, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FB, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FC, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FD, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F468, 0x01F3FE, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FB, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FC, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FD, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F469, 0x01F3FE, 0,
    0x01F9D1, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0,
    0x01F9D1, 0x01F3FB, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FB, 0,
    0x01F9D1, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FB, 0,
    0x01F9D1, 0x01F3FC, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FC, 0,
    0x01F9D1, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FB, 0,
    0x01F9D1, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FC, 0,
    0x01F9D1, 0x01F3FD, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FD, 0,
    0x01F9D1, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FB, 0,
    0x01F9D1, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FC, 0,
    0x01F9D1, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FD, 0,
    0x01F9D1, 0x01F3FE, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FE, 0,
    0x01F9D1, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FB, 0,
    0x01F9D1, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FC, 0,
    0x01F9D1, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FD, 0,
    0x01F9D1, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FE, 0,
    0x01F9D1, 0x01F3FF, 0x00200D, 0x01F91D, 0x00200D, 0x01F9D1, 0x01F3FF, 0,
    0x01F468, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x00200D, 0x01F373, 0,
    0x01F468, 0x00200D, 0x01F393, 0,
    0x01F468, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x00200D, 0x01F527, 0,
    0x01F468, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x00200D, 0x01F680, 0,
    0x01F468, 0x00200D, 0x01F692, 0,
    0x01F468, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x00200D, 0x01F9BD, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F373, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F393, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F527, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F680, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F692, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9BD, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F373, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F393, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F527, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F680, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F692, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9BD, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F373, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F393, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F527, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F680, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F692, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9BD, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F373, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F393, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F527, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F680, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F692, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9BD, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F33E, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F373, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F393, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F3A4, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F3A8, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F3EB, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F3ED, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F4BB, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F4BC, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F527, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F52C, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F680, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F692, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9AF, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9BC, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x00200D, 0x01F373, 0,
    0x01F469, 0x00200D, 0x01F393, 0,
    0x01F469, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x00200D, 0x01F527, 0,
    0x01F469, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x00200D, 0x01F680, 0,
    0x01F469, 0x00200D, 0x01F692, 0,
    0x01F469, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F373, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F393, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F527, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F680, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F692, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F373, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F393, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F527, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F680, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F692, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F373, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F393, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F527, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F680, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F692, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F373, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F393, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F527, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F680, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F692, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9BD, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x002695, 0x00FE0F, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x002696, 0x00FE0F, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x002708, 0x00FE0F, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F33E, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F373, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F393, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F3A4, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F3A8, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F3EB, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F3ED, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F4BB, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F4BC, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F527, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F52C, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F680, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F692, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9AF, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9BC, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9BD, 0,
    0x0026F9, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x0026F9, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x0026F9, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x0026F9, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x0026F9, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x0026F9, 0x00FE0F, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x0026F9, 0x00FE0F, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C3, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3C4, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CA, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CB, 0x00FE0F, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CB, 0x00FE0F, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F3CC, 0x00FE0F, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F3CC, 0x00FE0F, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46E, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46E, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F46F, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F46F, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F471, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F471, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F473, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F473, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F477, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F477, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F481, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F481, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F482, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F482, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x002764, 0x00FE0F, 0x00200D, 0x01F48B, 0x00200D,
        0x01F468, 0,
    0x01F482, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F482, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F482, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F482, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F482, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F482, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F482, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F482, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F482, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F486, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F486, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F487, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F487, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F575, 0x00FE0F, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F575, 0x00FE0F, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F645, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F645, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F646, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F646, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F647, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F647, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64B, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64B, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64D, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64D, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F64E, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F64E, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6A3, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B4, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B5, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F6B6, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F926, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F926, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F937, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F937, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F938, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F938, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F939, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F939, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93C, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93C, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93D, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93D, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F93E, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F93E, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B8, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9B9, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CD, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CE, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9CF, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D6, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D7, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D8, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9D9, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DA, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DB, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DC, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FB, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FC, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FC, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FD, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FD, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DD, 0x01F3FF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DE, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DE, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F9DF, 0x00200D, 0x002640, 0x00FE0F, 0,
    0x01F9DF, 0x00200D, 0x002642, 0x00FE0F, 0,
    0x01F468, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x00200D, 0x01F9B3, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x01F3FB, 0x00200D, 0x01F9B3, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x01F3FC, 0x00200D, 0x01F9B3, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x01F3FD, 0x00200D, 0x01F9B3, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x01F3FE, 0x00200D, 0x01F9B3, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9B0, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9B1, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9B2, 0,
    0x01F468, 0x01F3FF, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x01F3FB, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x01F3FC, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x01F3FD, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x01F3FE, 0x00200D, 0x01F9B3, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9B0, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9B1, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9B2, 0,
    0x01F469, 0x01F3FF, 0x00200D, 0x01F9B3, 0,
    0x01F3F3, 0x00FE0F, 0x00200D, 0x01F308, 0,
    0x01F3F4, 0x00200D, 0x002620, 0x00FE0F, 0,
    0x01F415, 0x00200D, 0x01F9BA, 0,
    0x01F482, 0x01F3FB, 0x00200D, 0x002640, 0x00FE0F, 0,
    0  // null-terminating the list
};
// clang-format on

}  // namespace internal
}  // namespace v8

#endif  // V8_INTL_SUPPORT
