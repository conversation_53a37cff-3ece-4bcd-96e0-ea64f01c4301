// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.

// Automatically generated by regexp/gen-regexp-special-case.cc

// The following functions are used to build UnicodeSets
// for special cases where the case-folding algorithm used by
// UnicodeSet::closeOver(USET_CASE_INSENSITIVE) does not match
// the algorithm defined in ECMAScript 2020 ********.2 (Runtime
// Semantics: Canonicalize) step 3.

#ifdef V8_INTL_SUPPORT
#include "irregexp/imported/special-case.h"

#include "unicode/uniset.h"
namespace v8 {
namespace internal {

icu::UnicodeSet BuildIgnoreSet() {
  icu::UnicodeSet set;
  set.add(0xdf);
  set.add(0x17f);
  set.add(0x390);
  set.add(0x3b0);
  set.add(0x3f4);
  set.add(0x1e9e);
  set.add(0x1f80, 0x1faf);
  set.add(0x1fb3);
  set.add(0x1fbc);
  set.add(0x1fc3);
  set.add(0x1fcc);
  set.add(0x1fd3);
  set.add(0x1fe3);
  set.add(0x1ff3);
  set.add(0x1ffc);
  set.add(0x2126);
  set.add(0x212a, 0x212b);
  set.add(0xfb05, 0xfb06);
  set.freeze();
  return set;
}

struct IgnoreSetData {
  IgnoreSetData() : set(BuildIgnoreSet()) {}
  const icu::UnicodeSet set;
};

//static
const icu::UnicodeSet& RegExpCaseFolding::IgnoreSet() {
  static base::LazyInstance<IgnoreSetData>::type set =
      LAZY_INSTANCE_INITIALIZER;
  return set.Pointer()->set;
}

icu::UnicodeSet BuildSpecialAddSet() {
  icu::UnicodeSet set;
  set.add(0x4b);
  set.add(0x53);
  set.add(0x6b);
  set.add(0x73);
  set.add(0xc5);
  set.add(0xe5);
  set.add(0x398);
  set.add(0x3a9);
  set.add(0x3b8);
  set.add(0x3c9);
  set.add(0x3d1);
  set.freeze();
  return set;
}

struct SpecialAddSetData {
  SpecialAddSetData() : set(BuildSpecialAddSet()) {}
  const icu::UnicodeSet set;
};

//static
const icu::UnicodeSet& RegExpCaseFolding::SpecialAddSet() {
  static base::LazyInstance<SpecialAddSetData>::type set =
      LAZY_INSTANCE_INITIALIZER;
  return set.Pointer()->set;
}


}  // namespace internal
}  // namespace v8
#endif  // V8_INTL_SUPPORT
