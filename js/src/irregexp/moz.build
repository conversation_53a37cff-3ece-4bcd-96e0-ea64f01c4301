# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

FINAL_LIBRARY = "js"

# Includes should be relative to parent path
LOCAL_INCLUDES += ["!..", ".."]

include("../js-config.mozbuild")
include("../js-cxxflags.mozbuild")

# Suppress spurious warnings in third-party code.
# See bug 1810584, bug 1879225, and bug 1895871.
CXXFLAGS += ["-Wno-error=type-limits", "-Wno-error=return-type", "-Wno-sign-compare"]
if CONFIG["CC_TYPE"] == "gcc":
    CXXFLAGS += ["-Wno-error=nonnull", "-Wno-narrowing"]
if CONFIG["CC_TYPE"] in ("clang", "clang-cl"):
    CXXFLAGS += ["-Wno-c++11-narrowing"]


UNIFIED_SOURCES += [
    "imported/regexp-bytecode-generator.cc",
    "imported/regexp-bytecode-peephole.cc",
    "imported/regexp-bytecodes.cc",
    "imported/regexp-compiler-tonode.cc",
    "imported/regexp-dotprinter.cc",
    "imported/regexp-interpreter.cc",
    "imported/regexp-macro-assembler-tracer.cc",
    "imported/regexp-macro-assembler.cc",
    "imported/regexp-parser.cc",
    "imported/regexp-stack.cc",
    "RegExpAPI.cpp",
    "RegExpShim.cpp",
    "util/UnicodeShim.cpp",
]
SOURCES += [
    "imported/regexp-ast.cc",
    "imported/regexp-compiler.cc",  # Bug 1643693
    "RegExpNativeMacroAssembler.cpp",
]

if CONFIG["JS_HAS_INTL_API"]:
    CXXFLAGS += ["-DV8_INTL_SUPPORT"]
    UNIFIED_SOURCES += ["imported/property-sequences.cc", "imported/special-case.cc"]

# Make sure all irregexp code is built with libfuzzer
# coverage instrumentation in FUZZING mode.
if CONFIG["FUZZING_INTERFACES"] and CONFIG["LIBFUZZER"]:
    include("/tools/fuzzing/libfuzzer-config.mozbuild")
