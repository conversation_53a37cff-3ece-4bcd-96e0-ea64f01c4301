schema: 1

bugzilla:
  product: Core
  component: "JavaScript Engine"

origin:
  name: irregexp
  description: A fast regular expression engine from V8
  url: https://v8.dev

  release: c00e5775d47580fe06bf8918346a2aa832782aed (Fri Dec 13 17:10:06 2024).
  revision: c00e5775d47580fe06bf8918346a2aa832782aed

  license: BSD-3-Clause
  license-file: LICENSE.v8

vendoring:
  url: https://chromium.googlesource.com/v8/v8.git
  source-hosting: googlesource
  vendor-directory: js/src/irregexp/
  skip-vendoring-steps: ['fetch', 'move-contents']

  update-actions:
    - action: run-script
      script: 'import-irregexp.py'
      cwd: '{yaml_dir}'

updatebot:
  maintainer-phab: iain
  maintainer-bz: <EMAIL>
  try-preset: sm-shell
  tasks:
    - type: vendoring
      enabled: True
      frequency: 1 week
