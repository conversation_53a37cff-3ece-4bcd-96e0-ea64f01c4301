# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

include("../js-config.mozbuild")
include("../js-cxxflags.mozbuild")

FINAL_LIBRARY = "js"

with Files("*"):
    BUG_COMPONENT = ("Core", "JavaScript: WebAssembly")

LOCAL_INCLUDES += [
    "!..",
    "..",
    "/third_party/gemmology",
    "/third_party/xsimd/include",
]

if CONFIG["INTEL_ARCHITECTURE"]:
    DEFINES["USE_SSE2"] = True
    SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineSSE2.cpp"]
    SOURCES["/third_party/gemmology/kernels/GemmologyEngineSSE2.cpp"].flags += CONFIG[
        "SSE2_FLAGS"
    ]
    if CONFIG["SSSE3_FLAGS"]:
        DEFINES["USE_SSSE3"] = True
        SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineSSSE3.cpp"]
        SOURCES[
            "/third_party/gemmology/kernels/GemmologyEngineSSSE3.cpp"
        ].flags += CONFIG["SSSE3_FLAGS"]
    if CONFIG["AVX2_FLAGS"]:
        DEFINES["USE_AVX2"] = True
        SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineAVX2.cpp"]
        SOURCES[
            "/third_party/gemmology/kernels/GemmologyEngineAVX2.cpp"
        ].flags += CONFIG["AVX2_FLAGS"]
    if CONFIG["AVXVNNI_FLAGS"]:
        DEFINES["USE_AVXVNNI"] = True
        SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineAVXVNNI.cpp"]
        SOURCES[
            "/third_party/gemmology/kernels/GemmologyEngineAVXVNNI.cpp"
        ].flags += CONFIG["AVXVNNI_FLAGS"]
    if CONFIG["AVX512BW_FLAGS"]:
        DEFINES["USE_AVX512BW"] = True
        SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineAVX512BW.cpp"]
        SOURCES[
            "/third_party/gemmology/kernels/GemmologyEngineAVX512BW.cpp"
        ].flags += CONFIG["AVX512BW_FLAGS"]
    if CONFIG["AVX512VNNI_FLAGS"]:
        DEFINES["USE_AVX512VNNI"] = True
        SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineAVX512VNNI.cpp"]
        SOURCES[
            "/third_party/gemmology/kernels/GemmologyEngineAVX512VNNI.cpp"
        ].flags += CONFIG["AVX512VNNI_FLAGS"]


if CONFIG["TARGET_CPU"] == "aarch64":
    DEFINES["USE_NEON"] = True
    SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineNeon64.cpp"]
    SOURCES["/third_party/gemmology/kernels/GemmologyEngineNeon64.cpp"].flags += CONFIG[
        "NEON_FLAGS"
    ]

    DEFINES["USE_NEON_I8MM"] = True
    SOURCES += ["/third_party/gemmology/kernels/GemmologyEngineNeon64I8mm.cpp"]
    SOURCES[
        "/third_party/gemmology/kernels/GemmologyEngineNeon64I8mm.cpp"
    ].flags += CONFIG["NEON_I8MM_FLAGS"]

SOURCES += [
    "IntegerGemmIntrinsic.cpp",
]

# We allow warnings for third-party code that can be updated from upstream.
AllowCompilerWarnings()
