# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 3

[[package]]
name = "aa-stroke"
version = "0.1.0"
source = "git+https://github.com/FirefoxGraphics/aa-stroke?rev=a821fa621c2def48e90c82774b4c6563b5a8ea4a#a821fa621c2def48e90c82774b4c6563b5a8ea4a"
dependencies = [
 "euclid",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f2135563fb5c609d2b2b87c1e8ce7bc41b0b45430fa9661f457981503dd5bf0"
dependencies = [
 "memchr",
]

[[package]]
name = "allocator-api2"
version = "0.2.999"
dependencies = [
 "serde",
]

[[package]]
name = "alsa"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce34de545ad29bcc00cb1b87a94c132256dcf83aa7eeb9674482568405a6ff0a"
dependencies = [
 "alsa-sys",
 "bitflags 2.6.0",
 "libc",
 "nix 0.26.99",
]

[[package]]
name = "alsa-sys"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db8fee663d06c4e303404ef5f40488a53e062f89ba8bfed81f42325aafad1527"
dependencies = [
 "libc",
 "pkg-config",
]

[[package]]
name = "android_log-sys"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85965b6739a430150bdd138e2374a98af0c3ee0d030b3bb7fc3bddff58d0102e"

[[package]]
name = "android_logger"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "037f3e1da32ddba7770530e69258b742c15ad67bdf90e5f6b35f4b6db9a60eb7"
dependencies = [
 "android_log-sys",
 "env_logger",
 "log",
 "once_cell",
]

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "anstyle"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bec1de6f59aedf83baf9ff929c98f2ad654b97c9510f4e70cf6f661d49fd5b1"

[[package]]
name = "any_all_workaround"
version = "0.1.0"
source = "git+https://github.com/hsivonen/any_all_workaround?rev=7fb1b7034c9f172aade21ee1c8554e8d8a48af80#7fb1b7034c9f172aade21ee1c8554e8d8a48af80"
dependencies = [
 "cfg-if",
 "version_check",
]

[[package]]
name = "anyhow"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "224afbd727c3d6e4b90103ece64b8d1b67fbb1973b1046c2281eed3f3803f800"

[[package]]
name = "app_services_logger"
version = "0.1.0"
dependencies = [
 "cstr",
 "golden_gate",
 "log",
 "nserror",
 "nsstring",
 "once_cell",
 "xpcom",
]

[[package]]
name = "app_units"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34655f9657fbb59fdd430438ba907566eb2d90b7fb0410d488d8ddabcd7b2cc9"
dependencies = [
 "num-traits",
 "serde",
]

[[package]]
name = "arbitrary"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d5a26814d8dcb93b0e5a0ff3c6d80a8843bafb21b39e8e18a6f05471870e110"
dependencies = [
 "derive_arbitrary",
]

[[package]]
name = "arrayref"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4c527152e37cf757a3f78aae5a06fbeefdb07ccc535c980a3208ee3060dd544"

[[package]]
name = "arraystring"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d517c467117e1d8ca795bc8cc90857ff7f79790cca0e26f6e9462694ece0185"
dependencies = [
 "typenum",
]

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"
dependencies = [
 "serde",
]

[[package]]
name = "ash"
version = "0.38.0+1.3.281"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bb44936d800fea8f016d7f2311c6a4f97aebd5dc86f09906139ec848cf3a46f"
dependencies = [
 "libloading",
]

[[package]]
name = "ashmem"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b947f77692187a29daa2768b6644e589cef61e7434bb96300197acfb876105ac"
dependencies = [
 "ioctl-sys",
 "libc",
]

[[package]]
name = "askama"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47cbc3cf73fa8d9833727bbee4835ba5c421a0d65b72daf9a7b5d0e0f9cfb57e"
dependencies = [
 "askama_derive",
 "askama_escape",
]

[[package]]
name = "askama_derive"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c22fbe0413545c098358e56966ff22cdd039e10215ae213cfbd65032b119fc94"
dependencies = [
 "basic-toml",
 "mime",
 "mime_guess",
 "nom",
 "proc-macro2",
 "quote",
 "serde",
 "syn",
]

[[package]]
name = "askama_escape"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "619743e34b5ba4e9703bba34deac3427c72507c7159f5fd030aea8cac0cfe341"

[[package]]
name = "async-task"
version = "4.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a40729d2133846d9ed0ea60a8b9541bccddab49cd30f0715a1da672fe9a2524"

[[package]]
name = "async-trait"
version = "0.1.68"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9ccdd8f2a161be9bd5c023df56f1b2a0bd1d83872ae53b71a84a12c9bf6e842"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "atomic"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64f46ca51dca4837f1520754d1c8c36636356b81553d928dc9c177025369a06e"

[[package]]
name = "atomic_refcell"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "857253367827bd9d0fd973f0ef15506a96e79e41b0ad7aa691203a4e3214f6c8"

[[package]]
name = "audio-mixer"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f1245ab2f85c284bac1ac1f03565539644566295ef6eb9c4eae42e9a40c51b2"
dependencies = [
 "bitflags 1.999.999",
]

[[package]]
name = "audio_thread_priority"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3632611da7e79f8fc8fd75840f1ccfa7792dbf1e25d00791344a4450dd8834f"
dependencies = [
 "cfg-if",
 "dbus",
 "libc",
 "log",
 "mach",
 "windows-sys",
]

[[package]]
name = "audioipc2"
version = "0.6.0"
source = "git+https://github.com/mozilla/audioipc?rev=e6f44a2bd1e57d11dfc737632a9e849077632330#e6f44a2bd1e57d11dfc737632a9e849077632330"
dependencies = [
 "arrayvec",
 "ashmem",
 "audio_thread_priority",
 "bincode",
 "byteorder",
 "bytes",
 "cc",
 "crossbeam-queue",
 "cubeb",
 "error-chain",
 "iovec",
 "libc",
 "log",
 "memmap2",
 "mio",
 "scopeguard",
 "serde",
 "serde_bytes",
 "serde_derive",
 "slab",
 "windows-sys",
]

[[package]]
name = "audioipc2-client"
version = "0.6.0"
source = "git+https://github.com/mozilla/audioipc?rev=e6f44a2bd1e57d11dfc737632a9e849077632330#e6f44a2bd1e57d11dfc737632a9e849077632330"
dependencies = [
 "audio_thread_priority",
 "audioipc2",
 "cubeb-backend",
 "log",
]

[[package]]
name = "audioipc2-server"
version = "0.6.0"
source = "git+https://github.com/mozilla/audioipc?rev=e6f44a2bd1e57d11dfc737632a9e849077632330#e6f44a2bd1e57d11dfc737632a9e849077632330"
dependencies = [
 "audio_thread_priority",
 "audioipc2",
 "cubeb-core",
 "error-chain",
 "log",
 "once_cell",
 "slab",
]

[[package]]
name = "authenticator"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82d71e457dc518a15eecc90d3b0660dee4b51623b34ac4262c9326e0d7e0f8e2"
dependencies = [
 "base64 0.21.3",
 "bitflags 1.999.999",
 "cfg-if",
 "core-foundation",
 "devd-rs",
 "libc",
 "libudev",
 "log",
 "memoffset 0.8.999",
 "nss-gk-api",
 "pkcs11-bindings",
 "rand",
 "runloop",
 "serde",
 "serde_bytes",
 "serde_cbor",
 "serde_json",
 "sha2",
 "winapi",
]

[[package]]
name = "authrs_bridge"
version = "0.1.0"
dependencies = [
 "authenticator",
 "base64 0.21.3",
 "cstr",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "rand",
 "serde",
 "serde_cbor",
 "serde_json",
 "static_prefs",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "autocfg"
version = "1.1.0"

[[package]]
name = "backtrace"
version = "0.3.999"

[[package]]
name = "base64"
version = "0.13.999"
dependencies = [
 "base64 0.21.3",
]

[[package]]
name = "base64"
version = "0.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "414dcefbc63d77c526a76b3afcf6fbb9b5e2791c19c3aa2297733208750c6e53"

[[package]]
name = "basic-toml"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c0de75129aa8d0cceaf750b89013f0e08804d6ec61416da787b35ad0d7cddf1"
dependencies = [
 "serde",
]

[[package]]
name = "bench-collections-gtest"
version = "0.1.0"
dependencies = [
 "fnv",
 "fxhash",
]

[[package]]
name = "bhttp"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1300dca7a20730cce82c33fbf8795862556645ef5e9ee835390278c3fe1eb1d0"
dependencies = [
 "thiserror",
]

[[package]]
name = "binary_http"
version = "0.1.0"
dependencies = [
 "bhttp",
 "nserror",
 "nsstring",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.64.999"
dependencies = [
 "bindgen 0.69.4",
]

[[package]]
name = "bindgen"
version = "0.69.4"
dependencies = [
 "bitflags 2.6.0",
 "cexpr",
 "clang-sys",
 "itertools",
 "lazy_static",
 "lazycell",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex",
 "syn",
]

[[package]]
name = "bit-set"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08807e080ed7f9d5433fa9b275196cfc35414f66a0c79d864dc51a0d825231a3"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e764a1d40d510daf35e07be9eb06e75770908c27d411ee6c92109c9840eaaf7"

[[package]]
name = "bitflags"
version = "1.999.999"
dependencies = [
 "bitflags 2.6.0",
]

[[package]]
name = "bitflags"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b048fb63fd8b5923fc5aa7b340d8e156aec7ec02f0c78fa8a6ddc2613f6f71de"
dependencies = [
 "serde",
]

[[package]]
name = "bitreader"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d84ea71c85d1fe98fe67a9b9988b1695bc24c0b0d3bfb18d4c510f44b4b09941"
dependencies = [
 "cfg-if",
]

[[package]]
name = "bits"
version = "0.2.0"
dependencies = [
 "comedy",
 "filetime_win",
 "guid_win",
 "serde",
 "serde_derive",
 "winapi",
]

[[package]]
name = "bits_client"
version = "0.2.0"
dependencies = [
 "bits",
 "comedy",
 "guid_win",
 "lazy_static",
 "rand",
 "regex",
 "tempfile",
 "thiserror",
]

[[package]]
name = "bitsdownload"
version = "0.1.0"
dependencies = [
 "bits_client",
 "comedy",
 "crossbeam-utils",
 "libc",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "xpcom",
]

[[package]]
name = "block"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d8c1fef690941d3e7788d328517591fecc684c084084702d6ff1641e993699a"

[[package]]
name = "block-buffer"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69cce20737498f97b993470a6e536b8523f0af7892a4f928cceb1ac5e52ebe7e"
dependencies = [
 "generic-array",
]

[[package]]
name = "bookmark_sync"
version = "0.1.0"
dependencies = [
 "atomic_refcell",
 "cstr",
 "dogear",
 "libc",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "storage",
 "storage_variant",
 "url",
 "xpcom",
]

[[package]]
name = "breakpad-symbols"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6aeaa2a7f839cbb61c2f59ad6e51cc3fd2c24aa2103cb24e6be143bcc114aa24"
dependencies = [
 "async-trait",
 "cachemap2",
 "circular",
 "debugid",
 "futures-util",
 "minidump-common",
 "nom",
 "range-map",
 "thiserror",
 "tracing",
]

[[package]]
name = "build-parallel"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8e3ff9db740167616e528c509b3618046fc05d337f8f3182d300f4aa977d2bb"
dependencies = [
 "crossbeam-utils",
 "jobserver",
 "num_cpus",
]

[[package]]
name = "buildid_reader"
version = "0.1.0"
dependencies = [
 "goblin 0.8.2",
 "libc",
 "log",
 "nserror",
 "nsstring",
 "scroll",
]

[[package]]
name = "builtins-static"
version = "0.1.0"
dependencies = [
 "bindgen 0.69.4",
 "mozbuild",
 "mozilla-central-workspace-hack",
 "nom",
 "pkcs11-bindings",
 "smallvec",
]

[[package]]
name = "bumpalo"
version = "3.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ff69b9dd49fd426c69a0db9fc04dd934cdb6645ff000864d98f7e2af8830eaa"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b2fd2a0dcf38d7971e2194b6b6eebab45ae01067456a7fd93d5547a61b70be"

[[package]]
name = "cache-padded"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1db59621ec70f09c5e9b597b220c7a2b43611f4710dc03ceb8748637775692c"

[[package]]
name = "cachemap2"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68ccbd3153aa153b2f5eff557537ffce81e4dd6c50ae0eddc41dc8d0c388436f"

[[package]]
name = "calendrical_calculations"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cec493b209a1b81fa32312d7ceca1b547d341c7b5f16a3edbf32b1d8b455bbdf"
dependencies = [
 "core_maths",
 "displaydoc",
]

[[package]]
name = "camino"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c77df041dc383319cc661b428b6961a005db4d6808d5e12536931b1ca9556055"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbdb825da8a5df079a43676dbe042702f1707b1109f713a01420fbb4cc71fa27"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08a1ec454bc3eead8719cb56e15dbbfecdbc14e4b3a3ae4936cc6e31f5fc0d07"
dependencies = [
 "camino",
 "cargo-platform",
 "semver",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cascade_bloom_filter"
version = "0.1.0"
dependencies = [
 "nserror",
 "nsstring",
 "rust_cascade",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "cc"
version = "1.0.89"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0ba8f7aaa012f30d5b2861462f6708eccd49c3c39863fe083a308035f63d723"
dependencies = [
 "jobserver",
 "libc",
]

[[package]]
name = "cert_storage"
version = "0.0.1"
dependencies = [
 "base64 0.21.3",
 "byteorder",
 "clubcard",
 "clubcard-crlite",
 "crossbeam-utils",
 "cstr",
 "firefox-on-glean",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "rkv",
 "rust_cascade",
 "sha2",
 "static_prefs",
 "storage_variant",
 "tempfile",
 "thin-vec",
 "time 0.1.45",
 "wr_malloc_size_of",
 "xpcom",
]

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.1.1"
dependencies = [
 "cfg_aliases 0.2.1",
]

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "cgl"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ced0551234e87afee12411d535648dd89d2e7f34c78b753395567aff3d447ff"
dependencies = [
 "libc",
]

[[package]]
name = "chardetng"
version = "0.1.9"
source = "git+https://github.com/hsivonen/chardetng?rev=3484d3e3ebdc8931493aa5df4d7ee9360a90e76b#3484d3e3ebdc8931493aa5df4d7ee9360a90e76b"
dependencies = [
 "encoding_rs",
 "memchr",
]

[[package]]
name = "chardetng_c"
version = "0.1.2"
source = "git+https://github.com/hsivonen/chardetng_c?rev=ed8a4c6f900a90d4dbc1d64b856e61490a1c3570#ed8a4c6f900a90d4dbc1d64b856e61490a1c3570"
dependencies = [
 "chardetng",
 "encoding_rs",
]

[[package]]
name = "chrono"
version = "0.4.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "670ad68c9088c2a963aaa298cb369688cf3f9465ce5e2d4ca10e6e0098a1ce73"
dependencies = [
 "libc",
 "num-integer",
 "num-traits",
 "serde",
 "time 0.1.45",
 "winapi",
]

[[package]]
name = "chunky-vec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb7bdea464ae038f09197b82430b921c53619fc8d2bcaf7b151013b3ca008017"

[[package]]
name = "circular"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fc239e0f6cb375d2402d48afb92f76f5404fd1df208a41930ec81eda078bea"

[[package]]
name = "clang-sys"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67523a3b4be3ce1989d607a828d036249522dd9c1c8de7f4dd2dae43a37369d1"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.5.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed6719fffa43d0d87e5fd8caeab59be1554fb028cd30edc88fc4369b17971019"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap-verbosity-flag"
version = "3.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54381ae56ad222eea3f529c692879e9c65e07945ae48d3dc4d1cb18dbec8cf44"
dependencies = [
 "clap",
 "log",
]

[[package]]
name = "clap_builder"
version = "4.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "216aec2b177652e3846684cbfe25c9964d18ec45234f0f5da5157b207ed1aab6"
dependencies = [
 "anstyle",
 "clap_lex",
 "strsim",
 "terminal_size",
]

[[package]]
name = "clap_derive"
version = "4.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501d359d5f3dcaf6ecdeee48833ae73ec6e42723a1e52419c79abf9507eec0a0"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "clap_lex"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1462739cb27611015575c0c11df5df7601141071f07518d56fcc1be504cbec97"

[[package]]
name = "clubcard"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ec3fe691cfeac642b45d2acee55f4c745fe9eed548380fd41d1fb7daf54297c"
dependencies = [
 "serde",
]

[[package]]
name = "clubcard-crlite"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd2c6f668aeaab6cf4c9637400ce5bc1f7ab1cf9b424dd0125273f16bd26ca25"
dependencies = [
 "base64 0.21.3",
 "bincode",
 "clubcard",
 "serde",
 "sha2",
]

[[package]]
name = "cmake"
version = "0.1.999"

[[package]]
name = "cocoabind"
version = "0.1.0"
dependencies = [
 "bindgen 0.69.4",
 "block",
 "mozbuild",
 "objc",
]

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width",
]

[[package]]
name = "comedy"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74428ae4f7f05f32f4448e9f42d371538196919c4834979f4f96d1fdebffcb47"
dependencies = [
 "winapi",
]

[[package]]
name = "cookie"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e859cd57d0710d9e06c381b550c06e76992472a8c6d527aecd2fc673dcc231fb"
dependencies = [
 "time 0.3.36",
 "version_check",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06ea2b9bc92be3c2baa9334a323ebca2d6f074ff852cd1d7b11064035cd3868f"

[[package]]
name = "core-graphics"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "970a29baf4110c26fedbc7f82107d42c23f7e88e404c4577ed73fe99ff85a212"
dependencies = [
 "bitflags 1.999.999",
 "core-foundation",
 "core-graphics-types",
 "foreign-types",
 "libc",
]

[[package]]
name = "core-graphics-types"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45390e6114f68f718cc7a830514a96f903cccd70d02a8f6d9f643ac4ba45afaf"
dependencies = [
 "bitflags 1.999.999",
 "core-foundation",
 "libc",
]

[[package]]
name = "core-text"
version = "20.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9d2790b5c08465d49f8dc05c8bcae9fea467855947db39b0f8145c091aaced5"
dependencies = [
 "core-foundation",
 "core-graphics",
 "foreign-types",
 "libc",
]

[[package]]
name = "core_maths"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3b02505ccb8c50b0aa21ace0fc08c3e53adebd4e58caa18a36152803c7709a3"
dependencies = [
 "libm",
]

[[package]]
name = "coreaudio-sys"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3120ebb80a9de008e638ad833d4127d50ea3d3a960ea23ea69bc66d9358a028"
dependencies = [
 "bindgen 0.69.4",
]

[[package]]
name = "coreaudio-sys-utils"
version = "0.1.0"
source = "git+https://github.com/mozilla/cubeb-coreaudio-rs?rev=2407441a2f67341a0e13b4ba6547555e387c671c#2407441a2f67341a0e13b4ba6547555e387c671c"
dependencies = [
 "core-foundation-sys",
 "coreaudio-sys",
]

[[package]]
name = "coremidi"
version = "0.6.0"
source = "git+https://github.com/chris-zen/coremidi.git?rev=fc68464b5445caf111e41f643a2e69ccce0b4f83#fc68464b5445caf111e41f643a2e69ccce0b4f83"
dependencies = [
 "core-foundation",
 "core-foundation-sys",
 "coremidi-sys",
]

[[package]]
name = "coremidi-sys"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79a6deed0c97b2d40abbab77e4c97f81d71e162600423382c277dd640019116c"
dependencies = [
 "core-foundation-sys",
]

[[package]]
name = "cose"
version = "0.1.4"
source = "git+https://github.com/franziskuskiefer/cose-rust?rev=43c22248d136c8b38fe42ea709d08da6355cf04b#43c22248d136c8b38fe42ea709d08da6355cf04b"
dependencies = [
 "moz_cbor",
]

[[package]]
name = "cose-c"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49726015ab0ca765144fcca61e4a7a543a16b795a777fa53f554da2fffff9a94"
dependencies = [
 "cose",
]

[[package]]
name = "cpufeatures"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03e69e28e9f7f77debdedbaafa2866e1de9ba56df55a8bd7cfc724c25a09987c"
dependencies = [
 "libc",
]

[[package]]
name = "crash-context"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b85cef661eeca0c6675116310936972c520ebb0a33ddef16fd7efc957f4c1288"
dependencies = [
 "cfg-if",
 "libc",
 "mach2",
]

[[package]]
name = "crashreporter"
version = "1.0.0"
dependencies = [
 "anyhow",
 "block",
 "bytes",
 "cfg-if",
 "cocoabind",
 "embed-manifest",
 "env_logger",
 "flate2",
 "fluent",
 "glean",
 "gtkbind",
 "intl-memoizer",
 "libloading",
 "lmdb-rkv-sys",
 "log",
 "minidump-analyzer",
 "mozbuild",
 "mozilla-central-workspace-hack",
 "objc",
 "once_cell",
 "phf",
 "phf_codegen",
 "serde",
 "serde_json",
 "sha2",
 "sys-locale",
 "time 0.3.36",
 "tokio",
 "unic-langid",
 "uuid",
 "warp",
 "windows-sys",
 "yaml-rust",
 "zip",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33480d6946193aa8033910124896ca395333cae7e2d1113d1fef6c3272217df2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "715e8152b692bba2d374b53d4875445368fdf21a94751410af607a5ac677d1fc"
dependencies = [
 "cfg-if",
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46bd5f3f85273295a9d14aedfb86f6aadbff6d8f5295c4a9edb08e819dcf5695"
dependencies = [
 "autocfg",
 "cfg-if",
 "crossbeam-utils",
 "memoffset 0.8.999",
 "scopeguard",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1cfb3ea8a53f37c40dea2c7bedcbd88bdfae54f5e2175d6ecaff1c988353add"
dependencies = [
 "cfg-if",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22ec99545bb0ed0ea7bb9b8e1e9122ea386ff8a48c0922e43f36d45ab09e0e80"

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "typenum",
]

[[package]]
name = "crypto_hash"
version = "0.1.0"
dependencies = [
 "base64 0.21.3",
 "digest",
 "libc",
 "md-5",
 "nserror",
 "nsstring",
 "sha1",
 "sha2",
 "xpcom",
]

[[package]]
name = "cssparser"
version = "0.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7c66d1cd8ed61bf80b38432613a7a2f09401ab8d0501110655f8b341484a3e3"
dependencies = [
 "cssparser-macros",
 "dtoa-short",
 "itoa",
 "phf",
 "smallvec",
]

[[package]]
name = "cssparser-macros"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13b588ba4ac1a99f7f2964d24b3d896ddc6bf847ee3855dbd4366f058cfcd331"
dependencies = [
 "quote",
 "syn",
]

[[package]]
name = "cstr"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aa998c33a6d3271e3678950a22134cd7dd27cef86dee1b611b5b14207d1d90b"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "cubeb"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d105547cf8036cdb30e796ce0d06832af4766106a44574402fa2fd3c861a042"
dependencies = [
 "cubeb-core",
]

[[package]]
name = "cubeb-backend"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67361fe9b49b4599e2a230ce322529b6ddd91df14897c872dcede716f8fbca81"
dependencies = [
 "cubeb-core",
]

[[package]]
name = "cubeb-core"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac08d314dd1ec6d41d9ccdeec70899c98ed3b89845367000dd6096099481bc73"
dependencies = [
 "bitflags 1.999.999",
 "cubeb-sys",
]

[[package]]
name = "cubeb-coreaudio"
version = "0.1.0"
source = "git+https://github.com/mozilla/cubeb-coreaudio-rs?rev=2407441a2f67341a0e13b4ba6547555e387c671c#2407441a2f67341a0e13b4ba6547555e387c671c"
dependencies = [
 "atomic",
 "audio-mixer",
 "bitflags 2.6.0",
 "coreaudio-sys-utils",
 "cubeb-backend",
 "float-cmp",
 "libc",
 "mach",
 "ringbuf",
 "triple_buffer",
 "whatsys",
]

[[package]]
name = "cubeb-pulse"
version = "0.5.0"
source = "git+https://github.com/mozilla/cubeb-pulse-rs?rev=8678dcab1c287de79c4c184ccc2e065bc62b70e2#8678dcab1c287de79c4c184ccc2e065bc62b70e2"
dependencies = [
 "cubeb-backend",
 "pulse",
 "pulse-ffi",
 "ringbuf",
 "semver",
]

[[package]]
name = "cubeb-sys"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26073cd50c7b6ba4272204839f56921557609a0d67e092882cbb903df94cab39"
dependencies = [
 "cmake",
 "pkg-config",
]

[[package]]
name = "dap_ffi"
version = "0.1.0"
dependencies = [
 "log",
 "prio",
 "rand",
 "thin-vec",
]

[[package]]
name = "dap_ffi-gtest"
version = "0.1.0"
dependencies = [
 "dap_ffi",
 "hex",
 "prio",
 "serde",
 "serde_json",
 "thin-vec",
]

[[package]]
name = "darling"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f63b86c8a8826a49b8c21f08a2d07338eec8d900540f8630dc76284be802989"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95133861a8032aaea082871032f5815eb9e98cef03fa916ab4500513994df9e5"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim",
 "syn",
]

[[package]]
name = "darling_macro"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d336a2a514f6ccccaa3e09b02d41d35330c07ddf03a62165fcec10bb561c7806"
dependencies = [
 "darling_core",
 "quote",
 "syn",
]

[[package]]
name = "data-encoding"
version = "2.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23d8666cb01533c39dde32bcbab8e227b4ed6679b2c925eba05feabea39508fb"

[[package]]
name = "data-encoding-ffi"
version = "0.1.0"
dependencies = [
 "data-encoding",
 "nsstring",
]

[[package]]
name = "data_storage"
version = "0.0.1"
dependencies = [
 "byteorder",
 "cstr",
 "firefox-on-glean",
 "log",
 "malloc_size_of_derive",
 "moz_task",
 "nserror",
 "nsstring",
 "thin-vec",
 "wr_malloc_size_of",
 "xpcom",
]

[[package]]
name = "dbus"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48b5f0f36f1eebe901b0e6bee369a77ed3396334bf3f09abd46454a576f71819"
dependencies = [
 "libc",
 "libdbus-sys",
]

[[package]]
name = "debug_tree"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d1ec383f2d844902d3c34e4253ba11ae48513cdaddc565cf1a6518db09a8e57"
dependencies = [
 "once_cell",
]

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "uuid",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derive_arbitrary"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67e77553c4162a157adbf834ebae5b415acbecbeafc7a74b0e886657506a7611"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "derive_more"
version = "0.99.999"
dependencies = [
 "derive_more 1.0.0-beta.2",
]

[[package]]
name = "derive_more"
version = "1.0.0-beta.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d79dfbcc1f34f3b3a0ce7574276f6f198acb811d70dd19d9dcbfe6263a83d983"
dependencies = [
 "derive_more-impl",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0-beta.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395aee42a456ecfd4c7034be5011e1a98edcbab2611867c8988a0f40d0bb242a"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "detect_win32k_conflicts"
version = "0.1.0"
dependencies = [
 "log",
 "thiserror",
 "winapi",
]

[[package]]
name = "devd-rs"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9313f104b590510b46fc01c0a324fc76505c13871454d3c48490468d04c8d395"
dependencies = [
 "libc",
 "nom",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer",
 "crypto-common",
]

[[package]]
name = "diplomat"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3137c640d2bac491dbfca7f9945c948f888dd8c95bdf7ee6b164fbdfa5d3efc2"
dependencies = [
 "diplomat_core",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "diplomat-runtime"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5124bed7f5a5bc993adc24b2ef2df45514f005c31c198e62fceb1b2fe8cdf9a9"

[[package]]
name = "diplomat_core"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d86a70d2e21480f7ecd11e69b517de5c04fa6708510d6e26f75ad35d6d0eb14"
dependencies = [
 "lazy_static",
 "proc-macro2",
 "quote",
 "serde",
 "smallvec",
 "strck_ident",
 "syn",
]

[[package]]
name = "dirs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "displaydoc"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "487585f4d0c6655fe74905e2504d8ad6908e4db67f744eb140876906c2f3175d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "dist-bin"
version = "0.1.0"
dependencies = [
 "mozbuild",
]

[[package]]
name = "dns-parser"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4d33be9473d06f75f58220f71f7a9317aca647dc061dbd3c361b0bef505fbea"
dependencies = [
 "byteorder",
 "quick-error",
]

[[package]]
name = "document-features"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb6969eaabd2421f8a2775cfd2471a2b634372b4a25d41e3bd647b79912850a0"
dependencies = [
 "litrs",
]

[[package]]
name = "dogear"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f430ca247b6a905681a3cce3eb4f1a72062f3e8dc178e7660c1acd06c64ecce"
dependencies = [
 "log",
 "smallbitvec",
]

[[package]]
name = "dom"
version = "0.1.0"
dependencies = [
 "bitflags 2.6.0",
 "malloc_size_of",
]

[[package]]
name = "dom_fragmentdirectives"
version = "0.1.0"
dependencies = [
 "nsstring",
 "percent-encoding",
 "thin-vec",
]

[[package]]
name = "dtoa"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56899898ce76aaf4a0f24d914c97ea6ed976d42fec6ad33fcbb0a1103e07b2b0"

[[package]]
name = "dtoa-short"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bde03329ae10e79ede66c9ce4dc930aa8599043b0743008548680f25b91502d6"
dependencies = [
 "dtoa",
]

[[package]]
name = "dwrote"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439a1c2ba5611ad3ed731280541d36d2e9c4ac5e7fb818a27b604bdc5a6aa65b"
dependencies = [
 "lazy_static",
 "libc",
 "serde",
 "serde_derive",
 "winapi",
 "wio",
]

[[package]]
name = "either"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcaabb2fef8c910e7f4c7ce9f67a1283a1715879a7c230ca9d6d1ae31f16d91"

[[package]]
name = "embed-manifest"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41cd446c890d6bed1d8b53acef5f240069ebef91d6fae7c5f52efe61fe8b5eae"

[[package]]
name = "encoding_c"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9af727805f3b0d79956bde5b35732669fb5c5d45a94893798e7b7e70cfbf9cc1"
dependencies = [
 "encoding_rs",
]

[[package]]
name = "encoding_c_mem"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a80a16821fe8c7cab96e0c67b57cd7090e021e9615e6ce6ab0cf866c44ed1f0"
dependencies = [
 "encoding_rs",
]

[[package]]
name = "encoding_glue"
version = "0.1.0"
dependencies = [
 "encoding_rs",
 "nserror",
 "nsstring",
 "xmldecl",
]

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "any_all_workaround",
 "cfg-if",
]

[[package]]
name = "enum-map"
version = "2.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6866f3bfdf8207509a033af1a75a7b08abda06bbaaeae6669323fd5a097df2e9"
dependencies = [
 "enum-map-derive",
]

[[package]]
name = "enum-map-derive"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f282cfdfe92516eb26c2af8589c274c7c17681f5ecc03c18255fe741c6aa64eb"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "enumset"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e875f1719c16de097dee81ed675e2d9bb63096823ed3f0ca827b7dea3028bbbb"
dependencies = [
 "enumset_derive",
]

[[package]]
name = "enumset_derive"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08b6c6ab82d70f08844964ba10c7babb716de2ecaeab9be5717918a5177d3af"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "env_logger"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85cdab6a89accf66733ad5a1693a4dcced6aeff64602b634530dd73c1f3ee9f0"
dependencies = [
 "log",
 "termcolor",
]

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "errno"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a258e46cdc063eb8519c00b9fc845fc47bcfca4130e2f08e88665ceda8474245"
dependencies = [
 "libc",
 "windows-sys",
]

[[package]]
name = "error-chain"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d2f06b9cac1506ece98fe3231e3cc9c4410ec3d5b1f24ae1c8946f0742cdefc"
dependencies = [
 "version_check",
]

[[package]]
name = "error-support"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "error-support-macros",
 "lazy_static",
 "log",
 "parking_lot",
 "uniffi",
]

[[package]]
name = "error-support-macros"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "etagere"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e2f1e3be19fb10f549be8c1bf013e8675b4066c445e36eb76d2ebb2f54ee495"
dependencies = [
 "euclid",
 "serde",
 "svg_fmt",
]

[[package]]
name = "euclid"
version = "0.22.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0f0eb73b934648cd7a4a61f1b15391cd95dab0b4da6e2e66c2a072c144b4a20"
dependencies = [
 "num-traits",
 "serde",
]

[[package]]
name = "extend"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "311a6d2f1f9d60bff73d2c78a0af97ed27f79672f15c238192a5bbb64db56d00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fallible-streaming-iterator"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7360491ce676a36bf9bb3c56c1aa791658183a54d2744120f27285738d90465a"

[[package]]
name = "fallible_collections"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a88c69768c0a15262df21899142bc6df9b9b823546d4b4b9a7bc2d6c448ec6fd"
dependencies = [
 "hashbrown 0.13.999",
]

[[package]]
name = "fastrand"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fc0510504f03c51ada170672ac806f1f105a88aa97a5281117e1ddc3368e51a"

[[package]]
name = "ffi-support"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27838c6815cfe9de2d3aeb145ffd19e565f577414b33f3bdbf42fe040e9e0ff6"
dependencies = [
 "lazy_static",
 "log",
]

[[package]]
name = "filetime_win"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b12c2c8d7d9f04d7952cc33bac89b7425fb3cf4c44773b06ea49ac3df259ac57"
dependencies = [
 "comedy",
 "winapi",
]

[[package]]
name = "firefox-on-glean"
version = "0.1.0"
dependencies = [
 "bincode",
 "chrono",
 "gecko-profiler",
 "glean",
 "inherent",
 "log",
 "mozbuild",
 "nsstring",
 "once_cell",
 "serde",
 "serde_json",
 "tempfile",
 "thin-vec",
 "time 0.1.45",
 "uuid",
 "xpcom",
]

[[package]]
name = "firefox-versioning"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "serde_json",
 "thiserror",
]

[[package]]
name = "flagset"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cda653ca797810c02f7ca4b804b40b8b95ae046eb989d356bce17919a8c25499"

[[package]]
name = "flate2"
version = "1.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f54427cfd1c7829e2a139fcefea601bf088ebca651d2bf53ebc600eac295dae"
dependencies = [
 "crc32fast",
 "miniz_oxide",
]

[[package]]
name = "float-cmp"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da62c4f1b81918835a8c6a484a397775fff5953fe83529afd51b05f5c6a6617d"
dependencies = [
 "num-traits",
]

[[package]]
name = "fluent"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61f69378194459db76abd2ce3952b790db103ceb003008d3d50d97c41ff847a7"
dependencies = [
 "fluent-bundle",
 "fluent-pseudo",
 "unic-langid",
]

[[package]]
name = "fluent-bundle"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e242c601dec9711505f6d5bbff5bedd4b61b2469f2e8bb8e57ee7c9747a87ffd"
dependencies = [
 "fluent-langneg",
 "fluent-syntax",
 "intl-memoizer",
 "intl_pluralrules",
 "rustc-hash",
 "self_cell",
 "smallvec",
 "unic-langid",
]

[[package]]
name = "fluent-fallback"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08fdcccdeb6c01cb085f2bb3420506e6c67f025cee5db047529838c673a7d82b"
dependencies = [
 "async-trait",
 "chunky-vec",
 "fluent-bundle",
 "futures",
 "once_cell",
 "rustc-hash",
 "unic-langid",
]

[[package]]
name = "fluent-ffi"
version = "0.1.0"
dependencies = [
 "cstr",
 "fluent",
 "fluent-pseudo",
 "fluent-syntax",
 "intl-memoizer",
 "nsstring",
 "thin-vec",
 "unic-langid",
 "xpcom",
]

[[package]]
name = "fluent-langneg"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c4ad0989667548f06ccd0e306ed56b61bd4d35458d54df5ec7587c0e8ed5e94"
dependencies = [
 "unic-langid",
]

[[package]]
name = "fluent-langneg-ffi"
version = "0.1.0"
dependencies = [
 "fluent-langneg",
 "nsstring",
 "thin-vec",
 "unic-langid",
 "unic-langid-ffi",
]

[[package]]
name = "fluent-pseudo"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fded62f95114baa010b4fcd54aedc7b0762059cf6dfb0097f5b5c95fb56ed42f"
dependencies = [
 "regex",
]

[[package]]
name = "fluent-syntax"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0abed97648395c902868fee9026de96483933faa54ea3b40d652f7dfe61ca78"
dependencies = [
 "thiserror",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "fog-gtest"
version = "0.1.0"
dependencies = [
 "firefox-on-glean",
 "jog",
 "nsstring",
]

[[package]]
name = "fog_control"
version = "0.1.0"
dependencies = [
 "bhttp",
 "cstr",
 "firefox-on-glean",
 "glean",
 "log",
 "mozbuild",
 "nserror",
 "nsstring",
 "ohttp",
 "once_cell",
 "static_prefs",
 "thin-vec",
 "thiserror",
 "url",
 "viaduct",
 "xpcom",
]

[[package]]
name = "foreign-types"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d737d9aa519fb7b749cbc3b962edcf310a8dd1f4b67c91c4f83975dbdd17d965"
dependencies = [
 "foreign-types-macros",
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-macros"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a5c6c585bc94aaf2c7b51dd4c2ba22680844aba4c687be581871a6f518c5742"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "foreign-types-shared"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa9a19cbb55df58761df49b23516a86d432839add4af60fc256da840f66ed35b"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "framehop"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fd28d2036d4fd99e3629487baca659e5af1c5d554e320168613be79028610fc"
dependencies = [
 "arrayvec",
 "cfg-if",
 "fallible-iterator",
 "gimli",
 "macho-unwind-info",
 "pe-unwind-info",
]

[[package]]
name = "freetype"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bee38378a9e3db1cc693b4f88d166ae375338a0ff75cb8263e1c601d51f35dc6"
dependencies = [
 "libc",
]

[[package]]
name = "fs-err"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0845fa252299212f0389d64ba26f34fa32cfe41588355f21ed507c59a0f64541"

[[package]]
name = "futures"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23342abe12aba583913b2e62f22225ff9c950774065e4bfb61a19cd9770fec40"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "955518d47e09b25bbebc7a18df10b81f0c766eaf4c4f1cccef2fca5f2a4fb5f2"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bca583b7e26f571124fe5b7561d49cb2868d79116cfa0eefce955557c6fee8c"

[[package]]
name = "futures-executor"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccecee823288125bd88b4d7f565c9e58e41858e47ab72e8ea2d64e93624386e0"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fff74096e71ed47f8e023204cfd0aa1289cd54ae5430a9523be060cdb849964"

[[package]]
name = "futures-macro"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89ca545a94061b6365f2c7355b4b32bd20df3ff95f02da9329b34ccc3bd6ee72"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "futures-sink"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f43be4fe21a13b9781a69afa4985b0f6ee0e1afab2c6f454a8cf30e2b2237b6e"

[[package]]
name = "futures-task"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76d3d132be6c0e6aa1534069c705a74a5997a356c0dc2f86a47765e5617c5b65"

[[package]]
name = "futures-util"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b01e40b772d54cf6c6d721c1d1abd0647a0106a12ecaa1c186273392a69533"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "gecko-fuzz-targets"
version = "0.1.0"
dependencies = [
 "lazy_static",
 "libc",
 "lmdb-rkv",
 "rkv",
 "tempfile",
]

[[package]]
name = "gecko-profiler"
version = "0.1.0"
dependencies = [
 "bincode",
 "bindgen 0.69.4",
 "lazy_static",
 "mozbuild",
 "profiler-macros",
 "serde",
]

[[package]]
name = "gecko_logger"
version = "0.1.0"
dependencies = [
 "app_services_logger",
 "env_logger",
 "lazy_static",
 "log",
]

[[package]]
name = "geckodriver"
version = "0.35.0"
dependencies = [
 "anyhow",
 "base64 0.21.3",
 "chrono",
 "clap",
 "flate2",
 "hyper",
 "icu_segmenter",
 "lazy_static",
 "log",
 "marionette",
 "mozdevice",
 "mozilla-central-workspace-hack",
 "mozprofile",
 "mozrunner",
 "mozversion",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_yaml",
 "tempfile",
 "thiserror",
 "url",
 "uuid",
 "webdriver",
 "zip",
]

[[package]]
name = "geckoservo"
version = "0.0.1"
dependencies = [
 "atomic_refcell",
 "cssparser",
 "cstr",
 "dom",
 "gecko-profiler",
 "ipdl_utils",
 "lazy_static",
 "libc",
 "log",
 "malloc_size_of",
 "nsstring",
 "num-traits",
 "selectors",
 "servo_arc",
 "smallvec",
 "style",
 "style_traits",
 "thin-vec",
 "to_shmem",
]

[[package]]
name = "generic-array"
version = "0.14.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bff49e947297f3312447abdca79f45f4738097cc82b06e72054d2223f601f1b9"
dependencies = [
 "typenum",
 "version_check",
]

[[package]]
name = "getrandom"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94b22e06ecb0110981051723910cbf0b5f5e09a2062dd7663334ee79a9d1286c"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "gimli"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2e1d97fbe9722ba9bbd0c97051c2956e726562b61f86a25a4360398a40edfc9"
dependencies = [
 "fallible-iterator",
 "stable_deref_trait",
]

[[package]]
name = "gkrust"
version = "0.1.0"
dependencies = [
 "gkrust-shared",
 "lmdb-rkv-sys",
 "mozglue-static",
 "mozilla-central-workspace-hack",
 "stylo_tests",
 "swgl",
]

[[package]]
name = "gkrust-gtest"
version = "0.1.0"
dependencies = [
 "bench-collections-gtest",
 "dap_ffi-gtest",
 "fog-gtest",
 "gecko-fuzz-targets",
 "gkrust-shared",
 "kvstore-gtest",
 "l10nregistry-ffi-gtest",
 "lmdb-rkv-sys",
 "moz_task-gtest",
 "mozglue-static",
 "mozilla-central-workspace-hack",
 "mp4parse-gtest",
 "nsstring-gtest",
 "swgl",
 "xpcom-gtest",
]

[[package]]
name = "gkrust-shared"
version = "0.1.0"
dependencies = [
 "aa-stroke",
 "app_services_logger",
 "audio_thread_priority",
 "audioipc2-client",
 "audioipc2-server",
 "authrs_bridge",
 "binary_http",
 "bitsdownload",
 "bookmark_sync",
 "buildid_reader",
 "cascade_bloom_filter",
 "cert_storage",
 "chardetng_c",
 "cose-c",
 "crypto_hash",
 "cubeb-coreaudio",
 "cubeb-pulse",
 "cubeb-sys",
 "dap_ffi",
 "data-encoding-ffi",
 "data_storage",
 "detect_win32k_conflicts",
 "dom",
 "dom_fragmentdirectives",
 "encoding_glue",
 "etagere",
 "fallible_collections",
 "fluent",
 "fluent-fallback",
 "fluent-ffi",
 "fluent-langneg",
 "fluent-langneg-ffi",
 "fog_control",
 "gecko-profiler",
 "gecko_logger",
 "geckoservo",
 "gkrust-uniffi-components",
 "gkrust-uniffi-fixtures",
 "gkrust_utils",
 "http_sfv",
 "idna_glue",
 "ipdl_utils",
 "jog",
 "jsrust_shared",
 "kvstore",
 "l10nregistry",
 "l10nregistry-ffi",
 "libz-rs-sys",
 "lmdb-rkv-sys",
 "localization-ffi",
 "log",
 "mapped_hyph",
 "mdns_service",
 "midir_impl",
 "mime-guess-ffi",
 "mls_gk",
 "moz_asserts",
 "mozannotation_client",
 "mozannotation_server",
 "mozglue-static",
 "mozurl",
 "mp4parse_capi",
 "neqo_glue",
 "netwerk_helper",
 "nserror",
 "nsstring",
 "oblivious_http",
 "origin-trials-ffi",
 "oxilangtag",
 "oxilangtag-ffi",
 "prefs_parser",
 "processtools",
 "profiler_helper",
 "qcms",
 "rsdparsa_capi",
 "rure",
 "rusqlite",
 "rust_minidump_writer_linux",
 "signature_cache",
 "static_prefs",
 "storage",
 "unic-langid",
 "unic-langid-ffi",
 "unicode-bidi",
 "unicode-bidi-ffi",
 "url",
 "viaduct",
 "webext-storage",
 "webrender_bindings",
 "wgpu_bindings",
 "wpf-gpu-raster",
 "xpcom",
]

[[package]]
name = "gkrust-uniffi-components"
version = "0.1.0"
dependencies = [
 "relevancy",
 "suggest",
 "tabs",
 "uniffi",
 "webext-storage",
]

[[package]]
name = "gkrust-uniffi-fixtures"
version = "0.1.0"
dependencies = [
 "uniffi",
 "uniffi-example-arithmetic",
 "uniffi-example-custom-types",
 "uniffi-example-geometry",
 "uniffi-example-rondpoint",
 "uniffi-example-sprites",
 "uniffi-example-todolist",
 "uniffi-fixture-callbacks",
 "uniffi-fixture-external-types",
 "uniffi-fixture-futures",
 "uniffi-fixture-refcounts",
 "uniffi-trait-interfaces",
]

[[package]]
name = "gkrust_utils"
version = "0.1.0"
dependencies = [
 "nsstring",
 "semver",
]

[[package]]
name = "gl_generator"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a95dfc23a2b4a9a2f5ab41d194f8bfda3cabec42af4e39f08c339eb2a0c124d"
dependencies = [
 "khronos_api",
 "log",
 "xml-rs",
]

[[package]]
name = "gleam"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0173481f2bb6e809bf4985de2e86c83876d84d2805830e3301cd37355e897f0f"
dependencies = [
 "gl_generator",
]

[[package]]
name = "glean"
version = "63.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6edc2134eac59587dc100b301eeba2dd473d74d6ecd51d635419a1865cc92496"
dependencies = [
 "crossbeam-channel",
 "glean-core",
 "inherent",
 "log",
 "once_cell",
 "whatsys",
]

[[package]]
name = "glean-core"
version = "63.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b840b5177e3e102332cfa09bbbe57ae69f939a180d73cb02351b2ddf4cb1fbee"
dependencies = [
 "android_logger",
 "bincode",
 "chrono",
 "crossbeam-channel",
 "flate2",
 "log",
 "once_cell",
 "oslog",
 "rkv",
 "serde",
 "serde_json",
 "thiserror",
 "time 0.1.45",
 "uniffi",
 "uuid",
 "zeitstempel",
]

[[package]]
name = "glob"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2fabcfbdc87f4758337ca535fb41a6d701b65693ce38287d856d1674551ec9b"

[[package]]
name = "glsl"
version = "6.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65c80dbf169ac31dbe6e0a69a7cef0b09ec9805f955da206ff1ee2e47895f836"
dependencies = [
 "nom",
]

[[package]]
name = "glsl-to-cxx"
version = "0.1.0"
dependencies = [
 "glsl",
]

[[package]]
name = "glslopt"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "913662ae8335df058d56e00f11340b20fa82e03e0276587797ef325ab01e50d4"
dependencies = [
 "cc",
]

[[package]]
name = "gluesmith"
version = "0.1.0"
dependencies = [
 "arbitrary",
 "libc",
 "wasm-smith",
]

[[package]]
name = "goblin"
version = "0.7.999"
dependencies = [
 "goblin 0.8.2",
]

[[package]]
name = "goblin"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b363a30c165f666402fe6a3024d3bec7ebc898f96a4a23bd1c99f8dbf3f4f47"
dependencies = [
 "log",
 "plain",
 "scroll",
]

[[package]]
name = "golden_gate"
version = "0.1.0"
dependencies = [
 "anyhow",
 "atomic_refcell",
 "cstr",
 "interrupt-support",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "serde_json",
 "storage_variant",
 "sync15",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "gpu-alloc"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbcd2dba93594b227a1f57ee09b8b9da8892c34d55aa332e034a228d0fe6a171"
dependencies = [
 "bitflags 2.6.0",
 "gpu-alloc-types",
]

[[package]]
name = "gpu-alloc-types"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98ff03b468aa837d70984d55f5d3f846f6ec31fe34bbb97c4f85219caeee1ca4"
dependencies = [
 "bitflags 2.6.0",
]

[[package]]
name = "gpu-allocator"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c151a2a5ef800297b4e79efa4f4bec035c5f51d5ae587287c9b952bdf734cacd"
dependencies = [
 "log",
 "presser",
 "thiserror",
 "windows",
]

[[package]]
name = "gpu-descriptor"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c08c1f623a8d0b722b8b99f821eb0ba672a1618f0d3b16ddbee1cedd2dd8557"
dependencies = [
 "bitflags 2.6.0",
 "gpu-descriptor-types",
 "hashbrown 0.14.5",
]

[[package]]
name = "gpu-descriptor-types"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdf242682df893b86f33a73828fb09ca4b2d3bb6cc95249707fc684d27484b91"
dependencies = [
 "bitflags 2.6.0",
]

[[package]]
name = "gtkbind"
version = "0.1.0"
dependencies = [
 "bindgen 0.69.4",
 "mozbuild",
]

[[package]]
name = "guid_win"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d87f4be87a557b98b4e4316f2009834f4448652938a950c1e8b33ae25f6f183b"
dependencies = [
 "comedy",
 "winapi",
]

[[package]]
name = "h2"
version = "0.3.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d6250322ef6e60f93f9a2162799302cd6f68f79f6e5d85c8c16f14d1d958178"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap 2.2.6",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eabb4a44450da02c90444cf74558da904edde8fb4e9035a9a6a4e15445af0bd7"

[[package]]
name = "hashbrown"
version = "0.13.999"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash",
 "allocator-api2",
]

[[package]]
name = "hashlink"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ba4ff7128dee98c7dc9794b6a411377e1404dba1c97deb8d1a55297bd25d8af"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "headers"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06683b93020a07e3dbcf5f8c0f6d40080d725bea7936fc01ad345c01b97dc270"
dependencies = [
 "base64 0.21.3",
 "bytes",
 "headers-core",
 "http",
 "httpdate",
 "mime",
 "sha1",
]

[[package]]
name = "headers-core"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7f66481bfee273957b1f20485a4ff3362987f85b2c236580d81b4eb7a326429"
dependencies = [
 "http",
]

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.3.999"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"
dependencies = [
 "serde",
]

[[package]]
name = "hexf-parse"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfa686283ad6dd069f105e5ab091b04c62850d3e4cf5d67debad1933f55023df"

[[package]]
name = "http"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd6effc99afb63425aff9b05836f029929e345a6148a14b7ecd5ab67af944482"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5f38f16d184e36f2408a55281cd658ecbd3ca05cce6d6510a176eca393e26d1"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite",
]

[[package]]
name = "http3server"
version = "0.1.1"
dependencies = [
 "base64 0.21.3",
 "bindgen 0.69.4",
 "cfg-if",
 "http",
 "hyper",
 "log",
 "mozilla-central-workspace-hack",
 "neqo-bin",
 "neqo-common",
 "neqo-crypto",
 "neqo-http3",
 "neqo-qpack",
 "neqo-transport",
 "tokio",
]

[[package]]
name = "http_sfv"
version = "0.1.0"
dependencies = [
 "nserror",
 "nsstring",
 "sfv",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "httpdate"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4a1e36c821dbe04574f602848a19f742f4fb3c98d40449f11bcad18d6b17421"

[[package]]
name = "hyper"
version = "0.14.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e011372fa0b68db8350aa7a248930ecc7839bf46d8485577d69f117a75f164c"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2 0.4.999",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "icu_calendar"
version = "1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7265b2137f9a36f7634a308d91f984574bbdba8cfd95ceffe1c345552275a8ff"
dependencies = [
 "calendrical_calculations",
 "displaydoc",
 "icu_calendar_data",
 "icu_locid",
 "icu_locid_transform",
 "icu_provider",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_calendar_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e009b7f0151ee6fb28c40b1283594397e0b7183820793e9ace3dcd13db126d0"

[[package]]
name = "icu_capi"
version = "1.5.0"
dependencies = [
 "diplomat",
 "diplomat-runtime",
 "icu_calendar",
 "icu_locid",
 "icu_properties",
 "icu_provider",
 "icu_provider_adapters",
 "icu_segmenter",
 "tinystr",
 "writeable",
]

[[package]]
name = "icu_collections"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fa452206ebee18c4b5c2274dbf1de17008e874b4dc4f0aea9d01ca79e4526"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locid"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13acbb8371917fc971be86fc8057c41a64b521c184808a698c02acc242dbf637"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_locid_transform"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01d11ac35de8e40fdeda00d9e1e9d92525f3f9d887cdd7aa81d727596788b54e"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_locid_transform_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_locid_transform_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc8ff3388f852bede6b579ad4e978ab004f139284d7b28715f773507b946f6e"

[[package]]
name = "icu_normalizer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19ce3e0da2ec68599d193c93d088142efd7f9c5d6fc9b803774855747dc6a84f"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "utf16_iter",
 "utf8_iter",
 "write16",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8cafbf7aa791e9b22bec55a167906f9e1215fd475cd22adfcf660e03e989516"

[[package]]
name = "icu_properties"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f8ac670d7422d7f76b32e17a5db556510825b29ec9154f235977c9caba61036"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locid_transform",
 "icu_properties_data",
 "icu_provider",
 "tinystr",
 "unicode-bidi",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67a8effbc3dd3e4ba1afa8ad918d5684b8868b3b26500753effea8d2eed19569"

[[package]]
name = "icu_provider"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ed421c8a8ef78d3e2dbc98a973be2f3770cb42b606e3ab18d6237c4dfde68d9"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_provider_macros",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_provider_adapters"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6324dfd08348a8e0374a447ebd334044d766b1839bb8d5ccf2482a99a77c0bc"
dependencies = [
 "icu_locid",
 "icu_locid_transform",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_provider_macros"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ec89e9337638ecdc08744df490b221a7399bf8d164eb52a665454e60e075ad6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "icu_segmenter"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a717725612346ffc2d7b42c94b820db6908048f39434504cb130e8b46256b0de"
dependencies = [
 "core_maths",
 "displaydoc",
 "icu_collections",
 "icu_locid",
 "icu_provider",
 "icu_segmenter_data",
 "utf8_iter",
 "zerovec",
]

[[package]]
name = "icu_segmenter_data"
version = "1.5.0"

[[package]]
name = "id-arena"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25a2bc672d1148e28034f176e01fffebb08b35768468cc954630da77a1449005"

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daca1df1c957320b2cf139ac61e7bd64fed304c5040df000a745aa1de3b4ef71"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "idna_glue"
version = "0.1.0"
dependencies = [
 "arraystring",
 "idna",
 "nserror",
 "nsstring",
 "percent-encoding",
]

[[package]]
name = "indexmap"
version = "1.999.999"
dependencies = [
 "indexmap 2.2.6",
]

[[package]]
name = "indexmap"
version = "2.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "168fb715dda47215e360912c096649d23d58bf392ac62f73919e831745e40f26"
dependencies = [
 "equivalent",
 "hashbrown 0.14.5",
 "serde",
]

[[package]]
name = "inherent"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc83c51f04b1ad3b24cbac53d2ec1a138d699caabe05d315cb8538e8624d01"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "interrupt-support"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "lazy_static",
 "parking_lot",
 "rusqlite",
 "uniffi",
]

[[package]]
name = "intl-memoizer"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c310433e4a310918d6ed9243542a6b83ec1183df95dff8f23f87bb88a264a66f"
dependencies = [
 "type-map",
 "unic-langid",
]

[[package]]
name = "intl_pluralrules"
version = "7.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "078ea7b7c29a2b4df841a7f6ac8775ff6074020c6776d48491ce2268e068f972"
dependencies = [
 "unic-langid",
]

[[package]]
name = "io-surface"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "861c6093cbc05599e66436aedf380bb0a23cec2180738393d3a340b80dd135ef"
dependencies = [
 "cgl",
 "core-foundation",
 "leaky-cow",
 "libc",
]

[[package]]
name = "ioctl-sys"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f9d0b6b23885487578d10590edc36fd95426257c7017973b20633e34df23b08"

[[package]]
name = "iovec"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2b3ea6ff95e175473f8ffe6a7eb7c00d054240321b84c57051175fe3c1e075e"
dependencies = [
 "libc",
]

[[package]]
name = "ipcclientcerts-static"
version = "0.1.0"
dependencies = [
 "byteorder",
 "mozilla-central-workspace-hack",
 "pkcs11-bindings",
 "rsclientcerts",
 "sha2",
]

[[package]]
name = "ipdl_utils"
version = "0.1.0"
dependencies = [
 "bincode",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fad582f4b9e86b6caa621cabeb0963332d92eea04729ab12892c2533951e6440"

[[package]]
name = "jobserver"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "068b1ee6743e4d11fb9c6a1e6064b3693a1b600e7f5f5988047d98b3dc9fb90b"
dependencies = [
 "libc",
]

[[package]]
name = "jog"
version = "0.1.0"
dependencies = [
 "firefox-on-glean",
 "log",
 "mozbuild",
 "nsstring",
 "once_cell",
 "serde",
 "serde_json",
 "thin-vec",
]

[[package]]
name = "js-sys"
version = "0.3.100"

[[package]]
name = "jsparagus"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "jsparagus-ast",
 "jsparagus-emitter",
 "jsparagus-generated-parser",
 "jsparagus-json-log",
 "jsparagus-parser",
 "jsparagus-scope",
 "jsparagus-stencil",
]

[[package]]
name = "jsparagus-ast"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "bumpalo",
 "indexmap 2.2.6",
]

[[package]]
name = "jsparagus-emitter"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "bumpalo",
 "byteorder",
 "indexmap 2.2.6",
 "jsparagus-ast",
 "jsparagus-scope",
 "jsparagus-stencil",
]

[[package]]
name = "jsparagus-generated-parser"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "bumpalo",
 "jsparagus-ast",
 "static_assertions",
]

[[package]]
name = "jsparagus-json-log"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"

[[package]]
name = "jsparagus-parser"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "arrayvec",
 "bumpalo",
 "jsparagus-ast",
 "jsparagus-generated-parser",
 "jsparagus-json-log",
]

[[package]]
name = "jsparagus-scope"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "indexmap 2.2.6",
 "jsparagus-ast",
 "jsparagus-stencil",
]

[[package]]
name = "jsparagus-stencil"
version = "0.1.0"
source = "git+https://github.com/mozilla-spidermonkey/jsparagus?rev=61f399c53a641ebd3077c1f39f054f6d396a633c#61f399c53a641ebd3077c1f39f054f6d396a633c"
dependencies = [
 "jsparagus-ast",
]

[[package]]
name = "jsrust"
version = "0.1.0"
dependencies = [
 "jsrust_shared",
 "mozglue-static",
 "mozilla-central-workspace-hack",
 "wast",
]

[[package]]
name = "jsrust_shared"
version = "0.1.0"
dependencies = [
 "encoding_c",
 "encoding_c_mem",
 "gluesmith",
 "icu_capi",
 "mozglue-static",
 "smoosh",
 "unicode-bidi-ffi",
]

[[package]]
name = "keccak"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f6d5ed8676d904364de097082f4e7d240b571b67989ced0240f08b7f966f940"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "khronos-egl"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6aae1df220ece3c0ada96b8153459b67eebe9ae9212258bb0134ae60416fdf76"
dependencies = [
 "libc",
 "pkg-config",
]

[[package]]
name = "khronos_api"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2db585e1d738fc771bf08a151420d3ed193d9d895a36df7f6f8a9456b911ddc"

[[package]]
name = "kvstore"
version = "0.1.0"
dependencies = [
 "atomic_refcell",
 "chrono",
 "crossbeam-utils",
 "cstr",
 "futures",
 "hashbrown 0.13.999",
 "lazy_static",
 "libc",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "rkv",
 "rusqlite",
 "serde",
 "serde_json",
 "storage_variant",
 "tempfile",
 "thin-vec",
 "thiserror",
 "xpcom",
]

[[package]]
name = "kvstore-gtest"
version = "0.1.0"
dependencies = [
 "kvstore",
 "moz_task",
 "rusqlite",
 "tempfile",
]

[[package]]
name = "l10nregistry"
version = "0.3.0"
dependencies = [
 "async-trait",
 "fluent-bundle",
 "fluent-fallback",
 "futures",
 "pin-project-lite",
 "replace_with",
 "rustc-hash",
 "unic-langid",
]

[[package]]
name = "l10nregistry-ffi"
version = "0.1.0"
dependencies = [
 "async-trait",
 "cstr",
 "fluent",
 "fluent-fallback",
 "fluent-ffi",
 "futures",
 "futures-channel",
 "l10nregistry",
 "libc",
 "log",
 "moz_task",
 "nserror",
 "nsstring",
 "thin-vec",
 "unic-langid",
 "xpcom",
]

[[package]]
name = "l10nregistry-ffi-gtest"
version = "0.1.0"
dependencies = [
 "l10nregistry-ffi",
 "moz_task",
]

[[package]]
name = "lazy_static"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "leak"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd100e01f1154f2908dfa7d02219aeab25d0b9c7fa955164192e3245255a0c73"

[[package]]
name = "leaky-cow"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40a8225d44241fd324a8af2806ba635fc7c8a7e9a7de4d5cf3ef54e71f5926fc"
dependencies = [
 "leak",
]

[[package]]
name = "leb128"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "884e2677b40cc8c339eaefcb701c32ef1fd2493d71118dc0ca4b6a736c93bd67"

[[package]]
name = "libc"
version = "0.2.158"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8adc4bb1803a324070e64a98ae98f38934d91957a99cfb3a43dcbc01bc56439"

[[package]]
name = "libdbus-sys"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c185b5b7ad900923ef3a8ff594083d4d9b5aea80bb4f32b8342363138c0d456b"
dependencies = [
 "pkg-config",
]

[[package]]
name = "libloading"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2a198fb6b0eada2a8df47933734e6d35d350665a33a3593d7164fa52c75c19"
dependencies = [
 "cfg-if",
 "windows-targets",
]

[[package]]
name = "libm"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "348108ab3fba42ec82ff6e9564fc4ca0247bdccdc68dd8af9764bbc79c3c8ffb"

[[package]]
name = "libsqlite3-sys"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c10584274047cb335c23d3e61bcef8e323adae7c5c8c760540f73610177fc3f"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "libudev"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea626d3bdf40a1c5aee3bcd4f40826970cae8d80a8fec934c82a63840094dcfe"
dependencies = [
 "libc",
 "libudev-sys",
]

[[package]]
name = "libudev-sys"
version = "0.1.3"
dependencies = [
 "lazy_static",
 "libc",
]

[[package]]
name = "libz-rs-sys"
version = "0.2.1"
source = "git+https://github.com/memorysafety/zlib-rs?rev=4aa430ccb77537d0d60dab8db993ca51bb1194c5#4aa430ccb77537d0d60dab8db993ca51bb1194c5"
dependencies = [
 "zlib-rs",
]

[[package]]
name = "line-wrap"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f30344350a2a51da54c1d53be93fade8a237e545dbcc4bdbe635413f2117cab9"
dependencies = [
 "safemem",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linux-raw-sys"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4cd1a83af159aa67994778be9070f0ae1bd732942279cabb14f86f986a21456"

[[package]]
name = "litemap"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "643cb0b8d4fcc284004d5fd0d67ccf61dfffadb7f75e1e71bc420f4688a3a704"

[[package]]
name = "litrs"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ce301924b7887e9d637144fdade93f9dfff9b60981d4ac161db09720d39aa5"

[[package]]
name = "lmdb-rkv"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "447a296f7aca299cfbb50f4e4f3d49451549af655fb7215d7f8c0c3d64bad42b"
dependencies = [
 "bitflags 1.999.999",
 "byteorder",
 "libc",
 "lmdb-rkv-sys",
]

[[package]]
name = "lmdb-rkv-sys"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61b9ce6b3be08acefa3003c57b7565377432a89ec24476bbe72e11d101f852fe"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "localization-ffi"
version = "0.1.0"
dependencies = [
 "async-trait",
 "cstr",
 "fluent",
 "fluent-fallback",
 "fluent-ffi",
 "futures",
 "futures-channel",
 "l10nregistry",
 "l10nregistry-ffi",
 "moz_task",
 "nserror",
 "nsstring",
 "thin-vec",
 "unic-langid",
 "xpcom",
]

[[package]]
name = "lock_api"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "435011366fe56583b16cf956f9df0095b405b82d76425bc8981c0e22e60ec4df"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6163cb8c49088c2c36f57875e58ccd8c87c7427f7fbd50ea6710b2f3f2e8f"

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc",
]

[[package]]
name = "mach2"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d0d1830bcd151a6fc4aea1369af235b36c1528fe976b8ff678683c9995eade8"
dependencies = [
 "libc",
]

[[package]]
name = "macho-unwind-info"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b6086acc74bc23f56b60e88bb082d505e23849d68d6c0f12bb6a7ad5c60e03e"
dependencies = [
 "thiserror",
 "zerocopy",
 "zerocopy-derive",
]

[[package]]
name = "malloc_buf"
version = "0.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62bb907fe88d54d8d9ce32a3cceab4218ed2f6b7d35617cafe9adf84e43919cb"
dependencies = [
 "libc",
]

[[package]]
name = "malloc_size_of"
version = "0.0.1"
dependencies = [
 "app_units",
 "cssparser",
 "euclid",
 "selectors",
 "servo_arc",
 "smallbitvec",
 "smallvec",
 "thin-vec",
 "void",
]

[[package]]
name = "malloc_size_of_derive"
version = "0.1.3"
dependencies = [
 "proc-macro2",
 "syn",
 "synstructure",
]

[[package]]
name = "mapped_hyph"
version = "0.4.3"
source = "git+https://github.com/jfkthame/mapped_hyph.git?rev=eff105f6ad7ec9b79816cfc1985a28e5340ad14b#eff105f6ad7ec9b79816cfc1985a28e5340ad14b"
dependencies = [
 "arrayref",
 "log",
 "memmap2",
]

[[package]]
name = "marionette"
version = "0.6.0"
dependencies = [
 "serde",
 "serde_json",
 "serde_repr",
]

[[package]]
name = "matches"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2532096657941c2fea9c289d370a250971c689d4f143798ff67113ec042024a5"

[[package]]
name = "maybe-async"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cf92c10c7e361d6b99666ec1c6f9805b0bea2c3bd8c78dc6fe98ac5bd78db11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "md-5"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6365506850d44bff6e2fbcb5176cf63650e48bd45ef2fe2665ae1570e0f4b9ca"
dependencies = [
 "digest",
]

[[package]]
name = "mdns_service"
version = "0.1.1"
dependencies = [
 "byteorder",
 "dns-parser",
 "gecko-profiler",
 "log",
 "socket2 0.4.999",
 "uuid",
]

[[package]]
name = "memalloc"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df39d232f5c40b0891c10216992c2f250c054105cb1e56f0fc9032db6203ecc1"

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memmap2"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45fd3a57831bf88bc63f8cebc0cf956116276e97fef3966103e96416209f7c92"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.8.999"
dependencies = [
 "memoffset 0.9.0",
]

[[package]]
name = "memoffset"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a634b1c61a95585bd15607c6ab0c4e5b226e695ff2800ba0cdccddf208c406c"
dependencies = [
 "autocfg",
]

[[package]]
name = "metal"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c3572083504c43e14aec05447f8a3d57cce0f66d7a3c1b9058572eca4d70ab9"
dependencies = [
 "bitflags 2.6.0",
 "block",
 "core-graphics-types",
 "foreign-types",
 "log",
 "objc",
 "paste",
]

[[package]]
name = "midir"
version = "0.7.0"
source = "git+https://github.com/mozilla/midir.git?rev=85156e360a37d851734118104619f86bd18e94c6#85156e360a37d851734118104619f86bd18e94c6"
dependencies = [
 "alsa",
 "coremidi",
 "js-sys",
 "libc",
 "memalloc",
 "wasm-bindgen",
 "web-sys",
 "winapi",
]

[[package]]
name = "midir_impl"
version = "0.1.0"
dependencies = [
 "midir",
 "nsstring",
 "thin-vec",
 "uuid",
]

[[package]]
name = "mime"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a60c7ce501c71e03a9c9c0d35b861413ae925bd979cc7a4e30d060069aaac8d"

[[package]]
name = "mime-guess-ffi"
version = "0.1.0"
dependencies = [
 "mime_guess",
 "nserror",
 "nsstring",
]

[[package]]
name = "mime_guess"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4192263c238a5f0d0c6bfd21f336a313a4ce1c450542449ca191bb657b4642ef"
dependencies = [
 "mime",
 "unicase",
]

[[package]]
name = "minidump"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cee91aa51259518a08a12c18b5754e45135f89f1d9d7d6aae76ce93b92686698"
dependencies = [
 "debugid",
 "encoding_rs",
 "memmap2",
 "minidump-common",
 "num-traits",
 "procfs-core",
 "range-map",
 "scroll",
 "thiserror",
 "time 0.3.36",
 "tracing",
 "uuid",
]

[[package]]
name = "minidump-analyzer"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "breakpad-symbols",
 "futures-executor",
 "futures-util",
 "lazy_static",
 "log",
 "minidump",
 "minidump-unwind",
 "serde_json",
 "windows-sys",
]

[[package]]
name = "minidump-analyzer-export"
version = "0.1.0"
dependencies = [
 "minidump-analyzer",
 "mozilla-central-workspace-hack",
]

[[package]]
name = "minidump-common"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cd8a9fb054833d2f402e82e256aeef544e595e45fe8fca2de6d03ed605f6647"
dependencies = [
 "bitflags 2.6.0",
 "debugid",
 "num-derive",
 "num-traits",
 "range-map",
 "scroll",
 "smart-default",
]

[[package]]
name = "minidump-unwind"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efde3c09258c297c0f6761f04d97771ef82a59a6734e7ba0e6e2ef961fb3cbb3"
dependencies = [
 "async-trait",
 "breakpad-symbols",
 "cachemap2",
 "framehop",
 "memmap2",
 "minidump",
 "minidump-common",
 "object",
 "scroll",
 "tracing",
]

[[package]]
name = "minidump-writer"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c75ff36a030d76801ed7ec3ea4ae45f12c0f1297f3447790288194274e9aa98"
dependencies = [
 "bitflags 2.6.0",
 "byteorder",
 "cfg-if",
 "crash-context",
 "goblin 0.8.2",
 "libc",
 "log",
 "mach2",
 "memmap2",
 "memoffset 0.9.0",
 "minidump-common",
 "nix 0.29.0",
 "procfs-core",
 "scroll",
 "tempfile",
 "thiserror",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7810e0be55b428ada41041c41f32c9f1a42817901b4ccf45fa3d4b6561e74c7"
dependencies = [
 "adler",
]

[[package]]
name = "mio"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4569e456d394deccd22ce1c1913e6ea0e54519f577285001215d33557431afe4"
dependencies = [
 "hermit-abi",
 "libc",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys",
]

[[package]]
name = "mls-platform-api"
version = "0.1.0"
source = "git+https://github.com/beurdouche/mls-platform-api?rev=19c3f18b747d13354370ba84440bb0b963932634#19c3f18b747d13354370ba84440bb0b963932634"
dependencies = [
 "bincode",
 "hex",
 "mls-rs",
 "mls-rs-crypto-nss",
 "mls-rs-provider-sqlite",
 "serde",
 "serde_json",
 "sha2",
 "thiserror",
]

[[package]]
name = "mls-rs"
version = "0.39.1"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "cfg-if",
 "debug_tree",
 "futures",
 "getrandom",
 "hex",
 "itertools",
 "maybe-async",
 "mls-rs-codec",
 "mls-rs-core",
 "mls-rs-identity-x509",
 "mls-rs-provider-sqlite",
 "rand_core",
 "rayon",
 "serde",
 "thiserror",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "mls-rs-codec"
version = "0.5.3"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "mls-rs-codec-derive",
 "thiserror",
 "wasm-bindgen",
]

[[package]]
name = "mls-rs-codec-derive"
version = "0.1.1"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "mls-rs-core"
version = "0.18.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "hex",
 "maybe-async",
 "mls-rs-codec",
 "serde",
 "serde_bytes",
 "thiserror",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "mls-rs-crypto-hpke"
version = "0.9.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "cfg-if",
 "maybe-async",
 "mls-rs-core",
 "mls-rs-crypto-traits",
 "thiserror",
 "zeroize",
]

[[package]]
name = "mls-rs-crypto-nss"
version = "0.1.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "getrandom",
 "hex",
 "maybe-async",
 "mls-rs-core",
 "mls-rs-crypto-hpke",
 "mls-rs-crypto-traits",
 "nss-gk-api",
 "rand_core",
 "serde",
 "thiserror",
 "zeroize",
]

[[package]]
name = "mls-rs-crypto-traits"
version = "0.10.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "maybe-async",
 "mls-rs-core",
]

[[package]]
name = "mls-rs-identity-x509"
version = "0.11.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "maybe-async",
 "mls-rs-core",
 "thiserror",
 "wasm-bindgen",
]

[[package]]
name = "mls-rs-provider-sqlite"
version = "0.11.0"
source = "git+https://github.com/beurdouche/mls-rs?rev=eedb37e50e3fca51863f460755afd632137da57c#eedb37e50e3fca51863f460755afd632137da57c"
dependencies = [
 "async-trait",
 "hex",
 "maybe-async",
 "mls-rs-core",
 "rand",
 "rusqlite",
 "thiserror",
 "zeroize",
]

[[package]]
name = "mls_gk"
version = "0.1.0"
dependencies = [
 "hex",
 "log",
 "mls-platform-api",
 "nserror",
 "nss-gk-api",
 "nsstring",
 "rusqlite",
 "static_prefs",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "moz_asserts"
version = "0.1.0"
dependencies = [
 "mozbuild",
]

[[package]]
name = "moz_cbor"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2133b12230591b7e727c8977b96b791bba421cd80ce8eb08b782dcb4a43fa1e9"

[[package]]
name = "moz_task"
version = "0.1.0"
dependencies = [
 "async-task",
 "cstr",
 "libc",
 "log",
 "nserror",
 "nsstring",
 "xpcom",
]

[[package]]
name = "moz_task-gtest"
version = "0.1.0"
dependencies = [
 "moz_task",
]

[[package]]
name = "mozannotation_client"
version = "0.1.0"
dependencies = [
 "nsstring",
]

[[package]]
name = "mozannotation_server"
version = "0.1.0"
dependencies = [
 "memoffset 0.8.999",
 "mozannotation_client",
 "process_reader",
 "thin-vec",
 "thiserror",
]

[[package]]
name = "mozbuild"
version = "0.1.0"

[[package]]
name = "mozdevice"
version = "0.5.3"
dependencies = [
 "log",
 "once_cell",
 "regex",
 "tempfile",
 "thiserror",
 "unix_path",
 "uuid",
 "walkdir",
]

[[package]]
name = "mozglue-static"
version = "0.1.0"
dependencies = [
 "arrayvec",
 "cc",
 "mozbuild",
 "rustc_version",
]

[[package]]
name = "mozilla-central-workspace-hack"
version = "0.1.0"
dependencies = [
 "arrayvec",
 "bindgen 0.69.4",
 "bitflags 2.6.0",
 "bytes",
 "cc",
 "chrono",
 "clap",
 "core-foundation-sys",
 "crossbeam-utils",
 "dist-bin",
 "env_logger",
 "flate2",
 "fluent",
 "fluent-langneg",
 "fnv",
 "futures",
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-sink",
 "futures-util",
 "getrandom",
 "hashbrown 0.13.999",
 "hex",
 "hyper",
 "icu_locid",
 "icu_properties",
 "indexmap 2.2.6",
 "itertools",
 "libc",
 "lmdb-rkv-sys",
 "log",
 "memchr",
 "mio",
 "nom",
 "num-integer",
 "num-traits",
 "once_cell",
 "phf",
 "proc-macro2",
 "quote",
 "regex",
 "rkv",
 "scopeguard",
 "scroll",
 "semver",
 "serde",
 "serde_json",
 "smallvec",
 "stable_deref_trait",
 "strsim",
 "syn",
 "time 0.3.36",
 "time-macros",
 "tinystr",
 "tokio",
 "tokio-util",
 "toml",
 "tracing",
 "unic-langid",
 "unic-langid-impl",
 "unicode-bidi",
 "uniffi",
 "url",
 "uuid",
 "winapi",
 "windows-sys",
 "xml-rs",
 "yoke",
 "zerofrom",
 "zerovec",
 "zip",
]

[[package]]
name = "mozprofile"
version = "0.9.3"
dependencies = [
 "tempfile",
 "thiserror",
]

[[package]]
name = "mozrunner"
version = "0.15.3"
dependencies = [
 "dirs",
 "log",
 "mozprofile",
 "plist",
 "thiserror",
 "winreg",
]

[[package]]
name = "mozurl"
version = "0.0.1"
dependencies = [
 "nserror",
 "nsstring",
 "url",
 "uuid",
 "xpcom",
]

[[package]]
name = "mozversion"
version = "0.5.3"
dependencies = [
 "regex",
 "rust-ini",
 "semver",
 "thiserror",
]

[[package]]
name = "mozwer_s"
version = "0.1.0"
dependencies = [
 "libc",
 "mozilla-central-workspace-hack",
 "process_reader",
 "rust-ini",
 "serde",
 "serde_json",
 "uuid",
 "windows-sys",
]

[[package]]
name = "mp4parse"
version = "0.17.0"
source = "git+https://github.com/mozilla/mp4parse-rust?rev=a138e40ec1c603615873e524b5b22e11c0ec4820#a138e40ec1c603615873e524b5b22e11c0ec4820"
dependencies = [
 "bitreader",
 "byteorder",
 "fallible_collections",
 "log",
 "num-traits",
 "static_assertions",
]

[[package]]
name = "mp4parse-gtest"
version = "0.1.0"

[[package]]
name = "mp4parse_capi"
version = "0.17.0"
source = "git+https://github.com/mozilla/mp4parse-rust?rev=a138e40ec1c603615873e524b5b22e11c0ec4820#a138e40ec1c603615873e524b5b22e11c0ec4820"
dependencies = [
 "byteorder",
 "fallible_collections",
 "log",
 "mp4parse",
 "num-traits",
]

[[package]]
name = "murmurhash3"
version = "0.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2983372caf4480544083767bf2d27defafe32af49ab4df3a0b7fc90793a3664"

[[package]]
name = "naga"
version = "23.0.0"
source = "git+https://github.com/gfx-rs/wgpu?rev=5543961a71cc8e5399b696fae3f6aae82c019717#5543961a71cc8e5399b696fae3f6aae82c019717"
dependencies = [
 "arrayvec",
 "bit-set",
 "bitflags 2.6.0",
 "cfg_aliases 0.1.1",
 "codespan-reporting",
 "hexf-parse",
 "indexmap 2.2.6",
 "log",
 "rustc-hash",
 "serde",
 "spirv",
 "termcolor",
 "thiserror",
 "unicode-xid",
]

[[package]]
name = "neqo-bin"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "clap",
 "clap-verbosity-flag",
 "futures",
 "hex",
 "log",
 "neqo-common",
 "neqo-crypto",
 "neqo-http3",
 "neqo-transport",
 "neqo-udp",
 "qlog",
 "quinn-udp",
 "regex",
 "tokio",
 "url",
]

[[package]]
name = "neqo-common"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "enum-map",
 "env_logger",
 "log",
 "qlog",
 "windows",
]

[[package]]
name = "neqo-crypto"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "bindgen 0.69.4",
 "log",
 "mozbuild",
 "neqo-common",
 "semver",
 "serde",
 "serde_derive",
 "toml",
]

[[package]]
name = "neqo-http3"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "enumset",
 "log",
 "neqo-common",
 "neqo-crypto",
 "neqo-qpack",
 "neqo-transport",
 "qlog",
 "sfv",
 "url",
]

[[package]]
name = "neqo-qpack"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "log",
 "neqo-common",
 "neqo-transport",
 "qlog",
 "static_assertions",
]

[[package]]
name = "neqo-transport"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "enum-map",
 "indexmap 2.2.6",
 "log",
 "neqo-common",
 "neqo-crypto",
 "qlog",
 "smallvec",
 "static_assertions",
]

[[package]]
name = "neqo-udp"
version = "0.11.0"
source = "git+https://github.com/mozilla/neqo?tag=v0.11.0#c6d5502fb5b827473e7c5d7c4c380275cdb3d931"
dependencies = [
 "log",
 "neqo-common",
 "quinn-udp",
]

[[package]]
name = "neqo_glue"
version = "0.1.0"
dependencies = [
 "firefox-on-glean",
 "libc",
 "log",
 "neqo-common",
 "neqo-crypto",
 "neqo-http3",
 "neqo-qpack",
 "neqo-transport",
 "neqo-udp",
 "nserror",
 "nsstring",
 "qlog",
 "static_prefs",
 "thin-vec",
 "uuid",
 "winapi",
 "xpcom",
]

[[package]]
name = "netwerk_helper"
version = "0.0.1"
dependencies = [
 "nserror",
 "nsstring",
 "thin-vec",
]

[[package]]
name = "new_debug_unreachable"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4a24736216ec316047a1fc4252e27dabb04218aa4a3f37c6e7ddbf1f9782b54"

[[package]]
name = "nix"
version = "0.26.99"
dependencies = [
 "nix 0.29.0",
]

[[package]]
name = "nix"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71e2746dc3a24dd78b3cfcb7be93368c6de9963d30f43a6a73998a9cf4b17b46"
dependencies = [
 "bitflags 2.6.0",
 "cfg-if",
 "cfg_aliases 0.2.1",
 "libc",
]

[[package]]
name = "nmhproxy"
version = "0.1.0"
dependencies = [
 "dirs",
 "mozbuild",
 "mozilla-central-workspace-hack",
 "serde",
 "serde_json",
 "tempfile",
 "url",
]

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nserror"
version = "0.1.0"
dependencies = [
 "mozbuild",
 "nsstring",
]

[[package]]
name = "nss-gk-api"
version = "0.3.0"
source = "git+https://github.com/beurdouche/nss-gk-api?rev=e48a946811ffd64abc78de3ee284957d8d1c0d63#e48a946811ffd64abc78de3ee284957d8d1c0d63"
dependencies = [
 "bindgen 0.69.4",
 "log",
 "mozbuild",
 "once_cell",
 "pkcs11-bindings",
 "serde",
 "serde_derive",
 "toml",
]

[[package]]
name = "nss_build_common"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"

[[package]]
name = "nsstring"
version = "0.1.0"
dependencies = [
 "bitflags 2.6.0",
 "encoding_rs",
]

[[package]]
name = "nsstring-gtest"
version = "0.1.0"
dependencies = [
 "nsstring",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "num-integer"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225d3389fb3509a24c93f5c29eb6bde2586b98d9f016636dff58d7c6f7569cd9"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi",
 "libc",
]

[[package]]
name = "objc"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "915b1b472bc21c53464d6c8461c9d3af805ba1ef837e1cac254428f4a77177b1"
dependencies = [
 "malloc_buf",
]

[[package]]
name = "object"
version = "0.36.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "084f1a5821ac4c651660a94a7153d27ac9d8a53736203f58b31945ded098070a"
dependencies = [
 "memchr",
]

[[package]]
name = "oblivious_http"
version = "0.1.0"
dependencies = [
 "nserror",
 "ohttp",
 "rand",
 "thin-vec",
 "xpcom",
]

[[package]]
name = "ohttp"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "578cb11a3fb5c85697ed8bb850d5ad1cbf819d3eea05c2b253cf1d240fbb10c5"
dependencies = [
 "bindgen 0.64.999",
 "byteorder",
 "hex",
 "lazy_static",
 "log",
 "mozbuild",
 "serde",
 "serde_derive",
 "thiserror",
 "toml",
]

[[package]]
name = "once_cell"
version = "1.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1261fe7e33c73b354eab43b1273a57c8f967d0391e80353e51f764ac02cf6775"

[[package]]
name = "ordered-float"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d84eb1409416d254e4a9c8fa56cc24701755025b458f0fcd8e59e1f5f40c23bf"
dependencies = [
 "num-traits",
]

[[package]]
name = "origin-trial-token"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94cb60fca11d2efd72ab0e0ad0298089307a15b14313178416a96476dbea4550"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "origin-trials-ffi"
version = "0.1.0"
dependencies = [
 "origin-trial-token",
]

[[package]]
name = "osclientcerts-static"
version = "0.1.4"
dependencies = [
 "byteorder",
 "core-foundation",
 "env_logger",
 "lazy_static",
 "libloading",
 "log",
 "mozilla-central-workspace-hack",
 "pkcs11-bindings",
 "rsclientcerts",
 "sha2",
 "winapi",
]

[[package]]
name = "oslog"
version = "0.1.999"
dependencies = [
 "log",
]

[[package]]
name = "oxilangtag"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d91edf4fbb970279443471345a4e8c491bf05bb283b3e6c88e4e606fd8c181b"

[[package]]
name = "oxilangtag-ffi"
version = "0.1.0"
dependencies = [
 "nsstring",
 "oxilangtag",
]

[[package]]
name = "parking_lot"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"
dependencies = [
 "lock_api",
 "parking_lot_core",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall",
 "smallvec",
 "windows-targets",
]

[[package]]
name = "paste"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d01a5bd0424d00070b0098dd17ebca6f961a959dead1dbcbbbc1d1cd8d3deeba"

[[package]]
name = "payload-support"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "pe-unwind-info"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ec3b43050c38ffb9de87e17d874e9956e3a9131b343c9b7b7002597727c3891"
dependencies = [
 "arrayvec",
 "bitflags 2.6.0",
 "thiserror",
 "zerocopy",
 "zerocopy-derive",
]

[[package]]
name = "peek-poke"
version = "0.3.0"
dependencies = [
 "euclid",
 "peek-poke-derive",
]

[[package]]
name = "peek-poke-derive"
version = "0.3.0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
 "unicode-xid",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "phf"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ade2d8b8f33c7333b51bcf0428d37e217e9f32192ae4772156f65063b8ce03dc"
dependencies = [
 "phf_macros",
 "phf_shared",
]

[[package]]
name = "phf_codegen"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8d39688d359e6b34654d328e262234662d16cc0f60ec8dcbe5e718709342a5a"
dependencies = [
 "phf_generator",
 "phf_shared",
]

[[package]]
name = "phf_generator"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48e4cc64c2ad9ebe670cb8fd69dd50ae301650392e81c05f9bfcb2d5bdbc24b0"
dependencies = [
 "phf_shared",
 "rand",
]

[[package]]
name = "phf_macros"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3444646e286606587e49f3bcf1679b8cef1dc2c5ecc29ddacaffc305180d464b"
dependencies = [
 "phf_generator",
 "phf_shared",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "phf_shared"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90fcb95eef784c2ac79119d1dd819e162b5da872ce6f3c3abe1e8ca1c082f72b"
dependencies = [
 "siphasher",
]

[[package]]
name = "pin-project"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c95a7476719eab1e366eaf73d0260af3021184f18177925b07f54b30089ceead"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39407670928234ebc5e6e580247dd567ad73a3578460c5990f9503df207e8f07"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "pin-project-lite"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bda66fc9667c18cb2758a2ac84d1167245054bcf85d5d1aaa6923f45801bdd02"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkcs11-bindings"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0fabbdbe64b22820753da90995b3a73d02907eaeeac6f2414962a566aaa18ea"
dependencies = [
 "bindgen 0.69.4",
]

[[package]]
name = "pkg-config"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ac9a59f73473f1b8d852421e59e64809f025994837ef743615c6d0c5b305160"

[[package]]
name = "plain"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4596b6d070b27117e987119b4dac604f3c58cfb0b191112e24771b2faeac1a6"

[[package]]
name = "plane-split"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c1f7d82649829ecdef8e258790b0587acf0a8403f0ce963473d8e918acc1643"
dependencies = [
 "euclid",
 "log",
 "smallvec",
]

[[package]]
name = "plist"
version = "1.3.1"
dependencies = [
 "base64 0.13.999",
 "indexmap 2.2.6",
 "line-wrap",
 "serde",
 "time 0.3.36",
 "xml-rs",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b40af805b3121feab8a3c29f04d8ad262fa8e0561883e7653e024ae4479e6de"

[[package]]
name = "precomputed-hash"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "925383efa346730478fb4838dbe9137d2a47675ad789c546d150a6e1dd4ab31c"

[[package]]
name = "prefs_parser"
version = "0.0.1"

[[package]]
name = "presser"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8cf8e6a8aa66ce33f63993ffc4ea4271eb5b0530a9002db8455ea6050c77bfa"

[[package]]
name = "prio"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cec5eb0d28eee4ea74be34b28ed4c625e88c54ff83c21b412a5ea7cc48624ae"
dependencies = [
 "byteorder",
 "getrandom",
 "rand_core",
 "serde",
 "sha3",
 "subtle",
 "thiserror",
]

[[package]]
name = "proc-macro2"
version = "1.0.86"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e719e8df665df0d1c8fbfd238015744736151d4445ec0836b8e628aae103b77"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "process_reader"
version = "0.1.0"
dependencies = [
 "goblin 0.7.999",
 "libc",
 "mach2",
 "memoffset 0.9.0",
 "mozilla-central-workspace-hack",
 "scroll",
 "thiserror",
 "windows-sys",
]

[[package]]
name = "processtools"
version = "0.1.0"
dependencies = [
 "libc",
 "log",
 "nserror",
 "winapi",
 "xpcom",
]

[[package]]
name = "procfs-core"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d3554923a69f4ce04c4a754260c338f505ce22642d3830e049a399fc2059a29"
dependencies = [
 "bitflags 2.6.0",
 "hex",
]

[[package]]
name = "profiler-macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn",
]

[[package]]
name = "profiler_helper"
version = "0.1.0"
dependencies = [
 "memmap2",
 "object",
 "rustc-demangle",
 "thin-vec",
 "uuid",
]

[[package]]
name = "profiling"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74605f360ce573babfe43964cbe520294dcb081afbf8c108fc6e23036b4da2df"

[[package]]
name = "prost"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4fdd22f3b9c31b53c060df4a0613a1c7f062d4115a2b984dd15b1858f7e340d"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-derive"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "265baba7fabd416cf5078179f7d2cbeca4ce7a9041111900675ea7c4cb8a4c32"
dependencies = [
 "anyhow",
 "itertools",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "pulse"
version = "0.3.0"
source = "git+https://github.com/mozilla/cubeb-pulse-rs?rev=8678dcab1c287de79c4c184ccc2e065bc62b70e2#8678dcab1c287de79c4c184ccc2e065bc62b70e2"
dependencies = [
 "bitflags 2.6.0",
 "pulse-ffi",
]

[[package]]
name = "pulse-ffi"
version = "0.1.0"
source = "git+https://github.com/mozilla/cubeb-pulse-rs?rev=8678dcab1c287de79c4c184ccc2e065bc62b70e2#8678dcab1c287de79c4c184ccc2e065bc62b70e2"
dependencies = [
 "libc",
]

[[package]]
name = "qcms"
version = "0.3.0"
dependencies = [
 "libc",
 "version_check",
]

[[package]]
name = "qlog"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b5f65b920fa913ce92267bb3c4ed3b9c2f81d05f8e1376c3bbc95455eedb7df"
dependencies = [
 "serde",
 "serde_derive",
 "serde_json",
 "serde_with",
 "smallvec",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quinn-udp"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c40286217b4ba3a71d644d752e6a0b71f13f1b6a2c5311acfcbe0c2418ed904"
dependencies = [
 "cfg_aliases 0.2.1",
 "libc",
 "log",
 "once_cell",
 "socket2 0.5.7",
 "windows-sys",
]

[[package]]
name = "quote"
version = "1.0.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "291ec9ab5efd934aaf503a6466c5d5251535d108ee747472c3977cc5acc868ef"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha",
 "rand_core",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom",
]

[[package]]
name = "rand_distr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32cb0b9bc82b0a0876c2dd994a7e7a2683d3e7390ca40e6886785ef0c7e3ee31"
dependencies = [
 "num-traits",
 "rand",
]

[[package]]
name = "range-alloc"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8a99fddc9f0ba0a85884b8d14e3592853e787d581ca1816c91349b10e4eeab"

[[package]]
name = "range-map"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12a5a2d6c7039059af621472a4389be1215a816df61aa4d531cfe85264aee95f"
dependencies = [
 "num-traits",
]

[[package]]
name = "raw-window-handle"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42a9830a0e1b9fb145ebb365b8bc4ccd75f290f98c0247deafbbe2c75cefb544"

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "redox_syscall"
version = "0.5.999"

[[package]]
name = "redox_users"
version = "0.4.999"

[[package]]
name = "regex"
version = "1.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12de2eff854e5fa4b1295edd650e227e9d8fb0c9e90b12e7f36d6a6811791a29"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata",
 "regex-syntax",
]

[[package]]
name = "regex-automata"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49530408a136e16e5b486e883fbb6ba058e8e4e8ae6621a77b048b314336e629"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax",
]

[[package]]
name = "regex-syntax"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbb5fb1acd8a1a18b3dd5be62d25485eb770e05afb408a9627d14d451bae12da"

[[package]]
name = "relevancy"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "base64 0.21.3",
 "error-support",
 "interrupt-support",
 "log",
 "md-5",
 "parking_lot",
 "rand",
 "rand_distr",
 "remote_settings",
 "rusqlite",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "sql-support",
 "thiserror",
 "uniffi",
 "url",
]

[[package]]
name = "remote_settings"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "camino",
 "error-support",
 "firefox-versioning",
 "log",
 "parking_lot",
 "regex",
 "rusqlite",
 "serde",
 "serde_json",
 "sha2",
 "thiserror",
 "uniffi",
 "url",
 "viaduct",
]

[[package]]
name = "remove_dir_all"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3acd125665422973a33ac9d3dd2df85edad0f4ae9b00dafb1a05e43a9f5ef8e7"
dependencies = [
 "winapi",
]

[[package]]
name = "replace_with"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3a8614ee435691de62bcffcf4a66d91b3594bf1428a5722e79103249a095690"

[[package]]
name = "ringbuf"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f65af18d50f789e74aaf23bbb3f65dcd22a3cb6e029b5bced149f6bd57c5c2a2"
dependencies = [
 "cache-padded",
]

[[package]]
name = "rkv"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c6d906922d99c677624d2042a93f89b2b7df0f6411032237d5d99a602c2487c"
dependencies = [
 "arrayref",
 "bincode",
 "bitflags 2.6.0",
 "byteorder",
 "id-arena",
 "lazy_static",
 "lmdb-rkv",
 "log",
 "ordered-float",
 "paste",
 "serde",
 "serde_derive",
 "thiserror",
 "url",
 "uuid",
]

[[package]]
name = "rmp"
version = "0.8.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "228ed7c16fa39782c3b3468e974aec2795e9089153cd08ee2e9aefb3613334c4"
dependencies = [
 "byteorder",
 "num-traits",
 "paste",
]

[[package]]
name = "rmp-serde"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52e599a477cf9840e92f2cde9a7189e67b42c57532749bf90aea6ec10facd4db"
dependencies = [
 "byteorder",
 "rmp",
 "serde",
]

[[package]]
name = "ron"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b91f7eff05f748767f183df4320a63d6936e9c6107d97c9e6bdd9784f4289c94"
dependencies = [
 "base64 0.21.3",
 "bitflags 2.6.0",
 "serde",
 "serde_derive",
]

[[package]]
name = "rsclientcerts"
version = "0.1.0"
dependencies = [
 "byteorder",
 "pkcs11-bindings",
]

[[package]]
name = "rsdparsa_capi"
version = "0.1.0"
dependencies = [
 "libc",
 "log",
 "nserror",
 "webrtc-sdp",
]

[[package]]
name = "runloop"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d79b4b604167921892e84afbbaad9d5ad74e091bf6c511d9dbfb0593f09fabd"

[[package]]
name = "rure"
version = "0.2.2"
dependencies = [
 "libc",
 "regex",
]

[[package]]
name = "rusqlite"
version = "0.31.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b838eba278d213a8beaf485bd313fd580ca4505a00d5871caeb1457c55322cae"
dependencies = [
 "bitflags 2.6.0",
 "fallible-iterator",
 "fallible-streaming-iterator",
 "hashlink",
 "libsqlite3-sys",
 "serde_json",
 "smallvec",
]

[[package]]
name = "rust-ini"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a654c5bda722c699be6b0fe4c0d90de218928da5b724c3e467fc48865c37263"

[[package]]
name = "rust_cascade"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04249959e1b66d36f746f45ca8d0eed17cdc30c30aad178a856b7c45d51fe127"
dependencies = [
 "byteorder",
 "murmurhash3",
 "rand",
 "sha2",
]

[[package]]
name = "rust_decimal"
version = "1.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13cf35f7140155d02ba4ec3294373d513a3c7baa8364c162b030e33c61520a8"
dependencies = [
 "arrayvec",
 "num-traits",
]

[[package]]
name = "rust_minidump_writer_linux"
version = "0.1.0"
dependencies = [
 "anyhow",
 "crash-context",
 "libc",
 "minidump-writer",
]

[[package]]
name = "rustc-demangle"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ef03e0a2b150c7a90d01faf6254c9c48a41e95fb2a8c2ac1c6f0d2b9aefc342"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver",
]

[[package]]
name = "rustix"
version = "0.38.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70dc5ec042f7a43c4a73241207cecc9873a06d45debb38b329f8541d85c2730f"
dependencies = [
 "bitflags 2.6.0",
 "errno",
 "libc",
 "linux-raw-sys",
 "windows-sys",
]

[[package]]
name = "ryu"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b4b9743ed687d4b4bcedf9ff5eaa7398495ae14e61cba0a295704edbc7decde"

[[package]]
name = "safemem"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef703b7cb59335eae2eb93ceb664c0eb7ea6bf567079d843e09420219668e072"

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "scoped-tls"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cf6437eb19a8f4a6cc0f7dca544973b0b78843adbfeb3683d1a94a0024a294"

[[package]]
name = "scopeguard"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd"

[[package]]
name = "scroll"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ab8598aa408498679922eff7fa985c25d58a90771bd6be794434c5277eab1a6"
dependencies = [
 "scroll_derive",
]

[[package]]
name = "scroll_derive"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f81c2fde025af7e69b1d1420531c8a8811ca898919db177141a85313b1cb932"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "selectors"
version = "0.26.0"
dependencies = [
 "bitflags 2.6.0",
 "cssparser",
 "derive_more 0.99.999",
 "fxhash",
 "log",
 "new_debug_unreachable",
 "phf",
 "phf_codegen",
 "precomputed-hash",
 "servo_arc",
 "smallvec",
 "to_shmem",
 "to_shmem_derive",
]

[[package]]
name = "self_cell"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ef965a420fe14fdac7dd018862966a4c14094f900e1650bbc71ddd7d580c8af"

[[package]]
name = "semver"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58bc9567378fc7690d6b2addae4e60ac2eeea07becb2c64b9f218b53865cba2a"
dependencies = [
 "serde",
]

[[package]]
name = "serde"
version = "1.0.215"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6513c1ad0b11a9376da888e3e0baa0077f1aed55c17f50e7b2397136129fb88f"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_bytes"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "416bda436f9aab92e02c8e10d49a15ddd339cea90b6e340fe51ed97abb548294"
dependencies = [
 "serde",
]

[[package]]
name = "serde_cbor"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bef2ebfde456fb76bbcf9f59315333decc4fda0b2b44b420243c11e0f5ec1f5"
dependencies = [
 "half",
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.215"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad1e866f866923f252f05c889987993144fb74e722403468a4ebd70c3cd756c0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "serde_json"
version = "1.0.116"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e17db7126d17feb94eb3fad46bf1a96b034e8aacbc2e775fe81505f8b0b2813"
dependencies = [
 "indexmap 2.2.6",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_path_to_error"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7f05c1d5476066defcdfacce1f52fc3cae3af1d3089727100c02ae92e5abbe0"
dependencies = [
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcec881020c684085e55a25f7fd888954d56609ef363479dc5a1305eb0d40cab"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f02d8aa6e3c385bf084924f660ce2a3a6bd333ba55b35e8590b321f35d88513"
dependencies = [
 "serde",
 "serde_with_macros",
]

[[package]]
name = "serde_with_macros"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edc7d5d3932fb12ce722ee5e64dd38c504efba37567f0c402f6ca728c3b8b070"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "serde_yaml"
version = "0.8.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "578a7433b776b56a35785ed5ce9a7e777ac0598aac5a6dd1b4b18a307c7fc71b"
dependencies = [
 "indexmap 1.999.999",
 "ryu",
 "serde",
 "yaml-rust",
]

[[package]]
name = "servo_arc"
version = "0.4.0"
dependencies = [
 "stable_deref_trait",
]

[[package]]
name = "sfv"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f27daf6ed3fc7ffd5ea3ce9f684fe351c47e50f2fdbb6236e2bad0b440dbe408"
dependencies = [
 "data-encoding",
 "indexmap 2.2.6",
 "rust_decimal",
]

[[package]]
name = "sha1"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f04293dc80c3993519f2d7f6f511707ee7094fe0c6d3406feb330cdb3540eba3"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest",
 "keccak",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signature_cache"
version = "0.1.0"
dependencies = [
 "hashlink",
]

[[package]]
name = "siphasher"
version = "0.3.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bd3e3206899af3f8b12af284fafc038cc1dc2b41d1b89dd17297221c5d225de"

[[package]]
name = "slab"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6528351c9bc8ab22353f9d776db39a20288e8d6c37ef8cfe3317cf875eecfc2d"
dependencies = [
 "autocfg",
]

[[package]]
name = "smallbitvec"
version = "2.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75ce4f9dc4a41b4c3476cc925f1efb11b66df373a8fde5d4b8915fa91b5d995e"

[[package]]
name = "smallvec"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6ecd384b10a64542d77071bd64bd7b231f4ed5940fba55e98c3de13824cf3d7"
dependencies = [
 "serde",
]

[[package]]
name = "smart-default"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eb01866308440fc64d6c44d9e86c5cc17adfe33c4d6eed55da9145044d0ffc1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "smawk"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7c388c1b5e93756d0c740965c41e8822f866621d41acbdf6336a6a168f8840c"

[[package]]
name = "smoosh"
version = "0.1.0"
dependencies = [
 "bumpalo",
 "env_logger",
 "jsparagus",
 "log",
]

[[package]]
name = "socket2"
version = "0.4.999"
dependencies = [
 "socket2 0.5.7",
]

[[package]]
name = "socket2"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce305eb0b4296696835b71df73eb912e0f1ffd2556a501fcede6e0c50349191c"
dependencies = [
 "libc",
 "windows-sys",
]

[[package]]
name = "spirv"
version = "0.3.0+sdk-1.3.268.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eda41003dc44290527a59b13432d4a0379379fa074b70174882adfbdfd917844"
dependencies = [
 "bitflags 2.6.0",
]

[[package]]
name = "sql-support"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "interrupt-support",
 "lazy_static",
 "log",
 "nss_build_common",
 "parking_lot",
 "rusqlite",
 "tempfile",
 "thiserror",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "static_prefs"
version = "0.1.0"
dependencies = [
 "mozbuild",
 "nsstring",
]

[[package]]
name = "storage"
version = "0.1.0"
dependencies = [
 "libc",
 "nserror",
 "nsstring",
 "storage_variant",
 "xpcom",
]

[[package]]
name = "storage_variant"
version = "0.1.0"
dependencies = [
 "libc",
 "nserror",
 "nsstring",
 "xpcom",
]

[[package]]
name = "strck"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be91090ded9d8f979d9fe921777342d37e769e0b6b7296843a7a38247240e917"

[[package]]
name = "strck_ident"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1c3802b169b3858a44667f221c9a0b3136e6019936ea926fc97fbad8af77202"
dependencies = [
 "strck",
 "unicode-ident",
]

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "style"
version = "0.0.1"
dependencies = [
 "app_units",
 "arrayvec",
 "atomic_refcell",
 "bindgen 0.69.4",
 "bitflags 2.6.0",
 "byteorder",
 "cssparser",
 "derive_more 0.99.999",
 "dom",
 "euclid",
 "fxhash",
 "gecko-profiler",
 "icu_segmenter",
 "indexmap 2.2.6",
 "itertools",
 "itoa",
 "lazy_static",
 "log",
 "malloc_size_of",
 "malloc_size_of_derive",
 "matches",
 "mozbuild",
 "new_debug_unreachable",
 "nsstring",
 "num-derive",
 "num-integer",
 "num-traits",
 "num_cpus",
 "parking_lot",
 "precomputed-hash",
 "rayon",
 "rayon-core",
 "regex",
 "selectors",
 "serde",
 "servo_arc",
 "smallbitvec",
 "smallvec",
 "static_assertions",
 "static_prefs",
 "style_derive",
 "style_traits",
 "thin-vec",
 "to_shmem",
 "to_shmem_derive",
 "toml",
 "uluru",
 "unicode-bidi",
 "void",
 "walkdir",
]

[[package]]
name = "style_derive"
version = "0.0.1"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "style_traits"
version = "0.0.1"
dependencies = [
 "app_units",
 "bitflags 2.6.0",
 "cssparser",
 "euclid",
 "lazy_static",
 "malloc_size_of",
 "malloc_size_of_derive",
 "nsstring",
 "selectors",
 "serde",
 "servo_arc",
 "thin-vec",
 "to_shmem",
 "to_shmem_derive",
]

[[package]]
name = "stylo_tests"
version = "0.0.1"
dependencies = [
 "euclid",
 "style",
]

[[package]]
name = "subtle"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81cdd64d312baedb58e21336b31bc043b77e01cc99033ce76ef539f78e965ebc"

[[package]]
name = "suggest"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "chrono",
 "error-support",
 "extend",
 "interrupt-support",
 "log",
 "once_cell",
 "parking_lot",
 "remote_settings",
 "rmp-serde",
 "rusqlite",
 "serde",
 "serde_json",
 "sql-support",
 "thiserror",
 "uniffi",
 "url",
 "viaduct",
]

[[package]]
name = "svg_fmt"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fb1df15f412ee2e9dfc1c504260fa695c1c3f10fe9f4a6ee2d2184d7d6450e2"

[[package]]
name = "swgl"
version = "0.1.0"
dependencies = [
 "cc",
 "gleam",
 "glsl-to-cxx",
 "webrender_build",
]

[[package]]
name = "syn"
version = "2.0.87"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25aa4ce346d03a6dcd68dd8b4010bcb74e54e62c90c573f394c46eae99aba32d"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync-guid"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "base64 0.21.3",
 "rand",
 "rusqlite",
 "serde",
]

[[package]]
name = "sync15"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "error-support",
 "interrupt-support",
 "lazy_static",
 "log",
 "payload-support",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_path_to_error",
 "sync-guid",
 "thiserror",
 "uniffi",
]

[[package]]
name = "synstructure"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8af7666ab7b6390ab78131fb5b0fce11d6b7a6951602017c35fa82800708971"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "sys-locale"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e801cf239ecd6ccd71f03d270d67dd53d13e90aab208bf4b8fe4ad957ea949b0"
dependencies = [
 "libc",
]

[[package]]
name = "tabs"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "error-support",
 "interrupt-support",
 "lazy_static",
 "log",
 "payload-support",
 "rusqlite",
 "serde",
 "serde_derive",
 "serde_json",
 "sql-support",
 "sync-guid",
 "sync15",
 "thiserror",
 "types",
 "uniffi",
 "url",
]

[[package]]
name = "tempfile"
version = "3.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85b77fafb263dd9d05cbeac119526425676db3784113aa9295c88498cbf8bff1"
dependencies = [
 "cfg-if",
 "fastrand",
 "rustix",
 "windows-sys",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "terminal_size"
version = "0.3.999"

[[package]]
name = "test-builtins-static"
version = "0.1.0"
dependencies = [
 "bindgen 0.69.4",
 "mozbuild",
 "mozilla-central-workspace-hack",
 "nom",
 "pkcs11-bindings",
 "smallvec",
]

[[package]]
name = "textwrap"
version = "0.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23d434d3f8967a09480fb04132ebe0a3e088c173e6d0ee7897abbdf4eab0f8b9"
dependencies = [
 "smawk",
]

[[package]]
name = "thin-vec"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aac81b6fd6beb5884b0cf3321b8117e6e5d47ecb6fc89f414cfdcca8b2fe2dd8"

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "threadbound"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06638f039e6c49cd649d92e0e792f665d088bece8eba4c99c6fdfc2776b4ddb0"

[[package]]
name = "time"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b797afad3f312d1c66a56d11d0316f916356d11bd158fbc6ca6389ff6bf805a"
dependencies = [
 "libc",
 "wasi 0.10.0+wasi-snapshot-preview999",
 "winapi",
]

[[package]]
name = "time"
version = "0.3.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5dfd88e563464686c916c7e46e623e520ddc6d79fa6641390f2e3fa86e83e885"
dependencies = [
 "deranged",
 "itoa",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef927ca75afb808a4d64dd374f00a2adf8d0fcff8e7b184af886c3c87ec4a3f3"

[[package]]
name = "time-macros"
version = "0.2.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f252a68540fde3a3877aeea552b832b40ab9a69e318efd078774a01ddee1ccf"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tinystr"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9117f5d4db391c1cf6927e7bea3db74b9a1c1add8f7eda9ffd5364f40f57b82f"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "to_shmem"
version = "0.1.0"
dependencies = [
 "cssparser",
 "servo_arc",
 "smallbitvec",
 "smallvec",
 "thin-vec",
]

[[package]]
name = "to_shmem_derive"
version = "0.1.0"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "tokio"
version = "1.39.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daa4fb1bc778bd6f04cbfc4bb2d06a7396a8f299dc33ea1900cedaa316f467b1"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio",
 "pin-project-lite",
 "socket2 0.5.7",
 "tokio-macros",
 "windows-sys",
]

[[package]]
name = "tokio-macros"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "693d596312e88961bc67d7f1f97af8a70227d9f90c31bba5806eec004978d752"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "tokio-stream"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fb52b74f05dbf495a8fba459fdc331812b96aa086d9eb78101fa0d4569c3313"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-util"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f988a1a1adc2fb21f9c12aa96441da33a1728193ae0b95d2be22dbd17fcb4e5c"
dependencies = [
 "bytes",
 "futures-core",
 "futures-sink",
 "pin-project-lite",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "topological-sort"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa7c7f42dea4b1b99439786f5633aeb9c14c1b53f75e282803c2ec2ad545873c"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce8c33a8d48bd45d624a6e523445fd21ec13d3653cd51f681abf67418f54eb8"
dependencies = [
 "cfg-if",
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f57e3ca2a01450b1a921183a9c9cbfda207fd822cef4ccb00a65402cbba7a74"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "tracing-core"
version = "0.1.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24eb03ba0eab1fd845050058ce5e616558e8f8d8fca633e6b163fe25c797213a"
dependencies = [
 "once_cell",
]

[[package]]
name = "tracy-rs"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce607aae8ab0ab3abf3a2723a9ab6f09bb8639ed83fdd888d857b8e556c868d8"

[[package]]
name = "triple_buffer"
version = "5.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "803966e5a8397a70d3d8111afa1597ba8381346d7de4720e9f539471d371a1a3"
dependencies = [
 "cache-padded",
]

[[package]]
name = "try-lock"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3528ecfd12c466c6f163363caf2d02a71161dd5e1cc6ae7b34207ea2d42d81ed"

[[package]]
name = "type-map"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6d3364c5e96cb2ad1603037ab253ddd34d7fb72a58bdddf4b7350760fc69a46"
dependencies = [
 "rustc-hash",
]

[[package]]
name = "typed-arena-nomut"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfc9d8d4e8c94375df96d6ac01a18c263d3d529bc4a53a207580ae9bc30e87c1"

[[package]]
name = "typenum"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "497961ef93d974e23eb6f433eb5fe1b7930b659f06d12dec6fc44a8f554c0bba"

[[package]]
name = "types"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "rusqlite",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "uluru"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "794a32261a1f5eb6a4462c81b59cec87b5c27d5deea7dd1ac8fc781c41d226db"
dependencies = [
 "arrayvec",
]

[[package]]
name = "unic-langid"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23dd9d1e72a73b25e07123a80776aae3e7b0ec461ef94f9151eed6ec88005a44"
dependencies = [
 "unic-langid-impl",
]

[[package]]
name = "unic-langid-ffi"
version = "0.1.0"
dependencies = [
 "nsstring",
 "thin-vec",
 "unic-langid",
]

[[package]]
name = "unic-langid-impl"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a5422c1f65949306c99240b81de9f3f15929f5a8bfe05bb44b034cc8bf593e5"
dependencies = [
 "tinystr",
]

[[package]]
name = "unicase"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50f37be617794602aabbeee0be4f259dc1778fabe05e2d67ee8f79326d5cb4f6"
dependencies = [
 "version_check",
]

[[package]]
name = "unicode-bidi"
version = "0.3.15"
source = "git+https://github.com/servo/unicode-bidi?rev=ca612daf1c08c53abe07327cb3e6ef6e0a760f0c#ca612daf1c08c53abe07327cb3e6ef6e0a760f0c"
dependencies = [
 "smallvec",
]

[[package]]
name = "unicode-bidi-ffi"
version = "0.1.0"
dependencies = [
 "icu_properties",
 "unicode-bidi",
]

[[package]]
name = "unicode-ident"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84a22b9f218b40614adcb3f4ff08b703773ad44fa9423e4e0d346d5db86e4ebc"

[[package]]
name = "unicode-width"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0edd1e5b14653f783770bce4a4dabb4a5108a5370a5f5d8cfe8710c361f6c8b"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "uniffi"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51ce6280c581045879e11b400bae14686a819df22b97171215d15549efa04ddb"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "uniffi_bindgen",
 "uniffi_build",
 "uniffi_core",
 "uniffi_macros",
]

[[package]]
name = "uniffi-bindgen-gecko-js"
version = "0.1.0"
dependencies = [
 "anyhow",
 "askama",
 "camino",
 "cargo_metadata",
 "clap",
 "extend",
 "heck",
 "serde",
 "textwrap",
 "toml",
 "uniffi",
 "uniffi_bindgen",
]

[[package]]
name = "uniffi-example-arithmetic"
version = "0.22.0"
dependencies = [
 "thiserror",
 "uniffi",
]

[[package]]
name = "uniffi-example-custom-types"
version = "0.19.6"
dependencies = [
 "anyhow",
 "bytes",
 "serde_json",
 "uniffi",
 "url",
]

[[package]]
name = "uniffi-example-geometry"
version = "0.22.0"
dependencies = [
 "uniffi",
]

[[package]]
name = "uniffi-example-rondpoint"
version = "0.22.0"
dependencies = [
 "uniffi",
]

[[package]]
name = "uniffi-example-sprites"
version = "0.22.0"
dependencies = [
 "uniffi",
]

[[package]]
name = "uniffi-example-todolist"
version = "0.22.0"
dependencies = [
 "once_cell",
 "thiserror",
 "uniffi",
]

[[package]]
name = "uniffi-fixture-callbacks"
version = "0.21.0"
dependencies = [
 "thiserror",
 "uniffi",
]

[[package]]
name = "uniffi-fixture-external-types"
version = "0.21.0"
dependencies = [
 "thiserror",
 "uniffi",
 "uniffi-example-geometry",
 "uniffi-example-sprites",
]

[[package]]
name = "uniffi-fixture-futures"
version = "0.21.0"
dependencies = [
 "log",
 "moz_task",
 "uniffi",
]

[[package]]
name = "uniffi-fixture-refcounts"
version = "0.21.0"
dependencies = [
 "uniffi",
]

[[package]]
name = "uniffi-trait-interfaces"
version = "0.22.0"
dependencies = [
 "uniffi",
]

[[package]]
name = "uniffi_bindgen"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e9f25730c9db2e878521d606f54e921edb719cdd94d735e7f97705d6796d024"
dependencies = [
 "anyhow",
 "askama",
 "camino",
 "cargo_metadata",
 "fs-err",
 "glob",
 "goblin 0.8.2",
 "heck",
 "once_cell",
 "paste",
 "serde",
 "textwrap",
 "toml",
 "uniffi_meta",
 "uniffi_testing",
 "uniffi_udl",
]

[[package]]
name = "uniffi_build"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88dba57ac699bd8ec53d6a352c8dd0e479b33f698c5659831bb1e4ce468c07bd"
dependencies = [
 "anyhow",
 "camino",
 "uniffi_bindgen",
]

[[package]]
name = "uniffi_checksum_derive"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2c801f0f05b06df456a2da4c41b9c2c4fdccc6b9916643c6c67275c4c9e4d07"
dependencies = [
 "quote",
 "syn",
]

[[package]]
name = "uniffi_core"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61049e4db6212d0ede80982adf0e1d6fa224e6118387324c5cfbe3083dfb2252"
dependencies = [
 "anyhow",
 "bytes",
 "log",
 "once_cell",
 "paste",
 "static_assertions",
]

[[package]]
name = "uniffi_macros"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b40fd2249e0c5dcbd2bfa3c263db1ec981f7273dca7f4132bf06a272359a586c"
dependencies = [
 "bincode",
 "camino",
 "fs-err",
 "once_cell",
 "proc-macro2",
 "quote",
 "serde",
 "syn",
 "toml",
 "uniffi_meta",
]

[[package]]
name = "uniffi_meta"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9ad57039b4fafdbf77428d74fff40e0908e5a1731e023c19cfe538f6d4a8ed6"
dependencies = [
 "anyhow",
 "bytes",
 "siphasher",
 "uniffi_checksum_derive",
]

[[package]]
name = "uniffi_testing"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21fa171d4d258dc51bbd01893cc9608c1b62273d2f9ea55fb64f639e77824567"
dependencies = [
 "anyhow",
 "camino",
 "cargo_metadata",
 "fs-err",
 "once_cell",
]

[[package]]
name = "uniffi_udl"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f52299e247419e7e2934bef2f94d7cccb0e6566f3248b1d48b160d8f369a2668"
dependencies = [
 "anyhow",
 "textwrap",
 "uniffi_meta",
 "uniffi_testing",
 "weedle2",
]

[[package]]
name = "unix_path"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af8e291873ae77c4c8d9c9b34d0bee68a35b048fb39c263a5155e0e353783eaf"
dependencies = [
 "unix_str",
]

[[package]]
name = "unix_str"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ace0b4755d0a2959962769239d56267f8a024fef2d9b32666b3dcd0946b0906"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
 "serde",
]

[[package]]
name = "utf16_iter"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8232dd3cdaed5356e0f716d285e4b40b932ac434100fe9b7e0e8e935b9e6246"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "uuid"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1674845326ee10d37ca60470760d4288a6f80f304007d92e5c53bab78c9cfd79"
dependencies = [
 "getrandom",
 "serde",
]

[[package]]
name = "vcpkg"
version = "0.2.999"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "viaduct"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "ffi-support",
 "log",
 "once_cell",
 "parking_lot",
 "prost",
 "serde",
 "serde_json",
 "thiserror",
 "url",
]

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "walkdir"
version = "2.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "808cf2735cd4b6866113f648b791c6adc5714537bc222d9347bb203386ffda56"
dependencies = [
 "same-file",
 "winapi",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ce8a968cb1cd110d136ff8b819a556d6fb6d919363c61534f6860c7eb172ba0"
dependencies = [
 "log",
 "try-lock",
]

[[package]]
name = "warp"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4378d202ff965b011c64817db11d5829506d3404edeadb61f190d111da3f231c"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "headers",
 "http",
 "hyper",
 "log",
 "mime",
 "mime_guess",
 "percent-encoding",
 "pin-project",
 "scoped-tls",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "tokio",
 "tokio-util",
 "tower-service",
 "tracing",
]

[[package]]
name = "wasi"
version = "0.10.0+wasi-snapshot-preview999"
dependencies = [
 "wasi 0.11.0+wasi-snapshot-preview1",
]

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasm-bindgen"
version = "0.2.100"

[[package]]
name = "wasm-encoder"
version = "0.219.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29cbbd772edcb8e7d524a82ee8cef8dd046fc14033796a754c3ad246d019fa54"
dependencies = [
 "leb128",
 "wasmparser",
]

[[package]]
name = "wasm-smith"
version = "0.219.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b526e4c6eed409b619960258ba5bd8a3b44dfb30c75c12fce80b750a4487fcc"
dependencies = [
 "anyhow",
 "arbitrary",
 "flagset",
 "indexmap 2.2.6",
 "leb128",
 "wasm-encoder",
]

[[package]]
name = "wasmparser"
version = "0.219.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c771866898879073c53b565a6c7b49953795159836714ac56a5befb581227c5"
dependencies = [
 "bitflags 2.6.0",
 "indexmap 2.2.6",
]

[[package]]
name = "wast"
version = "219.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f79a9d9df79986a68689a6b40bcc8d5d40d807487b235bebc2ac69a242b54a1"
dependencies = [
 "bumpalo",
 "leb128",
 "memchr",
 "unicode-width",
 "wasm-encoder",
]

[[package]]
name = "web-sys"
version = "0.3.100"

[[package]]
name = "webdriver"
version = "0.51.0"
dependencies = [
 "base64 0.21.3",
 "bytes",
 "cookie",
 "http",
 "icu_segmenter",
 "log",
 "serde",
 "serde_derive",
 "serde_json",
 "thiserror",
 "time 0.3.36",
 "tokio",
 "tokio-stream",
 "url",
 "warp",
]

[[package]]
name = "webext-storage"
version = "0.1.0"
source = "git+https://github.com/mozilla/application-services?rev=03cf4a362408b9caffa6848aae2fcf472a789460#03cf4a362408b9caffa6848aae2fcf472a789460"
dependencies = [
 "anyhow",
 "error-support",
 "ffi-support",
 "interrupt-support",
 "lazy_static",
 "log",
 "parking_lot",
 "rusqlite",
 "serde",
 "serde_derive",
 "serde_json",
 "sql-support",
 "sync-guid",
 "sync15",
 "thiserror",
 "uniffi",
 "url",
]

[[package]]
name = "webrender"
version = "0.62.0"
dependencies = [
 "allocator-api2",
 "bincode",
 "bitflags 2.6.0",
 "build-parallel",
 "byteorder",
 "derive_more 0.99.999",
 "etagere",
 "euclid",
 "firefox-on-glean",
 "fxhash",
 "gleam",
 "glean",
 "glslopt",
 "lazy_static",
 "log",
 "malloc_size_of_derive",
 "num-traits",
 "peek-poke",
 "plane-split",
 "rayon",
 "ron",
 "serde",
 "smallvec",
 "svg_fmt",
 "swgl",
 "time 0.1.45",
 "topological-sort",
 "tracy-rs",
 "webrender_api",
 "webrender_build",
 "wr_glyph_rasterizer",
 "wr_malloc_size_of",
]

[[package]]
name = "webrender_api"
version = "0.62.0"
dependencies = [
 "app_units",
 "bitflags 2.6.0",
 "byteorder",
 "crossbeam-channel",
 "euclid",
 "malloc_size_of_derive",
 "peek-poke",
 "serde",
 "serde_bytes",
 "serde_derive",
 "time 0.1.45",
 "wr_malloc_size_of",
]

[[package]]
name = "webrender_bindings"
version = "0.1.0"
dependencies = [
 "app_units",
 "bincode",
 "core-foundation",
 "core-graphics",
 "dirs",
 "dwrote",
 "euclid",
 "foreign-types",
 "fxhash",
 "gecko-profiler",
 "gleam",
 "log",
 "nsstring",
 "num_cpus",
 "objc",
 "rayon",
 "remove_dir_all",
 "static_prefs",
 "swgl",
 "thin-vec",
 "tracy-rs",
 "uuid",
 "webrender",
 "winapi",
 "wr_malloc_size_of",
]

[[package]]
name = "webrender_build"
version = "0.0.2"
dependencies = [
 "bitflags 2.6.0",
 "lazy_static",
 "serde",
]

[[package]]
name = "webrtc-sdp"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a87d58624aae43577604ea137de9dcaf92793eccc4d816efad482001c2e055ca"
dependencies = [
 "log",
 "url",
]

[[package]]
name = "weedle2"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "998d2c24ec099a87daf9467808859f9d82b61f1d9c9701251aea037f514eae0e"
dependencies = [
 "nom",
]

[[package]]
name = "wgpu-core"
version = "23.0.1"
source = "git+https://github.com/gfx-rs/wgpu?rev=5543961a71cc8e5399b696fae3f6aae82c019717#5543961a71cc8e5399b696fae3f6aae82c019717"
dependencies = [
 "arrayvec",
 "bit-vec",
 "bitflags 2.6.0",
 "cfg_aliases 0.1.1",
 "document-features",
 "indexmap 2.2.6",
 "log",
 "naga",
 "once_cell",
 "parking_lot",
 "profiling",
 "ron",
 "rustc-hash",
 "serde",
 "smallvec",
 "thiserror",
 "wgpu-hal",
 "wgpu-types",
]

[[package]]
name = "wgpu-hal"
version = "23.0.1"
source = "git+https://github.com/gfx-rs/wgpu?rev=5543961a71cc8e5399b696fae3f6aae82c019717#5543961a71cc8e5399b696fae3f6aae82c019717"
dependencies = [
 "android_system_properties",
 "arrayvec",
 "ash",
 "bit-set",
 "bitflags 2.6.0",
 "block",
 "cfg_aliases 0.1.1",
 "core-graphics-types",
 "gpu-alloc",
 "gpu-allocator",
 "gpu-descriptor",
 "js-sys",
 "khronos-egl",
 "libc",
 "libloading",
 "log",
 "metal",
 "naga",
 "objc",
 "once_cell",
 "parking_lot",
 "profiling",
 "range-alloc",
 "raw-window-handle",
 "rustc-hash",
 "smallvec",
 "thiserror",
 "wasm-bindgen",
 "web-sys",
 "wgpu-types",
 "windows",
 "windows-core",
]

[[package]]
name = "wgpu-types"
version = "23.0.0"
source = "git+https://github.com/gfx-rs/wgpu?rev=5543961a71cc8e5399b696fae3f6aae82c019717#5543961a71cc8e5399b696fae3f6aae82c019717"
dependencies = [
 "bitflags 2.6.0",
 "js-sys",
 "serde",
 "web-sys",
]

[[package]]
name = "wgpu_bindings"
version = "0.1.0"
dependencies = [
 "arrayvec",
 "ash",
 "bincode",
 "io-surface",
 "log",
 "metal",
 "nsstring",
 "objc",
 "parking_lot",
 "serde",
 "static_prefs",
 "wgpu-core",
 "wgpu-hal",
 "wgpu-types",
 "windows",
]

[[package]]
name = "whatsys"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb632c0076024630111a08ca9fcbd34736c80d10b9ae517077487b0c82f46a36"
dependencies = [
 "cc",
 "cfg-if",
 "libc",
]

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178"
dependencies = [
 "winapi",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.58.0"
dependencies = [
 "mozbuild",
 "windows-core",
 "windows-targets",
]

[[package]]
name = "windows-core"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ba6d44ec8c2591c134257ce647b7ea6b20335bf6379a27dac5f1641fcf59f99"
dependencies = [
 "windows-implement",
 "windows-interface",
 "windows-result",
 "windows-strings",
 "windows-targets",
]

[[package]]
name = "windows-implement"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bbd5b46c938e506ecbce286b6628a02171d56153ba733b6c741fc627ec9579b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "windows-interface"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "053c4c462dc91d3b1504c6fe5a726dd15e216ba718e84a0e46a88fbe5ded3515"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "windows-result"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d1043d8214f791817bab27572aaa8af63732e11bf84aa21a45a78d6c317ae0e"
dependencies = [
 "windows-targets",
]

[[package]]
name = "windows-strings"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd9b125c486025df0eabcb585e62173c6c9eddcec5d117d3b6e8c30e2ee4d10"
dependencies = [
 "windows-result",
 "windows-targets",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets",
]

[[package]]
name = "windows-targets"
version = "0.52.999"
dependencies = [
 "quote",
 "syn",
]

[[package]]
name = "winreg"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80d0f4e272c85def139476380b12f9ac60926689dd2e01d4923222f40580869d"
dependencies = [
 "winapi",
]

[[package]]
name = "wio"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d129932f4644ac2396cb456385cbf9e63b5b30c6e8dc4820bdca4eb082037a5"
dependencies = [
 "winapi",
]

[[package]]
name = "wpf-gpu-raster"
version = "0.1.0"
source = "git+https://github.com/FirefoxGraphics/wpf-gpu-raster?rev=99979da091fd58fba8477e7fcdf5ec0727102916#99979da091fd58fba8477e7fcdf5ec0727102916"
dependencies = [
 "typed-arena-nomut",
]

[[package]]
name = "wr_glyph_rasterizer"
version = "0.1.0"
dependencies = [
 "core-foundation",
 "core-graphics",
 "core-text",
 "dwrote",
 "euclid",
 "firefox-on-glean",
 "freetype",
 "fxhash",
 "glean",
 "lazy_static",
 "libc",
 "log",
 "malloc_size_of_derive",
 "objc",
 "rayon",
 "serde",
 "smallvec",
 "tracy-rs",
 "webrender_api",
 "wr_malloc_size_of",
]

[[package]]
name = "wr_malloc_size_of"
version = "0.0.2"
dependencies = [
 "app_units",
 "euclid",
]

[[package]]
name = "write16"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1890f4022759daae28ed4fe62859b1236caebfc61ede2f63ed4e695f3f6d936"

[[package]]
name = "writeable"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9df38ee2d2c3c5948ea468a8406ff0db0b29ae1ffde1bcf20ef305bcc95c51"

[[package]]
name = "xml-rs"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2d7d3948613f75c98fd9328cfdcc45acc4d360655289d0a7d4ec931392200a3"

[[package]]
name = "xmldecl"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efeb408acbc94f7459f1a3ee3620c108ebea5e5baf93a4641c07d57f59f5ffd1"
dependencies = [
 "encoding_rs",
]

[[package]]
name = "xpcom"
version = "0.1.0"
dependencies = [
 "cstr",
 "libc",
 "mozbuild",
 "nserror",
 "nsstring",
 "thin-vec",
 "threadbound",
 "xpcom_macros",
]

[[package]]
name = "xpcom-gtest"
version = "0.1.0"
dependencies = [
 "nserror",
 "nsstring",
 "xpcom",
]

[[package]]
name = "xpcom_macros"
version = "0.1.0"
dependencies = [
 "lazy_static",
 "mozbuild",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "yaml-rust"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56c1936c4cc7a1c9ab21a1ebb602eb942ba868cbd44a99cb7cdc5892335e1c85"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "yoke"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c5b1314b079b0930c31e3af543d8ee1757b1951ae1e1565ec704403a7240ca5"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28cc31741b18cb6f1d5ff12f5b7523e3d6eb0852bbbad19d73905511d9849b95"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "zeitstempel"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeea3eb6a30ed24e374f59368d3917c5180a845fdd4ed6f1b2278811a9e826f8"
dependencies = [
 "cfg-if",
 "libc",
 "once_cell",
]

[[package]]
name = "zerocopy"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74d4d3961e53fa4c9a25a8637fc2bfaf2595b3d3ae34875568a5cf64787716be"
dependencies = [
 "byteorder",
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce1b18ccd8e73a9321186f97e46f9f04b778851177567b1975109d26a08d2a6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "zerofrom"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91ec111ce797d0e0784a1116d0ddcdbea84322cd79e5d5ad173daeba4f93ab55"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6a647510471d372f2e6c2e6b7219e44d8c574d24fdc11c610a61455782f18c3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "serde",
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "zerovec"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2b893d79df23bfb12d5461018d408ea19dfafe76c2c7ef6d4eba614f8ff079"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eafa6dfb17584ea3e2bd6e76e0cc15ad7af12b09abdd1ca55961bed9b1063c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "zip"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "775a2b471036342aa69bc5a602bc889cb0a06cda00477d0c69566757d5553d39"
dependencies = [
 "arbitrary",
 "crc32fast",
 "crossbeam-utils",
 "displaydoc",
 "flate2",
 "indexmap 2.2.6",
 "memchr",
 "thiserror",
]

[[package]]
name = "zlib-rs"
version = "0.2.1"
source = "git+https://github.com/memorysafety/zlib-rs?rev=4aa430ccb77537d0d60dab8db993ca51bb1194c5#4aa430ccb77537d0d60dab8db993ca51bb1194c5"
