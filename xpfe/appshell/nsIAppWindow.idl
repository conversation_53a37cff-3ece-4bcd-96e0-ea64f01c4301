/* -*- Mode: IDL; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 2 -*-
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

/**
 * The nsIAppWindow
 *
 * When the window is destroyed, it will fire a "xul-window-destroyed"
 * notification through the global observer service.
 */

%{C++
#include "LiveResizeListener.h"
#include "nsTArray.h"
%}

interface nsIDocShell;
interface nsIDocShellTreeItem;
interface nsIXULBrowserWindow;
interface nsIRemoteTab;
interface mozIDOMWindowProxy;
interface nsIOpenWindowInfo;
webidl BrowsingContext;

native LiveResizeListenerArray(nsTArray<RefPtr<mozilla::LiveResizeListener>>);

[builtinclass, scriptable, uuid(d6d7a014-e28d-4c9d-8727-1cf6d870619b)]
interface nsIAppWindow : nsISupports
{
  /**
   * The docshell owning the XUL for this window.
   */
  readonly attribute nsIDocShell docShell;

  /**
   * Indicates if this window is instrinsically sized.
   */
  attribute boolean intrinsicallySized;

  /**
   * The primary content shell.
   *
   * Note that this is a docshell tree item and therefore can not be assured of
   * what object it is. It could be an editor, a docshell, or a browser object.
   * Or down the road any other object that supports being a DocShellTreeItem
   * Query accordingly to determine the capabilities.
   */
  readonly attribute nsIDocShellTreeItem primaryContentShell;

  /**
   * In multiprocess case we may not have primaryContentShell but
   * primaryRemoteTab.
   */
  readonly attribute nsIRemoteTab primaryRemoteTab;

  /**
   * Helper for getting the BrowsingContext from either `primaryContentShell` or
   * `primaryRemoteTab` depending on which is available.
   */
  readonly attribute BrowsingContext primaryContentBrowsingContext;

  void remoteTabAdded(in nsIRemoteTab aTab, in boolean aPrimary);
  void remoteTabRemoved(in nsIRemoteTab aTab);

  [noscript,notxpcom] LiveResizeListenerArray getLiveResizeListeners();

  /**
   * Returns the difference between the inner window size (client size) and the
   * outer window size, in CSS pixels.
   */
  [infallible] readonly attribute unsigned long outerToInnerHeightDifferenceInCSSPixels;
  [infallible] readonly attribute unsigned long outerToInnerWidthDifferenceInCSSPixels;

  /**
   * Move the window to a centered position.
   * @param aRelative If not null, the window relative to which the window is
   *                  moved. See aScreen parameter for details.
   * @param aScreen   PR_TRUE to center the window relative to the screen
   *                  containing aRelative if aRelative is not null. If
   *                  aRelative is null then relative to the screen of the
   *                  opener window if it was initialized by passing it to
   *                  nsWebShellWindow::Initialize. Failing that relative to
   *                  the main screen.
   *                  PR_FALSE to center it relative to aRelative itself.
   * @param aAlert    PR_TRUE to move the window to an alert position,
   *                  generally centered horizontally and 1/3 down from the top.
   */
  void center(in nsIAppWindow aRelative, in boolean aScreen, in boolean aAlert);

  /**
   * Shows the window as a modal window. That is, ensures that it is visible
   * and runs a local event loop, exiting only once the window has been closed.
   */
  void showModal();

  /**
   * Locks the aspect ratio for a window.
   * @param aShouldLock boolean
   */
  void lockAspectRatio(in boolean aShouldLock);

  attribute uint32_t chromeFlags;

  /**
   * Begin assuming |chromeFlags| don't change hereafter, and assert
   * if they do change.  The state change is one-way and idempotent.
   */
  void assumeChromeFlagsAreFrozen();

  /**
   * Create a new window.
   * @param aChromeFlags see nsIWebBrowserChrome
   * @param aOpenWindowInfo information about the request for a content window
   *                        to be opened. Will be null for non-content loads.
   * @return the newly minted window
   */
  nsIAppWindow createNewWindow(in int32_t aChromeFlags,
                               in nsIOpenWindowInfo aOpenWindowInfo);

  attribute nsIXULBrowserWindow XULBrowserWindow;

  /**
   * Back-door method to make sure some stuff is done when the document is
   * ready for layout, that would cause expensive computation otherwise later.
   *
   * Do NOT call this unless you know what you're doing!  In particular,
   * calling this when this XUL window doesn't yet have a document in its
   * docshell could cause problems.
   */
  [noscript] void beforeStartLayout();

  /**
   * If the window was opened as a content window, this will return the initial
   * nsIOpenWindowInfo to use.
   */
  readonly attribute nsIOpenWindowInfo initialOpenWindowInfo;

  /**
   * Request fast snapshot at RenderCompositor of WebRender.
   * Since readback of Windows DirectComposition is very slow.
   */
  void needFastSnaphot();

  /**
   * Ask the PopupManager to rollup all popups.
   * Can be used by popup-like elements to close other popups when shown.
   * Note that tooltips and noautohide popups won't be closed.
   */
  void rollupAllPopups();
};
