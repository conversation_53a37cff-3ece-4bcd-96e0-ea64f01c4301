# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'js_name': 'appShell',
        'cid': '{0099907d-123c-4853-a46a-43098b5fb68c}',
        'contract_ids': ['@mozilla.org/appshell/appShellService;1'],
        'interfaces': ['nsIAppShellService'],
        'type': 'nsAppShellService',
        'headers': ['/xpfe/appshell/nsAppShellService.h'],
    },
    {
        'js_name': 'wm',
        'cid': '{79a2b7cc-f05b-4605-bfa0-fac54f27eec8}',
        'contract_ids': ['@mozilla.org/appshell/window-mediator;1'],
        'interfaces': ['nsIWindowMediator'],
        'type': 'nsWindowMediator',
        'headers': ['/xpfe/appshell/nsWindowMediator.h'],
        'init_method': 'Init',
    },
]
