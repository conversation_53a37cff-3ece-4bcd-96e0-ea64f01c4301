<?xml version="1.0"?>
<?xml-stylesheet href="chrome://global/skin" type="text/css"?>
<?xml-stylesheet href="chrome://mochikit/content/tests/SimpleTest/test.css"
                 type="text/css"?>
<!--
https://bugzilla.mozilla.org/show_bug.cgi?id=815847
-->
<window title="Mozilla Bug 815847"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js" />

<body  xmlns="http://www.w3.org/1999/xhtml">
<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1214174">Mozilla Bug 1214174</a>
<p id="display"></p>
<div id="content" style="display: none">

</div>
<pre id="test">
</pre>
</body>

<script class="testbody" type="application/javascript">
<![CDATA[

function testWindowlessBrowser(chromePrivileged) {
  var webNav = Services.appShell.createWindowlessBrowser(chromePrivileged);

  ok(webNav, "createWindowlessBrowser should return a wevNav");

  let docShell = webNav.docShell;

  ok(docShell, "docShell should be defined");
  ok(docShell.docViewer.DOMDocument.defaultView, "docShell defaultView should be defined");

  var win = docShell.docViewer.DOMDocument.defaultView;

  ok(win.screenX == 0, "window.screenX should be 0 in a windowless browser");
  ok(win.screenY == 0, "window.screenY should be 0 in a windowless browser");
  ok(win.outerWidth == 0, "window.outerWidth should be 0 in a windowless browser");
  ok(win.outerHeight == 0, "window.outerHeight should be 0 in a windowless browser");

  ok(win.external, "window.external should be defined");

  var exception;

  try {
    win.external.AddSearchProvider("http://test-fake.url");
  } catch(e) {
    exception = e;
  }

  ok(!exception, "window.external.AddSearchProvider should be ignore withour raising an exception");

  webNav.close();
}

info("Test Bug 1214174 on a content privileged windowless browser");
testWindowlessBrowser(false);

info("Test Bug 1214174 on a chrome privileged windowless browser");
testWindowlessBrowser(true);

]]>
</script>

</window>
