/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

// clang-format off

#ifndef GLCONSTS_H_
#define GLCONSTS_H_

/**
 * GENERATED FILE, DO NOT MODIFY DIRECTLY.
 * This is a file generated directly from the official OpenGL registry
 * xml available http://www.opengl.org/registry/#specfiles.
 *
 * To generate this file, see tutorial in 'GLConsts.py'.
 */

// GL
#define LOCAL_GL_1PASS_EXT                                   0x80A1
#define LOCAL_GL_1PASS_SGIS                                  0x80A1
#define LOCAL_GL_2D                                          0x0600
#define LOCAL_GL_2PASS_0_EXT                                 0x80A2
#define LOCAL_GL_2PASS_0_SGIS                                0x80A2
#define LOCAL_GL_2PASS_1_EXT                                 0x80A3
#define LOCAL_GL_2PASS_1_SGIS                                0x80A3
#define LOCAL_GL_2X_BIT_ATI                                  0x00000001
#define LOCAL_GL_2_BYTES                                     0x1407
#define LOCAL_GL_2_BYTES_NV                                  0x1407
#define LOCAL_GL_3D                                          0x0601
#define LOCAL_GL_3DC_XY_AMD                                  0x87FA
#define LOCAL_GL_3DC_X_AMD                                   0x87F9
#define LOCAL_GL_3D_COLOR                                    0x0602
#define LOCAL_GL_3D_COLOR_TEXTURE                            0x0603
#define LOCAL_GL_3_BYTES                                     0x1408
#define LOCAL_GL_3_BYTES_NV                                  0x1408
#define LOCAL_GL_422_AVERAGE_EXT                             0x80CE
#define LOCAL_GL_422_EXT                                     0x80CC
#define LOCAL_GL_422_REV_AVERAGE_EXT                         0x80CF
#define LOCAL_GL_422_REV_EXT                                 0x80CD
#define LOCAL_GL_4D_COLOR_TEXTURE                            0x0604
#define LOCAL_GL_4PASS_0_EXT                                 0x80A4
#define LOCAL_GL_4PASS_0_SGIS                                0x80A4
#define LOCAL_GL_4PASS_1_EXT                                 0x80A5
#define LOCAL_GL_4PASS_1_SGIS                                0x80A5
#define LOCAL_GL_4PASS_2_EXT                                 0x80A6
#define LOCAL_GL_4PASS_2_SGIS                                0x80A6
#define LOCAL_GL_4PASS_3_EXT                                 0x80A7
#define LOCAL_GL_4PASS_3_SGIS                                0x80A7
#define LOCAL_GL_4X_BIT_ATI                                  0x00000002
#define LOCAL_GL_4_BYTES                                     0x1409
#define LOCAL_GL_4_BYTES_NV                                  0x1409
#define LOCAL_GL_8X_BIT_ATI                                  0x00000004
#define LOCAL_GL_ABGR_EXT                                    0x8000
#define LOCAL_GL_ACCUM                                       0x0100
#define LOCAL_GL_ACCUM_ADJACENT_PAIRS_NV                     0x90AD
#define LOCAL_GL_ACCUM_ALPHA_BITS                            0x0D5B
#define LOCAL_GL_ACCUM_BLUE_BITS                             0x0D5A
#define LOCAL_GL_ACCUM_BUFFER_BIT                            0x00000200
#define LOCAL_GL_ACCUM_CLEAR_VALUE                           0x0B80
#define LOCAL_GL_ACCUM_GREEN_BITS                            0x0D59
#define LOCAL_GL_ACCUM_RED_BITS                              0x0D58
#define LOCAL_GL_ACTIVE_ATOMIC_COUNTER_BUFFERS               0x92D9
#define LOCAL_GL_ACTIVE_ATTRIBUTES                           0x8B89
#define LOCAL_GL_ACTIVE_ATTRIBUTE_MAX_LENGTH                 0x8B8A
#define LOCAL_GL_ACTIVE_PROGRAM                              0x8259
#define LOCAL_GL_ACTIVE_PROGRAM_EXT                          0x8B8D
#define LOCAL_GL_ACTIVE_RESOURCES                            0x92F5
#define LOCAL_GL_ACTIVE_STENCIL_FACE_EXT                     0x8911
#define LOCAL_GL_ACTIVE_SUBROUTINES                          0x8DE5
#define LOCAL_GL_ACTIVE_SUBROUTINE_MAX_LENGTH                0x8E48
#define LOCAL_GL_ACTIVE_SUBROUTINE_UNIFORMS                  0x8DE6
#define LOCAL_GL_ACTIVE_SUBROUTINE_UNIFORM_LOCATIONS         0x8E47
#define LOCAL_GL_ACTIVE_SUBROUTINE_UNIFORM_MAX_LENGTH        0x8E49
#define LOCAL_GL_ACTIVE_TEXTURE                              0x84E0
#define LOCAL_GL_ACTIVE_TEXTURE_ARB                          0x84E0
#define LOCAL_GL_ACTIVE_UNIFORMS                             0x8B86
#define LOCAL_GL_ACTIVE_UNIFORM_BLOCKS                       0x8A36
#define LOCAL_GL_ACTIVE_UNIFORM_BLOCK_MAX_NAME_LENGTH        0x8A35
#define LOCAL_GL_ACTIVE_UNIFORM_MAX_LENGTH                   0x8B87
#define LOCAL_GL_ACTIVE_VARIABLES                            0x9305
#define LOCAL_GL_ACTIVE_VARYINGS_NV                          0x8C81
#define LOCAL_GL_ACTIVE_VARYING_MAX_LENGTH_NV                0x8C82
#define LOCAL_GL_ACTIVE_VERTEX_UNITS_ARB                     0x86A5
#define LOCAL_GL_ADD                                         0x0104
#define LOCAL_GL_ADD_ATI                                     0x8963
#define LOCAL_GL_ADD_BLEND_IMG                               0x8C09
#define LOCAL_GL_ADD_SIGNED                                  0x8574
#define LOCAL_GL_ADD_SIGNED_ARB                              0x8574
#define LOCAL_GL_ADD_SIGNED_EXT                              0x8574
#define LOCAL_GL_ADJACENT_PAIRS_NV                           0x90AE
#define LOCAL_GL_AFFINE_2D_NV                                0x9092
#define LOCAL_GL_AFFINE_3D_NV                                0x9094
#define LOCAL_GL_ALIASED_LINE_WIDTH_RANGE                    0x846E
#define LOCAL_GL_ALIASED_POINT_SIZE_RANGE                    0x846D
#define LOCAL_GL_ALLOW_DRAW_FRG_HINT_PGI                     0x1A210
#define LOCAL_GL_ALLOW_DRAW_MEM_HINT_PGI                     0x1A211
#define LOCAL_GL_ALLOW_DRAW_OBJ_HINT_PGI                     0x1A20E
#define LOCAL_GL_ALLOW_DRAW_WIN_HINT_PGI                     0x1A20F
#define LOCAL_GL_ALL_ATTRIB_BITS                             0xFFFFFFFF
#define LOCAL_GL_ALL_BARRIER_BITS                            0xFFFFFFFF
#define LOCAL_GL_ALL_BARRIER_BITS_EXT                        0xFFFFFFFF
#define LOCAL_GL_ALL_COMPLETED_NV                            0x84F2
#define LOCAL_GL_ALL_PIXELS_AMD                              0xFFFFFFFF
#define LOCAL_GL_ALL_SHADER_BITS                             0xFFFFFFFF
#define LOCAL_GL_ALL_SHADER_BITS_EXT                         0xFFFFFFFF
#define LOCAL_GL_ALL_STATIC_DATA_IBM                         103060
#define LOCAL_GL_ALPHA                                       0x1906
#define LOCAL_GL_ALPHA12                                     0x803D
#define LOCAL_GL_ALPHA12_EXT                                 0x803D
#define LOCAL_GL_ALPHA16                                     0x803E
#define LOCAL_GL_ALPHA16F_ARB                                0x881C
#define LOCAL_GL_ALPHA16F_EXT                                0x881C
#define LOCAL_GL_ALPHA16I_EXT                                0x8D8A
#define LOCAL_GL_ALPHA16UI_EXT                               0x8D78
#define LOCAL_GL_ALPHA16_EXT                                 0x803E
#define LOCAL_GL_ALPHA16_SNORM                               0x9018
#define LOCAL_GL_ALPHA32F_ARB                                0x8816
#define LOCAL_GL_ALPHA32F_EXT                                0x8816
#define LOCAL_GL_ALPHA32I_EXT                                0x8D84
#define LOCAL_GL_ALPHA32UI_EXT                               0x8D72
#define LOCAL_GL_ALPHA4                                      0x803B
#define LOCAL_GL_ALPHA4_EXT                                  0x803B
#define LOCAL_GL_ALPHA8                                      0x803C
#define LOCAL_GL_ALPHA8I_EXT                                 0x8D90
#define LOCAL_GL_ALPHA8UI_EXT                                0x8D7E
#define LOCAL_GL_ALPHA8_EXT                                  0x803C
#define LOCAL_GL_ALPHA8_OES                                  0x803C
#define LOCAL_GL_ALPHA8_SNORM                                0x9014
#define LOCAL_GL_ALPHA_BIAS                                  0x0D1D
#define LOCAL_GL_ALPHA_BITS                                  0x0D55
#define LOCAL_GL_ALPHA_FLOAT16_APPLE                         0x881C
#define LOCAL_GL_ALPHA_FLOAT16_ATI                           0x881C
#define LOCAL_GL_ALPHA_FLOAT32_APPLE                         0x8816
#define LOCAL_GL_ALPHA_FLOAT32_ATI                           0x8816
#define LOCAL_GL_ALPHA_INTEGER                               0x8D97
#define LOCAL_GL_ALPHA_INTEGER_EXT                           0x8D97
#define LOCAL_GL_ALPHA_MAX_CLAMP_INGR                        0x8567
#define LOCAL_GL_ALPHA_MAX_SGIX                              0x8321
#define LOCAL_GL_ALPHA_MIN_CLAMP_INGR                        0x8563
#define LOCAL_GL_ALPHA_MIN_SGIX                              0x8320
#define LOCAL_GL_ALPHA_REF_COMMAND_NV                        0x000F
#define LOCAL_GL_ALPHA_SCALE                                 0x0D1C
#define LOCAL_GL_ALPHA_SNORM                                 0x9010
#define LOCAL_GL_ALPHA_TEST                                  0x0BC0
#define LOCAL_GL_ALPHA_TEST_FUNC                             0x0BC1
#define LOCAL_GL_ALPHA_TEST_FUNC_QCOM                        0x0BC1
#define LOCAL_GL_ALPHA_TEST_QCOM                             0x0BC0
#define LOCAL_GL_ALPHA_TEST_REF                              0x0BC2
#define LOCAL_GL_ALPHA_TEST_REF_QCOM                         0x0BC2
#define LOCAL_GL_ALPHA_TO_COVERAGE_DITHER_DEFAULT_NV         0x934D
#define LOCAL_GL_ALPHA_TO_COVERAGE_DITHER_DISABLE_NV         0x934F
#define LOCAL_GL_ALPHA_TO_COVERAGE_DITHER_ENABLE_NV          0x934E
#define LOCAL_GL_ALPHA_TO_COVERAGE_DITHER_MODE_NV            0x92BF
#define LOCAL_GL_ALREADY_SIGNALED                            0x911A
#define LOCAL_GL_ALREADY_SIGNALED_APPLE                      0x911A
#define LOCAL_GL_ALWAYS                                      0x0207
#define LOCAL_GL_ALWAYS_FAST_HINT_PGI                        0x1A20C
#define LOCAL_GL_ALWAYS_SOFT_HINT_PGI                        0x1A20D
#define LOCAL_GL_AMBIENT                                     0x1200
#define LOCAL_GL_AMBIENT_AND_DIFFUSE                         0x1602
#define LOCAL_GL_AND                                         0x1501
#define LOCAL_GL_AND_INVERTED                                0x1504
#define LOCAL_GL_AND_REVERSE                                 0x1502
#define LOCAL_GL_ANY_SAMPLES_PASSED                          0x8C2F
#define LOCAL_GL_ANY_SAMPLES_PASSED_CONSERVATIVE             0x8D6A
#define LOCAL_GL_ANY_SAMPLES_PASSED_CONSERVATIVE_EXT         0x8D6A
#define LOCAL_GL_ANY_SAMPLES_PASSED_EXT                      0x8C2F
#define LOCAL_GL_ARC_TO_NV                                   0xFE
#define LOCAL_GL_ARRAY_BUFFER                                0x8892
#define LOCAL_GL_ARRAY_BUFFER_ARB                            0x8892
#define LOCAL_GL_ARRAY_BUFFER_BINDING                        0x8894
#define LOCAL_GL_ARRAY_BUFFER_BINDING_ARB                    0x8894
#define LOCAL_GL_ARRAY_ELEMENT_LOCK_COUNT_EXT                0x81A9
#define LOCAL_GL_ARRAY_ELEMENT_LOCK_FIRST_EXT                0x81A8
#define LOCAL_GL_ARRAY_OBJECT_BUFFER_ATI                     0x8766
#define LOCAL_GL_ARRAY_OBJECT_OFFSET_ATI                     0x8767
#define LOCAL_GL_ARRAY_SIZE                                  0x92FB
#define LOCAL_GL_ARRAY_STRIDE                                0x92FE
#define LOCAL_GL_ASYNC_DRAW_PIXELS_SGIX                      0x835D
#define LOCAL_GL_ASYNC_HISTOGRAM_SGIX                        0x832C
#define LOCAL_GL_ASYNC_MARKER_SGIX                           0x8329
#define LOCAL_GL_ASYNC_READ_PIXELS_SGIX                      0x835E
#define LOCAL_GL_ASYNC_TEX_IMAGE_SGIX                        0x835C
#define LOCAL_GL_ATC_RGBA_EXPLICIT_ALPHA_AMD                 0x8C93
#define LOCAL_GL_ATC_RGBA_INTERPOLATED_ALPHA_AMD             0x87EE
#define LOCAL_GL_ATC_RGB_AMD                                 0x8C92
#define LOCAL_GL_ATOMIC_COUNTER_BARRIER_BIT                  0x00001000
#define LOCAL_GL_ATOMIC_COUNTER_BARRIER_BIT_EXT              0x00001000
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER                       0x92C0
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTERS        0x92C5
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTER_INDICES 0x92C6
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_BINDING               0x92C1
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_DATA_SIZE             0x92C4
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_INDEX                 0x9301
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_COMPUTE_SHADER  0x90ED
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_FRAGMENT_SHADER 0x92CB
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_GEOMETRY_SHADER 0x92CA
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_MESH_SHADER_NV  0x959E
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TASK_SHADER_NV  0x959F
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_CONTROL_SHADER     0x92C8
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_EVALUATION_SHADER  0x92C9
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_VERTEX_SHADER   0x92C7
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_SIZE                  0x92C3
#define LOCAL_GL_ATOMIC_COUNTER_BUFFER_START                 0x92C2
#define LOCAL_GL_ATTACHED_MEMORY_OBJECT_NV                   0x95A4
#define LOCAL_GL_ATTACHED_MEMORY_OFFSET_NV                   0x95A5
#define LOCAL_GL_ATTACHED_SHADERS                            0x8B85
#define LOCAL_GL_ATTENUATION_EXT                             0x834D
#define LOCAL_GL_ATTRIBUTE_ADDRESS_COMMAND_NV                0x0009
#define LOCAL_GL_ATTRIB_ARRAY_POINTER_NV                     0x8645
#define LOCAL_GL_ATTRIB_ARRAY_SIZE_NV                        0x8623
#define LOCAL_GL_ATTRIB_ARRAY_STRIDE_NV                      0x8624
#define LOCAL_GL_ATTRIB_ARRAY_TYPE_NV                        0x8625
#define LOCAL_GL_ATTRIB_STACK_DEPTH                          0x0BB0
#define LOCAL_GL_AUTO_GENERATE_MIPMAP                        0x8295
#define LOCAL_GL_AUTO_NORMAL                                 0x0D80
#define LOCAL_GL_AUX0                                        0x0409
#define LOCAL_GL_AUX1                                        0x040A
#define LOCAL_GL_AUX2                                        0x040B
#define LOCAL_GL_AUX3                                        0x040C
#define LOCAL_GL_AUX_BUFFERS                                 0x0C00
#define LOCAL_GL_AUX_DEPTH_STENCIL_APPLE                     0x8A14
#define LOCAL_GL_AVERAGE_EXT                                 0x8335
#define LOCAL_GL_AVERAGE_HP                                  0x8160
#define LOCAL_GL_BACK                                        0x0405
#define LOCAL_GL_BACK_LEFT                                   0x0402
#define LOCAL_GL_BACK_NORMALS_HINT_PGI                       0x1A223
#define LOCAL_GL_BACK_PRIMARY_COLOR_NV                       0x8C77
#define LOCAL_GL_BACK_RIGHT                                  0x0403
#define LOCAL_GL_BACK_SECONDARY_COLOR_NV                     0x8C78
#define LOCAL_GL_BEVEL_NV                                    0x90A6
#define LOCAL_GL_BGR                                         0x80E0
#define LOCAL_GL_BGRA                                        0x80E1
#define LOCAL_GL_BGRA8_EXT                                   0x93A1
#define LOCAL_GL_BGRA_EXT                                    0x80E1
#define LOCAL_GL_BGRA_IMG                                    0x80E1
#define LOCAL_GL_BGRA_INTEGER                                0x8D9B
#define LOCAL_GL_BGRA_INTEGER_EXT                            0x8D9B
#define LOCAL_GL_BGR_EXT                                     0x80E0
#define LOCAL_GL_BGR_INTEGER                                 0x8D9A
#define LOCAL_GL_BGR_INTEGER_EXT                             0x8D9A
#define LOCAL_GL_BIAS_BIT_ATI                                0x00000008
#define LOCAL_GL_BIAS_BY_NEGATIVE_ONE_HALF_NV                0x8541
#define LOCAL_GL_BINNING_CONTROL_HINT_QCOM                   0x8FB0
#define LOCAL_GL_BINORMAL_ARRAY_EXT                          0x843A
#define LOCAL_GL_BINORMAL_ARRAY_POINTER_EXT                  0x8443
#define LOCAL_GL_BINORMAL_ARRAY_STRIDE_EXT                   0x8441
#define LOCAL_GL_BINORMAL_ARRAY_TYPE_EXT                     0x8440
#define LOCAL_GL_BITMAP                                      0x1A00
#define LOCAL_GL_BITMAP_TOKEN                                0x0704
#define LOCAL_GL_BLACKHOLE_RENDER_INTEL                      0x83FC
#define LOCAL_GL_BLEND                                       0x0BE2
#define LOCAL_GL_BLEND_ADVANCED_COHERENT_KHR                 0x9285
#define LOCAL_GL_BLEND_ADVANCED_COHERENT_NV                  0x9285
#define LOCAL_GL_BLEND_COLOR                                 0x8005
#define LOCAL_GL_BLEND_COLOR_COMMAND_NV                      0x000B
#define LOCAL_GL_BLEND_COLOR_EXT                             0x8005
#define LOCAL_GL_BLEND_DST                                   0x0BE0
#define LOCAL_GL_BLEND_DST_ALPHA                             0x80CA
#define LOCAL_GL_BLEND_DST_ALPHA_EXT                         0x80CA
#define LOCAL_GL_BLEND_DST_ALPHA_OES                         0x80CA
#define LOCAL_GL_BLEND_DST_RGB                               0x80C8
#define LOCAL_GL_BLEND_DST_RGB_EXT                           0x80C8
#define LOCAL_GL_BLEND_DST_RGB_OES                           0x80C8
#define LOCAL_GL_BLEND_EQUATION                              0x8009
#define LOCAL_GL_BLEND_EQUATION_ALPHA                        0x883D
#define LOCAL_GL_BLEND_EQUATION_ALPHA_EXT                    0x883D
#define LOCAL_GL_BLEND_EQUATION_ALPHA_OES                    0x883D
#define LOCAL_GL_BLEND_EQUATION_EXT                          0x8009
#define LOCAL_GL_BLEND_EQUATION_OES                          0x8009
#define LOCAL_GL_BLEND_EQUATION_RGB                          0x8009
#define LOCAL_GL_BLEND_EQUATION_RGB_EXT                      0x8009
#define LOCAL_GL_BLEND_EQUATION_RGB_OES                      0x8009
#define LOCAL_GL_BLEND_OVERLAP_NV                            0x9281
#define LOCAL_GL_BLEND_PREMULTIPLIED_SRC_NV                  0x9280
#define LOCAL_GL_BLEND_SRC                                   0x0BE1
#define LOCAL_GL_BLEND_SRC_ALPHA                             0x80CB
#define LOCAL_GL_BLEND_SRC_ALPHA_EXT                         0x80CB
#define LOCAL_GL_BLEND_SRC_ALPHA_OES                         0x80CB
#define LOCAL_GL_BLEND_SRC_RGB                               0x80C9
#define LOCAL_GL_BLEND_SRC_RGB_EXT                           0x80C9
#define LOCAL_GL_BLEND_SRC_RGB_OES                           0x80C9
#define LOCAL_GL_BLOCK_INDEX                                 0x92FD
#define LOCAL_GL_BLUE                                        0x1905
#define LOCAL_GL_BLUE_BIAS                                   0x0D1B
#define LOCAL_GL_BLUE_BITS                                   0x0D54
#define LOCAL_GL_BLUE_BIT_ATI                                0x00000004
#define LOCAL_GL_BLUE_INTEGER                                0x8D96
#define LOCAL_GL_BLUE_INTEGER_EXT                            0x8D96
#define LOCAL_GL_BLUE_MAX_CLAMP_INGR                         0x8566
#define LOCAL_GL_BLUE_MIN_CLAMP_INGR                         0x8562
#define LOCAL_GL_BLUE_NV                                     0x1905
#define LOCAL_GL_BLUE_SCALE                                  0x0D1A
#define LOCAL_GL_BOLD_BIT_NV                                 0x01
#define LOCAL_GL_BOOL                                        0x8B56
#define LOCAL_GL_BOOL_ARB                                    0x8B56
#define LOCAL_GL_BOOL_VEC2                                   0x8B57
#define LOCAL_GL_BOOL_VEC2_ARB                               0x8B57
#define LOCAL_GL_BOOL_VEC3                                   0x8B58
#define LOCAL_GL_BOOL_VEC3_ARB                               0x8B58
#define LOCAL_GL_BOOL_VEC4                                   0x8B59
#define LOCAL_GL_BOOL_VEC4_ARB                               0x8B59
#define LOCAL_GL_BOUNDING_BOX_NV                             0x908D
#define LOCAL_GL_BOUNDING_BOX_OF_BOUNDING_BOXES_NV           0x909C
#define LOCAL_GL_BROWSER_DEFAULT_WEBGL                       0x9244
#define LOCAL_GL_BUFFER                                      0x82E0
#define LOCAL_GL_BUFFER_ACCESS                               0x88BB
#define LOCAL_GL_BUFFER_ACCESS_ARB                           0x88BB
#define LOCAL_GL_BUFFER_ACCESS_FLAGS                         0x911F
#define LOCAL_GL_BUFFER_ACCESS_OES                           0x88BB
#define LOCAL_GL_BUFFER_BINDING                              0x9302
#define LOCAL_GL_BUFFER_DATA_SIZE                            0x9303
#define LOCAL_GL_BUFFER_FLUSHING_UNMAP_APPLE                 0x8A13
#define LOCAL_GL_BUFFER_GPU_ADDRESS_NV                       0x8F1D
#define LOCAL_GL_BUFFER_IMMUTABLE_STORAGE                    0x821F
#define LOCAL_GL_BUFFER_IMMUTABLE_STORAGE_EXT                0x821F
#define LOCAL_GL_BUFFER_KHR                                  0x82E0
#define LOCAL_GL_BUFFER_MAPPED                               0x88BC
#define LOCAL_GL_BUFFER_MAPPED_ARB                           0x88BC
#define LOCAL_GL_BUFFER_MAPPED_OES                           0x88BC
#define LOCAL_GL_BUFFER_MAP_LENGTH                           0x9120
#define LOCAL_GL_BUFFER_MAP_OFFSET                           0x9121
#define LOCAL_GL_BUFFER_MAP_POINTER                          0x88BD
#define LOCAL_GL_BUFFER_MAP_POINTER_ARB                      0x88BD
#define LOCAL_GL_BUFFER_MAP_POINTER_OES                      0x88BD
#define LOCAL_GL_BUFFER_OBJECT_APPLE                         0x85B3
#define LOCAL_GL_BUFFER_OBJECT_EXT                           0x9151
#define LOCAL_GL_BUFFER_SERIALIZED_MODIFY_APPLE              0x8A12
#define LOCAL_GL_BUFFER_SIZE                                 0x8764
#define LOCAL_GL_BUFFER_SIZE_ARB                             0x8764
#define LOCAL_GL_BUFFER_STORAGE_FLAGS                        0x8220
#define LOCAL_GL_BUFFER_STORAGE_FLAGS_EXT                    0x8220
#define LOCAL_GL_BUFFER_UPDATE_BARRIER_BIT                   0x00000200
#define LOCAL_GL_BUFFER_UPDATE_BARRIER_BIT_EXT               0x00000200
#define LOCAL_GL_BUFFER_USAGE                                0x8765
#define LOCAL_GL_BUFFER_USAGE_ARB                            0x8765
#define LOCAL_GL_BUFFER_VARIABLE                             0x92E5
#define LOCAL_GL_BUMP_ENVMAP_ATI                             0x877B
#define LOCAL_GL_BUMP_NUM_TEX_UNITS_ATI                      0x8777
#define LOCAL_GL_BUMP_ROT_MATRIX_ATI                         0x8775
#define LOCAL_GL_BUMP_ROT_MATRIX_SIZE_ATI                    0x8776
#define LOCAL_GL_BUMP_TARGET_ATI                             0x877C
#define LOCAL_GL_BUMP_TEX_UNITS_ATI                          0x8778
#define LOCAL_GL_BYTE                                        0x1400
#define LOCAL_GL_C3F_V3F                                     0x2A24
#define LOCAL_GL_C4F_N3F_V3F                                 0x2A26
#define LOCAL_GL_C4UB_V2F                                    0x2A22
#define LOCAL_GL_C4UB_V3F                                    0x2A23
#define LOCAL_GL_CALLIGRAPHIC_FRAGMENT_SGIX                  0x8183
#define LOCAL_GL_CAVEAT_SUPPORT                              0x82B8
#define LOCAL_GL_CCW                                         0x0901
#define LOCAL_GL_CIRCULAR_CCW_ARC_TO_NV                      0xF8
#define LOCAL_GL_CIRCULAR_CW_ARC_TO_NV                       0xFA
#define LOCAL_GL_CIRCULAR_TANGENT_ARC_TO_NV                  0xFC
#define LOCAL_GL_CLAMP                                       0x2900
#define LOCAL_GL_CLAMP_FRAGMENT_COLOR                        0x891B
#define LOCAL_GL_CLAMP_FRAGMENT_COLOR_ARB                    0x891B
#define LOCAL_GL_CLAMP_READ_COLOR                            0x891C
#define LOCAL_GL_CLAMP_READ_COLOR_ARB                        0x891C
#define LOCAL_GL_CLAMP_TO_BORDER                             0x812D
#define LOCAL_GL_CLAMP_TO_BORDER_ARB                         0x812D
#define LOCAL_GL_CLAMP_TO_BORDER_EXT                         0x812D
#define LOCAL_GL_CLAMP_TO_BORDER_NV                          0x812D
#define LOCAL_GL_CLAMP_TO_BORDER_OES                         0x812D
#define LOCAL_GL_CLAMP_TO_BORDER_SGIS                        0x812D
#define LOCAL_GL_CLAMP_TO_EDGE                               0x812F
#define LOCAL_GL_CLAMP_TO_EDGE_SGIS                          0x812F
#define LOCAL_GL_CLAMP_VERTEX_COLOR                          0x891A
#define LOCAL_GL_CLAMP_VERTEX_COLOR_ARB                      0x891A
#define LOCAL_GL_CLEAR                                       0x1500
#define LOCAL_GL_CLEAR_BUFFER                                0x82B4
#define LOCAL_GL_CLEAR_TEXTURE                               0x9365
#define LOCAL_GL_CLIENT_ACTIVE_TEXTURE                       0x84E1
#define LOCAL_GL_CLIENT_ACTIVE_TEXTURE_ARB                   0x84E1
#define LOCAL_GL_CLIENT_ALL_ATTRIB_BITS                      0xFFFFFFFF
#define LOCAL_GL_CLIENT_ATTRIB_STACK_DEPTH                   0x0BB1
#define LOCAL_GL_CLIENT_MAPPED_BUFFER_BARRIER_BIT            0x00004000
#define LOCAL_GL_CLIENT_MAPPED_BUFFER_BARRIER_BIT_EXT        0x00004000
#define LOCAL_GL_CLIENT_PIXEL_STORE_BIT                      0x00000001
#define LOCAL_GL_CLIENT_STORAGE_BIT                          0x0200
#define LOCAL_GL_CLIENT_STORAGE_BIT_EXT                      0x0200
#define LOCAL_GL_CLIENT_VERTEX_ARRAY_BIT                     0x00000002
#define LOCAL_GL_CLIPPING_INPUT_PRIMITIVES                   0x82F6
#define LOCAL_GL_CLIPPING_INPUT_PRIMITIVES_ARB               0x82F6
#define LOCAL_GL_CLIPPING_OUTPUT_PRIMITIVES                  0x82F7
#define LOCAL_GL_CLIPPING_OUTPUT_PRIMITIVES_ARB              0x82F7
#define LOCAL_GL_CLIP_DEPTH_MODE                             0x935D
#define LOCAL_GL_CLIP_DEPTH_MODE_EXT                         0x935D
#define LOCAL_GL_CLIP_DISTANCE0                              0x3000
#define LOCAL_GL_CLIP_DISTANCE0_APPLE                        0x3000
#define LOCAL_GL_CLIP_DISTANCE0_EXT                          0x3000
#define LOCAL_GL_CLIP_DISTANCE1                              0x3001
#define LOCAL_GL_CLIP_DISTANCE1_APPLE                        0x3001
#define LOCAL_GL_CLIP_DISTANCE1_EXT                          0x3001
#define LOCAL_GL_CLIP_DISTANCE2                              0x3002
#define LOCAL_GL_CLIP_DISTANCE2_APPLE                        0x3002
#define LOCAL_GL_CLIP_DISTANCE2_EXT                          0x3002
#define LOCAL_GL_CLIP_DISTANCE3                              0x3003
#define LOCAL_GL_CLIP_DISTANCE3_APPLE                        0x3003
#define LOCAL_GL_CLIP_DISTANCE3_EXT                          0x3003
#define LOCAL_GL_CLIP_DISTANCE4                              0x3004
#define LOCAL_GL_CLIP_DISTANCE4_APPLE                        0x3004
#define LOCAL_GL_CLIP_DISTANCE4_EXT                          0x3004
#define LOCAL_GL_CLIP_DISTANCE5                              0x3005
#define LOCAL_GL_CLIP_DISTANCE5_APPLE                        0x3005
#define LOCAL_GL_CLIP_DISTANCE5_EXT                          0x3005
#define LOCAL_GL_CLIP_DISTANCE6                              0x3006
#define LOCAL_GL_CLIP_DISTANCE6_APPLE                        0x3006
#define LOCAL_GL_CLIP_DISTANCE6_EXT                          0x3006
#define LOCAL_GL_CLIP_DISTANCE7                              0x3007
#define LOCAL_GL_CLIP_DISTANCE7_APPLE                        0x3007
#define LOCAL_GL_CLIP_DISTANCE7_EXT                          0x3007
#define LOCAL_GL_CLIP_DISTANCE_NV                            0x8C7A
#define LOCAL_GL_CLIP_FAR_HINT_PGI                           0x1A221
#define LOCAL_GL_CLIP_NEAR_HINT_PGI                          0x1A220
#define LOCAL_GL_CLIP_ORIGIN                                 0x935C
#define LOCAL_GL_CLIP_ORIGIN_EXT                             0x935C
#define LOCAL_GL_CLIP_PLANE0                                 0x3000
#define LOCAL_GL_CLIP_PLANE0_IMG                             0x3000
#define LOCAL_GL_CLIP_PLANE1                                 0x3001
#define LOCAL_GL_CLIP_PLANE1_IMG                             0x3001
#define LOCAL_GL_CLIP_PLANE2                                 0x3002
#define LOCAL_GL_CLIP_PLANE2_IMG                             0x3002
#define LOCAL_GL_CLIP_PLANE3                                 0x3003
#define LOCAL_GL_CLIP_PLANE3_IMG                             0x3003
#define LOCAL_GL_CLIP_PLANE4                                 0x3004
#define LOCAL_GL_CLIP_PLANE4_IMG                             0x3004
#define LOCAL_GL_CLIP_PLANE5                                 0x3005
#define LOCAL_GL_CLIP_PLANE5_IMG                             0x3005
#define LOCAL_GL_CLIP_VOLUME_CLIPPING_HINT_EXT               0x80F0
#define LOCAL_GL_CLOSE_PATH_NV                               0x00
#define LOCAL_GL_CMYKA_EXT                                   0x800D
#define LOCAL_GL_CMYK_EXT                                    0x800C
#define LOCAL_GL_CND0_ATI                                    0x896B
#define LOCAL_GL_CND_ATI                                     0x896A
#define LOCAL_GL_COEFF                                       0x0A00
#define LOCAL_GL_COLOR                                       0x1800
#define LOCAL_GL_COLOR3_BIT_PGI                              0x00010000
#define LOCAL_GL_COLOR4_BIT_PGI                              0x00020000
#define LOCAL_GL_COLORBURN                                   0x929A
#define LOCAL_GL_COLORBURN_KHR                               0x929A
#define LOCAL_GL_COLORBURN_NV                                0x929A
#define LOCAL_GL_COLORDODGE                                  0x9299
#define LOCAL_GL_COLORDODGE_KHR                              0x9299
#define LOCAL_GL_COLORDODGE_NV                               0x9299
#define LOCAL_GL_COLOR_ALPHA_PAIRING_ATI                     0x8975
#define LOCAL_GL_COLOR_ARRAY                                 0x8076
#define LOCAL_GL_COLOR_ARRAY_ADDRESS_NV                      0x8F23
#define LOCAL_GL_COLOR_ARRAY_BUFFER_BINDING                  0x8898
#define LOCAL_GL_COLOR_ARRAY_BUFFER_BINDING_ARB              0x8898
#define LOCAL_GL_COLOR_ARRAY_COUNT_EXT                       0x8084
#define LOCAL_GL_COLOR_ARRAY_EXT                             0x8076
#define LOCAL_GL_COLOR_ARRAY_LENGTH_NV                       0x8F2D
#define LOCAL_GL_COLOR_ARRAY_LIST_IBM                        103072
#define LOCAL_GL_COLOR_ARRAY_LIST_STRIDE_IBM                 103082
#define LOCAL_GL_COLOR_ARRAY_PARALLEL_POINTERS_INTEL         0x83F7
#define LOCAL_GL_COLOR_ARRAY_POINTER                         0x8090
#define LOCAL_GL_COLOR_ARRAY_POINTER_EXT                     0x8090
#define LOCAL_GL_COLOR_ARRAY_SIZE                            0x8081
#define LOCAL_GL_COLOR_ARRAY_SIZE_EXT                        0x8081
#define LOCAL_GL_COLOR_ARRAY_STRIDE                          0x8083
#define LOCAL_GL_COLOR_ARRAY_STRIDE_EXT                      0x8083
#define LOCAL_GL_COLOR_ARRAY_TYPE                            0x8082
#define LOCAL_GL_COLOR_ARRAY_TYPE_EXT                        0x8082
#define LOCAL_GL_COLOR_ATTACHMENT0                           0x8CE0
#define LOCAL_GL_COLOR_ATTACHMENT0_EXT                       0x8CE0
#define LOCAL_GL_COLOR_ATTACHMENT0_NV                        0x8CE0
#define LOCAL_GL_COLOR_ATTACHMENT0_OES                       0x8CE0
#define LOCAL_GL_COLOR_ATTACHMENT1                           0x8CE1
#define LOCAL_GL_COLOR_ATTACHMENT10                          0x8CEA
#define LOCAL_GL_COLOR_ATTACHMENT10_EXT                      0x8CEA
#define LOCAL_GL_COLOR_ATTACHMENT10_NV                       0x8CEA
#define LOCAL_GL_COLOR_ATTACHMENT11                          0x8CEB
#define LOCAL_GL_COLOR_ATTACHMENT11_EXT                      0x8CEB
#define LOCAL_GL_COLOR_ATTACHMENT11_NV                       0x8CEB
#define LOCAL_GL_COLOR_ATTACHMENT12                          0x8CEC
#define LOCAL_GL_COLOR_ATTACHMENT12_EXT                      0x8CEC
#define LOCAL_GL_COLOR_ATTACHMENT12_NV                       0x8CEC
#define LOCAL_GL_COLOR_ATTACHMENT13                          0x8CED
#define LOCAL_GL_COLOR_ATTACHMENT13_EXT                      0x8CED
#define LOCAL_GL_COLOR_ATTACHMENT13_NV                       0x8CED
#define LOCAL_GL_COLOR_ATTACHMENT14                          0x8CEE
#define LOCAL_GL_COLOR_ATTACHMENT14_EXT                      0x8CEE
#define LOCAL_GL_COLOR_ATTACHMENT14_NV                       0x8CEE
#define LOCAL_GL_COLOR_ATTACHMENT15                          0x8CEF
#define LOCAL_GL_COLOR_ATTACHMENT15_EXT                      0x8CEF
#define LOCAL_GL_COLOR_ATTACHMENT15_NV                       0x8CEF
#define LOCAL_GL_COLOR_ATTACHMENT16                          0x8CF0
#define LOCAL_GL_COLOR_ATTACHMENT17                          0x8CF1
#define LOCAL_GL_COLOR_ATTACHMENT18                          0x8CF2
#define LOCAL_GL_COLOR_ATTACHMENT19                          0x8CF3
#define LOCAL_GL_COLOR_ATTACHMENT1_EXT                       0x8CE1
#define LOCAL_GL_COLOR_ATTACHMENT1_NV                        0x8CE1
#define LOCAL_GL_COLOR_ATTACHMENT2                           0x8CE2
#define LOCAL_GL_COLOR_ATTACHMENT20                          0x8CF4
#define LOCAL_GL_COLOR_ATTACHMENT21                          0x8CF5
#define LOCAL_GL_COLOR_ATTACHMENT22                          0x8CF6
#define LOCAL_GL_COLOR_ATTACHMENT23                          0x8CF7
#define LOCAL_GL_COLOR_ATTACHMENT24                          0x8CF8
#define LOCAL_GL_COLOR_ATTACHMENT25                          0x8CF9
#define LOCAL_GL_COLOR_ATTACHMENT26                          0x8CFA
#define LOCAL_GL_COLOR_ATTACHMENT27                          0x8CFB
#define LOCAL_GL_COLOR_ATTACHMENT28                          0x8CFC
#define LOCAL_GL_COLOR_ATTACHMENT29                          0x8CFD
#define LOCAL_GL_COLOR_ATTACHMENT2_EXT                       0x8CE2
#define LOCAL_GL_COLOR_ATTACHMENT2_NV                        0x8CE2
#define LOCAL_GL_COLOR_ATTACHMENT3                           0x8CE3
#define LOCAL_GL_COLOR_ATTACHMENT30                          0x8CFE
#define LOCAL_GL_COLOR_ATTACHMENT31                          0x8CFF
#define LOCAL_GL_COLOR_ATTACHMENT3_EXT                       0x8CE3
#define LOCAL_GL_COLOR_ATTACHMENT3_NV                        0x8CE3
#define LOCAL_GL_COLOR_ATTACHMENT4                           0x8CE4
#define LOCAL_GL_COLOR_ATTACHMENT4_EXT                       0x8CE4
#define LOCAL_GL_COLOR_ATTACHMENT4_NV                        0x8CE4
#define LOCAL_GL_COLOR_ATTACHMENT5                           0x8CE5
#define LOCAL_GL_COLOR_ATTACHMENT5_EXT                       0x8CE5
#define LOCAL_GL_COLOR_ATTACHMENT5_NV                        0x8CE5
#define LOCAL_GL_COLOR_ATTACHMENT6                           0x8CE6
#define LOCAL_GL_COLOR_ATTACHMENT6_EXT                       0x8CE6
#define LOCAL_GL_COLOR_ATTACHMENT6_NV                        0x8CE6
#define LOCAL_GL_COLOR_ATTACHMENT7                           0x8CE7
#define LOCAL_GL_COLOR_ATTACHMENT7_EXT                       0x8CE7
#define LOCAL_GL_COLOR_ATTACHMENT7_NV                        0x8CE7
#define LOCAL_GL_COLOR_ATTACHMENT8                           0x8CE8
#define LOCAL_GL_COLOR_ATTACHMENT8_EXT                       0x8CE8
#define LOCAL_GL_COLOR_ATTACHMENT8_NV                        0x8CE8
#define LOCAL_GL_COLOR_ATTACHMENT9                           0x8CE9
#define LOCAL_GL_COLOR_ATTACHMENT9_EXT                       0x8CE9
#define LOCAL_GL_COLOR_ATTACHMENT9_NV                        0x8CE9
#define LOCAL_GL_COLOR_ATTACHMENT_EXT                        0x90F0
#define LOCAL_GL_COLOR_BUFFER_BIT                            0x00004000
#define LOCAL_GL_COLOR_BUFFER_BIT0_QCOM                      0x00000001
#define LOCAL_GL_COLOR_BUFFER_BIT1_QCOM                      0x00000002
#define LOCAL_GL_COLOR_BUFFER_BIT2_QCOM                      0x00000004
#define LOCAL_GL_COLOR_BUFFER_BIT3_QCOM                      0x00000008
#define LOCAL_GL_COLOR_BUFFER_BIT4_QCOM                      0x00000010
#define LOCAL_GL_COLOR_BUFFER_BIT5_QCOM                      0x00000020
#define LOCAL_GL_COLOR_BUFFER_BIT6_QCOM                      0x00000040
#define LOCAL_GL_COLOR_BUFFER_BIT7_QCOM                      0x00000080
#define LOCAL_GL_COLOR_CLEAR_UNCLAMPED_VALUE_ATI             0x8835
#define LOCAL_GL_COLOR_CLEAR_VALUE                           0x0C22
#define LOCAL_GL_COLOR_COMPONENTS                            0x8283
#define LOCAL_GL_COLOR_ENCODING                              0x8296
#define LOCAL_GL_COLOR_EXT                                   0x1800
#define LOCAL_GL_COLOR_FLOAT_APPLE                           0x8A0F
#define LOCAL_GL_COLOR_INDEX                                 0x1900
#define LOCAL_GL_COLOR_INDEX12_EXT                           0x80E6
#define LOCAL_GL_COLOR_INDEX16_EXT                           0x80E7
#define LOCAL_GL_COLOR_INDEX1_EXT                            0x80E2
#define LOCAL_GL_COLOR_INDEX2_EXT                            0x80E3
#define LOCAL_GL_COLOR_INDEX4_EXT                            0x80E4
#define LOCAL_GL_COLOR_INDEX8_EXT                            0x80E5
#define LOCAL_GL_COLOR_INDEXES                               0x1603
#define LOCAL_GL_COLOR_LOGIC_OP                              0x0BF2
#define LOCAL_GL_COLOR_MATERIAL                              0x0B57
#define LOCAL_GL_COLOR_MATERIAL_FACE                         0x0B55
#define LOCAL_GL_COLOR_MATERIAL_PARAMETER                    0x0B56
#define LOCAL_GL_COLOR_MATRIX                                0x80B1
#define LOCAL_GL_COLOR_MATRIX_SGI                            0x80B1
#define LOCAL_GL_COLOR_MATRIX_STACK_DEPTH                    0x80B2
#define LOCAL_GL_COLOR_MATRIX_STACK_DEPTH_SGI                0x80B2
#define LOCAL_GL_COLOR_RENDERABLE                            0x8286
#define LOCAL_GL_COLOR_SAMPLES_NV                            0x8E20
#define LOCAL_GL_COLOR_SUM                                   0x8458
#define LOCAL_GL_COLOR_SUM_ARB                               0x8458
#define LOCAL_GL_COLOR_SUM_CLAMP_NV                          0x854F
#define LOCAL_GL_COLOR_SUM_EXT                               0x8458
#define LOCAL_GL_COLOR_TABLE                                 0x80D0
#define LOCAL_GL_COLOR_TABLE_ALPHA_SIZE                      0x80DD
#define LOCAL_GL_COLOR_TABLE_ALPHA_SIZE_SGI                  0x80DD
#define LOCAL_GL_COLOR_TABLE_BIAS                            0x80D7
#define LOCAL_GL_COLOR_TABLE_BIAS_SGI                        0x80D7
#define LOCAL_GL_COLOR_TABLE_BLUE_SIZE                       0x80DC
#define LOCAL_GL_COLOR_TABLE_BLUE_SIZE_SGI                   0x80DC
#define LOCAL_GL_COLOR_TABLE_FORMAT                          0x80D8
#define LOCAL_GL_COLOR_TABLE_FORMAT_SGI                      0x80D8
#define LOCAL_GL_COLOR_TABLE_GREEN_SIZE                      0x80DB
#define LOCAL_GL_COLOR_TABLE_GREEN_SIZE_SGI                  0x80DB
#define LOCAL_GL_COLOR_TABLE_INTENSITY_SIZE                  0x80DF
#define LOCAL_GL_COLOR_TABLE_INTENSITY_SIZE_SGI              0x80DF
#define LOCAL_GL_COLOR_TABLE_LUMINANCE_SIZE                  0x80DE
#define LOCAL_GL_COLOR_TABLE_LUMINANCE_SIZE_SGI              0x80DE
#define LOCAL_GL_COLOR_TABLE_RED_SIZE                        0x80DA
#define LOCAL_GL_COLOR_TABLE_RED_SIZE_SGI                    0x80DA
#define LOCAL_GL_COLOR_TABLE_SCALE                           0x80D6
#define LOCAL_GL_COLOR_TABLE_SCALE_SGI                       0x80D6
#define LOCAL_GL_COLOR_TABLE_SGI                             0x80D0
#define LOCAL_GL_COLOR_TABLE_WIDTH                           0x80D9
#define LOCAL_GL_COLOR_TABLE_WIDTH_SGI                       0x80D9
#define LOCAL_GL_COLOR_WRITEMASK                             0x0C23
#define LOCAL_GL_COMBINE                                     0x8570
#define LOCAL_GL_COMBINE4_NV                                 0x8503
#define LOCAL_GL_COMBINER0_NV                                0x8550
#define LOCAL_GL_COMBINER1_NV                                0x8551
#define LOCAL_GL_COMBINER2_NV                                0x8552
#define LOCAL_GL_COMBINER3_NV                                0x8553
#define LOCAL_GL_COMBINER4_NV                                0x8554
#define LOCAL_GL_COMBINER5_NV                                0x8555
#define LOCAL_GL_COMBINER6_NV                                0x8556
#define LOCAL_GL_COMBINER7_NV                                0x8557
#define LOCAL_GL_COMBINER_AB_DOT_PRODUCT_NV                  0x8545
#define LOCAL_GL_COMBINER_AB_OUTPUT_NV                       0x854A
#define LOCAL_GL_COMBINER_BIAS_NV                            0x8549
#define LOCAL_GL_COMBINER_CD_DOT_PRODUCT_NV                  0x8546
#define LOCAL_GL_COMBINER_CD_OUTPUT_NV                       0x854B
#define LOCAL_GL_COMBINER_COMPONENT_USAGE_NV                 0x8544
#define LOCAL_GL_COMBINER_INPUT_NV                           0x8542
#define LOCAL_GL_COMBINER_MAPPING_NV                         0x8543
#define LOCAL_GL_COMBINER_MUX_SUM_NV                         0x8547
#define LOCAL_GL_COMBINER_SCALE_NV                           0x8548
#define LOCAL_GL_COMBINER_SUM_OUTPUT_NV                      0x854C
#define LOCAL_GL_COMBINE_ALPHA                               0x8572
#define LOCAL_GL_COMBINE_ALPHA_ARB                           0x8572
#define LOCAL_GL_COMBINE_ALPHA_EXT                           0x8572
#define LOCAL_GL_COMBINE_ARB                                 0x8570
#define LOCAL_GL_COMBINE_EXT                                 0x8570
#define LOCAL_GL_COMBINE_RGB                                 0x8571
#define LOCAL_GL_COMBINE_RGB_ARB                             0x8571
#define LOCAL_GL_COMBINE_RGB_EXT                             0x8571
#define LOCAL_GL_COMMAND_BARRIER_BIT                         0x00000040
#define LOCAL_GL_COMMAND_BARRIER_BIT_EXT                     0x00000040
#define LOCAL_GL_COMPARE_REF_DEPTH_TO_TEXTURE_EXT            0x884E
#define LOCAL_GL_COMPARE_REF_TO_TEXTURE                      0x884E
#define LOCAL_GL_COMPARE_REF_TO_TEXTURE_EXT                  0x884E
#define LOCAL_GL_COMPARE_R_TO_TEXTURE                        0x884E
#define LOCAL_GL_COMPARE_R_TO_TEXTURE_ARB                    0x884E
#define LOCAL_GL_COMPATIBLE_SUBROUTINES                      0x8E4B
#define LOCAL_GL_COMPILE                                     0x1300
#define LOCAL_GL_COMPILE_AND_EXECUTE                         0x1301
#define LOCAL_GL_COMPILE_STATUS                              0x8B81
#define LOCAL_GL_COMPLETION_STATUS_ARB                       0x91B1
#define LOCAL_GL_COMPLETION_STATUS_KHR                       0x91B1
#define LOCAL_GL_COMPRESSED_ALPHA                            0x84E9
#define LOCAL_GL_COMPRESSED_ALPHA_ARB                        0x84E9
#define LOCAL_GL_COMPRESSED_INTENSITY                        0x84EC
#define LOCAL_GL_COMPRESSED_INTENSITY_ARB                    0x84EC
#define LOCAL_GL_COMPRESSED_LUMINANCE                        0x84EA
#define LOCAL_GL_COMPRESSED_LUMINANCE_ALPHA                  0x84EB
#define LOCAL_GL_COMPRESSED_LUMINANCE_ALPHA_3DC_ATI          0x8837
#define LOCAL_GL_COMPRESSED_LUMINANCE_ALPHA_ARB              0x84EB
#define LOCAL_GL_COMPRESSED_LUMINANCE_ALPHA_LATC2_EXT        0x8C72
#define LOCAL_GL_COMPRESSED_LUMINANCE_ARB                    0x84EA
#define LOCAL_GL_COMPRESSED_LUMINANCE_LATC1_EXT              0x8C70
#define LOCAL_GL_COMPRESSED_R11_EAC                          0x9270
#define LOCAL_GL_COMPRESSED_R11_EAC_OES                      0x9270
#define LOCAL_GL_COMPRESSED_RED                              0x8225
#define LOCAL_GL_COMPRESSED_RED_GREEN_RGTC2_EXT              0x8DBD
#define LOCAL_GL_COMPRESSED_RED_RGTC1                        0x8DBB
#define LOCAL_GL_COMPRESSED_RED_RGTC1_EXT                    0x8DBB
#define LOCAL_GL_COMPRESSED_RG                               0x8226
#define LOCAL_GL_COMPRESSED_RG11_EAC                         0x9272
#define LOCAL_GL_COMPRESSED_RG11_EAC_OES                     0x9272
#define LOCAL_GL_COMPRESSED_RGB                              0x84ED
#define LOCAL_GL_COMPRESSED_RGB8_ETC2                        0x9274
#define LOCAL_GL_COMPRESSED_RGB8_ETC2_OES                    0x9274
#define LOCAL_GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2    0x9276
#define LOCAL_GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2_OES        0x9276
#define LOCAL_GL_COMPRESSED_RGBA                             0x84EE
#define LOCAL_GL_COMPRESSED_RGBA8_ETC2_EAC                   0x9278
#define LOCAL_GL_COMPRESSED_RGBA8_ETC2_EAC_OES               0x9278
#define LOCAL_GL_COMPRESSED_RGBA_ARB                         0x84EE
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x10                  0x93BB
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x10_KHR              0x93BB
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x5                   0x93B8
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x5_KHR               0x93B8
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x6                   0x93B9
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x6_KHR               0x93B9
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x8                   0x93BA
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_10x8_KHR               0x93BA
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_12x10                  0x93BC
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_12x10_KHR              0x93BC
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_12x12                  0x93BD
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_12x12_KHR              0x93BD
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_3x3x3_OES              0x93C0
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_4x3x3_OES              0x93C1
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_4x4                    0x93B0
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_4x4_KHR                0x93B0
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_4x4x3_OES              0x93C2
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_4x4x4_OES              0x93C3
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x4                    0x93B1
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x4_KHR                0x93B1
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x4x4_OES              0x93C4
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x5                    0x93B2
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x5_KHR                0x93B2
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x5x4_OES              0x93C5
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_5x5x5_OES              0x93C6
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x5                    0x93B3
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x5_KHR                0x93B3
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x5x5_OES              0x93C7
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x6                    0x93B4
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x6_KHR                0x93B4
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x6x5_OES              0x93C8
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_6x6x6_OES              0x93C9
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x5                    0x93B5
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x5_KHR                0x93B5
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x6                    0x93B6
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x6_KHR                0x93B6
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x8                    0x93B7
#define LOCAL_GL_COMPRESSED_RGBA_ASTC_8x8_KHR                0x93B7
#define LOCAL_GL_COMPRESSED_RGBA_BPTC_UNORM                  0x8E8C
#define LOCAL_GL_COMPRESSED_RGBA_BPTC_UNORM_ARB              0x8E8C
#define LOCAL_GL_COMPRESSED_RGBA_BPTC_UNORM_EXT              0x8E8C
#define LOCAL_GL_COMPRESSED_RGBA_FXT1_3DFX                   0x86B1
#define LOCAL_GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG            0x8C03
#define LOCAL_GL_COMPRESSED_RGBA_PVRTC_2BPPV2_IMG            0x9137
#define LOCAL_GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG            0x8C02
#define LOCAL_GL_COMPRESSED_RGBA_PVRTC_4BPPV2_IMG            0x9138
#define LOCAL_GL_COMPRESSED_RGBA_S3TC_DXT1_EXT               0x83F1
#define LOCAL_GL_COMPRESSED_RGBA_S3TC_DXT3_ANGLE             0x83F2
#define LOCAL_GL_COMPRESSED_RGBA_S3TC_DXT3_EXT               0x83F2
#define LOCAL_GL_COMPRESSED_RGBA_S3TC_DXT5_ANGLE             0x83F3
#define LOCAL_GL_COMPRESSED_RGBA_S3TC_DXT5_EXT               0x83F3
#define LOCAL_GL_COMPRESSED_RGB_ARB                          0x84ED
#define LOCAL_GL_COMPRESSED_RGB_BPTC_SIGNED_FLOAT            0x8E8E
#define LOCAL_GL_COMPRESSED_RGB_BPTC_SIGNED_FLOAT_ARB        0x8E8E
#define LOCAL_GL_COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT        0x8E8E
#define LOCAL_GL_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT          0x8E8F
#define LOCAL_GL_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_ARB      0x8E8F
#define LOCAL_GL_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT      0x8E8F
#define LOCAL_GL_COMPRESSED_RGB_FXT1_3DFX                    0x86B0
#define LOCAL_GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG             0x8C01
#define LOCAL_GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG             0x8C00
#define LOCAL_GL_COMPRESSED_RGB_S3TC_DXT1_EXT                0x83F0
#define LOCAL_GL_COMPRESSED_RG_RGTC2                         0x8DBD
#define LOCAL_GL_COMPRESSED_SIGNED_LUMINANCE_ALPHA_LATC2_EXT 0x8C73
#define LOCAL_GL_COMPRESSED_SIGNED_LUMINANCE_LATC1_EXT       0x8C71
#define LOCAL_GL_COMPRESSED_SIGNED_R11_EAC                   0x9271
#define LOCAL_GL_COMPRESSED_SIGNED_R11_EAC_OES               0x9271
#define LOCAL_GL_COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT       0x8DBE
#define LOCAL_GL_COMPRESSED_SIGNED_RED_RGTC1                 0x8DBC
#define LOCAL_GL_COMPRESSED_SIGNED_RED_RGTC1_EXT             0x8DBC
#define LOCAL_GL_COMPRESSED_SIGNED_RG11_EAC                  0x9273
#define LOCAL_GL_COMPRESSED_SIGNED_RG11_EAC_OES              0x9273
#define LOCAL_GL_COMPRESSED_SIGNED_RG_RGTC2                  0x8DBE
#define LOCAL_GL_COMPRESSED_SLUMINANCE                       0x8C4A
#define LOCAL_GL_COMPRESSED_SLUMINANCE_ALPHA                 0x8C4B
#define LOCAL_GL_COMPRESSED_SLUMINANCE_ALPHA_EXT             0x8C4B
#define LOCAL_GL_COMPRESSED_SLUMINANCE_EXT                   0x8C4A
#define LOCAL_GL_COMPRESSED_SRGB                             0x8C48
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x10          0x93DB
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR      0x93DB
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x5           0x93D8
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR       0x93D8
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x6           0x93D9
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR       0x93D9
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x8           0x93DA
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR       0x93DA
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12x10          0x93DC
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR      0x93DC
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12x12          0x93DD
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR      0x93DD
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_3x3x3_OES      0x93E0
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4x3x3_OES      0x93E1
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4            0x93D0
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR        0x93D0
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4x3_OES      0x93E2
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4x4_OES      0x93E3
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x4            0x93D1
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR        0x93D1
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x4x4_OES      0x93E4
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x5            0x93D2
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR        0x93D2
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x5x4_OES      0x93E5
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_5x5x5_OES      0x93E6
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x5            0x93D3
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR        0x93D3
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x5x5_OES      0x93E7
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x6            0x93D4
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR        0x93D4
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x6x5_OES      0x93E8
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_6x6x6_OES      0x93E9
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x5            0x93D5
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR        0x93D5
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x6            0x93D6
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR        0x93D6
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x8            0x93D7
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR        0x93D7
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC            0x9279
#define LOCAL_GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC_OES        0x9279
#define LOCAL_GL_COMPRESSED_SRGB8_ETC2                       0x9275
#define LOCAL_GL_COMPRESSED_SRGB8_ETC2_OES                   0x9275
#define LOCAL_GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2   0x9277
#define LOCAL_GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2_OES       0x9277
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA                       0x8C49
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_BPTC_UNORM            0x8E8D
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_BPTC_UNORM_ARB        0x8E8D
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT        0x8E8D
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_EXT                   0x8C49
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_PVRTC_2BPPV1_EXT      0x8A56
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_PVRTC_2BPPV2_IMG      0x93F0
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_PVRTC_4BPPV1_EXT      0x8A57
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_PVRTC_4BPPV2_IMG      0x93F1
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT         0x8C4D
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_NV          0x8C4D
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT         0x8C4E
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_NV          0x8C4E
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT         0x8C4F
#define LOCAL_GL_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_NV          0x8C4F
#define LOCAL_GL_COMPRESSED_SRGB_EXT                         0x8C48
#define LOCAL_GL_COMPRESSED_SRGB_PVRTC_2BPPV1_EXT            0x8A54
#define LOCAL_GL_COMPRESSED_SRGB_PVRTC_4BPPV1_EXT            0x8A55
#define LOCAL_GL_COMPRESSED_SRGB_S3TC_DXT1_EXT               0x8C4C
#define LOCAL_GL_COMPRESSED_SRGB_S3TC_DXT1_NV                0x8C4C
#define LOCAL_GL_COMPRESSED_TEXTURE_FORMATS                  0x86A3
#define LOCAL_GL_COMPRESSED_TEXTURE_FORMATS_ARB              0x86A3
#define LOCAL_GL_COMPUTE_PROGRAM_NV                          0x90FB
#define LOCAL_GL_COMPUTE_PROGRAM_PARAMETER_BUFFER_NV         0x90FC
#define LOCAL_GL_COMPUTE_SHADER                              0x91B9
#define LOCAL_GL_COMPUTE_SHADER_BIT                          0x00000020
#define LOCAL_GL_COMPUTE_SHADER_INVOCATIONS                  0x82F5
#define LOCAL_GL_COMPUTE_SHADER_INVOCATIONS_ARB              0x82F5
#define LOCAL_GL_COMPUTE_SUBROUTINE                          0x92ED
#define LOCAL_GL_COMPUTE_SUBROUTINE_UNIFORM                  0x92F3
#define LOCAL_GL_COMPUTE_TEXTURE                             0x82A0
#define LOCAL_GL_COMPUTE_WORK_GROUP_SIZE                     0x8267
#define LOCAL_GL_COMP_BIT_ATI                                0x00000002
#define LOCAL_GL_CONDITION_SATISFIED                         0x911C
#define LOCAL_GL_CONDITION_SATISFIED_APPLE                   0x911C
#define LOCAL_GL_CONFORMANT_NV                               0x9374
#define LOCAL_GL_CONIC_CURVE_TO_NV                           0x1A
#define LOCAL_GL_CONJOINT_NV                                 0x9284
#define LOCAL_GL_CONSERVATIVE_RASTERIZATION_INTEL            0x83FE
#define LOCAL_GL_CONSERVATIVE_RASTERIZATION_NV               0x9346
#define LOCAL_GL_CONSERVATIVE_RASTER_DILATE_GRANULARITY_NV   0x937B
#define LOCAL_GL_CONSERVATIVE_RASTER_DILATE_NV               0x9379
#define LOCAL_GL_CONSERVATIVE_RASTER_DILATE_RANGE_NV         0x937A
#define LOCAL_GL_CONSERVATIVE_RASTER_MODE_NV                 0x954D
#define LOCAL_GL_CONSERVATIVE_RASTER_MODE_POST_SNAP_NV       0x954E
#define LOCAL_GL_CONSERVATIVE_RASTER_MODE_PRE_SNAP_NV        0x9550
#define LOCAL_GL_CONSERVATIVE_RASTER_MODE_PRE_SNAP_TRIANGLES_NV      0x954F
#define LOCAL_GL_CONSERVE_MEMORY_HINT_PGI                    0x1A1FD
#define LOCAL_GL_CONSTANT                                    0x8576
#define LOCAL_GL_CONSTANT_ALPHA                              0x8003
#define LOCAL_GL_CONSTANT_ALPHA_EXT                          0x8003
#define LOCAL_GL_CONSTANT_ARB                                0x8576
#define LOCAL_GL_CONSTANT_ATTENUATION                        0x1207
#define LOCAL_GL_CONSTANT_BORDER                             0x8151
#define LOCAL_GL_CONSTANT_BORDER_HP                          0x8151
#define LOCAL_GL_CONSTANT_COLOR                              0x8001
#define LOCAL_GL_CONSTANT_COLOR0_NV                          0x852A
#define LOCAL_GL_CONSTANT_COLOR1_NV                          0x852B
#define LOCAL_GL_CONSTANT_COLOR_EXT                          0x8001
#define LOCAL_GL_CONSTANT_EXT                                0x8576
#define LOCAL_GL_CONSTANT_NV                                 0x8576
#define LOCAL_GL_CONST_EYE_NV                                0x86E5
#define LOCAL_GL_CONTEXT_COMPATIBILITY_PROFILE_BIT           0x00000002
#define LOCAL_GL_CONTEXT_CORE_PROFILE_BIT                    0x00000001
#define LOCAL_GL_CONTEXT_FLAGS                               0x821E
#define LOCAL_GL_CONTEXT_FLAG_DEBUG_BIT                      0x00000002
#define LOCAL_GL_CONTEXT_FLAG_DEBUG_BIT_KHR                  0x00000002
#define LOCAL_GL_CONTEXT_FLAG_FORWARD_COMPATIBLE_BIT         0x00000001
#define LOCAL_GL_CONTEXT_FLAG_NO_ERROR_BIT                   0x00000008
#define LOCAL_GL_CONTEXT_FLAG_NO_ERROR_BIT_KHR               0x00000008
#define LOCAL_GL_CONTEXT_FLAG_PROTECTED_CONTENT_BIT_EXT      0x00000010
#define LOCAL_GL_CONTEXT_FLAG_ROBUST_ACCESS_BIT              0x00000004
#define LOCAL_GL_CONTEXT_FLAG_ROBUST_ACCESS_BIT_ARB          0x00000004
#define LOCAL_GL_CONTEXT_LOST                                0x0507
#define LOCAL_GL_CONTEXT_LOST_KHR                            0x0507
#define LOCAL_GL_CONTEXT_LOST_WEBGL                          0x9242
#define LOCAL_GL_CONTEXT_PROFILE_MASK                        0x9126
#define LOCAL_GL_CONTEXT_RELEASE_BEHAVIOR                    0x82FB
#define LOCAL_GL_CONTEXT_RELEASE_BEHAVIOR_FLUSH              0x82FC
#define LOCAL_GL_CONTEXT_RELEASE_BEHAVIOR_FLUSH_KHR          0x82FC
#define LOCAL_GL_CONTEXT_RELEASE_BEHAVIOR_KHR                0x82FB
#define LOCAL_GL_CONTEXT_ROBUST_ACCESS                       0x90F3
#define LOCAL_GL_CONTEXT_ROBUST_ACCESS_EXT                   0x90F3
#define LOCAL_GL_CONTEXT_ROBUST_ACCESS_KHR                   0x90F3
#define LOCAL_GL_CONTINUOUS_AMD                              0x9007
#define LOCAL_GL_CONTRAST_NV                                 0x92A1
#define LOCAL_GL_CONVEX_HULL_NV                              0x908B
#define LOCAL_GL_CONVOLUTION_1D                              0x8010
#define LOCAL_GL_CONVOLUTION_1D_EXT                          0x8010
#define LOCAL_GL_CONVOLUTION_2D                              0x8011
#define LOCAL_GL_CONVOLUTION_2D_EXT                          0x8011
#define LOCAL_GL_CONVOLUTION_BORDER_COLOR                    0x8154
#define LOCAL_GL_CONVOLUTION_BORDER_COLOR_HP                 0x8154
#define LOCAL_GL_CONVOLUTION_BORDER_MODE                     0x8013
#define LOCAL_GL_CONVOLUTION_BORDER_MODE_EXT                 0x8013
#define LOCAL_GL_CONVOLUTION_FILTER_BIAS                     0x8015
#define LOCAL_GL_CONVOLUTION_FILTER_BIAS_EXT                 0x8015
#define LOCAL_GL_CONVOLUTION_FILTER_SCALE                    0x8014
#define LOCAL_GL_CONVOLUTION_FILTER_SCALE_EXT                0x8014
#define LOCAL_GL_CONVOLUTION_FORMAT                          0x8017
#define LOCAL_GL_CONVOLUTION_FORMAT_EXT                      0x8017
#define LOCAL_GL_CONVOLUTION_HEIGHT                          0x8019
#define LOCAL_GL_CONVOLUTION_HEIGHT_EXT                      0x8019
#define LOCAL_GL_CONVOLUTION_HINT_SGIX                       0x8316
#define LOCAL_GL_CONVOLUTION_WIDTH                           0x8018
#define LOCAL_GL_CONVOLUTION_WIDTH_EXT                       0x8018
#define LOCAL_GL_CON_0_ATI                                   0x8941
#define LOCAL_GL_CON_10_ATI                                  0x894B
#define LOCAL_GL_CON_11_ATI                                  0x894C
#define LOCAL_GL_CON_12_ATI                                  0x894D
#define LOCAL_GL_CON_13_ATI                                  0x894E
#define LOCAL_GL_CON_14_ATI                                  0x894F
#define LOCAL_GL_CON_15_ATI                                  0x8950
#define LOCAL_GL_CON_16_ATI                                  0x8951
#define LOCAL_GL_CON_17_ATI                                  0x8952
#define LOCAL_GL_CON_18_ATI                                  0x8953
#define LOCAL_GL_CON_19_ATI                                  0x8954
#define LOCAL_GL_CON_1_ATI                                   0x8942
#define LOCAL_GL_CON_20_ATI                                  0x8955
#define LOCAL_GL_CON_21_ATI                                  0x8956
#define LOCAL_GL_CON_22_ATI                                  0x8957
#define LOCAL_GL_CON_23_ATI                                  0x8958
#define LOCAL_GL_CON_24_ATI                                  0x8959
#define LOCAL_GL_CON_25_ATI                                  0x895A
#define LOCAL_GL_CON_26_ATI                                  0x895B
#define LOCAL_GL_CON_27_ATI                                  0x895C
#define LOCAL_GL_CON_28_ATI                                  0x895D
#define LOCAL_GL_CON_29_ATI                                  0x895E
#define LOCAL_GL_CON_2_ATI                                   0x8943
#define LOCAL_GL_CON_30_ATI                                  0x895F
#define LOCAL_GL_CON_31_ATI                                  0x8960
#define LOCAL_GL_CON_3_ATI                                   0x8944
#define LOCAL_GL_CON_4_ATI                                   0x8945
#define LOCAL_GL_CON_5_ATI                                   0x8946
#define LOCAL_GL_CON_6_ATI                                   0x8947
#define LOCAL_GL_CON_7_ATI                                   0x8948
#define LOCAL_GL_CON_8_ATI                                   0x8949
#define LOCAL_GL_CON_9_ATI                                   0x894A
#define LOCAL_GL_COORD_REPLACE                               0x8862
#define LOCAL_GL_COORD_REPLACE_ARB                           0x8862
#define LOCAL_GL_COORD_REPLACE_NV                            0x8862
#define LOCAL_GL_COORD_REPLACE_OES                           0x8862
#define LOCAL_GL_COPY                                        0x1503
#define LOCAL_GL_COPY_INVERTED                               0x150C
#define LOCAL_GL_COPY_PIXEL_TOKEN                            0x0706
#define LOCAL_GL_COPY_READ_BUFFER                            0x8F36
#define LOCAL_GL_COPY_READ_BUFFER_BINDING                    0x8F36
#define LOCAL_GL_COPY_READ_BUFFER_NV                         0x8F36
#define LOCAL_GL_COPY_WRITE_BUFFER                           0x8F37
#define LOCAL_GL_COPY_WRITE_BUFFER_BINDING                   0x8F37
#define LOCAL_GL_COPY_WRITE_BUFFER_NV                        0x8F37
#define LOCAL_GL_COUNTER_RANGE_AMD                           0x8BC1
#define LOCAL_GL_COUNTER_TYPE_AMD                            0x8BC0
#define LOCAL_GL_COUNT_DOWN_NV                               0x9089
#define LOCAL_GL_COUNT_UP_NV                                 0x9088
#define LOCAL_GL_COVERAGE_ALL_FRAGMENTS_NV                   0x8ED5
#define LOCAL_GL_COVERAGE_ATTACHMENT_NV                      0x8ED2
#define LOCAL_GL_COVERAGE_AUTOMATIC_NV                       0x8ED7
#define LOCAL_GL_COVERAGE_BUFFERS_NV                         0x8ED3
#define LOCAL_GL_COVERAGE_BUFFER_BIT_NV                      0x00008000
#define LOCAL_GL_COVERAGE_COMPONENT4_NV                      0x8ED1
#define LOCAL_GL_COVERAGE_COMPONENT_NV                       0x8ED0
#define LOCAL_GL_COVERAGE_EDGE_FRAGMENTS_NV                  0x8ED6
#define LOCAL_GL_COVERAGE_MODULATION_NV                      0x9332
#define LOCAL_GL_COVERAGE_MODULATION_TABLE_NV                0x9331
#define LOCAL_GL_COVERAGE_MODULATION_TABLE_SIZE_NV           0x9333
#define LOCAL_GL_COVERAGE_SAMPLES_NV                         0x8ED4
#define LOCAL_GL_CPU_OPTIMIZED_QCOM                          0x8FB1
#define LOCAL_GL_CUBIC_CURVE_TO_NV                           0x0C
#define LOCAL_GL_CUBIC_EXT                                   0x8334
#define LOCAL_GL_CUBIC_HP                                    0x815F
#define LOCAL_GL_CUBIC_IMG                                   0x9139
#define LOCAL_GL_CUBIC_MIPMAP_LINEAR_IMG                     0x913B
#define LOCAL_GL_CUBIC_MIPMAP_NEAREST_IMG                    0x913A
#define LOCAL_GL_CULL_FACE                                   0x0B44
#define LOCAL_GL_CULL_FACE_MODE                              0x0B45
#define LOCAL_GL_CULL_FRAGMENT_NV                            0x86E7
#define LOCAL_GL_CULL_MODES_NV                               0x86E0
#define LOCAL_GL_CULL_VERTEX_EXT                             0x81AA
#define LOCAL_GL_CULL_VERTEX_EYE_POSITION_EXT                0x81AB
#define LOCAL_GL_CULL_VERTEX_IBM                             103050
#define LOCAL_GL_CULL_VERTEX_OBJECT_POSITION_EXT             0x81AC
#define LOCAL_GL_CURRENT_ATTRIB_NV                           0x8626
#define LOCAL_GL_CURRENT_BINORMAL_EXT                        0x843C
#define LOCAL_GL_CURRENT_BIT                                 0x00000001
#define LOCAL_GL_CURRENT_COLOR                               0x0B00
#define LOCAL_GL_CURRENT_FOG_COORD                           0x8453
#define LOCAL_GL_CURRENT_FOG_COORDINATE                      0x8453
#define LOCAL_GL_CURRENT_FOG_COORDINATE_EXT                  0x8453
#define LOCAL_GL_CURRENT_INDEX                               0x0B01
#define LOCAL_GL_CURRENT_MATRIX_ARB                          0x8641
#define LOCAL_GL_CURRENT_MATRIX_INDEX_ARB                    0x8845
#define LOCAL_GL_CURRENT_MATRIX_NV                           0x8641
#define LOCAL_GL_CURRENT_MATRIX_STACK_DEPTH_ARB              0x8640
#define LOCAL_GL_CURRENT_MATRIX_STACK_DEPTH_NV               0x8640
#define LOCAL_GL_CURRENT_NORMAL                              0x0B02
#define LOCAL_GL_CURRENT_OCCLUSION_QUERY_ID_NV               0x8865
#define LOCAL_GL_CURRENT_PALETTE_MATRIX_ARB                  0x8843
#define LOCAL_GL_CURRENT_PALETTE_MATRIX_OES                  0x8843
#define LOCAL_GL_CURRENT_PROGRAM                             0x8B8D
#define LOCAL_GL_CURRENT_QUERY                               0x8865
#define LOCAL_GL_CURRENT_QUERY_ARB                           0x8865
#define LOCAL_GL_CURRENT_QUERY_EXT                           0x8865
#define LOCAL_GL_CURRENT_RASTER_COLOR                        0x0B04
#define LOCAL_GL_CURRENT_RASTER_DISTANCE                     0x0B09
#define LOCAL_GL_CURRENT_RASTER_INDEX                        0x0B05
#define LOCAL_GL_CURRENT_RASTER_NORMAL_SGIX                  0x8406
#define LOCAL_GL_CURRENT_RASTER_POSITION                     0x0B07
#define LOCAL_GL_CURRENT_RASTER_POSITION_VALID               0x0B08
#define LOCAL_GL_CURRENT_RASTER_SECONDARY_COLOR              0x845F
#define LOCAL_GL_CURRENT_RASTER_TEXTURE_COORDS               0x0B06
#define LOCAL_GL_CURRENT_SECONDARY_COLOR                     0x8459
#define LOCAL_GL_CURRENT_SECONDARY_COLOR_EXT                 0x8459
#define LOCAL_GL_CURRENT_TANGENT_EXT                         0x843B
#define LOCAL_GL_CURRENT_TEXTURE_COORDS                      0x0B03
#define LOCAL_GL_CURRENT_TIME_NV                             0x8E28
#define LOCAL_GL_CURRENT_VERTEX_ATTRIB                       0x8626
#define LOCAL_GL_CURRENT_VERTEX_ATTRIB_ARB                   0x8626
#define LOCAL_GL_CURRENT_VERTEX_EXT                          0x87E2
#define LOCAL_GL_CURRENT_VERTEX_WEIGHT_EXT                   0x850B
#define LOCAL_GL_CURRENT_WEIGHT_ARB                          0x86A8
#define LOCAL_GL_CW                                          0x0900
#define LOCAL_GL_D3D12_FENCE_VALUE_EXT                       0x9595
#define LOCAL_GL_DARKEN                                      0x9297
#define LOCAL_GL_DARKEN_KHR                                  0x9297
#define LOCAL_GL_DARKEN_NV                                   0x9297
#define LOCAL_GL_DATA_BUFFER_AMD                             0x9151
#define LOCAL_GL_DEBUG_ASSERT_MESA                           0x875B
#define LOCAL_GL_DEBUG_CALLBACK_FUNCTION                     0x8244
#define LOCAL_GL_DEBUG_CALLBACK_FUNCTION_ARB                 0x8244
#define LOCAL_GL_DEBUG_CALLBACK_FUNCTION_KHR                 0x8244
#define LOCAL_GL_DEBUG_CALLBACK_USER_PARAM                   0x8245
#define LOCAL_GL_DEBUG_CALLBACK_USER_PARAM_ARB               0x8245
#define LOCAL_GL_DEBUG_CALLBACK_USER_PARAM_KHR               0x8245
#define LOCAL_GL_DEBUG_CATEGORY_API_ERROR_AMD                0x9149
#define LOCAL_GL_DEBUG_CATEGORY_APPLICATION_AMD              0x914F
#define LOCAL_GL_DEBUG_CATEGORY_DEPRECATION_AMD              0x914B
#define LOCAL_GL_DEBUG_CATEGORY_OTHER_AMD                    0x9150
#define LOCAL_GL_DEBUG_CATEGORY_PERFORMANCE_AMD              0x914D
#define LOCAL_GL_DEBUG_CATEGORY_SHADER_COMPILER_AMD          0x914E
#define LOCAL_GL_DEBUG_CATEGORY_UNDEFINED_BEHAVIOR_AMD       0x914C
#define LOCAL_GL_DEBUG_CATEGORY_WINDOW_SYSTEM_AMD            0x914A
#define LOCAL_GL_DEBUG_GROUP_STACK_DEPTH                     0x826D
#define LOCAL_GL_DEBUG_GROUP_STACK_DEPTH_KHR                 0x826D
#define LOCAL_GL_DEBUG_LOGGED_MESSAGES                       0x9145
#define LOCAL_GL_DEBUG_LOGGED_MESSAGES_AMD                   0x9145
#define LOCAL_GL_DEBUG_LOGGED_MESSAGES_ARB                   0x9145
#define LOCAL_GL_DEBUG_LOGGED_MESSAGES_KHR                   0x9145
#define LOCAL_GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH            0x8243
#define LOCAL_GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH_ARB        0x8243
#define LOCAL_GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH_KHR        0x8243
#define LOCAL_GL_DEBUG_OBJECT_MESA                           0x8759
#define LOCAL_GL_DEBUG_OUTPUT                                0x92E0
#define LOCAL_GL_DEBUG_OUTPUT_KHR                            0x92E0
#define LOCAL_GL_DEBUG_OUTPUT_SYNCHRONOUS                    0x8242
#define LOCAL_GL_DEBUG_OUTPUT_SYNCHRONOUS_ARB                0x8242
#define LOCAL_GL_DEBUG_OUTPUT_SYNCHRONOUS_KHR                0x8242
#define LOCAL_GL_DEBUG_PRINT_MESA                            0x875A
#define LOCAL_GL_DEBUG_SEVERITY_HIGH                         0x9146
#define LOCAL_GL_DEBUG_SEVERITY_HIGH_AMD                     0x9146
#define LOCAL_GL_DEBUG_SEVERITY_HIGH_ARB                     0x9146
#define LOCAL_GL_DEBUG_SEVERITY_HIGH_KHR                     0x9146
#define LOCAL_GL_DEBUG_SEVERITY_LOW                          0x9148
#define LOCAL_GL_DEBUG_SEVERITY_LOW_AMD                      0x9148
#define LOCAL_GL_DEBUG_SEVERITY_LOW_ARB                      0x9148
#define LOCAL_GL_DEBUG_SEVERITY_LOW_KHR                      0x9148
#define LOCAL_GL_DEBUG_SEVERITY_MEDIUM                       0x9147
#define LOCAL_GL_DEBUG_SEVERITY_MEDIUM_AMD                   0x9147
#define LOCAL_GL_DEBUG_SEVERITY_MEDIUM_ARB                   0x9147
#define LOCAL_GL_DEBUG_SEVERITY_MEDIUM_KHR                   0x9147
#define LOCAL_GL_DEBUG_SEVERITY_NOTIFICATION                 0x826B
#define LOCAL_GL_DEBUG_SEVERITY_NOTIFICATION_KHR             0x826B
#define LOCAL_GL_DEBUG_SOURCE_API                            0x8246
#define LOCAL_GL_DEBUG_SOURCE_API_ARB                        0x8246
#define LOCAL_GL_DEBUG_SOURCE_API_KHR                        0x8246
#define LOCAL_GL_DEBUG_SOURCE_APPLICATION                    0x824A
#define LOCAL_GL_DEBUG_SOURCE_APPLICATION_ARB                0x824A
#define LOCAL_GL_DEBUG_SOURCE_APPLICATION_KHR                0x824A
#define LOCAL_GL_DEBUG_SOURCE_OTHER                          0x824B
#define LOCAL_GL_DEBUG_SOURCE_OTHER_ARB                      0x824B
#define LOCAL_GL_DEBUG_SOURCE_OTHER_KHR                      0x824B
#define LOCAL_GL_DEBUG_SOURCE_SHADER_COMPILER                0x8248
#define LOCAL_GL_DEBUG_SOURCE_SHADER_COMPILER_ARB            0x8248
#define LOCAL_GL_DEBUG_SOURCE_SHADER_COMPILER_KHR            0x8248
#define LOCAL_GL_DEBUG_SOURCE_THIRD_PARTY                    0x8249
#define LOCAL_GL_DEBUG_SOURCE_THIRD_PARTY_ARB                0x8249
#define LOCAL_GL_DEBUG_SOURCE_THIRD_PARTY_KHR                0x8249
#define LOCAL_GL_DEBUG_SOURCE_WINDOW_SYSTEM                  0x8247
#define LOCAL_GL_DEBUG_SOURCE_WINDOW_SYSTEM_ARB              0x8247
#define LOCAL_GL_DEBUG_SOURCE_WINDOW_SYSTEM_KHR              0x8247
#define LOCAL_GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR              0x824D
#define LOCAL_GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR_ARB          0x824D
#define LOCAL_GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR_KHR          0x824D
#define LOCAL_GL_DEBUG_TYPE_ERROR                            0x824C
#define LOCAL_GL_DEBUG_TYPE_ERROR_ARB                        0x824C
#define LOCAL_GL_DEBUG_TYPE_ERROR_KHR                        0x824C
#define LOCAL_GL_DEBUG_TYPE_MARKER                           0x8268
#define LOCAL_GL_DEBUG_TYPE_MARKER_KHR                       0x8268
#define LOCAL_GL_DEBUG_TYPE_OTHER                            0x8251
#define LOCAL_GL_DEBUG_TYPE_OTHER_ARB                        0x8251
#define LOCAL_GL_DEBUG_TYPE_OTHER_KHR                        0x8251
#define LOCAL_GL_DEBUG_TYPE_PERFORMANCE                      0x8250
#define LOCAL_GL_DEBUG_TYPE_PERFORMANCE_ARB                  0x8250
#define LOCAL_GL_DEBUG_TYPE_PERFORMANCE_KHR                  0x8250
#define LOCAL_GL_DEBUG_TYPE_POP_GROUP                        0x826A
#define LOCAL_GL_DEBUG_TYPE_POP_GROUP_KHR                    0x826A
#define LOCAL_GL_DEBUG_TYPE_PORTABILITY                      0x824F
#define LOCAL_GL_DEBUG_TYPE_PORTABILITY_ARB                  0x824F
#define LOCAL_GL_DEBUG_TYPE_PORTABILITY_KHR                  0x824F
#define LOCAL_GL_DEBUG_TYPE_PUSH_GROUP                       0x8269
#define LOCAL_GL_DEBUG_TYPE_PUSH_GROUP_KHR                   0x8269
#define LOCAL_GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR               0x824E
#define LOCAL_GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR_ARB           0x824E
#define LOCAL_GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR_KHR           0x824E
#define LOCAL_GL_DECAL                                       0x2101
#define LOCAL_GL_DECODE_EXT                                  0x8A49
#define LOCAL_GL_DECR                                        0x1E03
#define LOCAL_GL_DECR_WRAP                                   0x8508
#define LOCAL_GL_DECR_WRAP_EXT                               0x8508
#define LOCAL_GL_DECR_WRAP_OES                               0x8508
#define LOCAL_GL_DEDICATED_MEMORY_OBJECT_EXT                 0x9581
#define LOCAL_GL_DEFORMATIONS_MASK_SGIX                      0x8196
#define LOCAL_GL_DELETE_STATUS                               0x8B80
#define LOCAL_GL_DEPENDENT_AR_TEXTURE_2D_NV                  0x86E9
#define LOCAL_GL_DEPENDENT_GB_TEXTURE_2D_NV                  0x86EA
#define LOCAL_GL_DEPENDENT_HILO_TEXTURE_2D_NV                0x8858
#define LOCAL_GL_DEPENDENT_RGB_TEXTURE_3D_NV                 0x8859
#define LOCAL_GL_DEPENDENT_RGB_TEXTURE_CUBE_MAP_NV           0x885A
#define LOCAL_GL_DEPTH                                       0x1801
#define LOCAL_GL_DEPTH24_STENCIL8                            0x88F0
#define LOCAL_GL_DEPTH24_STENCIL8_EXT                        0x88F0
#define LOCAL_GL_DEPTH24_STENCIL8_OES                        0x88F0
#define LOCAL_GL_DEPTH32F_STENCIL8                           0x8CAD
#define LOCAL_GL_DEPTH32F_STENCIL8_NV                        0x8DAC
#define LOCAL_GL_DEPTH_ATTACHMENT                            0x8D00
#define LOCAL_GL_DEPTH_ATTACHMENT_EXT                        0x8D00
#define LOCAL_GL_DEPTH_ATTACHMENT_OES                        0x8D00
#define LOCAL_GL_DEPTH_BIAS                                  0x0D1F
#define LOCAL_GL_DEPTH_BITS                                  0x0D56
#define LOCAL_GL_DEPTH_BOUNDS_EXT                            0x8891
#define LOCAL_GL_DEPTH_BOUNDS_TEST_EXT                       0x8890
#define LOCAL_GL_DEPTH_BUFFER_BIT                            0x00000100
#define LOCAL_GL_DEPTH_BUFFER_BIT0_QCOM                      0x00000100
#define LOCAL_GL_DEPTH_BUFFER_BIT1_QCOM                      0x00000200
#define LOCAL_GL_DEPTH_BUFFER_BIT2_QCOM                      0x00000400
#define LOCAL_GL_DEPTH_BUFFER_BIT3_QCOM                      0x00000800
#define LOCAL_GL_DEPTH_BUFFER_BIT4_QCOM                      0x00001000
#define LOCAL_GL_DEPTH_BUFFER_BIT5_QCOM                      0x00002000
#define LOCAL_GL_DEPTH_BUFFER_BIT6_QCOM                      0x00004000
#define LOCAL_GL_DEPTH_BUFFER_BIT7_QCOM                      0x00008000
#define LOCAL_GL_DEPTH_BUFFER_FLOAT_MODE_NV                  0x8DAF
#define LOCAL_GL_DEPTH_CLAMP                                 0x864F
#define LOCAL_GL_DEPTH_CLAMP_EXT                             0x864F
#define LOCAL_GL_DEPTH_CLAMP_FAR_AMD                         0x901F
#define LOCAL_GL_DEPTH_CLAMP_NEAR_AMD                        0x901E
#define LOCAL_GL_DEPTH_CLAMP_NV                              0x864F
#define LOCAL_GL_DEPTH_CLEAR_VALUE                           0x0B73
#define LOCAL_GL_DEPTH_COMPONENT                             0x1902
#define LOCAL_GL_DEPTH_COMPONENT16                           0x81A5
#define LOCAL_GL_DEPTH_COMPONENT16_ARB                       0x81A5
#define LOCAL_GL_DEPTH_COMPONENT16_NONLINEAR_NV              0x8E2C
#define LOCAL_GL_DEPTH_COMPONENT16_OES                       0x81A5
#define LOCAL_GL_DEPTH_COMPONENT16_SGIX                      0x81A5
#define LOCAL_GL_DEPTH_COMPONENT24                           0x81A6
#define LOCAL_GL_DEPTH_COMPONENT24_ARB                       0x81A6
#define LOCAL_GL_DEPTH_COMPONENT24_OES                       0x81A6
#define LOCAL_GL_DEPTH_COMPONENT24_SGIX                      0x81A6
#define LOCAL_GL_DEPTH_COMPONENT32                           0x81A7
#define LOCAL_GL_DEPTH_COMPONENT32F                          0x8CAC
#define LOCAL_GL_DEPTH_COMPONENT32F_NV                       0x8DAB
#define LOCAL_GL_DEPTH_COMPONENT32_ARB                       0x81A7
#define LOCAL_GL_DEPTH_COMPONENT32_OES                       0x81A7
#define LOCAL_GL_DEPTH_COMPONENT32_SGIX                      0x81A7
#define LOCAL_GL_DEPTH_COMPONENTS                            0x8284
#define LOCAL_GL_DEPTH_EXT                                   0x1801
#define LOCAL_GL_DEPTH_FUNC                                  0x0B74
#define LOCAL_GL_DEPTH_PASS_INSTRUMENT_COUNTERS_SGIX         0x8311
#define LOCAL_GL_DEPTH_PASS_INSTRUMENT_MAX_SGIX              0x8312
#define LOCAL_GL_DEPTH_PASS_INSTRUMENT_SGIX                  0x8310
#define LOCAL_GL_DEPTH_RANGE                                 0x0B70
#define LOCAL_GL_DEPTH_RENDERABLE                            0x8287
#define LOCAL_GL_DEPTH_SAMPLES_NV                            0x932D
#define LOCAL_GL_DEPTH_SCALE                                 0x0D1E
#define LOCAL_GL_DEPTH_STENCIL                               0x84F9
#define LOCAL_GL_DEPTH_STENCIL_ATTACHMENT                    0x821A
#define LOCAL_GL_DEPTH_STENCIL_EXT                           0x84F9
#define LOCAL_GL_DEPTH_STENCIL_MESA                          0x8750
#define LOCAL_GL_DEPTH_STENCIL_NV                            0x84F9
#define LOCAL_GL_DEPTH_STENCIL_OES                           0x84F9
#define LOCAL_GL_DEPTH_STENCIL_TEXTURE_MODE                  0x90EA
#define LOCAL_GL_DEPTH_STENCIL_TO_BGRA_NV                    0x886F
#define LOCAL_GL_DEPTH_STENCIL_TO_RGBA_NV                    0x886E
#define LOCAL_GL_DEPTH_TEST                                  0x0B71
#define LOCAL_GL_DEPTH_TEXTURE_MODE                          0x884B
#define LOCAL_GL_DEPTH_TEXTURE_MODE_ARB                      0x884B
#define LOCAL_GL_DEPTH_WRITEMASK                             0x0B72
#define LOCAL_GL_DETACHED_BUFFERS_NV                         0x95AB
#define LOCAL_GL_DETACHED_MEMORY_INCARNATION_NV              0x95A9
#define LOCAL_GL_DETACHED_TEXTURES_NV                        0x95AA
#define LOCAL_GL_DETAIL_TEXTURE_2D_BINDING_SGIS              0x8096
#define LOCAL_GL_DETAIL_TEXTURE_2D_SGIS                      0x8095
#define LOCAL_GL_DETAIL_TEXTURE_FUNC_POINTS_SGIS             0x809C
#define LOCAL_GL_DETAIL_TEXTURE_LEVEL_SGIS                   0x809A
#define LOCAL_GL_DETAIL_TEXTURE_MODE_SGIS                    0x809B
#define LOCAL_GL_DEVICE_LUID_EXT                             0x9599
#define LOCAL_GL_DEVICE_NODE_MASK_EXT                        0x959A
#define LOCAL_GL_DEVICE_UUID_EXT                             0x9597
#define LOCAL_GL_DIFFERENCE                                  0x929E
#define LOCAL_GL_DIFFERENCE_KHR                              0x929E
#define LOCAL_GL_DIFFERENCE_NV                               0x929E
#define LOCAL_GL_DIFFUSE                                     0x1201
#define LOCAL_GL_DISCARD_ATI                                 0x8763
#define LOCAL_GL_DISCARD_NV                                  0x8530
#define LOCAL_GL_DISCRETE_AMD                                0x9006
#define LOCAL_GL_DISJOINT_NV                                 0x9283
#define LOCAL_GL_DISPATCH_INDIRECT_BUFFER                    0x90EE
#define LOCAL_GL_DISPATCH_INDIRECT_BUFFER_BINDING            0x90EF
#define LOCAL_GL_DISPLAY_LIST                                0x82E7
#define LOCAL_GL_DISTANCE_ATTENUATION_EXT                    0x8129
#define LOCAL_GL_DISTANCE_ATTENUATION_SGIS                   0x8129
#define LOCAL_GL_DITHER                                      0x0BD0
#define LOCAL_GL_DMP_PROGRAM_BINARY_DMP                      0x9253
#define LOCAL_GL_DOMAIN                                      0x0A02
#define LOCAL_GL_DONT_CARE                                   0x1100
#define LOCAL_GL_DOT2_ADD_ATI                                0x896C
#define LOCAL_GL_DOT3_ATI                                    0x8966
#define LOCAL_GL_DOT3_RGB                                    0x86AE
#define LOCAL_GL_DOT3_RGBA                                   0x86AF
#define LOCAL_GL_DOT3_RGBA_ARB                               0x86AF
#define LOCAL_GL_DOT3_RGBA_EXT                               0x8741
#define LOCAL_GL_DOT3_RGBA_IMG                               0x86AF
#define LOCAL_GL_DOT3_RGB_ARB                                0x86AE
#define LOCAL_GL_DOT3_RGB_EXT                                0x8740
#define LOCAL_GL_DOT4_ATI                                    0x8967
#define LOCAL_GL_DOT_PRODUCT_AFFINE_DEPTH_REPLACE_NV         0x885D
#define LOCAL_GL_DOT_PRODUCT_CONST_EYE_REFLECT_CUBE_MAP_NV   0x86F3
#define LOCAL_GL_DOT_PRODUCT_DEPTH_REPLACE_NV                0x86ED
#define LOCAL_GL_DOT_PRODUCT_DIFFUSE_CUBE_MAP_NV             0x86F1
#define LOCAL_GL_DOT_PRODUCT_NV                              0x86EC
#define LOCAL_GL_DOT_PRODUCT_PASS_THROUGH_NV                 0x885B
#define LOCAL_GL_DOT_PRODUCT_REFLECT_CUBE_MAP_NV             0x86F2
#define LOCAL_GL_DOT_PRODUCT_TEXTURE_1D_NV                   0x885C
#define LOCAL_GL_DOT_PRODUCT_TEXTURE_2D_NV                   0x86EE
#define LOCAL_GL_DOT_PRODUCT_TEXTURE_3D_NV                   0x86EF
#define LOCAL_GL_DOT_PRODUCT_TEXTURE_CUBE_MAP_NV             0x86F0
#define LOCAL_GL_DOT_PRODUCT_TEXTURE_RECTANGLE_NV            0x864E
#define LOCAL_GL_DOUBLE                                      0x140A
#define LOCAL_GL_DOUBLEBUFFER                                0x0C32
#define LOCAL_GL_DOUBLE_EXT                                  0x140A
#define LOCAL_GL_DOUBLE_MAT2                                 0x8F46
#define LOCAL_GL_DOUBLE_MAT2_EXT                             0x8F46
#define LOCAL_GL_DOUBLE_MAT2x3                               0x8F49
#define LOCAL_GL_DOUBLE_MAT2x3_EXT                           0x8F49
#define LOCAL_GL_DOUBLE_MAT2x4                               0x8F4A
#define LOCAL_GL_DOUBLE_MAT2x4_EXT                           0x8F4A
#define LOCAL_GL_DOUBLE_MAT3                                 0x8F47
#define LOCAL_GL_DOUBLE_MAT3_EXT                             0x8F47
#define LOCAL_GL_DOUBLE_MAT3x2                               0x8F4B
#define LOCAL_GL_DOUBLE_MAT3x2_EXT                           0x8F4B
#define LOCAL_GL_DOUBLE_MAT3x4                               0x8F4C
#define LOCAL_GL_DOUBLE_MAT3x4_EXT                           0x8F4C
#define LOCAL_GL_DOUBLE_MAT4                                 0x8F48
#define LOCAL_GL_DOUBLE_MAT4_EXT                             0x8F48
#define LOCAL_GL_DOUBLE_MAT4x2                               0x8F4D
#define LOCAL_GL_DOUBLE_MAT4x2_EXT                           0x8F4D
#define LOCAL_GL_DOUBLE_MAT4x3                               0x8F4E
#define LOCAL_GL_DOUBLE_MAT4x3_EXT                           0x8F4E
#define LOCAL_GL_DOUBLE_VEC2                                 0x8FFC
#define LOCAL_GL_DOUBLE_VEC2_EXT                             0x8FFC
#define LOCAL_GL_DOUBLE_VEC3                                 0x8FFD
#define LOCAL_GL_DOUBLE_VEC3_EXT                             0x8FFD
#define LOCAL_GL_DOUBLE_VEC4                                 0x8FFE
#define LOCAL_GL_DOUBLE_VEC4_EXT                             0x8FFE
#define LOCAL_GL_DOWNSAMPLE_SCALES_IMG                       0x913E
#define LOCAL_GL_DRAW_ARRAYS_COMMAND_NV                      0x0003
#define LOCAL_GL_DRAW_ARRAYS_INSTANCED_COMMAND_NV            0x0007
#define LOCAL_GL_DRAW_ARRAYS_STRIP_COMMAND_NV                0x0005
#define LOCAL_GL_DRAW_BUFFER                                 0x0C01
#define LOCAL_GL_DRAW_BUFFER0                                0x8825
#define LOCAL_GL_DRAW_BUFFER0_ARB                            0x8825
#define LOCAL_GL_DRAW_BUFFER0_ATI                            0x8825
#define LOCAL_GL_DRAW_BUFFER0_EXT                            0x8825
#define LOCAL_GL_DRAW_BUFFER0_NV                             0x8825
#define LOCAL_GL_DRAW_BUFFER1                                0x8826
#define LOCAL_GL_DRAW_BUFFER10                               0x882F
#define LOCAL_GL_DRAW_BUFFER10_ARB                           0x882F
#define LOCAL_GL_DRAW_BUFFER10_ATI                           0x882F
#define LOCAL_GL_DRAW_BUFFER10_EXT                           0x882F
#define LOCAL_GL_DRAW_BUFFER10_NV                            0x882F
#define LOCAL_GL_DRAW_BUFFER11                               0x8830
#define LOCAL_GL_DRAW_BUFFER11_ARB                           0x8830
#define LOCAL_GL_DRAW_BUFFER11_ATI                           0x8830
#define LOCAL_GL_DRAW_BUFFER11_EXT                           0x8830
#define LOCAL_GL_DRAW_BUFFER11_NV                            0x8830
#define LOCAL_GL_DRAW_BUFFER12                               0x8831
#define LOCAL_GL_DRAW_BUFFER12_ARB                           0x8831
#define LOCAL_GL_DRAW_BUFFER12_ATI                           0x8831
#define LOCAL_GL_DRAW_BUFFER12_EXT                           0x8831
#define LOCAL_GL_DRAW_BUFFER12_NV                            0x8831
#define LOCAL_GL_DRAW_BUFFER13                               0x8832
#define LOCAL_GL_DRAW_BUFFER13_ARB                           0x8832
#define LOCAL_GL_DRAW_BUFFER13_ATI                           0x8832
#define LOCAL_GL_DRAW_BUFFER13_EXT                           0x8832
#define LOCAL_GL_DRAW_BUFFER13_NV                            0x8832
#define LOCAL_GL_DRAW_BUFFER14                               0x8833
#define LOCAL_GL_DRAW_BUFFER14_ARB                           0x8833
#define LOCAL_GL_DRAW_BUFFER14_ATI                           0x8833
#define LOCAL_GL_DRAW_BUFFER14_EXT                           0x8833
#define LOCAL_GL_DRAW_BUFFER14_NV                            0x8833
#define LOCAL_GL_DRAW_BUFFER15                               0x8834
#define LOCAL_GL_DRAW_BUFFER15_ARB                           0x8834
#define LOCAL_GL_DRAW_BUFFER15_ATI                           0x8834
#define LOCAL_GL_DRAW_BUFFER15_EXT                           0x8834
#define LOCAL_GL_DRAW_BUFFER15_NV                            0x8834
#define LOCAL_GL_DRAW_BUFFER1_ARB                            0x8826
#define LOCAL_GL_DRAW_BUFFER1_ATI                            0x8826
#define LOCAL_GL_DRAW_BUFFER1_EXT                            0x8826
#define LOCAL_GL_DRAW_BUFFER1_NV                             0x8826
#define LOCAL_GL_DRAW_BUFFER2                                0x8827
#define LOCAL_GL_DRAW_BUFFER2_ARB                            0x8827
#define LOCAL_GL_DRAW_BUFFER2_ATI                            0x8827
#define LOCAL_GL_DRAW_BUFFER2_EXT                            0x8827
#define LOCAL_GL_DRAW_BUFFER2_NV                             0x8827
#define LOCAL_GL_DRAW_BUFFER3                                0x8828
#define LOCAL_GL_DRAW_BUFFER3_ARB                            0x8828
#define LOCAL_GL_DRAW_BUFFER3_ATI                            0x8828
#define LOCAL_GL_DRAW_BUFFER3_EXT                            0x8828
#define LOCAL_GL_DRAW_BUFFER3_NV                             0x8828
#define LOCAL_GL_DRAW_BUFFER4                                0x8829
#define LOCAL_GL_DRAW_BUFFER4_ARB                            0x8829
#define LOCAL_GL_DRAW_BUFFER4_ATI                            0x8829
#define LOCAL_GL_DRAW_BUFFER4_EXT                            0x8829
#define LOCAL_GL_DRAW_BUFFER4_NV                             0x8829
#define LOCAL_GL_DRAW_BUFFER5                                0x882A
#define LOCAL_GL_DRAW_BUFFER5_ARB                            0x882A
#define LOCAL_GL_DRAW_BUFFER5_ATI                            0x882A
#define LOCAL_GL_DRAW_BUFFER5_EXT                            0x882A
#define LOCAL_GL_DRAW_BUFFER5_NV                             0x882A
#define LOCAL_GL_DRAW_BUFFER6                                0x882B
#define LOCAL_GL_DRAW_BUFFER6_ARB                            0x882B
#define LOCAL_GL_DRAW_BUFFER6_ATI                            0x882B
#define LOCAL_GL_DRAW_BUFFER6_EXT                            0x882B
#define LOCAL_GL_DRAW_BUFFER6_NV                             0x882B
#define LOCAL_GL_DRAW_BUFFER7                                0x882C
#define LOCAL_GL_DRAW_BUFFER7_ARB                            0x882C
#define LOCAL_GL_DRAW_BUFFER7_ATI                            0x882C
#define LOCAL_GL_DRAW_BUFFER7_EXT                            0x882C
#define LOCAL_GL_DRAW_BUFFER7_NV                             0x882C
#define LOCAL_GL_DRAW_BUFFER8                                0x882D
#define LOCAL_GL_DRAW_BUFFER8_ARB                            0x882D
#define LOCAL_GL_DRAW_BUFFER8_ATI                            0x882D
#define LOCAL_GL_DRAW_BUFFER8_EXT                            0x882D
#define LOCAL_GL_DRAW_BUFFER8_NV                             0x882D
#define LOCAL_GL_DRAW_BUFFER9                                0x882E
#define LOCAL_GL_DRAW_BUFFER9_ARB                            0x882E
#define LOCAL_GL_DRAW_BUFFER9_ATI                            0x882E
#define LOCAL_GL_DRAW_BUFFER9_EXT                            0x882E
#define LOCAL_GL_DRAW_BUFFER9_NV                             0x882E
#define LOCAL_GL_DRAW_BUFFER_EXT                             0x0C01
#define LOCAL_GL_DRAW_ELEMENTS_COMMAND_NV                    0x0002
#define LOCAL_GL_DRAW_ELEMENTS_INSTANCED_COMMAND_NV          0x0006
#define LOCAL_GL_DRAW_ELEMENTS_STRIP_COMMAND_NV              0x0004
#define LOCAL_GL_DRAW_FRAMEBUFFER                            0x8CA9
#define LOCAL_GL_DRAW_FRAMEBUFFER_ANGLE                      0x8CA9
#define LOCAL_GL_DRAW_FRAMEBUFFER_APPLE                      0x8CA9
#define LOCAL_GL_DRAW_FRAMEBUFFER_BINDING                    0x8CA6
#define LOCAL_GL_DRAW_FRAMEBUFFER_BINDING_ANGLE              0x8CA6
#define LOCAL_GL_DRAW_FRAMEBUFFER_BINDING_APPLE              0x8CA6
#define LOCAL_GL_DRAW_FRAMEBUFFER_BINDING_EXT                0x8CA6
#define LOCAL_GL_DRAW_FRAMEBUFFER_BINDING_NV                 0x8CA6
#define LOCAL_GL_DRAW_FRAMEBUFFER_EXT                        0x8CA9
#define LOCAL_GL_DRAW_FRAMEBUFFER_NV                         0x8CA9
#define LOCAL_GL_DRAW_INDIRECT_ADDRESS_NV                    0x8F41
#define LOCAL_GL_DRAW_INDIRECT_BUFFER                        0x8F3F
#define LOCAL_GL_DRAW_INDIRECT_BUFFER_BINDING                0x8F43
#define LOCAL_GL_DRAW_INDIRECT_LENGTH_NV                     0x8F42
#define LOCAL_GL_DRAW_INDIRECT_UNIFIED_NV                    0x8F40
#define LOCAL_GL_DRAW_PIXELS_APPLE                           0x8A0A
#define LOCAL_GL_DRAW_PIXEL_TOKEN                            0x0705
#define LOCAL_GL_DRIVER_UUID_EXT                             0x9598
#define LOCAL_GL_DSDT8_MAG8_INTENSITY8_NV                    0x870B
#define LOCAL_GL_DSDT8_MAG8_NV                               0x870A
#define LOCAL_GL_DSDT8_NV                                    0x8709
#define LOCAL_GL_DSDT_MAG_INTENSITY_NV                       0x86DC
#define LOCAL_GL_DSDT_MAG_NV                                 0x86F6
#define LOCAL_GL_DSDT_MAG_VIB_NV                             0x86F7
#define LOCAL_GL_DSDT_NV                                     0x86F5
#define LOCAL_GL_DST_ALPHA                                   0x0304
#define LOCAL_GL_DST_ATOP_NV                                 0x928F
#define LOCAL_GL_DST_COLOR                                   0x0306
#define LOCAL_GL_DST_IN_NV                                   0x928B
#define LOCAL_GL_DST_NV                                      0x9287
#define LOCAL_GL_DST_OUT_NV                                  0x928D
#define LOCAL_GL_DST_OVER_NV                                 0x9289
#define LOCAL_GL_DS_BIAS_NV                                  0x8716
#define LOCAL_GL_DS_SCALE_NV                                 0x8710
#define LOCAL_GL_DT_BIAS_NV                                  0x8717
#define LOCAL_GL_DT_SCALE_NV                                 0x8711
#define LOCAL_GL_DU8DV8_ATI                                  0x877A
#define LOCAL_GL_DUAL_ALPHA12_SGIS                           0x8112
#define LOCAL_GL_DUAL_ALPHA16_SGIS                           0x8113
#define LOCAL_GL_DUAL_ALPHA4_SGIS                            0x8110
#define LOCAL_GL_DUAL_ALPHA8_SGIS                            0x8111
#define LOCAL_GL_DUAL_INTENSITY12_SGIS                       0x811A
#define LOCAL_GL_DUAL_INTENSITY16_SGIS                       0x811B
#define LOCAL_GL_DUAL_INTENSITY4_SGIS                        0x8118
#define LOCAL_GL_DUAL_INTENSITY8_SGIS                        0x8119
#define LOCAL_GL_DUAL_LUMINANCE12_SGIS                       0x8116
#define LOCAL_GL_DUAL_LUMINANCE16_SGIS                       0x8117
#define LOCAL_GL_DUAL_LUMINANCE4_SGIS                        0x8114
#define LOCAL_GL_DUAL_LUMINANCE8_SGIS                        0x8115
#define LOCAL_GL_DUAL_LUMINANCE_ALPHA4_SGIS                  0x811C
#define LOCAL_GL_DUAL_LUMINANCE_ALPHA8_SGIS                  0x811D
#define LOCAL_GL_DUAL_TEXTURE_SELECT_SGIS                    0x8124
#define LOCAL_GL_DUDV_ATI                                    0x8779
#define LOCAL_GL_DUP_FIRST_CUBIC_CURVE_TO_NV                 0xF2
#define LOCAL_GL_DUP_LAST_CUBIC_CURVE_TO_NV                  0xF4
#define LOCAL_GL_DYNAMIC_ATI                                 0x8761
#define LOCAL_GL_DYNAMIC_COPY                                0x88EA
#define LOCAL_GL_DYNAMIC_COPY_ARB                            0x88EA
#define LOCAL_GL_DYNAMIC_DRAW                                0x88E8
#define LOCAL_GL_DYNAMIC_DRAW_ARB                            0x88E8
#define LOCAL_GL_DYNAMIC_READ                                0x88E9
#define LOCAL_GL_DYNAMIC_READ_ARB                            0x88E9
#define LOCAL_GL_DYNAMIC_STORAGE_BIT                         0x0100
#define LOCAL_GL_DYNAMIC_STORAGE_BIT_EXT                     0x0100
#define LOCAL_GL_EDGEFLAG_BIT_PGI                            0x00040000
#define LOCAL_GL_EDGE_FLAG                                   0x0B43
#define LOCAL_GL_EDGE_FLAG_ARRAY                             0x8079
#define LOCAL_GL_EDGE_FLAG_ARRAY_ADDRESS_NV                  0x8F26
#define LOCAL_GL_EDGE_FLAG_ARRAY_BUFFER_BINDING              0x889B
#define LOCAL_GL_EDGE_FLAG_ARRAY_BUFFER_BINDING_ARB          0x889B
#define LOCAL_GL_EDGE_FLAG_ARRAY_COUNT_EXT                   0x808D
#define LOCAL_GL_EDGE_FLAG_ARRAY_EXT                         0x8079
#define LOCAL_GL_EDGE_FLAG_ARRAY_LENGTH_NV                   0x8F30
#define LOCAL_GL_EDGE_FLAG_ARRAY_LIST_IBM                    103075
#define LOCAL_GL_EDGE_FLAG_ARRAY_LIST_STRIDE_IBM             103085
#define LOCAL_GL_EDGE_FLAG_ARRAY_POINTER                     0x8093
#define LOCAL_GL_EDGE_FLAG_ARRAY_POINTER_EXT                 0x8093
#define LOCAL_GL_EDGE_FLAG_ARRAY_STRIDE                      0x808C
#define LOCAL_GL_EDGE_FLAG_ARRAY_STRIDE_EXT                  0x808C
#define LOCAL_GL_EFFECTIVE_RASTER_SAMPLES_EXT                0x932C
#define LOCAL_GL_EIGHTH_BIT_ATI                              0x00000020
#define LOCAL_GL_ELEMENT_ADDRESS_COMMAND_NV                  0x0008
#define LOCAL_GL_ELEMENT_ARRAY_ADDRESS_NV                    0x8F29
#define LOCAL_GL_ELEMENT_ARRAY_APPLE                         0x8A0C
#define LOCAL_GL_ELEMENT_ARRAY_ATI                           0x8768
#define LOCAL_GL_ELEMENT_ARRAY_BARRIER_BIT                   0x00000002
#define LOCAL_GL_ELEMENT_ARRAY_BARRIER_BIT_EXT               0x00000002
#define LOCAL_GL_ELEMENT_ARRAY_BUFFER                        0x8893
#define LOCAL_GL_ELEMENT_ARRAY_BUFFER_ARB                    0x8893
#define LOCAL_GL_ELEMENT_ARRAY_BUFFER_BINDING                0x8895
#define LOCAL_GL_ELEMENT_ARRAY_BUFFER_BINDING_ARB            0x8895
#define LOCAL_GL_ELEMENT_ARRAY_LENGTH_NV                     0x8F33
#define LOCAL_GL_ELEMENT_ARRAY_POINTER_APPLE                 0x8A0E
#define LOCAL_GL_ELEMENT_ARRAY_POINTER_ATI                   0x876A
#define LOCAL_GL_ELEMENT_ARRAY_TYPE_APPLE                    0x8A0D
#define LOCAL_GL_ELEMENT_ARRAY_TYPE_ATI                      0x8769
#define LOCAL_GL_ELEMENT_ARRAY_UNIFIED_NV                    0x8F1F
#define LOCAL_GL_EMBOSS_CONSTANT_NV                          0x855E
#define LOCAL_GL_EMBOSS_LIGHT_NV                             0x855D
#define LOCAL_GL_EMBOSS_MAP_NV                               0x855F
#define LOCAL_GL_EMISSION                                    0x1600
#define LOCAL_GL_ENABLE_BIT                                  0x00002000
#define LOCAL_GL_EQUAL                                       0x0202
#define LOCAL_GL_EQUIV                                       0x1509
#define LOCAL_GL_ETC1_RGB8_OES                               0x8D64
#define LOCAL_GL_ETC1_SRGB8_NV                               0x88EE
#define LOCAL_GL_EVAL_2D_NV                                  0x86C0
#define LOCAL_GL_EVAL_BIT                                    0x00010000
#define LOCAL_GL_EVAL_FRACTIONAL_TESSELLATION_NV             0x86C5
#define LOCAL_GL_EVAL_TRIANGULAR_2D_NV                       0x86C1
#define LOCAL_GL_EVAL_VERTEX_ATTRIB0_NV                      0x86C6
#define LOCAL_GL_EVAL_VERTEX_ATTRIB10_NV                     0x86D0
#define LOCAL_GL_EVAL_VERTEX_ATTRIB11_NV                     0x86D1
#define LOCAL_GL_EVAL_VERTEX_ATTRIB12_NV                     0x86D2
#define LOCAL_GL_EVAL_VERTEX_ATTRIB13_NV                     0x86D3
#define LOCAL_GL_EVAL_VERTEX_ATTRIB14_NV                     0x86D4
#define LOCAL_GL_EVAL_VERTEX_ATTRIB15_NV                     0x86D5
#define LOCAL_GL_EVAL_VERTEX_ATTRIB1_NV                      0x86C7
#define LOCAL_GL_EVAL_VERTEX_ATTRIB2_NV                      0x86C8
#define LOCAL_GL_EVAL_VERTEX_ATTRIB3_NV                      0x86C9
#define LOCAL_GL_EVAL_VERTEX_ATTRIB4_NV                      0x86CA
#define LOCAL_GL_EVAL_VERTEX_ATTRIB5_NV                      0x86CB
#define LOCAL_GL_EVAL_VERTEX_ATTRIB6_NV                      0x86CC
#define LOCAL_GL_EVAL_VERTEX_ATTRIB7_NV                      0x86CD
#define LOCAL_GL_EVAL_VERTEX_ATTRIB8_NV                      0x86CE
#define LOCAL_GL_EVAL_VERTEX_ATTRIB9_NV                      0x86CF
#define LOCAL_GL_EXCLUSION                                   0x92A0
#define LOCAL_GL_EXCLUSION_KHR                               0x92A0
#define LOCAL_GL_EXCLUSION_NV                                0x92A0
#define LOCAL_GL_EXCLUSIVE_EXT                               0x8F11
#define LOCAL_GL_EXP                                         0x0800
#define LOCAL_GL_EXP2                                        0x0801
#define LOCAL_GL_EXPAND_NEGATE_NV                            0x8539
#define LOCAL_GL_EXPAND_NORMAL_NV                            0x8538
#define LOCAL_GL_EXTENSIONS                                  0x1F03
#define LOCAL_GL_EXTERNAL_STORAGE_BIT_NVX                    0x2000
#define LOCAL_GL_EXTERNAL_VIRTUAL_MEMORY_BUFFER_AMD          0x9160
#define LOCAL_GL_EYE_DISTANCE_TO_LINE_SGIS                   0x81F2
#define LOCAL_GL_EYE_DISTANCE_TO_POINT_SGIS                  0x81F0
#define LOCAL_GL_EYE_LINEAR                                  0x2400
#define LOCAL_GL_EYE_LINEAR_NV                               0x2400
#define LOCAL_GL_EYE_LINE_SGIS                               0x81F6
#define LOCAL_GL_EYE_PLANE                                   0x2502
#define LOCAL_GL_EYE_PLANE_ABSOLUTE_NV                       0x855C
#define LOCAL_GL_EYE_POINT_SGIS                              0x81F4
#define LOCAL_GL_EYE_RADIAL_NV                               0x855B
#define LOCAL_GL_E_TIMES_F_NV                                0x8531
#define LOCAL_GL_FACTOR_ALPHA_MODULATE_IMG                   0x8C07
#define LOCAL_GL_FACTOR_MAX_AMD                              0x901D
#define LOCAL_GL_FACTOR_MIN_AMD                              0x901C
#define LOCAL_GL_FAILURE_NV                                  0x9030
#define LOCAL_GL_FALSE                                       0
#define LOCAL_GL_FASTEST                                     0x1101
#define LOCAL_GL_FEEDBACK                                    0x1C01
#define LOCAL_GL_FEEDBACK_BUFFER_POINTER                     0x0DF0
#define LOCAL_GL_FEEDBACK_BUFFER_SIZE                        0x0DF1
#define LOCAL_GL_FEEDBACK_BUFFER_TYPE                        0x0DF2
#define LOCAL_GL_FENCE_APPLE                                 0x8A0B
#define LOCAL_GL_FENCE_CONDITION_NV                          0x84F4
#define LOCAL_GL_FENCE_STATUS_NV                             0x84F3
#define LOCAL_GL_FETCH_PER_SAMPLE_ARM                        0x8F65
#define LOCAL_GL_FIELDS_NV                                   0x8E27
#define LOCAL_GL_FIELD_LOWER_NV                              0x9023
#define LOCAL_GL_FIELD_UPPER_NV                              0x9022
#define LOCAL_GL_FILE_NAME_NV                                0x9074
#define LOCAL_GL_FILL                                        0x1B02
#define LOCAL_GL_FILL_NV                                     0x1B02
#define LOCAL_GL_FILL_RECTANGLE_NV                           0x933C
#define LOCAL_GL_FILTER                                      0x829A
#define LOCAL_GL_FILTER4_SGIS                                0x8146
#define LOCAL_GL_FIRST_TO_REST_NV                            0x90AF
#define LOCAL_GL_FIRST_VERTEX_CONVENTION                     0x8E4D
#define LOCAL_GL_FIRST_VERTEX_CONVENTION_EXT                 0x8E4D
#define LOCAL_GL_FIRST_VERTEX_CONVENTION_OES                 0x8E4D
#define LOCAL_GL_FIXED                                       0x140C
#define LOCAL_GL_FIXED_OES                                   0x140C
#define LOCAL_GL_FIXED_ONLY                                  0x891D
#define LOCAL_GL_FIXED_ONLY_ARB                              0x891D
#define LOCAL_GL_FLAT                                        0x1D00
#define LOCAL_GL_FLOAT                                       0x1406
#define LOCAL_GL_FLOAT16_MAT2_AMD                            0x91C5
#define LOCAL_GL_FLOAT16_MAT2x3_AMD                          0x91C8
#define LOCAL_GL_FLOAT16_MAT2x4_AMD                          0x91C9
#define LOCAL_GL_FLOAT16_MAT3_AMD                            0x91C6
#define LOCAL_GL_FLOAT16_MAT3x2_AMD                          0x91CA
#define LOCAL_GL_FLOAT16_MAT3x4_AMD                          0x91CB
#define LOCAL_GL_FLOAT16_MAT4_AMD                            0x91C7
#define LOCAL_GL_FLOAT16_MAT4x2_AMD                          0x91CC
#define LOCAL_GL_FLOAT16_MAT4x3_AMD                          0x91CD
#define LOCAL_GL_FLOAT16_NV                                  0x8FF8
#define LOCAL_GL_FLOAT16_VEC2_NV                             0x8FF9
#define LOCAL_GL_FLOAT16_VEC3_NV                             0x8FFA
#define LOCAL_GL_FLOAT16_VEC4_NV                             0x8FFB
#define LOCAL_GL_FLOAT_32_UNSIGNED_INT_24_8_REV              0x8DAD
#define LOCAL_GL_FLOAT_32_UNSIGNED_INT_24_8_REV_NV           0x8DAD
#define LOCAL_GL_FLOAT_CLEAR_COLOR_VALUE_NV                  0x888D
#define LOCAL_GL_FLOAT_MAT2                                  0x8B5A
#define LOCAL_GL_FLOAT_MAT2_ARB                              0x8B5A
#define LOCAL_GL_FLOAT_MAT2x3                                0x8B65
#define LOCAL_GL_FLOAT_MAT2x3_NV                             0x8B65
#define LOCAL_GL_FLOAT_MAT2x4                                0x8B66
#define LOCAL_GL_FLOAT_MAT2x4_NV                             0x8B66
#define LOCAL_GL_FLOAT_MAT3                                  0x8B5B
#define LOCAL_GL_FLOAT_MAT3_ARB                              0x8B5B
#define LOCAL_GL_FLOAT_MAT3x2                                0x8B67
#define LOCAL_GL_FLOAT_MAT3x2_NV                             0x8B67
#define LOCAL_GL_FLOAT_MAT3x4                                0x8B68
#define LOCAL_GL_FLOAT_MAT3x4_NV                             0x8B68
#define LOCAL_GL_FLOAT_MAT4                                  0x8B5C
#define LOCAL_GL_FLOAT_MAT4_ARB                              0x8B5C
#define LOCAL_GL_FLOAT_MAT4x2                                0x8B69
#define LOCAL_GL_FLOAT_MAT4x2_NV                             0x8B69
#define LOCAL_GL_FLOAT_MAT4x3                                0x8B6A
#define LOCAL_GL_FLOAT_MAT4x3_NV                             0x8B6A
#define LOCAL_GL_FLOAT_R16_NV                                0x8884
#define LOCAL_GL_FLOAT_R32_NV                                0x8885
#define LOCAL_GL_FLOAT_RG16_NV                               0x8886
#define LOCAL_GL_FLOAT_RG32_NV                               0x8887
#define LOCAL_GL_FLOAT_RGB16_NV                              0x8888
#define LOCAL_GL_FLOAT_RGB32_NV                              0x8889
#define LOCAL_GL_FLOAT_RGBA16_NV                             0x888A
#define LOCAL_GL_FLOAT_RGBA32_NV                             0x888B
#define LOCAL_GL_FLOAT_RGBA_MODE_NV                          0x888E
#define LOCAL_GL_FLOAT_RGBA_NV                               0x8883
#define LOCAL_GL_FLOAT_RGB_NV                                0x8882
#define LOCAL_GL_FLOAT_RG_NV                                 0x8881
#define LOCAL_GL_FLOAT_R_NV                                  0x8880
#define LOCAL_GL_FLOAT_VEC2                                  0x8B50
#define LOCAL_GL_FLOAT_VEC2_ARB                              0x8B50
#define LOCAL_GL_FLOAT_VEC3                                  0x8B51
#define LOCAL_GL_FLOAT_VEC3_ARB                              0x8B51
#define LOCAL_GL_FLOAT_VEC4                                  0x8B52
#define LOCAL_GL_FLOAT_VEC4_ARB                              0x8B52
#define LOCAL_GL_FOG                                         0x0B60
#define LOCAL_GL_FOG_BIT                                     0x00000080
#define LOCAL_GL_FOG_COLOR                                   0x0B66
#define LOCAL_GL_FOG_COORD                                   0x8451
#define LOCAL_GL_FOG_COORDINATE                              0x8451
#define LOCAL_GL_FOG_COORDINATE_ARRAY                        0x8457
#define LOCAL_GL_FOG_COORDINATE_ARRAY_BUFFER_BINDING         0x889D
#define LOCAL_GL_FOG_COORDINATE_ARRAY_BUFFER_BINDING_ARB     0x889D
#define LOCAL_GL_FOG_COORDINATE_ARRAY_EXT                    0x8457
#define LOCAL_GL_FOG_COORDINATE_ARRAY_LIST_IBM               103076
#define LOCAL_GL_FOG_COORDINATE_ARRAY_LIST_STRIDE_IBM        103086
#define LOCAL_GL_FOG_COORDINATE_ARRAY_POINTER                0x8456
#define LOCAL_GL_FOG_COORDINATE_ARRAY_POINTER_EXT            0x8456
#define LOCAL_GL_FOG_COORDINATE_ARRAY_STRIDE                 0x8455
#define LOCAL_GL_FOG_COORDINATE_ARRAY_STRIDE_EXT             0x8455
#define LOCAL_GL_FOG_COORDINATE_ARRAY_TYPE                   0x8454
#define LOCAL_GL_FOG_COORDINATE_ARRAY_TYPE_EXT               0x8454
#define LOCAL_GL_FOG_COORDINATE_EXT                          0x8451
#define LOCAL_GL_FOG_COORDINATE_SOURCE                       0x8450
#define LOCAL_GL_FOG_COORDINATE_SOURCE_EXT                   0x8450
#define LOCAL_GL_FOG_COORD_ARRAY                             0x8457
#define LOCAL_GL_FOG_COORD_ARRAY_ADDRESS_NV                  0x8F28
#define LOCAL_GL_FOG_COORD_ARRAY_BUFFER_BINDING              0x889D
#define LOCAL_GL_FOG_COORD_ARRAY_LENGTH_NV                   0x8F32
#define LOCAL_GL_FOG_COORD_ARRAY_POINTER                     0x8456
#define LOCAL_GL_FOG_COORD_ARRAY_STRIDE                      0x8455
#define LOCAL_GL_FOG_COORD_ARRAY_TYPE                        0x8454
#define LOCAL_GL_FOG_COORD_SRC                               0x8450
#define LOCAL_GL_FOG_DENSITY                                 0x0B62
#define LOCAL_GL_FOG_DISTANCE_MODE_NV                        0x855A
#define LOCAL_GL_FOG_END                                     0x0B64
#define LOCAL_GL_FOG_FUNC_POINTS_SGIS                        0x812B
#define LOCAL_GL_FOG_FUNC_SGIS                               0x812A
#define LOCAL_GL_FOG_HINT                                    0x0C54
#define LOCAL_GL_FOG_INDEX                                   0x0B61
#define LOCAL_GL_FOG_MODE                                    0x0B65
#define LOCAL_GL_FOG_OFFSET_SGIX                             0x8198
#define LOCAL_GL_FOG_OFFSET_VALUE_SGIX                       0x8199
#define LOCAL_GL_FOG_SPECULAR_TEXTURE_WIN                    0x80EC
#define LOCAL_GL_FOG_START                                   0x0B63
#define LOCAL_GL_FONT_ASCENDER_BIT_NV                        0x00200000
#define LOCAL_GL_FONT_DESCENDER_BIT_NV                       0x00400000
#define LOCAL_GL_FONT_GLYPHS_AVAILABLE_NV                    0x9368
#define LOCAL_GL_FONT_HAS_KERNING_BIT_NV                     0x10000000
#define LOCAL_GL_FONT_HEIGHT_BIT_NV                          0x00800000
#define LOCAL_GL_FONT_MAX_ADVANCE_HEIGHT_BIT_NV              0x02000000
#define LOCAL_GL_FONT_MAX_ADVANCE_WIDTH_BIT_NV               0x01000000
#define LOCAL_GL_FONT_NUM_GLYPH_INDICES_BIT_NV               0x20000000
#define LOCAL_GL_FONT_TARGET_UNAVAILABLE_NV                  0x9369
#define LOCAL_GL_FONT_UNAVAILABLE_NV                         0x936A
#define LOCAL_GL_FONT_UNDERLINE_POSITION_BIT_NV              0x04000000
#define LOCAL_GL_FONT_UNDERLINE_THICKNESS_BIT_NV             0x08000000
#define LOCAL_GL_FONT_UNINTELLIGIBLE_NV                      0x936B
#define LOCAL_GL_FONT_UNITS_PER_EM_BIT_NV                    0x00100000
#define LOCAL_GL_FONT_X_MAX_BOUNDS_BIT_NV                    0x00040000
#define LOCAL_GL_FONT_X_MIN_BOUNDS_BIT_NV                    0x00010000
#define LOCAL_GL_FONT_Y_MAX_BOUNDS_BIT_NV                    0x00080000
#define LOCAL_GL_FONT_Y_MIN_BOUNDS_BIT_NV                    0x00020000
#define LOCAL_GL_FORCE_BLUE_TO_ONE_NV                        0x8860
#define LOCAL_GL_FORMAT_SUBSAMPLE_244_244_OML                0x8983
#define LOCAL_GL_FORMAT_SUBSAMPLE_24_24_OML                  0x8982
#define LOCAL_GL_FOVEATION_ENABLE_BIT_QCOM                   0x00000001
#define LOCAL_GL_FOVEATION_SCALED_BIN_METHOD_BIT_QCOM        0x00000002
#define LOCAL_GL_FOVEATION_SUBSAMPLED_LAYOUT_METHOD_BIT_QCOM 0x00000004
#define LOCAL_GL_FRACTIONAL_EVEN                             0x8E7C
#define LOCAL_GL_FRACTIONAL_EVEN_EXT                         0x8E7C
#define LOCAL_GL_FRACTIONAL_EVEN_OES                         0x8E7C
#define LOCAL_GL_FRACTIONAL_ODD                              0x8E7B
#define LOCAL_GL_FRACTIONAL_ODD_EXT                          0x8E7B
#define LOCAL_GL_FRACTIONAL_ODD_OES                          0x8E7B
#define LOCAL_GL_FRAGMENTS_INSTRUMENT_COUNTERS_SGIX          0x8314
#define LOCAL_GL_FRAGMENTS_INSTRUMENT_MAX_SGIX               0x8315
#define LOCAL_GL_FRAGMENTS_INSTRUMENT_SGIX                   0x8313
#define LOCAL_GL_FRAGMENT_ALPHA_MODULATE_IMG                 0x8C08
#define LOCAL_GL_FRAGMENT_COLOR_EXT                          0x834C
#define LOCAL_GL_FRAGMENT_COLOR_MATERIAL_FACE_SGIX           0x8402
#define LOCAL_GL_FRAGMENT_COLOR_MATERIAL_PARAMETER_SGIX      0x8403
#define LOCAL_GL_FRAGMENT_COLOR_MATERIAL_SGIX                0x8401
#define LOCAL_GL_FRAGMENT_COVERAGE_COLOR_NV                  0x92DE
#define LOCAL_GL_FRAGMENT_COVERAGE_TO_COLOR_NV               0x92DD
#define LOCAL_GL_FRAGMENT_DEPTH                              0x8452
#define LOCAL_GL_FRAGMENT_DEPTH_EXT                          0x8452
#define LOCAL_GL_FRAGMENT_INPUT_NV                           0x936D
#define LOCAL_GL_FRAGMENT_INTERPOLATION_OFFSET_BITS          0x8E5D
#define LOCAL_GL_FRAGMENT_INTERPOLATION_OFFSET_BITS_OES      0x8E5D
#define LOCAL_GL_FRAGMENT_LIGHT0_SGIX                        0x840C
#define LOCAL_GL_FRAGMENT_LIGHT1_SGIX                        0x840D
#define LOCAL_GL_FRAGMENT_LIGHT2_SGIX                        0x840E
#define LOCAL_GL_FRAGMENT_LIGHT3_SGIX                        0x840F
#define LOCAL_GL_FRAGMENT_LIGHT4_SGIX                        0x8410
#define LOCAL_GL_FRAGMENT_LIGHT5_SGIX                        0x8411
#define LOCAL_GL_FRAGMENT_LIGHT6_SGIX                        0x8412
#define LOCAL_GL_FRAGMENT_LIGHT7_SGIX                        0x8413
#define LOCAL_GL_FRAGMENT_LIGHTING_SGIX                      0x8400
#define LOCAL_GL_FRAGMENT_LIGHT_MODEL_AMBIENT_SGIX           0x840A
#define LOCAL_GL_FRAGMENT_LIGHT_MODEL_LOCAL_VIEWER_SGIX      0x8408
#define LOCAL_GL_FRAGMENT_LIGHT_MODEL_NORMAL_INTERPOLATION_SGIX      0x840B
#define LOCAL_GL_FRAGMENT_LIGHT_MODEL_TWO_SIDE_SGIX          0x8409
#define LOCAL_GL_FRAGMENT_MATERIAL_EXT                       0x8349
#define LOCAL_GL_FRAGMENT_NORMAL_EXT                         0x834A
#define LOCAL_GL_FRAGMENT_PROGRAM_ARB                        0x8804
#define LOCAL_GL_FRAGMENT_PROGRAM_BINDING_NV                 0x8873
#define LOCAL_GL_FRAGMENT_PROGRAM_CALLBACK_DATA_MESA         0x8BB3
#define LOCAL_GL_FRAGMENT_PROGRAM_CALLBACK_FUNC_MESA         0x8BB2
#define LOCAL_GL_FRAGMENT_PROGRAM_CALLBACK_MESA              0x8BB1
#define LOCAL_GL_FRAGMENT_PROGRAM_INTERPOLATION_OFFSET_BITS_NV       0x8E5D
#define LOCAL_GL_FRAGMENT_PROGRAM_NV                         0x8870
#define LOCAL_GL_FRAGMENT_PROGRAM_PARAMETER_BUFFER_NV        0x8DA4
#define LOCAL_GL_FRAGMENT_PROGRAM_POSITION_MESA              0x8BB0
#define LOCAL_GL_FRAGMENT_SHADER                             0x8B30
#define LOCAL_GL_FRAGMENT_SHADER_ARB                         0x8B30
#define LOCAL_GL_FRAGMENT_SHADER_ATI                         0x8920
#define LOCAL_GL_FRAGMENT_SHADER_BIT                         0x00000002
#define LOCAL_GL_FRAGMENT_SHADER_BIT_EXT                     0x00000002
#define LOCAL_GL_FRAGMENT_SHADER_DERIVATIVE_HINT             0x8B8B
#define LOCAL_GL_FRAGMENT_SHADER_DERIVATIVE_HINT_ARB         0x8B8B
#define LOCAL_GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES         0x8B8B
#define LOCAL_GL_FRAGMENT_SHADER_DISCARDS_SAMPLES_EXT        0x8A52
#define LOCAL_GL_FRAGMENT_SHADER_FRAMEBUFFER_FETCH_MRT_ARM   0x8F66
#define LOCAL_GL_FRAGMENT_SHADER_INVOCATIONS                 0x82F4
#define LOCAL_GL_FRAGMENT_SHADER_INVOCATIONS_ARB             0x82F4
#define LOCAL_GL_FRAGMENT_SUBROUTINE                         0x92EC
#define LOCAL_GL_FRAGMENT_SUBROUTINE_UNIFORM                 0x92F2
#define LOCAL_GL_FRAGMENT_TEXTURE                            0x829F
#define LOCAL_GL_FRAMEBUFFER                                 0x8D40
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE           0x8215
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_ANGLE                0x93A3
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE            0x8214
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING       0x8210
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT   0x8210
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE       0x8211
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT   0x8211
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE           0x8216
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE           0x8213
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_LAYERED              0x8DA7
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_LAYERED_ARB          0x8DA7
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_LAYERED_EXT          0x8DA7
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_LAYERED_OES          0x8DA7
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME          0x8CD1
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME_EXT      0x8CD1
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME_OES      0x8CD1
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE          0x8CD0
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE_EXT      0x8CD0
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE_OES      0x8CD0
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE             0x8212
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE         0x8217
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_3D_ZOFFSET_EXT       0x8CD4
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_3D_ZOFFSET_OES       0x8CD4
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_BASE_VIEW_INDEX_OVR  0x9632
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE        0x8CD3
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE_EXT    0x8CD3
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE_OES    0x8CD3
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER        0x8CD4
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER_EXT    0x8CD4
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL        0x8CD2
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL_EXT    0x8CD2
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL_OES    0x8CD2
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_NUM_VIEWS_OVR        0x9630
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_SAMPLES_EXT  0x8D6C
#define LOCAL_GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_SCALE_IMG    0x913F
#define LOCAL_GL_FRAMEBUFFER_BARRIER_BIT                     0x00000400
#define LOCAL_GL_FRAMEBUFFER_BARRIER_BIT_EXT                 0x00000400
#define LOCAL_GL_FRAMEBUFFER_BINDING                         0x8CA6
#define LOCAL_GL_FRAMEBUFFER_BINDING_ANGLE                   0x8CA6
#define LOCAL_GL_FRAMEBUFFER_BINDING_EXT                     0x8CA6
#define LOCAL_GL_FRAMEBUFFER_BINDING_OES                     0x8CA6
#define LOCAL_GL_FRAMEBUFFER_BLEND                           0x828B
#define LOCAL_GL_FRAMEBUFFER_COMPLETE                        0x8CD5
#define LOCAL_GL_FRAMEBUFFER_COMPLETE_EXT                    0x8CD5
#define LOCAL_GL_FRAMEBUFFER_COMPLETE_OES                    0x8CD5
#define LOCAL_GL_FRAMEBUFFER_DEFAULT                         0x8218
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_FIXED_SAMPLE_LOCATIONS  0x9314
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_HEIGHT                  0x9311
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_LAYERS                  0x9312
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_LAYERS_EXT              0x9312
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_LAYERS_OES              0x9312
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_SAMPLES                 0x9313
#define LOCAL_GL_FRAMEBUFFER_DEFAULT_WIDTH                   0x9310
#define LOCAL_GL_FRAMEBUFFER_EXT                             0x8D40
#define LOCAL_GL_FRAMEBUFFER_FETCH_NONCOHERENT_QCOM          0x96A2
#define LOCAL_GL_FRAMEBUFFER_FLIP_X_MESA                     0x8BBC
#define LOCAL_GL_FRAMEBUFFER_FLIP_Y_MESA                     0x8BBB
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT           0x8CD6
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT_EXT       0x8CD6
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT_OES       0x8CD6
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS           0x8CD9
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS_EXT       0x8CD9
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS_OES       0x8CD9
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER          0x8CDB
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER_EXT      0x8CDB
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER_OES      0x8CDB
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_FORMATS_EXT          0x8CDA
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_FORMATS_OES          0x8CDA
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_FOVEATION_QCOM       0x8BFF
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_INSUFFICIENT_SHADER_COMBINED_LOCAL_STORAGE_EXT       0x9652
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_COUNT_ARB      0x8DA9
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_COUNT_EXT      0x8DA9
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS        0x8DA8
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS_ARB    0x8DA8
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS_EXT    0x8DA8
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS_OES    0x8DA8
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT   0x8CD7
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT_EXT       0x8CD7
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT_OES       0x8CD7
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE          0x8D56
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_AND_DOWNSAMPLE_IMG       0x913C
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_ANGLE    0x8D56
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_APPLE    0x8D56
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_EXT      0x8D56
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_IMG      0x9134
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_NV       0x8D56
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER          0x8CDC
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER_EXT      0x8CDC
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER_OES      0x8CDC
#define LOCAL_GL_FRAMEBUFFER_INCOMPLETE_VIEW_TARGETS_OVR     0x9633
#define LOCAL_GL_FRAMEBUFFER_OES                             0x8D40
#define LOCAL_GL_FRAMEBUFFER_PROGRAMMABLE_SAMPLE_LOCATIONS_ARB       0x9342
#define LOCAL_GL_FRAMEBUFFER_PROGRAMMABLE_SAMPLE_LOCATIONS_NV        0x9342
#define LOCAL_GL_FRAMEBUFFER_RENDERABLE                      0x8289
#define LOCAL_GL_FRAMEBUFFER_RENDERABLE_LAYERED              0x828A
#define LOCAL_GL_FRAMEBUFFER_SAMPLE_LOCATION_PIXEL_GRID_ARB  0x9343
#define LOCAL_GL_FRAMEBUFFER_SAMPLE_LOCATION_PIXEL_GRID_NV   0x9343
#define LOCAL_GL_FRAMEBUFFER_SRGB                            0x8DB9
#define LOCAL_GL_FRAMEBUFFER_SRGB_CAPABLE_EXT                0x8DBA
#define LOCAL_GL_FRAMEBUFFER_SRGB_EXT                        0x8DB9
#define LOCAL_GL_FRAMEBUFFER_SWAP_XY_MESA                    0x8BBD
#define LOCAL_GL_FRAMEBUFFER_UNDEFINED                       0x8219
#define LOCAL_GL_FRAMEBUFFER_UNDEFINED_OES                   0x8219
#define LOCAL_GL_FRAMEBUFFER_UNSUPPORTED                     0x8CDD
#define LOCAL_GL_FRAMEBUFFER_UNSUPPORTED_EXT                 0x8CDD
#define LOCAL_GL_FRAMEBUFFER_UNSUPPORTED_OES                 0x8CDD
#define LOCAL_GL_FRAMEZOOM_FACTOR_SGIX                       0x818C
#define LOCAL_GL_FRAMEZOOM_SGIX                              0x818B
#define LOCAL_GL_FRAME_NV                                    0x8E26
#define LOCAL_GL_FRONT                                       0x0404
#define LOCAL_GL_FRONT_AND_BACK                              0x0408
#define LOCAL_GL_FRONT_FACE                                  0x0B46
#define LOCAL_GL_FRONT_FACE_COMMAND_NV                       0x0012
#define LOCAL_GL_FRONT_LEFT                                  0x0400
#define LOCAL_GL_FRONT_RIGHT                                 0x0401
#define LOCAL_GL_FULL_RANGE_EXT                              0x87E1
#define LOCAL_GL_FULL_STIPPLE_HINT_PGI                       0x1A219
#define LOCAL_GL_FULL_SUPPORT                                0x82B7
#define LOCAL_GL_FUNC_ADD                                    0x8006
#define LOCAL_GL_FUNC_ADD_EXT                                0x8006
#define LOCAL_GL_FUNC_ADD_OES                                0x8006
#define LOCAL_GL_FUNC_REVERSE_SUBTRACT                       0x800B
#define LOCAL_GL_FUNC_REVERSE_SUBTRACT_EXT                   0x800B
#define LOCAL_GL_FUNC_REVERSE_SUBTRACT_OES                   0x800B
#define LOCAL_GL_FUNC_SUBTRACT                               0x800A
#define LOCAL_GL_FUNC_SUBTRACT_EXT                           0x800A
#define LOCAL_GL_FUNC_SUBTRACT_OES                           0x800A
#define LOCAL_GL_GCCSO_SHADER_BINARY_FJ                      0x9260
#define LOCAL_GL_GENERATE_MIPMAP                             0x8191
#define LOCAL_GL_GENERATE_MIPMAP_HINT                        0x8192
#define LOCAL_GL_GENERATE_MIPMAP_HINT_SGIS                   0x8192
#define LOCAL_GL_GENERATE_MIPMAP_SGIS                        0x8191
#define LOCAL_GL_GENERIC_ATTRIB_NV                           0x8C7D
#define LOCAL_GL_GEOMETRY_DEFORMATION_BIT_SGIX               0x00000002
#define LOCAL_GL_GEOMETRY_DEFORMATION_SGIX                   0x8194
#define LOCAL_GL_GEOMETRY_INPUT_TYPE                         0x8917
#define LOCAL_GL_GEOMETRY_INPUT_TYPE_ARB                     0x8DDB
#define LOCAL_GL_GEOMETRY_INPUT_TYPE_EXT                     0x8DDB
#define LOCAL_GL_GEOMETRY_LINKED_INPUT_TYPE_EXT              0x8917
#define LOCAL_GL_GEOMETRY_LINKED_INPUT_TYPE_OES              0x8917
#define LOCAL_GL_GEOMETRY_LINKED_OUTPUT_TYPE_EXT             0x8918
#define LOCAL_GL_GEOMETRY_LINKED_OUTPUT_TYPE_OES             0x8918
#define LOCAL_GL_GEOMETRY_LINKED_VERTICES_OUT_EXT            0x8916
#define LOCAL_GL_GEOMETRY_LINKED_VERTICES_OUT_OES            0x8916
#define LOCAL_GL_GEOMETRY_OUTPUT_TYPE                        0x8918
#define LOCAL_GL_GEOMETRY_OUTPUT_TYPE_ARB                    0x8DDC
#define LOCAL_GL_GEOMETRY_OUTPUT_TYPE_EXT                    0x8DDC
#define LOCAL_GL_GEOMETRY_PROGRAM_NV                         0x8C26
#define LOCAL_GL_GEOMETRY_PROGRAM_PARAMETER_BUFFER_NV        0x8DA3
#define LOCAL_GL_GEOMETRY_SHADER                             0x8DD9
#define LOCAL_GL_GEOMETRY_SHADER_ARB                         0x8DD9
#define LOCAL_GL_GEOMETRY_SHADER_BIT                         0x00000004
#define LOCAL_GL_GEOMETRY_SHADER_BIT_EXT                     0x00000004
#define LOCAL_GL_GEOMETRY_SHADER_BIT_OES                     0x00000004
#define LOCAL_GL_GEOMETRY_SHADER_EXT                         0x8DD9
#define LOCAL_GL_GEOMETRY_SHADER_INVOCATIONS                 0x887F
#define LOCAL_GL_GEOMETRY_SHADER_INVOCATIONS_EXT             0x887F
#define LOCAL_GL_GEOMETRY_SHADER_INVOCATIONS_OES             0x887F
#define LOCAL_GL_GEOMETRY_SHADER_OES                         0x8DD9
#define LOCAL_GL_GEOMETRY_SHADER_PRIMITIVES_EMITTED          0x82F3
#define LOCAL_GL_GEOMETRY_SHADER_PRIMITIVES_EMITTED_ARB      0x82F3
#define LOCAL_GL_GEOMETRY_SUBROUTINE                         0x92EB
#define LOCAL_GL_GEOMETRY_SUBROUTINE_UNIFORM                 0x92F1
#define LOCAL_GL_GEOMETRY_TEXTURE                            0x829E
#define LOCAL_GL_GEOMETRY_VERTICES_OUT                       0x8916
#define LOCAL_GL_GEOMETRY_VERTICES_OUT_ARB                   0x8DDA
#define LOCAL_GL_GEOMETRY_VERTICES_OUT_EXT                   0x8DDA
#define LOCAL_GL_GEQUAL                                      0x0206
#define LOCAL_GL_GET_TEXTURE_IMAGE_FORMAT                    0x8291
#define LOCAL_GL_GET_TEXTURE_IMAGE_TYPE                      0x8292
#define LOCAL_GL_GLOBAL_ALPHA_FACTOR_SUN                     0x81DA
#define LOCAL_GL_GLOBAL_ALPHA_SUN                            0x81D9
#define LOCAL_GL_GLYPH_HAS_KERNING_BIT_NV                    0x100
#define LOCAL_GL_GLYPH_HEIGHT_BIT_NV                         0x02
#define LOCAL_GL_GLYPH_HORIZONTAL_BEARING_ADVANCE_BIT_NV     0x10
#define LOCAL_GL_GLYPH_HORIZONTAL_BEARING_X_BIT_NV           0x04
#define LOCAL_GL_GLYPH_HORIZONTAL_BEARING_Y_BIT_NV           0x08
#define LOCAL_GL_GLYPH_VERTICAL_BEARING_ADVANCE_BIT_NV       0x80
#define LOCAL_GL_GLYPH_VERTICAL_BEARING_X_BIT_NV             0x20
#define LOCAL_GL_GLYPH_VERTICAL_BEARING_Y_BIT_NV             0x40
#define LOCAL_GL_GLYPH_WIDTH_BIT_NV                          0x01
#define LOCAL_GL_GPU_ADDRESS_NV                              0x8F34
#define LOCAL_GL_GPU_DISJOINT_EXT                            0x8FBB
#define LOCAL_GL_GPU_MEMORY_INFO_CURRENT_AVAILABLE_VIDMEM_NVX        0x9049
#define LOCAL_GL_GPU_MEMORY_INFO_DEDICATED_VIDMEM_NVX        0x9047
#define LOCAL_GL_GPU_MEMORY_INFO_EVICTED_MEMORY_NVX          0x904B
#define LOCAL_GL_GPU_MEMORY_INFO_EVICTION_COUNT_NVX          0x904A
#define LOCAL_GL_GPU_MEMORY_INFO_TOTAL_AVAILABLE_MEMORY_NVX  0x9048
#define LOCAL_GL_GPU_OPTIMIZED_QCOM                          0x8FB2
#define LOCAL_GL_GREATER                                     0x0204
#define LOCAL_GL_GREEN                                       0x1904
#define LOCAL_GL_GREEN_BIAS                                  0x0D19
#define LOCAL_GL_GREEN_BITS                                  0x0D53
#define LOCAL_GL_GREEN_BIT_ATI                               0x00000002
#define LOCAL_GL_GREEN_INTEGER                               0x8D95
#define LOCAL_GL_GREEN_INTEGER_EXT                           0x8D95
#define LOCAL_GL_GREEN_MAX_CLAMP_INGR                        0x8565
#define LOCAL_GL_GREEN_MIN_CLAMP_INGR                        0x8561
#define LOCAL_GL_GREEN_NV                                    0x1904
#define LOCAL_GL_GREEN_SCALE                                 0x0D18
#define LOCAL_GL_GS_PROGRAM_BINARY_MTK                       0x9641
#define LOCAL_GL_GS_SHADER_BINARY_MTK                        0x9640
#define LOCAL_GL_GUILTY_CONTEXT_RESET                        0x8253
#define LOCAL_GL_GUILTY_CONTEXT_RESET_ARB                    0x8253
#define LOCAL_GL_GUILTY_CONTEXT_RESET_EXT                    0x8253
#define LOCAL_GL_GUILTY_CONTEXT_RESET_KHR                    0x8253
#define LOCAL_GL_HALF_APPLE                                  0x140B
#define LOCAL_GL_HALF_BIAS_NEGATE_NV                         0x853B
#define LOCAL_GL_HALF_BIAS_NORMAL_NV                         0x853A
#define LOCAL_GL_HALF_BIT_ATI                                0x00000008
#define LOCAL_GL_HALF_FLOAT                                  0x140B
#define LOCAL_GL_HALF_FLOAT_ARB                              0x140B
#define LOCAL_GL_HALF_FLOAT_NV                               0x140B
#define LOCAL_GL_HALF_FLOAT_OES                              0x8D61
#define LOCAL_GL_HANDLE_TYPE_D3D11_IMAGE_EXT                 0x958B
#define LOCAL_GL_HANDLE_TYPE_D3D11_IMAGE_KMT_EXT             0x958C
#define LOCAL_GL_HANDLE_TYPE_D3D12_FENCE_EXT                 0x9594
#define LOCAL_GL_HANDLE_TYPE_D3D12_RESOURCE_EXT              0x958A
#define LOCAL_GL_HANDLE_TYPE_D3D12_TILEPOOL_EXT              0x9589
#define LOCAL_GL_HANDLE_TYPE_OPAQUE_FD_EXT                   0x9586
#define LOCAL_GL_HANDLE_TYPE_OPAQUE_WIN32_EXT                0x9587
#define LOCAL_GL_HANDLE_TYPE_OPAQUE_WIN32_KMT_EXT            0x9588
#define LOCAL_GL_HARDLIGHT                                   0x929B
#define LOCAL_GL_HARDLIGHT_KHR                               0x929B
#define LOCAL_GL_HARDLIGHT_NV                                0x929B
#define LOCAL_GL_HARDMIX_NV                                  0x92A9
#define LOCAL_GL_HIGH_FLOAT                                  0x8DF2
#define LOCAL_GL_HIGH_INT                                    0x8DF5
#define LOCAL_GL_HILO16_NV                                   0x86F8
#define LOCAL_GL_HILO8_NV                                    0x885E
#define LOCAL_GL_HILO_NV                                     0x86F4
#define LOCAL_GL_HINT_BIT                                    0x00008000
#define LOCAL_GL_HISTOGRAM                                   0x8024
#define LOCAL_GL_HISTOGRAM_ALPHA_SIZE                        0x802B
#define LOCAL_GL_HISTOGRAM_ALPHA_SIZE_EXT                    0x802B
#define LOCAL_GL_HISTOGRAM_BLUE_SIZE                         0x802A
#define LOCAL_GL_HISTOGRAM_BLUE_SIZE_EXT                     0x802A
#define LOCAL_GL_HISTOGRAM_EXT                               0x8024
#define LOCAL_GL_HISTOGRAM_FORMAT                            0x8027
#define LOCAL_GL_HISTOGRAM_FORMAT_EXT                        0x8027
#define LOCAL_GL_HISTOGRAM_GREEN_SIZE                        0x8029
#define LOCAL_GL_HISTOGRAM_GREEN_SIZE_EXT                    0x8029
#define LOCAL_GL_HISTOGRAM_LUMINANCE_SIZE                    0x802C
#define LOCAL_GL_HISTOGRAM_LUMINANCE_SIZE_EXT                0x802C
#define LOCAL_GL_HISTOGRAM_RED_SIZE                          0x8028
#define LOCAL_GL_HISTOGRAM_RED_SIZE_EXT                      0x8028
#define LOCAL_GL_HISTOGRAM_SINK                              0x802D
#define LOCAL_GL_HISTOGRAM_SINK_EXT                          0x802D
#define LOCAL_GL_HISTOGRAM_WIDTH                             0x8026
#define LOCAL_GL_HISTOGRAM_WIDTH_EXT                         0x8026
#define LOCAL_GL_HI_BIAS_NV                                  0x8714
#define LOCAL_GL_HI_SCALE_NV                                 0x870E
#define LOCAL_GL_HORIZONTAL_LINE_TO_NV                       0x06
#define LOCAL_GL_HSL_COLOR                                   0x92AF
#define LOCAL_GL_HSL_COLOR_KHR                               0x92AF
#define LOCAL_GL_HSL_COLOR_NV                                0x92AF
#define LOCAL_GL_HSL_HUE                                     0x92AD
#define LOCAL_GL_HSL_HUE_KHR                                 0x92AD
#define LOCAL_GL_HSL_HUE_NV                                  0x92AD
#define LOCAL_GL_HSL_LUMINOSITY                              0x92B0
#define LOCAL_GL_HSL_LUMINOSITY_KHR                          0x92B0
#define LOCAL_GL_HSL_LUMINOSITY_NV                           0x92B0
#define LOCAL_GL_HSL_SATURATION                              0x92AE
#define LOCAL_GL_HSL_SATURATION_KHR                          0x92AE
#define LOCAL_GL_HSL_SATURATION_NV                           0x92AE
#define LOCAL_GL_IDENTITY_NV                                 0x862A
#define LOCAL_GL_IGNORE_BORDER_HP                            0x8150
#define LOCAL_GL_IMAGE_1D                                    0x904C
#define LOCAL_GL_IMAGE_1D_ARRAY                              0x9052
#define LOCAL_GL_IMAGE_1D_ARRAY_EXT                          0x9052
#define LOCAL_GL_IMAGE_1D_EXT                                0x904C
#define LOCAL_GL_IMAGE_2D                                    0x904D
#define LOCAL_GL_IMAGE_2D_ARRAY                              0x9053
#define LOCAL_GL_IMAGE_2D_ARRAY_EXT                          0x9053
#define LOCAL_GL_IMAGE_2D_EXT                                0x904D
#define LOCAL_GL_IMAGE_2D_MULTISAMPLE                        0x9055
#define LOCAL_GL_IMAGE_2D_MULTISAMPLE_ARRAY                  0x9056
#define LOCAL_GL_IMAGE_2D_MULTISAMPLE_ARRAY_EXT              0x9056
#define LOCAL_GL_IMAGE_2D_MULTISAMPLE_EXT                    0x9055
#define LOCAL_GL_IMAGE_2D_RECT                               0x904F
#define LOCAL_GL_IMAGE_2D_RECT_EXT                           0x904F
#define LOCAL_GL_IMAGE_3D                                    0x904E
#define LOCAL_GL_IMAGE_3D_EXT                                0x904E
#define LOCAL_GL_IMAGE_BINDING_ACCESS                        0x8F3E
#define LOCAL_GL_IMAGE_BINDING_ACCESS_EXT                    0x8F3E
#define LOCAL_GL_IMAGE_BINDING_FORMAT                        0x906E
#define LOCAL_GL_IMAGE_BINDING_FORMAT_EXT                    0x906E
#define LOCAL_GL_IMAGE_BINDING_LAYER                         0x8F3D
#define LOCAL_GL_IMAGE_BINDING_LAYERED                       0x8F3C
#define LOCAL_GL_IMAGE_BINDING_LAYERED_EXT                   0x8F3C
#define LOCAL_GL_IMAGE_BINDING_LAYER_EXT                     0x8F3D
#define LOCAL_GL_IMAGE_BINDING_LEVEL                         0x8F3B
#define LOCAL_GL_IMAGE_BINDING_LEVEL_EXT                     0x8F3B
#define LOCAL_GL_IMAGE_BINDING_NAME                          0x8F3A
#define LOCAL_GL_IMAGE_BINDING_NAME_EXT                      0x8F3A
#define LOCAL_GL_IMAGE_BUFFER                                0x9051
#define LOCAL_GL_IMAGE_BUFFER_EXT                            0x9051
#define LOCAL_GL_IMAGE_BUFFER_OES                            0x9051
#define LOCAL_GL_IMAGE_CLASS_10_10_10_2                      0x82C3
#define LOCAL_GL_IMAGE_CLASS_11_11_10                        0x82C2
#define LOCAL_GL_IMAGE_CLASS_1_X_16                          0x82BE
#define LOCAL_GL_IMAGE_CLASS_1_X_32                          0x82BB
#define LOCAL_GL_IMAGE_CLASS_1_X_8                           0x82C1
#define LOCAL_GL_IMAGE_CLASS_2_X_16                          0x82BD
#define LOCAL_GL_IMAGE_CLASS_2_X_32                          0x82BA
#define LOCAL_GL_IMAGE_CLASS_2_X_8                           0x82C0
#define LOCAL_GL_IMAGE_CLASS_4_X_16                          0x82BC
#define LOCAL_GL_IMAGE_CLASS_4_X_32                          0x82B9
#define LOCAL_GL_IMAGE_CLASS_4_X_8                           0x82BF
#define LOCAL_GL_IMAGE_COMPATIBILITY_CLASS                   0x82A8
#define LOCAL_GL_IMAGE_CUBE                                  0x9050
#define LOCAL_GL_IMAGE_CUBE_EXT                              0x9050
#define LOCAL_GL_IMAGE_CUBE_MAP_ARRAY                        0x9054
#define LOCAL_GL_IMAGE_CUBE_MAP_ARRAY_EXT                    0x9054
#define LOCAL_GL_IMAGE_CUBE_MAP_ARRAY_OES                    0x9054
#define LOCAL_GL_IMAGE_CUBIC_WEIGHT_HP                       0x815E
#define LOCAL_GL_IMAGE_FORMAT_COMPATIBILITY_BY_CLASS         0x90C9
#define LOCAL_GL_IMAGE_FORMAT_COMPATIBILITY_BY_SIZE          0x90C8
#define LOCAL_GL_IMAGE_FORMAT_COMPATIBILITY_TYPE             0x90C7
#define LOCAL_GL_IMAGE_MAG_FILTER_HP                         0x815C
#define LOCAL_GL_IMAGE_MIN_FILTER_HP                         0x815D
#define LOCAL_GL_IMAGE_PIXEL_FORMAT                          0x82A9
#define LOCAL_GL_IMAGE_PIXEL_TYPE                            0x82AA
#define LOCAL_GL_IMAGE_ROTATE_ANGLE_HP                       0x8159
#define LOCAL_GL_IMAGE_ROTATE_ORIGIN_X_HP                    0x815A
#define LOCAL_GL_IMAGE_ROTATE_ORIGIN_Y_HP                    0x815B
#define LOCAL_GL_IMAGE_SCALE_X_HP                            0x8155
#define LOCAL_GL_IMAGE_SCALE_Y_HP                            0x8156
#define LOCAL_GL_IMAGE_TEXEL_SIZE                            0x82A7
#define LOCAL_GL_IMAGE_TRANSFORM_2D_HP                       0x8161
#define LOCAL_GL_IMAGE_TRANSLATE_X_HP                        0x8157
#define LOCAL_GL_IMAGE_TRANSLATE_Y_HP                        0x8158
#define LOCAL_GL_IMPLEMENTATION_COLOR_READ_FORMAT            0x8B9B
#define LOCAL_GL_IMPLEMENTATION_COLOR_READ_FORMAT_OES        0x8B9B
#define LOCAL_GL_IMPLEMENTATION_COLOR_READ_TYPE              0x8B9A
#define LOCAL_GL_IMPLEMENTATION_COLOR_READ_TYPE_OES          0x8B9A
#define LOCAL_GL_INCLUSIVE_EXT                               0x8F10
#define LOCAL_GL_INCR                                        0x1E02
#define LOCAL_GL_INCR_WRAP                                   0x8507
#define LOCAL_GL_INCR_WRAP_EXT                               0x8507
#define LOCAL_GL_INCR_WRAP_OES                               0x8507
#define LOCAL_GL_INDEX                                       0x8222
#define LOCAL_GL_INDEX_ARRAY                                 0x8077
#define LOCAL_GL_INDEX_ARRAY_ADDRESS_NV                      0x8F24
#define LOCAL_GL_INDEX_ARRAY_BUFFER_BINDING                  0x8899
#define LOCAL_GL_INDEX_ARRAY_BUFFER_BINDING_ARB              0x8899
#define LOCAL_GL_INDEX_ARRAY_COUNT_EXT                       0x8087
#define LOCAL_GL_INDEX_ARRAY_EXT                             0x8077
#define LOCAL_GL_INDEX_ARRAY_LENGTH_NV                       0x8F2E
#define LOCAL_GL_INDEX_ARRAY_LIST_IBM                        103073
#define LOCAL_GL_INDEX_ARRAY_LIST_STRIDE_IBM                 103083
#define LOCAL_GL_INDEX_ARRAY_POINTER                         0x8091
#define LOCAL_GL_INDEX_ARRAY_POINTER_EXT                     0x8091
#define LOCAL_GL_INDEX_ARRAY_STRIDE                          0x8086
#define LOCAL_GL_INDEX_ARRAY_STRIDE_EXT                      0x8086
#define LOCAL_GL_INDEX_ARRAY_TYPE                            0x8085
#define LOCAL_GL_INDEX_ARRAY_TYPE_EXT                        0x8085
#define LOCAL_GL_INDEX_BITS                                  0x0D51
#define LOCAL_GL_INDEX_BIT_PGI                               0x00080000
#define LOCAL_GL_INDEX_CLEAR_VALUE                           0x0C20
#define LOCAL_GL_INDEX_LOGIC_OP                              0x0BF1
#define LOCAL_GL_INDEX_MATERIAL_EXT                          0x81B8
#define LOCAL_GL_INDEX_MATERIAL_FACE_EXT                     0x81BA
#define LOCAL_GL_INDEX_MATERIAL_PARAMETER_EXT                0x81B9
#define LOCAL_GL_INDEX_MODE                                  0x0C30
#define LOCAL_GL_INDEX_OFFSET                                0x0D13
#define LOCAL_GL_INDEX_SHIFT                                 0x0D12
#define LOCAL_GL_INDEX_TEST_EXT                              0x81B5
#define LOCAL_GL_INDEX_TEST_FUNC_EXT                         0x81B6
#define LOCAL_GL_INDEX_TEST_REF_EXT                          0x81B7
#define LOCAL_GL_INDEX_WRITEMASK                             0x0C21
#define LOCAL_GL_INFO_LOG_LENGTH                             0x8B84
#define LOCAL_GL_INNOCENT_CONTEXT_RESET                      0x8254
#define LOCAL_GL_INNOCENT_CONTEXT_RESET_ARB                  0x8254
#define LOCAL_GL_INNOCENT_CONTEXT_RESET_EXT                  0x8254
#define LOCAL_GL_INNOCENT_CONTEXT_RESET_KHR                  0x8254
#define LOCAL_GL_INSTRUMENT_BUFFER_POINTER_SGIX              0x8180
#define LOCAL_GL_INSTRUMENT_MEASUREMENTS_SGIX                0x8181
#define LOCAL_GL_INT                                         0x1404
#define LOCAL_GL_INT16_NV                                    0x8FE4
#define LOCAL_GL_INT16_VEC2_NV                               0x8FE5
#define LOCAL_GL_INT16_VEC3_NV                               0x8FE6
#define LOCAL_GL_INT16_VEC4_NV                               0x8FE7
#define LOCAL_GL_INT64_ARB                                   0x140E
#define LOCAL_GL_INT64_NV                                    0x140E
#define LOCAL_GL_INT64_VEC2_ARB                              0x8FE9
#define LOCAL_GL_INT64_VEC2_NV                               0x8FE9
#define LOCAL_GL_INT64_VEC3_ARB                              0x8FEA
#define LOCAL_GL_INT64_VEC3_NV                               0x8FEA
#define LOCAL_GL_INT64_VEC4_ARB                              0x8FEB
#define LOCAL_GL_INT64_VEC4_NV                               0x8FEB
#define LOCAL_GL_INT8_NV                                     0x8FE0
#define LOCAL_GL_INT8_VEC2_NV                                0x8FE1
#define LOCAL_GL_INT8_VEC3_NV                                0x8FE2
#define LOCAL_GL_INT8_VEC4_NV                                0x8FE3
#define LOCAL_GL_INTENSITY                                   0x8049
#define LOCAL_GL_INTENSITY12                                 0x804C
#define LOCAL_GL_INTENSITY12_EXT                             0x804C
#define LOCAL_GL_INTENSITY16                                 0x804D
#define LOCAL_GL_INTENSITY16F_ARB                            0x881D
#define LOCAL_GL_INTENSITY16I_EXT                            0x8D8B
#define LOCAL_GL_INTENSITY16UI_EXT                           0x8D79
#define LOCAL_GL_INTENSITY16_EXT                             0x804D
#define LOCAL_GL_INTENSITY16_SNORM                           0x901B
#define LOCAL_GL_INTENSITY32F_ARB                            0x8817
#define LOCAL_GL_INTENSITY32I_EXT                            0x8D85
#define LOCAL_GL_INTENSITY32UI_EXT                           0x8D73
#define LOCAL_GL_INTENSITY4                                  0x804A
#define LOCAL_GL_INTENSITY4_EXT                              0x804A
#define LOCAL_GL_INTENSITY8                                  0x804B
#define LOCAL_GL_INTENSITY8I_EXT                             0x8D91
#define LOCAL_GL_INTENSITY8UI_EXT                            0x8D7F
#define LOCAL_GL_INTENSITY8_EXT                              0x804B
#define LOCAL_GL_INTENSITY8_SNORM                            0x9017
#define LOCAL_GL_INTENSITY_EXT                               0x8049
#define LOCAL_GL_INTENSITY_FLOAT16_APPLE                     0x881D
#define LOCAL_GL_INTENSITY_FLOAT16_ATI                       0x881D
#define LOCAL_GL_INTENSITY_FLOAT32_APPLE                     0x8817
#define LOCAL_GL_INTENSITY_FLOAT32_ATI                       0x8817
#define LOCAL_GL_INTENSITY_SNORM                             0x9013
#define LOCAL_GL_INTERLACE_OML                               0x8980
#define LOCAL_GL_INTERLACE_READ_INGR                         0x8568
#define LOCAL_GL_INTERLACE_READ_OML                          0x8981
#define LOCAL_GL_INTERLACE_SGIX                              0x8094
#define LOCAL_GL_INTERLEAVED_ATTRIBS                         0x8C8C
#define LOCAL_GL_INTERLEAVED_ATTRIBS_EXT                     0x8C8C
#define LOCAL_GL_INTERLEAVED_ATTRIBS_NV                      0x8C8C
#define LOCAL_GL_INTERNALFORMAT_ALPHA_SIZE                   0x8274
#define LOCAL_GL_INTERNALFORMAT_ALPHA_TYPE                   0x827B
#define LOCAL_GL_INTERNALFORMAT_BLUE_SIZE                    0x8273
#define LOCAL_GL_INTERNALFORMAT_BLUE_TYPE                    0x827A
#define LOCAL_GL_INTERNALFORMAT_DEPTH_SIZE                   0x8275
#define LOCAL_GL_INTERNALFORMAT_DEPTH_TYPE                   0x827C
#define LOCAL_GL_INTERNALFORMAT_GREEN_SIZE                   0x8272
#define LOCAL_GL_INTERNALFORMAT_GREEN_TYPE                   0x8279
#define LOCAL_GL_INTERNALFORMAT_PREFERRED                    0x8270
#define LOCAL_GL_INTERNALFORMAT_RED_SIZE                     0x8271
#define LOCAL_GL_INTERNALFORMAT_RED_TYPE                     0x8278
#define LOCAL_GL_INTERNALFORMAT_SHARED_SIZE                  0x8277
#define LOCAL_GL_INTERNALFORMAT_STENCIL_SIZE                 0x8276
#define LOCAL_GL_INTERNALFORMAT_STENCIL_TYPE                 0x827D
#define LOCAL_GL_INTERNALFORMAT_SUPPORTED                    0x826F
#define LOCAL_GL_INTERPOLATE                                 0x8575
#define LOCAL_GL_INTERPOLATE_ARB                             0x8575
#define LOCAL_GL_INTERPOLATE_EXT                             0x8575
#define LOCAL_GL_INT_10_10_10_2_OES                          0x8DF7
#define LOCAL_GL_INT_2_10_10_10_REV                          0x8D9F
#define LOCAL_GL_INT_IMAGE_1D                                0x9057
#define LOCAL_GL_INT_IMAGE_1D_ARRAY                          0x905D
#define LOCAL_GL_INT_IMAGE_1D_ARRAY_EXT                      0x905D
#define LOCAL_GL_INT_IMAGE_1D_EXT                            0x9057
#define LOCAL_GL_INT_IMAGE_2D                                0x9058
#define LOCAL_GL_INT_IMAGE_2D_ARRAY                          0x905E
#define LOCAL_GL_INT_IMAGE_2D_ARRAY_EXT                      0x905E
#define LOCAL_GL_INT_IMAGE_2D_EXT                            0x9058
#define LOCAL_GL_INT_IMAGE_2D_MULTISAMPLE                    0x9060
#define LOCAL_GL_INT_IMAGE_2D_MULTISAMPLE_ARRAY              0x9061
#define LOCAL_GL_INT_IMAGE_2D_MULTISAMPLE_ARRAY_EXT          0x9061
#define LOCAL_GL_INT_IMAGE_2D_MULTISAMPLE_EXT                0x9060
#define LOCAL_GL_INT_IMAGE_2D_RECT                           0x905A
#define LOCAL_GL_INT_IMAGE_2D_RECT_EXT                       0x905A
#define LOCAL_GL_INT_IMAGE_3D                                0x9059
#define LOCAL_GL_INT_IMAGE_3D_EXT                            0x9059
#define LOCAL_GL_INT_IMAGE_BUFFER                            0x905C
#define LOCAL_GL_INT_IMAGE_BUFFER_EXT                        0x905C
#define LOCAL_GL_INT_IMAGE_BUFFER_OES                        0x905C
#define LOCAL_GL_INT_IMAGE_CUBE                              0x905B
#define LOCAL_GL_INT_IMAGE_CUBE_EXT                          0x905B
#define LOCAL_GL_INT_IMAGE_CUBE_MAP_ARRAY                    0x905F
#define LOCAL_GL_INT_IMAGE_CUBE_MAP_ARRAY_EXT                0x905F
#define LOCAL_GL_INT_IMAGE_CUBE_MAP_ARRAY_OES                0x905F
#define LOCAL_GL_INT_SAMPLER_1D                              0x8DC9
#define LOCAL_GL_INT_SAMPLER_1D_ARRAY                        0x8DCE
#define LOCAL_GL_INT_SAMPLER_1D_ARRAY_EXT                    0x8DCE
#define LOCAL_GL_INT_SAMPLER_1D_EXT                          0x8DC9
#define LOCAL_GL_INT_SAMPLER_2D                              0x8DCA
#define LOCAL_GL_INT_SAMPLER_2D_ARRAY                        0x8DCF
#define LOCAL_GL_INT_SAMPLER_2D_ARRAY_EXT                    0x8DCF
#define LOCAL_GL_INT_SAMPLER_2D_EXT                          0x8DCA
#define LOCAL_GL_INT_SAMPLER_2D_MULTISAMPLE                  0x9109
#define LOCAL_GL_INT_SAMPLER_2D_MULTISAMPLE_ARRAY            0x910C
#define LOCAL_GL_INT_SAMPLER_2D_MULTISAMPLE_ARRAY_OES        0x910C
#define LOCAL_GL_INT_SAMPLER_2D_RECT                         0x8DCD
#define LOCAL_GL_INT_SAMPLER_2D_RECT_EXT                     0x8DCD
#define LOCAL_GL_INT_SAMPLER_3D                              0x8DCB
#define LOCAL_GL_INT_SAMPLER_3D_EXT                          0x8DCB
#define LOCAL_GL_INT_SAMPLER_BUFFER                          0x8DD0
#define LOCAL_GL_INT_SAMPLER_BUFFER_AMD                      0x9002
#define LOCAL_GL_INT_SAMPLER_BUFFER_EXT                      0x8DD0
#define LOCAL_GL_INT_SAMPLER_BUFFER_OES                      0x8DD0
#define LOCAL_GL_INT_SAMPLER_CUBE                            0x8DCC
#define LOCAL_GL_INT_SAMPLER_CUBE_EXT                        0x8DCC
#define LOCAL_GL_INT_SAMPLER_CUBE_MAP_ARRAY                  0x900E
#define LOCAL_GL_INT_SAMPLER_CUBE_MAP_ARRAY_ARB              0x900E
#define LOCAL_GL_INT_SAMPLER_CUBE_MAP_ARRAY_EXT              0x900E
#define LOCAL_GL_INT_SAMPLER_CUBE_MAP_ARRAY_OES              0x900E
#define LOCAL_GL_INT_SAMPLER_RENDERBUFFER_NV                 0x8E57
#define LOCAL_GL_INT_VEC2                                    0x8B53
#define LOCAL_GL_INT_VEC2_ARB                                0x8B53
#define LOCAL_GL_INT_VEC3                                    0x8B54
#define LOCAL_GL_INT_VEC3_ARB                                0x8B54
#define LOCAL_GL_INT_VEC4                                    0x8B55
#define LOCAL_GL_INT_VEC4_ARB                                0x8B55
#define LOCAL_GL_INVALID_ENUM                                0x0500
#define LOCAL_GL_INVALID_FRAMEBUFFER_OPERATION               0x0506
#define LOCAL_GL_INVALID_FRAMEBUFFER_OPERATION_EXT           0x0506
#define LOCAL_GL_INVALID_FRAMEBUFFER_OPERATION_OES           0x0506
#define LOCAL_GL_INVALID_INDEX                               0xFFFFFFFF
#define LOCAL_GL_INVALID_OPERATION                           0x0502
#define LOCAL_GL_INVALID_VALUE                               0x0501
#define LOCAL_GL_INVARIANT_DATATYPE_EXT                      0x87EB
#define LOCAL_GL_INVARIANT_EXT                               0x87C2
#define LOCAL_GL_INVARIANT_VALUE_EXT                         0x87EA
#define LOCAL_GL_INVERSE_NV                                  0x862B
#define LOCAL_GL_INVERSE_TRANSPOSE_NV                        0x862D
#define LOCAL_GL_INVERT                                      0x150A
#define LOCAL_GL_INVERTED_SCREEN_W_REND                      0x8491
#define LOCAL_GL_INVERT_OVG_NV                               0x92B4
#define LOCAL_GL_INVERT_RGB_NV                               0x92A3
#define LOCAL_GL_IR_INSTRUMENT1_SGIX                         0x817F
#define LOCAL_GL_ISOLINES                                    0x8E7A
#define LOCAL_GL_ISOLINES_EXT                                0x8E7A
#define LOCAL_GL_ISOLINES_OES                                0x8E7A
#define LOCAL_GL_IS_PER_PATCH                                0x92E7
#define LOCAL_GL_IS_PER_PATCH_EXT                            0x92E7
#define LOCAL_GL_IS_PER_PATCH_OES                            0x92E7
#define LOCAL_GL_IS_ROW_MAJOR                                0x9300
#define LOCAL_GL_ITALIC_BIT_NV                               0x02
#define LOCAL_GL_IUI_N3F_V2F_EXT                             0x81AF
#define LOCAL_GL_IUI_N3F_V3F_EXT                             0x81B0
#define LOCAL_GL_IUI_V2F_EXT                                 0x81AD
#define LOCAL_GL_IUI_V3F_EXT                                 0x81AE
#define LOCAL_GL_KEEP                                        0x1E00
#define LOCAL_GL_LARGE_CCW_ARC_TO_NV                         0x16
#define LOCAL_GL_LARGE_CW_ARC_TO_NV                          0x18
#define LOCAL_GL_LAST_VERTEX_CONVENTION                      0x8E4E
#define LOCAL_GL_LAST_VERTEX_CONVENTION_EXT                  0x8E4E
#define LOCAL_GL_LAST_VERTEX_CONVENTION_OES                  0x8E4E
#define LOCAL_GL_LAST_VIDEO_CAPTURE_STATUS_NV                0x9027
#define LOCAL_GL_LAYER_NV                                    0x8DAA
#define LOCAL_GL_LAYER_PROVOKING_VERTEX                      0x825E
#define LOCAL_GL_LAYER_PROVOKING_VERTEX_EXT                  0x825E
#define LOCAL_GL_LAYER_PROVOKING_VERTEX_OES                  0x825E
#define LOCAL_GL_LAYOUT_COLOR_ATTACHMENT_EXT                 0x958E
#define LOCAL_GL_LAYOUT_DEFAULT_INTEL                        0
#define LOCAL_GL_LAYOUT_DEPTH_ATTACHMENT_STENCIL_READ_ONLY_EXT       0x9531
#define LOCAL_GL_LAYOUT_DEPTH_READ_ONLY_STENCIL_ATTACHMENT_EXT       0x9530
#define LOCAL_GL_LAYOUT_DEPTH_STENCIL_ATTACHMENT_EXT         0x958F
#define LOCAL_GL_LAYOUT_DEPTH_STENCIL_READ_ONLY_EXT          0x9590
#define LOCAL_GL_LAYOUT_GENERAL_EXT                          0x958D
#define LOCAL_GL_LAYOUT_LINEAR_CPU_CACHED_INTEL              2
#define LOCAL_GL_LAYOUT_LINEAR_INTEL                         1
#define LOCAL_GL_LAYOUT_SHADER_READ_ONLY_EXT                 0x9591
#define LOCAL_GL_LAYOUT_TRANSFER_DST_EXT                     0x9593
#define LOCAL_GL_LAYOUT_TRANSFER_SRC_EXT                     0x9592
#define LOCAL_GL_LEFT                                        0x0406
#define LOCAL_GL_LEQUAL                                      0x0203
#define LOCAL_GL_LERP_ATI                                    0x8969
#define LOCAL_GL_LESS                                        0x0201
#define LOCAL_GL_LGPU_SEPARATE_STORAGE_BIT_NVX               0x0800
#define LOCAL_GL_LIGHT0                                      0x4000
#define LOCAL_GL_LIGHT1                                      0x4001
#define LOCAL_GL_LIGHT2                                      0x4002
#define LOCAL_GL_LIGHT3                                      0x4003
#define LOCAL_GL_LIGHT4                                      0x4004
#define LOCAL_GL_LIGHT5                                      0x4005
#define LOCAL_GL_LIGHT6                                      0x4006
#define LOCAL_GL_LIGHT7                                      0x4007
#define LOCAL_GL_LIGHTEN                                     0x9298
#define LOCAL_GL_LIGHTEN_KHR                                 0x9298
#define LOCAL_GL_LIGHTEN_NV                                  0x9298
#define LOCAL_GL_LIGHTING                                    0x0B50
#define LOCAL_GL_LIGHTING_BIT                                0x00000040
#define LOCAL_GL_LIGHT_ENV_MODE_SGIX                         0x8407
#define LOCAL_GL_LIGHT_MODEL_AMBIENT                         0x0B53
#define LOCAL_GL_LIGHT_MODEL_COLOR_CONTROL                   0x81F8
#define LOCAL_GL_LIGHT_MODEL_COLOR_CONTROL_EXT               0x81F8
#define LOCAL_GL_LIGHT_MODEL_LOCAL_VIEWER                    0x0B51
#define LOCAL_GL_LIGHT_MODEL_SPECULAR_VECTOR_APPLE           0x85B0
#define LOCAL_GL_LIGHT_MODEL_TWO_SIDE                        0x0B52
#define LOCAL_GL_LINE                                        0x1B01
#define LOCAL_GL_LINEAR                                      0x2601
#define LOCAL_GL_LINEARBURN_NV                               0x92A5
#define LOCAL_GL_LINEARDODGE_NV                              0x92A4
#define LOCAL_GL_LINEARLIGHT_NV                              0x92A7
#define LOCAL_GL_LINEAR_ATTENUATION                          0x1208
#define LOCAL_GL_LINEAR_CLIPMAP_LINEAR_SGIX                  0x8170
#define LOCAL_GL_LINEAR_CLIPMAP_NEAREST_SGIX                 0x844F
#define LOCAL_GL_LINEAR_DETAIL_ALPHA_SGIS                    0x8098
#define LOCAL_GL_LINEAR_DETAIL_COLOR_SGIS                    0x8099
#define LOCAL_GL_LINEAR_DETAIL_SGIS                          0x8097
#define LOCAL_GL_LINEAR_MIPMAP_LINEAR                        0x2703
#define LOCAL_GL_LINEAR_MIPMAP_NEAREST                       0x2701
#define LOCAL_GL_LINEAR_SHARPEN_ALPHA_SGIS                   0x80AE
#define LOCAL_GL_LINEAR_SHARPEN_COLOR_SGIS                   0x80AF
#define LOCAL_GL_LINEAR_SHARPEN_SGIS                         0x80AD
#define LOCAL_GL_LINEAR_TILING_EXT                           0x9585
#define LOCAL_GL_LINES                                       0x0001
#define LOCAL_GL_LINES_ADJACENCY                             0x000A
#define LOCAL_GL_LINES_ADJACENCY_ARB                         0x000A
#define LOCAL_GL_LINES_ADJACENCY_EXT                         0x000A
#define LOCAL_GL_LINES_ADJACENCY_OES                         0x000A
#define LOCAL_GL_LINE_BIT                                    0x00000004
#define LOCAL_GL_LINE_LOOP                                   0x0002
#define LOCAL_GL_LINE_NV                                     0x1B01
#define LOCAL_GL_LINE_QUALITY_HINT_SGIX                      0x835B
#define LOCAL_GL_LINE_RESET_TOKEN                            0x0707
#define LOCAL_GL_LINE_SMOOTH                                 0x0B20
#define LOCAL_GL_LINE_SMOOTH_HINT                            0x0C52
#define LOCAL_GL_LINE_STIPPLE                                0x0B24
#define LOCAL_GL_LINE_STIPPLE_PATTERN                        0x0B25
#define LOCAL_GL_LINE_STIPPLE_REPEAT                         0x0B26
#define LOCAL_GL_LINE_STRIP                                  0x0003
#define LOCAL_GL_LINE_STRIP_ADJACENCY                        0x000B
#define LOCAL_GL_LINE_STRIP_ADJACENCY_ARB                    0x000B
#define LOCAL_GL_LINE_STRIP_ADJACENCY_EXT                    0x000B
#define LOCAL_GL_LINE_STRIP_ADJACENCY_OES                    0x000B
#define LOCAL_GL_LINE_TOKEN                                  0x0702
#define LOCAL_GL_LINE_TO_NV                                  0x04
#define LOCAL_GL_LINE_WIDTH                                  0x0B21
#define LOCAL_GL_LINE_WIDTH_COMMAND_NV                       0x000D
#define LOCAL_GL_LINE_WIDTH_GRANULARITY                      0x0B23
#define LOCAL_GL_LINE_WIDTH_RANGE                            0x0B22
#define LOCAL_GL_LINK_STATUS                                 0x8B82
#define LOCAL_GL_LIST_BASE                                   0x0B32
#define LOCAL_GL_LIST_BIT                                    0x00020000
#define LOCAL_GL_LIST_INDEX                                  0x0B33
#define LOCAL_GL_LIST_MODE                                   0x0B30
#define LOCAL_GL_LIST_PRIORITY_SGIX                          0x8182
#define LOCAL_GL_LOAD                                        0x0101
#define LOCAL_GL_LOCAL_CONSTANT_DATATYPE_EXT                 0x87ED
#define LOCAL_GL_LOCAL_CONSTANT_EXT                          0x87C3
#define LOCAL_GL_LOCAL_CONSTANT_VALUE_EXT                    0x87EC
#define LOCAL_GL_LOCAL_EXT                                   0x87C4
#define LOCAL_GL_LOCATION                                    0x930E
#define LOCAL_GL_LOCATION_COMPONENT                          0x934A
#define LOCAL_GL_LOCATION_INDEX                              0x930F
#define LOCAL_GL_LOCATION_INDEX_EXT                          0x930F
#define LOCAL_GL_LOGIC_OP                                    0x0BF1
#define LOCAL_GL_LOGIC_OP_MODE                               0x0BF0
#define LOCAL_GL_LOSE_CONTEXT_ON_RESET                       0x8252
#define LOCAL_GL_LOSE_CONTEXT_ON_RESET_ARB                   0x8252
#define LOCAL_GL_LOSE_CONTEXT_ON_RESET_EXT                   0x8252
#define LOCAL_GL_LOSE_CONTEXT_ON_RESET_KHR                   0x8252
#define LOCAL_GL_LOWER_LEFT                                  0x8CA1
#define LOCAL_GL_LOWER_LEFT_EXT                              0x8CA1
#define LOCAL_GL_LOW_FLOAT                                   0x8DF0
#define LOCAL_GL_LOW_INT                                     0x8DF3
#define LOCAL_GL_LO_BIAS_NV                                  0x8715
#define LOCAL_GL_LO_SCALE_NV                                 0x870F
#define LOCAL_GL_LUID_SIZE_EXT                               8
#define LOCAL_GL_LUMINANCE                                   0x1909
#define LOCAL_GL_LUMINANCE12                                 0x8041
#define LOCAL_GL_LUMINANCE12_ALPHA12                         0x8047
#define LOCAL_GL_LUMINANCE12_ALPHA12_EXT                     0x8047
#define LOCAL_GL_LUMINANCE12_ALPHA4                          0x8046
#define LOCAL_GL_LUMINANCE12_ALPHA4_EXT                      0x8046
#define LOCAL_GL_LUMINANCE12_EXT                             0x8041
#define LOCAL_GL_LUMINANCE16                                 0x8042
#define LOCAL_GL_LUMINANCE16F_ARB                            0x881E
#define LOCAL_GL_LUMINANCE16F_EXT                            0x881E
#define LOCAL_GL_LUMINANCE16I_EXT                            0x8D8C
#define LOCAL_GL_LUMINANCE16UI_EXT                           0x8D7A
#define LOCAL_GL_LUMINANCE16_ALPHA16                         0x8048
#define LOCAL_GL_LUMINANCE16_ALPHA16_EXT                     0x8048
#define LOCAL_GL_LUMINANCE16_ALPHA16_SNORM                   0x901A
#define LOCAL_GL_LUMINANCE16_EXT                             0x8042
#define LOCAL_GL_LUMINANCE16_SNORM                           0x9019
#define LOCAL_GL_LUMINANCE32F_ARB                            0x8818
#define LOCAL_GL_LUMINANCE32F_EXT                            0x8818
#define LOCAL_GL_LUMINANCE32I_EXT                            0x8D86
#define LOCAL_GL_LUMINANCE32UI_EXT                           0x8D74
#define LOCAL_GL_LUMINANCE4                                  0x803F
#define LOCAL_GL_LUMINANCE4_ALPHA4                           0x8043
#define LOCAL_GL_LUMINANCE4_ALPHA4_EXT                       0x8043
#define LOCAL_GL_LUMINANCE4_ALPHA4_OES                       0x8043
#define LOCAL_GL_LUMINANCE4_EXT                              0x803F
#define LOCAL_GL_LUMINANCE6_ALPHA2                           0x8044
#define LOCAL_GL_LUMINANCE6_ALPHA2_EXT                       0x8044
#define LOCAL_GL_LUMINANCE8                                  0x8040
#define LOCAL_GL_LUMINANCE8I_EXT                             0x8D92
#define LOCAL_GL_LUMINANCE8UI_EXT                            0x8D80
#define LOCAL_GL_LUMINANCE8_ALPHA8                           0x8045
#define LOCAL_GL_LUMINANCE8_ALPHA8_EXT                       0x8045
#define LOCAL_GL_LUMINANCE8_ALPHA8_OES                       0x8045
#define LOCAL_GL_LUMINANCE8_ALPHA8_SNORM                     0x9016
#define LOCAL_GL_LUMINANCE8_EXT                              0x8040
#define LOCAL_GL_LUMINANCE8_OES                              0x8040
#define LOCAL_GL_LUMINANCE8_SNORM                            0x9015
#define LOCAL_GL_LUMINANCE_ALPHA                             0x190A
#define LOCAL_GL_LUMINANCE_ALPHA16F_ARB                      0x881F
#define LOCAL_GL_LUMINANCE_ALPHA16F_EXT                      0x881F
#define LOCAL_GL_LUMINANCE_ALPHA16I_EXT                      0x8D8D
#define LOCAL_GL_LUMINANCE_ALPHA16UI_EXT                     0x8D7B
#define LOCAL_GL_LUMINANCE_ALPHA32F_ARB                      0x8819
#define LOCAL_GL_LUMINANCE_ALPHA32F_EXT                      0x8819
#define LOCAL_GL_LUMINANCE_ALPHA32I_EXT                      0x8D87
#define LOCAL_GL_LUMINANCE_ALPHA32UI_EXT                     0x8D75
#define LOCAL_GL_LUMINANCE_ALPHA8I_EXT                       0x8D93
#define LOCAL_GL_LUMINANCE_ALPHA8UI_EXT                      0x8D81
#define LOCAL_GL_LUMINANCE_ALPHA_FLOAT16_APPLE               0x881F
#define LOCAL_GL_LUMINANCE_ALPHA_FLOAT16_ATI                 0x881F
#define LOCAL_GL_LUMINANCE_ALPHA_FLOAT32_APPLE               0x8819
#define LOCAL_GL_LUMINANCE_ALPHA_FLOAT32_ATI                 0x8819
#define LOCAL_GL_LUMINANCE_ALPHA_INTEGER_EXT                 0x8D9D
#define LOCAL_GL_LUMINANCE_ALPHA_SNORM                       0x9012
#define LOCAL_GL_LUMINANCE_FLOAT16_APPLE                     0x881E
#define LOCAL_GL_LUMINANCE_FLOAT16_ATI                       0x881E
#define LOCAL_GL_LUMINANCE_FLOAT32_APPLE                     0x8818
#define LOCAL_GL_LUMINANCE_FLOAT32_ATI                       0x8818
#define LOCAL_GL_LUMINANCE_INTEGER_EXT                       0x8D9C
#define LOCAL_GL_LUMINANCE_SNORM                             0x9011
#define LOCAL_GL_MAD_ATI                                     0x8968
#define LOCAL_GL_MAGNITUDE_BIAS_NV                           0x8718
#define LOCAL_GL_MAGNITUDE_SCALE_NV                          0x8712
#define LOCAL_GL_MAJOR_VERSION                               0x821B
#define LOCAL_GL_MALI_PROGRAM_BINARY_ARM                     0x8F61
#define LOCAL_GL_MALI_SHADER_BINARY_ARM                      0x8F60
#define LOCAL_GL_MANUAL_GENERATE_MIPMAP                      0x8294
#define LOCAL_GL_MAP1_BINORMAL_EXT                           0x8446
#define LOCAL_GL_MAP1_COLOR_4                                0x0D90
#define LOCAL_GL_MAP1_GRID_DOMAIN                            0x0DD0
#define LOCAL_GL_MAP1_GRID_SEGMENTS                          0x0DD1
#define LOCAL_GL_MAP1_INDEX                                  0x0D91
#define LOCAL_GL_MAP1_NORMAL                                 0x0D92
#define LOCAL_GL_MAP1_TANGENT_EXT                            0x8444
#define LOCAL_GL_MAP1_TEXTURE_COORD_1                        0x0D93
#define LOCAL_GL_MAP1_TEXTURE_COORD_2                        0x0D94
#define LOCAL_GL_MAP1_TEXTURE_COORD_3                        0x0D95
#define LOCAL_GL_MAP1_TEXTURE_COORD_4                        0x0D96
#define LOCAL_GL_MAP1_VERTEX_3                               0x0D97
#define LOCAL_GL_MAP1_VERTEX_4                               0x0D98
#define LOCAL_GL_MAP1_VERTEX_ATTRIB0_4_NV                    0x8660
#define LOCAL_GL_MAP1_VERTEX_ATTRIB10_4_NV                   0x866A
#define LOCAL_GL_MAP1_VERTEX_ATTRIB11_4_NV                   0x866B
#define LOCAL_GL_MAP1_VERTEX_ATTRIB12_4_NV                   0x866C
#define LOCAL_GL_MAP1_VERTEX_ATTRIB13_4_NV                   0x866D
#define LOCAL_GL_MAP1_VERTEX_ATTRIB14_4_NV                   0x866E
#define LOCAL_GL_MAP1_VERTEX_ATTRIB15_4_NV                   0x866F
#define LOCAL_GL_MAP1_VERTEX_ATTRIB1_4_NV                    0x8661
#define LOCAL_GL_MAP1_VERTEX_ATTRIB2_4_NV                    0x8662
#define LOCAL_GL_MAP1_VERTEX_ATTRIB3_4_NV                    0x8663
#define LOCAL_GL_MAP1_VERTEX_ATTRIB4_4_NV                    0x8664
#define LOCAL_GL_MAP1_VERTEX_ATTRIB5_4_NV                    0x8665
#define LOCAL_GL_MAP1_VERTEX_ATTRIB6_4_NV                    0x8666
#define LOCAL_GL_MAP1_VERTEX_ATTRIB7_4_NV                    0x8667
#define LOCAL_GL_MAP1_VERTEX_ATTRIB8_4_NV                    0x8668
#define LOCAL_GL_MAP1_VERTEX_ATTRIB9_4_NV                    0x8669
#define LOCAL_GL_MAP2_BINORMAL_EXT                           0x8447
#define LOCAL_GL_MAP2_COLOR_4                                0x0DB0
#define LOCAL_GL_MAP2_GRID_DOMAIN                            0x0DD2
#define LOCAL_GL_MAP2_GRID_SEGMENTS                          0x0DD3
#define LOCAL_GL_MAP2_INDEX                                  0x0DB1
#define LOCAL_GL_MAP2_NORMAL                                 0x0DB2
#define LOCAL_GL_MAP2_TANGENT_EXT                            0x8445
#define LOCAL_GL_MAP2_TEXTURE_COORD_1                        0x0DB3
#define LOCAL_GL_MAP2_TEXTURE_COORD_2                        0x0DB4
#define LOCAL_GL_MAP2_TEXTURE_COORD_3                        0x0DB5
#define LOCAL_GL_MAP2_TEXTURE_COORD_4                        0x0DB6
#define LOCAL_GL_MAP2_VERTEX_3                               0x0DB7
#define LOCAL_GL_MAP2_VERTEX_4                               0x0DB8
#define LOCAL_GL_MAP2_VERTEX_ATTRIB0_4_NV                    0x8670
#define LOCAL_GL_MAP2_VERTEX_ATTRIB10_4_NV                   0x867A
#define LOCAL_GL_MAP2_VERTEX_ATTRIB11_4_NV                   0x867B
#define LOCAL_GL_MAP2_VERTEX_ATTRIB12_4_NV                   0x867C
#define LOCAL_GL_MAP2_VERTEX_ATTRIB13_4_NV                   0x867D
#define LOCAL_GL_MAP2_VERTEX_ATTRIB14_4_NV                   0x867E
#define LOCAL_GL_MAP2_VERTEX_ATTRIB15_4_NV                   0x867F
#define LOCAL_GL_MAP2_VERTEX_ATTRIB1_4_NV                    0x8671
#define LOCAL_GL_MAP2_VERTEX_ATTRIB2_4_NV                    0x8672
#define LOCAL_GL_MAP2_VERTEX_ATTRIB3_4_NV                    0x8673
#define LOCAL_GL_MAP2_VERTEX_ATTRIB4_4_NV                    0x8674
#define LOCAL_GL_MAP2_VERTEX_ATTRIB5_4_NV                    0x8675
#define LOCAL_GL_MAP2_VERTEX_ATTRIB6_4_NV                    0x8676
#define LOCAL_GL_MAP2_VERTEX_ATTRIB7_4_NV                    0x8677
#define LOCAL_GL_MAP2_VERTEX_ATTRIB8_4_NV                    0x8678
#define LOCAL_GL_MAP2_VERTEX_ATTRIB9_4_NV                    0x8679
#define LOCAL_GL_MAP_ATTRIB_U_ORDER_NV                       0x86C3
#define LOCAL_GL_MAP_ATTRIB_V_ORDER_NV                       0x86C4
#define LOCAL_GL_MAP_COHERENT_BIT                            0x0080
#define LOCAL_GL_MAP_COHERENT_BIT_EXT                        0x0080
#define LOCAL_GL_MAP_COLOR                                   0x0D10
#define LOCAL_GL_MAP_FLUSH_EXPLICIT_BIT                      0x0010
#define LOCAL_GL_MAP_FLUSH_EXPLICIT_BIT_EXT                  0x0010
#define LOCAL_GL_MAP_INVALIDATE_BUFFER_BIT                   0x0008
#define LOCAL_GL_MAP_INVALIDATE_BUFFER_BIT_EXT               0x0008
#define LOCAL_GL_MAP_INVALIDATE_RANGE_BIT                    0x0004
#define LOCAL_GL_MAP_INVALIDATE_RANGE_BIT_EXT                0x0004
#define LOCAL_GL_MAP_PERSISTENT_BIT                          0x0040
#define LOCAL_GL_MAP_PERSISTENT_BIT_EXT                      0x0040
#define LOCAL_GL_MAP_READ_BIT                                0x0001
#define LOCAL_GL_MAP_READ_BIT_EXT                            0x0001
#define LOCAL_GL_MAP_STENCIL                                 0x0D11
#define LOCAL_GL_MAP_TESSELLATION_NV                         0x86C2
#define LOCAL_GL_MAP_UNSYNCHRONIZED_BIT                      0x0020
#define LOCAL_GL_MAP_UNSYNCHRONIZED_BIT_EXT                  0x0020
#define LOCAL_GL_MAP_WRITE_BIT                               0x0002
#define LOCAL_GL_MAP_WRITE_BIT_EXT                           0x0002
#define LOCAL_GL_MATERIAL_SIDE_HINT_PGI                      0x1A22C
#define LOCAL_GL_MATRIX0_ARB                                 0x88C0
#define LOCAL_GL_MATRIX0_NV                                  0x8630
#define LOCAL_GL_MATRIX10_ARB                                0x88CA
#define LOCAL_GL_MATRIX11_ARB                                0x88CB
#define LOCAL_GL_MATRIX12_ARB                                0x88CC
#define LOCAL_GL_MATRIX13_ARB                                0x88CD
#define LOCAL_GL_MATRIX14_ARB                                0x88CE
#define LOCAL_GL_MATRIX15_ARB                                0x88CF
#define LOCAL_GL_MATRIX16_ARB                                0x88D0
#define LOCAL_GL_MATRIX17_ARB                                0x88D1
#define LOCAL_GL_MATRIX18_ARB                                0x88D2
#define LOCAL_GL_MATRIX19_ARB                                0x88D3
#define LOCAL_GL_MATRIX1_ARB                                 0x88C1
#define LOCAL_GL_MATRIX1_NV                                  0x8631
#define LOCAL_GL_MATRIX20_ARB                                0x88D4
#define LOCAL_GL_MATRIX21_ARB                                0x88D5
#define LOCAL_GL_MATRIX22_ARB                                0x88D6
#define LOCAL_GL_MATRIX23_ARB                                0x88D7
#define LOCAL_GL_MATRIX24_ARB                                0x88D8
#define LOCAL_GL_MATRIX25_ARB                                0x88D9
#define LOCAL_GL_MATRIX26_ARB                                0x88DA
#define LOCAL_GL_MATRIX27_ARB                                0x88DB
#define LOCAL_GL_MATRIX28_ARB                                0x88DC
#define LOCAL_GL_MATRIX29_ARB                                0x88DD
#define LOCAL_GL_MATRIX2_ARB                                 0x88C2
#define LOCAL_GL_MATRIX2_NV                                  0x8632
#define LOCAL_GL_MATRIX30_ARB                                0x88DE
#define LOCAL_GL_MATRIX31_ARB                                0x88DF
#define LOCAL_GL_MATRIX3_ARB                                 0x88C3
#define LOCAL_GL_MATRIX3_NV                                  0x8633
#define LOCAL_GL_MATRIX4_ARB                                 0x88C4
#define LOCAL_GL_MATRIX4_NV                                  0x8634
#define LOCAL_GL_MATRIX5_ARB                                 0x88C5
#define LOCAL_GL_MATRIX5_NV                                  0x8635
#define LOCAL_GL_MATRIX6_ARB                                 0x88C6
#define LOCAL_GL_MATRIX6_NV                                  0x8636
#define LOCAL_GL_MATRIX7_ARB                                 0x88C7
#define LOCAL_GL_MATRIX7_NV                                  0x8637
#define LOCAL_GL_MATRIX8_ARB                                 0x88C8
#define LOCAL_GL_MATRIX9_ARB                                 0x88C9
#define LOCAL_GL_MATRIX_EXT                                  0x87C0
#define LOCAL_GL_MATRIX_INDEX_ARRAY_ARB                      0x8844
#define LOCAL_GL_MATRIX_INDEX_ARRAY_BUFFER_BINDING_OES       0x8B9E
#define LOCAL_GL_MATRIX_INDEX_ARRAY_OES                      0x8844
#define LOCAL_GL_MATRIX_INDEX_ARRAY_POINTER_ARB              0x8849
#define LOCAL_GL_MATRIX_INDEX_ARRAY_POINTER_OES              0x8849
#define LOCAL_GL_MATRIX_INDEX_ARRAY_SIZE_ARB                 0x8846
#define LOCAL_GL_MATRIX_INDEX_ARRAY_SIZE_OES                 0x8846
#define LOCAL_GL_MATRIX_INDEX_ARRAY_STRIDE_ARB               0x8848
#define LOCAL_GL_MATRIX_INDEX_ARRAY_STRIDE_OES               0x8848
#define LOCAL_GL_MATRIX_INDEX_ARRAY_TYPE_ARB                 0x8847
#define LOCAL_GL_MATRIX_INDEX_ARRAY_TYPE_OES                 0x8847
#define LOCAL_GL_MATRIX_MODE                                 0x0BA0
#define LOCAL_GL_MATRIX_PALETTE_ARB                          0x8840
#define LOCAL_GL_MATRIX_PALETTE_OES                          0x8840
#define LOCAL_GL_MATRIX_STRIDE                               0x92FF
#define LOCAL_GL_MAT_AMBIENT_AND_DIFFUSE_BIT_PGI             0x00200000
#define LOCAL_GL_MAT_AMBIENT_BIT_PGI                         0x00100000
#define LOCAL_GL_MAT_COLOR_INDEXES_BIT_PGI                   0x01000000
#define LOCAL_GL_MAT_DIFFUSE_BIT_PGI                         0x00400000
#define LOCAL_GL_MAT_EMISSION_BIT_PGI                        0x00800000
#define LOCAL_GL_MAT_SHININESS_BIT_PGI                       0x02000000
#define LOCAL_GL_MAT_SPECULAR_BIT_PGI                        0x04000000
#define LOCAL_GL_MAX                                         0x8008
#define LOCAL_GL_MAX_3D_TEXTURE_SIZE                         0x8073
#define LOCAL_GL_MAX_3D_TEXTURE_SIZE_EXT                     0x8073
#define LOCAL_GL_MAX_3D_TEXTURE_SIZE_OES                     0x8073
#define LOCAL_GL_MAX_4D_TEXTURE_SIZE_SGIS                    0x8138
#define LOCAL_GL_MAX_ACTIVE_LIGHTS_SGIX                      0x8405
#define LOCAL_GL_MAX_ARRAY_TEXTURE_LAYERS                    0x88FF
#define LOCAL_GL_MAX_ARRAY_TEXTURE_LAYERS_EXT                0x88FF
#define LOCAL_GL_MAX_ASYNC_DRAW_PIXELS_SGIX                  0x8360
#define LOCAL_GL_MAX_ASYNC_HISTOGRAM_SGIX                    0x832D
#define LOCAL_GL_MAX_ASYNC_READ_PIXELS_SGIX                  0x8361
#define LOCAL_GL_MAX_ASYNC_TEX_IMAGE_SGIX                    0x835F
#define LOCAL_GL_MAX_ATOMIC_COUNTER_BUFFER_BINDINGS          0x92DC
#define LOCAL_GL_MAX_ATOMIC_COUNTER_BUFFER_SIZE              0x92D8
#define LOCAL_GL_MAX_ATTRIB_STACK_DEPTH                      0x0D35
#define LOCAL_GL_MAX_BINDABLE_UNIFORM_SIZE_EXT               0x8DED
#define LOCAL_GL_MAX_CLIENT_ATTRIB_STACK_DEPTH               0x0D3B
#define LOCAL_GL_MAX_CLIPMAP_DEPTH_SGIX                      0x8177
#define LOCAL_GL_MAX_CLIPMAP_VIRTUAL_DEPTH_SGIX              0x8178
#define LOCAL_GL_MAX_CLIP_DISTANCES                          0x0D32
#define LOCAL_GL_MAX_CLIP_DISTANCES_APPLE                    0x0D32
#define LOCAL_GL_MAX_CLIP_DISTANCES_EXT                      0x0D32
#define LOCAL_GL_MAX_CLIP_PLANES                             0x0D32
#define LOCAL_GL_MAX_CLIP_PLANES_IMG                         0x0D32
#define LOCAL_GL_MAX_COARSE_FRAGMENT_SAMPLES_NV              0x955F
#define LOCAL_GL_MAX_COLOR_ATTACHMENTS                       0x8CDF
#define LOCAL_GL_MAX_COLOR_ATTACHMENTS_EXT                   0x8CDF
#define LOCAL_GL_MAX_COLOR_ATTACHMENTS_NV                    0x8CDF
#define LOCAL_GL_MAX_COLOR_FRAMEBUFFER_SAMPLES_AMD           0x91B3
#define LOCAL_GL_MAX_COLOR_FRAMEBUFFER_STORAGE_SAMPLES_AMD   0x91B4
#define LOCAL_GL_MAX_COLOR_MATRIX_STACK_DEPTH                0x80B3
#define LOCAL_GL_MAX_COLOR_MATRIX_STACK_DEPTH_SGI            0x80B3
#define LOCAL_GL_MAX_COLOR_TEXTURE_SAMPLES                   0x910E
#define LOCAL_GL_MAX_COMBINED_ATOMIC_COUNTERS                0x92D7
#define LOCAL_GL_MAX_COMBINED_ATOMIC_COUNTER_BUFFERS         0x92D1
#define LOCAL_GL_MAX_COMBINED_CLIP_AND_CULL_DISTANCES        0x82FA
#define LOCAL_GL_MAX_COMBINED_CLIP_AND_CULL_DISTANCES_EXT    0x82FA
#define LOCAL_GL_MAX_COMBINED_COMPUTE_UNIFORM_COMPONENTS     0x8266
#define LOCAL_GL_MAX_COMBINED_DIMENSIONS                     0x8282
#define LOCAL_GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS    0x8A33
#define LOCAL_GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS    0x8A32
#define LOCAL_GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS_EXT        0x8A32
#define LOCAL_GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS_OES        0x8A32
#define LOCAL_GL_MAX_COMBINED_IMAGE_UNIFORMS                 0x90CF
#define LOCAL_GL_MAX_COMBINED_IMAGE_UNITS_AND_FRAGMENT_OUTPUTS       0x8F39
#define LOCAL_GL_MAX_COMBINED_IMAGE_UNITS_AND_FRAGMENT_OUTPUTS_EXT   0x8F39
#define LOCAL_GL_MAX_COMBINED_MESH_UNIFORM_COMPONENTS_NV     0x8E67
#define LOCAL_GL_MAX_COMBINED_SHADER_OUTPUT_RESOURCES        0x8F39
#define LOCAL_GL_MAX_COMBINED_SHADER_STORAGE_BLOCKS          0x90DC
#define LOCAL_GL_MAX_COMBINED_TASK_UNIFORM_COMPONENTS_NV     0x8E6F
#define LOCAL_GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS        0x8E1E
#define LOCAL_GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS_EXT    0x8E1E
#define LOCAL_GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS_OES    0x8E1E
#define LOCAL_GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS     0x8E1F
#define LOCAL_GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS_EXT 0x8E1F
#define LOCAL_GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS_OES 0x8E1F
#define LOCAL_GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS            0x8B4D
#define LOCAL_GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS_ARB        0x8B4D
#define LOCAL_GL_MAX_COMBINED_UNIFORM_BLOCKS                 0x8A2E
#define LOCAL_GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS      0x8A31
#define LOCAL_GL_MAX_COMPUTE_ATOMIC_COUNTERS                 0x8265
#define LOCAL_GL_MAX_COMPUTE_ATOMIC_COUNTER_BUFFERS          0x8264
#define LOCAL_GL_MAX_COMPUTE_FIXED_GROUP_INVOCATIONS_ARB     0x90EB
#define LOCAL_GL_MAX_COMPUTE_FIXED_GROUP_SIZE_ARB            0x91BF
#define LOCAL_GL_MAX_COMPUTE_IMAGE_UNIFORMS                  0x91BD
#define LOCAL_GL_MAX_COMPUTE_SHADER_STORAGE_BLOCKS           0x90DB
#define LOCAL_GL_MAX_COMPUTE_SHARED_MEMORY_SIZE              0x8262
#define LOCAL_GL_MAX_COMPUTE_TEXTURE_IMAGE_UNITS             0x91BC
#define LOCAL_GL_MAX_COMPUTE_UNIFORM_BLOCKS                  0x91BB
#define LOCAL_GL_MAX_COMPUTE_UNIFORM_COMPONENTS              0x8263
#define LOCAL_GL_MAX_COMPUTE_VARIABLE_GROUP_INVOCATIONS_ARB  0x9344
#define LOCAL_GL_MAX_COMPUTE_VARIABLE_GROUP_SIZE_ARB         0x9345
#define LOCAL_GL_MAX_COMPUTE_WORK_GROUP_COUNT                0x91BE
#define LOCAL_GL_MAX_COMPUTE_WORK_GROUP_INVOCATIONS          0x90EB
#define LOCAL_GL_MAX_COMPUTE_WORK_GROUP_SIZE                 0x91BF
#define LOCAL_GL_MAX_CONVOLUTION_HEIGHT                      0x801B
#define LOCAL_GL_MAX_CONVOLUTION_HEIGHT_EXT                  0x801B
#define LOCAL_GL_MAX_CONVOLUTION_WIDTH                       0x801A
#define LOCAL_GL_MAX_CONVOLUTION_WIDTH_EXT                   0x801A
#define LOCAL_GL_MAX_CUBE_MAP_TEXTURE_SIZE                   0x851C
#define LOCAL_GL_MAX_CUBE_MAP_TEXTURE_SIZE_ARB               0x851C
#define LOCAL_GL_MAX_CUBE_MAP_TEXTURE_SIZE_EXT               0x851C
#define LOCAL_GL_MAX_CUBE_MAP_TEXTURE_SIZE_OES               0x851C
#define LOCAL_GL_MAX_CULL_DISTANCES                          0x82F9
#define LOCAL_GL_MAX_CULL_DISTANCES_EXT                      0x82F9
#define LOCAL_GL_MAX_DEBUG_GROUP_STACK_DEPTH                 0x826C
#define LOCAL_GL_MAX_DEBUG_GROUP_STACK_DEPTH_KHR             0x826C
#define LOCAL_GL_MAX_DEBUG_LOGGED_MESSAGES                   0x9144
#define LOCAL_GL_MAX_DEBUG_LOGGED_MESSAGES_AMD               0x9144
#define LOCAL_GL_MAX_DEBUG_LOGGED_MESSAGES_ARB               0x9144
#define LOCAL_GL_MAX_DEBUG_LOGGED_MESSAGES_KHR               0x9144
#define LOCAL_GL_MAX_DEBUG_MESSAGE_LENGTH                    0x9143
#define LOCAL_GL_MAX_DEBUG_MESSAGE_LENGTH_AMD                0x9143
#define LOCAL_GL_MAX_DEBUG_MESSAGE_LENGTH_ARB                0x9143
#define LOCAL_GL_MAX_DEBUG_MESSAGE_LENGTH_KHR                0x9143
#define LOCAL_GL_MAX_DEEP_3D_TEXTURE_DEPTH_NV                0x90D1
#define LOCAL_GL_MAX_DEEP_3D_TEXTURE_WIDTH_HEIGHT_NV         0x90D0
#define LOCAL_GL_MAX_DEFORMATION_ORDER_SGIX                  0x8197
#define LOCAL_GL_MAX_DEPTH                                   0x8280
#define LOCAL_GL_MAX_DEPTH_STENCIL_FRAMEBUFFER_SAMPLES_AMD   0x91B5
#define LOCAL_GL_MAX_DEPTH_TEXTURE_SAMPLES                   0x910F
#define LOCAL_GL_MAX_DETACHED_BUFFERS_NV                     0x95AD
#define LOCAL_GL_MAX_DETACHED_TEXTURES_NV                    0x95AC
#define LOCAL_GL_MAX_DRAW_BUFFERS                            0x8824
#define LOCAL_GL_MAX_DRAW_BUFFERS_ARB                        0x8824
#define LOCAL_GL_MAX_DRAW_BUFFERS_ATI                        0x8824
#define LOCAL_GL_MAX_DRAW_BUFFERS_EXT                        0x8824
#define LOCAL_GL_MAX_DRAW_BUFFERS_NV                         0x8824
#define LOCAL_GL_MAX_DRAW_MESH_TASKS_COUNT_NV                0x953D
#define LOCAL_GL_MAX_DUAL_SOURCE_DRAW_BUFFERS                0x88FC
#define LOCAL_GL_MAX_DUAL_SOURCE_DRAW_BUFFERS_EXT            0x88FC
#define LOCAL_GL_MAX_ELEMENTS_INDICES                        0x80E9
#define LOCAL_GL_MAX_ELEMENTS_INDICES_EXT                    0x80E9
#define LOCAL_GL_MAX_ELEMENTS_VERTICES                       0x80E8
#define LOCAL_GL_MAX_ELEMENTS_VERTICES_EXT                   0x80E8
#define LOCAL_GL_MAX_ELEMENT_INDEX                           0x8D6B
#define LOCAL_GL_MAX_EVAL_ORDER                              0x0D30
#define LOCAL_GL_MAX_EXT                                     0x8008
#define LOCAL_GL_MAX_FOG_FUNC_POINTS_SGIS                    0x812C
#define LOCAL_GL_MAX_FRAGMENT_ATOMIC_COUNTERS                0x92D6
#define LOCAL_GL_MAX_FRAGMENT_ATOMIC_COUNTER_BUFFERS         0x92D0
#define LOCAL_GL_MAX_FRAGMENT_BINDABLE_UNIFORMS_EXT          0x8DE3
#define LOCAL_GL_MAX_FRAGMENT_IMAGE_UNIFORMS                 0x90CE
#define LOCAL_GL_MAX_FRAGMENT_INPUT_COMPONENTS               0x9125
#define LOCAL_GL_MAX_FRAGMENT_INTERPOLATION_OFFSET           0x8E5C
#define LOCAL_GL_MAX_FRAGMENT_INTERPOLATION_OFFSET_NV        0x8E5C
#define LOCAL_GL_MAX_FRAGMENT_INTERPOLATION_OFFSET_OES       0x8E5C
#define LOCAL_GL_MAX_FRAGMENT_LIGHTS_SGIX                    0x8404
#define LOCAL_GL_MAX_FRAGMENT_PROGRAM_LOCAL_PARAMETERS_NV    0x8868
#define LOCAL_GL_MAX_FRAGMENT_SHADER_STORAGE_BLOCKS          0x90DA
#define LOCAL_GL_MAX_FRAGMENT_UNIFORM_BLOCKS                 0x8A2D
#define LOCAL_GL_MAX_FRAGMENT_UNIFORM_COMPONENTS             0x8B49
#define LOCAL_GL_MAX_FRAGMENT_UNIFORM_COMPONENTS_ARB         0x8B49
#define LOCAL_GL_MAX_FRAGMENT_UNIFORM_VECTORS                0x8DFD
#define LOCAL_GL_MAX_FRAMEBUFFER_HEIGHT                      0x9316
#define LOCAL_GL_MAX_FRAMEBUFFER_LAYERS                      0x9317
#define LOCAL_GL_MAX_FRAMEBUFFER_LAYERS_EXT                  0x9317
#define LOCAL_GL_MAX_FRAMEBUFFER_LAYERS_OES                  0x9317
#define LOCAL_GL_MAX_FRAMEBUFFER_SAMPLES                     0x9318
#define LOCAL_GL_MAX_FRAMEBUFFER_WIDTH                       0x9315
#define LOCAL_GL_MAX_FRAMEZOOM_FACTOR_SGIX                   0x818D
#define LOCAL_GL_MAX_GENERAL_COMBINERS_NV                    0x854D
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTERS                0x92D5
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTERS_EXT            0x92D5
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTERS_OES            0x92D5
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTER_BUFFERS         0x92CF
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTER_BUFFERS_EXT     0x92CF
#define LOCAL_GL_MAX_GEOMETRY_ATOMIC_COUNTER_BUFFERS_OES     0x92CF
#define LOCAL_GL_MAX_GEOMETRY_BINDABLE_UNIFORMS_EXT          0x8DE4
#define LOCAL_GL_MAX_GEOMETRY_IMAGE_UNIFORMS                 0x90CD
#define LOCAL_GL_MAX_GEOMETRY_IMAGE_UNIFORMS_EXT             0x90CD
#define LOCAL_GL_MAX_GEOMETRY_IMAGE_UNIFORMS_OES             0x90CD
#define LOCAL_GL_MAX_GEOMETRY_INPUT_COMPONENTS               0x9123
#define LOCAL_GL_MAX_GEOMETRY_INPUT_COMPONENTS_EXT           0x9123
#define LOCAL_GL_MAX_GEOMETRY_INPUT_COMPONENTS_OES           0x9123
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_COMPONENTS              0x9124
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_COMPONENTS_EXT          0x9124
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_COMPONENTS_OES          0x9124
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_VERTICES                0x8DE0
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_VERTICES_ARB            0x8DE0
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_VERTICES_EXT            0x8DE0
#define LOCAL_GL_MAX_GEOMETRY_OUTPUT_VERTICES_OES            0x8DE0
#define LOCAL_GL_MAX_GEOMETRY_PROGRAM_INVOCATIONS_NV         0x8E5A
#define LOCAL_GL_MAX_GEOMETRY_SHADER_INVOCATIONS             0x8E5A
#define LOCAL_GL_MAX_GEOMETRY_SHADER_INVOCATIONS_EXT         0x8E5A
#define LOCAL_GL_MAX_GEOMETRY_SHADER_INVOCATIONS_OES         0x8E5A
#define LOCAL_GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS          0x90D7
#define LOCAL_GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS_EXT      0x90D7
#define LOCAL_GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS_OES      0x90D7
#define LOCAL_GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS            0x8C29
#define LOCAL_GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS_ARB        0x8C29
#define LOCAL_GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS_EXT        0x8C29
#define LOCAL_GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS_OES        0x8C29
#define LOCAL_GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS        0x8DE1
#define LOCAL_GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS_ARB    0x8DE1
#define LOCAL_GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS_EXT    0x8DE1
#define LOCAL_GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS_OES    0x8DE1
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_BLOCKS                 0x8A2C
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_BLOCKS_EXT             0x8A2C
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_BLOCKS_OES             0x8A2C
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_COMPONENTS             0x8DDF
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_COMPONENTS_ARB         0x8DDF
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_COMPONENTS_EXT         0x8DDF
#define LOCAL_GL_MAX_GEOMETRY_UNIFORM_COMPONENTS_OES         0x8DDF
#define LOCAL_GL_MAX_GEOMETRY_VARYING_COMPONENTS_ARB         0x8DDD
#define LOCAL_GL_MAX_GEOMETRY_VARYING_COMPONENTS_EXT         0x8DDD
#define LOCAL_GL_MAX_HEIGHT                                  0x827F
#define LOCAL_GL_MAX_IMAGE_SAMPLES                           0x906D
#define LOCAL_GL_MAX_IMAGE_SAMPLES_EXT                       0x906D
#define LOCAL_GL_MAX_IMAGE_UNITS                             0x8F38
#define LOCAL_GL_MAX_IMAGE_UNITS_EXT                         0x8F38
#define LOCAL_GL_MAX_INTEGER_SAMPLES                         0x9110
#define LOCAL_GL_MAX_LABEL_LENGTH                            0x82E8
#define LOCAL_GL_MAX_LABEL_LENGTH_KHR                        0x82E8
#define LOCAL_GL_MAX_LAYERS                                  0x8281
#define LOCAL_GL_MAX_LGPU_GPUS_NVX                           0x92BA
#define LOCAL_GL_MAX_LIGHTS                                  0x0D31
#define LOCAL_GL_MAX_LIST_NESTING                            0x0B31
#define LOCAL_GL_MAX_MAP_TESSELLATION_NV                     0x86D6
#define LOCAL_GL_MAX_MATRIX_PALETTE_STACK_DEPTH_ARB          0x8841
#define LOCAL_GL_MAX_MESH_ATOMIC_COUNTERS_NV                 0x8E65
#define LOCAL_GL_MAX_MESH_ATOMIC_COUNTER_BUFFERS_NV          0x8E64
#define LOCAL_GL_MAX_MESH_IMAGE_UNIFORMS_NV                  0x8E62
#define LOCAL_GL_MAX_MESH_OUTPUT_PRIMITIVES_NV               0x9539
#define LOCAL_GL_MAX_MESH_OUTPUT_VERTICES_NV                 0x9538
#define LOCAL_GL_MAX_MESH_SHADER_STORAGE_BLOCKS_NV           0x8E66
#define LOCAL_GL_MAX_MESH_TEXTURE_IMAGE_UNITS_NV             0x8E61
#define LOCAL_GL_MAX_MESH_TOTAL_MEMORY_SIZE_NV               0x9536
#define LOCAL_GL_MAX_MESH_UNIFORM_BLOCKS_NV                  0x8E60
#define LOCAL_GL_MAX_MESH_UNIFORM_COMPONENTS_NV              0x8E63
#define LOCAL_GL_MAX_MESH_VIEWS_NV                           0x9557
#define LOCAL_GL_MAX_MESH_WORK_GROUP_INVOCATIONS_NV          0x95A2
#define LOCAL_GL_MAX_MESH_WORK_GROUP_SIZE_NV                 0x953B
#define LOCAL_GL_MAX_MODELVIEW_STACK_DEPTH                   0x0D36
#define LOCAL_GL_MAX_MULTISAMPLE_COVERAGE_MODES_NV           0x8E11
#define LOCAL_GL_MAX_MULTIVIEW_BUFFERS_EXT                   0x90F2
#define LOCAL_GL_MAX_NAME_LENGTH                             0x92F6
#define LOCAL_GL_MAX_NAME_STACK_DEPTH                        0x0D37
#define LOCAL_GL_MAX_NUM_ACTIVE_VARIABLES                    0x92F7
#define LOCAL_GL_MAX_NUM_COMPATIBLE_SUBROUTINES              0x92F8
#define LOCAL_GL_MAX_OPTIMIZED_VERTEX_SHADER_INSTRUCTIONS_EXT        0x87CA
#define LOCAL_GL_MAX_OPTIMIZED_VERTEX_SHADER_INVARIANTS_EXT  0x87CD
#define LOCAL_GL_MAX_OPTIMIZED_VERTEX_SHADER_LOCALS_EXT      0x87CE
#define LOCAL_GL_MAX_OPTIMIZED_VERTEX_SHADER_LOCAL_CONSTANTS_EXT     0x87CC
#define LOCAL_GL_MAX_OPTIMIZED_VERTEX_SHADER_VARIANTS_EXT    0x87CB
#define LOCAL_GL_MAX_PALETTE_MATRICES_ARB                    0x8842
#define LOCAL_GL_MAX_PALETTE_MATRICES_OES                    0x8842
#define LOCAL_GL_MAX_PATCH_VERTICES                          0x8E7D
#define LOCAL_GL_MAX_PATCH_VERTICES_EXT                      0x8E7D
#define LOCAL_GL_MAX_PATCH_VERTICES_OES                      0x8E7D
#define LOCAL_GL_MAX_PIXEL_MAP_TABLE                         0x0D34
#define LOCAL_GL_MAX_PIXEL_TRANSFORM_2D_STACK_DEPTH_EXT      0x8337
#define LOCAL_GL_MAX_PN_TRIANGLES_TESSELATION_LEVEL_ATI      0x87F1
#define LOCAL_GL_MAX_PROGRAM_ADDRESS_REGISTERS_ARB           0x88B1
#define LOCAL_GL_MAX_PROGRAM_ALU_INSTRUCTIONS_ARB            0x880B
#define LOCAL_GL_MAX_PROGRAM_ATTRIBS_ARB                     0x88AD
#define LOCAL_GL_MAX_PROGRAM_ATTRIB_COMPONENTS_NV            0x8908
#define LOCAL_GL_MAX_PROGRAM_CALL_DEPTH_NV                   0x88F5
#define LOCAL_GL_MAX_PROGRAM_ENV_PARAMETERS_ARB              0x88B5
#define LOCAL_GL_MAX_PROGRAM_EXEC_INSTRUCTIONS_NV            0x88F4
#define LOCAL_GL_MAX_PROGRAM_GENERIC_ATTRIBS_NV              0x8DA5
#define LOCAL_GL_MAX_PROGRAM_GENERIC_RESULTS_NV              0x8DA6
#define LOCAL_GL_MAX_PROGRAM_IF_DEPTH_NV                     0x88F6
#define LOCAL_GL_MAX_PROGRAM_INSTRUCTIONS_ARB                0x88A1
#define LOCAL_GL_MAX_PROGRAM_LOCAL_PARAMETERS_ARB            0x88B4
#define LOCAL_GL_MAX_PROGRAM_LOOP_COUNT_NV                   0x88F8
#define LOCAL_GL_MAX_PROGRAM_LOOP_DEPTH_NV                   0x88F7
#define LOCAL_GL_MAX_PROGRAM_MATRICES_ARB                    0x862F
#define LOCAL_GL_MAX_PROGRAM_MATRIX_STACK_DEPTH_ARB          0x862E
#define LOCAL_GL_MAX_PROGRAM_NATIVE_ADDRESS_REGISTERS_ARB    0x88B3
#define LOCAL_GL_MAX_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB     0x880E
#define LOCAL_GL_MAX_PROGRAM_NATIVE_ATTRIBS_ARB              0x88AF
#define LOCAL_GL_MAX_PROGRAM_NATIVE_INSTRUCTIONS_ARB         0x88A3
#define LOCAL_GL_MAX_PROGRAM_NATIVE_PARAMETERS_ARB           0x88AB
#define LOCAL_GL_MAX_PROGRAM_NATIVE_TEMPORARIES_ARB          0x88A7
#define LOCAL_GL_MAX_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB     0x8810
#define LOCAL_GL_MAX_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB     0x880F
#define LOCAL_GL_MAX_PROGRAM_OUTPUT_VERTICES_NV              0x8C27
#define LOCAL_GL_MAX_PROGRAM_PARAMETERS_ARB                  0x88A9
#define LOCAL_GL_MAX_PROGRAM_PARAMETER_BUFFER_BINDINGS_NV    0x8DA0
#define LOCAL_GL_MAX_PROGRAM_PARAMETER_BUFFER_SIZE_NV        0x8DA1
#define LOCAL_GL_MAX_PROGRAM_PATCH_ATTRIBS_NV                0x86D8
#define LOCAL_GL_MAX_PROGRAM_RESULT_COMPONENTS_NV            0x8909
#define LOCAL_GL_MAX_PROGRAM_SUBROUTINE_NUM_NV               0x8F45
#define LOCAL_GL_MAX_PROGRAM_SUBROUTINE_PARAMETERS_NV        0x8F44
#define LOCAL_GL_MAX_PROGRAM_TEMPORARIES_ARB                 0x88A5
#define LOCAL_GL_MAX_PROGRAM_TEXEL_OFFSET                    0x8905
#define LOCAL_GL_MAX_PROGRAM_TEXEL_OFFSET_EXT                0x8905
#define LOCAL_GL_MAX_PROGRAM_TEXEL_OFFSET_NV                 0x8905
#define LOCAL_GL_MAX_PROGRAM_TEXTURE_GATHER_COMPONENTS_ARB   0x8F9F
#define LOCAL_GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET           0x8E5F
#define LOCAL_GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET_ARB       0x8E5F
#define LOCAL_GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET_NV        0x8E5F
#define LOCAL_GL_MAX_PROGRAM_TEX_INDIRECTIONS_ARB            0x880D
#define LOCAL_GL_MAX_PROGRAM_TEX_INSTRUCTIONS_ARB            0x880C
#define LOCAL_GL_MAX_PROGRAM_TOTAL_OUTPUT_COMPONENTS_NV      0x8C28
#define LOCAL_GL_MAX_PROJECTION_STACK_DEPTH                  0x0D38
#define LOCAL_GL_MAX_RASTER_SAMPLES_EXT                      0x9329
#define LOCAL_GL_MAX_RATIONAL_EVAL_ORDER_NV                  0x86D7
#define LOCAL_GL_MAX_RECTANGLE_TEXTURE_SIZE                  0x84F8
#define LOCAL_GL_MAX_RECTANGLE_TEXTURE_SIZE_ARB              0x84F8
#define LOCAL_GL_MAX_RECTANGLE_TEXTURE_SIZE_NV               0x84F8
#define LOCAL_GL_MAX_RENDERBUFFER_SIZE                       0x84E8
#define LOCAL_GL_MAX_RENDERBUFFER_SIZE_EXT                   0x84E8
#define LOCAL_GL_MAX_RENDERBUFFER_SIZE_OES                   0x84E8
#define LOCAL_GL_MAX_SAMPLES                                 0x8D57
#define LOCAL_GL_MAX_SAMPLES_ANGLE                           0x8D57
#define LOCAL_GL_MAX_SAMPLES_APPLE                           0x8D57
#define LOCAL_GL_MAX_SAMPLES_EXT                             0x8D57
#define LOCAL_GL_MAX_SAMPLES_IMG                             0x9135
#define LOCAL_GL_MAX_SAMPLES_NV                              0x8D57
#define LOCAL_GL_MAX_SAMPLE_MASK_WORDS                       0x8E59
#define LOCAL_GL_MAX_SAMPLE_MASK_WORDS_NV                    0x8E59
#define LOCAL_GL_MAX_SERVER_WAIT_TIMEOUT                     0x9111
#define LOCAL_GL_MAX_SERVER_WAIT_TIMEOUT_APPLE               0x9111
#define LOCAL_GL_MAX_SHADER_BUFFER_ADDRESS_NV                0x8F35
#define LOCAL_GL_MAX_SHADER_COMBINED_LOCAL_STORAGE_FAST_SIZE_EXT     0x9650
#define LOCAL_GL_MAX_SHADER_COMBINED_LOCAL_STORAGE_SIZE_EXT  0x9651
#define LOCAL_GL_MAX_SHADER_COMPILER_THREADS_ARB             0x91B0
#define LOCAL_GL_MAX_SHADER_COMPILER_THREADS_KHR             0x91B0
#define LOCAL_GL_MAX_SHADER_PIXEL_LOCAL_STORAGE_FAST_SIZE_EXT        0x8F63
#define LOCAL_GL_MAX_SHADER_PIXEL_LOCAL_STORAGE_SIZE_EXT     0x8F67
#define LOCAL_GL_MAX_SHADER_STORAGE_BLOCK_SIZE               0x90DE
#define LOCAL_GL_MAX_SHADER_STORAGE_BUFFER_BINDINGS          0x90DD
#define LOCAL_GL_MAX_SHADER_SUBSAMPLED_IMAGE_UNITS_QCOM      0x8FA1
#define LOCAL_GL_MAX_SHININESS_NV                            0x8504
#define LOCAL_GL_MAX_SPARSE_3D_TEXTURE_SIZE_AMD              0x9199
#define LOCAL_GL_MAX_SPARSE_3D_TEXTURE_SIZE_ARB              0x9199
#define LOCAL_GL_MAX_SPARSE_3D_TEXTURE_SIZE_EXT              0x9199
#define LOCAL_GL_MAX_SPARSE_ARRAY_TEXTURE_LAYERS             0x919A
#define LOCAL_GL_MAX_SPARSE_ARRAY_TEXTURE_LAYERS_ARB         0x919A
#define LOCAL_GL_MAX_SPARSE_ARRAY_TEXTURE_LAYERS_EXT         0x919A
#define LOCAL_GL_MAX_SPARSE_TEXTURE_SIZE_AMD                 0x9198
#define LOCAL_GL_MAX_SPARSE_TEXTURE_SIZE_ARB                 0x9198
#define LOCAL_GL_MAX_SPARSE_TEXTURE_SIZE_EXT                 0x9198
#define LOCAL_GL_MAX_SPOT_EXPONENT_NV                        0x8505
#define LOCAL_GL_MAX_SUBPIXEL_PRECISION_BIAS_BITS_NV         0x9349
#define LOCAL_GL_MAX_SUBROUTINES                             0x8DE7
#define LOCAL_GL_MAX_SUBROUTINE_UNIFORM_LOCATIONS            0x8DE8
#define LOCAL_GL_MAX_TASK_ATOMIC_COUNTERS_NV                 0x8E6D
#define LOCAL_GL_MAX_TASK_ATOMIC_COUNTER_BUFFERS_NV          0x8E6C
#define LOCAL_GL_MAX_TASK_IMAGE_UNIFORMS_NV                  0x8E6A
#define LOCAL_GL_MAX_TASK_OUTPUT_COUNT_NV                    0x953A
#define LOCAL_GL_MAX_TASK_SHADER_STORAGE_BLOCKS_NV           0x8E6E
#define LOCAL_GL_MAX_TASK_TEXTURE_IMAGE_UNITS_NV             0x8E69
#define LOCAL_GL_MAX_TASK_TOTAL_MEMORY_SIZE_NV               0x9537
#define LOCAL_GL_MAX_TASK_UNIFORM_BLOCKS_NV                  0x8E68
#define LOCAL_GL_MAX_TASK_UNIFORM_COMPONENTS_NV              0x8E6B
#define LOCAL_GL_MAX_TASK_WORK_GROUP_INVOCATIONS_NV          0x95A3
#define LOCAL_GL_MAX_TASK_WORK_GROUP_SIZE_NV                 0x953C
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTERS            0x92D3
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTERS_EXT        0x92D3
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTERS_OES        0x92D3
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTER_BUFFERS     0x92CD
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTER_BUFFERS_EXT 0x92CD
#define LOCAL_GL_MAX_TESS_CONTROL_ATOMIC_COUNTER_BUFFERS_OES 0x92CD
#define LOCAL_GL_MAX_TESS_CONTROL_IMAGE_UNIFORMS             0x90CB
#define LOCAL_GL_MAX_TESS_CONTROL_IMAGE_UNIFORMS_EXT         0x90CB
#define LOCAL_GL_MAX_TESS_CONTROL_IMAGE_UNIFORMS_OES         0x90CB
#define LOCAL_GL_MAX_TESS_CONTROL_INPUT_COMPONENTS           0x886C
#define LOCAL_GL_MAX_TESS_CONTROL_INPUT_COMPONENTS_EXT       0x886C
#define LOCAL_GL_MAX_TESS_CONTROL_INPUT_COMPONENTS_OES       0x886C
#define LOCAL_GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS          0x8E83
#define LOCAL_GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS_EXT      0x8E83
#define LOCAL_GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS_OES      0x8E83
#define LOCAL_GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS      0x90D8
#define LOCAL_GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS_EXT  0x90D8
#define LOCAL_GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS_OES  0x90D8
#define LOCAL_GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS        0x8E81
#define LOCAL_GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS_EXT    0x8E81
#define LOCAL_GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS_OES    0x8E81
#define LOCAL_GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS    0x8E85
#define LOCAL_GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS_EXT        0x8E85
#define LOCAL_GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS_OES        0x8E85
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS             0x8E89
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS_EXT         0x8E89
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS_OES         0x8E89
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS         0x8E7F
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS_EXT     0x8E7F
#define LOCAL_GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS_OES     0x8E7F
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTERS         0x92D4
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTERS_EXT     0x92D4
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTERS_OES     0x92D4
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTER_BUFFERS  0x92CE
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTER_BUFFERS_EXT      0x92CE
#define LOCAL_GL_MAX_TESS_EVALUATION_ATOMIC_COUNTER_BUFFERS_OES      0x92CE
#define LOCAL_GL_MAX_TESS_EVALUATION_IMAGE_UNIFORMS          0x90CC
#define LOCAL_GL_MAX_TESS_EVALUATION_IMAGE_UNIFORMS_EXT      0x90CC
#define LOCAL_GL_MAX_TESS_EVALUATION_IMAGE_UNIFORMS_OES      0x90CC
#define LOCAL_GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS        0x886D
#define LOCAL_GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS_EXT    0x886D
#define LOCAL_GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS_OES    0x886D
#define LOCAL_GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS       0x8E86
#define LOCAL_GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS_EXT   0x8E86
#define LOCAL_GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS_OES   0x8E86
#define LOCAL_GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS   0x90D9
#define LOCAL_GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS_EXT       0x90D9
#define LOCAL_GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS_OES       0x90D9
#define LOCAL_GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS     0x8E82
#define LOCAL_GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS_EXT 0x8E82
#define LOCAL_GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS_OES 0x8E82
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS          0x8E8A
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS_EXT      0x8E8A
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS_OES      0x8E8A
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS      0x8E80
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS_EXT  0x8E80
#define LOCAL_GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS_OES  0x8E80
#define LOCAL_GL_MAX_TESS_GEN_LEVEL                          0x8E7E
#define LOCAL_GL_MAX_TESS_GEN_LEVEL_EXT                      0x8E7E
#define LOCAL_GL_MAX_TESS_GEN_LEVEL_OES                      0x8E7E
#define LOCAL_GL_MAX_TESS_PATCH_COMPONENTS                   0x8E84
#define LOCAL_GL_MAX_TESS_PATCH_COMPONENTS_EXT               0x8E84
#define LOCAL_GL_MAX_TESS_PATCH_COMPONENTS_OES               0x8E84
#define LOCAL_GL_MAX_TEXTURE_BUFFER_SIZE                     0x8C2B
#define LOCAL_GL_MAX_TEXTURE_BUFFER_SIZE_ARB                 0x8C2B
#define LOCAL_GL_MAX_TEXTURE_BUFFER_SIZE_EXT                 0x8C2B
#define LOCAL_GL_MAX_TEXTURE_BUFFER_SIZE_OES                 0x8C2B
#define LOCAL_GL_MAX_TEXTURE_COORDS                          0x8871
#define LOCAL_GL_MAX_TEXTURE_COORDS_ARB                      0x8871
#define LOCAL_GL_MAX_TEXTURE_COORDS_NV                       0x8871
#define LOCAL_GL_MAX_TEXTURE_IMAGE_UNITS                     0x8872
#define LOCAL_GL_MAX_TEXTURE_IMAGE_UNITS_ARB                 0x8872
#define LOCAL_GL_MAX_TEXTURE_IMAGE_UNITS_NV                  0x8872
#define LOCAL_GL_MAX_TEXTURE_LOD_BIAS                        0x84FD
#define LOCAL_GL_MAX_TEXTURE_LOD_BIAS_EXT                    0x84FD
#define LOCAL_GL_MAX_TEXTURE_MAX_ANISOTROPY                  0x84FF
#define LOCAL_GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT              0x84FF
#define LOCAL_GL_MAX_TEXTURE_SIZE                            0x0D33
#define LOCAL_GL_MAX_TEXTURE_STACK_DEPTH                     0x0D39
#define LOCAL_GL_MAX_TEXTURE_UNITS                           0x84E2
#define LOCAL_GL_MAX_TEXTURE_UNITS_ARB                       0x84E2
#define LOCAL_GL_MAX_TIMELINE_SEMAPHORE_VALUE_DIFFERENCE_NV  0x95B6
#define LOCAL_GL_MAX_TRACK_MATRICES_NV                       0x862F
#define LOCAL_GL_MAX_TRACK_MATRIX_STACK_DEPTH_NV             0x862E
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_BUFFERS              0x8E70
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS       0x8C8A
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS_EXT   0x8C8A
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS_NV    0x8C8A
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS     0x8C8B
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS_EXT 0x8C8B
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS_NV  0x8C8B
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS  0x8C80
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS_EXT      0x8C80
#define LOCAL_GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS_NV       0x8C80
#define LOCAL_GL_MAX_UNIFORM_BLOCK_SIZE                      0x8A30
#define LOCAL_GL_MAX_UNIFORM_BUFFER_BINDINGS                 0x8A2F
#define LOCAL_GL_MAX_UNIFORM_LOCATIONS                       0x826E
#define LOCAL_GL_MAX_VARYING_COMPONENTS                      0x8B4B
#define LOCAL_GL_MAX_VARYING_COMPONENTS_EXT                  0x8B4B
#define LOCAL_GL_MAX_VARYING_FLOATS                          0x8B4B
#define LOCAL_GL_MAX_VARYING_FLOATS_ARB                      0x8B4B
#define LOCAL_GL_MAX_VARYING_VECTORS                         0x8DFC
#define LOCAL_GL_MAX_VERTEX_ARRAY_RANGE_ELEMENT_NV           0x8520
#define LOCAL_GL_MAX_VERTEX_ATOMIC_COUNTERS                  0x92D2
#define LOCAL_GL_MAX_VERTEX_ATOMIC_COUNTER_BUFFERS           0x92CC
#define LOCAL_GL_MAX_VERTEX_ATTRIBS                          0x8869
#define LOCAL_GL_MAX_VERTEX_ATTRIBS_ARB                      0x8869
#define LOCAL_GL_MAX_VERTEX_ATTRIB_BINDINGS                  0x82DA
#define LOCAL_GL_MAX_VERTEX_ATTRIB_RELATIVE_OFFSET           0x82D9
#define LOCAL_GL_MAX_VERTEX_ATTRIB_STRIDE                    0x82E5
#define LOCAL_GL_MAX_VERTEX_BINDABLE_UNIFORMS_EXT            0x8DE2
#define LOCAL_GL_MAX_VERTEX_HINT_PGI                         0x1A22D
#define LOCAL_GL_MAX_VERTEX_IMAGE_UNIFORMS                   0x90CA
#define LOCAL_GL_MAX_VERTEX_OUTPUT_COMPONENTS                0x9122
#define LOCAL_GL_MAX_VERTEX_SHADER_INSTRUCTIONS_EXT          0x87C5
#define LOCAL_GL_MAX_VERTEX_SHADER_INVARIANTS_EXT            0x87C7
#define LOCAL_GL_MAX_VERTEX_SHADER_LOCALS_EXT                0x87C9
#define LOCAL_GL_MAX_VERTEX_SHADER_LOCAL_CONSTANTS_EXT       0x87C8
#define LOCAL_GL_MAX_VERTEX_SHADER_STORAGE_BLOCKS            0x90D6
#define LOCAL_GL_MAX_VERTEX_SHADER_VARIANTS_EXT              0x87C6
#define LOCAL_GL_MAX_VERTEX_STREAMS                          0x8E71
#define LOCAL_GL_MAX_VERTEX_STREAMS_ATI                      0x876B
#define LOCAL_GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS              0x8B4C
#define LOCAL_GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS_ARB          0x8B4C
#define LOCAL_GL_MAX_VERTEX_UNIFORM_BLOCKS                   0x8A2B
#define LOCAL_GL_MAX_VERTEX_UNIFORM_COMPONENTS               0x8B4A
#define LOCAL_GL_MAX_VERTEX_UNIFORM_COMPONENTS_ARB           0x8B4A
#define LOCAL_GL_MAX_VERTEX_UNIFORM_VECTORS                  0x8DFB
#define LOCAL_GL_MAX_VERTEX_UNITS_ARB                        0x86A4
#define LOCAL_GL_MAX_VERTEX_UNITS_OES                        0x86A4
#define LOCAL_GL_MAX_VERTEX_VARYING_COMPONENTS_ARB           0x8DDE
#define LOCAL_GL_MAX_VERTEX_VARYING_COMPONENTS_EXT           0x8DDE
#define LOCAL_GL_MAX_VIEWPORTS                               0x825B
#define LOCAL_GL_MAX_VIEWPORTS_NV                            0x825B
#define LOCAL_GL_MAX_VIEWPORTS_OES                           0x825B
#define LOCAL_GL_MAX_VIEWPORT_DIMS                           0x0D3A
#define LOCAL_GL_MAX_VIEWS_OVR                               0x9631
#define LOCAL_GL_MAX_WIDTH                                   0x827E
#define LOCAL_GL_MAX_WINDOW_RECTANGLES_EXT                   0x8F14
#define LOCAL_GL_MEDIUM_FLOAT                                0x8DF1
#define LOCAL_GL_MEDIUM_INT                                  0x8DF4
#define LOCAL_GL_MEMORY_ATTACHABLE_ALIGNMENT_NV              0x95A6
#define LOCAL_GL_MEMORY_ATTACHABLE_NV                        0x95A8
#define LOCAL_GL_MEMORY_ATTACHABLE_SIZE_NV                   0x95A7
#define LOCAL_GL_MESH_OUTPUT_PER_PRIMITIVE_GRANULARITY_NV    0x9543
#define LOCAL_GL_MESH_OUTPUT_PER_VERTEX_GRANULARITY_NV       0x92DF
#define LOCAL_GL_MESH_OUTPUT_TYPE_NV                         0x957B
#define LOCAL_GL_MESH_PRIMITIVES_OUT_NV                      0x957A
#define LOCAL_GL_MESH_SHADER_BIT_NV                          0x00000040
#define LOCAL_GL_MESH_SHADER_NV                              0x9559
#define LOCAL_GL_MESH_SUBROUTINE_NV                          0x957C
#define LOCAL_GL_MESH_SUBROUTINE_UNIFORM_NV                  0x957E
#define LOCAL_GL_MESH_VERTICES_OUT_NV                        0x9579
#define LOCAL_GL_MESH_WORK_GROUP_SIZE_NV                     0x953E
#define LOCAL_GL_MIN                                         0x8007
#define LOCAL_GL_MINMAX                                      0x802E
#define LOCAL_GL_MINMAX_EXT                                  0x802E
#define LOCAL_GL_MINMAX_FORMAT                               0x802F
#define LOCAL_GL_MINMAX_FORMAT_EXT                           0x802F
#define LOCAL_GL_MINMAX_SINK                                 0x8030
#define LOCAL_GL_MINMAX_SINK_EXT                             0x8030
#define LOCAL_GL_MINOR_VERSION                               0x821C
#define LOCAL_GL_MINUS_CLAMPED_NV                            0x92B3
#define LOCAL_GL_MINUS_NV                                    0x929F
#define LOCAL_GL_MIN_EXT                                     0x8007
#define LOCAL_GL_MIN_FRAGMENT_INTERPOLATION_OFFSET           0x8E5B
#define LOCAL_GL_MIN_FRAGMENT_INTERPOLATION_OFFSET_NV        0x8E5B
#define LOCAL_GL_MIN_FRAGMENT_INTERPOLATION_OFFSET_OES       0x8E5B
#define LOCAL_GL_MIN_LOD_WARNING_AMD                         0x919C
#define LOCAL_GL_MIN_MAP_BUFFER_ALIGNMENT                    0x90BC
#define LOCAL_GL_MIN_PROGRAM_TEXEL_OFFSET                    0x8904
#define LOCAL_GL_MIN_PROGRAM_TEXEL_OFFSET_EXT                0x8904
#define LOCAL_GL_MIN_PROGRAM_TEXEL_OFFSET_NV                 0x8904
#define LOCAL_GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET           0x8E5E
#define LOCAL_GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET_ARB       0x8E5E
#define LOCAL_GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET_NV        0x8E5E
#define LOCAL_GL_MIN_SAMPLE_SHADING_VALUE                    0x8C37
#define LOCAL_GL_MIN_SAMPLE_SHADING_VALUE_ARB                0x8C37
#define LOCAL_GL_MIN_SAMPLE_SHADING_VALUE_OES                0x8C37
#define LOCAL_GL_MIN_SPARSE_LEVEL_AMD                        0x919B
#define LOCAL_GL_MIPMAP                                      0x8293
#define LOCAL_GL_MIRRORED_REPEAT                             0x8370
#define LOCAL_GL_MIRRORED_REPEAT_ARB                         0x8370
#define LOCAL_GL_MIRRORED_REPEAT_IBM                         0x8370
#define LOCAL_GL_MIRRORED_REPEAT_OES                         0x8370
#define LOCAL_GL_MIRROR_CLAMP_ATI                            0x8742
#define LOCAL_GL_MIRROR_CLAMP_EXT                            0x8742
#define LOCAL_GL_MIRROR_CLAMP_TO_BORDER_EXT                  0x8912
#define LOCAL_GL_MIRROR_CLAMP_TO_EDGE                        0x8743
#define LOCAL_GL_MIRROR_CLAMP_TO_EDGE_ATI                    0x8743
#define LOCAL_GL_MIRROR_CLAMP_TO_EDGE_EXT                    0x8743
#define LOCAL_GL_MITER_REVERT_NV                             0x90A7
#define LOCAL_GL_MITER_TRUNCATE_NV                           0x90A8
#define LOCAL_GL_MIXED_DEPTH_SAMPLES_SUPPORTED_NV            0x932F
#define LOCAL_GL_MIXED_STENCIL_SAMPLES_SUPPORTED_NV          0x9330
#define LOCAL_GL_MODELVIEW                                   0x1700
#define LOCAL_GL_MODELVIEW0_ARB                              0x1700
#define LOCAL_GL_MODELVIEW0_EXT                              0x1700
#define LOCAL_GL_MODELVIEW0_MATRIX_EXT                       0x0BA6
#define LOCAL_GL_MODELVIEW0_STACK_DEPTH_EXT                  0x0BA3
#define LOCAL_GL_MODELVIEW10_ARB                             0x872A
#define LOCAL_GL_MODELVIEW11_ARB                             0x872B
#define LOCAL_GL_MODELVIEW12_ARB                             0x872C
#define LOCAL_GL_MODELVIEW13_ARB                             0x872D
#define LOCAL_GL_MODELVIEW14_ARB                             0x872E
#define LOCAL_GL_MODELVIEW15_ARB                             0x872F
#define LOCAL_GL_MODELVIEW16_ARB                             0x8730
#define LOCAL_GL_MODELVIEW17_ARB                             0x8731
#define LOCAL_GL_MODELVIEW18_ARB                             0x8732
#define LOCAL_GL_MODELVIEW19_ARB                             0x8733
#define LOCAL_GL_MODELVIEW1_ARB                              0x850A
#define LOCAL_GL_MODELVIEW1_EXT                              0x850A
#define LOCAL_GL_MODELVIEW1_MATRIX_EXT                       0x8506
#define LOCAL_GL_MODELVIEW1_STACK_DEPTH_EXT                  0x8502
#define LOCAL_GL_MODELVIEW20_ARB                             0x8734
#define LOCAL_GL_MODELVIEW21_ARB                             0x8735
#define LOCAL_GL_MODELVIEW22_ARB                             0x8736
#define LOCAL_GL_MODELVIEW23_ARB                             0x8737
#define LOCAL_GL_MODELVIEW24_ARB                             0x8738
#define LOCAL_GL_MODELVIEW25_ARB                             0x8739
#define LOCAL_GL_MODELVIEW26_ARB                             0x873A
#define LOCAL_GL_MODELVIEW27_ARB                             0x873B
#define LOCAL_GL_MODELVIEW28_ARB                             0x873C
#define LOCAL_GL_MODELVIEW29_ARB                             0x873D
#define LOCAL_GL_MODELVIEW2_ARB                              0x8722
#define LOCAL_GL_MODELVIEW30_ARB                             0x873E
#define LOCAL_GL_MODELVIEW31_ARB                             0x873F
#define LOCAL_GL_MODELVIEW3_ARB                              0x8723
#define LOCAL_GL_MODELVIEW4_ARB                              0x8724
#define LOCAL_GL_MODELVIEW5_ARB                              0x8725
#define LOCAL_GL_MODELVIEW6_ARB                              0x8726
#define LOCAL_GL_MODELVIEW7_ARB                              0x8727
#define LOCAL_GL_MODELVIEW8_ARB                              0x8728
#define LOCAL_GL_MODELVIEW9_ARB                              0x8729
#define LOCAL_GL_MODELVIEW_MATRIX                            0x0BA6
#define LOCAL_GL_MODELVIEW_MATRIX_FLOAT_AS_INT_BITS_OES      0x898D
#define LOCAL_GL_MODELVIEW_PROJECTION_NV                     0x8629
#define LOCAL_GL_MODELVIEW_STACK_DEPTH                       0x0BA3
#define LOCAL_GL_MODULATE                                    0x2100
#define LOCAL_GL_MODULATE_ADD_ATI                            0x8744
#define LOCAL_GL_MODULATE_COLOR_IMG                          0x8C04
#define LOCAL_GL_MODULATE_SIGNED_ADD_ATI                     0x8745
#define LOCAL_GL_MODULATE_SUBTRACT_ATI                       0x8746
#define LOCAL_GL_MOTION_ESTIMATION_SEARCH_BLOCK_X_QCOM       0x8C90
#define LOCAL_GL_MOTION_ESTIMATION_SEARCH_BLOCK_Y_QCOM       0x8C91
#define LOCAL_GL_MOVE_TO_CONTINUES_NV                        0x90B6
#define LOCAL_GL_MOVE_TO_NV                                  0x02
#define LOCAL_GL_MOVE_TO_RESETS_NV                           0x90B5
#define LOCAL_GL_MOV_ATI                                     0x8961
#define LOCAL_GL_MULT                                        0x0103
#define LOCAL_GL_MULTICAST_GPUS_NV                           0x92BA
#define LOCAL_GL_MULTICAST_PROGRAMMABLE_SAMPLE_LOCATION_NV   0x9549
#define LOCAL_GL_MULTIPLY                                    0x9294
#define LOCAL_GL_MULTIPLY_KHR                                0x9294
#define LOCAL_GL_MULTIPLY_NV                                 0x9294
#define LOCAL_GL_MULTISAMPLE                                 0x809D
#define LOCAL_GL_MULTISAMPLES_NV                             0x9371
#define LOCAL_GL_MULTISAMPLE_3DFX                            0x86B2
#define LOCAL_GL_MULTISAMPLE_ARB                             0x809D
#define LOCAL_GL_MULTISAMPLE_BIT                             0x20000000
#define LOCAL_GL_MULTISAMPLE_BIT_3DFX                        0x20000000
#define LOCAL_GL_MULTISAMPLE_BIT_ARB                         0x20000000
#define LOCAL_GL_MULTISAMPLE_BIT_EXT                         0x20000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT0_QCOM                0x01000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT1_QCOM                0x02000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT2_QCOM                0x04000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT3_QCOM                0x08000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT4_QCOM                0x10000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT5_QCOM                0x20000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT6_QCOM                0x40000000
#define LOCAL_GL_MULTISAMPLE_BUFFER_BIT7_QCOM                0x80000000
#define LOCAL_GL_MULTISAMPLE_COVERAGE_MODES_NV               0x8E12
#define LOCAL_GL_MULTISAMPLE_EXT                             0x809D
#define LOCAL_GL_MULTISAMPLE_FILTER_HINT_NV                  0x8534
#define LOCAL_GL_MULTISAMPLE_LINE_WIDTH_GRANULARITY          0x9382
#define LOCAL_GL_MULTISAMPLE_LINE_WIDTH_GRANULARITY_ARB      0x9382
#define LOCAL_GL_MULTISAMPLE_LINE_WIDTH_RANGE                0x9381
#define LOCAL_GL_MULTISAMPLE_LINE_WIDTH_RANGE_ARB            0x9381
#define LOCAL_GL_MULTISAMPLE_RASTERIZATION_ALLOWED_EXT       0x932B
#define LOCAL_GL_MULTISAMPLE_SGIS                            0x809D
#define LOCAL_GL_MULTIVIEW_EXT                               0x90F1
#define LOCAL_GL_MUL_ATI                                     0x8964
#define LOCAL_GL_MVP_MATRIX_EXT                              0x87E3
#define LOCAL_GL_N3F_V3F                                     0x2A25
#define LOCAL_GL_NAMED_STRING_LENGTH_ARB                     0x8DE9
#define LOCAL_GL_NAMED_STRING_TYPE_ARB                       0x8DEA
#define LOCAL_GL_NAME_LENGTH                                 0x92F9
#define LOCAL_GL_NAME_STACK_DEPTH                            0x0D70
#define LOCAL_GL_NAND                                        0x150E
#define LOCAL_GL_NATIVE_GRAPHICS_BEGIN_HINT_PGI              0x1A203
#define LOCAL_GL_NATIVE_GRAPHICS_END_HINT_PGI                0x1A204
#define LOCAL_GL_NATIVE_GRAPHICS_HANDLE_PGI                  0x1A202
#define LOCAL_GL_NEAREST                                     0x2600
#define LOCAL_GL_NEAREST_CLIPMAP_LINEAR_SGIX                 0x844E
#define LOCAL_GL_NEAREST_CLIPMAP_NEAREST_SGIX                0x844D
#define LOCAL_GL_NEAREST_MIPMAP_LINEAR                       0x2702
#define LOCAL_GL_NEAREST_MIPMAP_NEAREST                      0x2700
#define LOCAL_GL_NEGATE_BIT_ATI                              0x00000004
#define LOCAL_GL_NEGATIVE_ONE_EXT                            0x87DF
#define LOCAL_GL_NEGATIVE_ONE_TO_ONE                         0x935E
#define LOCAL_GL_NEGATIVE_ONE_TO_ONE_EXT                     0x935E
#define LOCAL_GL_NEGATIVE_W_EXT                              0x87DC
#define LOCAL_GL_NEGATIVE_X_EXT                              0x87D9
#define LOCAL_GL_NEGATIVE_Y_EXT                              0x87DA
#define LOCAL_GL_NEGATIVE_Z_EXT                              0x87DB
#define LOCAL_GL_NEVER                                       0x0200
#define LOCAL_GL_NEXT_BUFFER_NV                              -2
#define LOCAL_GL_NEXT_VIDEO_CAPTURE_BUFFER_STATUS_NV         0x9025
#define LOCAL_GL_NICEST                                      0x1102
#define LOCAL_GL_NONE                                        0
#define LOCAL_GL_NONE_OES                                    0
#define LOCAL_GL_NOOP                                        0x1505
#define LOCAL_GL_NOP_COMMAND_NV                              0x0001
#define LOCAL_GL_NOR                                         0x1508
#define LOCAL_GL_NORMALIZE                                   0x0BA1
#define LOCAL_GL_NORMALIZED_RANGE_EXT                        0x87E0
#define LOCAL_GL_NORMAL_ARRAY                                0x8075
#define LOCAL_GL_NORMAL_ARRAY_ADDRESS_NV                     0x8F22
#define LOCAL_GL_NORMAL_ARRAY_BUFFER_BINDING                 0x8897
#define LOCAL_GL_NORMAL_ARRAY_BUFFER_BINDING_ARB             0x8897
#define LOCAL_GL_NORMAL_ARRAY_COUNT_EXT                      0x8080
#define LOCAL_GL_NORMAL_ARRAY_EXT                            0x8075
#define LOCAL_GL_NORMAL_ARRAY_LENGTH_NV                      0x8F2C
#define LOCAL_GL_NORMAL_ARRAY_LIST_IBM                       103071
#define LOCAL_GL_NORMAL_ARRAY_LIST_STRIDE_IBM                103081
#define LOCAL_GL_NORMAL_ARRAY_PARALLEL_POINTERS_INTEL        0x83F6
#define LOCAL_GL_NORMAL_ARRAY_POINTER                        0x808F
#define LOCAL_GL_NORMAL_ARRAY_POINTER_EXT                    0x808F
#define LOCAL_GL_NORMAL_ARRAY_STRIDE                         0x807F
#define LOCAL_GL_NORMAL_ARRAY_STRIDE_EXT                     0x807F
#define LOCAL_GL_NORMAL_ARRAY_TYPE                           0x807E
#define LOCAL_GL_NORMAL_ARRAY_TYPE_EXT                       0x807E
#define LOCAL_GL_NORMAL_BIT_PGI                              0x08000000
#define LOCAL_GL_NORMAL_MAP                                  0x8511
#define LOCAL_GL_NORMAL_MAP_ARB                              0x8511
#define LOCAL_GL_NORMAL_MAP_EXT                              0x8511
#define LOCAL_GL_NORMAL_MAP_NV                               0x8511
#define LOCAL_GL_NORMAL_MAP_OES                              0x8511
#define LOCAL_GL_NOTEQUAL                                    0x0205
#define LOCAL_GL_NO_ERROR                                    0
#define LOCAL_GL_NO_RESET_NOTIFICATION                       0x8261
#define LOCAL_GL_NO_RESET_NOTIFICATION_ARB                   0x8261
#define LOCAL_GL_NO_RESET_NOTIFICATION_EXT                   0x8261
#define LOCAL_GL_NO_RESET_NOTIFICATION_KHR                   0x8261
#define LOCAL_GL_NUM_ACTIVE_VARIABLES                        0x9304
#define LOCAL_GL_NUM_COMPATIBLE_SUBROUTINES                  0x8E4A
#define LOCAL_GL_NUM_COMPRESSED_TEXTURE_FORMATS              0x86A2
#define LOCAL_GL_NUM_COMPRESSED_TEXTURE_FORMATS_ARB          0x86A2
#define LOCAL_GL_NUM_DEVICE_UUIDS_EXT                        0x9596
#define LOCAL_GL_NUM_DOWNSAMPLE_SCALES_IMG                   0x913D
#define LOCAL_GL_NUM_EXTENSIONS                              0x821D
#define LOCAL_GL_NUM_FILL_STREAMS_NV                         0x8E29
#define LOCAL_GL_NUM_FRAGMENT_CONSTANTS_ATI                  0x896F
#define LOCAL_GL_NUM_FRAGMENT_REGISTERS_ATI                  0x896E
#define LOCAL_GL_NUM_GENERAL_COMBINERS_NV                    0x854E
#define LOCAL_GL_NUM_INPUT_INTERPOLATOR_COMPONENTS_ATI       0x8973
#define LOCAL_GL_NUM_INSTRUCTIONS_PER_PASS_ATI               0x8971
#define LOCAL_GL_NUM_INSTRUCTIONS_TOTAL_ATI                  0x8972
#define LOCAL_GL_NUM_LOOPBACK_COMPONENTS_ATI                 0x8974
#define LOCAL_GL_NUM_PASSES_ATI                              0x8970
#define LOCAL_GL_NUM_PROGRAM_BINARY_FORMATS                  0x87FE
#define LOCAL_GL_NUM_PROGRAM_BINARY_FORMATS_OES              0x87FE
#define LOCAL_GL_NUM_SAMPLE_COUNTS                           0x9380
#define LOCAL_GL_NUM_SHADER_BINARY_FORMATS                   0x8DF9
#define LOCAL_GL_NUM_SHADING_LANGUAGE_VERSIONS               0x82E9
#define LOCAL_GL_NUM_SPARSE_LEVELS_ARB                       0x91AA
#define LOCAL_GL_NUM_SPARSE_LEVELS_EXT                       0x91AA
#define LOCAL_GL_NUM_SPIR_V_EXTENSIONS                       0x9554
#define LOCAL_GL_NUM_SUPPORTED_MULTISAMPLE_MODES_AMD         0x91B6
#define LOCAL_GL_NUM_TILING_TYPES_EXT                        0x9582
#define LOCAL_GL_NUM_VIDEO_CAPTURE_STREAMS_NV                0x9024
#define LOCAL_GL_NUM_VIRTUAL_PAGE_SIZES_ARB                  0x91A8
#define LOCAL_GL_NUM_VIRTUAL_PAGE_SIZES_EXT                  0x91A8
#define LOCAL_GL_NUM_WINDOW_RECTANGLES_EXT                   0x8F15
#define LOCAL_GL_OBJECT_ACTIVE_ATTRIBUTES_ARB                0x8B89
#define LOCAL_GL_OBJECT_ACTIVE_ATTRIBUTE_MAX_LENGTH_ARB      0x8B8A
#define LOCAL_GL_OBJECT_ACTIVE_UNIFORMS_ARB                  0x8B86
#define LOCAL_GL_OBJECT_ACTIVE_UNIFORM_MAX_LENGTH_ARB        0x8B87
#define LOCAL_GL_OBJECT_ATTACHED_OBJECTS_ARB                 0x8B85
#define LOCAL_GL_OBJECT_BUFFER_SIZE_ATI                      0x8764
#define LOCAL_GL_OBJECT_BUFFER_USAGE_ATI                     0x8765
#define LOCAL_GL_OBJECT_COMPILE_STATUS_ARB                   0x8B81
#define LOCAL_GL_OBJECT_DELETE_STATUS_ARB                    0x8B80
#define LOCAL_GL_OBJECT_DISTANCE_TO_LINE_SGIS                0x81F3
#define LOCAL_GL_OBJECT_DISTANCE_TO_POINT_SGIS               0x81F1
#define LOCAL_GL_OBJECT_INFO_LOG_LENGTH_ARB                  0x8B84
#define LOCAL_GL_OBJECT_LINEAR                               0x2401
#define LOCAL_GL_OBJECT_LINEAR_NV                            0x2401
#define LOCAL_GL_OBJECT_LINE_SGIS                            0x81F7
#define LOCAL_GL_OBJECT_LINK_STATUS_ARB                      0x8B82
#define LOCAL_GL_OBJECT_PLANE                                0x2501
#define LOCAL_GL_OBJECT_POINT_SGIS                           0x81F5
#define LOCAL_GL_OBJECT_SHADER_SOURCE_LENGTH_ARB             0x8B88
#define LOCAL_GL_OBJECT_SUBTYPE_ARB                          0x8B4F
#define LOCAL_GL_OBJECT_TYPE                                 0x9112
#define LOCAL_GL_OBJECT_TYPE_APPLE                           0x9112
#define LOCAL_GL_OBJECT_TYPE_ARB                             0x8B4E
#define LOCAL_GL_OBJECT_VALIDATE_STATUS_ARB                  0x8B83
#define LOCAL_GL_OCCLUSION_QUERY_EVENT_MASK_AMD              0x874F
#define LOCAL_GL_OCCLUSION_TEST_HP                           0x8165
#define LOCAL_GL_OCCLUSION_TEST_RESULT_HP                    0x8166
#define LOCAL_GL_OFFSET                                      0x92FC
#define LOCAL_GL_OFFSET_HILO_PROJECTIVE_TEXTURE_2D_NV        0x8856
#define LOCAL_GL_OFFSET_HILO_PROJECTIVE_TEXTURE_RECTANGLE_NV 0x8857
#define LOCAL_GL_OFFSET_HILO_TEXTURE_2D_NV                   0x8854
#define LOCAL_GL_OFFSET_HILO_TEXTURE_RECTANGLE_NV            0x8855
#define LOCAL_GL_OFFSET_PROJECTIVE_TEXTURE_2D_NV             0x8850
#define LOCAL_GL_OFFSET_PROJECTIVE_TEXTURE_2D_SCALE_NV       0x8851
#define LOCAL_GL_OFFSET_PROJECTIVE_TEXTURE_RECTANGLE_NV      0x8852
#define LOCAL_GL_OFFSET_PROJECTIVE_TEXTURE_RECTANGLE_SCALE_NV        0x8853
#define LOCAL_GL_OFFSET_TEXTURE_2D_BIAS_NV                   0x86E3
#define LOCAL_GL_OFFSET_TEXTURE_2D_MATRIX_NV                 0x86E1
#define LOCAL_GL_OFFSET_TEXTURE_2D_NV                        0x86E8
#define LOCAL_GL_OFFSET_TEXTURE_2D_SCALE_NV                  0x86E2
#define LOCAL_GL_OFFSET_TEXTURE_BIAS_NV                      0x86E3
#define LOCAL_GL_OFFSET_TEXTURE_MATRIX_NV                    0x86E1
#define LOCAL_GL_OFFSET_TEXTURE_RECTANGLE_NV                 0x864C
#define LOCAL_GL_OFFSET_TEXTURE_RECTANGLE_SCALE_NV           0x864D
#define LOCAL_GL_OFFSET_TEXTURE_SCALE_NV                     0x86E2
#define LOCAL_GL_ONE                                         1
#define LOCAL_GL_ONE_EXT                                     0x87DE
#define LOCAL_GL_ONE_MINUS_CONSTANT_ALPHA                    0x8004
#define LOCAL_GL_ONE_MINUS_CONSTANT_ALPHA_EXT                0x8004
#define LOCAL_GL_ONE_MINUS_CONSTANT_COLOR                    0x8002
#define LOCAL_GL_ONE_MINUS_CONSTANT_COLOR_EXT                0x8002
#define LOCAL_GL_ONE_MINUS_DST_ALPHA                         0x0305
#define LOCAL_GL_ONE_MINUS_DST_COLOR                         0x0307
#define LOCAL_GL_ONE_MINUS_SRC1_ALPHA                        0x88FB
#define LOCAL_GL_ONE_MINUS_SRC1_ALPHA_EXT                    0x88FB
#define LOCAL_GL_ONE_MINUS_SRC1_COLOR                        0x88FA
#define LOCAL_GL_ONE_MINUS_SRC1_COLOR_EXT                    0x88FA
#define LOCAL_GL_ONE_MINUS_SRC_ALPHA                         0x0303
#define LOCAL_GL_ONE_MINUS_SRC_COLOR                         0x0301
#define LOCAL_GL_OPERAND0_ALPHA                              0x8598
#define LOCAL_GL_OPERAND0_ALPHA_ARB                          0x8598
#define LOCAL_GL_OPERAND0_ALPHA_EXT                          0x8598
#define LOCAL_GL_OPERAND0_RGB                                0x8590
#define LOCAL_GL_OPERAND0_RGB_ARB                            0x8590
#define LOCAL_GL_OPERAND0_RGB_EXT                            0x8590
#define LOCAL_GL_OPERAND1_ALPHA                              0x8599
#define LOCAL_GL_OPERAND1_ALPHA_ARB                          0x8599
#define LOCAL_GL_OPERAND1_ALPHA_EXT                          0x8599
#define LOCAL_GL_OPERAND1_RGB                                0x8591
#define LOCAL_GL_OPERAND1_RGB_ARB                            0x8591
#define LOCAL_GL_OPERAND1_RGB_EXT                            0x8591
#define LOCAL_GL_OPERAND2_ALPHA                              0x859A
#define LOCAL_GL_OPERAND2_ALPHA_ARB                          0x859A
#define LOCAL_GL_OPERAND2_ALPHA_EXT                          0x859A
#define LOCAL_GL_OPERAND2_RGB                                0x8592
#define LOCAL_GL_OPERAND2_RGB_ARB                            0x8592
#define LOCAL_GL_OPERAND2_RGB_EXT                            0x8592
#define LOCAL_GL_OPERAND3_ALPHA_NV                           0x859B
#define LOCAL_GL_OPERAND3_RGB_NV                             0x8593
#define LOCAL_GL_OPTIMAL_TILING_EXT                          0x9584
#define LOCAL_GL_OP_ADD_EXT                                  0x8787
#define LOCAL_GL_OP_CLAMP_EXT                                0x878E
#define LOCAL_GL_OP_CROSS_PRODUCT_EXT                        0x8797
#define LOCAL_GL_OP_DOT3_EXT                                 0x8784
#define LOCAL_GL_OP_DOT4_EXT                                 0x8785
#define LOCAL_GL_OP_EXP_BASE_2_EXT                           0x8791
#define LOCAL_GL_OP_FLOOR_EXT                                0x878F
#define LOCAL_GL_OP_FRAC_EXT                                 0x8789
#define LOCAL_GL_OP_INDEX_EXT                                0x8782
#define LOCAL_GL_OP_LOG_BASE_2_EXT                           0x8792
#define LOCAL_GL_OP_MADD_EXT                                 0x8788
#define LOCAL_GL_OP_MAX_EXT                                  0x878A
#define LOCAL_GL_OP_MIN_EXT                                  0x878B
#define LOCAL_GL_OP_MOV_EXT                                  0x8799
#define LOCAL_GL_OP_MULTIPLY_MATRIX_EXT                      0x8798
#define LOCAL_GL_OP_MUL_EXT                                  0x8786
#define LOCAL_GL_OP_NEGATE_EXT                               0x8783
#define LOCAL_GL_OP_POWER_EXT                                0x8793
#define LOCAL_GL_OP_RECIP_EXT                                0x8794
#define LOCAL_GL_OP_RECIP_SQRT_EXT                           0x8795
#define LOCAL_GL_OP_ROUND_EXT                                0x8790
#define LOCAL_GL_OP_SET_GE_EXT                               0x878C
#define LOCAL_GL_OP_SET_LT_EXT                               0x878D
#define LOCAL_GL_OP_SUB_EXT                                  0x8796
#define LOCAL_GL_OR                                          0x1507
#define LOCAL_GL_ORDER                                       0x0A01
#define LOCAL_GL_OR_INVERTED                                 0x150D
#define LOCAL_GL_OR_REVERSE                                  0x150B
#define LOCAL_GL_OUTPUT_COLOR0_EXT                           0x879B
#define LOCAL_GL_OUTPUT_COLOR1_EXT                           0x879C
#define LOCAL_GL_OUTPUT_FOG_EXT                              0x87BD
#define LOCAL_GL_OUTPUT_TEXTURE_COORD0_EXT                   0x879D
#define LOCAL_GL_OUTPUT_TEXTURE_COORD10_EXT                  0x87A7
#define LOCAL_GL_OUTPUT_TEXTURE_COORD11_EXT                  0x87A8
#define LOCAL_GL_OUTPUT_TEXTURE_COORD12_EXT                  0x87A9
#define LOCAL_GL_OUTPUT_TEXTURE_COORD13_EXT                  0x87AA
#define LOCAL_GL_OUTPUT_TEXTURE_COORD14_EXT                  0x87AB
#define LOCAL_GL_OUTPUT_TEXTURE_COORD15_EXT                  0x87AC
#define LOCAL_GL_OUTPUT_TEXTURE_COORD16_EXT                  0x87AD
#define LOCAL_GL_OUTPUT_TEXTURE_COORD17_EXT                  0x87AE
#define LOCAL_GL_OUTPUT_TEXTURE_COORD18_EXT                  0x87AF
#define LOCAL_GL_OUTPUT_TEXTURE_COORD19_EXT                  0x87B0
#define LOCAL_GL_OUTPUT_TEXTURE_COORD1_EXT                   0x879E
#define LOCAL_GL_OUTPUT_TEXTURE_COORD20_EXT                  0x87B1
#define LOCAL_GL_OUTPUT_TEXTURE_COORD21_EXT                  0x87B2
#define LOCAL_GL_OUTPUT_TEXTURE_COORD22_EXT                  0x87B3
#define LOCAL_GL_OUTPUT_TEXTURE_COORD23_EXT                  0x87B4
#define LOCAL_GL_OUTPUT_TEXTURE_COORD24_EXT                  0x87B5
#define LOCAL_GL_OUTPUT_TEXTURE_COORD25_EXT                  0x87B6
#define LOCAL_GL_OUTPUT_TEXTURE_COORD26_EXT                  0x87B7
#define LOCAL_GL_OUTPUT_TEXTURE_COORD27_EXT                  0x87B8
#define LOCAL_GL_OUTPUT_TEXTURE_COORD28_EXT                  0x87B9
#define LOCAL_GL_OUTPUT_TEXTURE_COORD29_EXT                  0x87BA
#define LOCAL_GL_OUTPUT_TEXTURE_COORD2_EXT                   0x879F
#define LOCAL_GL_OUTPUT_TEXTURE_COORD30_EXT                  0x87BB
#define LOCAL_GL_OUTPUT_TEXTURE_COORD31_EXT                  0x87BC
#define LOCAL_GL_OUTPUT_TEXTURE_COORD3_EXT                   0x87A0
#define LOCAL_GL_OUTPUT_TEXTURE_COORD4_EXT                   0x87A1
#define LOCAL_GL_OUTPUT_TEXTURE_COORD5_EXT                   0x87A2
#define LOCAL_GL_OUTPUT_TEXTURE_COORD6_EXT                   0x87A3
#define LOCAL_GL_OUTPUT_TEXTURE_COORD7_EXT                   0x87A4
#define LOCAL_GL_OUTPUT_TEXTURE_COORD8_EXT                   0x87A5
#define LOCAL_GL_OUTPUT_TEXTURE_COORD9_EXT                   0x87A6
#define LOCAL_GL_OUTPUT_VERTEX_EXT                           0x879A
#define LOCAL_GL_OUT_OF_MEMORY                               0x0505
#define LOCAL_GL_OVERLAY                                     0x9296
#define LOCAL_GL_OVERLAY_KHR                                 0x9296
#define LOCAL_GL_OVERLAY_NV                                  0x9296
#define LOCAL_GL_PACK_ALIGNMENT                              0x0D05
#define LOCAL_GL_PACK_CMYK_HINT_EXT                          0x800E
#define LOCAL_GL_PACK_COMPRESSED_BLOCK_DEPTH                 0x912D
#define LOCAL_GL_PACK_COMPRESSED_BLOCK_HEIGHT                0x912C
#define LOCAL_GL_PACK_COMPRESSED_BLOCK_SIZE                  0x912E
#define LOCAL_GL_PACK_COMPRESSED_BLOCK_WIDTH                 0x912B
#define LOCAL_GL_PACK_COMPRESSED_SIZE_SGIX                   0x831C
#define LOCAL_GL_PACK_IMAGE_DEPTH_SGIS                       0x8131
#define LOCAL_GL_PACK_IMAGE_HEIGHT                           0x806C
#define LOCAL_GL_PACK_IMAGE_HEIGHT_EXT                       0x806C
#define LOCAL_GL_PACK_INVERT_MESA                            0x8758
#define LOCAL_GL_PACK_LSB_FIRST                              0x0D01
#define LOCAL_GL_PACK_MAX_COMPRESSED_SIZE_SGIX               0x831B
#define LOCAL_GL_PACK_RESAMPLE_OML                           0x8984
#define LOCAL_GL_PACK_RESAMPLE_SGIX                          0x842E
#define LOCAL_GL_PACK_REVERSE_ROW_ORDER_ANGLE                0x93A4
#define LOCAL_GL_PACK_ROW_BYTES_APPLE                        0x8A15
#define LOCAL_GL_PACK_ROW_LENGTH                             0x0D02
#define LOCAL_GL_PACK_SKIP_IMAGES                            0x806B
#define LOCAL_GL_PACK_SKIP_IMAGES_EXT                        0x806B
#define LOCAL_GL_PACK_SKIP_PIXELS                            0x0D04
#define LOCAL_GL_PACK_SKIP_ROWS                              0x0D03
#define LOCAL_GL_PACK_SKIP_VOLUMES_SGIS                      0x8130
#define LOCAL_GL_PACK_SUBSAMPLE_RATE_SGIX                    0x85A0
#define LOCAL_GL_PACK_SWAP_BYTES                             0x0D00
#define LOCAL_GL_PALETTE4_R5_G6_B5_OES                       0x8B92
#define LOCAL_GL_PALETTE4_RGB5_A1_OES                        0x8B94
#define LOCAL_GL_PALETTE4_RGB8_OES                           0x8B90
#define LOCAL_GL_PALETTE4_RGBA4_OES                          0x8B93
#define LOCAL_GL_PALETTE4_RGBA8_OES                          0x8B91
#define LOCAL_GL_PALETTE8_R5_G6_B5_OES                       0x8B97
#define LOCAL_GL_PALETTE8_RGB5_A1_OES                        0x8B99
#define LOCAL_GL_PALETTE8_RGB8_OES                           0x8B95
#define LOCAL_GL_PALETTE8_RGBA4_OES                          0x8B98
#define LOCAL_GL_PALETTE8_RGBA8_OES                          0x8B96
#define LOCAL_GL_PARALLEL_ARRAYS_INTEL                       0x83F4
#define LOCAL_GL_PARAMETER_BUFFER                            0x80EE
#define LOCAL_GL_PARAMETER_BUFFER_ARB                        0x80EE
#define LOCAL_GL_PARAMETER_BUFFER_BINDING                    0x80EF
#define LOCAL_GL_PARAMETER_BUFFER_BINDING_ARB                0x80EF
#define LOCAL_GL_PARTIAL_SUCCESS_NV                          0x902E
#define LOCAL_GL_PASS_THROUGH_NV                             0x86E6
#define LOCAL_GL_PASS_THROUGH_TOKEN                          0x0700
#define LOCAL_GL_PATCHES                                     0x000E
#define LOCAL_GL_PATCHES_EXT                                 0x000E
#define LOCAL_GL_PATCHES_OES                                 0x000E
#define LOCAL_GL_PATCH_DEFAULT_INNER_LEVEL                   0x8E73
#define LOCAL_GL_PATCH_DEFAULT_INNER_LEVEL_EXT               0x8E73
#define LOCAL_GL_PATCH_DEFAULT_OUTER_LEVEL                   0x8E74
#define LOCAL_GL_PATCH_DEFAULT_OUTER_LEVEL_EXT               0x8E74
#define LOCAL_GL_PATCH_VERTICES                              0x8E72
#define LOCAL_GL_PATCH_VERTICES_EXT                          0x8E72
#define LOCAL_GL_PATCH_VERTICES_OES                          0x8E72
#define LOCAL_GL_PATH_CLIENT_LENGTH_NV                       0x907F
#define LOCAL_GL_PATH_COMMAND_COUNT_NV                       0x909D
#define LOCAL_GL_PATH_COMPUTED_LENGTH_NV                     0x90A0
#define LOCAL_GL_PATH_COORD_COUNT_NV                         0x909E
#define LOCAL_GL_PATH_COVER_DEPTH_FUNC_NV                    0x90BF
#define LOCAL_GL_PATH_DASH_ARRAY_COUNT_NV                    0x909F
#define LOCAL_GL_PATH_DASH_CAPS_NV                           0x907B
#define LOCAL_GL_PATH_DASH_OFFSET_NV                         0x907E
#define LOCAL_GL_PATH_DASH_OFFSET_RESET_NV                   0x90B4
#define LOCAL_GL_PATH_END_CAPS_NV                            0x9076
#define LOCAL_GL_PATH_ERROR_POSITION_NV                      0x90AB
#define LOCAL_GL_PATH_FILL_BOUNDING_BOX_NV                   0x90A1
#define LOCAL_GL_PATH_FILL_COVER_MODE_NV                     0x9082
#define LOCAL_GL_PATH_FILL_MASK_NV                           0x9081
#define LOCAL_GL_PATH_FILL_MODE_NV                           0x9080
#define LOCAL_GL_PATH_FOG_GEN_MODE_NV                        0x90AC
#define LOCAL_GL_PATH_FORMAT_PS_NV                           0x9071
#define LOCAL_GL_PATH_FORMAT_SVG_NV                          0x9070
#define LOCAL_GL_PATH_GEN_COEFF_NV                           0x90B1
#define LOCAL_GL_PATH_GEN_COLOR_FORMAT_NV                    0x90B2
#define LOCAL_GL_PATH_GEN_COMPONENTS_NV                      0x90B3
#define LOCAL_GL_PATH_GEN_MODE_NV                            0x90B0
#define LOCAL_GL_PATH_INITIAL_DASH_CAP_NV                    0x907C
#define LOCAL_GL_PATH_INITIAL_END_CAP_NV                     0x9077
#define LOCAL_GL_PATH_JOIN_STYLE_NV                          0x9079
#define LOCAL_GL_PATH_MAX_MODELVIEW_STACK_DEPTH_NV           0x0D36
#define LOCAL_GL_PATH_MAX_PROJECTION_STACK_DEPTH_NV          0x0D38
#define LOCAL_GL_PATH_MITER_LIMIT_NV                         0x907A
#define LOCAL_GL_PATH_MODELVIEW_MATRIX_NV                    0x0BA6
#define LOCAL_GL_PATH_MODELVIEW_NV                           0x1700
#define LOCAL_GL_PATH_MODELVIEW_STACK_DEPTH_NV               0x0BA3
#define LOCAL_GL_PATH_OBJECT_BOUNDING_BOX_NV                 0x908A
#define LOCAL_GL_PATH_PROJECTION_MATRIX_NV                   0x0BA7
#define LOCAL_GL_PATH_PROJECTION_NV                          0x1701
#define LOCAL_GL_PATH_PROJECTION_STACK_DEPTH_NV              0x0BA4
#define LOCAL_GL_PATH_STENCIL_DEPTH_OFFSET_FACTOR_NV         0x90BD
#define LOCAL_GL_PATH_STENCIL_DEPTH_OFFSET_UNITS_NV          0x90BE
#define LOCAL_GL_PATH_STENCIL_FUNC_NV                        0x90B7
#define LOCAL_GL_PATH_STENCIL_REF_NV                         0x90B8
#define LOCAL_GL_PATH_STENCIL_VALUE_MASK_NV                  0x90B9
#define LOCAL_GL_PATH_STROKE_BOUNDING_BOX_NV                 0x90A2
#define LOCAL_GL_PATH_STROKE_COVER_MODE_NV                   0x9083
#define LOCAL_GL_PATH_STROKE_MASK_NV                         0x9084
#define LOCAL_GL_PATH_STROKE_WIDTH_NV                        0x9075
#define LOCAL_GL_PATH_TERMINAL_DASH_CAP_NV                   0x907D
#define LOCAL_GL_PATH_TERMINAL_END_CAP_NV                    0x9078
#define LOCAL_GL_PATH_TRANSPOSE_MODELVIEW_MATRIX_NV          0x84E3
#define LOCAL_GL_PATH_TRANSPOSE_PROJECTION_MATRIX_NV         0x84E4
#define LOCAL_GL_PERCENTAGE_AMD                              0x8BC3
#define LOCAL_GL_PERFMON_GLOBAL_MODE_QCOM                    0x8FA0
#define LOCAL_GL_PERFMON_RESULT_AMD                          0x8BC6
#define LOCAL_GL_PERFMON_RESULT_AVAILABLE_AMD                0x8BC4
#define LOCAL_GL_PERFMON_RESULT_SIZE_AMD                     0x8BC5
#define LOCAL_GL_PERFORMANCE_MONITOR_AMD                     0x9152
#define LOCAL_GL_PERFQUERY_COUNTER_DATA_BOOL32_INTEL         0x94FC
#define LOCAL_GL_PERFQUERY_COUNTER_DATA_DOUBLE_INTEL         0x94FB
#define LOCAL_GL_PERFQUERY_COUNTER_DATA_FLOAT_INTEL          0x94FA
#define LOCAL_GL_PERFQUERY_COUNTER_DATA_UINT32_INTEL         0x94F8
#define LOCAL_GL_PERFQUERY_COUNTER_DATA_UINT64_INTEL         0x94F9
#define LOCAL_GL_PERFQUERY_COUNTER_DESC_LENGTH_MAX_INTEL     0x94FF
#define LOCAL_GL_PERFQUERY_COUNTER_DURATION_NORM_INTEL       0x94F1
#define LOCAL_GL_PERFQUERY_COUNTER_DURATION_RAW_INTEL        0x94F2
#define LOCAL_GL_PERFQUERY_COUNTER_EVENT_INTEL               0x94F0
#define LOCAL_GL_PERFQUERY_COUNTER_NAME_LENGTH_MAX_INTEL     0x94FE
#define LOCAL_GL_PERFQUERY_COUNTER_RAW_INTEL                 0x94F4
#define LOCAL_GL_PERFQUERY_COUNTER_THROUGHPUT_INTEL          0x94F3
#define LOCAL_GL_PERFQUERY_COUNTER_TIMESTAMP_INTEL           0x94F5
#define LOCAL_GL_PERFQUERY_DONOT_FLUSH_INTEL                 0x83F9
#define LOCAL_GL_PERFQUERY_FLUSH_INTEL                       0x83FA
#define LOCAL_GL_PERFQUERY_GLOBAL_CONTEXT_INTEL              0x00000001
#define LOCAL_GL_PERFQUERY_GPA_EXTENDED_COUNTERS_INTEL       0x9500
#define LOCAL_GL_PERFQUERY_QUERY_NAME_LENGTH_MAX_INTEL       0x94FD
#define LOCAL_GL_PERFQUERY_SINGLE_CONTEXT_INTEL              0x00000000
#define LOCAL_GL_PERFQUERY_WAIT_INTEL                        0x83FB
#define LOCAL_GL_PERSPECTIVE_CORRECTION_HINT                 0x0C50
#define LOCAL_GL_PERTURB_EXT                                 0x85AE
#define LOCAL_GL_PER_GPU_STORAGE_BIT_NV                      0x0800
#define LOCAL_GL_PER_GPU_STORAGE_NV                          0x9548
#define LOCAL_GL_PER_STAGE_CONSTANTS_NV                      0x8535
#define LOCAL_GL_PHONG_HINT_WIN                              0x80EB
#define LOCAL_GL_PHONG_WIN                                   0x80EA
#define LOCAL_GL_PINLIGHT_NV                                 0x92A8
#define LOCAL_GL_PIXELS_PER_SAMPLE_PATTERN_X_AMD             0x91AE
#define LOCAL_GL_PIXELS_PER_SAMPLE_PATTERN_Y_AMD             0x91AF
#define LOCAL_GL_PIXEL_BUFFER_BARRIER_BIT                    0x00000080
#define LOCAL_GL_PIXEL_BUFFER_BARRIER_BIT_EXT                0x00000080
#define LOCAL_GL_PIXEL_COUNTER_BITS_NV                       0x8864
#define LOCAL_GL_PIXEL_COUNT_AVAILABLE_NV                    0x8867
#define LOCAL_GL_PIXEL_COUNT_NV                              0x8866
#define LOCAL_GL_PIXEL_CUBIC_WEIGHT_EXT                      0x8333
#define LOCAL_GL_PIXEL_FRAGMENT_ALPHA_SOURCE_SGIS            0x8355
#define LOCAL_GL_PIXEL_FRAGMENT_RGB_SOURCE_SGIS              0x8354
#define LOCAL_GL_PIXEL_GROUP_COLOR_SGIS                      0x8356
#define LOCAL_GL_PIXEL_MAG_FILTER_EXT                        0x8331
#define LOCAL_GL_PIXEL_MAP_A_TO_A                            0x0C79
#define LOCAL_GL_PIXEL_MAP_A_TO_A_SIZE                       0x0CB9
#define LOCAL_GL_PIXEL_MAP_B_TO_B                            0x0C78
#define LOCAL_GL_PIXEL_MAP_B_TO_B_SIZE                       0x0CB8
#define LOCAL_GL_PIXEL_MAP_G_TO_G                            0x0C77
#define LOCAL_GL_PIXEL_MAP_G_TO_G_SIZE                       0x0CB7
#define LOCAL_GL_PIXEL_MAP_I_TO_A                            0x0C75
#define LOCAL_GL_PIXEL_MAP_I_TO_A_SIZE                       0x0CB5
#define LOCAL_GL_PIXEL_MAP_I_TO_B                            0x0C74
#define LOCAL_GL_PIXEL_MAP_I_TO_B_SIZE                       0x0CB4
#define LOCAL_GL_PIXEL_MAP_I_TO_G                            0x0C73
#define LOCAL_GL_PIXEL_MAP_I_TO_G_SIZE                       0x0CB3
#define LOCAL_GL_PIXEL_MAP_I_TO_I                            0x0C70
#define LOCAL_GL_PIXEL_MAP_I_TO_I_SIZE                       0x0CB0
#define LOCAL_GL_PIXEL_MAP_I_TO_R                            0x0C72
#define LOCAL_GL_PIXEL_MAP_I_TO_R_SIZE                       0x0CB2
#define LOCAL_GL_PIXEL_MAP_R_TO_R                            0x0C76
#define LOCAL_GL_PIXEL_MAP_R_TO_R_SIZE                       0x0CB6
#define LOCAL_GL_PIXEL_MAP_S_TO_S                            0x0C71
#define LOCAL_GL_PIXEL_MAP_S_TO_S_SIZE                       0x0CB1
#define LOCAL_GL_PIXEL_MIN_FILTER_EXT                        0x8332
#define LOCAL_GL_PIXEL_MODE_BIT                              0x00000020
#define LOCAL_GL_PIXEL_PACK_BUFFER                           0x88EB
#define LOCAL_GL_PIXEL_PACK_BUFFER_ARB                       0x88EB
#define LOCAL_GL_PIXEL_PACK_BUFFER_BINDING                   0x88ED
#define LOCAL_GL_PIXEL_PACK_BUFFER_BINDING_ARB               0x88ED
#define LOCAL_GL_PIXEL_PACK_BUFFER_BINDING_EXT               0x88ED
#define LOCAL_GL_PIXEL_PACK_BUFFER_BINDING_NV                0x88ED
#define LOCAL_GL_PIXEL_PACK_BUFFER_EXT                       0x88EB
#define LOCAL_GL_PIXEL_PACK_BUFFER_NV                        0x88EB
#define LOCAL_GL_PIXEL_SUBSAMPLE_2424_SGIX                   0x85A3
#define LOCAL_GL_PIXEL_SUBSAMPLE_4242_SGIX                   0x85A4
#define LOCAL_GL_PIXEL_SUBSAMPLE_4444_SGIX                   0x85A2
#define LOCAL_GL_PIXEL_TEXTURE_SGIS                          0x8353
#define LOCAL_GL_PIXEL_TEX_GEN_ALPHA_LS_SGIX                 0x8189
#define LOCAL_GL_PIXEL_TEX_GEN_ALPHA_MS_SGIX                 0x818A
#define LOCAL_GL_PIXEL_TEX_GEN_ALPHA_NO_REPLACE_SGIX         0x8188
#define LOCAL_GL_PIXEL_TEX_GEN_ALPHA_REPLACE_SGIX            0x8187
#define LOCAL_GL_PIXEL_TEX_GEN_MODE_SGIX                     0x832B
#define LOCAL_GL_PIXEL_TEX_GEN_Q_CEILING_SGIX                0x8184
#define LOCAL_GL_PIXEL_TEX_GEN_Q_FLOOR_SGIX                  0x8186
#define LOCAL_GL_PIXEL_TEX_GEN_Q_ROUND_SGIX                  0x8185
#define LOCAL_GL_PIXEL_TEX_GEN_SGIX                          0x8139
#define LOCAL_GL_PIXEL_TILE_BEST_ALIGNMENT_SGIX              0x813E
#define LOCAL_GL_PIXEL_TILE_CACHE_INCREMENT_SGIX             0x813F
#define LOCAL_GL_PIXEL_TILE_CACHE_SIZE_SGIX                  0x8145
#define LOCAL_GL_PIXEL_TILE_GRID_DEPTH_SGIX                  0x8144
#define LOCAL_GL_PIXEL_TILE_GRID_HEIGHT_SGIX                 0x8143
#define LOCAL_GL_PIXEL_TILE_GRID_WIDTH_SGIX                  0x8142
#define LOCAL_GL_PIXEL_TILE_HEIGHT_SGIX                      0x8141
#define LOCAL_GL_PIXEL_TILE_WIDTH_SGIX                       0x8140
#define LOCAL_GL_PIXEL_TRANSFORM_2D_EXT                      0x8330
#define LOCAL_GL_PIXEL_TRANSFORM_2D_MATRIX_EXT               0x8338
#define LOCAL_GL_PIXEL_TRANSFORM_2D_STACK_DEPTH_EXT          0x8336
#define LOCAL_GL_PIXEL_UNPACK_BUFFER                         0x88EC
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_ARB                     0x88EC
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_BINDING                 0x88EF
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_BINDING_ARB             0x88EF
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_BINDING_EXT             0x88EF
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_BINDING_NV              0x88EF
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_EXT                     0x88EC
#define LOCAL_GL_PIXEL_UNPACK_BUFFER_NV                      0x88EC
#define LOCAL_GL_PLUS_CLAMPED_ALPHA_NV                       0x92B2
#define LOCAL_GL_PLUS_CLAMPED_NV                             0x92B1
#define LOCAL_GL_PLUS_DARKER_NV                              0x9292
#define LOCAL_GL_PLUS_NV                                     0x9291
#define LOCAL_GL_PN_TRIANGLES_ATI                            0x87F0
#define LOCAL_GL_PN_TRIANGLES_NORMAL_MODE_ATI                0x87F3
#define LOCAL_GL_PN_TRIANGLES_NORMAL_MODE_LINEAR_ATI         0x87F7
#define LOCAL_GL_PN_TRIANGLES_NORMAL_MODE_QUADRATIC_ATI      0x87F8
#define LOCAL_GL_PN_TRIANGLES_POINT_MODE_ATI                 0x87F2
#define LOCAL_GL_PN_TRIANGLES_POINT_MODE_CUBIC_ATI           0x87F6
#define LOCAL_GL_PN_TRIANGLES_POINT_MODE_LINEAR_ATI          0x87F5
#define LOCAL_GL_PN_TRIANGLES_TESSELATION_LEVEL_ATI          0x87F4
#define LOCAL_GL_POINT                                       0x1B00
#define LOCAL_GL_POINTS                                      0x0000
#define LOCAL_GL_POINT_BIT                                   0x00000002
#define LOCAL_GL_POINT_DISTANCE_ATTENUATION                  0x8129
#define LOCAL_GL_POINT_DISTANCE_ATTENUATION_ARB              0x8129
#define LOCAL_GL_POINT_FADE_THRESHOLD_SIZE                   0x8128
#define LOCAL_GL_POINT_FADE_THRESHOLD_SIZE_ARB               0x8128
#define LOCAL_GL_POINT_FADE_THRESHOLD_SIZE_EXT               0x8128
#define LOCAL_GL_POINT_FADE_THRESHOLD_SIZE_SGIS              0x8128
#define LOCAL_GL_POINT_NV                                    0x1B00
#define LOCAL_GL_POINT_SIZE                                  0x0B11
#define LOCAL_GL_POINT_SIZE_ARRAY_BUFFER_BINDING_OES         0x8B9F
#define LOCAL_GL_POINT_SIZE_ARRAY_OES                        0x8B9C
#define LOCAL_GL_POINT_SIZE_ARRAY_POINTER_OES                0x898C
#define LOCAL_GL_POINT_SIZE_ARRAY_STRIDE_OES                 0x898B
#define LOCAL_GL_POINT_SIZE_ARRAY_TYPE_OES                   0x898A
#define LOCAL_GL_POINT_SIZE_GRANULARITY                      0x0B13
#define LOCAL_GL_POINT_SIZE_MAX                              0x8127
#define LOCAL_GL_POINT_SIZE_MAX_ARB                          0x8127
#define LOCAL_GL_POINT_SIZE_MAX_EXT                          0x8127
#define LOCAL_GL_POINT_SIZE_MAX_SGIS                         0x8127
#define LOCAL_GL_POINT_SIZE_MIN                              0x8126
#define LOCAL_GL_POINT_SIZE_MIN_ARB                          0x8126
#define LOCAL_GL_POINT_SIZE_MIN_EXT                          0x8126
#define LOCAL_GL_POINT_SIZE_MIN_SGIS                         0x8126
#define LOCAL_GL_POINT_SIZE_RANGE                            0x0B12
#define LOCAL_GL_POINT_SMOOTH                                0x0B10
#define LOCAL_GL_POINT_SMOOTH_HINT                           0x0C51
#define LOCAL_GL_POINT_SPRITE                                0x8861
#define LOCAL_GL_POINT_SPRITE_ARB                            0x8861
#define LOCAL_GL_POINT_SPRITE_COORD_ORIGIN                   0x8CA0
#define LOCAL_GL_POINT_SPRITE_NV                             0x8861
#define LOCAL_GL_POINT_SPRITE_OES                            0x8861
#define LOCAL_GL_POINT_SPRITE_R_MODE_NV                      0x8863
#define LOCAL_GL_POINT_TOKEN                                 0x0701
#define LOCAL_GL_POLYGON                                     0x0009
#define LOCAL_GL_POLYGON_BIT                                 0x00000008
#define LOCAL_GL_POLYGON_MODE                                0x0B40
#define LOCAL_GL_POLYGON_MODE_NV                             0x0B40
#define LOCAL_GL_POLYGON_OFFSET_BIAS_EXT                     0x8039
#define LOCAL_GL_POLYGON_OFFSET_CLAMP                        0x8E1B
#define LOCAL_GL_POLYGON_OFFSET_CLAMP_EXT                    0x8E1B
#define LOCAL_GL_POLYGON_OFFSET_COMMAND_NV                   0x000E
#define LOCAL_GL_POLYGON_OFFSET_EXT                          0x8037
#define LOCAL_GL_POLYGON_OFFSET_FACTOR                       0x8038
#define LOCAL_GL_POLYGON_OFFSET_FACTOR_EXT                   0x8038
#define LOCAL_GL_POLYGON_OFFSET_FILL                         0x8037
#define LOCAL_GL_POLYGON_OFFSET_LINE                         0x2A02
#define LOCAL_GL_POLYGON_OFFSET_LINE_NV                      0x2A02
#define LOCAL_GL_POLYGON_OFFSET_POINT                        0x2A01
#define LOCAL_GL_POLYGON_OFFSET_POINT_NV                     0x2A01
#define LOCAL_GL_POLYGON_OFFSET_UNITS                        0x2A00
#define LOCAL_GL_POLYGON_SMOOTH                              0x0B41
#define LOCAL_GL_POLYGON_SMOOTH_HINT                         0x0C53
#define LOCAL_GL_POLYGON_STIPPLE                             0x0B42
#define LOCAL_GL_POLYGON_STIPPLE_BIT                         0x00000010
#define LOCAL_GL_POLYGON_TOKEN                               0x0703
#define LOCAL_GL_POSITION                                    0x1203
#define LOCAL_GL_POST_COLOR_MATRIX_ALPHA_BIAS                0x80BB
#define LOCAL_GL_POST_COLOR_MATRIX_ALPHA_BIAS_SGI            0x80BB
#define LOCAL_GL_POST_COLOR_MATRIX_ALPHA_SCALE               0x80B7
#define LOCAL_GL_POST_COLOR_MATRIX_ALPHA_SCALE_SGI           0x80B7
#define LOCAL_GL_POST_COLOR_MATRIX_BLUE_BIAS                 0x80BA
#define LOCAL_GL_POST_COLOR_MATRIX_BLUE_BIAS_SGI             0x80BA
#define LOCAL_GL_POST_COLOR_MATRIX_BLUE_SCALE                0x80B6
#define LOCAL_GL_POST_COLOR_MATRIX_BLUE_SCALE_SGI            0x80B6
#define LOCAL_GL_POST_COLOR_MATRIX_COLOR_TABLE               0x80D2
#define LOCAL_GL_POST_COLOR_MATRIX_COLOR_TABLE_SGI           0x80D2
#define LOCAL_GL_POST_COLOR_MATRIX_GREEN_BIAS                0x80B9
#define LOCAL_GL_POST_COLOR_MATRIX_GREEN_BIAS_SGI            0x80B9
#define LOCAL_GL_POST_COLOR_MATRIX_GREEN_SCALE               0x80B5
#define LOCAL_GL_POST_COLOR_MATRIX_GREEN_SCALE_SGI           0x80B5
#define LOCAL_GL_POST_COLOR_MATRIX_RED_BIAS                  0x80B8
#define LOCAL_GL_POST_COLOR_MATRIX_RED_BIAS_SGI              0x80B8
#define LOCAL_GL_POST_COLOR_MATRIX_RED_SCALE                 0x80B4
#define LOCAL_GL_POST_COLOR_MATRIX_RED_SCALE_SGI             0x80B4
#define LOCAL_GL_POST_CONVOLUTION_ALPHA_BIAS                 0x8023
#define LOCAL_GL_POST_CONVOLUTION_ALPHA_BIAS_EXT             0x8023
#define LOCAL_GL_POST_CONVOLUTION_ALPHA_SCALE                0x801F
#define LOCAL_GL_POST_CONVOLUTION_ALPHA_SCALE_EXT            0x801F
#define LOCAL_GL_POST_CONVOLUTION_BLUE_BIAS                  0x8022
#define LOCAL_GL_POST_CONVOLUTION_BLUE_BIAS_EXT              0x8022
#define LOCAL_GL_POST_CONVOLUTION_BLUE_SCALE                 0x801E
#define LOCAL_GL_POST_CONVOLUTION_BLUE_SCALE_EXT             0x801E
#define LOCAL_GL_POST_CONVOLUTION_COLOR_TABLE                0x80D1
#define LOCAL_GL_POST_CONVOLUTION_COLOR_TABLE_SGI            0x80D1
#define LOCAL_GL_POST_CONVOLUTION_GREEN_BIAS                 0x8021
#define LOCAL_GL_POST_CONVOLUTION_GREEN_BIAS_EXT             0x8021
#define LOCAL_GL_POST_CONVOLUTION_GREEN_SCALE                0x801D
#define LOCAL_GL_POST_CONVOLUTION_GREEN_SCALE_EXT            0x801D
#define LOCAL_GL_POST_CONVOLUTION_RED_BIAS                   0x8020
#define LOCAL_GL_POST_CONVOLUTION_RED_BIAS_EXT               0x8020
#define LOCAL_GL_POST_CONVOLUTION_RED_SCALE                  0x801C
#define LOCAL_GL_POST_CONVOLUTION_RED_SCALE_EXT              0x801C
#define LOCAL_GL_POST_IMAGE_TRANSFORM_COLOR_TABLE_HP         0x8162
#define LOCAL_GL_POST_TEXTURE_FILTER_BIAS_RANGE_SGIX         0x817B
#define LOCAL_GL_POST_TEXTURE_FILTER_BIAS_SGIX               0x8179
#define LOCAL_GL_POST_TEXTURE_FILTER_SCALE_RANGE_SGIX        0x817C
#define LOCAL_GL_POST_TEXTURE_FILTER_SCALE_SGIX              0x817A
#define LOCAL_GL_PREFER_DOUBLEBUFFER_HINT_PGI                0x1A1F8
#define LOCAL_GL_PRESENT_DURATION_NV                         0x8E2B
#define LOCAL_GL_PRESENT_TIME_NV                             0x8E2A
#define LOCAL_GL_PRESERVE_ATI                                0x8762
#define LOCAL_GL_PREVIOUS                                    0x8578
#define LOCAL_GL_PREVIOUS_ARB                                0x8578
#define LOCAL_GL_PREVIOUS_EXT                                0x8578
#define LOCAL_GL_PREVIOUS_TEXTURE_INPUT_NV                   0x86E4
#define LOCAL_GL_PRIMARY_COLOR                               0x8577
#define LOCAL_GL_PRIMARY_COLOR_ARB                           0x8577
#define LOCAL_GL_PRIMARY_COLOR_EXT                           0x8577
#define LOCAL_GL_PRIMARY_COLOR_NV                            0x852C
#define LOCAL_GL_PRIMITIVES_GENERATED                        0x8C87
#define LOCAL_GL_PRIMITIVES_GENERATED_EXT                    0x8C87
#define LOCAL_GL_PRIMITIVES_GENERATED_NV                     0x8C87
#define LOCAL_GL_PRIMITIVES_GENERATED_OES                    0x8C87
#define LOCAL_GL_PRIMITIVES_SUBMITTED                        0x82EF
#define LOCAL_GL_PRIMITIVES_SUBMITTED_ARB                    0x82EF
#define LOCAL_GL_PRIMITIVE_BOUNDING_BOX                      0x92BE
#define LOCAL_GL_PRIMITIVE_BOUNDING_BOX_ARB                  0x92BE
#define LOCAL_GL_PRIMITIVE_BOUNDING_BOX_EXT                  0x92BE
#define LOCAL_GL_PRIMITIVE_BOUNDING_BOX_OES                  0x92BE
#define LOCAL_GL_PRIMITIVE_ID_NV                             0x8C7C
#define LOCAL_GL_PRIMITIVE_RESTART                           0x8F9D
#define LOCAL_GL_PRIMITIVE_RESTART_FIXED_INDEX               0x8D69
#define LOCAL_GL_PRIMITIVE_RESTART_FOR_PATCHES_SUPPORTED     0x8221
#define LOCAL_GL_PRIMITIVE_RESTART_FOR_PATCHES_SUPPORTED_OES 0x8221
#define LOCAL_GL_PRIMITIVE_RESTART_INDEX                     0x8F9E
#define LOCAL_GL_PRIMITIVE_RESTART_INDEX_NV                  0x8559
#define LOCAL_GL_PRIMITIVE_RESTART_NV                        0x8558
#define LOCAL_GL_PROGRAM                                     0x82E2
#define LOCAL_GL_PROGRAMMABLE_SAMPLE_LOCATION_ARB            0x9341
#define LOCAL_GL_PROGRAMMABLE_SAMPLE_LOCATION_NV             0x9341
#define LOCAL_GL_PROGRAMMABLE_SAMPLE_LOCATION_TABLE_SIZE_ARB 0x9340
#define LOCAL_GL_PROGRAMMABLE_SAMPLE_LOCATION_TABLE_SIZE_NV  0x9340
#define LOCAL_GL_PROGRAM_ADDRESS_REGISTERS_ARB               0x88B0
#define LOCAL_GL_PROGRAM_ALU_INSTRUCTIONS_ARB                0x8805
#define LOCAL_GL_PROGRAM_ATTRIBS_ARB                         0x88AC
#define LOCAL_GL_PROGRAM_ATTRIB_COMPONENTS_NV                0x8906
#define LOCAL_GL_PROGRAM_BINARY_ANGLE                        0x93A6
#define LOCAL_GL_PROGRAM_BINARY_FORMATS                      0x87FF
#define LOCAL_GL_PROGRAM_BINARY_FORMATS_OES                  0x87FF
#define LOCAL_GL_PROGRAM_BINARY_FORMAT_MESA                  0x875F
#define LOCAL_GL_PROGRAM_BINARY_LENGTH                       0x8741
#define LOCAL_GL_PROGRAM_BINARY_LENGTH_OES                   0x8741
#define LOCAL_GL_PROGRAM_BINARY_RETRIEVABLE_HINT             0x8257
#define LOCAL_GL_PROGRAM_BINDING_ARB                         0x8677
#define LOCAL_GL_PROGRAM_ERROR_POSITION_ARB                  0x864B
#define LOCAL_GL_PROGRAM_ERROR_POSITION_NV                   0x864B
#define LOCAL_GL_PROGRAM_ERROR_STRING_ARB                    0x8874
#define LOCAL_GL_PROGRAM_ERROR_STRING_NV                     0x8874
#define LOCAL_GL_PROGRAM_FORMAT_ARB                          0x8876
#define LOCAL_GL_PROGRAM_FORMAT_ASCII_ARB                    0x8875
#define LOCAL_GL_PROGRAM_INPUT                               0x92E3
#define LOCAL_GL_PROGRAM_INSTRUCTIONS_ARB                    0x88A0
#define LOCAL_GL_PROGRAM_KHR                                 0x82E2
#define LOCAL_GL_PROGRAM_LENGTH_ARB                          0x8627
#define LOCAL_GL_PROGRAM_LENGTH_NV                           0x8627
#define LOCAL_GL_PROGRAM_MATRIX_EXT                          0x8E2D
#define LOCAL_GL_PROGRAM_MATRIX_STACK_DEPTH_EXT              0x8E2F
#define LOCAL_GL_PROGRAM_NATIVE_ADDRESS_REGISTERS_ARB        0x88B2
#define LOCAL_GL_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB         0x8808
#define LOCAL_GL_PROGRAM_NATIVE_ATTRIBS_ARB                  0x88AE
#define LOCAL_GL_PROGRAM_NATIVE_INSTRUCTIONS_ARB             0x88A2
#define LOCAL_GL_PROGRAM_NATIVE_PARAMETERS_ARB               0x88AA
#define LOCAL_GL_PROGRAM_NATIVE_TEMPORARIES_ARB              0x88A6
#define LOCAL_GL_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB         0x880A
#define LOCAL_GL_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB         0x8809
#define LOCAL_GL_PROGRAM_OBJECT_ARB                          0x8B40
#define LOCAL_GL_PROGRAM_OBJECT_EXT                          0x8B40
#define LOCAL_GL_PROGRAM_OUTPUT                              0x92E4
#define LOCAL_GL_PROGRAM_PARAMETERS_ARB                      0x88A8
#define LOCAL_GL_PROGRAM_PARAMETER_NV                        0x8644
#define LOCAL_GL_PROGRAM_PIPELINE                            0x82E4
#define LOCAL_GL_PROGRAM_PIPELINE_BINDING                    0x825A
#define LOCAL_GL_PROGRAM_PIPELINE_BINDING_EXT                0x825A
#define LOCAL_GL_PROGRAM_PIPELINE_KHR                        0x82E4
#define LOCAL_GL_PROGRAM_PIPELINE_OBJECT_EXT                 0x8A4F
#define LOCAL_GL_PROGRAM_POINT_SIZE                          0x8642
#define LOCAL_GL_PROGRAM_POINT_SIZE_ARB                      0x8642
#define LOCAL_GL_PROGRAM_POINT_SIZE_EXT                      0x8642
#define LOCAL_GL_PROGRAM_RESIDENT_NV                         0x8647
#define LOCAL_GL_PROGRAM_RESULT_COMPONENTS_NV                0x8907
#define LOCAL_GL_PROGRAM_SEPARABLE                           0x8258
#define LOCAL_GL_PROGRAM_SEPARABLE_EXT                       0x8258
#define LOCAL_GL_PROGRAM_STRING_ARB                          0x8628
#define LOCAL_GL_PROGRAM_STRING_NV                           0x8628
#define LOCAL_GL_PROGRAM_TARGET_NV                           0x8646
#define LOCAL_GL_PROGRAM_TEMPORARIES_ARB                     0x88A4
#define LOCAL_GL_PROGRAM_TEX_INDIRECTIONS_ARB                0x8807
#define LOCAL_GL_PROGRAM_TEX_INSTRUCTIONS_ARB                0x8806
#define LOCAL_GL_PROGRAM_UNDER_NATIVE_LIMITS_ARB             0x88B6
#define LOCAL_GL_PROJECTION                                  0x1701
#define LOCAL_GL_PROJECTION_MATRIX                           0x0BA7
#define LOCAL_GL_PROJECTION_MATRIX_FLOAT_AS_INT_BITS_OES     0x898E
#define LOCAL_GL_PROJECTION_STACK_DEPTH                      0x0BA4
#define LOCAL_GL_PROTECTED_MEMORY_OBJECT_EXT                 0x959B
#define LOCAL_GL_PROVOKING_VERTEX                            0x8E4F
#define LOCAL_GL_PROVOKING_VERTEX_EXT                        0x8E4F
#define LOCAL_GL_PROXY_COLOR_TABLE                           0x80D3
#define LOCAL_GL_PROXY_COLOR_TABLE_SGI                       0x80D3
#define LOCAL_GL_PROXY_HISTOGRAM                             0x8025
#define LOCAL_GL_PROXY_HISTOGRAM_EXT                         0x8025
#define LOCAL_GL_PROXY_POST_COLOR_MATRIX_COLOR_TABLE         0x80D5
#define LOCAL_GL_PROXY_POST_COLOR_MATRIX_COLOR_TABLE_SGI     0x80D5
#define LOCAL_GL_PROXY_POST_CONVOLUTION_COLOR_TABLE          0x80D4
#define LOCAL_GL_PROXY_POST_CONVOLUTION_COLOR_TABLE_SGI      0x80D4
#define LOCAL_GL_PROXY_POST_IMAGE_TRANSFORM_COLOR_TABLE_HP   0x8163
#define LOCAL_GL_PROXY_TEXTURE_1D                            0x8063
#define LOCAL_GL_PROXY_TEXTURE_1D_ARRAY                      0x8C19
#define LOCAL_GL_PROXY_TEXTURE_1D_ARRAY_EXT                  0x8C19
#define LOCAL_GL_PROXY_TEXTURE_1D_EXT                        0x8063
#define LOCAL_GL_PROXY_TEXTURE_1D_STACK_MESAX                0x875B
#define LOCAL_GL_PROXY_TEXTURE_2D                            0x8064
#define LOCAL_GL_PROXY_TEXTURE_2D_ARRAY                      0x8C1B
#define LOCAL_GL_PROXY_TEXTURE_2D_ARRAY_EXT                  0x8C1B
#define LOCAL_GL_PROXY_TEXTURE_2D_EXT                        0x8064
#define LOCAL_GL_PROXY_TEXTURE_2D_MULTISAMPLE                0x9101
#define LOCAL_GL_PROXY_TEXTURE_2D_MULTISAMPLE_ARRAY          0x9103
#define LOCAL_GL_PROXY_TEXTURE_2D_STACK_MESAX                0x875C
#define LOCAL_GL_PROXY_TEXTURE_3D                            0x8070
#define LOCAL_GL_PROXY_TEXTURE_3D_EXT                        0x8070
#define LOCAL_GL_PROXY_TEXTURE_4D_SGIS                       0x8135
#define LOCAL_GL_PROXY_TEXTURE_COLOR_TABLE_SGI               0x80BD
#define LOCAL_GL_PROXY_TEXTURE_CUBE_MAP                      0x851B
#define LOCAL_GL_PROXY_TEXTURE_CUBE_MAP_ARB                  0x851B
#define LOCAL_GL_PROXY_TEXTURE_CUBE_MAP_ARRAY                0x900B
#define LOCAL_GL_PROXY_TEXTURE_CUBE_MAP_ARRAY_ARB            0x900B
#define LOCAL_GL_PROXY_TEXTURE_CUBE_MAP_EXT                  0x851B
#define LOCAL_GL_PROXY_TEXTURE_RECTANGLE                     0x84F7
#define LOCAL_GL_PROXY_TEXTURE_RECTANGLE_ARB                 0x84F7
#define LOCAL_GL_PROXY_TEXTURE_RECTANGLE_NV                  0x84F7
#define LOCAL_GL_PURGEABLE_APPLE                             0x8A1D
#define LOCAL_GL_PURGED_CONTEXT_RESET_NV                     0x92BB
#define LOCAL_GL_Q                                           0x2003
#define LOCAL_GL_QUADRATIC_ATTENUATION                       0x1209
#define LOCAL_GL_QUADRATIC_CURVE_TO_NV                       0x0A
#define LOCAL_GL_QUADS                                       0x0007
#define LOCAL_GL_QUADS_EXT                                   0x0007
#define LOCAL_GL_QUADS_FOLLOW_PROVOKING_VERTEX_CONVENTION    0x8E4C
#define LOCAL_GL_QUADS_FOLLOW_PROVOKING_VERTEX_CONVENTION_EXT        0x8E4C
#define LOCAL_GL_QUADS_OES                                   0x0007
#define LOCAL_GL_QUAD_ALPHA4_SGIS                            0x811E
#define LOCAL_GL_QUAD_ALPHA8_SGIS                            0x811F
#define LOCAL_GL_QUAD_INTENSITY4_SGIS                        0x8122
#define LOCAL_GL_QUAD_INTENSITY8_SGIS                        0x8123
#define LOCAL_GL_QUAD_LUMINANCE4_SGIS                        0x8120
#define LOCAL_GL_QUAD_LUMINANCE8_SGIS                        0x8121
#define LOCAL_GL_QUAD_MESH_SUN                               0x8614
#define LOCAL_GL_QUAD_STRIP                                  0x0008
#define LOCAL_GL_QUAD_TEXTURE_SELECT_SGIS                    0x8125
#define LOCAL_GL_QUARTER_BIT_ATI                             0x00000010
#define LOCAL_GL_QUERY                                       0x82E3
#define LOCAL_GL_QUERY_ALL_EVENT_BITS_AMD                    0xFFFFFFFF
#define LOCAL_GL_QUERY_BUFFER                                0x9192
#define LOCAL_GL_QUERY_BUFFER_AMD                            0x9192
#define LOCAL_GL_QUERY_BUFFER_BARRIER_BIT                    0x00008000
#define LOCAL_GL_QUERY_BUFFER_BINDING                        0x9193
#define LOCAL_GL_QUERY_BUFFER_BINDING_AMD                    0x9193
#define LOCAL_GL_QUERY_BY_REGION_NO_WAIT                     0x8E16
#define LOCAL_GL_QUERY_BY_REGION_NO_WAIT_INVERTED            0x8E1A
#define LOCAL_GL_QUERY_BY_REGION_NO_WAIT_NV                  0x8E16
#define LOCAL_GL_QUERY_BY_REGION_WAIT                        0x8E15
#define LOCAL_GL_QUERY_BY_REGION_WAIT_INVERTED               0x8E19
#define LOCAL_GL_QUERY_BY_REGION_WAIT_NV                     0x8E15
#define LOCAL_GL_QUERY_COUNTER_BITS                          0x8864
#define LOCAL_GL_QUERY_COUNTER_BITS_ARB                      0x8864
#define LOCAL_GL_QUERY_COUNTER_BITS_EXT                      0x8864
#define LOCAL_GL_QUERY_DEPTH_BOUNDS_FAIL_EVENT_BIT_AMD       0x00000008
#define LOCAL_GL_QUERY_DEPTH_FAIL_EVENT_BIT_AMD              0x00000002
#define LOCAL_GL_QUERY_DEPTH_PASS_EVENT_BIT_AMD              0x00000001
#define LOCAL_GL_QUERY_KHR                                   0x82E3
#define LOCAL_GL_QUERY_NO_WAIT                               0x8E14
#define LOCAL_GL_QUERY_NO_WAIT_INVERTED                      0x8E18
#define LOCAL_GL_QUERY_NO_WAIT_NV                            0x8E14
#define LOCAL_GL_QUERY_OBJECT_AMD                            0x9153
#define LOCAL_GL_QUERY_OBJECT_EXT                            0x9153
#define LOCAL_GL_QUERY_RESOURCE_BUFFEROBJECT_NV              0x9547
#define LOCAL_GL_QUERY_RESOURCE_MEMTYPE_VIDMEM_NV            0x9542
#define LOCAL_GL_QUERY_RESOURCE_RENDERBUFFER_NV              0x9546
#define LOCAL_GL_QUERY_RESOURCE_SYS_RESERVED_NV              0x9544
#define LOCAL_GL_QUERY_RESOURCE_TEXTURE_NV                   0x9545
#define LOCAL_GL_QUERY_RESOURCE_TYPE_VIDMEM_ALLOC_NV         0x9540
#define LOCAL_GL_QUERY_RESULT                                0x8866
#define LOCAL_GL_QUERY_RESULT_ARB                            0x8866
#define LOCAL_GL_QUERY_RESULT_AVAILABLE                      0x8867
#define LOCAL_GL_QUERY_RESULT_AVAILABLE_ARB                  0x8867
#define LOCAL_GL_QUERY_RESULT_AVAILABLE_EXT                  0x8867
#define LOCAL_GL_QUERY_RESULT_EXT                            0x8866
#define LOCAL_GL_QUERY_RESULT_NO_WAIT                        0x9194
#define LOCAL_GL_QUERY_RESULT_NO_WAIT_AMD                    0x9194
#define LOCAL_GL_QUERY_STENCIL_FAIL_EVENT_BIT_AMD            0x00000004
#define LOCAL_GL_QUERY_TARGET                                0x82EA
#define LOCAL_GL_QUERY_WAIT                                  0x8E13
#define LOCAL_GL_QUERY_WAIT_INVERTED                         0x8E17
#define LOCAL_GL_QUERY_WAIT_NV                               0x8E13
#define LOCAL_GL_R                                           0x2002
#define LOCAL_GL_R11F_G11F_B10F                              0x8C3A
#define LOCAL_GL_R11F_G11F_B10F_APPLE                        0x8C3A
#define LOCAL_GL_R11F_G11F_B10F_EXT                          0x8C3A
#define LOCAL_GL_R16                                         0x822A
#define LOCAL_GL_R16F                                        0x822D
#define LOCAL_GL_R16F_EXT                                    0x822D
#define LOCAL_GL_R16I                                        0x8233
#define LOCAL_GL_R16UI                                       0x8234
#define LOCAL_GL_R16_EXT                                     0x822A
#define LOCAL_GL_R16_SNORM                                   0x8F98
#define LOCAL_GL_R16_SNORM_EXT                               0x8F98
#define LOCAL_GL_R1UI_C3F_V3F_SUN                            0x85C6
#define LOCAL_GL_R1UI_C4F_N3F_V3F_SUN                        0x85C8
#define LOCAL_GL_R1UI_C4UB_V3F_SUN                           0x85C5
#define LOCAL_GL_R1UI_N3F_V3F_SUN                            0x85C7
#define LOCAL_GL_R1UI_T2F_C4F_N3F_V3F_SUN                    0x85CB
#define LOCAL_GL_R1UI_T2F_N3F_V3F_SUN                        0x85CA
#define LOCAL_GL_R1UI_T2F_V3F_SUN                            0x85C9
#define LOCAL_GL_R1UI_V3F_SUN                                0x85C4
#define LOCAL_GL_R32F                                        0x822E
#define LOCAL_GL_R32F_EXT                                    0x822E
#define LOCAL_GL_R32I                                        0x8235
#define LOCAL_GL_R32UI                                       0x8236
#define LOCAL_GL_R3_G3_B2                                    0x2A10
#define LOCAL_GL_R8                                          0x8229
#define LOCAL_GL_R8I                                         0x8231
#define LOCAL_GL_R8UI                                        0x8232
#define LOCAL_GL_R8_EXT                                      0x8229
#define LOCAL_GL_R8_SNORM                                    0x8F94
#define LOCAL_GL_RASTERIZER_DISCARD                          0x8C89
#define LOCAL_GL_RASTERIZER_DISCARD_EXT                      0x8C89
#define LOCAL_GL_RASTERIZER_DISCARD_NV                       0x8C89
#define LOCAL_GL_RASTER_FIXED_SAMPLE_LOCATIONS_EXT           0x932A
#define LOCAL_GL_RASTER_MULTISAMPLE_EXT                      0x9327
#define LOCAL_GL_RASTER_POSITION_UNCLIPPED_IBM               0x19262
#define LOCAL_GL_RASTER_SAMPLES_EXT                          0x9328
#define LOCAL_GL_READ_BUFFER                                 0x0C02
#define LOCAL_GL_READ_BUFFER_EXT                             0x0C02
#define LOCAL_GL_READ_BUFFER_NV                              0x0C02
#define LOCAL_GL_READ_FRAMEBUFFER                            0x8CA8
#define LOCAL_GL_READ_FRAMEBUFFER_ANGLE                      0x8CA8
#define LOCAL_GL_READ_FRAMEBUFFER_APPLE                      0x8CA8
#define LOCAL_GL_READ_FRAMEBUFFER_BINDING                    0x8CAA
#define LOCAL_GL_READ_FRAMEBUFFER_BINDING_ANGLE              0x8CAA
#define LOCAL_GL_READ_FRAMEBUFFER_BINDING_APPLE              0x8CAA
#define LOCAL_GL_READ_FRAMEBUFFER_BINDING_EXT                0x8CAA
#define LOCAL_GL_READ_FRAMEBUFFER_BINDING_NV                 0x8CAA
#define LOCAL_GL_READ_FRAMEBUFFER_EXT                        0x8CA8
#define LOCAL_GL_READ_FRAMEBUFFER_NV                         0x8CA8
#define LOCAL_GL_READ_ONLY                                   0x88B8
#define LOCAL_GL_READ_ONLY_ARB                               0x88B8
#define LOCAL_GL_READ_PIXELS                                 0x828C
#define LOCAL_GL_READ_PIXELS_FORMAT                          0x828D
#define LOCAL_GL_READ_PIXELS_TYPE                            0x828E
#define LOCAL_GL_READ_PIXEL_DATA_RANGE_LENGTH_NV             0x887B
#define LOCAL_GL_READ_PIXEL_DATA_RANGE_NV                    0x8879
#define LOCAL_GL_READ_PIXEL_DATA_RANGE_POINTER_NV            0x887D
#define LOCAL_GL_READ_WRITE                                  0x88BA
#define LOCAL_GL_READ_WRITE_ARB                              0x88BA
#define LOCAL_GL_RECIP_ADD_SIGNED_ALPHA_IMG                  0x8C05
#define LOCAL_GL_RECLAIM_MEMORY_HINT_PGI                     0x1A1FE
#define LOCAL_GL_RECT_NV                                     0xF6
#define LOCAL_GL_RED                                         0x1903
#define LOCAL_GL_REDUCE                                      0x8016
#define LOCAL_GL_REDUCE_EXT                                  0x8016
#define LOCAL_GL_RED_BIAS                                    0x0D15
#define LOCAL_GL_RED_BITS                                    0x0D52
#define LOCAL_GL_RED_BIT_ATI                                 0x00000001
#define LOCAL_GL_RED_EXT                                     0x1903
#define LOCAL_GL_RED_INTEGER                                 0x8D94
#define LOCAL_GL_RED_INTEGER_EXT                             0x8D94
#define LOCAL_GL_RED_MAX_CLAMP_INGR                          0x8564
#define LOCAL_GL_RED_MIN_CLAMP_INGR                          0x8560
#define LOCAL_GL_RED_NV                                      0x1903
#define LOCAL_GL_RED_SCALE                                   0x0D14
#define LOCAL_GL_RED_SNORM                                   0x8F90
#define LOCAL_GL_REFERENCED_BY_COMPUTE_SHADER                0x930B
#define LOCAL_GL_REFERENCED_BY_FRAGMENT_SHADER               0x930A
#define LOCAL_GL_REFERENCED_BY_GEOMETRY_SHADER               0x9309
#define LOCAL_GL_REFERENCED_BY_GEOMETRY_SHADER_EXT           0x9309
#define LOCAL_GL_REFERENCED_BY_GEOMETRY_SHADER_OES           0x9309
#define LOCAL_GL_REFERENCED_BY_MESH_SHADER_NV                0x95A0
#define LOCAL_GL_REFERENCED_BY_TASK_SHADER_NV                0x95A1
#define LOCAL_GL_REFERENCED_BY_TESS_CONTROL_SHADER           0x9307
#define LOCAL_GL_REFERENCED_BY_TESS_CONTROL_SHADER_EXT       0x9307
#define LOCAL_GL_REFERENCED_BY_TESS_CONTROL_SHADER_OES       0x9307
#define LOCAL_GL_REFERENCED_BY_TESS_EVALUATION_SHADER        0x9308
#define LOCAL_GL_REFERENCED_BY_TESS_EVALUATION_SHADER_EXT    0x9308
#define LOCAL_GL_REFERENCED_BY_TESS_EVALUATION_SHADER_OES    0x9308
#define LOCAL_GL_REFERENCED_BY_VERTEX_SHADER                 0x9306
#define LOCAL_GL_REFERENCE_PLANE_EQUATION_SGIX               0x817E
#define LOCAL_GL_REFERENCE_PLANE_SGIX                        0x817D
#define LOCAL_GL_REFLECTION_MAP                              0x8512
#define LOCAL_GL_REFLECTION_MAP_ARB                          0x8512
#define LOCAL_GL_REFLECTION_MAP_EXT                          0x8512
#define LOCAL_GL_REFLECTION_MAP_NV                           0x8512
#define LOCAL_GL_REFLECTION_MAP_OES                          0x8512
#define LOCAL_GL_REGISTER_COMBINERS_NV                       0x8522
#define LOCAL_GL_REG_0_ATI                                   0x8921
#define LOCAL_GL_REG_10_ATI                                  0x892B
#define LOCAL_GL_REG_11_ATI                                  0x892C
#define LOCAL_GL_REG_12_ATI                                  0x892D
#define LOCAL_GL_REG_13_ATI                                  0x892E
#define LOCAL_GL_REG_14_ATI                                  0x892F
#define LOCAL_GL_REG_15_ATI                                  0x8930
#define LOCAL_GL_REG_16_ATI                                  0x8931
#define LOCAL_GL_REG_17_ATI                                  0x8932
#define LOCAL_GL_REG_18_ATI                                  0x8933
#define LOCAL_GL_REG_19_ATI                                  0x8934
#define LOCAL_GL_REG_1_ATI                                   0x8922
#define LOCAL_GL_REG_20_ATI                                  0x8935
#define LOCAL_GL_REG_21_ATI                                  0x8936
#define LOCAL_GL_REG_22_ATI                                  0x8937
#define LOCAL_GL_REG_23_ATI                                  0x8938
#define LOCAL_GL_REG_24_ATI                                  0x8939
#define LOCAL_GL_REG_25_ATI                                  0x893A
#define LOCAL_GL_REG_26_ATI                                  0x893B
#define LOCAL_GL_REG_27_ATI                                  0x893C
#define LOCAL_GL_REG_28_ATI                                  0x893D
#define LOCAL_GL_REG_29_ATI                                  0x893E
#define LOCAL_GL_REG_2_ATI                                   0x8923
#define LOCAL_GL_REG_30_ATI                                  0x893F
#define LOCAL_GL_REG_31_ATI                                  0x8940
#define LOCAL_GL_REG_3_ATI                                   0x8924
#define LOCAL_GL_REG_4_ATI                                   0x8925
#define LOCAL_GL_REG_5_ATI                                   0x8926
#define LOCAL_GL_REG_6_ATI                                   0x8927
#define LOCAL_GL_REG_7_ATI                                   0x8928
#define LOCAL_GL_REG_8_ATI                                   0x8929
#define LOCAL_GL_REG_9_ATI                                   0x892A
#define LOCAL_GL_RELATIVE_ARC_TO_NV                          0xFF
#define LOCAL_GL_RELATIVE_CONIC_CURVE_TO_NV                  0x1B
#define LOCAL_GL_RELATIVE_CUBIC_CURVE_TO_NV                  0x0D
#define LOCAL_GL_RELATIVE_HORIZONTAL_LINE_TO_NV              0x07
#define LOCAL_GL_RELATIVE_LARGE_CCW_ARC_TO_NV                0x17
#define LOCAL_GL_RELATIVE_LARGE_CW_ARC_TO_NV                 0x19
#define LOCAL_GL_RELATIVE_LINE_TO_NV                         0x05
#define LOCAL_GL_RELATIVE_MOVE_TO_NV                         0x03
#define LOCAL_GL_RELATIVE_QUADRATIC_CURVE_TO_NV              0x0B
#define LOCAL_GL_RELATIVE_RECT_NV                            0xF7
#define LOCAL_GL_RELATIVE_ROUNDED_RECT2_NV                   0xEB
#define LOCAL_GL_RELATIVE_ROUNDED_RECT4_NV                   0xED
#define LOCAL_GL_RELATIVE_ROUNDED_RECT8_NV                   0xEF
#define LOCAL_GL_RELATIVE_ROUNDED_RECT_NV                    0xE9
#define LOCAL_GL_RELATIVE_SMALL_CCW_ARC_TO_NV                0x13
#define LOCAL_GL_RELATIVE_SMALL_CW_ARC_TO_NV                 0x15
#define LOCAL_GL_RELATIVE_SMOOTH_CUBIC_CURVE_TO_NV           0x11
#define LOCAL_GL_RELATIVE_SMOOTH_QUADRATIC_CURVE_TO_NV       0x0F
#define LOCAL_GL_RELATIVE_VERTICAL_LINE_TO_NV                0x09
#define LOCAL_GL_RELEASED_APPLE                              0x8A19
#define LOCAL_GL_RENDER                                      0x1C00
#define LOCAL_GL_RENDERBUFFER                                0x8D41
#define LOCAL_GL_RENDERBUFFER_ALPHA_SIZE                     0x8D53
#define LOCAL_GL_RENDERBUFFER_ALPHA_SIZE_EXT                 0x8D53
#define LOCAL_GL_RENDERBUFFER_ALPHA_SIZE_OES                 0x8D53
#define LOCAL_GL_RENDERBUFFER_BINDING                        0x8CA7
#define LOCAL_GL_RENDERBUFFER_BINDING_ANGLE                  0x8CA7
#define LOCAL_GL_RENDERBUFFER_BINDING_EXT                    0x8CA7
#define LOCAL_GL_RENDERBUFFER_BINDING_OES                    0x8CA7
#define LOCAL_GL_RENDERBUFFER_BLUE_SIZE                      0x8D52
#define LOCAL_GL_RENDERBUFFER_BLUE_SIZE_EXT                  0x8D52
#define LOCAL_GL_RENDERBUFFER_BLUE_SIZE_OES                  0x8D52
#define LOCAL_GL_RENDERBUFFER_COLOR_SAMPLES_NV               0x8E10
#define LOCAL_GL_RENDERBUFFER_COVERAGE_SAMPLES_NV            0x8CAB
#define LOCAL_GL_RENDERBUFFER_DEPTH_SIZE                     0x8D54
#define LOCAL_GL_RENDERBUFFER_DEPTH_SIZE_EXT                 0x8D54
#define LOCAL_GL_RENDERBUFFER_DEPTH_SIZE_OES                 0x8D54
#define LOCAL_GL_RENDERBUFFER_EXT                            0x8D41
#define LOCAL_GL_RENDERBUFFER_FREE_MEMORY_ATI                0x87FD
#define LOCAL_GL_RENDERBUFFER_GREEN_SIZE                     0x8D51
#define LOCAL_GL_RENDERBUFFER_GREEN_SIZE_EXT                 0x8D51
#define LOCAL_GL_RENDERBUFFER_GREEN_SIZE_OES                 0x8D51
#define LOCAL_GL_RENDERBUFFER_HEIGHT                         0x8D43
#define LOCAL_GL_RENDERBUFFER_HEIGHT_EXT                     0x8D43
#define LOCAL_GL_RENDERBUFFER_HEIGHT_OES                     0x8D43
#define LOCAL_GL_RENDERBUFFER_INTERNAL_FORMAT                0x8D44
#define LOCAL_GL_RENDERBUFFER_INTERNAL_FORMAT_EXT            0x8D44
#define LOCAL_GL_RENDERBUFFER_INTERNAL_FORMAT_OES            0x8D44
#define LOCAL_GL_RENDERBUFFER_OES                            0x8D41
#define LOCAL_GL_RENDERBUFFER_RED_SIZE                       0x8D50
#define LOCAL_GL_RENDERBUFFER_RED_SIZE_EXT                   0x8D50
#define LOCAL_GL_RENDERBUFFER_RED_SIZE_OES                   0x8D50
#define LOCAL_GL_RENDERBUFFER_SAMPLES                        0x8CAB
#define LOCAL_GL_RENDERBUFFER_SAMPLES_ANGLE                  0x8CAB
#define LOCAL_GL_RENDERBUFFER_SAMPLES_APPLE                  0x8CAB
#define LOCAL_GL_RENDERBUFFER_SAMPLES_EXT                    0x8CAB
#define LOCAL_GL_RENDERBUFFER_SAMPLES_IMG                    0x9133
#define LOCAL_GL_RENDERBUFFER_SAMPLES_NV                     0x8CAB
#define LOCAL_GL_RENDERBUFFER_STENCIL_SIZE                   0x8D55
#define LOCAL_GL_RENDERBUFFER_STENCIL_SIZE_EXT               0x8D55
#define LOCAL_GL_RENDERBUFFER_STENCIL_SIZE_OES               0x8D55
#define LOCAL_GL_RENDERBUFFER_STORAGE_SAMPLES_AMD            0x91B2
#define LOCAL_GL_RENDERBUFFER_WIDTH                          0x8D42
#define LOCAL_GL_RENDERBUFFER_WIDTH_EXT                      0x8D42
#define LOCAL_GL_RENDERBUFFER_WIDTH_OES                      0x8D42
#define LOCAL_GL_RENDERER                                    0x1F01
#define LOCAL_GL_RENDER_DIRECT_TO_FRAMEBUFFER_QCOM           0x8FB3
#define LOCAL_GL_RENDER_GPU_MASK_NV                          0x9558
#define LOCAL_GL_RENDER_MODE                                 0x0C40
#define LOCAL_GL_REPEAT                                      0x2901
#define LOCAL_GL_REPLACE                                     0x1E01
#define LOCAL_GL_REPLACEMENT_CODE_ARRAY_POINTER_SUN          0x85C3
#define LOCAL_GL_REPLACEMENT_CODE_ARRAY_STRIDE_SUN           0x85C2
#define LOCAL_GL_REPLACEMENT_CODE_ARRAY_SUN                  0x85C0
#define LOCAL_GL_REPLACEMENT_CODE_ARRAY_TYPE_SUN             0x85C1
#define LOCAL_GL_REPLACEMENT_CODE_SUN                        0x81D8
#define LOCAL_GL_REPLACE_EXT                                 0x8062
#define LOCAL_GL_REPLACE_MIDDLE_SUN                          0x0002
#define LOCAL_GL_REPLACE_OLDEST_SUN                          0x0003
#define LOCAL_GL_REPLACE_VALUE_AMD                           0x874B
#define LOCAL_GL_REPLICATE_BORDER                            0x8153
#define LOCAL_GL_REPLICATE_BORDER_HP                         0x8153
#define LOCAL_GL_REPRESENTATIVE_FRAGMENT_TEST_NV             0x937F
#define LOCAL_GL_REQUIRED_TEXTURE_IMAGE_UNITS_OES            0x8D68
#define LOCAL_GL_RESAMPLE_AVERAGE_OML                        0x8988
#define LOCAL_GL_RESAMPLE_DECIMATE_OML                       0x8989
#define LOCAL_GL_RESAMPLE_DECIMATE_SGIX                      0x8430
#define LOCAL_GL_RESAMPLE_REPLICATE_OML                      0x8986
#define LOCAL_GL_RESAMPLE_REPLICATE_SGIX                     0x8433
#define LOCAL_GL_RESAMPLE_ZERO_FILL_OML                      0x8987
#define LOCAL_GL_RESAMPLE_ZERO_FILL_SGIX                     0x8434
#define LOCAL_GL_RESCALE_NORMAL                              0x803A
#define LOCAL_GL_RESCALE_NORMAL_EXT                          0x803A
#define LOCAL_GL_RESET_NOTIFICATION_STRATEGY                 0x8256
#define LOCAL_GL_RESET_NOTIFICATION_STRATEGY_ARB             0x8256
#define LOCAL_GL_RESET_NOTIFICATION_STRATEGY_EXT             0x8256
#define LOCAL_GL_RESET_NOTIFICATION_STRATEGY_KHR             0x8256
#define LOCAL_GL_RESTART_PATH_NV                             0xF0
#define LOCAL_GL_RESTART_SUN                                 0x0001
#define LOCAL_GL_RETAINED_APPLE                              0x8A1B
#define LOCAL_GL_RETURN                                      0x0102
#define LOCAL_GL_RG                                          0x8227
#define LOCAL_GL_RG16                                        0x822C
#define LOCAL_GL_RG16F                                       0x822F
#define LOCAL_GL_RG16F_EXT                                   0x822F
#define LOCAL_GL_RG16I                                       0x8239
#define LOCAL_GL_RG16UI                                      0x823A
#define LOCAL_GL_RG16_EXT                                    0x822C
#define LOCAL_GL_RG16_SNORM                                  0x8F99
#define LOCAL_GL_RG16_SNORM_EXT                              0x8F99
#define LOCAL_GL_RG32F                                       0x8230
#define LOCAL_GL_RG32F_EXT                                   0x8230
#define LOCAL_GL_RG32I                                       0x823B
#define LOCAL_GL_RG32UI                                      0x823C
#define LOCAL_GL_RG8                                         0x822B
#define LOCAL_GL_RG8I                                        0x8237
#define LOCAL_GL_RG8UI                                       0x8238
#define LOCAL_GL_RG8_EXT                                     0x822B
#define LOCAL_GL_RG8_SNORM                                   0x8F95
#define LOCAL_GL_RGB                                         0x1907
#define LOCAL_GL_RGB10                                       0x8052
#define LOCAL_GL_RGB10_A2                                    0x8059
#define LOCAL_GL_RGB10_A2UI                                  0x906F
#define LOCAL_GL_RGB10_A2_EXT                                0x8059
#define LOCAL_GL_RGB10_EXT                                   0x8052
#define LOCAL_GL_RGB12                                       0x8053
#define LOCAL_GL_RGB12_EXT                                   0x8053
#define LOCAL_GL_RGB16                                       0x8054
#define LOCAL_GL_RGB16F                                      0x881B
#define LOCAL_GL_RGB16F_ARB                                  0x881B
#define LOCAL_GL_RGB16F_EXT                                  0x881B
#define LOCAL_GL_RGB16I                                      0x8D89
#define LOCAL_GL_RGB16I_EXT                                  0x8D89
#define LOCAL_GL_RGB16UI                                     0x8D77
#define LOCAL_GL_RGB16UI_EXT                                 0x8D77
#define LOCAL_GL_RGB16_EXT                                   0x8054
#define LOCAL_GL_RGB16_SNORM                                 0x8F9A
#define LOCAL_GL_RGB16_SNORM_EXT                             0x8F9A
#define LOCAL_GL_RGB2_EXT                                    0x804E
#define LOCAL_GL_RGB32F                                      0x8815
#define LOCAL_GL_RGB32F_ARB                                  0x8815
#define LOCAL_GL_RGB32F_EXT                                  0x8815
#define LOCAL_GL_RGB32I                                      0x8D83
#define LOCAL_GL_RGB32I_EXT                                  0x8D83
#define LOCAL_GL_RGB32UI                                     0x8D71
#define LOCAL_GL_RGB32UI_EXT                                 0x8D71
#define LOCAL_GL_RGB4                                        0x804F
#define LOCAL_GL_RGB4_EXT                                    0x804F
#define LOCAL_GL_RGB4_S3TC                                   0x83A1
#define LOCAL_GL_RGB5                                        0x8050
#define LOCAL_GL_RGB565                                      0x8D62
#define LOCAL_GL_RGB565_OES                                  0x8D62
#define LOCAL_GL_RGB5_A1                                     0x8057
#define LOCAL_GL_RGB5_A1_EXT                                 0x8057
#define LOCAL_GL_RGB5_A1_OES                                 0x8057
#define LOCAL_GL_RGB5_EXT                                    0x8050
#define LOCAL_GL_RGB8                                        0x8051
#define LOCAL_GL_RGB8I                                       0x8D8F
#define LOCAL_GL_RGB8I_EXT                                   0x8D8F
#define LOCAL_GL_RGB8UI                                      0x8D7D
#define LOCAL_GL_RGB8UI_EXT                                  0x8D7D
#define LOCAL_GL_RGB8_EXT                                    0x8051
#define LOCAL_GL_RGB8_OES                                    0x8051
#define LOCAL_GL_RGB8_SNORM                                  0x8F96
#define LOCAL_GL_RGB9_E5                                     0x8C3D
#define LOCAL_GL_RGB9_E5_APPLE                               0x8C3D
#define LOCAL_GL_RGB9_E5_EXT                                 0x8C3D
#define LOCAL_GL_RGBA                                        0x1908
#define LOCAL_GL_RGBA12                                      0x805A
#define LOCAL_GL_RGBA12_EXT                                  0x805A
#define LOCAL_GL_RGBA16                                      0x805B
#define LOCAL_GL_RGBA16F                                     0x881A
#define LOCAL_GL_RGBA16F_ARB                                 0x881A
#define LOCAL_GL_RGBA16F_EXT                                 0x881A
#define LOCAL_GL_RGBA16I                                     0x8D88
#define LOCAL_GL_RGBA16I_EXT                                 0x8D88
#define LOCAL_GL_RGBA16UI                                    0x8D76
#define LOCAL_GL_RGBA16UI_EXT                                0x8D76
#define LOCAL_GL_RGBA16_EXT                                  0x805B
#define LOCAL_GL_RGBA16_SNORM                                0x8F9B
#define LOCAL_GL_RGBA16_SNORM_EXT                            0x8F9B
#define LOCAL_GL_RGBA2                                       0x8055
#define LOCAL_GL_RGBA2_EXT                                   0x8055
#define LOCAL_GL_RGBA32F                                     0x8814
#define LOCAL_GL_RGBA32F_ARB                                 0x8814
#define LOCAL_GL_RGBA32F_EXT                                 0x8814
#define LOCAL_GL_RGBA32I                                     0x8D82
#define LOCAL_GL_RGBA32I_EXT                                 0x8D82
#define LOCAL_GL_RGBA32UI                                    0x8D70
#define LOCAL_GL_RGBA32UI_EXT                                0x8D70
#define LOCAL_GL_RGBA4                                       0x8056
#define LOCAL_GL_RGBA4_DXT5_S3TC                             0x83A5
#define LOCAL_GL_RGBA4_EXT                                   0x8056
#define LOCAL_GL_RGBA4_OES                                   0x8056
#define LOCAL_GL_RGBA4_S3TC                                  0x83A3
#define LOCAL_GL_RGBA8                                       0x8058
#define LOCAL_GL_RGBA8I                                      0x8D8E
#define LOCAL_GL_RGBA8I_EXT                                  0x8D8E
#define LOCAL_GL_RGBA8UI                                     0x8D7C
#define LOCAL_GL_RGBA8UI_EXT                                 0x8D7C
#define LOCAL_GL_RGBA8_EXT                                   0x8058
#define LOCAL_GL_RGBA8_OES                                   0x8058
#define LOCAL_GL_RGBA8_SNORM                                 0x8F97
#define LOCAL_GL_RGBA_DXT5_S3TC                              0x83A4
#define LOCAL_GL_RGBA_FLOAT16_APPLE                          0x881A
#define LOCAL_GL_RGBA_FLOAT16_ATI                            0x881A
#define LOCAL_GL_RGBA_FLOAT32_APPLE                          0x8814
#define LOCAL_GL_RGBA_FLOAT32_ATI                            0x8814
#define LOCAL_GL_RGBA_FLOAT_MODE_ARB                         0x8820
#define LOCAL_GL_RGBA_FLOAT_MODE_ATI                         0x8820
#define LOCAL_GL_RGBA_INTEGER                                0x8D99
#define LOCAL_GL_RGBA_INTEGER_EXT                            0x8D99
#define LOCAL_GL_RGBA_INTEGER_MODE_EXT                       0x8D9E
#define LOCAL_GL_RGBA_MODE                                   0x0C31
#define LOCAL_GL_RGBA_S3TC                                   0x83A2
#define LOCAL_GL_RGBA_SIGNED_COMPONENTS_EXT                  0x8C3C
#define LOCAL_GL_RGBA_SNORM                                  0x8F93
#define LOCAL_GL_RGBA_UNSIGNED_DOT_PRODUCT_MAPPING_NV        0x86D9
#define LOCAL_GL_RGB_422_APPLE                               0x8A1F
#define LOCAL_GL_RGB_FLOAT16_APPLE                           0x881B
#define LOCAL_GL_RGB_FLOAT16_ATI                             0x881B
#define LOCAL_GL_RGB_FLOAT32_APPLE                           0x8815
#define LOCAL_GL_RGB_FLOAT32_ATI                             0x8815
#define LOCAL_GL_RGB_INTEGER                                 0x8D98
#define LOCAL_GL_RGB_INTEGER_EXT                             0x8D98
#define LOCAL_GL_RGB_RAW_422_APPLE                           0x8A51
#define LOCAL_GL_RGB_S3TC                                    0x83A0
#define LOCAL_GL_RGB_SCALE                                   0x8573
#define LOCAL_GL_RGB_SCALE_ARB                               0x8573
#define LOCAL_GL_RGB_SCALE_EXT                               0x8573
#define LOCAL_GL_RGB_SNORM                                   0x8F92
#define LOCAL_GL_RG_EXT                                      0x8227
#define LOCAL_GL_RG_INTEGER                                  0x8228
#define LOCAL_GL_RG_SNORM                                    0x8F91
#define LOCAL_GL_RIGHT                                       0x0407
#define LOCAL_GL_ROBUST_GPU_TIMEOUT_MS_KHR                   0x82FD
#define LOCAL_GL_ROUNDED_RECT2_NV                            0xEA
#define LOCAL_GL_ROUNDED_RECT4_NV                            0xEC
#define LOCAL_GL_ROUNDED_RECT8_NV                            0xEE
#define LOCAL_GL_ROUNDED_RECT_NV                             0xE8
#define LOCAL_GL_ROUND_NV                                    0x90A4
#define LOCAL_GL_S                                           0x2000
#define LOCAL_GL_SAMPLER                                     0x82E6
#define LOCAL_GL_SAMPLER_1D                                  0x8B5D
#define LOCAL_GL_SAMPLER_1D_ARB                              0x8B5D
#define LOCAL_GL_SAMPLER_1D_ARRAY                            0x8DC0
#define LOCAL_GL_SAMPLER_1D_ARRAY_EXT                        0x8DC0
#define LOCAL_GL_SAMPLER_1D_ARRAY_SHADOW                     0x8DC3
#define LOCAL_GL_SAMPLER_1D_ARRAY_SHADOW_EXT                 0x8DC3
#define LOCAL_GL_SAMPLER_1D_SHADOW                           0x8B61
#define LOCAL_GL_SAMPLER_1D_SHADOW_ARB                       0x8B61
#define LOCAL_GL_SAMPLER_2D                                  0x8B5E
#define LOCAL_GL_SAMPLER_2D_ARB                              0x8B5E
#define LOCAL_GL_SAMPLER_2D_ARRAY                            0x8DC1
#define LOCAL_GL_SAMPLER_2D_ARRAY_EXT                        0x8DC1
#define LOCAL_GL_SAMPLER_2D_ARRAY_SHADOW                     0x8DC4
#define LOCAL_GL_SAMPLER_2D_ARRAY_SHADOW_EXT                 0x8DC4
#define LOCAL_GL_SAMPLER_2D_ARRAY_SHADOW_NV                  0x8DC4
#define LOCAL_GL_SAMPLER_2D_MULTISAMPLE                      0x9108
#define LOCAL_GL_SAMPLER_2D_MULTISAMPLE_ARRAY                0x910B
#define LOCAL_GL_SAMPLER_2D_MULTISAMPLE_ARRAY_OES            0x910B
#define LOCAL_GL_SAMPLER_2D_RECT                             0x8B63
#define LOCAL_GL_SAMPLER_2D_RECT_ARB                         0x8B63
#define LOCAL_GL_SAMPLER_2D_RECT_SHADOW                      0x8B64
#define LOCAL_GL_SAMPLER_2D_RECT_SHADOW_ARB                  0x8B64
#define LOCAL_GL_SAMPLER_2D_SHADOW                           0x8B62
#define LOCAL_GL_SAMPLER_2D_SHADOW_ARB                       0x8B62
#define LOCAL_GL_SAMPLER_2D_SHADOW_EXT                       0x8B62
#define LOCAL_GL_SAMPLER_3D                                  0x8B5F
#define LOCAL_GL_SAMPLER_3D_ARB                              0x8B5F
#define LOCAL_GL_SAMPLER_3D_OES                              0x8B5F
#define LOCAL_GL_SAMPLER_BINDING                             0x8919
#define LOCAL_GL_SAMPLER_BUFFER                              0x8DC2
#define LOCAL_GL_SAMPLER_BUFFER_AMD                          0x9001
#define LOCAL_GL_SAMPLER_BUFFER_EXT                          0x8DC2
#define LOCAL_GL_SAMPLER_BUFFER_OES                          0x8DC2
#define LOCAL_GL_SAMPLER_CUBE                                0x8B60
#define LOCAL_GL_SAMPLER_CUBE_ARB                            0x8B60
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY                      0x900C
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_ARB                  0x900C
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_EXT                  0x900C
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_OES                  0x900C
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW               0x900D
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW_ARB           0x900D
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW_EXT           0x900D
#define LOCAL_GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW_OES           0x900D
#define LOCAL_GL_SAMPLER_CUBE_SHADOW                         0x8DC5
#define LOCAL_GL_SAMPLER_CUBE_SHADOW_EXT                     0x8DC5
#define LOCAL_GL_SAMPLER_CUBE_SHADOW_NV                      0x8DC5
#define LOCAL_GL_SAMPLER_EXTERNAL_2D_Y2Y_EXT                 0x8BE7
#define LOCAL_GL_SAMPLER_EXTERNAL_OES                        0x8D66
#define LOCAL_GL_SAMPLER_KHR                                 0x82E6
#define LOCAL_GL_SAMPLER_OBJECT_AMD                          0x9155
#define LOCAL_GL_SAMPLER_RENDERBUFFER_NV                     0x8E56
#define LOCAL_GL_SAMPLES                                     0x80A9
#define LOCAL_GL_SAMPLES_3DFX                                0x86B4
#define LOCAL_GL_SAMPLES_ARB                                 0x80A9
#define LOCAL_GL_SAMPLES_EXT                                 0x80A9
#define LOCAL_GL_SAMPLES_PASSED                              0x8914
#define LOCAL_GL_SAMPLES_PASSED_ARB                          0x8914
#define LOCAL_GL_SAMPLES_SGIS                                0x80A9
#define LOCAL_GL_SAMPLE_ALPHA_TO_COVERAGE                    0x809E
#define LOCAL_GL_SAMPLE_ALPHA_TO_COVERAGE_ARB                0x809E
#define LOCAL_GL_SAMPLE_ALPHA_TO_MASK_EXT                    0x809E
#define LOCAL_GL_SAMPLE_ALPHA_TO_MASK_SGIS                   0x809E
#define LOCAL_GL_SAMPLE_ALPHA_TO_ONE                         0x809F
#define LOCAL_GL_SAMPLE_ALPHA_TO_ONE_ARB                     0x809F
#define LOCAL_GL_SAMPLE_ALPHA_TO_ONE_EXT                     0x809F
#define LOCAL_GL_SAMPLE_ALPHA_TO_ONE_SGIS                    0x809F
#define LOCAL_GL_SAMPLE_BUFFERS                              0x80A8
#define LOCAL_GL_SAMPLE_BUFFERS_3DFX                         0x86B3
#define LOCAL_GL_SAMPLE_BUFFERS_ARB                          0x80A8
#define LOCAL_GL_SAMPLE_BUFFERS_EXT                          0x80A8
#define LOCAL_GL_SAMPLE_BUFFERS_SGIS                         0x80A8
#define LOCAL_GL_SAMPLE_COVERAGE                             0x80A0
#define LOCAL_GL_SAMPLE_COVERAGE_ARB                         0x80A0
#define LOCAL_GL_SAMPLE_COVERAGE_INVERT                      0x80AB
#define LOCAL_GL_SAMPLE_COVERAGE_INVERT_ARB                  0x80AB
#define LOCAL_GL_SAMPLE_COVERAGE_VALUE                       0x80AA
#define LOCAL_GL_SAMPLE_COVERAGE_VALUE_ARB                   0x80AA
#define LOCAL_GL_SAMPLE_LOCATION_ARB                         0x8E50
#define LOCAL_GL_SAMPLE_LOCATION_NV                          0x8E50
#define LOCAL_GL_SAMPLE_LOCATION_PIXEL_GRID_HEIGHT_ARB       0x933F
#define LOCAL_GL_SAMPLE_LOCATION_PIXEL_GRID_HEIGHT_NV        0x933F
#define LOCAL_GL_SAMPLE_LOCATION_PIXEL_GRID_WIDTH_ARB        0x933E
#define LOCAL_GL_SAMPLE_LOCATION_PIXEL_GRID_WIDTH_NV         0x933E
#define LOCAL_GL_SAMPLE_LOCATION_SUBPIXEL_BITS_ARB           0x933D
#define LOCAL_GL_SAMPLE_LOCATION_SUBPIXEL_BITS_NV            0x933D
#define LOCAL_GL_SAMPLE_MASK                                 0x8E51
#define LOCAL_GL_SAMPLE_MASK_EXT                             0x80A0
#define LOCAL_GL_SAMPLE_MASK_INVERT_EXT                      0x80AB
#define LOCAL_GL_SAMPLE_MASK_INVERT_SGIS                     0x80AB
#define LOCAL_GL_SAMPLE_MASK_NV                              0x8E51
#define LOCAL_GL_SAMPLE_MASK_SGIS                            0x80A0
#define LOCAL_GL_SAMPLE_MASK_VALUE                           0x8E52
#define LOCAL_GL_SAMPLE_MASK_VALUE_EXT                       0x80AA
#define LOCAL_GL_SAMPLE_MASK_VALUE_NV                        0x8E52
#define LOCAL_GL_SAMPLE_MASK_VALUE_SGIS                      0x80AA
#define LOCAL_GL_SAMPLE_PATTERN_EXT                          0x80AC
#define LOCAL_GL_SAMPLE_PATTERN_SGIS                         0x80AC
#define LOCAL_GL_SAMPLE_POSITION                             0x8E50
#define LOCAL_GL_SAMPLE_POSITION_NV                          0x8E50
#define LOCAL_GL_SAMPLE_SHADING                              0x8C36
#define LOCAL_GL_SAMPLE_SHADING_ARB                          0x8C36
#define LOCAL_GL_SAMPLE_SHADING_OES                          0x8C36
#define LOCAL_GL_SATURATE_BIT_ATI                            0x00000040
#define LOCAL_GL_SCALAR_EXT                                  0x87BE
#define LOCAL_GL_SCALEBIAS_HINT_SGIX                         0x8322
#define LOCAL_GL_SCALED_RESOLVE_FASTEST_EXT                  0x90BA
#define LOCAL_GL_SCALED_RESOLVE_NICEST_EXT                   0x90BB
#define LOCAL_GL_SCALE_BY_FOUR_NV                            0x853F
#define LOCAL_GL_SCALE_BY_ONE_HALF_NV                        0x8540
#define LOCAL_GL_SCALE_BY_TWO_NV                             0x853E
#define LOCAL_GL_SCISSOR_BIT                                 0x00080000
#define LOCAL_GL_SCISSOR_BOX                                 0x0C10
#define LOCAL_GL_SCISSOR_BOX_EXCLUSIVE_NV                    0x9556
#define LOCAL_GL_SCISSOR_COMMAND_NV                          0x0011
#define LOCAL_GL_SCISSOR_TEST                                0x0C11
#define LOCAL_GL_SCISSOR_TEST_EXCLUSIVE_NV                   0x9555
#define LOCAL_GL_SCREEN                                      0x9295
#define LOCAL_GL_SCREEN_COORDINATES_REND                     0x8490
#define LOCAL_GL_SCREEN_KHR                                  0x9295
#define LOCAL_GL_SCREEN_NV                                   0x9295
#define LOCAL_GL_SECONDARY_COLOR_ARRAY                       0x845E
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_ADDRESS_NV            0x8F27
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_BUFFER_BINDING        0x889C
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_BUFFER_BINDING_ARB    0x889C
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_EXT                   0x845E
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_LENGTH_NV             0x8F31
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_LIST_IBM              103077
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_LIST_STRIDE_IBM       103087
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_POINTER               0x845D
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_POINTER_EXT           0x845D
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_SIZE                  0x845A
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_SIZE_EXT              0x845A
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_STRIDE                0x845C
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_STRIDE_EXT            0x845C
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_TYPE                  0x845B
#define LOCAL_GL_SECONDARY_COLOR_ARRAY_TYPE_EXT              0x845B
#define LOCAL_GL_SECONDARY_COLOR_NV                          0x852D
#define LOCAL_GL_SECONDARY_INTERPOLATOR_ATI                  0x896D
#define LOCAL_GL_SELECT                                      0x1C02
#define LOCAL_GL_SELECTION_BUFFER_POINTER                    0x0DF3
#define LOCAL_GL_SELECTION_BUFFER_SIZE                       0x0DF4
#define LOCAL_GL_SEMAPHORE_TYPE_BINARY_NV                    0x95B4
#define LOCAL_GL_SEMAPHORE_TYPE_NV                           0x95B3
#define LOCAL_GL_SEMAPHORE_TYPE_TIMELINE_NV                  0x95B5
#define LOCAL_GL_SEPARABLE_2D                                0x8012
#define LOCAL_GL_SEPARABLE_2D_EXT                            0x8012
#define LOCAL_GL_SEPARATE_ATTRIBS                            0x8C8D
#define LOCAL_GL_SEPARATE_ATTRIBS_EXT                        0x8C8D
#define LOCAL_GL_SEPARATE_ATTRIBS_NV                         0x8C8D
#define LOCAL_GL_SEPARATE_SPECULAR_COLOR                     0x81FA
#define LOCAL_GL_SEPARATE_SPECULAR_COLOR_EXT                 0x81FA
#define LOCAL_GL_SET                                         0x150F
#define LOCAL_GL_SET_AMD                                     0x874A
#define LOCAL_GL_SGX_BINARY_IMG                              0x8C0A
#define LOCAL_GL_SGX_PROGRAM_BINARY_IMG                      0x9130
#define LOCAL_GL_SHADER                                      0x82E1
#define LOCAL_GL_SHADER_BINARY_DMP                           0x9250
#define LOCAL_GL_SHADER_BINARY_FORMATS                       0x8DF8
#define LOCAL_GL_SHADER_BINARY_FORMAT_SPIR_V                 0x9551
#define LOCAL_GL_SHADER_BINARY_FORMAT_SPIR_V_ARB             0x9551
#define LOCAL_GL_SHADER_BINARY_VIV                           0x8FC4
#define LOCAL_GL_SHADER_COMPILER                             0x8DFA
#define LOCAL_GL_SHADER_CONSISTENT_NV                        0x86DD
#define LOCAL_GL_SHADER_GLOBAL_ACCESS_BARRIER_BIT_NV         0x00000010
#define LOCAL_GL_SHADER_IMAGE_ACCESS_BARRIER_BIT             0x00000020
#define LOCAL_GL_SHADER_IMAGE_ACCESS_BARRIER_BIT_EXT         0x00000020
#define LOCAL_GL_SHADER_IMAGE_ATOMIC                         0x82A6
#define LOCAL_GL_SHADER_IMAGE_LOAD                           0x82A4
#define LOCAL_GL_SHADER_IMAGE_STORE                          0x82A5
#define LOCAL_GL_SHADER_INCLUDE_ARB                          0x8DAE
#define LOCAL_GL_SHADER_KHR                                  0x82E1
#define LOCAL_GL_SHADER_OBJECT_ARB                           0x8B48
#define LOCAL_GL_SHADER_OBJECT_EXT                           0x8B48
#define LOCAL_GL_SHADER_OPERATION_NV                         0x86DF
#define LOCAL_GL_SHADER_PIXEL_LOCAL_STORAGE_EXT              0x8F64
#define LOCAL_GL_SHADER_SOURCE_LENGTH                        0x8B88
#define LOCAL_GL_SHADER_STORAGE_BARRIER_BIT                  0x00002000
#define LOCAL_GL_SHADER_STORAGE_BLOCK                        0x92E6
#define LOCAL_GL_SHADER_STORAGE_BUFFER                       0x90D2
#define LOCAL_GL_SHADER_STORAGE_BUFFER_BINDING               0x90D3
#define LOCAL_GL_SHADER_STORAGE_BUFFER_OFFSET_ALIGNMENT      0x90DF
#define LOCAL_GL_SHADER_STORAGE_BUFFER_SIZE                  0x90D5
#define LOCAL_GL_SHADER_STORAGE_BUFFER_START                 0x90D4
#define LOCAL_GL_SHADER_TYPE                                 0x8B4F
#define LOCAL_GL_SHADE_MODEL                                 0x0B54
#define LOCAL_GL_SHADING_LANGUAGE_VERSION                    0x8B8C
#define LOCAL_GL_SHADING_LANGUAGE_VERSION_ARB                0x8B8C
#define LOCAL_GL_SHADING_RATE_16_INVOCATIONS_PER_PIXEL_NV    0x956F
#define LOCAL_GL_SHADING_RATE_1X1_PIXELS_QCOM                0x96A6
#define LOCAL_GL_SHADING_RATE_1X2_PIXELS_QCOM                0x96A7
#define LOCAL_GL_SHADING_RATE_1X4_PIXELS_QCOM                0x96AA
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_1X2_PIXELS_NV 0x9566
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_2X1_PIXELS_NV 0x9567
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_2X2_PIXELS_NV 0x9568
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_2X4_PIXELS_NV 0x9569
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_4X2_PIXELS_NV 0x956A
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_4X4_PIXELS_NV 0x956B
#define LOCAL_GL_SHADING_RATE_1_INVOCATION_PER_PIXEL_NV      0x9565
#define LOCAL_GL_SHADING_RATE_2X1_PIXELS_QCOM                0x96A8
#define LOCAL_GL_SHADING_RATE_2X2_PIXELS_QCOM                0x96A9
#define LOCAL_GL_SHADING_RATE_2X4_PIXELS_QCOM                0x96AD
#define LOCAL_GL_SHADING_RATE_2_INVOCATIONS_PER_PIXEL_NV     0x956C
#define LOCAL_GL_SHADING_RATE_4X1_PIXELS_QCOM                0x96AB
#define LOCAL_GL_SHADING_RATE_4X2_PIXELS_QCOM                0x96AC
#define LOCAL_GL_SHADING_RATE_4X4_PIXELS_QCOM                0x96AE
#define LOCAL_GL_SHADING_RATE_4_INVOCATIONS_PER_PIXEL_NV     0x956D
#define LOCAL_GL_SHADING_RATE_8_INVOCATIONS_PER_PIXEL_NV     0x956E
#define LOCAL_GL_SHADING_RATE_IMAGE_BINDING_NV               0x955B
#define LOCAL_GL_SHADING_RATE_IMAGE_NV                       0x9563
#define LOCAL_GL_SHADING_RATE_IMAGE_PALETTE_COUNT_NV         0x95B2
#define LOCAL_GL_SHADING_RATE_IMAGE_PALETTE_SIZE_NV          0x955E
#define LOCAL_GL_SHADING_RATE_IMAGE_PER_PRIMITIVE_NV         0x95B1
#define LOCAL_GL_SHADING_RATE_IMAGE_TEXEL_HEIGHT_NV          0x955D
#define LOCAL_GL_SHADING_RATE_IMAGE_TEXEL_WIDTH_NV           0x955C
#define LOCAL_GL_SHADING_RATE_NO_INVOCATIONS_NV              0x9564
#define LOCAL_GL_SHADING_RATE_PRESERVE_ASPECT_RATIO_QCOM     0x96A5
#define LOCAL_GL_SHADING_RATE_QCOM                           0x96A4
#define LOCAL_GL_SHADING_RATE_SAMPLE_ORDER_DEFAULT_NV        0x95AE
#define LOCAL_GL_SHADING_RATE_SAMPLE_ORDER_PIXEL_MAJOR_NV    0x95AF
#define LOCAL_GL_SHADING_RATE_SAMPLE_ORDER_SAMPLE_MAJOR_NV   0x95B0
#define LOCAL_GL_SHADOW_AMBIENT_SGIX                         0x80BF
#define LOCAL_GL_SHADOW_ATTENUATION_EXT                      0x834E
#define LOCAL_GL_SHARED_EDGE_NV                              0xC0
#define LOCAL_GL_SHARED_TEXTURE_PALETTE_EXT                  0x81FB
#define LOCAL_GL_SHARPEN_TEXTURE_FUNC_POINTS_SGIS            0x80B0
#define LOCAL_GL_SHININESS                                   0x1601
#define LOCAL_GL_SHORT                                       0x1402
#define LOCAL_GL_SIGNALED                                    0x9119
#define LOCAL_GL_SIGNALED_APPLE                              0x9119
#define LOCAL_GL_SIGNED_ALPHA8_NV                            0x8706
#define LOCAL_GL_SIGNED_ALPHA_NV                             0x8705
#define LOCAL_GL_SIGNED_HILO16_NV                            0x86FA
#define LOCAL_GL_SIGNED_HILO8_NV                             0x885F
#define LOCAL_GL_SIGNED_HILO_NV                              0x86F9
#define LOCAL_GL_SIGNED_IDENTITY_NV                          0x853C
#define LOCAL_GL_SIGNED_INTENSITY8_NV                        0x8708
#define LOCAL_GL_SIGNED_INTENSITY_NV                         0x8707
#define LOCAL_GL_SIGNED_LUMINANCE8_ALPHA8_NV                 0x8704
#define LOCAL_GL_SIGNED_LUMINANCE8_NV                        0x8702
#define LOCAL_GL_SIGNED_LUMINANCE_ALPHA_NV                   0x8703
#define LOCAL_GL_SIGNED_LUMINANCE_NV                         0x8701
#define LOCAL_GL_SIGNED_NEGATE_NV                            0x853D
#define LOCAL_GL_SIGNED_NORMALIZED                           0x8F9C
#define LOCAL_GL_SIGNED_RGB8_NV                              0x86FF
#define LOCAL_GL_SIGNED_RGB8_UNSIGNED_ALPHA8_NV              0x870D
#define LOCAL_GL_SIGNED_RGBA8_NV                             0x86FC
#define LOCAL_GL_SIGNED_RGBA_NV                              0x86FB
#define LOCAL_GL_SIGNED_RGB_NV                               0x86FE
#define LOCAL_GL_SIGNED_RGB_UNSIGNED_ALPHA_NV                0x870C
#define LOCAL_GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_TEST         0x82AC
#define LOCAL_GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_WRITE        0x82AE
#define LOCAL_GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_TEST       0x82AD
#define LOCAL_GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_WRITE      0x82AF
#define LOCAL_GL_SINGLE_COLOR                                0x81F9
#define LOCAL_GL_SINGLE_COLOR_EXT                            0x81F9
#define LOCAL_GL_SKIP_COMPONENTS1_NV                         -6
#define LOCAL_GL_SKIP_COMPONENTS2_NV                         -5
#define LOCAL_GL_SKIP_COMPONENTS3_NV                         -4
#define LOCAL_GL_SKIP_COMPONENTS4_NV                         -3
#define LOCAL_GL_SKIP_DECODE_EXT                             0x8A4A
#define LOCAL_GL_SKIP_MISSING_GLYPH_NV                       0x90A9
#define LOCAL_GL_SLICE_ACCUM_SUN                             0x85CC
#define LOCAL_GL_SLIM10U_SGIX                                0x831E
#define LOCAL_GL_SLIM12S_SGIX                                0x831F
#define LOCAL_GL_SLIM8U_SGIX                                 0x831D
#define LOCAL_GL_SLUMINANCE                                  0x8C46
#define LOCAL_GL_SLUMINANCE8                                 0x8C47
#define LOCAL_GL_SLUMINANCE8_ALPHA8                          0x8C45
#define LOCAL_GL_SLUMINANCE8_ALPHA8_EXT                      0x8C45
#define LOCAL_GL_SLUMINANCE8_ALPHA8_NV                       0x8C45
#define LOCAL_GL_SLUMINANCE8_EXT                             0x8C47
#define LOCAL_GL_SLUMINANCE8_NV                              0x8C47
#define LOCAL_GL_SLUMINANCE_ALPHA                            0x8C44
#define LOCAL_GL_SLUMINANCE_ALPHA_EXT                        0x8C44
#define LOCAL_GL_SLUMINANCE_ALPHA_NV                         0x8C44
#define LOCAL_GL_SLUMINANCE_EXT                              0x8C46
#define LOCAL_GL_SLUMINANCE_NV                               0x8C46
#define LOCAL_GL_SMALL_CCW_ARC_TO_NV                         0x12
#define LOCAL_GL_SMALL_CW_ARC_TO_NV                          0x14
#define LOCAL_GL_SMAPHS30_PROGRAM_BINARY_DMP                 0x9251
#define LOCAL_GL_SMAPHS_PROGRAM_BINARY_DMP                   0x9252
#define LOCAL_GL_SMOOTH                                      0x1D01
#define LOCAL_GL_SMOOTH_CUBIC_CURVE_TO_NV                    0x10
#define LOCAL_GL_SMOOTH_LINE_WIDTH_GRANULARITY               0x0B23
#define LOCAL_GL_SMOOTH_LINE_WIDTH_RANGE                     0x0B22
#define LOCAL_GL_SMOOTH_POINT_SIZE_GRANULARITY               0x0B13
#define LOCAL_GL_SMOOTH_POINT_SIZE_RANGE                     0x0B12
#define LOCAL_GL_SMOOTH_QUADRATIC_CURVE_TO_NV                0x0E
#define LOCAL_GL_SM_COUNT_NV                                 0x933B
#define LOCAL_GL_SOFTLIGHT                                   0x929C
#define LOCAL_GL_SOFTLIGHT_KHR                               0x929C
#define LOCAL_GL_SOFTLIGHT_NV                                0x929C
#define LOCAL_GL_SOURCE0_ALPHA                               0x8588
#define LOCAL_GL_SOURCE0_ALPHA_ARB                           0x8588
#define LOCAL_GL_SOURCE0_ALPHA_EXT                           0x8588
#define LOCAL_GL_SOURCE0_RGB                                 0x8580
#define LOCAL_GL_SOURCE0_RGB_ARB                             0x8580
#define LOCAL_GL_SOURCE0_RGB_EXT                             0x8580
#define LOCAL_GL_SOURCE1_ALPHA                               0x8589
#define LOCAL_GL_SOURCE1_ALPHA_ARB                           0x8589
#define LOCAL_GL_SOURCE1_ALPHA_EXT                           0x8589
#define LOCAL_GL_SOURCE1_RGB                                 0x8581
#define LOCAL_GL_SOURCE1_RGB_ARB                             0x8581
#define LOCAL_GL_SOURCE1_RGB_EXT                             0x8581
#define LOCAL_GL_SOURCE2_ALPHA                               0x858A
#define LOCAL_GL_SOURCE2_ALPHA_ARB                           0x858A
#define LOCAL_GL_SOURCE2_ALPHA_EXT                           0x858A
#define LOCAL_GL_SOURCE2_RGB                                 0x8582
#define LOCAL_GL_SOURCE2_RGB_ARB                             0x8582
#define LOCAL_GL_SOURCE2_RGB_EXT                             0x8582
#define LOCAL_GL_SOURCE3_ALPHA_NV                            0x858B
#define LOCAL_GL_SOURCE3_RGB_NV                              0x8583
#define LOCAL_GL_SPARE0_NV                                   0x852E
#define LOCAL_GL_SPARE0_PLUS_SECONDARY_COLOR_NV              0x8532
#define LOCAL_GL_SPARE1_NV                                   0x852F
#define LOCAL_GL_SPARSE_BUFFER_PAGE_SIZE_ARB                 0x82F8
#define LOCAL_GL_SPARSE_STORAGE_BIT_ARB                      0x0400
#define LOCAL_GL_SPARSE_TEXTURE_FULL_ARRAY_CUBE_MIPMAPS_ARB  0x91A9
#define LOCAL_GL_SPARSE_TEXTURE_FULL_ARRAY_CUBE_MIPMAPS_EXT  0x91A9
#define LOCAL_GL_SPECULAR                                    0x1202
#define LOCAL_GL_SPHERE_MAP                                  0x2402
#define LOCAL_GL_SPIR_V_BINARY                               0x9552
#define LOCAL_GL_SPIR_V_BINARY_ARB                           0x9552
#define LOCAL_GL_SPIR_V_EXTENSIONS                           0x9553
#define LOCAL_GL_SPOT_CUTOFF                                 0x1206
#define LOCAL_GL_SPOT_DIRECTION                              0x1204
#define LOCAL_GL_SPOT_EXPONENT                               0x1205
#define LOCAL_GL_SPRITE_AXIAL_SGIX                           0x814C
#define LOCAL_GL_SPRITE_AXIS_SGIX                            0x814A
#define LOCAL_GL_SPRITE_EYE_ALIGNED_SGIX                     0x814E
#define LOCAL_GL_SPRITE_MODE_SGIX                            0x8149
#define LOCAL_GL_SPRITE_OBJECT_ALIGNED_SGIX                  0x814D
#define LOCAL_GL_SPRITE_SGIX                                 0x8148
#define LOCAL_GL_SPRITE_TRANSLATION_SGIX                     0x814B
#define LOCAL_GL_SQUARE_NV                                   0x90A3
#define LOCAL_GL_SR8_EXT                                     0x8FBD
#define LOCAL_GL_SRC0_ALPHA                                  0x8588
#define LOCAL_GL_SRC0_RGB                                    0x8580
#define LOCAL_GL_SRC1_ALPHA                                  0x8589
#define LOCAL_GL_SRC1_ALPHA_EXT                              0x8589
#define LOCAL_GL_SRC1_COLOR                                  0x88F9
#define LOCAL_GL_SRC1_COLOR_EXT                              0x88F9
#define LOCAL_GL_SRC1_RGB                                    0x8581
#define LOCAL_GL_SRC2_ALPHA                                  0x858A
#define LOCAL_GL_SRC2_RGB                                    0x8582
#define LOCAL_GL_SRC_ALPHA                                   0x0302
#define LOCAL_GL_SRC_ALPHA_SATURATE                          0x0308
#define LOCAL_GL_SRC_ALPHA_SATURATE_EXT                      0x0308
#define LOCAL_GL_SRC_ATOP_NV                                 0x928E
#define LOCAL_GL_SRC_COLOR                                   0x0300
#define LOCAL_GL_SRC_IN_NV                                   0x928A
#define LOCAL_GL_SRC_NV                                      0x9286
#define LOCAL_GL_SRC_OUT_NV                                  0x928C
#define LOCAL_GL_SRC_OVER_NV                                 0x9288
#define LOCAL_GL_SRG8_EXT                                    0x8FBE
#define LOCAL_GL_SRGB                                        0x8C40
#define LOCAL_GL_SRGB8                                       0x8C41
#define LOCAL_GL_SRGB8_ALPHA8                                0x8C43
#define LOCAL_GL_SRGB8_ALPHA8_EXT                            0x8C43
#define LOCAL_GL_SRGB8_EXT                                   0x8C41
#define LOCAL_GL_SRGB8_NV                                    0x8C41
#define LOCAL_GL_SRGB_ALPHA                                  0x8C42
#define LOCAL_GL_SRGB_ALPHA_EXT                              0x8C42
#define LOCAL_GL_SRGB_DECODE_ARB                             0x8299
#define LOCAL_GL_SRGB_EXT                                    0x8C40
#define LOCAL_GL_SRGB_READ                                   0x8297
#define LOCAL_GL_SRGB_WRITE                                  0x8298
#define LOCAL_GL_STACK_OVERFLOW                              0x0503
#define LOCAL_GL_STACK_OVERFLOW_KHR                          0x0503
#define LOCAL_GL_STACK_UNDERFLOW                             0x0504
#define LOCAL_GL_STACK_UNDERFLOW_KHR                         0x0504
#define LOCAL_GL_STANDARD_FONT_FORMAT_NV                     0x936C
#define LOCAL_GL_STANDARD_FONT_NAME_NV                       0x9072
#define LOCAL_GL_STATE_RESTORE                               0x8BDC
#define LOCAL_GL_STATIC_ATI                                  0x8760
#define LOCAL_GL_STATIC_COPY                                 0x88E6
#define LOCAL_GL_STATIC_COPY_ARB                             0x88E6
#define LOCAL_GL_STATIC_DRAW                                 0x88E4
#define LOCAL_GL_STATIC_DRAW_ARB                             0x88E4
#define LOCAL_GL_STATIC_READ                                 0x88E5
#define LOCAL_GL_STATIC_READ_ARB                             0x88E5
#define LOCAL_GL_STATIC_VERTEX_ARRAY_IBM                     103061
#define LOCAL_GL_STENCIL                                     0x1802
#define LOCAL_GL_STENCIL_ATTACHMENT                          0x8D20
#define LOCAL_GL_STENCIL_ATTACHMENT_EXT                      0x8D20
#define LOCAL_GL_STENCIL_ATTACHMENT_OES                      0x8D20
#define LOCAL_GL_STENCIL_BACK_FAIL                           0x8801
#define LOCAL_GL_STENCIL_BACK_FAIL_ATI                       0x8801
#define LOCAL_GL_STENCIL_BACK_FUNC                           0x8800
#define LOCAL_GL_STENCIL_BACK_FUNC_ATI                       0x8800
#define LOCAL_GL_STENCIL_BACK_OP_VALUE_AMD                   0x874D
#define LOCAL_GL_STENCIL_BACK_PASS_DEPTH_FAIL                0x8802
#define LOCAL_GL_STENCIL_BACK_PASS_DEPTH_FAIL_ATI            0x8802
#define LOCAL_GL_STENCIL_BACK_PASS_DEPTH_PASS                0x8803
#define LOCAL_GL_STENCIL_BACK_PASS_DEPTH_PASS_ATI            0x8803
#define LOCAL_GL_STENCIL_BACK_REF                            0x8CA3
#define LOCAL_GL_STENCIL_BACK_VALUE_MASK                     0x8CA4
#define LOCAL_GL_STENCIL_BACK_WRITEMASK                      0x8CA5
#define LOCAL_GL_STENCIL_BITS                                0x0D57
#define LOCAL_GL_STENCIL_BUFFER_BIT                          0x00000400
#define LOCAL_GL_STENCIL_BUFFER_BIT0_QCOM                    0x00010000
#define LOCAL_GL_STENCIL_BUFFER_BIT1_QCOM                    0x00020000
#define LOCAL_GL_STENCIL_BUFFER_BIT2_QCOM                    0x00040000
#define LOCAL_GL_STENCIL_BUFFER_BIT3_QCOM                    0x00080000
#define LOCAL_GL_STENCIL_BUFFER_BIT4_QCOM                    0x00100000
#define LOCAL_GL_STENCIL_BUFFER_BIT5_QCOM                    0x00200000
#define LOCAL_GL_STENCIL_BUFFER_BIT6_QCOM                    0x00400000
#define LOCAL_GL_STENCIL_BUFFER_BIT7_QCOM                    0x00800000
#define LOCAL_GL_STENCIL_CLEAR_TAG_VALUE_EXT                 0x88F3
#define LOCAL_GL_STENCIL_CLEAR_VALUE                         0x0B91
#define LOCAL_GL_STENCIL_COMPONENTS                          0x8285
#define LOCAL_GL_STENCIL_EXT                                 0x1802
#define LOCAL_GL_STENCIL_FAIL                                0x0B94
#define LOCAL_GL_STENCIL_FUNC                                0x0B92
#define LOCAL_GL_STENCIL_INDEX                               0x1901
#define LOCAL_GL_STENCIL_INDEX1                              0x8D46
#define LOCAL_GL_STENCIL_INDEX16                             0x8D49
#define LOCAL_GL_STENCIL_INDEX16_EXT                         0x8D49
#define LOCAL_GL_STENCIL_INDEX1_EXT                          0x8D46
#define LOCAL_GL_STENCIL_INDEX1_OES                          0x8D46
#define LOCAL_GL_STENCIL_INDEX4                              0x8D47
#define LOCAL_GL_STENCIL_INDEX4_EXT                          0x8D47
#define LOCAL_GL_STENCIL_INDEX4_OES                          0x8D47
#define LOCAL_GL_STENCIL_INDEX8                              0x8D48
#define LOCAL_GL_STENCIL_INDEX8_EXT                          0x8D48
#define LOCAL_GL_STENCIL_INDEX8_OES                          0x8D48
#define LOCAL_GL_STENCIL_INDEX_OES                           0x1901
#define LOCAL_GL_STENCIL_OP_VALUE_AMD                        0x874C
#define LOCAL_GL_STENCIL_PASS_DEPTH_FAIL                     0x0B95
#define LOCAL_GL_STENCIL_PASS_DEPTH_PASS                     0x0B96
#define LOCAL_GL_STENCIL_REF                                 0x0B97
#define LOCAL_GL_STENCIL_REF_COMMAND_NV                      0x000C
#define LOCAL_GL_STENCIL_RENDERABLE                          0x8288
#define LOCAL_GL_STENCIL_SAMPLES_NV                          0x932E
#define LOCAL_GL_STENCIL_TAG_BITS_EXT                        0x88F2
#define LOCAL_GL_STENCIL_TEST                                0x0B90
#define LOCAL_GL_STENCIL_TEST_TWO_SIDE_EXT                   0x8910
#define LOCAL_GL_STENCIL_VALUE_MASK                          0x0B93
#define LOCAL_GL_STENCIL_WRITEMASK                           0x0B98
#define LOCAL_GL_STEREO                                      0x0C33
#define LOCAL_GL_STORAGE_CACHED_APPLE                        0x85BE
#define LOCAL_GL_STORAGE_CLIENT_APPLE                        0x85B4
#define LOCAL_GL_STORAGE_PRIVATE_APPLE                       0x85BD
#define LOCAL_GL_STORAGE_SHARED_APPLE                        0x85BF
#define LOCAL_GL_STREAM_COPY                                 0x88E2
#define LOCAL_GL_STREAM_COPY_ARB                             0x88E2
#define LOCAL_GL_STREAM_DRAW                                 0x88E0
#define LOCAL_GL_STREAM_DRAW_ARB                             0x88E0
#define LOCAL_GL_STREAM_RASTERIZATION_AMD                    0x91A0
#define LOCAL_GL_STREAM_READ                                 0x88E1
#define LOCAL_GL_STREAM_READ_ARB                             0x88E1
#define LOCAL_GL_STRICT_DEPTHFUNC_HINT_PGI                   0x1A216
#define LOCAL_GL_STRICT_LIGHTING_HINT_PGI                    0x1A217
#define LOCAL_GL_STRICT_SCISSOR_HINT_PGI                     0x1A218
#define LOCAL_GL_SUBGROUP_FEATURE_ARITHMETIC_BIT_KHR         0x00000004
#define LOCAL_GL_SUBGROUP_FEATURE_BALLOT_BIT_KHR             0x00000008
#define LOCAL_GL_SUBGROUP_FEATURE_BASIC_BIT_KHR              0x00000001
#define LOCAL_GL_SUBGROUP_FEATURE_CLUSTERED_BIT_KHR          0x00000040
#define LOCAL_GL_SUBGROUP_FEATURE_PARTITIONED_BIT_NV         0x00000100
#define LOCAL_GL_SUBGROUP_FEATURE_QUAD_BIT_KHR               0x00000080
#define LOCAL_GL_SUBGROUP_FEATURE_SHUFFLE_BIT_KHR            0x00000010
#define LOCAL_GL_SUBGROUP_FEATURE_SHUFFLE_RELATIVE_BIT_KHR   0x00000020
#define LOCAL_GL_SUBGROUP_FEATURE_VOTE_BIT_KHR               0x00000002
#define LOCAL_GL_SUBGROUP_QUAD_ALL_STAGES_KHR                0x9535
#define LOCAL_GL_SUBGROUP_SIZE_KHR                           0x9532
#define LOCAL_GL_SUBGROUP_SUPPORTED_FEATURES_KHR             0x9534
#define LOCAL_GL_SUBGROUP_SUPPORTED_STAGES_KHR               0x9533
#define LOCAL_GL_SUBPIXEL_BITS                               0x0D50
#define LOCAL_GL_SUBPIXEL_PRECISION_BIAS_X_BITS_NV           0x9347
#define LOCAL_GL_SUBPIXEL_PRECISION_BIAS_Y_BITS_NV           0x9348
#define LOCAL_GL_SUBSAMPLE_DISTANCE_AMD                      0x883F
#define LOCAL_GL_SUBTRACT                                    0x84E7
#define LOCAL_GL_SUBTRACT_ARB                                0x84E7
#define LOCAL_GL_SUB_ATI                                     0x8965
#define LOCAL_GL_SUCCESS_NV                                  0x902F
#define LOCAL_GL_SUPERSAMPLE_SCALE_X_NV                      0x9372
#define LOCAL_GL_SUPERSAMPLE_SCALE_Y_NV                      0x9373
#define LOCAL_GL_SUPPORTED_MULTISAMPLE_MODES_AMD             0x91B7
#define LOCAL_GL_SURFACE_MAPPED_NV                           0x8700
#define LOCAL_GL_SURFACE_REGISTERED_NV                       0x86FD
#define LOCAL_GL_SURFACE_STATE_NV                            0x86EB
#define LOCAL_GL_SWIZZLE_STQ_ATI                             0x8977
#define LOCAL_GL_SWIZZLE_STQ_DQ_ATI                          0x8979
#define LOCAL_GL_SWIZZLE_STRQ_ATI                            0x897A
#define LOCAL_GL_SWIZZLE_STRQ_DQ_ATI                         0x897B
#define LOCAL_GL_SWIZZLE_STR_ATI                             0x8976
#define LOCAL_GL_SWIZZLE_STR_DR_ATI                          0x8978
#define LOCAL_GL_SYNC_CL_EVENT_ARB                           0x8240
#define LOCAL_GL_SYNC_CL_EVENT_COMPLETE_ARB                  0x8241
#define LOCAL_GL_SYNC_CONDITION                              0x9113
#define LOCAL_GL_SYNC_CONDITION_APPLE                        0x9113
#define LOCAL_GL_SYNC_FENCE                                  0x9116
#define LOCAL_GL_SYNC_FENCE_APPLE                            0x9116
#define LOCAL_GL_SYNC_FLAGS                                  0x9115
#define LOCAL_GL_SYNC_FLAGS_APPLE                            0x9115
#define LOCAL_GL_SYNC_FLUSH_COMMANDS_BIT                     0x00000001
#define LOCAL_GL_SYNC_FLUSH_COMMANDS_BIT_APPLE               0x00000001
#define LOCAL_GL_SYNC_GPU_COMMANDS_COMPLETE                  0x9117
#define LOCAL_GL_SYNC_GPU_COMMANDS_COMPLETE_APPLE            0x9117
#define LOCAL_GL_SYNC_OBJECT_APPLE                           0x8A53
#define LOCAL_GL_SYNC_STATUS                                 0x9114
#define LOCAL_GL_SYNC_STATUS_APPLE                           0x9114
#define LOCAL_GL_SYNC_X11_FENCE_EXT                          0x90E1
#define LOCAL_GL_SYSTEM_FONT_NAME_NV                         0x9073
#define LOCAL_GL_T                                           0x2001
#define LOCAL_GL_T2F_C3F_V3F                                 0x2A2A
#define LOCAL_GL_T2F_C4F_N3F_V3F                             0x2A2C
#define LOCAL_GL_T2F_C4UB_V3F                                0x2A29
#define LOCAL_GL_T2F_IUI_N3F_V2F_EXT                         0x81B3
#define LOCAL_GL_T2F_IUI_N3F_V3F_EXT                         0x81B4
#define LOCAL_GL_T2F_IUI_V2F_EXT                             0x81B1
#define LOCAL_GL_T2F_IUI_V3F_EXT                             0x81B2
#define LOCAL_GL_T2F_N3F_V3F                                 0x2A2B
#define LOCAL_GL_T2F_V3F                                     0x2A27
#define LOCAL_GL_T4F_C4F_N3F_V4F                             0x2A2D
#define LOCAL_GL_T4F_V4F                                     0x2A28
#define LOCAL_GL_TABLE_TOO_LARGE                             0x8031
#define LOCAL_GL_TABLE_TOO_LARGE_EXT                         0x8031
#define LOCAL_GL_TANGENT_ARRAY_EXT                           0x8439
#define LOCAL_GL_TANGENT_ARRAY_POINTER_EXT                   0x8442
#define LOCAL_GL_TANGENT_ARRAY_STRIDE_EXT                    0x843F
#define LOCAL_GL_TANGENT_ARRAY_TYPE_EXT                      0x843E
#define LOCAL_GL_TASK_SHADER_BIT_NV                          0x00000080
#define LOCAL_GL_TASK_SHADER_NV                              0x955A
#define LOCAL_GL_TASK_SUBROUTINE_NV                          0x957D
#define LOCAL_GL_TASK_SUBROUTINE_UNIFORM_NV                  0x957F
#define LOCAL_GL_TASK_WORK_GROUP_SIZE_NV                     0x953F
#define LOCAL_GL_TERMINATE_SEQUENCE_COMMAND_NV               0x0000
#define LOCAL_GL_TESSELLATION_FACTOR_AMD                     0x9005
#define LOCAL_GL_TESSELLATION_MODE_AMD                       0x9004
#define LOCAL_GL_TESS_CONTROL_OUTPUT_VERTICES                0x8E75
#define LOCAL_GL_TESS_CONTROL_OUTPUT_VERTICES_EXT            0x8E75
#define LOCAL_GL_TESS_CONTROL_OUTPUT_VERTICES_OES            0x8E75
#define LOCAL_GL_TESS_CONTROL_PROGRAM_NV                     0x891E
#define LOCAL_GL_TESS_CONTROL_PROGRAM_PARAMETER_BUFFER_NV    0x8C74
#define LOCAL_GL_TESS_CONTROL_SHADER                         0x8E88
#define LOCAL_GL_TESS_CONTROL_SHADER_BIT                     0x00000008
#define LOCAL_GL_TESS_CONTROL_SHADER_BIT_EXT                 0x00000008
#define LOCAL_GL_TESS_CONTROL_SHADER_BIT_OES                 0x00000008
#define LOCAL_GL_TESS_CONTROL_SHADER_EXT                     0x8E88
#define LOCAL_GL_TESS_CONTROL_SHADER_OES                     0x8E88
#define LOCAL_GL_TESS_CONTROL_SHADER_PATCHES                 0x82F1
#define LOCAL_GL_TESS_CONTROL_SHADER_PATCHES_ARB             0x82F1
#define LOCAL_GL_TESS_CONTROL_SUBROUTINE                     0x92E9
#define LOCAL_GL_TESS_CONTROL_SUBROUTINE_UNIFORM             0x92EF
#define LOCAL_GL_TESS_CONTROL_TEXTURE                        0x829C
#define LOCAL_GL_TESS_EVALUATION_PROGRAM_NV                  0x891F
#define LOCAL_GL_TESS_EVALUATION_PROGRAM_PARAMETER_BUFFER_NV 0x8C75
#define LOCAL_GL_TESS_EVALUATION_SHADER                      0x8E87
#define LOCAL_GL_TESS_EVALUATION_SHADER_BIT                  0x00000010
#define LOCAL_GL_TESS_EVALUATION_SHADER_BIT_EXT              0x00000010
#define LOCAL_GL_TESS_EVALUATION_SHADER_BIT_OES              0x00000010
#define LOCAL_GL_TESS_EVALUATION_SHADER_EXT                  0x8E87
#define LOCAL_GL_TESS_EVALUATION_SHADER_INVOCATIONS          0x82F2
#define LOCAL_GL_TESS_EVALUATION_SHADER_INVOCATIONS_ARB      0x82F2
#define LOCAL_GL_TESS_EVALUATION_SHADER_OES                  0x8E87
#define LOCAL_GL_TESS_EVALUATION_SUBROUTINE                  0x92EA
#define LOCAL_GL_TESS_EVALUATION_SUBROUTINE_UNIFORM          0x92F0
#define LOCAL_GL_TESS_EVALUATION_TEXTURE                     0x829D
#define LOCAL_GL_TESS_GEN_MODE                               0x8E76
#define LOCAL_GL_TESS_GEN_MODE_EXT                           0x8E76
#define LOCAL_GL_TESS_GEN_MODE_OES                           0x8E76
#define LOCAL_GL_TESS_GEN_POINT_MODE                         0x8E79
#define LOCAL_GL_TESS_GEN_POINT_MODE_EXT                     0x8E79
#define LOCAL_GL_TESS_GEN_POINT_MODE_OES                     0x8E79
#define LOCAL_GL_TESS_GEN_SPACING                            0x8E77
#define LOCAL_GL_TESS_GEN_SPACING_EXT                        0x8E77
#define LOCAL_GL_TESS_GEN_SPACING_OES                        0x8E77
#define LOCAL_GL_TESS_GEN_VERTEX_ORDER                       0x8E78
#define LOCAL_GL_TESS_GEN_VERTEX_ORDER_EXT                   0x8E78
#define LOCAL_GL_TESS_GEN_VERTEX_ORDER_OES                   0x8E78
#define LOCAL_GL_TEXCOORD1_BIT_PGI                           0x10000000
#define LOCAL_GL_TEXCOORD2_BIT_PGI                           0x20000000
#define LOCAL_GL_TEXCOORD3_BIT_PGI                           0x40000000
#define LOCAL_GL_TEXCOORD4_BIT_PGI                           0x80000000
#define LOCAL_GL_TEXTURE                                     0x1702
#define LOCAL_GL_TEXTURE0                                    0x84C0
#define LOCAL_GL_TEXTURE0_ARB                                0x84C0
#define LOCAL_GL_TEXTURE1                                    0x84C1
#define LOCAL_GL_TEXTURE10                                   0x84CA
#define LOCAL_GL_TEXTURE10_ARB                               0x84CA
#define LOCAL_GL_TEXTURE11                                   0x84CB
#define LOCAL_GL_TEXTURE11_ARB                               0x84CB
#define LOCAL_GL_TEXTURE12                                   0x84CC
#define LOCAL_GL_TEXTURE12_ARB                               0x84CC
#define LOCAL_GL_TEXTURE13                                   0x84CD
#define LOCAL_GL_TEXTURE13_ARB                               0x84CD
#define LOCAL_GL_TEXTURE14                                   0x84CE
#define LOCAL_GL_TEXTURE14_ARB                               0x84CE
#define LOCAL_GL_TEXTURE15                                   0x84CF
#define LOCAL_GL_TEXTURE15_ARB                               0x84CF
#define LOCAL_GL_TEXTURE16                                   0x84D0
#define LOCAL_GL_TEXTURE16_ARB                               0x84D0
#define LOCAL_GL_TEXTURE17                                   0x84D1
#define LOCAL_GL_TEXTURE17_ARB                               0x84D1
#define LOCAL_GL_TEXTURE18                                   0x84D2
#define LOCAL_GL_TEXTURE18_ARB                               0x84D2
#define LOCAL_GL_TEXTURE19                                   0x84D3
#define LOCAL_GL_TEXTURE19_ARB                               0x84D3
#define LOCAL_GL_TEXTURE1_ARB                                0x84C1
#define LOCAL_GL_TEXTURE2                                    0x84C2
#define LOCAL_GL_TEXTURE20                                   0x84D4
#define LOCAL_GL_TEXTURE20_ARB                               0x84D4
#define LOCAL_GL_TEXTURE21                                   0x84D5
#define LOCAL_GL_TEXTURE21_ARB                               0x84D5
#define LOCAL_GL_TEXTURE22                                   0x84D6
#define LOCAL_GL_TEXTURE22_ARB                               0x84D6
#define LOCAL_GL_TEXTURE23                                   0x84D7
#define LOCAL_GL_TEXTURE23_ARB                               0x84D7
#define LOCAL_GL_TEXTURE24                                   0x84D8
#define LOCAL_GL_TEXTURE24_ARB                               0x84D8
#define LOCAL_GL_TEXTURE25                                   0x84D9
#define LOCAL_GL_TEXTURE25_ARB                               0x84D9
#define LOCAL_GL_TEXTURE26                                   0x84DA
#define LOCAL_GL_TEXTURE26_ARB                               0x84DA
#define LOCAL_GL_TEXTURE27                                   0x84DB
#define LOCAL_GL_TEXTURE27_ARB                               0x84DB
#define LOCAL_GL_TEXTURE28                                   0x84DC
#define LOCAL_GL_TEXTURE28_ARB                               0x84DC
#define LOCAL_GL_TEXTURE29                                   0x84DD
#define LOCAL_GL_TEXTURE29_ARB                               0x84DD
#define LOCAL_GL_TEXTURE2_ARB                                0x84C2
#define LOCAL_GL_TEXTURE3                                    0x84C3
#define LOCAL_GL_TEXTURE30                                   0x84DE
#define LOCAL_GL_TEXTURE30_ARB                               0x84DE
#define LOCAL_GL_TEXTURE31                                   0x84DF
#define LOCAL_GL_TEXTURE31_ARB                               0x84DF
#define LOCAL_GL_TEXTURE3_ARB                                0x84C3
#define LOCAL_GL_TEXTURE4                                    0x84C4
#define LOCAL_GL_TEXTURE4_ARB                                0x84C4
#define LOCAL_GL_TEXTURE5                                    0x84C5
#define LOCAL_GL_TEXTURE5_ARB                                0x84C5
#define LOCAL_GL_TEXTURE6                                    0x84C6
#define LOCAL_GL_TEXTURE6_ARB                                0x84C6
#define LOCAL_GL_TEXTURE7                                    0x84C7
#define LOCAL_GL_TEXTURE7_ARB                                0x84C7
#define LOCAL_GL_TEXTURE8                                    0x84C8
#define LOCAL_GL_TEXTURE8_ARB                                0x84C8
#define LOCAL_GL_TEXTURE9                                    0x84C9
#define LOCAL_GL_TEXTURE9_ARB                                0x84C9
#define LOCAL_GL_TEXTURE_1D                                  0x0DE0
#define LOCAL_GL_TEXTURE_1D_ARRAY                            0x8C18
#define LOCAL_GL_TEXTURE_1D_ARRAY_EXT                        0x8C18
#define LOCAL_GL_TEXTURE_1D_BINDING_EXT                      0x8068
#define LOCAL_GL_TEXTURE_1D_STACK_BINDING_MESAX              0x875D
#define LOCAL_GL_TEXTURE_1D_STACK_MESAX                      0x8759
#define LOCAL_GL_TEXTURE_2D                                  0x0DE1
#define LOCAL_GL_TEXTURE_2D_ARRAY                            0x8C1A
#define LOCAL_GL_TEXTURE_2D_ARRAY_EXT                        0x8C1A
#define LOCAL_GL_TEXTURE_2D_BINDING_EXT                      0x8069
#define LOCAL_GL_TEXTURE_2D_MULTISAMPLE                      0x9100
#define LOCAL_GL_TEXTURE_2D_MULTISAMPLE_ARRAY                0x9102
#define LOCAL_GL_TEXTURE_2D_MULTISAMPLE_ARRAY_OES            0x9102
#define LOCAL_GL_TEXTURE_2D_STACK_BINDING_MESAX              0x875E
#define LOCAL_GL_TEXTURE_2D_STACK_MESAX                      0x875A
#define LOCAL_GL_TEXTURE_3D                                  0x806F
#define LOCAL_GL_TEXTURE_3D_BINDING_EXT                      0x806A
#define LOCAL_GL_TEXTURE_3D_BINDING_OES                      0x806A
#define LOCAL_GL_TEXTURE_3D_EXT                              0x806F
#define LOCAL_GL_TEXTURE_3D_OES                              0x806F
#define LOCAL_GL_TEXTURE_4DSIZE_SGIS                         0x8136
#define LOCAL_GL_TEXTURE_4D_BINDING_SGIS                     0x814F
#define LOCAL_GL_TEXTURE_4D_SGIS                             0x8134
#define LOCAL_GL_TEXTURE_ALPHA_MODULATE_IMG                  0x8C06
#define LOCAL_GL_TEXTURE_ALPHA_SIZE                          0x805F
#define LOCAL_GL_TEXTURE_ALPHA_SIZE_EXT                      0x805F
#define LOCAL_GL_TEXTURE_ALPHA_TYPE                          0x8C13
#define LOCAL_GL_TEXTURE_ALPHA_TYPE_ARB                      0x8C13
#define LOCAL_GL_TEXTURE_APPLICATION_MODE_EXT                0x834F
#define LOCAL_GL_TEXTURE_ASTC_DECODE_PRECISION_EXT           0x8F69
#define LOCAL_GL_TEXTURE_BASE_LEVEL                          0x813C
#define LOCAL_GL_TEXTURE_BASE_LEVEL_SGIS                     0x813C
#define LOCAL_GL_TEXTURE_BINDING_1D                          0x8068
#define LOCAL_GL_TEXTURE_BINDING_1D_ARRAY                    0x8C1C
#define LOCAL_GL_TEXTURE_BINDING_1D_ARRAY_EXT                0x8C1C
#define LOCAL_GL_TEXTURE_BINDING_2D                          0x8069
#define LOCAL_GL_TEXTURE_BINDING_2D_ARRAY                    0x8C1D
#define LOCAL_GL_TEXTURE_BINDING_2D_ARRAY_EXT                0x8C1D
#define LOCAL_GL_TEXTURE_BINDING_2D_MULTISAMPLE              0x9104
#define LOCAL_GL_TEXTURE_BINDING_2D_MULTISAMPLE_ARRAY        0x9105
#define LOCAL_GL_TEXTURE_BINDING_2D_MULTISAMPLE_ARRAY_OES    0x9105
#define LOCAL_GL_TEXTURE_BINDING_3D                          0x806A
#define LOCAL_GL_TEXTURE_BINDING_3D_OES                      0x806A
#define LOCAL_GL_TEXTURE_BINDING_BUFFER                      0x8C2C
#define LOCAL_GL_TEXTURE_BINDING_BUFFER_ARB                  0x8C2C
#define LOCAL_GL_TEXTURE_BINDING_BUFFER_EXT                  0x8C2C
#define LOCAL_GL_TEXTURE_BINDING_BUFFER_OES                  0x8C2C
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP                    0x8514
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_ARB                0x8514
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_ARRAY              0x900A
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_ARRAY_ARB          0x900A
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_ARRAY_EXT          0x900A
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_ARRAY_OES          0x900A
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_EXT                0x8514
#define LOCAL_GL_TEXTURE_BINDING_CUBE_MAP_OES                0x8514
#define LOCAL_GL_TEXTURE_BINDING_EXTERNAL_OES                0x8D67
#define LOCAL_GL_TEXTURE_BINDING_RECTANGLE                   0x84F6
#define LOCAL_GL_TEXTURE_BINDING_RECTANGLE_ARB               0x84F6
#define LOCAL_GL_TEXTURE_BINDING_RECTANGLE_NV                0x84F6
#define LOCAL_GL_TEXTURE_BINDING_RENDERBUFFER_NV             0x8E53
#define LOCAL_GL_TEXTURE_BIT                                 0x00040000
#define LOCAL_GL_TEXTURE_BLUE_SIZE                           0x805E
#define LOCAL_GL_TEXTURE_BLUE_SIZE_EXT                       0x805E
#define LOCAL_GL_TEXTURE_BLUE_TYPE                           0x8C12
#define LOCAL_GL_TEXTURE_BLUE_TYPE_ARB                       0x8C12
#define LOCAL_GL_TEXTURE_BORDER                              0x1005
#define LOCAL_GL_TEXTURE_BORDER_COLOR                        0x1004
#define LOCAL_GL_TEXTURE_BORDER_COLOR_EXT                    0x1004
#define LOCAL_GL_TEXTURE_BORDER_COLOR_NV                     0x1004
#define LOCAL_GL_TEXTURE_BORDER_COLOR_OES                    0x1004
#define LOCAL_GL_TEXTURE_BORDER_VALUES_NV                    0x871A
#define LOCAL_GL_TEXTURE_BUFFER                              0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_ARB                          0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_BINDING                      0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_BINDING_EXT                  0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_BINDING_OES                  0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_DATA_STORE_BINDING           0x8C2D
#define LOCAL_GL_TEXTURE_BUFFER_DATA_STORE_BINDING_ARB       0x8C2D
#define LOCAL_GL_TEXTURE_BUFFER_DATA_STORE_BINDING_EXT       0x8C2D
#define LOCAL_GL_TEXTURE_BUFFER_DATA_STORE_BINDING_OES       0x8C2D
#define LOCAL_GL_TEXTURE_BUFFER_EXT                          0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_FORMAT_ARB                   0x8C2E
#define LOCAL_GL_TEXTURE_BUFFER_FORMAT_EXT                   0x8C2E
#define LOCAL_GL_TEXTURE_BUFFER_OES                          0x8C2A
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET                       0x919D
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT             0x919F
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT_EXT         0x919F
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT_OES         0x919F
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET_EXT                   0x919D
#define LOCAL_GL_TEXTURE_BUFFER_OFFSET_OES                   0x919D
#define LOCAL_GL_TEXTURE_BUFFER_SIZE                         0x919E
#define LOCAL_GL_TEXTURE_BUFFER_SIZE_EXT                     0x919E
#define LOCAL_GL_TEXTURE_BUFFER_SIZE_OES                     0x919E
#define LOCAL_GL_TEXTURE_CLIPMAP_CENTER_SGIX                 0x8171
#define LOCAL_GL_TEXTURE_CLIPMAP_DEPTH_SGIX                  0x8176
#define LOCAL_GL_TEXTURE_CLIPMAP_FRAME_SGIX                  0x8172
#define LOCAL_GL_TEXTURE_CLIPMAP_LOD_OFFSET_SGIX             0x8175
#define LOCAL_GL_TEXTURE_CLIPMAP_OFFSET_SGIX                 0x8173
#define LOCAL_GL_TEXTURE_CLIPMAP_VIRTUAL_DEPTH_SGIX          0x8174
#define LOCAL_GL_TEXTURE_COLOR_SAMPLES_NV                    0x9046
#define LOCAL_GL_TEXTURE_COLOR_TABLE_SGI                     0x80BC
#define LOCAL_GL_TEXTURE_COLOR_WRITEMASK_SGIS                0x81EF
#define LOCAL_GL_TEXTURE_COMPARE_FAIL_VALUE_ARB              0x80BF
#define LOCAL_GL_TEXTURE_COMPARE_FUNC                        0x884D
#define LOCAL_GL_TEXTURE_COMPARE_FUNC_ARB                    0x884D
#define LOCAL_GL_TEXTURE_COMPARE_FUNC_EXT                    0x884D
#define LOCAL_GL_TEXTURE_COMPARE_MODE                        0x884C
#define LOCAL_GL_TEXTURE_COMPARE_MODE_ARB                    0x884C
#define LOCAL_GL_TEXTURE_COMPARE_MODE_EXT                    0x884C
#define LOCAL_GL_TEXTURE_COMPARE_OPERATOR_SGIX               0x819B
#define LOCAL_GL_TEXTURE_COMPARE_SGIX                        0x819A
#define LOCAL_GL_TEXTURE_COMPONENTS                          0x1003
#define LOCAL_GL_TEXTURE_COMPRESSED                          0x86A1
#define LOCAL_GL_TEXTURE_COMPRESSED_ARB                      0x86A1
#define LOCAL_GL_TEXTURE_COMPRESSED_BLOCK_HEIGHT             0x82B2
#define LOCAL_GL_TEXTURE_COMPRESSED_BLOCK_SIZE               0x82B3
#define LOCAL_GL_TEXTURE_COMPRESSED_BLOCK_WIDTH              0x82B1
#define LOCAL_GL_TEXTURE_COMPRESSED_IMAGE_SIZE               0x86A0
#define LOCAL_GL_TEXTURE_COMPRESSED_IMAGE_SIZE_ARB           0x86A0
#define LOCAL_GL_TEXTURE_COMPRESSION_HINT                    0x84EF
#define LOCAL_GL_TEXTURE_COMPRESSION_HINT_ARB                0x84EF
#define LOCAL_GL_TEXTURE_CONSTANT_DATA_SUNX                  0x81D6
#define LOCAL_GL_TEXTURE_COORD_ARRAY                         0x8078
#define LOCAL_GL_TEXTURE_COORD_ARRAY_ADDRESS_NV              0x8F25
#define LOCAL_GL_TEXTURE_COORD_ARRAY_BUFFER_BINDING          0x889A
#define LOCAL_GL_TEXTURE_COORD_ARRAY_BUFFER_BINDING_ARB      0x889A
#define LOCAL_GL_TEXTURE_COORD_ARRAY_COUNT_EXT               0x808B
#define LOCAL_GL_TEXTURE_COORD_ARRAY_EXT                     0x8078
#define LOCAL_GL_TEXTURE_COORD_ARRAY_LENGTH_NV               0x8F2F
#define LOCAL_GL_TEXTURE_COORD_ARRAY_LIST_IBM                103074
#define LOCAL_GL_TEXTURE_COORD_ARRAY_LIST_STRIDE_IBM         103084
#define LOCAL_GL_TEXTURE_COORD_ARRAY_PARALLEL_POINTERS_INTEL 0x83F8
#define LOCAL_GL_TEXTURE_COORD_ARRAY_POINTER                 0x8092
#define LOCAL_GL_TEXTURE_COORD_ARRAY_POINTER_EXT             0x8092
#define LOCAL_GL_TEXTURE_COORD_ARRAY_SIZE                    0x8088
#define LOCAL_GL_TEXTURE_COORD_ARRAY_SIZE_EXT                0x8088
#define LOCAL_GL_TEXTURE_COORD_ARRAY_STRIDE                  0x808A
#define LOCAL_GL_TEXTURE_COORD_ARRAY_STRIDE_EXT              0x808A
#define LOCAL_GL_TEXTURE_COORD_ARRAY_TYPE                    0x8089
#define LOCAL_GL_TEXTURE_COORD_ARRAY_TYPE_EXT                0x8089
#define LOCAL_GL_TEXTURE_COORD_NV                            0x8C79
#define LOCAL_GL_TEXTURE_COVERAGE_SAMPLES_NV                 0x9045
#define LOCAL_GL_TEXTURE_CROP_RECT_OES                       0x8B9D
#define LOCAL_GL_TEXTURE_CUBE_MAP                            0x8513
#define LOCAL_GL_TEXTURE_CUBE_MAP_ARB                        0x8513
#define LOCAL_GL_TEXTURE_CUBE_MAP_ARRAY                      0x9009
#define LOCAL_GL_TEXTURE_CUBE_MAP_ARRAY_ARB                  0x9009
#define LOCAL_GL_TEXTURE_CUBE_MAP_ARRAY_EXT                  0x9009
#define LOCAL_GL_TEXTURE_CUBE_MAP_ARRAY_OES                  0x9009
#define LOCAL_GL_TEXTURE_CUBE_MAP_EXT                        0x8513
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X                 0x8516
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB             0x8516
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X_EXT             0x8516
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X_OES             0x8516
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y                 0x8518
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB             0x8518
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y_EXT             0x8518
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y_OES             0x8518
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z                 0x851A
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB             0x851A
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z_EXT             0x851A
#define LOCAL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z_OES             0x851A
#define LOCAL_GL_TEXTURE_CUBE_MAP_OES                        0x8513
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_X                 0x8515
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB             0x8515
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_X_EXT             0x8515
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_X_OES             0x8515
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y                 0x8517
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB             0x8517
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y_EXT             0x8517
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y_OES             0x8517
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z                 0x8519
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB             0x8519
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z_EXT             0x8519
#define LOCAL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z_OES             0x8519
#define LOCAL_GL_TEXTURE_CUBE_MAP_SEAMLESS                   0x884F
#define LOCAL_GL_TEXTURE_DEFORMATION_BIT_SGIX                0x00000001
#define LOCAL_GL_TEXTURE_DEFORMATION_SGIX                    0x8195
#define LOCAL_GL_TEXTURE_DEPTH                               0x8071
#define LOCAL_GL_TEXTURE_DEPTH_EXT                           0x8071
#define LOCAL_GL_TEXTURE_DEPTH_QCOM                          0x8BD4
#define LOCAL_GL_TEXTURE_DEPTH_SIZE                          0x884A
#define LOCAL_GL_TEXTURE_DEPTH_SIZE_ARB                      0x884A
#define LOCAL_GL_TEXTURE_DEPTH_TYPE                          0x8C16
#define LOCAL_GL_TEXTURE_DEPTH_TYPE_ARB                      0x8C16
#define LOCAL_GL_TEXTURE_DS_SIZE_NV                          0x871D
#define LOCAL_GL_TEXTURE_DT_SIZE_NV                          0x871E
#define LOCAL_GL_TEXTURE_ENV                                 0x2300
#define LOCAL_GL_TEXTURE_ENV_BIAS_SGIX                       0x80BE
#define LOCAL_GL_TEXTURE_ENV_COLOR                           0x2201
#define LOCAL_GL_TEXTURE_ENV_MODE                            0x2200
#define LOCAL_GL_TEXTURE_EXTERNAL_OES                        0x8D65
#define LOCAL_GL_TEXTURE_FETCH_BARRIER_BIT                   0x00000008
#define LOCAL_GL_TEXTURE_FETCH_BARRIER_BIT_EXT               0x00000008
#define LOCAL_GL_TEXTURE_FILTER4_SIZE_SGIS                   0x8147
#define LOCAL_GL_TEXTURE_FILTER_CONTROL                      0x8500
#define LOCAL_GL_TEXTURE_FILTER_CONTROL_EXT                  0x8500
#define LOCAL_GL_TEXTURE_FIXED_SAMPLE_LOCATIONS              0x9107
#define LOCAL_GL_TEXTURE_FLOAT_COMPONENTS_NV                 0x888C
#define LOCAL_GL_TEXTURE_FORMAT_QCOM                         0x8BD6
#define LOCAL_GL_TEXTURE_FORMAT_SRGB_OVERRIDE_EXT            0x8FBF
#define LOCAL_GL_TEXTURE_FOVEATED_CUTOFF_DENSITY_QCOM        0x96A0
#define LOCAL_GL_TEXTURE_FOVEATED_FEATURE_BITS_QCOM          0x8BFB
#define LOCAL_GL_TEXTURE_FOVEATED_FEATURE_QUERY_QCOM         0x8BFD
#define LOCAL_GL_TEXTURE_FOVEATED_MIN_PIXEL_DENSITY_QCOM     0x8BFC
#define LOCAL_GL_TEXTURE_FOVEATED_NUM_FOCAL_POINTS_QUERY_QCOM        0x8BFE
#define LOCAL_GL_TEXTURE_FREE_MEMORY_ATI                     0x87FC
#define LOCAL_GL_TEXTURE_GATHER                              0x82A2
#define LOCAL_GL_TEXTURE_GATHER_SHADOW                       0x82A3
#define LOCAL_GL_TEXTURE_GEN_MODE                            0x2500
#define LOCAL_GL_TEXTURE_GEN_MODE_OES                        0x2500
#define LOCAL_GL_TEXTURE_GEN_Q                               0x0C63
#define LOCAL_GL_TEXTURE_GEN_R                               0x0C62
#define LOCAL_GL_TEXTURE_GEN_S                               0x0C60
#define LOCAL_GL_TEXTURE_GEN_STR_OES                         0x8D60
#define LOCAL_GL_TEXTURE_GEN_T                               0x0C61
#define LOCAL_GL_TEXTURE_GEQUAL_R_SGIX                       0x819D
#define LOCAL_GL_TEXTURE_GREEN_SIZE                          0x805D
#define LOCAL_GL_TEXTURE_GREEN_SIZE_EXT                      0x805D
#define LOCAL_GL_TEXTURE_GREEN_TYPE                          0x8C11
#define LOCAL_GL_TEXTURE_GREEN_TYPE_ARB                      0x8C11
#define LOCAL_GL_TEXTURE_HEIGHT                              0x1001
#define LOCAL_GL_TEXTURE_HEIGHT_QCOM                         0x8BD3
#define LOCAL_GL_TEXTURE_HI_SIZE_NV                          0x871B
#define LOCAL_GL_TEXTURE_IMAGE_FORMAT                        0x828F
#define LOCAL_GL_TEXTURE_IMAGE_TYPE                          0x8290
#define LOCAL_GL_TEXTURE_IMAGE_VALID_QCOM                    0x8BD8
#define LOCAL_GL_TEXTURE_IMMUTABLE_FORMAT                    0x912F
#define LOCAL_GL_TEXTURE_IMMUTABLE_FORMAT_EXT                0x912F
#define LOCAL_GL_TEXTURE_IMMUTABLE_LEVELS                    0x82DF
#define LOCAL_GL_TEXTURE_INDEX_SIZE_EXT                      0x80ED
#define LOCAL_GL_TEXTURE_INTENSITY_SIZE                      0x8061
#define LOCAL_GL_TEXTURE_INTENSITY_SIZE_EXT                  0x8061
#define LOCAL_GL_TEXTURE_INTENSITY_TYPE                      0x8C15
#define LOCAL_GL_TEXTURE_INTENSITY_TYPE_ARB                  0x8C15
#define LOCAL_GL_TEXTURE_INTERNAL_FORMAT                     0x1003
#define LOCAL_GL_TEXTURE_INTERNAL_FORMAT_QCOM                0x8BD5
#define LOCAL_GL_TEXTURE_LEQUAL_R_SGIX                       0x819C
#define LOCAL_GL_TEXTURE_LIGHTING_MODE_HP                    0x8167
#define LOCAL_GL_TEXTURE_LIGHT_EXT                           0x8350
#define LOCAL_GL_TEXTURE_LOD_BIAS                            0x8501
#define LOCAL_GL_TEXTURE_LOD_BIAS_EXT                        0x8501
#define LOCAL_GL_TEXTURE_LOD_BIAS_R_SGIX                     0x8190
#define LOCAL_GL_TEXTURE_LOD_BIAS_S_SGIX                     0x818E
#define LOCAL_GL_TEXTURE_LOD_BIAS_T_SGIX                     0x818F
#define LOCAL_GL_TEXTURE_LO_SIZE_NV                          0x871C
#define LOCAL_GL_TEXTURE_LUMINANCE_SIZE                      0x8060
#define LOCAL_GL_TEXTURE_LUMINANCE_SIZE_EXT                  0x8060
#define LOCAL_GL_TEXTURE_LUMINANCE_TYPE                      0x8C14
#define LOCAL_GL_TEXTURE_LUMINANCE_TYPE_ARB                  0x8C14
#define LOCAL_GL_TEXTURE_MAG_FILTER                          0x2800
#define LOCAL_GL_TEXTURE_MAG_SIZE_NV                         0x871F
#define LOCAL_GL_TEXTURE_MATERIAL_FACE_EXT                   0x8351
#define LOCAL_GL_TEXTURE_MATERIAL_PARAMETER_EXT              0x8352
#define LOCAL_GL_TEXTURE_MATRIX                              0x0BA8
#define LOCAL_GL_TEXTURE_MATRIX_FLOAT_AS_INT_BITS_OES        0x898F
#define LOCAL_GL_TEXTURE_MAX_ANISOTROPY                      0x84FE
#define LOCAL_GL_TEXTURE_MAX_ANISOTROPY_EXT                  0x84FE
#define LOCAL_GL_TEXTURE_MAX_CLAMP_R_SGIX                    0x836B
#define LOCAL_GL_TEXTURE_MAX_CLAMP_S_SGIX                    0x8369
#define LOCAL_GL_TEXTURE_MAX_CLAMP_T_SGIX                    0x836A
#define LOCAL_GL_TEXTURE_MAX_LEVEL                           0x813D
#define LOCAL_GL_TEXTURE_MAX_LEVEL_APPLE                     0x813D
#define LOCAL_GL_TEXTURE_MAX_LEVEL_SGIS                      0x813D
#define LOCAL_GL_TEXTURE_MAX_LOD                             0x813B
#define LOCAL_GL_TEXTURE_MAX_LOD_SGIS                        0x813B
#define LOCAL_GL_TEXTURE_MEMORY_LAYOUT_INTEL                 0x83FF
#define LOCAL_GL_TEXTURE_MIN_FILTER                          0x2801
#define LOCAL_GL_TEXTURE_MIN_LOD                             0x813A
#define LOCAL_GL_TEXTURE_MIN_LOD_SGIS                        0x813A
#define LOCAL_GL_TEXTURE_MULTI_BUFFER_HINT_SGIX              0x812E
#define LOCAL_GL_TEXTURE_NORMAL_EXT                          0x85AF
#define LOCAL_GL_TEXTURE_NUM_LEVELS_QCOM                     0x8BD9
#define LOCAL_GL_TEXTURE_OBJECT_VALID_QCOM                   0x8BDB
#define LOCAL_GL_TEXTURE_POST_SPECULAR_HP                    0x8168
#define LOCAL_GL_TEXTURE_PRE_SPECULAR_HP                     0x8169
#define LOCAL_GL_TEXTURE_PRIORITY                            0x8066
#define LOCAL_GL_TEXTURE_PRIORITY_EXT                        0x8066
#define LOCAL_GL_TEXTURE_PROTECTED_EXT                       0x8BFA
#define LOCAL_GL_TEXTURE_RANGE_LENGTH_APPLE                  0x85B7
#define LOCAL_GL_TEXTURE_RANGE_POINTER_APPLE                 0x85B8
#define LOCAL_GL_TEXTURE_RECTANGLE                           0x84F5
#define LOCAL_GL_TEXTURE_RECTANGLE_ARB                       0x84F5
#define LOCAL_GL_TEXTURE_RECTANGLE_NV                        0x84F5
#define LOCAL_GL_TEXTURE_REDUCTION_MODE_ARB                  0x9366
#define LOCAL_GL_TEXTURE_REDUCTION_MODE_EXT                  0x9366
#define LOCAL_GL_TEXTURE_RED_SIZE                            0x805C
#define LOCAL_GL_TEXTURE_RED_SIZE_EXT                        0x805C
#define LOCAL_GL_TEXTURE_RED_TYPE                            0x8C10
#define LOCAL_GL_TEXTURE_RED_TYPE_ARB                        0x8C10
#define LOCAL_GL_TEXTURE_RENDERBUFFER_DATA_STORE_BINDING_NV  0x8E54
#define LOCAL_GL_TEXTURE_RENDERBUFFER_NV                     0x8E55
#define LOCAL_GL_TEXTURE_RESIDENT                            0x8067
#define LOCAL_GL_TEXTURE_RESIDENT_EXT                        0x8067
#define LOCAL_GL_TEXTURE_SAMPLES                             0x9106
#define LOCAL_GL_TEXTURE_SAMPLES_IMG                         0x9136
#define LOCAL_GL_TEXTURE_SHADER_NV                           0x86DE
#define LOCAL_GL_TEXTURE_SHADOW                              0x82A1
#define LOCAL_GL_TEXTURE_SHARED_SIZE                         0x8C3F
#define LOCAL_GL_TEXTURE_SHARED_SIZE_EXT                     0x8C3F
#define LOCAL_GL_TEXTURE_SPARSE_ARB                          0x91A6
#define LOCAL_GL_TEXTURE_SPARSE_EXT                          0x91A6
#define LOCAL_GL_TEXTURE_SRGB_DECODE_EXT                     0x8A48
#define LOCAL_GL_TEXTURE_STACK_DEPTH                         0x0BA5
#define LOCAL_GL_TEXTURE_STENCIL_SIZE                        0x88F1
#define LOCAL_GL_TEXTURE_STENCIL_SIZE_EXT                    0x88F1
#define LOCAL_GL_TEXTURE_STORAGE_HINT_APPLE                  0x85BC
#define LOCAL_GL_TEXTURE_STORAGE_SPARSE_BIT_AMD              0x00000001
#define LOCAL_GL_TEXTURE_SWIZZLE_A                           0x8E45
#define LOCAL_GL_TEXTURE_SWIZZLE_A_EXT                       0x8E45
#define LOCAL_GL_TEXTURE_SWIZZLE_B                           0x8E44
#define LOCAL_GL_TEXTURE_SWIZZLE_B_EXT                       0x8E44
#define LOCAL_GL_TEXTURE_SWIZZLE_G                           0x8E43
#define LOCAL_GL_TEXTURE_SWIZZLE_G_EXT                       0x8E43
#define LOCAL_GL_TEXTURE_SWIZZLE_R                           0x8E42
#define LOCAL_GL_TEXTURE_SWIZZLE_RGBA                        0x8E46
#define LOCAL_GL_TEXTURE_SWIZZLE_RGBA_EXT                    0x8E46
#define LOCAL_GL_TEXTURE_SWIZZLE_R_EXT                       0x8E42
#define LOCAL_GL_TEXTURE_TARGET                              0x1006
#define LOCAL_GL_TEXTURE_TARGET_QCOM                         0x8BDA
#define LOCAL_GL_TEXTURE_TILING_EXT                          0x9580
#define LOCAL_GL_TEXTURE_TOO_LARGE_EXT                       0x8065
#define LOCAL_GL_TEXTURE_TYPE_QCOM                           0x8BD7
#define LOCAL_GL_TEXTURE_UNNORMALIZED_COORDINATES_ARM        0x8F6A
#define LOCAL_GL_TEXTURE_UNSIGNED_REMAP_MODE_NV              0x888F
#define LOCAL_GL_TEXTURE_UPDATE_BARRIER_BIT                  0x00000100
#define LOCAL_GL_TEXTURE_UPDATE_BARRIER_BIT_EXT              0x00000100
#define LOCAL_GL_TEXTURE_USAGE_ANGLE                         0x93A2
#define LOCAL_GL_TEXTURE_VIEW                                0x82B5
#define LOCAL_GL_TEXTURE_VIEW_MIN_LAYER                      0x82DD
#define LOCAL_GL_TEXTURE_VIEW_MIN_LAYER_EXT                  0x82DD
#define LOCAL_GL_TEXTURE_VIEW_MIN_LAYER_OES                  0x82DD
#define LOCAL_GL_TEXTURE_VIEW_MIN_LEVEL                      0x82DB
#define LOCAL_GL_TEXTURE_VIEW_MIN_LEVEL_EXT                  0x82DB
#define LOCAL_GL_TEXTURE_VIEW_MIN_LEVEL_OES                  0x82DB
#define LOCAL_GL_TEXTURE_VIEW_NUM_LAYERS                     0x82DE
#define LOCAL_GL_TEXTURE_VIEW_NUM_LAYERS_EXT                 0x82DE
#define LOCAL_GL_TEXTURE_VIEW_NUM_LAYERS_OES                 0x82DE
#define LOCAL_GL_TEXTURE_VIEW_NUM_LEVELS                     0x82DC
#define LOCAL_GL_TEXTURE_VIEW_NUM_LEVELS_EXT                 0x82DC
#define LOCAL_GL_TEXTURE_VIEW_NUM_LEVELS_OES                 0x82DC
#define LOCAL_GL_TEXTURE_WIDTH                               0x1000
#define LOCAL_GL_TEXTURE_WIDTH_QCOM                          0x8BD2
#define LOCAL_GL_TEXTURE_WRAP_Q_SGIS                         0x8137
#define LOCAL_GL_TEXTURE_WRAP_R                              0x8072
#define LOCAL_GL_TEXTURE_WRAP_R_EXT                          0x8072
#define LOCAL_GL_TEXTURE_WRAP_R_OES                          0x8072
#define LOCAL_GL_TEXTURE_WRAP_S                              0x2802
#define LOCAL_GL_TEXTURE_WRAP_T                              0x2803
#define LOCAL_GL_TEXT_FRAGMENT_SHADER_ATI                    0x8200
#define LOCAL_GL_TILE_RASTER_ORDER_FIXED_MESA                0x8BB8
#define LOCAL_GL_TILE_RASTER_ORDER_INCREASING_X_MESA         0x8BB9
#define LOCAL_GL_TILE_RASTER_ORDER_INCREASING_Y_MESA         0x8BBA
#define LOCAL_GL_TILING_TYPES_EXT                            0x9583
#define LOCAL_GL_TIMELINE_SEMAPHORE_VALUE_NV                 0x9595
#define LOCAL_GL_TIMEOUT_EXPIRED                             0x911B
#define LOCAL_GL_TIMEOUT_EXPIRED_APPLE                       0x911B
#define LOCAL_GL_TIMEOUT_IGNORED                             0xFFFFFFFFFFFFFFFF
#define LOCAL_GL_TIMEOUT_IGNORED_APPLE                       0xFFFFFFFFFFFFFFFF
#define LOCAL_GL_TIMESTAMP                                   0x8E28
#define LOCAL_GL_TIMESTAMP_EXT                               0x8E28
#define LOCAL_GL_TIME_ELAPSED                                0x88BF
#define LOCAL_GL_TIME_ELAPSED_EXT                            0x88BF
#define LOCAL_GL_TOP_LEVEL_ARRAY_SIZE                        0x930C
#define LOCAL_GL_TOP_LEVEL_ARRAY_STRIDE                      0x930D
#define LOCAL_GL_TRACE_ALL_BITS_MESA                         0xFFFF
#define LOCAL_GL_TRACE_ARRAYS_BIT_MESA                       0x0004
#define LOCAL_GL_TRACE_ERRORS_BIT_MESA                       0x0020
#define LOCAL_GL_TRACE_MASK_MESA                             0x8755
#define LOCAL_GL_TRACE_NAME_MESA                             0x8756
#define LOCAL_GL_TRACE_OPERATIONS_BIT_MESA                   0x0001
#define LOCAL_GL_TRACE_PIXELS_BIT_MESA                       0x0010
#define LOCAL_GL_TRACE_PRIMITIVES_BIT_MESA                   0x0002
#define LOCAL_GL_TRACE_TEXTURES_BIT_MESA                     0x0008
#define LOCAL_GL_TRACK_MATRIX_NV                             0x8648
#define LOCAL_GL_TRACK_MATRIX_TRANSFORM_NV                   0x8649
#define LOCAL_GL_TRANSFORM_BIT                               0x00001000
#define LOCAL_GL_TRANSFORM_FEEDBACK                          0x8E22
#define LOCAL_GL_TRANSFORM_FEEDBACK_ACTIVE                   0x8E24
#define LOCAL_GL_TRANSFORM_FEEDBACK_ATTRIBS_NV               0x8C7E
#define LOCAL_GL_TRANSFORM_FEEDBACK_BARRIER_BIT              0x00000800
#define LOCAL_GL_TRANSFORM_FEEDBACK_BARRIER_BIT_EXT          0x00000800
#define LOCAL_GL_TRANSFORM_FEEDBACK_BINDING                  0x8E25
#define LOCAL_GL_TRANSFORM_FEEDBACK_BINDING_NV               0x8E25
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER                   0x8C8E
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE            0x8E24
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE_NV         0x8E24
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_BINDING           0x8C8F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_BINDING_EXT       0x8C8F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_BINDING_NV        0x8C8F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_EXT               0x8C8E
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_INDEX             0x934B
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_MODE              0x8C7F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_MODE_EXT          0x8C7F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_MODE_NV           0x8C7F
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_NV                0x8C8E
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED            0x8E23
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED_NV         0x8E23
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_SIZE              0x8C85
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_SIZE_EXT          0x8C85
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_SIZE_NV           0x8C85
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_START             0x8C84
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_START_EXT         0x8C84
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_START_NV          0x8C84
#define LOCAL_GL_TRANSFORM_FEEDBACK_BUFFER_STRIDE            0x934C
#define LOCAL_GL_TRANSFORM_FEEDBACK_NV                       0x8E22
#define LOCAL_GL_TRANSFORM_FEEDBACK_OVERFLOW                 0x82EC
#define LOCAL_GL_TRANSFORM_FEEDBACK_OVERFLOW_ARB             0x82EC
#define LOCAL_GL_TRANSFORM_FEEDBACK_PAUSED                   0x8E23
#define LOCAL_GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN       0x8C88
#define LOCAL_GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN_EXT   0x8C88
#define LOCAL_GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN_NV    0x8C88
#define LOCAL_GL_TRANSFORM_FEEDBACK_RECORD_NV                0x8C86
#define LOCAL_GL_TRANSFORM_FEEDBACK_STREAM_OVERFLOW          0x82ED
#define LOCAL_GL_TRANSFORM_FEEDBACK_STREAM_OVERFLOW_ARB      0x82ED
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYING                  0x92F4
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYINGS                 0x8C83
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYINGS_EXT             0x8C83
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYINGS_NV              0x8C83
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYING_MAX_LENGTH       0x8C76
#define LOCAL_GL_TRANSFORM_FEEDBACK_VARYING_MAX_LENGTH_EXT   0x8C76
#define LOCAL_GL_TRANSFORM_HINT_APPLE                        0x85B1
#define LOCAL_GL_TRANSLATED_SHADER_SOURCE_LENGTH_ANGLE       0x93A0
#define LOCAL_GL_TRANSLATE_2D_NV                             0x9090
#define LOCAL_GL_TRANSLATE_3D_NV                             0x9091
#define LOCAL_GL_TRANSLATE_X_NV                              0x908E
#define LOCAL_GL_TRANSLATE_Y_NV                              0x908F
#define LOCAL_GL_TRANSPOSE_AFFINE_2D_NV                      0x9096
#define LOCAL_GL_TRANSPOSE_AFFINE_3D_NV                      0x9098
#define LOCAL_GL_TRANSPOSE_COLOR_MATRIX                      0x84E6
#define LOCAL_GL_TRANSPOSE_COLOR_MATRIX_ARB                  0x84E6
#define LOCAL_GL_TRANSPOSE_CURRENT_MATRIX_ARB                0x88B7
#define LOCAL_GL_TRANSPOSE_MODELVIEW_MATRIX                  0x84E3
#define LOCAL_GL_TRANSPOSE_MODELVIEW_MATRIX_ARB              0x84E3
#define LOCAL_GL_TRANSPOSE_NV                                0x862C
#define LOCAL_GL_TRANSPOSE_PROGRAM_MATRIX_EXT                0x8E2E
#define LOCAL_GL_TRANSPOSE_PROJECTION_MATRIX                 0x84E4
#define LOCAL_GL_TRANSPOSE_PROJECTION_MATRIX_ARB             0x84E4
#define LOCAL_GL_TRANSPOSE_TEXTURE_MATRIX                    0x84E5
#define LOCAL_GL_TRANSPOSE_TEXTURE_MATRIX_ARB                0x84E5
#define LOCAL_GL_TRIANGLES                                   0x0004
#define LOCAL_GL_TRIANGLES_ADJACENCY                         0x000C
#define LOCAL_GL_TRIANGLES_ADJACENCY_ARB                     0x000C
#define LOCAL_GL_TRIANGLES_ADJACENCY_EXT                     0x000C
#define LOCAL_GL_TRIANGLES_ADJACENCY_OES                     0x000C
#define LOCAL_GL_TRIANGLE_FAN                                0x0006
#define LOCAL_GL_TRIANGLE_LIST_SUN                           0x81D7
#define LOCAL_GL_TRIANGLE_MESH_SUN                           0x8615
#define LOCAL_GL_TRIANGLE_STRIP                              0x0005
#define LOCAL_GL_TRIANGLE_STRIP_ADJACENCY                    0x000D
#define LOCAL_GL_TRIANGLE_STRIP_ADJACENCY_ARB                0x000D
#define LOCAL_GL_TRIANGLE_STRIP_ADJACENCY_EXT                0x000D
#define LOCAL_GL_TRIANGLE_STRIP_ADJACENCY_OES                0x000D
#define LOCAL_GL_TRIANGULAR_NV                               0x90A5
#define LOCAL_GL_TRUE                                        1
#define LOCAL_GL_TYPE                                        0x92FA
#define LOCAL_GL_UNCORRELATED_NV                             0x9282
#define LOCAL_GL_UNDEFINED_APPLE                             0x8A1C
#define LOCAL_GL_UNDEFINED_VERTEX                            0x8260
#define LOCAL_GL_UNDEFINED_VERTEX_EXT                        0x8260
#define LOCAL_GL_UNDEFINED_VERTEX_OES                        0x8260
#define LOCAL_GL_UNIFORM                                     0x92E1
#define LOCAL_GL_UNIFORM_ADDRESS_COMMAND_NV                  0x000A
#define LOCAL_GL_UNIFORM_ARRAY_STRIDE                        0x8A3C
#define LOCAL_GL_UNIFORM_ATOMIC_COUNTER_BUFFER_INDEX         0x92DA
#define LOCAL_GL_UNIFORM_BARRIER_BIT                         0x00000004
#define LOCAL_GL_UNIFORM_BARRIER_BIT_EXT                     0x00000004
#define LOCAL_GL_UNIFORM_BLOCK                               0x92E2
#define LOCAL_GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS               0x8A42
#define LOCAL_GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES        0x8A43
#define LOCAL_GL_UNIFORM_BLOCK_BINDING                       0x8A3F
#define LOCAL_GL_UNIFORM_BLOCK_DATA_SIZE                     0x8A40
#define LOCAL_GL_UNIFORM_BLOCK_INDEX                         0x8A3A
#define LOCAL_GL_UNIFORM_BLOCK_NAME_LENGTH                   0x8A41
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_COMPUTE_SHADER  0x90EC
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER 0x8A46
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_GEOMETRY_SHADER 0x8A45
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_MESH_SHADER_NV  0x959C
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_TASK_SHADER_NV  0x959D
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_CONTROL_SHADER     0x84F0
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_EVALUATION_SHADER  0x84F1
#define LOCAL_GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER   0x8A44
#define LOCAL_GL_UNIFORM_BUFFER                              0x8A11
#define LOCAL_GL_UNIFORM_BUFFER_ADDRESS_NV                   0x936F
#define LOCAL_GL_UNIFORM_BUFFER_BINDING                      0x8A28
#define LOCAL_GL_UNIFORM_BUFFER_BINDING_EXT                  0x8DEF
#define LOCAL_GL_UNIFORM_BUFFER_EXT                          0x8DEE
#define LOCAL_GL_UNIFORM_BUFFER_LENGTH_NV                    0x9370
#define LOCAL_GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT             0x8A34
#define LOCAL_GL_UNIFORM_BUFFER_SIZE                         0x8A2A
#define LOCAL_GL_UNIFORM_BUFFER_START                        0x8A29
#define LOCAL_GL_UNIFORM_BUFFER_UNIFIED_NV                   0x936E
#define LOCAL_GL_UNIFORM_IS_ROW_MAJOR                        0x8A3E
#define LOCAL_GL_UNIFORM_MATRIX_STRIDE                       0x8A3D
#define LOCAL_GL_UNIFORM_NAME_LENGTH                         0x8A39
#define LOCAL_GL_UNIFORM_OFFSET                              0x8A3B
#define LOCAL_GL_UNIFORM_SIZE                                0x8A38
#define LOCAL_GL_UNIFORM_TYPE                                0x8A37
#define LOCAL_GL_UNKNOWN_CONTEXT_RESET                       0x8255
#define LOCAL_GL_UNKNOWN_CONTEXT_RESET_ARB                   0x8255
#define LOCAL_GL_UNKNOWN_CONTEXT_RESET_EXT                   0x8255
#define LOCAL_GL_UNKNOWN_CONTEXT_RESET_KHR                   0x8255
#define LOCAL_GL_UNPACK_ALIGNMENT                            0x0CF5
#define LOCAL_GL_UNPACK_CLIENT_STORAGE_APPLE                 0x85B2
#define LOCAL_GL_UNPACK_CMYK_HINT_EXT                        0x800F
#define LOCAL_GL_UNPACK_COLORSPACE_CONVERSION_WEBGL          0x9243
#define LOCAL_GL_UNPACK_COMPRESSED_BLOCK_DEPTH               0x9129
#define LOCAL_GL_UNPACK_COMPRESSED_BLOCK_HEIGHT              0x9128
#define LOCAL_GL_UNPACK_COMPRESSED_BLOCK_SIZE                0x912A
#define LOCAL_GL_UNPACK_COMPRESSED_BLOCK_WIDTH               0x9127
#define LOCAL_GL_UNPACK_COMPRESSED_SIZE_SGIX                 0x831A
#define LOCAL_GL_UNPACK_CONSTANT_DATA_SUNX                   0x81D5
#define LOCAL_GL_UNPACK_FLIP_Y_WEBGL                         0x9240
#define LOCAL_GL_UNPACK_IMAGE_DEPTH_SGIS                     0x8133
#define LOCAL_GL_UNPACK_IMAGE_HEIGHT                         0x806E
#define LOCAL_GL_UNPACK_IMAGE_HEIGHT_EXT                     0x806E
#define LOCAL_GL_UNPACK_LSB_FIRST                            0x0CF1
#define LOCAL_GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL              0x9241
#define LOCAL_GL_UNPACK_RESAMPLE_OML                         0x8985
#define LOCAL_GL_UNPACK_RESAMPLE_SGIX                        0x842F
#define LOCAL_GL_UNPACK_ROW_BYTES_APPLE                      0x8A16
#define LOCAL_GL_UNPACK_ROW_LENGTH                           0x0CF2
#define LOCAL_GL_UNPACK_ROW_LENGTH_EXT                       0x0CF2
#define LOCAL_GL_UNPACK_SKIP_IMAGES                          0x806D
#define LOCAL_GL_UNPACK_SKIP_IMAGES_EXT                      0x806D
#define LOCAL_GL_UNPACK_SKIP_PIXELS                          0x0CF4
#define LOCAL_GL_UNPACK_SKIP_PIXELS_EXT                      0x0CF4
#define LOCAL_GL_UNPACK_SKIP_ROWS                            0x0CF3
#define LOCAL_GL_UNPACK_SKIP_ROWS_EXT                        0x0CF3
#define LOCAL_GL_UNPACK_SKIP_VOLUMES_SGIS                    0x8132
#define LOCAL_GL_UNPACK_SUBSAMPLE_RATE_SGIX                  0x85A1
#define LOCAL_GL_UNPACK_SWAP_BYTES                           0x0CF0
#define LOCAL_GL_UNSIGNALED                                  0x9118
#define LOCAL_GL_UNSIGNALED_APPLE                            0x9118
#define LOCAL_GL_UNSIGNED_BYTE                               0x1401
#define LOCAL_GL_UNSIGNED_BYTE_2_3_3_REV                     0x8362
#define LOCAL_GL_UNSIGNED_BYTE_2_3_3_REV_EXT                 0x8362
#define LOCAL_GL_UNSIGNED_BYTE_3_3_2                         0x8032
#define LOCAL_GL_UNSIGNED_BYTE_3_3_2_EXT                     0x8032
#define LOCAL_GL_UNSIGNED_IDENTITY_NV                        0x8536
#define LOCAL_GL_UNSIGNED_INT                                0x1405
#define LOCAL_GL_UNSIGNED_INT16_NV                           0x8FF0
#define LOCAL_GL_UNSIGNED_INT16_VEC2_NV                      0x8FF1
#define LOCAL_GL_UNSIGNED_INT16_VEC3_NV                      0x8FF2
#define LOCAL_GL_UNSIGNED_INT16_VEC4_NV                      0x8FF3
#define LOCAL_GL_UNSIGNED_INT64_AMD                          0x8BC2
#define LOCAL_GL_UNSIGNED_INT64_ARB                          0x140F
#define LOCAL_GL_UNSIGNED_INT64_NV                           0x140F
#define LOCAL_GL_UNSIGNED_INT64_VEC2_ARB                     0x8FF5
#define LOCAL_GL_UNSIGNED_INT64_VEC2_NV                      0x8FF5
#define LOCAL_GL_UNSIGNED_INT64_VEC3_ARB                     0x8FF6
#define LOCAL_GL_UNSIGNED_INT64_VEC3_NV                      0x8FF6
#define LOCAL_GL_UNSIGNED_INT64_VEC4_ARB                     0x8FF7
#define LOCAL_GL_UNSIGNED_INT64_VEC4_NV                      0x8FF7
#define LOCAL_GL_UNSIGNED_INT8_NV                            0x8FEC
#define LOCAL_GL_UNSIGNED_INT8_VEC2_NV                       0x8FED
#define LOCAL_GL_UNSIGNED_INT8_VEC3_NV                       0x8FEE
#define LOCAL_GL_UNSIGNED_INT8_VEC4_NV                       0x8FEF
#define LOCAL_GL_UNSIGNED_INT_10F_11F_11F_REV                0x8C3B
#define LOCAL_GL_UNSIGNED_INT_10F_11F_11F_REV_APPLE          0x8C3B
#define LOCAL_GL_UNSIGNED_INT_10F_11F_11F_REV_EXT            0x8C3B
#define LOCAL_GL_UNSIGNED_INT_10_10_10_2                     0x8036
#define LOCAL_GL_UNSIGNED_INT_10_10_10_2_EXT                 0x8036
#define LOCAL_GL_UNSIGNED_INT_10_10_10_2_OES                 0x8DF6
#define LOCAL_GL_UNSIGNED_INT_24_8                           0x84FA
#define LOCAL_GL_UNSIGNED_INT_24_8_EXT                       0x84FA
#define LOCAL_GL_UNSIGNED_INT_24_8_MESA                      0x8751
#define LOCAL_GL_UNSIGNED_INT_24_8_NV                        0x84FA
#define LOCAL_GL_UNSIGNED_INT_24_8_OES                       0x84FA
#define LOCAL_GL_UNSIGNED_INT_2_10_10_10_REV                 0x8368
#define LOCAL_GL_UNSIGNED_INT_2_10_10_10_REV_EXT             0x8368
#define LOCAL_GL_UNSIGNED_INT_5_9_9_9_REV                    0x8C3E
#define LOCAL_GL_UNSIGNED_INT_5_9_9_9_REV_APPLE              0x8C3E
#define LOCAL_GL_UNSIGNED_INT_5_9_9_9_REV_EXT                0x8C3E
#define LOCAL_GL_UNSIGNED_INT_8_24_REV_MESA                  0x8752
#define LOCAL_GL_UNSIGNED_INT_8_8_8_8                        0x8035
#define LOCAL_GL_UNSIGNED_INT_8_8_8_8_EXT                    0x8035
#define LOCAL_GL_UNSIGNED_INT_8_8_8_8_REV                    0x8367
#define LOCAL_GL_UNSIGNED_INT_8_8_8_8_REV_EXT                0x8367
#define LOCAL_GL_UNSIGNED_INT_8_8_S8_S8_REV_NV               0x86DB
#define LOCAL_GL_UNSIGNED_INT_ATOMIC_COUNTER                 0x92DB
#define LOCAL_GL_UNSIGNED_INT_IMAGE_1D                       0x9062
#define LOCAL_GL_UNSIGNED_INT_IMAGE_1D_ARRAY                 0x9068
#define LOCAL_GL_UNSIGNED_INT_IMAGE_1D_ARRAY_EXT             0x9068
#define LOCAL_GL_UNSIGNED_INT_IMAGE_1D_EXT                   0x9062
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D                       0x9063
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_ARRAY                 0x9069
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_ARRAY_EXT             0x9069
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_EXT                   0x9063
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE           0x906B
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE_ARRAY     0x906C
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE_ARRAY_EXT 0x906C
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE_EXT       0x906B
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_RECT                  0x9065
#define LOCAL_GL_UNSIGNED_INT_IMAGE_2D_RECT_EXT              0x9065
#define LOCAL_GL_UNSIGNED_INT_IMAGE_3D                       0x9064
#define LOCAL_GL_UNSIGNED_INT_IMAGE_3D_EXT                   0x9064
#define LOCAL_GL_UNSIGNED_INT_IMAGE_BUFFER                   0x9067
#define LOCAL_GL_UNSIGNED_INT_IMAGE_BUFFER_EXT               0x9067
#define LOCAL_GL_UNSIGNED_INT_IMAGE_BUFFER_OES               0x9067
#define LOCAL_GL_UNSIGNED_INT_IMAGE_CUBE                     0x9066
#define LOCAL_GL_UNSIGNED_INT_IMAGE_CUBE_EXT                 0x9066
#define LOCAL_GL_UNSIGNED_INT_IMAGE_CUBE_MAP_ARRAY           0x906A
#define LOCAL_GL_UNSIGNED_INT_IMAGE_CUBE_MAP_ARRAY_EXT       0x906A
#define LOCAL_GL_UNSIGNED_INT_IMAGE_CUBE_MAP_ARRAY_OES       0x906A
#define LOCAL_GL_UNSIGNED_INT_S8_S8_8_8_NV                   0x86DA
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_1D                     0x8DD1
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_1D_ARRAY               0x8DD6
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_1D_ARRAY_EXT           0x8DD6
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_1D_EXT                 0x8DD1
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D                     0x8DD2
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_ARRAY               0x8DD7
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_ARRAY_EXT           0x8DD7
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_EXT                 0x8DD2
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE         0x910A
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE_ARRAY   0x910D
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE_ARRAY_OES       0x910D
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_RECT                0x8DD5
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_2D_RECT_EXT            0x8DD5
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_3D                     0x8DD3
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_3D_EXT                 0x8DD3
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_BUFFER                 0x8DD8
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_BUFFER_AMD             0x9003
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_BUFFER_EXT             0x8DD8
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_BUFFER_OES             0x8DD8
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE                   0x8DD4
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE_EXT               0x8DD4
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY         0x900F
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY_ARB     0x900F
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY_EXT     0x900F
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY_OES     0x900F
#define LOCAL_GL_UNSIGNED_INT_SAMPLER_RENDERBUFFER_NV        0x8E58
#define LOCAL_GL_UNSIGNED_INT_VEC2                           0x8DC6
#define LOCAL_GL_UNSIGNED_INT_VEC2_EXT                       0x8DC6
#define LOCAL_GL_UNSIGNED_INT_VEC3                           0x8DC7
#define LOCAL_GL_UNSIGNED_INT_VEC3_EXT                       0x8DC7
#define LOCAL_GL_UNSIGNED_INT_VEC4                           0x8DC8
#define LOCAL_GL_UNSIGNED_INT_VEC4_EXT                       0x8DC8
#define LOCAL_GL_UNSIGNED_INVERT_NV                          0x8537
#define LOCAL_GL_UNSIGNED_NORMALIZED                         0x8C17
#define LOCAL_GL_UNSIGNED_NORMALIZED_ARB                     0x8C17
#define LOCAL_GL_UNSIGNED_NORMALIZED_EXT                     0x8C17
#define LOCAL_GL_UNSIGNED_SHORT                              0x1403
#define LOCAL_GL_UNSIGNED_SHORT_15_1_MESA                    0x8753
#define LOCAL_GL_UNSIGNED_SHORT_1_15_REV_MESA                0x8754
#define LOCAL_GL_UNSIGNED_SHORT_1_5_5_5_REV                  0x8366
#define LOCAL_GL_UNSIGNED_SHORT_1_5_5_5_REV_EXT              0x8366
#define LOCAL_GL_UNSIGNED_SHORT_4_4_4_4                      0x8033
#define LOCAL_GL_UNSIGNED_SHORT_4_4_4_4_EXT                  0x8033
#define LOCAL_GL_UNSIGNED_SHORT_4_4_4_4_REV                  0x8365
#define LOCAL_GL_UNSIGNED_SHORT_4_4_4_4_REV_EXT              0x8365
#define LOCAL_GL_UNSIGNED_SHORT_4_4_4_4_REV_IMG              0x8365
#define LOCAL_GL_UNSIGNED_SHORT_5_5_5_1                      0x8034
#define LOCAL_GL_UNSIGNED_SHORT_5_5_5_1_EXT                  0x8034
#define LOCAL_GL_UNSIGNED_SHORT_5_6_5                        0x8363
#define LOCAL_GL_UNSIGNED_SHORT_5_6_5_EXT                    0x8363
#define LOCAL_GL_UNSIGNED_SHORT_5_6_5_REV                    0x8364
#define LOCAL_GL_UNSIGNED_SHORT_5_6_5_REV_EXT                0x8364
#define LOCAL_GL_UNSIGNED_SHORT_8_8_APPLE                    0x85BA
#define LOCAL_GL_UNSIGNED_SHORT_8_8_MESA                     0x85BA
#define LOCAL_GL_UNSIGNED_SHORT_8_8_REV_APPLE                0x85BB
#define LOCAL_GL_UNSIGNED_SHORT_8_8_REV_MESA                 0x85BB
#define LOCAL_GL_UPLOAD_GPU_MASK_NVX                         0x954A
#define LOCAL_GL_UPPER_LEFT                                  0x8CA2
#define LOCAL_GL_UPPER_LEFT_EXT                              0x8CA2
#define LOCAL_GL_USE_MISSING_GLYPH_NV                        0x90AA
#define LOCAL_GL_UTF16_NV                                    0x909B
#define LOCAL_GL_UTF8_NV                                     0x909A
#define LOCAL_GL_UUID_SIZE_EXT                               16
#define LOCAL_GL_V2F                                         0x2A20
#define LOCAL_GL_V3F                                         0x2A21
#define LOCAL_GL_VALIDATE_SHADER_BINARY_QCOM                 0x96A3
#define LOCAL_GL_VALIDATE_STATUS                             0x8B83
#define LOCAL_GL_VARIABLE_A_NV                               0x8523
#define LOCAL_GL_VARIABLE_B_NV                               0x8524
#define LOCAL_GL_VARIABLE_C_NV                               0x8525
#define LOCAL_GL_VARIABLE_D_NV                               0x8526
#define LOCAL_GL_VARIABLE_E_NV                               0x8527
#define LOCAL_GL_VARIABLE_F_NV                               0x8528
#define LOCAL_GL_VARIABLE_G_NV                               0x8529
#define LOCAL_GL_VARIANT_ARRAY_EXT                           0x87E8
#define LOCAL_GL_VARIANT_ARRAY_POINTER_EXT                   0x87E9
#define LOCAL_GL_VARIANT_ARRAY_STRIDE_EXT                    0x87E6
#define LOCAL_GL_VARIANT_ARRAY_TYPE_EXT                      0x87E7
#define LOCAL_GL_VARIANT_DATATYPE_EXT                        0x87E5
#define LOCAL_GL_VARIANT_EXT                                 0x87C1
#define LOCAL_GL_VARIANT_VALUE_EXT                           0x87E4
#define LOCAL_GL_VBO_FREE_MEMORY_ATI                         0x87FB
#define LOCAL_GL_VECTOR_EXT                                  0x87BF
#define LOCAL_GL_VENDOR                                      0x1F00
#define LOCAL_GL_VERSION                                     0x1F02
#define LOCAL_GL_VERSION_ES_CL_1_0                           1
#define LOCAL_GL_VERSION_ES_CL_1_1                           1
#define LOCAL_GL_VERSION_ES_CM_1_1                           1
#define LOCAL_GL_VERTEX23_BIT_PGI                            0x00000004
#define LOCAL_GL_VERTEX4_BIT_PGI                             0x00000008
#define LOCAL_GL_VERTEX_ARRAY                                0x8074
#define LOCAL_GL_VERTEX_ARRAY_ADDRESS_NV                     0x8F21
#define LOCAL_GL_VERTEX_ARRAY_BINDING                        0x85B5
#define LOCAL_GL_VERTEX_ARRAY_BINDING_APPLE                  0x85B5
#define LOCAL_GL_VERTEX_ARRAY_BINDING_OES                    0x85B5
#define LOCAL_GL_VERTEX_ARRAY_BUFFER_BINDING                 0x8896
#define LOCAL_GL_VERTEX_ARRAY_BUFFER_BINDING_ARB             0x8896
#define LOCAL_GL_VERTEX_ARRAY_COUNT_EXT                      0x807D
#define LOCAL_GL_VERTEX_ARRAY_EXT                            0x8074
#define LOCAL_GL_VERTEX_ARRAY_KHR                            0x8074
#define LOCAL_GL_VERTEX_ARRAY_LENGTH_NV                      0x8F2B
#define LOCAL_GL_VERTEX_ARRAY_LIST_IBM                       103070
#define LOCAL_GL_VERTEX_ARRAY_LIST_STRIDE_IBM                103080
#define LOCAL_GL_VERTEX_ARRAY_OBJECT_AMD                     0x9154
#define LOCAL_GL_VERTEX_ARRAY_OBJECT_EXT                     0x9154
#define LOCAL_GL_VERTEX_ARRAY_PARALLEL_POINTERS_INTEL        0x83F5
#define LOCAL_GL_VERTEX_ARRAY_POINTER                        0x808E
#define LOCAL_GL_VERTEX_ARRAY_POINTER_EXT                    0x808E
#define LOCAL_GL_VERTEX_ARRAY_RANGE_APPLE                    0x851D
#define LOCAL_GL_VERTEX_ARRAY_RANGE_LENGTH_APPLE             0x851E
#define LOCAL_GL_VERTEX_ARRAY_RANGE_LENGTH_NV                0x851E
#define LOCAL_GL_VERTEX_ARRAY_RANGE_NV                       0x851D
#define LOCAL_GL_VERTEX_ARRAY_RANGE_POINTER_APPLE            0x8521
#define LOCAL_GL_VERTEX_ARRAY_RANGE_POINTER_NV               0x8521
#define LOCAL_GL_VERTEX_ARRAY_RANGE_VALID_NV                 0x851F
#define LOCAL_GL_VERTEX_ARRAY_RANGE_WITHOUT_FLUSH_NV         0x8533
#define LOCAL_GL_VERTEX_ARRAY_SIZE                           0x807A
#define LOCAL_GL_VERTEX_ARRAY_SIZE_EXT                       0x807A
#define LOCAL_GL_VERTEX_ARRAY_STORAGE_HINT_APPLE             0x851F
#define LOCAL_GL_VERTEX_ARRAY_STRIDE                         0x807C
#define LOCAL_GL_VERTEX_ARRAY_STRIDE_EXT                     0x807C
#define LOCAL_GL_VERTEX_ARRAY_TYPE                           0x807B
#define LOCAL_GL_VERTEX_ARRAY_TYPE_EXT                       0x807B
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY0_NV                     0x8650
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY10_NV                    0x865A
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY11_NV                    0x865B
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY12_NV                    0x865C
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY13_NV                    0x865D
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY14_NV                    0x865E
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY15_NV                    0x865F
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY1_NV                     0x8651
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY2_NV                     0x8652
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY3_NV                     0x8653
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY4_NV                     0x8654
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY5_NV                     0x8655
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY6_NV                     0x8656
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY7_NV                     0x8657
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY8_NV                     0x8658
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY9_NV                     0x8659
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_ADDRESS_NV              0x8F20
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_BARRIER_BIT             0x00000001
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_BARRIER_BIT_EXT         0x00000001
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING          0x889F
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING_ARB      0x889F
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_DIVISOR                 0x88FE
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE           0x88FE
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ARB             0x88FE
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_DIVISOR_EXT             0x88FE
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_DIVISOR_NV              0x88FE
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_ENABLED                 0x8622
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_ENABLED_ARB             0x8622
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_INTEGER                 0x88FD
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_INTEGER_EXT             0x88FD
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_INTEGER_NV              0x88FD
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_LENGTH_NV               0x8F2A
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_LONG                    0x874E
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_NORMALIZED              0x886A
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_NORMALIZED_ARB          0x886A
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_POINTER                 0x8645
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_POINTER_ARB             0x8645
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_SIZE                    0x8623
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_SIZE_ARB                0x8623
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_STRIDE                  0x8624
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_STRIDE_ARB              0x8624
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_TYPE                    0x8625
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_TYPE_ARB                0x8625
#define LOCAL_GL_VERTEX_ATTRIB_ARRAY_UNIFIED_NV              0x8F1E
#define LOCAL_GL_VERTEX_ATTRIB_BINDING                       0x82D4
#define LOCAL_GL_VERTEX_ATTRIB_MAP1_APPLE                    0x8A00
#define LOCAL_GL_VERTEX_ATTRIB_MAP1_COEFF_APPLE              0x8A03
#define LOCAL_GL_VERTEX_ATTRIB_MAP1_DOMAIN_APPLE             0x8A05
#define LOCAL_GL_VERTEX_ATTRIB_MAP1_ORDER_APPLE              0x8A04
#define LOCAL_GL_VERTEX_ATTRIB_MAP1_SIZE_APPLE               0x8A02
#define LOCAL_GL_VERTEX_ATTRIB_MAP2_APPLE                    0x8A01
#define LOCAL_GL_VERTEX_ATTRIB_MAP2_COEFF_APPLE              0x8A07
#define LOCAL_GL_VERTEX_ATTRIB_MAP2_DOMAIN_APPLE             0x8A09
#define LOCAL_GL_VERTEX_ATTRIB_MAP2_ORDER_APPLE              0x8A08
#define LOCAL_GL_VERTEX_ATTRIB_MAP2_SIZE_APPLE               0x8A06
#define LOCAL_GL_VERTEX_ATTRIB_RELATIVE_OFFSET               0x82D5
#define LOCAL_GL_VERTEX_BINDING_BUFFER                       0x8F4F
#define LOCAL_GL_VERTEX_BINDING_DIVISOR                      0x82D6
#define LOCAL_GL_VERTEX_BINDING_OFFSET                       0x82D7
#define LOCAL_GL_VERTEX_BINDING_STRIDE                       0x82D8
#define LOCAL_GL_VERTEX_BLEND_ARB                            0x86A7
#define LOCAL_GL_VERTEX_CONSISTENT_HINT_PGI                  0x1A22B
#define LOCAL_GL_VERTEX_DATA_HINT_PGI                        0x1A22A
#define LOCAL_GL_VERTEX_ELEMENT_SWIZZLE_AMD                  0x91A4
#define LOCAL_GL_VERTEX_ID_NV                                0x8C7B
#define LOCAL_GL_VERTEX_ID_SWIZZLE_AMD                       0x91A5
#define LOCAL_GL_VERTEX_PRECLIP_HINT_SGIX                    0x83EF
#define LOCAL_GL_VERTEX_PRECLIP_SGIX                         0x83EE
#define LOCAL_GL_VERTEX_PROGRAM_ARB                          0x8620
#define LOCAL_GL_VERTEX_PROGRAM_BINDING_NV                   0x864A
#define LOCAL_GL_VERTEX_PROGRAM_CALLBACK_DATA_MESA           0x8BB7
#define LOCAL_GL_VERTEX_PROGRAM_CALLBACK_FUNC_MESA           0x8BB6
#define LOCAL_GL_VERTEX_PROGRAM_CALLBACK_MESA                0x8BB5
#define LOCAL_GL_VERTEX_PROGRAM_NV                           0x8620
#define LOCAL_GL_VERTEX_PROGRAM_PARAMETER_BUFFER_NV          0x8DA2
#define LOCAL_GL_VERTEX_PROGRAM_POINT_SIZE                   0x8642
#define LOCAL_GL_VERTEX_PROGRAM_POINT_SIZE_ARB               0x8642
#define LOCAL_GL_VERTEX_PROGRAM_POINT_SIZE_NV                0x8642
#define LOCAL_GL_VERTEX_PROGRAM_POSITION_MESA                0x8BB4
#define LOCAL_GL_VERTEX_PROGRAM_TWO_SIDE                     0x8643
#define LOCAL_GL_VERTEX_PROGRAM_TWO_SIDE_ARB                 0x8643
#define LOCAL_GL_VERTEX_PROGRAM_TWO_SIDE_NV                  0x8643
#define LOCAL_GL_VERTEX_SHADER                               0x8B31
#define LOCAL_GL_VERTEX_SHADER_ARB                           0x8B31
#define LOCAL_GL_VERTEX_SHADER_BINDING_EXT                   0x8781
#define LOCAL_GL_VERTEX_SHADER_BIT                           0x00000001
#define LOCAL_GL_VERTEX_SHADER_BIT_EXT                       0x00000001
#define LOCAL_GL_VERTEX_SHADER_EXT                           0x8780
#define LOCAL_GL_VERTEX_SHADER_INSTRUCTIONS_EXT              0x87CF
#define LOCAL_GL_VERTEX_SHADER_INVARIANTS_EXT                0x87D1
#define LOCAL_GL_VERTEX_SHADER_INVOCATIONS                   0x82F0
#define LOCAL_GL_VERTEX_SHADER_INVOCATIONS_ARB               0x82F0
#define LOCAL_GL_VERTEX_SHADER_LOCALS_EXT                    0x87D3
#define LOCAL_GL_VERTEX_SHADER_LOCAL_CONSTANTS_EXT           0x87D2
#define LOCAL_GL_VERTEX_SHADER_OPTIMIZED_EXT                 0x87D4
#define LOCAL_GL_VERTEX_SHADER_VARIANTS_EXT                  0x87D0
#define LOCAL_GL_VERTEX_SOURCE_ATI                           0x8774
#define LOCAL_GL_VERTEX_STATE_PROGRAM_NV                     0x8621
#define LOCAL_GL_VERTEX_STREAM0_ATI                          0x876C
#define LOCAL_GL_VERTEX_STREAM1_ATI                          0x876D
#define LOCAL_GL_VERTEX_STREAM2_ATI                          0x876E
#define LOCAL_GL_VERTEX_STREAM3_ATI                          0x876F
#define LOCAL_GL_VERTEX_STREAM4_ATI                          0x8770
#define LOCAL_GL_VERTEX_STREAM5_ATI                          0x8771
#define LOCAL_GL_VERTEX_STREAM6_ATI                          0x8772
#define LOCAL_GL_VERTEX_STREAM7_ATI                          0x8773
#define LOCAL_GL_VERTEX_SUBROUTINE                           0x92E8
#define LOCAL_GL_VERTEX_SUBROUTINE_UNIFORM                   0x92EE
#define LOCAL_GL_VERTEX_TEXTURE                              0x829B
#define LOCAL_GL_VERTEX_WEIGHTING_EXT                        0x8509
#define LOCAL_GL_VERTEX_WEIGHT_ARRAY_EXT                     0x850C
#define LOCAL_GL_VERTEX_WEIGHT_ARRAY_POINTER_EXT             0x8510
#define LOCAL_GL_VERTEX_WEIGHT_ARRAY_SIZE_EXT                0x850D
#define LOCAL_GL_VERTEX_WEIGHT_ARRAY_STRIDE_EXT              0x850F
#define LOCAL_GL_VERTEX_WEIGHT_ARRAY_TYPE_EXT                0x850E
#define LOCAL_GL_VERTICAL_LINE_TO_NV                         0x08
#define LOCAL_GL_VERTICES_SUBMITTED                          0x82EE
#define LOCAL_GL_VERTICES_SUBMITTED_ARB                      0x82EE
#define LOCAL_GL_VIBRANCE_BIAS_NV                            0x8719
#define LOCAL_GL_VIBRANCE_SCALE_NV                           0x8713
#define LOCAL_GL_VIDEO_BUFFER_BINDING_NV                     0x9021
#define LOCAL_GL_VIDEO_BUFFER_INTERNAL_FORMAT_NV             0x902D
#define LOCAL_GL_VIDEO_BUFFER_NV                             0x9020
#define LOCAL_GL_VIDEO_BUFFER_PITCH_NV                       0x9028
#define LOCAL_GL_VIDEO_CAPTURE_FIELD_LOWER_HEIGHT_NV         0x903B
#define LOCAL_GL_VIDEO_CAPTURE_FIELD_UPPER_HEIGHT_NV         0x903A
#define LOCAL_GL_VIDEO_CAPTURE_FRAME_HEIGHT_NV               0x9039
#define LOCAL_GL_VIDEO_CAPTURE_FRAME_WIDTH_NV                0x9038
#define LOCAL_GL_VIDEO_CAPTURE_SURFACE_ORIGIN_NV             0x903C
#define LOCAL_GL_VIDEO_CAPTURE_TO_422_SUPPORTED_NV           0x9026
#define LOCAL_GL_VIDEO_COLOR_CONVERSION_MATRIX_NV            0x9029
#define LOCAL_GL_VIDEO_COLOR_CONVERSION_MAX_NV               0x902A
#define LOCAL_GL_VIDEO_COLOR_CONVERSION_MIN_NV               0x902B
#define LOCAL_GL_VIDEO_COLOR_CONVERSION_OFFSET_NV            0x902C
#define LOCAL_GL_VIEWPORT                                    0x0BA2
#define LOCAL_GL_VIEWPORT_BIT                                0x00000800
#define LOCAL_GL_VIEWPORT_BOUNDS_RANGE                       0x825D
#define LOCAL_GL_VIEWPORT_BOUNDS_RANGE_EXT                   0x825D
#define LOCAL_GL_VIEWPORT_BOUNDS_RANGE_NV                    0x825D
#define LOCAL_GL_VIEWPORT_BOUNDS_RANGE_OES                   0x825D
#define LOCAL_GL_VIEWPORT_COMMAND_NV                         0x0010
#define LOCAL_GL_VIEWPORT_INDEX_PROVOKING_VERTEX             0x825F
#define LOCAL_GL_VIEWPORT_INDEX_PROVOKING_VERTEX_EXT         0x825F
#define LOCAL_GL_VIEWPORT_INDEX_PROVOKING_VERTEX_NV          0x825F
#define LOCAL_GL_VIEWPORT_INDEX_PROVOKING_VERTEX_OES         0x825F
#define LOCAL_GL_VIEWPORT_POSITION_W_SCALE_NV                0x937C
#define LOCAL_GL_VIEWPORT_POSITION_W_SCALE_X_COEFF_NV        0x937D
#define LOCAL_GL_VIEWPORT_POSITION_W_SCALE_Y_COEFF_NV        0x937E
#define LOCAL_GL_VIEWPORT_SUBPIXEL_BITS                      0x825C
#define LOCAL_GL_VIEWPORT_SUBPIXEL_BITS_EXT                  0x825C
#define LOCAL_GL_VIEWPORT_SUBPIXEL_BITS_NV                   0x825C
#define LOCAL_GL_VIEWPORT_SUBPIXEL_BITS_OES                  0x825C
#define LOCAL_GL_VIEWPORT_SWIZZLE_NEGATIVE_W_NV              0x9357
#define LOCAL_GL_VIEWPORT_SWIZZLE_NEGATIVE_X_NV              0x9351
#define LOCAL_GL_VIEWPORT_SWIZZLE_NEGATIVE_Y_NV              0x9353
#define LOCAL_GL_VIEWPORT_SWIZZLE_NEGATIVE_Z_NV              0x9355
#define LOCAL_GL_VIEWPORT_SWIZZLE_POSITIVE_W_NV              0x9356
#define LOCAL_GL_VIEWPORT_SWIZZLE_POSITIVE_X_NV              0x9350
#define LOCAL_GL_VIEWPORT_SWIZZLE_POSITIVE_Y_NV              0x9352
#define LOCAL_GL_VIEWPORT_SWIZZLE_POSITIVE_Z_NV              0x9354
#define LOCAL_GL_VIEWPORT_SWIZZLE_W_NV                       0x935B
#define LOCAL_GL_VIEWPORT_SWIZZLE_X_NV                       0x9358
#define LOCAL_GL_VIEWPORT_SWIZZLE_Y_NV                       0x9359
#define LOCAL_GL_VIEWPORT_SWIZZLE_Z_NV                       0x935A
#define LOCAL_GL_VIEW_CLASS_128_BITS                         0x82C4
#define LOCAL_GL_VIEW_CLASS_16_BITS                          0x82CA
#define LOCAL_GL_VIEW_CLASS_24_BITS                          0x82C9
#define LOCAL_GL_VIEW_CLASS_32_BITS                          0x82C8
#define LOCAL_GL_VIEW_CLASS_48_BITS                          0x82C7
#define LOCAL_GL_VIEW_CLASS_64_BITS                          0x82C6
#define LOCAL_GL_VIEW_CLASS_8_BITS                           0x82CB
#define LOCAL_GL_VIEW_CLASS_96_BITS                          0x82C5
#define LOCAL_GL_VIEW_CLASS_ASTC_10x10_RGBA                  0x9393
#define LOCAL_GL_VIEW_CLASS_ASTC_10x5_RGBA                   0x9390
#define LOCAL_GL_VIEW_CLASS_ASTC_10x6_RGBA                   0x9391
#define LOCAL_GL_VIEW_CLASS_ASTC_10x8_RGBA                   0x9392
#define LOCAL_GL_VIEW_CLASS_ASTC_12x10_RGBA                  0x9394
#define LOCAL_GL_VIEW_CLASS_ASTC_12x12_RGBA                  0x9395
#define LOCAL_GL_VIEW_CLASS_ASTC_4x4_RGBA                    0x9388
#define LOCAL_GL_VIEW_CLASS_ASTC_5x4_RGBA                    0x9389
#define LOCAL_GL_VIEW_CLASS_ASTC_5x5_RGBA                    0x938A
#define LOCAL_GL_VIEW_CLASS_ASTC_6x5_RGBA                    0x938B
#define LOCAL_GL_VIEW_CLASS_ASTC_6x6_RGBA                    0x938C
#define LOCAL_GL_VIEW_CLASS_ASTC_8x5_RGBA                    0x938D
#define LOCAL_GL_VIEW_CLASS_ASTC_8x6_RGBA                    0x938E
#define LOCAL_GL_VIEW_CLASS_ASTC_8x8_RGBA                    0x938F
#define LOCAL_GL_VIEW_CLASS_BPTC_FLOAT                       0x82D3
#define LOCAL_GL_VIEW_CLASS_BPTC_UNORM                       0x82D2
#define LOCAL_GL_VIEW_CLASS_EAC_R11                          0x9383
#define LOCAL_GL_VIEW_CLASS_EAC_RG11                         0x9384
#define LOCAL_GL_VIEW_CLASS_ETC2_EAC_RGBA                    0x9387
#define LOCAL_GL_VIEW_CLASS_ETC2_RGB                         0x9385
#define LOCAL_GL_VIEW_CLASS_ETC2_RGBA                        0x9386
#define LOCAL_GL_VIEW_CLASS_RGTC1_RED                        0x82D0
#define LOCAL_GL_VIEW_CLASS_RGTC2_RG                         0x82D1
#define LOCAL_GL_VIEW_CLASS_S3TC_DXT1_RGB                    0x82CC
#define LOCAL_GL_VIEW_CLASS_S3TC_DXT1_RGBA                   0x82CD
#define LOCAL_GL_VIEW_CLASS_S3TC_DXT3_RGBA                   0x82CE
#define LOCAL_GL_VIEW_CLASS_S3TC_DXT5_RGBA                   0x82CF
#define LOCAL_GL_VIEW_COMPATIBILITY_CLASS                    0x82B6
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_INDEX_ARB                 0x91A7
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_INDEX_EXT                 0x91A7
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_X_AMD                     0x9195
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_X_ARB                     0x9195
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_X_EXT                     0x9195
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Y_AMD                     0x9196
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Y_ARB                     0x9196
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Y_EXT                     0x9196
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Z_AMD                     0x9197
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Z_ARB                     0x9197
#define LOCAL_GL_VIRTUAL_PAGE_SIZE_Z_EXT                     0x9197
#define LOCAL_GL_VIVIDLIGHT_NV                               0x92A6
#define LOCAL_GL_VOLATILE_APPLE                              0x8A1A
#define LOCAL_GL_WAIT_FAILED                                 0x911D
#define LOCAL_GL_WAIT_FAILED_APPLE                           0x911D
#define LOCAL_GL_WARPS_PER_SM_NV                             0x933A
#define LOCAL_GL_WARP_SIZE_NV                                0x9339
#define LOCAL_GL_WEIGHTED_AVERAGE_ARB                        0x9367
#define LOCAL_GL_WEIGHTED_AVERAGE_EXT                        0x9367
#define LOCAL_GL_WEIGHT_ARRAY_ARB                            0x86AD
#define LOCAL_GL_WEIGHT_ARRAY_BUFFER_BINDING                 0x889E
#define LOCAL_GL_WEIGHT_ARRAY_BUFFER_BINDING_ARB             0x889E
#define LOCAL_GL_WEIGHT_ARRAY_BUFFER_BINDING_OES             0x889E
#define LOCAL_GL_WEIGHT_ARRAY_OES                            0x86AD
#define LOCAL_GL_WEIGHT_ARRAY_POINTER_ARB                    0x86AC
#define LOCAL_GL_WEIGHT_ARRAY_POINTER_OES                    0x86AC
#define LOCAL_GL_WEIGHT_ARRAY_SIZE_ARB                       0x86AB
#define LOCAL_GL_WEIGHT_ARRAY_SIZE_OES                       0x86AB
#define LOCAL_GL_WEIGHT_ARRAY_STRIDE_ARB                     0x86AA
#define LOCAL_GL_WEIGHT_ARRAY_STRIDE_OES                     0x86AA
#define LOCAL_GL_WEIGHT_ARRAY_TYPE_ARB                       0x86A9
#define LOCAL_GL_WEIGHT_ARRAY_TYPE_OES                       0x86A9
#define LOCAL_GL_WEIGHT_SUM_UNITY_ARB                        0x86A6
#define LOCAL_GL_WIDE_LINE_HINT_PGI                          0x1A222
#define LOCAL_GL_WINDOW_RECTANGLE_EXT                        0x8F12
#define LOCAL_GL_WINDOW_RECTANGLE_MODE_EXT                   0x8F13
#define LOCAL_GL_WRAP_BORDER_SUN                             0x81D4
#define LOCAL_GL_WRITEONLY_RENDERING_QCOM                    0x8823
#define LOCAL_GL_WRITE_DISCARD_NV                            0x88BE
#define LOCAL_GL_WRITE_ONLY                                  0x88B9
#define LOCAL_GL_WRITE_ONLY_ARB                              0x88B9
#define LOCAL_GL_WRITE_ONLY_OES                              0x88B9
#define LOCAL_GL_WRITE_PIXEL_DATA_RANGE_LENGTH_NV            0x887A
#define LOCAL_GL_WRITE_PIXEL_DATA_RANGE_NV                   0x8878
#define LOCAL_GL_WRITE_PIXEL_DATA_RANGE_POINTER_NV           0x887C
#define LOCAL_GL_W_EXT                                       0x87D8
#define LOCAL_GL_XOR                                         0x1506
#define LOCAL_GL_XOR_NV                                      0x1506
#define LOCAL_GL_X_EXT                                       0x87D5
#define LOCAL_GL_YCBAYCR8A_4224_NV                           0x9032
#define LOCAL_GL_YCBCR_422_APPLE                             0x85B9
#define LOCAL_GL_YCBCR_MESA                                  0x8757
#define LOCAL_GL_YCBYCR8_422_NV                              0x9031
#define LOCAL_GL_YCRCBA_SGIX                                 0x8319
#define LOCAL_GL_YCRCB_422_SGIX                              0x81BB
#define LOCAL_GL_YCRCB_444_SGIX                              0x81BC
#define LOCAL_GL_YCRCB_SGIX                                  0x8318
#define LOCAL_GL_Y_EXT                                       0x87D6
#define LOCAL_GL_Z400_BINARY_AMD                             0x8740
#define LOCAL_GL_Z4Y12Z4CB12Z4A12Z4Y12Z4CR12Z4A12_4224_NV    0x9036
#define LOCAL_GL_Z4Y12Z4CB12Z4CR12_444_NV                    0x9037
#define LOCAL_GL_Z4Y12Z4CB12Z4Y12Z4CR12_422_NV               0x9035
#define LOCAL_GL_Z6Y10Z6CB10Z6A10Z6Y10Z6CR10Z6A10_4224_NV    0x9034
#define LOCAL_GL_Z6Y10Z6CB10Z6Y10Z6CR10_422_NV               0x9033
#define LOCAL_GL_ZERO                                        0
#define LOCAL_GL_ZERO_EXT                                    0x87DD
#define LOCAL_GL_ZERO_TO_ONE                                 0x935F
#define LOCAL_GL_ZERO_TO_ONE_EXT                             0x935F
#define LOCAL_GL_ZOOM_X                                      0x0D16
#define LOCAL_GL_ZOOM_Y                                      0x0D17
#define LOCAL_GL_Z_EXT                                       0x87D7

// EGL
#define LOCAL_EGL_ALPHA_FORMAT                               0x3088
#define LOCAL_EGL_ALPHA_FORMAT_NONPRE                        0x308B
#define LOCAL_EGL_ALPHA_FORMAT_PRE                           0x308C
#define LOCAL_EGL_ALPHA_MASK_SIZE                            0x303E
#define LOCAL_EGL_ALPHA_SIZE                                 0x3021
#define LOCAL_EGL_ALREADY_SIGNALED_NV                        0x30EA
#define LOCAL_EGL_AUTO_STEREO_NV                             0x3136
#define LOCAL_EGL_BACK_BUFFER                                0x3084
#define LOCAL_EGL_BAD_ACCESS                                 0x3002
#define LOCAL_EGL_BAD_ALLOC                                  0x3003
#define LOCAL_EGL_BAD_ATTRIBUTE                              0x3004
#define LOCAL_EGL_BAD_CONFIG                                 0x3005
#define LOCAL_EGL_BAD_CONTEXT                                0x3006
#define LOCAL_EGL_BAD_CURRENT_SURFACE                        0x3007
#define LOCAL_EGL_BAD_DEVICE_EXT                             0x322B
#define LOCAL_EGL_BAD_DISPLAY                                0x3008
#define LOCAL_EGL_BAD_MATCH                                  0x3009
#define LOCAL_EGL_BAD_NATIVE_PIXMAP                          0x300A
#define LOCAL_EGL_BAD_NATIVE_WINDOW                          0x300B
#define LOCAL_EGL_BAD_OUTPUT_LAYER_EXT                       0x322D
#define LOCAL_EGL_BAD_OUTPUT_PORT_EXT                        0x322E
#define LOCAL_EGL_BAD_PARAMETER                              0x300C
#define LOCAL_EGL_BAD_STATE_KHR                              0x321C
#define LOCAL_EGL_BAD_STREAM_KHR                             0x321B
#define LOCAL_EGL_BAD_SURFACE                                0x300D
#define LOCAL_EGL_BIND_TO_TEXTURE_RGB                        0x3039
#define LOCAL_EGL_BIND_TO_TEXTURE_RGBA                       0x303A
#define LOCAL_EGL_BITMAP_ORIGIN_KHR                          0x30C8
#define LOCAL_EGL_BITMAP_PITCH_KHR                           0x30C7
#define LOCAL_EGL_BITMAP_PIXEL_ALPHA_OFFSET_KHR              0x30CC
#define LOCAL_EGL_BITMAP_PIXEL_BLUE_OFFSET_KHR               0x30CB
#define LOCAL_EGL_BITMAP_PIXEL_GREEN_OFFSET_KHR              0x30CA
#define LOCAL_EGL_BITMAP_PIXEL_LUMINANCE_OFFSET_KHR          0x30CD
#define LOCAL_EGL_BITMAP_PIXEL_RED_OFFSET_KHR                0x30C9
#define LOCAL_EGL_BITMAP_PIXEL_SIZE_KHR                      0x3110
#define LOCAL_EGL_BITMAP_POINTER_KHR                         0x30C6
#define LOCAL_EGL_BLUE_SIZE                                  0x3022
#define LOCAL_EGL_BOTTOM_NV                                  0x336E
#define LOCAL_EGL_BUFFER_AGE_EXT                             0x313D
#define LOCAL_EGL_BUFFER_AGE_KHR                             0x313D
#define LOCAL_EGL_BUFFER_COUNT_NV                            0x321D
#define LOCAL_EGL_BUFFER_DESTROYED                           0x3095
#define LOCAL_EGL_BUFFER_PRESERVED                           0x3094
#define LOCAL_EGL_BUFFER_SIZE                                0x3020
#define LOCAL_EGL_CLIENT_APIS                                0x308D
#define LOCAL_EGL_CLIENT_PIXMAP_POINTER_HI                   0x8F74
#define LOCAL_EGL_CL_EVENT_HANDLE                            0x309C
#define LOCAL_EGL_CL_EVENT_HANDLE_KHR                        0x309C
#define LOCAL_EGL_COLORSPACE                                 0x3087
#define LOCAL_EGL_COLORSPACE_LINEAR                          0x308A
#define LOCAL_EGL_COLORSPACE_sRGB                            0x3089
#define LOCAL_EGL_COLOR_ARGB_HI                              0x8F73
#define LOCAL_EGL_COLOR_BUFFER_TYPE                          0x303F
#define LOCAL_EGL_COLOR_COMPONENT_TYPE_EXT                   0x3339
#define LOCAL_EGL_COLOR_COMPONENT_TYPE_FIXED_EXT             0x333A
#define LOCAL_EGL_COLOR_COMPONENT_TYPE_FLOAT_EXT             0x333B
#define LOCAL_EGL_COLOR_COMPONENT_TYPE_INTEGER_ARM           0x3288
#define LOCAL_EGL_COLOR_COMPONENT_TYPE_UNSIGNED_INTEGER_ARM  0x3287
#define LOCAL_EGL_COLOR_FORMAT_HI                            0x8F70
#define LOCAL_EGL_COLOR_RGBA_HI                              0x8F72
#define LOCAL_EGL_COLOR_RGB_HI                               0x8F71
#define LOCAL_EGL_COMPOSITE_DEADLINE_ANDROID                 0x3431
#define LOCAL_EGL_COMPOSITE_INTERVAL_ANDROID                 0x3432
#define LOCAL_EGL_COMPOSITE_TO_PRESENT_LATENCY_ANDROID       0x3433
#define LOCAL_EGL_COMPOSITION_LATCH_TIME_ANDROID             0x3436
#define LOCAL_EGL_COMPOSITOR_DROP_NEWEST_FRAME_EXT           0x3462
#define LOCAL_EGL_COMPOSITOR_KEEP_NEWEST_FRAME_EXT           0x3463
#define LOCAL_EGL_CONDITION_SATISFIED                        0x30F6
#define LOCAL_EGL_CONDITION_SATISFIED_KHR                    0x30F6
#define LOCAL_EGL_CONDITION_SATISFIED_NV                     0x30EC
#define LOCAL_EGL_CONFIG_CAVEAT                              0x3027
#define LOCAL_EGL_CONFIG_ID                                  0x3028
#define LOCAL_EGL_CONFIG_SELECT_GROUP_EXT                    0x34C0
#define LOCAL_EGL_CONFORMANT                                 0x3042
#define LOCAL_EGL_CONFORMANT_KHR                             0x3042
#define LOCAL_EGL_CONSUMER_ACQUIRE_TIMEOUT_USEC_KHR          0x321E
#define LOCAL_EGL_CONSUMER_AUTO_ORIENTATION_NV               0x3369
#define LOCAL_EGL_CONSUMER_FRAME_KHR                         0x3213
#define LOCAL_EGL_CONSUMER_LATENCY_USEC_KHR                  0x3210
#define LOCAL_EGL_CONSUMER_MAX_FRAME_HINT_NV                 0x3338
#define LOCAL_EGL_CONSUMER_METADATA_NV                       0x3254
#define LOCAL_EGL_CONTEXT_CLIENT_TYPE                        0x3097
#define LOCAL_EGL_CONTEXT_CLIENT_VERSION                     0x3098
#define LOCAL_EGL_CONTEXT_FLAGS_KHR                          0x30FC
#define LOCAL_EGL_CONTEXT_LOST                               0x300E
#define LOCAL_EGL_CONTEXT_MAJOR_VERSION                      0x3098
#define LOCAL_EGL_CONTEXT_MAJOR_VERSION_KHR                  0x3098
#define LOCAL_EGL_CONTEXT_MINOR_VERSION                      0x30FB
#define LOCAL_EGL_CONTEXT_MINOR_VERSION_KHR                  0x30FB
#define LOCAL_EGL_CONTEXT_OPENGL_COMPATIBILITY_PROFILE_BIT   0x00000002
#define LOCAL_EGL_CONTEXT_OPENGL_COMPATIBILITY_PROFILE_BIT_KHR       0x00000002
#define LOCAL_EGL_CONTEXT_OPENGL_CORE_PROFILE_BIT            0x00000001
#define LOCAL_EGL_CONTEXT_OPENGL_CORE_PROFILE_BIT_KHR        0x00000001
#define LOCAL_EGL_CONTEXT_OPENGL_DEBUG                       0x31B0
#define LOCAL_EGL_CONTEXT_OPENGL_DEBUG_BIT_KHR               0x00000001
#define LOCAL_EGL_CONTEXT_OPENGL_FORWARD_COMPATIBLE          0x31B1
#define LOCAL_EGL_CONTEXT_OPENGL_FORWARD_COMPATIBLE_BIT_KHR  0x00000002
#define LOCAL_EGL_CONTEXT_OPENGL_NO_ERROR_KHR                0x31B3
#define LOCAL_EGL_CONTEXT_OPENGL_PROFILE_MASK                0x30FD
#define LOCAL_EGL_CONTEXT_OPENGL_PROFILE_MASK_KHR            0x30FD
#define LOCAL_EGL_CONTEXT_OPENGL_RESET_NOTIFICATION_STRATEGY 0x31BD
#define LOCAL_EGL_CONTEXT_OPENGL_RESET_NOTIFICATION_STRATEGY_EXT     0x3138
#define LOCAL_EGL_CONTEXT_OPENGL_RESET_NOTIFICATION_STRATEGY_KHR     0x31BD
#define LOCAL_EGL_CONTEXT_OPENGL_ROBUST_ACCESS               0x31B2
#define LOCAL_EGL_CONTEXT_OPENGL_ROBUST_ACCESS_BIT_KHR       0x00000004
#define LOCAL_EGL_CONTEXT_OPENGL_ROBUST_ACCESS_EXT           0x30BF
#define LOCAL_EGL_CONTEXT_PRIORITY_HIGH_IMG                  0x3101
#define LOCAL_EGL_CONTEXT_PRIORITY_LEVEL_IMG                 0x3100
#define LOCAL_EGL_CONTEXT_PRIORITY_LOW_IMG                   0x3103
#define LOCAL_EGL_CONTEXT_PRIORITY_MEDIUM_IMG                0x3102
#define LOCAL_EGL_CONTEXT_PRIORITY_REALTIME_NV               0x3357
#define LOCAL_EGL_CONTEXT_RELEASE_BEHAVIOR_FLUSH_KHR         0x2098
#define LOCAL_EGL_CONTEXT_RELEASE_BEHAVIOR_KHR               0x2097
#define LOCAL_EGL_CONTEXT_RELEASE_BEHAVIOR_NONE_KHR          0
#define LOCAL_EGL_CORE_NATIVE_ENGINE                         0x305B
#define LOCAL_EGL_COVERAGE_BUFFERS_NV                        0x30E0
#define LOCAL_EGL_COVERAGE_SAMPLES_NV                        0x30E1
#define LOCAL_EGL_COVERAGE_SAMPLE_RESOLVE_DEFAULT_NV         0x3132
#define LOCAL_EGL_COVERAGE_SAMPLE_RESOLVE_NONE_NV            0x3133
#define LOCAL_EGL_COVERAGE_SAMPLE_RESOLVE_NV                 0x3131
#define LOCAL_EGL_CTA861_3_MAX_CONTENT_LIGHT_LEVEL_EXT       0x3360
#define LOCAL_EGL_CTA861_3_MAX_FRAME_AVERAGE_LEVEL_EXT       0x3361
#define LOCAL_EGL_CUDA_DEVICE_NV                             0x323A
#define LOCAL_EGL_CUDA_EVENT_HANDLE_NV                       0x323B
#define LOCAL_EGL_D3D11_DEVICE_ANGLE                         0x33A1
#define LOCAL_EGL_D3D9_DEVICE_ANGLE                          0x33A0
#define LOCAL_EGL_D3D_TEXTURE_2D_SHARE_HANDLE_ANGLE          0x3200
#define LOCAL_EGL_DEBUG_CALLBACK_KHR                         0x33B8
#define LOCAL_EGL_DEBUG_MSG_CRITICAL_KHR                     0x33B9
#define LOCAL_EGL_DEBUG_MSG_ERROR_KHR                        0x33BA
#define LOCAL_EGL_DEBUG_MSG_INFO_KHR                         0x33BC
#define LOCAL_EGL_DEBUG_MSG_WARN_KHR                         0x33BB
#define LOCAL_EGL_DEFAULT_DISPLAY                            EGL_CAST(EGLNativeDisplayType,0)
#define LOCAL_EGL_DEPTH_ENCODING_NONE_NV                     0
#define LOCAL_EGL_DEPTH_ENCODING_NONLINEAR_NV                0x30E3
#define LOCAL_EGL_DEPTH_ENCODING_NV                          0x30E2
#define LOCAL_EGL_DEPTH_SIZE                                 0x3025
#define LOCAL_EGL_DEQUEUE_READY_TIME_ANDROID                 0x343B
#define LOCAL_EGL_DEVICE_EXT                                 0x322C
#define LOCAL_EGL_DEVICE_UUID_EXT                            0x335C
#define LOCAL_EGL_DISCARD_SAMPLES_ARM                        0x3286
#define LOCAL_EGL_DISPLAY_PRESENT_TIME_ANDROID               0x343A
#define LOCAL_EGL_DISPLAY_SCALING                            10000
#define LOCAL_EGL_DMA_BUF_PLANE0_FD_EXT                      0x3272
#define LOCAL_EGL_DMA_BUF_PLANE0_MODIFIER_HI_EXT             0x3444
#define LOCAL_EGL_DMA_BUF_PLANE0_MODIFIER_LO_EXT             0x3443
#define LOCAL_EGL_DMA_BUF_PLANE0_OFFSET_EXT                  0x3273
#define LOCAL_EGL_DMA_BUF_PLANE0_PITCH_EXT                   0x3274
#define LOCAL_EGL_DMA_BUF_PLANE1_FD_EXT                      0x3275
#define LOCAL_EGL_DMA_BUF_PLANE1_MODIFIER_HI_EXT             0x3446
#define LOCAL_EGL_DMA_BUF_PLANE1_MODIFIER_LO_EXT             0x3445
#define LOCAL_EGL_DMA_BUF_PLANE1_OFFSET_EXT                  0x3276
#define LOCAL_EGL_DMA_BUF_PLANE1_PITCH_EXT                   0x3277
#define LOCAL_EGL_DMA_BUF_PLANE2_FD_EXT                      0x3278
#define LOCAL_EGL_DMA_BUF_PLANE2_MODIFIER_HI_EXT             0x3448
#define LOCAL_EGL_DMA_BUF_PLANE2_MODIFIER_LO_EXT             0x3447
#define LOCAL_EGL_DMA_BUF_PLANE2_OFFSET_EXT                  0x3279
#define LOCAL_EGL_DMA_BUF_PLANE2_PITCH_EXT                   0x327A
#define LOCAL_EGL_DMA_BUF_PLANE3_FD_EXT                      0x3440
#define LOCAL_EGL_DMA_BUF_PLANE3_MODIFIER_HI_EXT             0x344A
#define LOCAL_EGL_DMA_BUF_PLANE3_MODIFIER_LO_EXT             0x3449
#define LOCAL_EGL_DMA_BUF_PLANE3_OFFSET_EXT                  0x3441
#define LOCAL_EGL_DMA_BUF_PLANE3_PITCH_EXT                   0x3442
#define LOCAL_EGL_DONT_CARE                                  EGL_CAST(EGLint,-1)
#define LOCAL_EGL_DRAW                                       0x3059
#define LOCAL_EGL_DRIVER_NAME_EXT                            0x335E
#define LOCAL_EGL_DRIVER_UUID_EXT                            0x335D
#define LOCAL_EGL_DRM_BUFFER_FORMAT_ARGB32_MESA              0x31D2
#define LOCAL_EGL_DRM_BUFFER_FORMAT_MESA                     0x31D0
#define LOCAL_EGL_DRM_BUFFER_MESA                            0x31D3
#define LOCAL_EGL_DRM_BUFFER_STRIDE_MESA                     0x31D4
#define LOCAL_EGL_DRM_BUFFER_USE_CURSOR_MESA                 0x00000004
#define LOCAL_EGL_DRM_BUFFER_USE_MESA                        0x31D1
#define LOCAL_EGL_DRM_BUFFER_USE_SCANOUT_MESA                0x00000001
#define LOCAL_EGL_DRM_BUFFER_USE_SHARE_MESA                  0x00000002
#define LOCAL_EGL_DRM_CONNECTOR_EXT                          0x3236
#define LOCAL_EGL_DRM_CRTC_EXT                               0x3234
#define LOCAL_EGL_DRM_DEVICE_FILE_EXT                        0x3233
#define LOCAL_EGL_DRM_MASTER_FD_EXT                          0x333C
#define LOCAL_EGL_DRM_PLANE_EXT                              0x3235
#define LOCAL_EGL_DRM_RENDER_NODE_FILE_EXT                   0x3377
#define LOCAL_EGL_EXTENSIONS                                 0x3055
#define LOCAL_EGL_EXTERNAL_REF_ID_EXT                        0x3461
#define LOCAL_EGL_FALSE                                      0
#define LOCAL_EGL_FIRST_COMPOSITION_GPU_FINISHED_TIME_ANDROID        0x3439
#define LOCAL_EGL_FIRST_COMPOSITION_START_TIME_ANDROID       0x3437
#define LOCAL_EGL_FIXED_SIZE_ANGLE                           0x3201
#define LOCAL_EGL_FOREVER                                    0xFFFFFFFFFFFFFFFF
#define LOCAL_EGL_FOREVER_KHR                                0xFFFFFFFFFFFFFFFF
#define LOCAL_EGL_FOREVER_NV                                 0xFFFFFFFFFFFFFFFF
#define LOCAL_EGL_FORMAT_ASTC_10X10_QCOM                     0x33ED
#define LOCAL_EGL_FORMAT_ASTC_10X10_SRGB_QCOM                0x340B
#define LOCAL_EGL_FORMAT_ASTC_10X5_QCOM                      0x33EA
#define LOCAL_EGL_FORMAT_ASTC_10X5_SRGB_QCOM                 0x3408
#define LOCAL_EGL_FORMAT_ASTC_10X6_QCOM                      0x33EB
#define LOCAL_EGL_FORMAT_ASTC_10X6_SRGB_QCOM                 0x3409
#define LOCAL_EGL_FORMAT_ASTC_10X8_QCOM                      0x33EC
#define LOCAL_EGL_FORMAT_ASTC_10X8_SRGB_QCOM                 0x340A
#define LOCAL_EGL_FORMAT_ASTC_12X10_QCOM                     0x33EE
#define LOCAL_EGL_FORMAT_ASTC_12X10_SRGB_QCOM                0x340C
#define LOCAL_EGL_FORMAT_ASTC_12X12_QCOM                     0x33EF
#define LOCAL_EGL_FORMAT_ASTC_12X12_SRGB_QCOM                0x340D
#define LOCAL_EGL_FORMAT_ASTC_4X4_QCOM                       0x33E2
#define LOCAL_EGL_FORMAT_ASTC_4X4_SRGB_QCOM                  0x3400
#define LOCAL_EGL_FORMAT_ASTC_5X4_QCOM                       0x33E3
#define LOCAL_EGL_FORMAT_ASTC_5X4_SRGB_QCOM                  0x3401
#define LOCAL_EGL_FORMAT_ASTC_5X5_QCOM                       0x33E4
#define LOCAL_EGL_FORMAT_ASTC_5X5_SRGB_QCOM                  0x3402
#define LOCAL_EGL_FORMAT_ASTC_6X5_QCOM                       0x33E5
#define LOCAL_EGL_FORMAT_ASTC_6X5_SRGB_QCOM                  0x3403
#define LOCAL_EGL_FORMAT_ASTC_6X6_QCOM                       0x33E6
#define LOCAL_EGL_FORMAT_ASTC_6X6_SRGB_QCOM                  0x3404
#define LOCAL_EGL_FORMAT_ASTC_8X5_QCOM                       0x33E7
#define LOCAL_EGL_FORMAT_ASTC_8X5_SRGB_QCOM                  0x3405
#define LOCAL_EGL_FORMAT_ASTC_8X6_QCOM                       0x33E8
#define LOCAL_EGL_FORMAT_ASTC_8X6_SRGB_QCOM                  0x3406
#define LOCAL_EGL_FORMAT_ASTC_8X8_QCOM                       0x33E9
#define LOCAL_EGL_FORMAT_ASTC_8X8_SRGB_QCOM                  0x3407
#define LOCAL_EGL_FORMAT_BGRA_8888_QCOM                      0x3129
#define LOCAL_EGL_FORMAT_BGRX_8888_QCOM                      0x312A
#define LOCAL_EGL_FORMAT_FLAG_MACROTILE_QCOM                 0x33E1
#define LOCAL_EGL_FORMAT_FLAG_QCOM                           0x31CF
#define LOCAL_EGL_FORMAT_FLAG_UBWC_QCOM                      0x33E0
#define LOCAL_EGL_FORMAT_IYUV_QCOM                           0x31C7
#define LOCAL_EGL_FORMAT_NV12_4R_QCOM                        0x3412
#define LOCAL_EGL_FORMAT_NV12_4R_UV_QCOM                     0x3414
#define LOCAL_EGL_FORMAT_NV12_4R_Y_QCOM                      0x3413
#define LOCAL_EGL_FORMAT_NV12_QCOM                           0x31C2
#define LOCAL_EGL_FORMAT_NV12_TILED_QCOM                     0x3128
#define LOCAL_EGL_FORMAT_NV12_UV_QCOM                        0x3410
#define LOCAL_EGL_FORMAT_NV12_Y_QCOM                         0x340F
#define LOCAL_EGL_FORMAT_NV21_QCOM                           0x3127
#define LOCAL_EGL_FORMAT_NV21_VU_QCOM                        0x3411
#define LOCAL_EGL_FORMAT_P010_QCOM                           0x3415
#define LOCAL_EGL_FORMAT_P010_UV_QCOM                        0x3417
#define LOCAL_EGL_FORMAT_P010_Y_QCOM                         0x3416
#define LOCAL_EGL_FORMAT_R8_QCOM                             0x31C0
#define LOCAL_EGL_FORMAT_RG88_QCOM                           0x31C1
#define LOCAL_EGL_FORMAT_RGBA_1010102_QCOM                   0x31CE
#define LOCAL_EGL_FORMAT_RGBA_16_FLOAT_QCOM                  0x31CD
#define LOCAL_EGL_FORMAT_RGBA_4444_QCOM                      0x31CA
#define LOCAL_EGL_FORMAT_RGBA_5551_QCOM                      0x31C9
#define LOCAL_EGL_FORMAT_RGBA_8888_EXACT_KHR                 0x30C2
#define LOCAL_EGL_FORMAT_RGBA_8888_KHR                       0x30C3
#define LOCAL_EGL_FORMAT_RGBA_8888_QCOM                      0x3122
#define LOCAL_EGL_FORMAT_RGBX_8888_QCOM                      0x312F
#define LOCAL_EGL_FORMAT_RGB_565_EXACT_KHR                   0x30C0
#define LOCAL_EGL_FORMAT_RGB_565_KHR                         0x30C1
#define LOCAL_EGL_FORMAT_RGB_565_QCOM                        0x3123
#define LOCAL_EGL_FORMAT_RGB_888_QCOM                        0x31C8
#define LOCAL_EGL_FORMAT_RG_1616_FLOAT_QCOM                  0x31CC
#define LOCAL_EGL_FORMAT_R_16_FLOAT_QCOM                     0x31CB
#define LOCAL_EGL_FORMAT_SRGBA_8888_QCOM                     0x31C4
#define LOCAL_EGL_FORMAT_SRGBX_8888_QCOM                     0x31C3
#define LOCAL_EGL_FORMAT_TP10_QCOM                           0x340E
#define LOCAL_EGL_FORMAT_TP10_UV_QCOM                        0x3419
#define LOCAL_EGL_FORMAT_TP10_Y_QCOM                         0x3418
#define LOCAL_EGL_FORMAT_UYVY_QCOM                           0x3125
#define LOCAL_EGL_FORMAT_VYUY_QCOM                           0x31C6
#define LOCAL_EGL_FORMAT_YUYV_QCOM                           0x3124
#define LOCAL_EGL_FORMAT_YV12_QCOM                           0x3126
#define LOCAL_EGL_FORMAT_YVYU_QCOM                           0x31C5
#define LOCAL_EGL_FRAMEBUFFER_TARGET_ANDROID                 0x3147
#define LOCAL_EGL_FRONT_BUFFER_AUTO_REFRESH_ANDROID          0x314C
#define LOCAL_EGL_FRONT_BUFFER_EXT                           0x3464
#define LOCAL_EGL_GENERATE_RESET_ON_VIDEO_MEMORY_PURGE_NV    0x334C
#define LOCAL_EGL_GENERIC_TOKEN_1_QCOM                       0x3420
#define LOCAL_EGL_GENERIC_TOKEN_2_QCOM                       0x3421
#define LOCAL_EGL_GENERIC_TOKEN_3_QCOM                       0x3422
#define LOCAL_EGL_GL_COLORSPACE                              0x309D
#define LOCAL_EGL_GL_COLORSPACE_BT2020_LINEAR_EXT            0x333F
#define LOCAL_EGL_GL_COLORSPACE_BT2020_PQ_EXT                0x3340
#define LOCAL_EGL_GL_COLORSPACE_DEFAULT_EXT                  0x314D
#define LOCAL_EGL_GL_COLORSPACE_DISPLAY_P3_EXT               0x3363
#define LOCAL_EGL_GL_COLORSPACE_DISPLAY_P3_LINEAR_EXT        0x3362
#define LOCAL_EGL_GL_COLORSPACE_DISPLAY_P3_PASSTHROUGH_EXT   0x3490
#define LOCAL_EGL_GL_COLORSPACE_KHR                          0x309D
#define LOCAL_EGL_GL_COLORSPACE_LINEAR                       0x308A
#define LOCAL_EGL_GL_COLORSPACE_LINEAR_KHR                   0x308A
#define LOCAL_EGL_GL_COLORSPACE_SCRGB_EXT                    0x3351
#define LOCAL_EGL_GL_COLORSPACE_SCRGB_LINEAR_EXT             0x3350
#define LOCAL_EGL_GL_COLORSPACE_SRGB                         0x3089
#define LOCAL_EGL_GL_COLORSPACE_SRGB_KHR                     0x3089
#define LOCAL_EGL_GL_RENDERBUFFER                            0x30B9
#define LOCAL_EGL_GL_RENDERBUFFER_KHR                        0x30B9
#define LOCAL_EGL_GL_TEXTURE_2D                              0x30B1
#define LOCAL_EGL_GL_TEXTURE_2D_KHR                          0x30B1
#define LOCAL_EGL_GL_TEXTURE_3D                              0x30B2
#define LOCAL_EGL_GL_TEXTURE_3D_KHR                          0x30B2
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X             0x30B4
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_X_KHR         0x30B4
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y             0x30B6
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Y_KHR         0x30B6
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z             0x30B8
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_NEGATIVE_Z_KHR         0x30B8
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_X             0x30B3
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_X_KHR         0x30B3
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y             0x30B5
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_Y_KHR         0x30B5
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z             0x30B7
#define LOCAL_EGL_GL_TEXTURE_CUBE_MAP_POSITIVE_Z_KHR         0x30B7
#define LOCAL_EGL_GL_TEXTURE_LEVEL                           0x30BC
#define LOCAL_EGL_GL_TEXTURE_LEVEL_KHR                       0x30BC
#define LOCAL_EGL_GL_TEXTURE_ZOFFSET                         0x30BD
#define LOCAL_EGL_GL_TEXTURE_ZOFFSET_KHR                     0x30BD
#define LOCAL_EGL_GPU_PERF_HINT_QCOM                         0x32D0
#define LOCAL_EGL_GREEN_SIZE                                 0x3023
#define LOCAL_EGL_HEIGHT                                     0x3056
#define LOCAL_EGL_HINT_PERSISTENT_QCOM                       0x32D1
#define LOCAL_EGL_HORIZONTAL_RESOLUTION                      0x3090
#define LOCAL_EGL_IMAGE_FORMAT_QCOM                          0x3121
#define LOCAL_EGL_IMAGE_NUM_PLANES_QCOM                      0x32B0
#define LOCAL_EGL_IMAGE_PLANE_DEPTH_0_QCOM                   0x32B4
#define LOCAL_EGL_IMAGE_PLANE_DEPTH_1_QCOM                   0x32B5
#define LOCAL_EGL_IMAGE_PLANE_DEPTH_2_QCOM                   0x32B6
#define LOCAL_EGL_IMAGE_PLANE_HEIGHT_0_QCOM                  0x32BA
#define LOCAL_EGL_IMAGE_PLANE_HEIGHT_1_QCOM                  0x32BB
#define LOCAL_EGL_IMAGE_PLANE_HEIGHT_2_QCOM                  0x32BC
#define LOCAL_EGL_IMAGE_PLANE_PITCH_0_QCOM                   0x32B1
#define LOCAL_EGL_IMAGE_PLANE_PITCH_1_QCOM                   0x32B2
#define LOCAL_EGL_IMAGE_PLANE_PITCH_2_QCOM                   0x32B3
#define LOCAL_EGL_IMAGE_PLANE_POINTER_0_QCOM                 0x32BD
#define LOCAL_EGL_IMAGE_PLANE_POINTER_1_QCOM                 0x32BE
#define LOCAL_EGL_IMAGE_PLANE_POINTER_2_QCOM                 0x32BF
#define LOCAL_EGL_IMAGE_PLANE_WIDTH_0_QCOM                   0x32B7
#define LOCAL_EGL_IMAGE_PLANE_WIDTH_1_QCOM                   0x32B8
#define LOCAL_EGL_IMAGE_PLANE_WIDTH_2_QCOM                   0x32B9
#define LOCAL_EGL_IMAGE_PRESERVED                            0x30D2
#define LOCAL_EGL_IMAGE_PRESERVED_KHR                        0x30D2
#define LOCAL_EGL_IMPORT_EXPLICIT_SYNC_EXT                   0x3472
#define LOCAL_EGL_IMPORT_IMPLICIT_SYNC_EXT                   0x3471
#define LOCAL_EGL_IMPORT_SYNC_TYPE_EXT                       0x3470
#define LOCAL_EGL_INTEROP_BIT_KHR                            0x0010
#define LOCAL_EGL_ITU_REC2020_EXT                            0x3281
#define LOCAL_EGL_ITU_REC601_EXT                             0x327F
#define LOCAL_EGL_ITU_REC709_EXT                             0x3280
#define LOCAL_EGL_LARGEST_PBUFFER                            0x3058
#define LOCAL_EGL_LAST_COMPOSITION_START_TIME_ANDROID        0x3438
#define LOCAL_EGL_LEFT_NV                                    0x336B
#define LOCAL_EGL_LEVEL                                      0x3029
#define LOCAL_EGL_LINUX_DMA_BUF_EXT                          0x3270
#define LOCAL_EGL_LINUX_DRM_FOURCC_EXT                       0x3271
#define LOCAL_EGL_LOCK_SURFACE_BIT_KHR                       0x0080
#define LOCAL_EGL_LOCK_USAGE_HINT_KHR                        0x30C5
#define LOCAL_EGL_LOSE_CONTEXT_ON_RESET                      0x31BF
#define LOCAL_EGL_LOSE_CONTEXT_ON_RESET_EXT                  0x31BF
#define LOCAL_EGL_LOSE_CONTEXT_ON_RESET_KHR                  0x31BF
#define LOCAL_EGL_LOWER_LEFT_KHR                             0x30CE
#define LOCAL_EGL_LUMINANCE_BUFFER                           0x308F
#define LOCAL_EGL_LUMINANCE_SIZE                             0x303D
#define LOCAL_EGL_MAP_PRESERVE_PIXELS_KHR                    0x30C4
#define LOCAL_EGL_MATCH_FORMAT_KHR                           0x3043
#define LOCAL_EGL_MATCH_NATIVE_PIXMAP                        0x3041
#define LOCAL_EGL_MAX_PBUFFER_HEIGHT                         0x302A
#define LOCAL_EGL_MAX_PBUFFER_PIXELS                         0x302B
#define LOCAL_EGL_MAX_PBUFFER_WIDTH                          0x302C
#define LOCAL_EGL_MAX_STREAM_METADATA_BLOCKS_NV              0x3250
#define LOCAL_EGL_MAX_STREAM_METADATA_BLOCK_SIZE_NV          0x3251
#define LOCAL_EGL_MAX_STREAM_METADATA_TOTAL_SIZE_NV          0x3252
#define LOCAL_EGL_MAX_SWAP_INTERVAL                          0x303C
#define LOCAL_EGL_METADATA0_SIZE_NV                          0x3255
#define LOCAL_EGL_METADATA0_TYPE_NV                          0x3259
#define LOCAL_EGL_METADATA1_SIZE_NV                          0x3256
#define LOCAL_EGL_METADATA1_TYPE_NV                          0x325A
#define LOCAL_EGL_METADATA2_SIZE_NV                          0x3257
#define LOCAL_EGL_METADATA2_TYPE_NV                          0x325B
#define LOCAL_EGL_METADATA3_SIZE_NV                          0x3258
#define LOCAL_EGL_METADATA3_TYPE_NV                          0x325C
#define LOCAL_EGL_METADATA_SCALING_EXT                       50000
#define LOCAL_EGL_MIN_SWAP_INTERVAL                          0x303B
#define LOCAL_EGL_MIPMAP_LEVEL                               0x3083
#define LOCAL_EGL_MIPMAP_TEXTURE                             0x3082
#define LOCAL_EGL_MULTISAMPLE_RESOLVE                        0x3099
#define LOCAL_EGL_MULTISAMPLE_RESOLVE_BOX                    0x309B
#define LOCAL_EGL_MULTISAMPLE_RESOLVE_BOX_BIT                0x0200
#define LOCAL_EGL_MULTISAMPLE_RESOLVE_DEFAULT                0x309A
#define LOCAL_EGL_MULTIVIEW_VIEW_COUNT_EXT                   0x3134
#define LOCAL_EGL_MUTABLE_RENDER_BUFFER_BIT_KHR              0x1000
#define LOCAL_EGL_NATIVE_BUFFER_ANDROID                      0x3140
#define LOCAL_EGL_NATIVE_BUFFER_MULTIPLANE_SEPARATE_IMG      0x3105
#define LOCAL_EGL_NATIVE_BUFFER_PLANE_OFFSET_IMG             0x3106
#define LOCAL_EGL_NATIVE_BUFFER_TIZEN                        0x32A0
#define LOCAL_EGL_NATIVE_BUFFER_USAGE_ANDROID                0x3143
#define LOCAL_EGL_NATIVE_BUFFER_USAGE_PROTECTED_BIT_ANDROID  0x00000001
#define LOCAL_EGL_NATIVE_BUFFER_USAGE_RENDERBUFFER_BIT_ANDROID       0x00000002
#define LOCAL_EGL_NATIVE_BUFFER_USAGE_TEXTURE_BIT_ANDROID    0x00000004
#define LOCAL_EGL_NATIVE_PIXMAP_KHR                          0x30B0
#define LOCAL_EGL_NATIVE_RENDERABLE                          0x302D
#define LOCAL_EGL_NATIVE_SURFACE_TIZEN                       0x32A1
#define LOCAL_EGL_NATIVE_VISUAL_ID                           0x302E
#define LOCAL_EGL_NATIVE_VISUAL_TYPE                         0x302F
#define LOCAL_EGL_NEW_IMAGE_QCOM                             0x3120
#define LOCAL_EGL_NONE                                       0x3038
#define LOCAL_EGL_NON_CONFORMANT_CONFIG                      0x3051
#define LOCAL_EGL_NOT_INITIALIZED                            0x3001
#define LOCAL_EGL_NO_CONFIG_KHR                              EGL_CAST(EGLConfig,0)
#define LOCAL_EGL_NO_CONTEXT                                 EGL_CAST(EGLContext,0)
#define LOCAL_EGL_NO_DEVICE_EXT                              EGL_CAST(EGLDeviceEXT,0)
#define LOCAL_EGL_NO_DISPLAY                                 EGL_CAST(EGLDisplay,0)
#define LOCAL_EGL_NO_FILE_DESCRIPTOR_KHR                     EGL_CAST(EGLNativeFileDescriptorKHR,-1)
#define LOCAL_EGL_NO_IMAGE                                   EGL_CAST(EGLImage,0)
#define LOCAL_EGL_NO_IMAGE_KHR                               EGL_CAST(EGLImageKHR,0)
#define LOCAL_EGL_NO_NATIVE_FENCE_FD_ANDROID                 -1
#define LOCAL_EGL_NO_OUTPUT_LAYER_EXT                        EGL_CAST(EGLOutputLayerEXT,0)
#define LOCAL_EGL_NO_OUTPUT_PORT_EXT                         EGL_CAST(EGLOutputPortEXT,0)
#define LOCAL_EGL_NO_RESET_NOTIFICATION                      0x31BE
#define LOCAL_EGL_NO_RESET_NOTIFICATION_EXT                  0x31BE
#define LOCAL_EGL_NO_RESET_NOTIFICATION_KHR                  0x31BE
#define LOCAL_EGL_NO_STREAM_KHR                              EGL_CAST(EGLStreamKHR,0)
#define LOCAL_EGL_NO_SURFACE                                 EGL_CAST(EGLSurface,0)
#define LOCAL_EGL_NO_SYNC                                    EGL_CAST(EGLSync,0)
#define LOCAL_EGL_NO_SYNC_KHR                                EGL_CAST(EGLSyncKHR,0)
#define LOCAL_EGL_NO_SYNC_NV                                 EGL_CAST(EGLSyncNV,0)
#define LOCAL_EGL_NO_TEXTURE                                 0x305C
#define LOCAL_EGL_OBJECT_CONTEXT_KHR                         0x33B2
#define LOCAL_EGL_OBJECT_DISPLAY_KHR                         0x33B1
#define LOCAL_EGL_OBJECT_IMAGE_KHR                           0x33B4
#define LOCAL_EGL_OBJECT_STREAM_KHR                          0x33B6
#define LOCAL_EGL_OBJECT_SURFACE_KHR                         0x33B3
#define LOCAL_EGL_OBJECT_SYNC_KHR                            0x33B5
#define LOCAL_EGL_OBJECT_THREAD_KHR                          0x33B0
#define LOCAL_EGL_OPENGL_API                                 0x30A2
#define LOCAL_EGL_OPENGL_BIT                                 0x0008
#define LOCAL_EGL_OPENGL_ES2_BIT                             0x0004
#define LOCAL_EGL_OPENGL_ES3_BIT                             0x00000040
#define LOCAL_EGL_OPENGL_ES3_BIT_KHR                         0x00000040
#define LOCAL_EGL_OPENGL_ES_API                              0x30A0
#define LOCAL_EGL_OPENGL_ES_BIT                              0x0001
#define LOCAL_EGL_OPENMAX_IL_BIT_KHR                         0x0020
#define LOCAL_EGL_OPENVG_API                                 0x30A1
#define LOCAL_EGL_OPENVG_BIT                                 0x0002
#define LOCAL_EGL_OPENVG_IMAGE                               0x3096
#define LOCAL_EGL_OPENWF_DEVICE_ID_EXT                       0x3237
#define LOCAL_EGL_OPENWF_PIPELINE_ID_EXT                     0x3238
#define LOCAL_EGL_OPENWF_PORT_ID_EXT                         0x3239
#define LOCAL_EGL_OPTIMAL_FORMAT_BIT_KHR                     0x0100
#define LOCAL_EGL_PBUFFER_BIT                                0x0001
#define LOCAL_EGL_PBUFFER_IMAGE_BIT_TAO                      0x0008
#define LOCAL_EGL_PBUFFER_PALETTE_IMAGE_BIT_TAO              0x0010
#define LOCAL_EGL_PENDING_FRAME_NV                           0x3329
#define LOCAL_EGL_PENDING_METADATA_NV                        0x3328
#define LOCAL_EGL_PIXEL_ASPECT_RATIO                         0x3092
#define LOCAL_EGL_PIXMAP_BIT                                 0x0002
#define LOCAL_EGL_PLATFORM_ANDROID_KHR                       0x3141
#define LOCAL_EGL_PLATFORM_DEVICE_EXT                        0x313F
#define LOCAL_EGL_PLATFORM_GBM_KHR                           0x31D7
#define LOCAL_EGL_PLATFORM_GBM_MESA                          0x31D7
#define LOCAL_EGL_PLATFORM_SURFACELESS_MESA                  0x31DD
#define LOCAL_EGL_PLATFORM_WAYLAND_EXT                       0x31D8
#define LOCAL_EGL_PLATFORM_WAYLAND_KHR                       0x31D8
#define LOCAL_EGL_PLATFORM_X11_EXT                           0x31D5
#define LOCAL_EGL_PLATFORM_X11_KHR                           0x31D5
#define LOCAL_EGL_PLATFORM_X11_SCREEN_EXT                    0x31D6
#define LOCAL_EGL_PLATFORM_X11_SCREEN_KHR                    0x31D6
#define LOCAL_EGL_PLATFORM_XCB_EXT                           0x31DC
#define LOCAL_EGL_PLATFORM_XCB_SCREEN_EXT                    0x31DE
#define LOCAL_EGL_POST_SUB_BUFFER_SUPPORTED_NV               0x30BE
#define LOCAL_EGL_PRIMARY_COMPOSITOR_CONTEXT_EXT             0x3460
#define LOCAL_EGL_PRODUCER_AUTO_ORIENTATION_NV               0x336A
#define LOCAL_EGL_PRODUCER_FRAME_KHR                         0x3212
#define LOCAL_EGL_PRODUCER_MAX_FRAME_HINT_NV                 0x3337
#define LOCAL_EGL_PRODUCER_METADATA_NV                       0x3253
#define LOCAL_EGL_PROTECTED_CONTENT_EXT                      0x32C0
#define LOCAL_EGL_QUADRUPLE_BUFFER_NV                        0x3231
#define LOCAL_EGL_READ                                       0x305A
#define LOCAL_EGL_READS_DONE_TIME_ANDROID                    0x343C
#define LOCAL_EGL_READ_SURFACE_BIT_KHR                       0x0001
#define LOCAL_EGL_RECORDABLE_ANDROID                         0x3142
#define LOCAL_EGL_RED_SIZE                                   0x3024
#define LOCAL_EGL_RENDERABLE_TYPE                            0x3040
#define LOCAL_EGL_RENDERER_EXT                               0x335F
#define LOCAL_EGL_RENDERING_COMPLETE_TIME_ANDROID            0x3435
#define LOCAL_EGL_RENDER_BUFFER                              0x3086
#define LOCAL_EGL_REQUESTED_PRESENT_TIME_ANDROID             0x3434
#define LOCAL_EGL_RGB_BUFFER                                 0x308E
#define LOCAL_EGL_RIGHT_NV                                   0x336C
#define LOCAL_EGL_SAMPLES                                    0x3031
#define LOCAL_EGL_SAMPLE_BUFFERS                             0x3032
#define LOCAL_EGL_SAMPLE_RANGE_HINT_EXT                      0x327C
#define LOCAL_EGL_SHARED_IMAGE_NOK                           0x30DA
#define LOCAL_EGL_SIGNALED                                   0x30F2
#define LOCAL_EGL_SIGNALED_KHR                               0x30F2
#define LOCAL_EGL_SIGNALED_NV                                0x30E8
#define LOCAL_EGL_SINGLE_BUFFER                              0x3085
#define LOCAL_EGL_SLOW_CONFIG                                0x3050
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_BX_EXT           0x3345
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_BY_EXT           0x3346
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_GX_EXT           0x3343
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_GY_EXT           0x3344
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_RX_EXT           0x3341
#define LOCAL_EGL_SMPTE2086_DISPLAY_PRIMARY_RY_EXT           0x3342
#define LOCAL_EGL_SMPTE2086_MAX_LUMINANCE_EXT                0x3349
#define LOCAL_EGL_SMPTE2086_MIN_LUMINANCE_EXT                0x334A
#define LOCAL_EGL_SMPTE2086_WHITE_POINT_X_EXT                0x3347
#define LOCAL_EGL_SMPTE2086_WHITE_POINT_Y_EXT                0x3348
#define LOCAL_EGL_SOCKET_HANDLE_NV                           0x324C
#define LOCAL_EGL_SOCKET_TYPE_INET_NV                        0x324F
#define LOCAL_EGL_SOCKET_TYPE_NV                             0x324D
#define LOCAL_EGL_SOCKET_TYPE_UNIX_NV                        0x324E
#define LOCAL_EGL_STENCIL_SIZE                               0x3026
#define LOCAL_EGL_STREAM_BIT_KHR                             0x0800
#define LOCAL_EGL_STREAM_CONSUMER_IMAGE_NV                   0x3373
#define LOCAL_EGL_STREAM_CONSUMER_NV                         0x3248
#define LOCAL_EGL_STREAM_CROSS_DISPLAY_NV                    0x334E
#define LOCAL_EGL_STREAM_CROSS_OBJECT_NV                     0x334D
#define LOCAL_EGL_STREAM_CROSS_PARTITION_NV                  0x323F
#define LOCAL_EGL_STREAM_CROSS_PROCESS_NV                    0x3245
#define LOCAL_EGL_STREAM_CROSS_SYSTEM_NV                     0x334F
#define LOCAL_EGL_STREAM_DMA_NV                              0x3371
#define LOCAL_EGL_STREAM_DMA_SERVER_NV                       0x3372
#define LOCAL_EGL_STREAM_ENDPOINT_NV                         0x3243
#define LOCAL_EGL_STREAM_FIFO_LENGTH_KHR                     0x31FC
#define LOCAL_EGL_STREAM_FIFO_SYNCHRONOUS_NV                 0x3336
#define LOCAL_EGL_STREAM_FRAME_MAJOR_AXIS_NV                 0x3368
#define LOCAL_EGL_STREAM_FRAME_ORIGIN_X_NV                   0x3366
#define LOCAL_EGL_STREAM_FRAME_ORIGIN_Y_NV                   0x3367
#define LOCAL_EGL_STREAM_IMAGE_ADD_NV                        0x3374
#define LOCAL_EGL_STREAM_IMAGE_AVAILABLE_NV                  0x3376
#define LOCAL_EGL_STREAM_IMAGE_REMOVE_NV                     0x3375
#define LOCAL_EGL_STREAM_LOCAL_NV                            0x3244
#define LOCAL_EGL_STREAM_PRODUCER_NV                         0x3247
#define LOCAL_EGL_STREAM_PROTOCOL_FD_NV                      0x3246
#define LOCAL_EGL_STREAM_PROTOCOL_NV                         0x3242
#define LOCAL_EGL_STREAM_PROTOCOL_SOCKET_NV                  0x324B
#define LOCAL_EGL_STREAM_STATE_CONNECTING_KHR                0x3216
#define LOCAL_EGL_STREAM_STATE_CREATED_KHR                   0x3215
#define LOCAL_EGL_STREAM_STATE_DISCONNECTED_KHR              0x321A
#define LOCAL_EGL_STREAM_STATE_EMPTY_KHR                     0x3217
#define LOCAL_EGL_STREAM_STATE_INITIALIZING_NV               0x3240
#define LOCAL_EGL_STREAM_STATE_KHR                           0x3214
#define LOCAL_EGL_STREAM_STATE_NEW_FRAME_AVAILABLE_KHR       0x3218
#define LOCAL_EGL_STREAM_STATE_OLD_FRAME_AVAILABLE_KHR       0x3219
#define LOCAL_EGL_STREAM_TIME_CONSUMER_KHR                   0x31FE
#define LOCAL_EGL_STREAM_TIME_NOW_KHR                        0x31FD
#define LOCAL_EGL_STREAM_TIME_PENDING_NV                     0x332A
#define LOCAL_EGL_STREAM_TIME_PRODUCER_KHR                   0x31FF
#define LOCAL_EGL_STREAM_TYPE_NV                             0x3241
#define LOCAL_EGL_SUCCESS                                    0x3000
#define LOCAL_EGL_SUPPORT_RESET_NV                           0x3334
#define LOCAL_EGL_SUPPORT_REUSE_NV                           0x3335
#define LOCAL_EGL_SURFACE_TYPE                               0x3033
#define LOCAL_EGL_SWAP_BEHAVIOR                              0x3093
#define LOCAL_EGL_SWAP_BEHAVIOR_PRESERVED_BIT                0x0400
#define LOCAL_EGL_SWAP_INTERVAL_EXT                          0x322F
#define LOCAL_EGL_SYNC_CLIENT_EXT                            0x3364
#define LOCAL_EGL_SYNC_CLIENT_SIGNAL_EXT                     0x3365
#define LOCAL_EGL_SYNC_CL_EVENT                              0x30FE
#define LOCAL_EGL_SYNC_CL_EVENT_COMPLETE                     0x30FF
#define LOCAL_EGL_SYNC_CL_EVENT_COMPLETE_KHR                 0x30FF
#define LOCAL_EGL_SYNC_CL_EVENT_KHR                          0x30FE
#define LOCAL_EGL_SYNC_CONDITION                             0x30F8
#define LOCAL_EGL_SYNC_CONDITION_KHR                         0x30F8
#define LOCAL_EGL_SYNC_CONDITION_NV                          0x30EE
#define LOCAL_EGL_SYNC_CUDA_EVENT_COMPLETE_NV                0x323D
#define LOCAL_EGL_SYNC_CUDA_EVENT_NV                         0x323C
#define LOCAL_EGL_SYNC_FENCE                                 0x30F9
#define LOCAL_EGL_SYNC_FENCE_KHR                             0x30F9
#define LOCAL_EGL_SYNC_FENCE_NV                              0x30EF
#define LOCAL_EGL_SYNC_FLUSH_COMMANDS_BIT                    0x0001
#define LOCAL_EGL_SYNC_FLUSH_COMMANDS_BIT_KHR                0x0001
#define LOCAL_EGL_SYNC_FLUSH_COMMANDS_BIT_NV                 0x0001
#define LOCAL_EGL_SYNC_NATIVE_FENCE_ANDROID                  0x3144
#define LOCAL_EGL_SYNC_NATIVE_FENCE_FD_ANDROID               0x3145
#define LOCAL_EGL_SYNC_NATIVE_FENCE_SIGNALED_ANDROID         0x3146
#define LOCAL_EGL_SYNC_NEW_FRAME_NV                          0x321F
#define LOCAL_EGL_SYNC_PRIOR_COMMANDS_COMPLETE               0x30F0
#define LOCAL_EGL_SYNC_PRIOR_COMMANDS_COMPLETE_KHR           0x30F0
#define LOCAL_EGL_SYNC_PRIOR_COMMANDS_COMPLETE_NV            0x30E6
#define LOCAL_EGL_SYNC_PRIOR_COMMANDS_IMPLICIT_EXTERNAL_ARM  0x328A
#define LOCAL_EGL_SYNC_REUSABLE_KHR                          0x30FA
#define LOCAL_EGL_SYNC_STATUS                                0x30F1
#define LOCAL_EGL_SYNC_STATUS_KHR                            0x30F1
#define LOCAL_EGL_SYNC_STATUS_NV                             0x30E7
#define LOCAL_EGL_SYNC_TYPE                                  0x30F7
#define LOCAL_EGL_SYNC_TYPE_KHR                              0x30F7
#define LOCAL_EGL_SYNC_TYPE_NV                               0x30ED
#define LOCAL_EGL_TEXTURE_2D                                 0x305F
#define LOCAL_EGL_TEXTURE_EXTERNAL_WL                        0x31DA
#define LOCAL_EGL_TEXTURE_FORMAT                             0x3080
#define LOCAL_EGL_TEXTURE_RGB                                0x305D
#define LOCAL_EGL_TEXTURE_RGBA                               0x305E
#define LOCAL_EGL_TEXTURE_TARGET                             0x3081
#define LOCAL_EGL_TEXTURE_Y_UV_WL                            0x31D8
#define LOCAL_EGL_TEXTURE_Y_U_V_WL                           0x31D7
#define LOCAL_EGL_TEXTURE_Y_XUXV_WL                          0x31D9
#define LOCAL_EGL_TIMEOUT_EXPIRED                            0x30F5
#define LOCAL_EGL_TIMEOUT_EXPIRED_KHR                        0x30F5
#define LOCAL_EGL_TIMEOUT_EXPIRED_NV                         0x30EB
#define LOCAL_EGL_TIMESTAMPS_ANDROID                         0x3430
#define LOCAL_EGL_TIMESTAMP_INVALID_ANDROID                  EGL_CAST(EGLnsecsANDROID,-1)
#define LOCAL_EGL_TIMESTAMP_PENDING_ANDROID                  EGL_CAST(EGLnsecsANDROID,-2)
#define LOCAL_EGL_TOP_NV                                     0x336D
#define LOCAL_EGL_TRACK_REFERENCES_KHR                       0x3352
#define LOCAL_EGL_TRANSPARENT_BLUE_VALUE                     0x3035
#define LOCAL_EGL_TRANSPARENT_GREEN_VALUE                    0x3036
#define LOCAL_EGL_TRANSPARENT_RED_VALUE                      0x3037
#define LOCAL_EGL_TRANSPARENT_RGB                            0x3052
#define LOCAL_EGL_TRANSPARENT_TYPE                           0x3034
#define LOCAL_EGL_TRIPLE_BUFFER_NV                           0x3230
#define LOCAL_EGL_TRUE                                       1
#define LOCAL_EGL_UNKNOWN                                    EGL_CAST(EGLint,-1)
#define LOCAL_EGL_UNSIGNALED                                 0x30F3
#define LOCAL_EGL_UNSIGNALED_KHR                             0x30F3
#define LOCAL_EGL_UNSIGNALED_NV                              0x30E9
#define LOCAL_EGL_UPPER_LEFT_KHR                             0x30CF
#define LOCAL_EGL_VENDOR                                     0x3053
#define LOCAL_EGL_VERSION                                    0x3054
#define LOCAL_EGL_VERTICAL_RESOLUTION                        0x3091
#define LOCAL_EGL_VG_ALPHA_FORMAT                            0x3088
#define LOCAL_EGL_VG_ALPHA_FORMAT_NONPRE                     0x308B
#define LOCAL_EGL_VG_ALPHA_FORMAT_PRE                        0x308C
#define LOCAL_EGL_VG_ALPHA_FORMAT_PRE_BIT                    0x0040
#define LOCAL_EGL_VG_ALPHA_FORMAT_PRE_BIT_KHR                0x0040
#define LOCAL_EGL_VG_COLORSPACE                              0x3087
#define LOCAL_EGL_VG_COLORSPACE_LINEAR                       0x308A
#define LOCAL_EGL_VG_COLORSPACE_LINEAR_BIT                   0x0020
#define LOCAL_EGL_VG_COLORSPACE_LINEAR_BIT_KHR               0x0020
#define LOCAL_EGL_VG_COLORSPACE_sRGB                         0x3089
#define LOCAL_EGL_VG_PARENT_IMAGE_KHR                        0x30BA
#define LOCAL_EGL_WAYLAND_BUFFER_WL                          0x31D5
#define LOCAL_EGL_WAYLAND_PLANE_WL                           0x31D6
#define LOCAL_EGL_WAYLAND_Y_INVERTED_WL                      0x31DB
#define LOCAL_EGL_WIDTH                                      0x3057
#define LOCAL_EGL_WINDOW_BIT                                 0x0004
#define LOCAL_EGL_WRITE_SURFACE_BIT_KHR                      0x0002
#define LOCAL_EGL_X_AXIS_NV                                  0x336F
#define LOCAL_EGL_YUV_BUFFER_EXT                             0x3300
#define LOCAL_EGL_YUV_CHROMA_HORIZONTAL_SITING_HINT_EXT      0x327D
#define LOCAL_EGL_YUV_CHROMA_SITING_0_5_EXT                  0x3285
#define LOCAL_EGL_YUV_CHROMA_SITING_0_EXT                    0x3284
#define LOCAL_EGL_YUV_CHROMA_VERTICAL_SITING_HINT_EXT        0x327E
#define LOCAL_EGL_YUV_COLOR_SPACE_HINT_EXT                   0x327B
#define LOCAL_EGL_YUV_CSC_STANDARD_2020_EXT                  0x330D
#define LOCAL_EGL_YUV_CSC_STANDARD_601_EXT                   0x330B
#define LOCAL_EGL_YUV_CSC_STANDARD_709_EXT                   0x330C
#define LOCAL_EGL_YUV_CSC_STANDARD_EXT                       0x330A
#define LOCAL_EGL_YUV_DEPTH_RANGE_EXT                        0x3317
#define LOCAL_EGL_YUV_DEPTH_RANGE_FULL_EXT                   0x3319
#define LOCAL_EGL_YUV_DEPTH_RANGE_LIMITED_EXT                0x3318
#define LOCAL_EGL_YUV_FULL_RANGE_EXT                         0x3282
#define LOCAL_EGL_YUV_NARROW_RANGE_EXT                       0x3283
#define LOCAL_EGL_YUV_NUMBER_OF_PLANES_EXT                   0x3311
#define LOCAL_EGL_YUV_ORDER_AYUV_EXT                         0x3308
#define LOCAL_EGL_YUV_ORDER_EXT                              0x3301
#define LOCAL_EGL_YUV_ORDER_UYVY_EXT                         0x3305
#define LOCAL_EGL_YUV_ORDER_VYUY_EXT                         0x3307
#define LOCAL_EGL_YUV_ORDER_YUV_EXT                          0x3302
#define LOCAL_EGL_YUV_ORDER_YUYV_EXT                         0x3304
#define LOCAL_EGL_YUV_ORDER_YVU_EXT                          0x3303
#define LOCAL_EGL_YUV_ORDER_YVYU_EXT                         0x3306
#define LOCAL_EGL_YUV_PLANE0_TEXTURE_UNIT_NV                 0x332C
#define LOCAL_EGL_YUV_PLANE1_TEXTURE_UNIT_NV                 0x332D
#define LOCAL_EGL_YUV_PLANE2_TEXTURE_UNIT_NV                 0x332E
#define LOCAL_EGL_YUV_PLANE_BPP_0_EXT                        0x331B
#define LOCAL_EGL_YUV_PLANE_BPP_10_EXT                       0x331D
#define LOCAL_EGL_YUV_PLANE_BPP_8_EXT                        0x331C
#define LOCAL_EGL_YUV_PLANE_BPP_EXT                          0x331A
#define LOCAL_EGL_YUV_SUBSAMPLE_4_2_0_EXT                    0x3313
#define LOCAL_EGL_YUV_SUBSAMPLE_4_2_2_EXT                    0x3314
#define LOCAL_EGL_YUV_SUBSAMPLE_4_4_4_EXT                    0x3315
#define LOCAL_EGL_YUV_SUBSAMPLE_EXT                          0x3312
#define LOCAL_EGL_Y_AXIS_NV                                  0x3370
#define LOCAL_EGL_Y_INVERTED_NOK                             0x307F

// GLX
#define LOCAL_GLX_3DFX_FULLSCREEN_MODE_MESA                  0x2
#define LOCAL_GLX_3DFX_WINDOW_MODE_MESA                      0x1
#define LOCAL_GLX_ACCUM_ALPHA_SIZE                           17
#define LOCAL_GLX_ACCUM_BLUE_SIZE                            16
#define LOCAL_GLX_ACCUM_BUFFER_BIT                           0x00000080
#define LOCAL_GLX_ACCUM_BUFFER_BIT_SGIX                      0x00000080
#define LOCAL_GLX_ACCUM_GREEN_SIZE                           15
#define LOCAL_GLX_ACCUM_RED_SIZE                             14
#define LOCAL_GLX_ALPHA_SIZE                                 11
#define LOCAL_GLX_AUX0_EXT                                   0x20E2
#define LOCAL_GLX_AUX1_EXT                                   0x20E3
#define LOCAL_GLX_AUX2_EXT                                   0x20E4
#define LOCAL_GLX_AUX3_EXT                                   0x20E5
#define LOCAL_GLX_AUX4_EXT                                   0x20E6
#define LOCAL_GLX_AUX5_EXT                                   0x20E7
#define LOCAL_GLX_AUX6_EXT                                   0x20E8
#define LOCAL_GLX_AUX7_EXT                                   0x20E9
#define LOCAL_GLX_AUX8_EXT                                   0x20EA
#define LOCAL_GLX_AUX9_EXT                                   0x20EB
#define LOCAL_GLX_AUX_BUFFERS                                7
#define LOCAL_GLX_AUX_BUFFERS_BIT                            0x00000010
#define LOCAL_GLX_AUX_BUFFERS_BIT_SGIX                       0x00000010
#define LOCAL_GLX_BACK_BUFFER_AGE_EXT                        0x20F4
#define LOCAL_GLX_BACK_EXT                                   0x20E0
#define LOCAL_GLX_BACK_LEFT_BUFFER_BIT                       0x00000004
#define LOCAL_GLX_BACK_LEFT_BUFFER_BIT_SGIX                  0x00000004
#define LOCAL_GLX_BACK_LEFT_EXT                              0x20E0
#define LOCAL_GLX_BACK_RIGHT_BUFFER_BIT                      0x00000008
#define LOCAL_GLX_BACK_RIGHT_BUFFER_BIT_SGIX                 0x00000008
#define LOCAL_GLX_BACK_RIGHT_EXT                             0x20E1
#define LOCAL_GLX_BAD_ATTRIBUTE                              2
#define LOCAL_GLX_BAD_CONTEXT                                5
#define LOCAL_GLX_BAD_ENUM                                   7
#define LOCAL_GLX_BAD_HYPERPIPE_CONFIG_SGIX                  91
#define LOCAL_GLX_BAD_HYPERPIPE_SGIX                         92
#define LOCAL_GLX_BAD_SCREEN                                 1
#define LOCAL_GLX_BAD_VALUE                                  6
#define LOCAL_GLX_BAD_VISUAL                                 4
#define LOCAL_GLX_BIND_TO_MIPMAP_TEXTURE_EXT                 0x20D2
#define LOCAL_GLX_BIND_TO_TEXTURE_RGBA_EXT                   0x20D1
#define LOCAL_GLX_BIND_TO_TEXTURE_RGB_EXT                    0x20D0
#define LOCAL_GLX_BIND_TO_TEXTURE_TARGETS_EXT                0x20D3
#define LOCAL_GLX_BLENDED_RGBA_SGIS                          0x8025
#define LOCAL_GLX_BLUE_SIZE                                  10
#define LOCAL_GLX_BUFFER_CLOBBER_MASK_SGIX                   0x08000000
#define LOCAL_GLX_BUFFER_SIZE                                2
#define LOCAL_GLX_BUFFER_SWAP_COMPLETE_INTEL_MASK            0x04000000
#define LOCAL_GLX_BufferSwapComplete                         1
#define LOCAL_GLX_COLOR_INDEX_BIT                            0x00000002
#define LOCAL_GLX_COLOR_INDEX_BIT_SGIX                       0x00000002
#define LOCAL_GLX_COLOR_INDEX_TYPE                           0x8015
#define LOCAL_GLX_COLOR_INDEX_TYPE_SGIX                      0x8015
#define LOCAL_GLX_COLOR_SAMPLES_NV                           0x20B3
#define LOCAL_GLX_CONFIG_CAVEAT                              0x20
#define LOCAL_GLX_CONTEXT_ALLOW_BUFFER_BYTE_ORDER_MISMATCH_ARB       0x2095
#define LOCAL_GLX_CONTEXT_COMPATIBILITY_PROFILE_BIT_ARB      0x00000002
#define LOCAL_GLX_CONTEXT_CORE_PROFILE_BIT_ARB               0x00000001
#define LOCAL_GLX_CONTEXT_DEBUG_BIT_ARB                      0x00000001
#define LOCAL_GLX_CONTEXT_ES2_PROFILE_BIT_EXT                0x00000004
#define LOCAL_GLX_CONTEXT_ES_PROFILE_BIT_EXT                 0x00000004
#define LOCAL_GLX_CONTEXT_FLAGS_ARB                          0x2094
#define LOCAL_GLX_CONTEXT_FORWARD_COMPATIBLE_BIT_ARB         0x00000002
#define LOCAL_GLX_CONTEXT_MAJOR_VERSION_ARB                  0x2091
#define LOCAL_GLX_CONTEXT_MINOR_VERSION_ARB                  0x2092
#define LOCAL_GLX_CONTEXT_MULTIGPU_ATTRIB_AFR_NV             0x20AC
#define LOCAL_GLX_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV       0x20AD
#define LOCAL_GLX_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV 0x20AE
#define LOCAL_GLX_CONTEXT_MULTIGPU_ATTRIB_NV                 0x20AA
#define LOCAL_GLX_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV          0x20AB
#define LOCAL_GLX_CONTEXT_OPENGL_NO_ERROR_ARB                0x31B3
#define LOCAL_GLX_CONTEXT_PRIORITY_HIGH_EXT                  0x3101
#define LOCAL_GLX_CONTEXT_PRIORITY_LEVEL_EXT                 0x3100
#define LOCAL_GLX_CONTEXT_PRIORITY_LOW_EXT                   0x3103
#define LOCAL_GLX_CONTEXT_PRIORITY_MEDIUM_EXT                0x3102
#define LOCAL_GLX_CONTEXT_PROFILE_MASK_ARB                   0x9126
#define LOCAL_GLX_CONTEXT_RELEASE_BEHAVIOR_ARB               0x2097
#define LOCAL_GLX_CONTEXT_RELEASE_BEHAVIOR_FLUSH_ARB         0x2098
#define LOCAL_GLX_CONTEXT_RELEASE_BEHAVIOR_NONE_ARB          0
#define LOCAL_GLX_CONTEXT_RESET_ISOLATION_BIT_ARB            0x00000008
#define LOCAL_GLX_CONTEXT_RESET_NOTIFICATION_STRATEGY_ARB    0x8256
#define LOCAL_GLX_CONTEXT_ROBUST_ACCESS_BIT_ARB              0x00000004
#define LOCAL_GLX_COPY_COMPLETE_INTEL                        0x8181
#define LOCAL_GLX_COVERAGE_SAMPLES_NV                        100001
#define LOCAL_GLX_DAMAGED                                    0x8020
#define LOCAL_GLX_DAMAGED_SGIX                               0x8020
#define LOCAL_GLX_DEPTH_BUFFER_BIT                           0x00000020
#define LOCAL_GLX_DEPTH_BUFFER_BIT_SGIX                      0x00000020
#define LOCAL_GLX_DEPTH_SIZE                                 12
#define LOCAL_GLX_DEVICE_ID_NV                               0x20CD
#define LOCAL_GLX_DIGITAL_MEDIA_PBUFFER_SGIX                 0x8024
#define LOCAL_GLX_DIRECT_COLOR                               0x8003
#define LOCAL_GLX_DIRECT_COLOR_EXT                           0x8003
#define LOCAL_GLX_DONT_CARE                                  0xFFFFFFFF
#define LOCAL_GLX_DOUBLEBUFFER                               5
#define LOCAL_GLX_DRAWABLE_TYPE                              0x8010
#define LOCAL_GLX_DRAWABLE_TYPE_SGIX                         0x8010
#define LOCAL_GLX_EVENT_MASK                                 0x801F
#define LOCAL_GLX_EVENT_MASK_SGIX                            0x801F
#define LOCAL_GLX_EXCHANGE_COMPLETE_INTEL                    0x8180
#define LOCAL_GLX_EXTENSIONS                                 0x3
#define LOCAL_GLX_EXTENSION_NAME                             "GLX"
#define LOCAL_GLX_FBCONFIG_ID                                0x8013
#define LOCAL_GLX_FBCONFIG_ID_SGIX                           0x8013
#define LOCAL_GLX_FLIP_COMPLETE_INTEL                        0x8182
#define LOCAL_GLX_FLOAT_COMPONENTS_NV                        0x20B0
#define LOCAL_GLX_FRAMEBUFFER_SRGB_CAPABLE_ARB               0x20B2
#define LOCAL_GLX_FRAMEBUFFER_SRGB_CAPABLE_EXT               0x20B2
#define LOCAL_GLX_FRONT_EXT                                  0x20DE
#define LOCAL_GLX_FRONT_LEFT_BUFFER_BIT                      0x00000001
#define LOCAL_GLX_FRONT_LEFT_BUFFER_BIT_SGIX                 0x00000001
#define LOCAL_GLX_FRONT_LEFT_EXT                             0x20DE
#define LOCAL_GLX_FRONT_RIGHT_BUFFER_BIT                     0x00000002
#define LOCAL_GLX_FRONT_RIGHT_BUFFER_BIT_SGIX                0x00000002
#define LOCAL_GLX_FRONT_RIGHT_EXT                            0x20DF
#define LOCAL_GLX_GENERATE_RESET_ON_VIDEO_MEMORY_PURGE_NV    0x20F7
#define LOCAL_GLX_GPU_CLOCK_AMD                              0x21A4
#define LOCAL_GLX_GPU_FASTEST_TARGET_GPUS_AMD                0x21A2
#define LOCAL_GLX_GPU_NUM_PIPES_AMD                          0x21A5
#define LOCAL_GLX_GPU_NUM_RB_AMD                             0x21A7
#define LOCAL_GLX_GPU_NUM_SIMD_AMD                           0x21A6
#define LOCAL_GLX_GPU_NUM_SPI_AMD                            0x21A8
#define LOCAL_GLX_GPU_OPENGL_VERSION_STRING_AMD              0x1F02
#define LOCAL_GLX_GPU_RAM_AMD                                0x21A3
#define LOCAL_GLX_GPU_RENDERER_STRING_AMD                    0x1F01
#define LOCAL_GLX_GPU_VENDOR_AMD                             0x1F00
#define LOCAL_GLX_GRAY_SCALE                                 0x8006
#define LOCAL_GLX_GRAY_SCALE_EXT                             0x8006
#define LOCAL_GLX_GREEN_SIZE                                 9
#define LOCAL_GLX_HEIGHT                                     0x801E
#define LOCAL_GLX_HEIGHT_SGIX                                0x801E
#define LOCAL_GLX_HYPERPIPE_DISPLAY_PIPE_SGIX                0x00000001
#define LOCAL_GLX_HYPERPIPE_ID_SGIX                          0x8030
#define LOCAL_GLX_HYPERPIPE_PIPE_NAME_LENGTH_SGIX            80
#define LOCAL_GLX_HYPERPIPE_PIXEL_AVERAGE_SGIX               0x00000004
#define LOCAL_GLX_HYPERPIPE_RENDER_PIPE_SGIX                 0x00000002
#define LOCAL_GLX_HYPERPIPE_STEREO_SGIX                      0x00000003
#define LOCAL_GLX_LARGEST_PBUFFER                            0x801C
#define LOCAL_GLX_LARGEST_PBUFFER_SGIX                       0x801C
#define LOCAL_GLX_LATE_SWAPS_TEAR_EXT                        0x20F3
#define LOCAL_GLX_LEVEL                                      3
#define LOCAL_GLX_LOSE_CONTEXT_ON_RESET_ARB                  0x8252
#define LOCAL_GLX_MAX_PBUFFER_HEIGHT                         0x8017
#define LOCAL_GLX_MAX_PBUFFER_HEIGHT_SGIX                    0x8017
#define LOCAL_GLX_MAX_PBUFFER_PIXELS                         0x8018
#define LOCAL_GLX_MAX_PBUFFER_PIXELS_SGIX                    0x8018
#define LOCAL_GLX_MAX_PBUFFER_WIDTH                          0x8016
#define LOCAL_GLX_MAX_PBUFFER_WIDTH_SGIX                     0x8016
#define LOCAL_GLX_MAX_SWAP_INTERVAL_EXT                      0x20F2
#define LOCAL_GLX_MIPMAP_TEXTURE_EXT                         0x20D7
#define LOCAL_GLX_MULTISAMPLE_SUB_RECT_HEIGHT_SGIS           0x8027
#define LOCAL_GLX_MULTISAMPLE_SUB_RECT_WIDTH_SGIS            0x8026
#define LOCAL_GLX_NONE                                       0x8000
#define LOCAL_GLX_NONE_EXT                                   0x8000
#define LOCAL_GLX_NON_CONFORMANT_CONFIG                      0x800D
#define LOCAL_GLX_NON_CONFORMANT_VISUAL_EXT                  0x800D
#define LOCAL_GLX_NO_EXTENSION                               3
#define LOCAL_GLX_NO_RESET_NOTIFICATION_ARB                  0x8261
#define LOCAL_GLX_NUM_VIDEO_CAPTURE_SLOTS_NV                 0x20CF
#define LOCAL_GLX_NUM_VIDEO_SLOTS_NV                         0x20F0
#define LOCAL_GLX_OPTIMAL_PBUFFER_HEIGHT_SGIX                0x801A
#define LOCAL_GLX_OPTIMAL_PBUFFER_WIDTH_SGIX                 0x8019
#define LOCAL_GLX_PBUFFER                                    0x8023
#define LOCAL_GLX_PBUFFER_BIT                                0x00000004
#define LOCAL_GLX_PBUFFER_BIT_SGIX                           0x00000004
#define LOCAL_GLX_PBUFFER_CLOBBER_MASK                       0x08000000
#define LOCAL_GLX_PBUFFER_HEIGHT                             0x8040
#define LOCAL_GLX_PBUFFER_SGIX                               0x8023
#define LOCAL_GLX_PBUFFER_WIDTH                              0x8041
#define LOCAL_GLX_PIPE_RECT_LIMITS_SGIX                      0x00000002
#define LOCAL_GLX_PIPE_RECT_SGIX                             0x00000001
#define LOCAL_GLX_PIXMAP_BIT                                 0x00000002
#define LOCAL_GLX_PIXMAP_BIT_SGIX                            0x00000002
#define LOCAL_GLX_PRESERVED_CONTENTS                         0x801B
#define LOCAL_GLX_PRESERVED_CONTENTS_SGIX                    0x801B
#define LOCAL_GLX_PSEUDO_COLOR                               0x8004
#define LOCAL_GLX_PSEUDO_COLOR_EXT                           0x8004
#define LOCAL_GLX_PbufferClobber                             0
#define LOCAL_GLX_RED_SIZE                                   8
#define LOCAL_GLX_RENDERER_ACCELERATED_MESA                  0x8186
#define LOCAL_GLX_RENDERER_DEVICE_ID_MESA                    0x8184
#define LOCAL_GLX_RENDERER_OPENGL_COMPATIBILITY_PROFILE_VERSION_MESA 0x818B
#define LOCAL_GLX_RENDERER_OPENGL_CORE_PROFILE_VERSION_MESA  0x818A
#define LOCAL_GLX_RENDERER_OPENGL_ES2_PROFILE_VERSION_MESA   0x818D
#define LOCAL_GLX_RENDERER_OPENGL_ES_PROFILE_VERSION_MESA    0x818C
#define LOCAL_GLX_RENDERER_PREFERRED_PROFILE_MESA            0x8189
#define LOCAL_GLX_RENDERER_UNIFIED_MEMORY_ARCHITECTURE_MESA  0x8188
#define LOCAL_GLX_RENDERER_VENDOR_ID_MESA                    0x8183
#define LOCAL_GLX_RENDERER_VERSION_MESA                      0x8185
#define LOCAL_GLX_RENDERER_VIDEO_MEMORY_MESA                 0x8187
#define LOCAL_GLX_RENDER_TYPE                                0x8011
#define LOCAL_GLX_RENDER_TYPE_SGIX                           0x8011
#define LOCAL_GLX_RGBA                                       4
#define LOCAL_GLX_RGBA_BIT                                   0x00000001
#define LOCAL_GLX_RGBA_BIT_SGIX                              0x00000001
#define LOCAL_GLX_RGBA_FLOAT_BIT_ARB                         0x00000004
#define LOCAL_GLX_RGBA_FLOAT_TYPE_ARB                        0x20B9
#define LOCAL_GLX_RGBA_TYPE                                  0x8014
#define LOCAL_GLX_RGBA_TYPE_SGIX                             0x8014
#define LOCAL_GLX_RGBA_UNSIGNED_FLOAT_BIT_EXT                0x00000008
#define LOCAL_GLX_RGBA_UNSIGNED_FLOAT_TYPE_EXT               0x20B1
#define LOCAL_GLX_SAMPLES                                    100001
#define LOCAL_GLX_SAMPLES_3DFX                               0x8051
#define LOCAL_GLX_SAMPLES_ARB                                100001
#define LOCAL_GLX_SAMPLES_SGIS                               100001
#define LOCAL_GLX_SAMPLE_BUFFERS                             100000
#define LOCAL_GLX_SAMPLE_BUFFERS_3DFX                        0x8050
#define LOCAL_GLX_SAMPLE_BUFFERS_ARB                         100000
#define LOCAL_GLX_SAMPLE_BUFFERS_BIT_SGIX                    0x00000100
#define LOCAL_GLX_SAMPLE_BUFFERS_SGIS                        100000
#define LOCAL_GLX_SAVED                                      0x8021
#define LOCAL_GLX_SAVED_SGIX                                 0x8021
#define LOCAL_GLX_SCREEN                                     0x800C
#define LOCAL_GLX_SCREEN_EXT                                 0x800C
#define LOCAL_GLX_SHARE_CONTEXT_EXT                          0x800A
#define LOCAL_GLX_SLOW_CONFIG                                0x8001
#define LOCAL_GLX_SLOW_VISUAL_EXT                            0x8001
#define LOCAL_GLX_STATIC_COLOR                               0x8005
#define LOCAL_GLX_STATIC_COLOR_EXT                           0x8005
#define LOCAL_GLX_STATIC_GRAY                                0x8007
#define LOCAL_GLX_STATIC_GRAY_EXT                            0x8007
#define LOCAL_GLX_STENCIL_BUFFER_BIT                         0x00000040
#define LOCAL_GLX_STENCIL_BUFFER_BIT_SGIX                    0x00000040
#define LOCAL_GLX_STENCIL_SIZE                               13
#define LOCAL_GLX_STEREO                                     6
#define LOCAL_GLX_STEREO_NOTIFY_EXT                          0x00000000
#define LOCAL_GLX_STEREO_NOTIFY_MASK_EXT                     0x00000001
#define LOCAL_GLX_STEREO_TREE_EXT                            0x20F5
#define LOCAL_GLX_SWAP_COPY_OML                              0x8062
#define LOCAL_GLX_SWAP_EXCHANGE_OML                          0x8061
#define LOCAL_GLX_SWAP_INTERVAL_EXT                          0x20F1
#define LOCAL_GLX_SWAP_METHOD_OML                            0x8060
#define LOCAL_GLX_SWAP_UNDEFINED_OML                         0x8063
#define LOCAL_GLX_SYNC_FRAME_SGIX                            0x00000000
#define LOCAL_GLX_SYNC_SWAP_SGIX                             0x00000001
#define LOCAL_GLX_TEXTURE_1D_BIT_EXT                         0x00000001
#define LOCAL_GLX_TEXTURE_1D_EXT                             0x20DB
#define LOCAL_GLX_TEXTURE_2D_BIT_EXT                         0x00000002
#define LOCAL_GLX_TEXTURE_2D_EXT                             0x20DC
#define LOCAL_GLX_TEXTURE_FORMAT_EXT                         0x20D5
#define LOCAL_GLX_TEXTURE_FORMAT_NONE_EXT                    0x20D8
#define LOCAL_GLX_TEXTURE_FORMAT_RGBA_EXT                    0x20DA
#define LOCAL_GLX_TEXTURE_FORMAT_RGB_EXT                     0x20D9
#define LOCAL_GLX_TEXTURE_RECTANGLE_BIT_EXT                  0x00000004
#define LOCAL_GLX_TEXTURE_RECTANGLE_EXT                      0x20DD
#define LOCAL_GLX_TEXTURE_TARGET_EXT                         0x20D6
#define LOCAL_GLX_TRANSPARENT_ALPHA_VALUE                    0x28
#define LOCAL_GLX_TRANSPARENT_ALPHA_VALUE_EXT                0x28
#define LOCAL_GLX_TRANSPARENT_BLUE_VALUE                     0x27
#define LOCAL_GLX_TRANSPARENT_BLUE_VALUE_EXT                 0x27
#define LOCAL_GLX_TRANSPARENT_GREEN_VALUE                    0x26
#define LOCAL_GLX_TRANSPARENT_GREEN_VALUE_EXT                0x26
#define LOCAL_GLX_TRANSPARENT_INDEX                          0x8009
#define LOCAL_GLX_TRANSPARENT_INDEX_EXT                      0x8009
#define LOCAL_GLX_TRANSPARENT_INDEX_VALUE                    0x24
#define LOCAL_GLX_TRANSPARENT_INDEX_VALUE_EXT                0x24
#define LOCAL_GLX_TRANSPARENT_RED_VALUE                      0x25
#define LOCAL_GLX_TRANSPARENT_RED_VALUE_EXT                  0x25
#define LOCAL_GLX_TRANSPARENT_RGB                            0x8008
#define LOCAL_GLX_TRANSPARENT_RGB_EXT                        0x8008
#define LOCAL_GLX_TRANSPARENT_TYPE                           0x23
#define LOCAL_GLX_TRANSPARENT_TYPE_EXT                       0x23
#define LOCAL_GLX_TRUE_COLOR                                 0x8002
#define LOCAL_GLX_TRUE_COLOR_EXT                             0x8002
#define LOCAL_GLX_UNIQUE_ID_NV                               0x20CE
#define LOCAL_GLX_USE_GL                                     1
#define LOCAL_GLX_VENDOR                                     0x1
#define LOCAL_GLX_VENDOR_NAMES_EXT                           0x20F6
#define LOCAL_GLX_VERSION                                    0x2
#define LOCAL_GLX_VIDEO_OUT_ALPHA_NV                         0x20C4
#define LOCAL_GLX_VIDEO_OUT_COLOR_AND_ALPHA_NV               0x20C6
#define LOCAL_GLX_VIDEO_OUT_COLOR_AND_DEPTH_NV               0x20C7
#define LOCAL_GLX_VIDEO_OUT_COLOR_NV                         0x20C3
#define LOCAL_GLX_VIDEO_OUT_DEPTH_NV                         0x20C5
#define LOCAL_GLX_VIDEO_OUT_FIELD_1_NV                       0x20C9
#define LOCAL_GLX_VIDEO_OUT_FIELD_2_NV                       0x20CA
#define LOCAL_GLX_VIDEO_OUT_FRAME_NV                         0x20C8
#define LOCAL_GLX_VIDEO_OUT_STACKED_FIELDS_1_2_NV            0x20CB
#define LOCAL_GLX_VIDEO_OUT_STACKED_FIELDS_2_1_NV            0x20CC
#define LOCAL_GLX_VISUAL_CAVEAT_EXT                          0x20
#define LOCAL_GLX_VISUAL_ID                                  0x800B
#define LOCAL_GLX_VISUAL_ID_EXT                              0x800B
#define LOCAL_GLX_VISUAL_SELECT_GROUP_SGIX                   0x8028
#define LOCAL_GLX_WIDTH                                      0x801D
#define LOCAL_GLX_WIDTH_SGIX                                 0x801D
#define LOCAL_GLX_WINDOW                                     0x8022
#define LOCAL_GLX_WINDOW_BIT                                 0x00000001
#define LOCAL_GLX_WINDOW_BIT_SGIX                            0x00000001
#define LOCAL_GLX_WINDOW_SGIX                                0x8022
#define LOCAL_GLX_X_RENDERABLE                               0x8012
#define LOCAL_GLX_X_RENDERABLE_SGIX                          0x8012
#define LOCAL_GLX_X_VISUAL_TYPE                              0x22
#define LOCAL_GLX_X_VISUAL_TYPE_EXT                          0x22
#define LOCAL_GLX_Y_INVERTED_EXT                             0x20D4

// WGL
#define LOCAL_WGL_ACCELERATION_ARB                           0x2003
#define LOCAL_WGL_ACCELERATION_EXT                           0x2003
#define LOCAL_WGL_ACCESS_READ_ONLY_NV                        0x00000000
#define LOCAL_WGL_ACCESS_READ_WRITE_NV                       0x00000001
#define LOCAL_WGL_ACCESS_WRITE_DISCARD_NV                    0x00000002
#define LOCAL_WGL_ACCUM_ALPHA_BITS_ARB                       0x2021
#define LOCAL_WGL_ACCUM_ALPHA_BITS_EXT                       0x2021
#define LOCAL_WGL_ACCUM_BITS_ARB                             0x201D
#define LOCAL_WGL_ACCUM_BITS_EXT                             0x201D
#define LOCAL_WGL_ACCUM_BLUE_BITS_ARB                        0x2020
#define LOCAL_WGL_ACCUM_BLUE_BITS_EXT                        0x2020
#define LOCAL_WGL_ACCUM_GREEN_BITS_ARB                       0x201F
#define LOCAL_WGL_ACCUM_GREEN_BITS_EXT                       0x201F
#define LOCAL_WGL_ACCUM_RED_BITS_ARB                         0x201E
#define LOCAL_WGL_ACCUM_RED_BITS_EXT                         0x201E
#define LOCAL_WGL_ALPHA_BITS_ARB                             0x201B
#define LOCAL_WGL_ALPHA_BITS_EXT                             0x201B
#define LOCAL_WGL_ALPHA_SHIFT_ARB                            0x201C
#define LOCAL_WGL_ALPHA_SHIFT_EXT                            0x201C
#define LOCAL_WGL_AUX0_ARB                                   0x2087
#define LOCAL_WGL_AUX1_ARB                                   0x2088
#define LOCAL_WGL_AUX2_ARB                                   0x2089
#define LOCAL_WGL_AUX3_ARB                                   0x208A
#define LOCAL_WGL_AUX4_ARB                                   0x208B
#define LOCAL_WGL_AUX5_ARB                                   0x208C
#define LOCAL_WGL_AUX6_ARB                                   0x208D
#define LOCAL_WGL_AUX7_ARB                                   0x208E
#define LOCAL_WGL_AUX8_ARB                                   0x208F
#define LOCAL_WGL_AUX9_ARB                                   0x2090
#define LOCAL_WGL_AUX_BUFFERS_ARB                            0x2024
#define LOCAL_WGL_AUX_BUFFERS_EXT                            0x2024
#define LOCAL_WGL_BACK_COLOR_BUFFER_BIT_ARB                  0x00000002
#define LOCAL_WGL_BACK_LEFT_ARB                              0x2085
#define LOCAL_WGL_BACK_RIGHT_ARB                             0x2086
#define LOCAL_WGL_BIND_TO_TEXTURE_DEPTH_NV                   0x20A3
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV         0x20A4
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGBA_NV    0x20B4
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RGB_NV     0x20B3
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_RG_NV      0x20B2
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_FLOAT_R_NV       0x20B1
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_RGBA_NV          0x20A1
#define LOCAL_WGL_BIND_TO_TEXTURE_RECTANGLE_RGB_NV           0x20A0
#define LOCAL_WGL_BIND_TO_TEXTURE_RGBA_ARB                   0x2071
#define LOCAL_WGL_BIND_TO_TEXTURE_RGB_ARB                    0x2070
#define LOCAL_WGL_BIND_TO_VIDEO_RGBA_NV                      0x20C1
#define LOCAL_WGL_BIND_TO_VIDEO_RGB_AND_DEPTH_NV             0x20C2
#define LOCAL_WGL_BIND_TO_VIDEO_RGB_NV                       0x20C0
#define LOCAL_WGL_BLUE_BITS_ARB                              0x2019
#define LOCAL_WGL_BLUE_BITS_EXT                              0x2019
#define LOCAL_WGL_BLUE_SHIFT_ARB                             0x201A
#define LOCAL_WGL_BLUE_SHIFT_EXT                             0x201A
#define LOCAL_WGL_COLORSPACE_EXT                             0x309D
#define LOCAL_WGL_COLORSPACE_LINEAR_EXT                      0x308A
#define LOCAL_WGL_COLORSPACE_SRGB_EXT                        0x3089
#define LOCAL_WGL_COLOR_BITS_ARB                             0x2014
#define LOCAL_WGL_COLOR_BITS_EXT                             0x2014
#define LOCAL_WGL_COLOR_SAMPLES_NV                           0x20B9
#define LOCAL_WGL_CONTEXT_COMPATIBILITY_PROFILE_BIT_ARB      0x00000002
#define LOCAL_WGL_CONTEXT_CORE_PROFILE_BIT_ARB               0x00000001
#define LOCAL_WGL_CONTEXT_DEBUG_BIT_ARB                      0x00000001
#define LOCAL_WGL_CONTEXT_ES2_PROFILE_BIT_EXT                0x00000004
#define LOCAL_WGL_CONTEXT_ES_PROFILE_BIT_EXT                 0x00000004
#define LOCAL_WGL_CONTEXT_FLAGS_ARB                          0x2094
#define LOCAL_WGL_CONTEXT_FORWARD_COMPATIBLE_BIT_ARB         0x00000002
#define LOCAL_WGL_CONTEXT_LAYER_PLANE_ARB                    0x2093
#define LOCAL_WGL_CONTEXT_MAJOR_VERSION_ARB                  0x2091
#define LOCAL_WGL_CONTEXT_MINOR_VERSION_ARB                  0x2092
#define LOCAL_WGL_CONTEXT_MULTIGPU_ATTRIB_AFR_NV             0x20AC
#define LOCAL_WGL_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV       0x20AD
#define LOCAL_WGL_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV 0x20AE
#define LOCAL_WGL_CONTEXT_MULTIGPU_ATTRIB_NV                 0x20AA
#define LOCAL_WGL_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV          0x20AB
#define LOCAL_WGL_CONTEXT_OPENGL_NO_ERROR_ARB                0x31B3
#define LOCAL_WGL_CONTEXT_PROFILE_MASK_ARB                   0x9126
#define LOCAL_WGL_CONTEXT_RELEASE_BEHAVIOR_ARB               0x2097
#define LOCAL_WGL_CONTEXT_RELEASE_BEHAVIOR_FLUSH_ARB         0x2098
#define LOCAL_WGL_CONTEXT_RELEASE_BEHAVIOR_NONE_ARB          0
#define LOCAL_WGL_CONTEXT_RESET_ISOLATION_BIT_ARB            0x00000008
#define LOCAL_WGL_CONTEXT_RESET_NOTIFICATION_STRATEGY_ARB    0x8256
#define LOCAL_WGL_CONTEXT_ROBUST_ACCESS_BIT_ARB              0x00000004
#define LOCAL_WGL_COVERAGE_SAMPLES_NV                        0x2042
#define LOCAL_WGL_CUBE_MAP_FACE_ARB                          0x207C
#define LOCAL_WGL_DEPTH_BITS_ARB                             0x2022
#define LOCAL_WGL_DEPTH_BITS_EXT                             0x2022
#define LOCAL_WGL_DEPTH_BUFFER_BIT_ARB                       0x00000004
#define LOCAL_WGL_DEPTH_COMPONENT_NV                         0x20A7
#define LOCAL_WGL_DEPTH_FLOAT_EXT                            0x2040
#define LOCAL_WGL_DEPTH_TEXTURE_FORMAT_NV                    0x20A5
#define LOCAL_WGL_DIGITAL_VIDEO_CURSOR_ALPHA_FRAMEBUFFER_I3D 0x2050
#define LOCAL_WGL_DIGITAL_VIDEO_CURSOR_ALPHA_VALUE_I3D       0x2051
#define LOCAL_WGL_DIGITAL_VIDEO_CURSOR_INCLUDED_I3D          0x2052
#define LOCAL_WGL_DIGITAL_VIDEO_GAMMA_CORRECTED_I3D          0x2053
#define LOCAL_WGL_DOUBLE_BUFFER_ARB                          0x2011
#define LOCAL_WGL_DOUBLE_BUFFER_EXT                          0x2011
#define LOCAL_WGL_DRAW_TO_BITMAP_ARB                         0x2002
#define LOCAL_WGL_DRAW_TO_BITMAP_EXT                         0x2002
#define LOCAL_WGL_DRAW_TO_PBUFFER_ARB                        0x202D
#define LOCAL_WGL_DRAW_TO_PBUFFER_EXT                        0x202D
#define LOCAL_WGL_DRAW_TO_WINDOW_ARB                         0x2001
#define LOCAL_WGL_DRAW_TO_WINDOW_EXT                         0x2001
#define LOCAL_WGL_FLOAT_COMPONENTS_NV                        0x20B0
#define LOCAL_WGL_FONT_LINES                                 0
#define LOCAL_WGL_FONT_POLYGONS                              1
#define LOCAL_WGL_FRAMEBUFFER_SRGB_CAPABLE_ARB               0x20A9
#define LOCAL_WGL_FRAMEBUFFER_SRGB_CAPABLE_EXT               0x20A9
#define LOCAL_WGL_FRONT_COLOR_BUFFER_BIT_ARB                 0x00000001
#define LOCAL_WGL_FRONT_LEFT_ARB                             0x2083
#define LOCAL_WGL_FRONT_RIGHT_ARB                            0x2084
#define LOCAL_WGL_FULL_ACCELERATION_ARB                      0x2027
#define LOCAL_WGL_FULL_ACCELERATION_EXT                      0x2027
#define LOCAL_WGL_GAMMA_EXCLUDE_DESKTOP_I3D                  0x204F
#define LOCAL_WGL_GAMMA_TABLE_SIZE_I3D                       0x204E
#define LOCAL_WGL_GENERIC_ACCELERATION_ARB                   0x2026
#define LOCAL_WGL_GENERIC_ACCELERATION_EXT                   0x2026
#define LOCAL_WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D           0x2049
#define LOCAL_WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D            0x2048
#define LOCAL_WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D               0x204C
#define LOCAL_WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D            0x204A
#define LOCAL_WGL_GENLOCK_SOURCE_EDGE_RISING_I3D             0x204B
#define LOCAL_WGL_GENLOCK_SOURCE_EXTERNAL_FIELD_I3D          0x2046
#define LOCAL_WGL_GENLOCK_SOURCE_EXTERNAL_SYNC_I3D           0x2045
#define LOCAL_WGL_GENLOCK_SOURCE_EXTERNAL_TTL_I3D            0x2047
#define LOCAL_WGL_GENLOCK_SOURCE_MULTIVIEW_I3D               0x2044
#define LOCAL_WGL_GPU_CLOCK_AMD                              0x21A4
#define LOCAL_WGL_GPU_FASTEST_TARGET_GPUS_AMD                0x21A2
#define LOCAL_WGL_GPU_NUM_PIPES_AMD                          0x21A5
#define LOCAL_WGL_GPU_NUM_RB_AMD                             0x21A7
#define LOCAL_WGL_GPU_NUM_SIMD_AMD                           0x21A6
#define LOCAL_WGL_GPU_NUM_SPI_AMD                            0x21A8
#define LOCAL_WGL_GPU_OPENGL_VERSION_STRING_AMD              0x1F02
#define LOCAL_WGL_GPU_RAM_AMD                                0x21A3
#define LOCAL_WGL_GPU_RENDERER_STRING_AMD                    0x1F01
#define LOCAL_WGL_GPU_VENDOR_AMD                             0x1F00
#define LOCAL_WGL_GREEN_BITS_ARB                             0x2017
#define LOCAL_WGL_GREEN_BITS_EXT                             0x2017
#define LOCAL_WGL_GREEN_SHIFT_ARB                            0x2018
#define LOCAL_WGL_GREEN_SHIFT_EXT                            0x2018
#define LOCAL_WGL_IMAGE_BUFFER_LOCK_I3D                      0x00000002
#define LOCAL_WGL_IMAGE_BUFFER_MIN_ACCESS_I3D                0x00000001
#define LOCAL_WGL_LOSE_CONTEXT_ON_RESET_ARB                  0x8252
#define LOCAL_WGL_MAX_PBUFFER_HEIGHT_ARB                     0x2030
#define LOCAL_WGL_MAX_PBUFFER_HEIGHT_EXT                     0x2030
#define LOCAL_WGL_MAX_PBUFFER_PIXELS_ARB                     0x202E
#define LOCAL_WGL_MAX_PBUFFER_PIXELS_EXT                     0x202E
#define LOCAL_WGL_MAX_PBUFFER_WIDTH_ARB                      0x202F
#define LOCAL_WGL_MAX_PBUFFER_WIDTH_EXT                      0x202F
#define LOCAL_WGL_MIPMAP_LEVEL_ARB                           0x207B
#define LOCAL_WGL_MIPMAP_TEXTURE_ARB                         0x2074
#define LOCAL_WGL_NEED_PALETTE_ARB                           0x2004
#define LOCAL_WGL_NEED_PALETTE_EXT                           0x2004
#define LOCAL_WGL_NEED_SYSTEM_PALETTE_ARB                    0x2005
#define LOCAL_WGL_NEED_SYSTEM_PALETTE_EXT                    0x2005
#define LOCAL_WGL_NO_ACCELERATION_ARB                        0x2025
#define LOCAL_WGL_NO_ACCELERATION_EXT                        0x2025
#define LOCAL_WGL_NO_RESET_NOTIFICATION_ARB                  0x8261
#define LOCAL_WGL_NO_TEXTURE_ARB                             0x2077
#define LOCAL_WGL_NUMBER_OVERLAYS_ARB                        0x2008
#define LOCAL_WGL_NUMBER_OVERLAYS_EXT                        0x2008
#define LOCAL_WGL_NUMBER_PIXEL_FORMATS_ARB                   0x2000
#define LOCAL_WGL_NUMBER_PIXEL_FORMATS_EXT                   0x2000
#define LOCAL_WGL_NUMBER_UNDERLAYS_ARB                       0x2009
#define LOCAL_WGL_NUMBER_UNDERLAYS_EXT                       0x2009
#define LOCAL_WGL_NUM_VIDEO_CAPTURE_SLOTS_NV                 0x20CF
#define LOCAL_WGL_NUM_VIDEO_SLOTS_NV                         0x20F0
#define LOCAL_WGL_OPTIMAL_PBUFFER_HEIGHT_EXT                 0x2032
#define LOCAL_WGL_OPTIMAL_PBUFFER_WIDTH_EXT                  0x2031
#define LOCAL_WGL_PBUFFER_HEIGHT_ARB                         0x2035
#define LOCAL_WGL_PBUFFER_HEIGHT_EXT                         0x2035
#define LOCAL_WGL_PBUFFER_LARGEST_ARB                        0x2033
#define LOCAL_WGL_PBUFFER_LARGEST_EXT                        0x2033
#define LOCAL_WGL_PBUFFER_LOST_ARB                           0x2036
#define LOCAL_WGL_PBUFFER_WIDTH_ARB                          0x2034
#define LOCAL_WGL_PBUFFER_WIDTH_EXT                          0x2034
#define LOCAL_WGL_PIXEL_TYPE_ARB                             0x2013
#define LOCAL_WGL_PIXEL_TYPE_EXT                             0x2013
#define LOCAL_WGL_RED_BITS_ARB                               0x2015
#define LOCAL_WGL_RED_BITS_EXT                               0x2015
#define LOCAL_WGL_RED_SHIFT_ARB                              0x2016
#define LOCAL_WGL_RED_SHIFT_EXT                              0x2016
#define LOCAL_WGL_SAMPLES_3DFX                               0x2061
#define LOCAL_WGL_SAMPLES_ARB                                0x2042
#define LOCAL_WGL_SAMPLES_EXT                                0x2042
#define LOCAL_WGL_SAMPLE_BUFFERS_3DFX                        0x2060
#define LOCAL_WGL_SAMPLE_BUFFERS_ARB                         0x2041
#define LOCAL_WGL_SAMPLE_BUFFERS_EXT                         0x2041
#define LOCAL_WGL_SHARE_ACCUM_ARB                            0x200E
#define LOCAL_WGL_SHARE_ACCUM_EXT                            0x200E
#define LOCAL_WGL_SHARE_DEPTH_ARB                            0x200C
#define LOCAL_WGL_SHARE_DEPTH_EXT                            0x200C
#define LOCAL_WGL_SHARE_STENCIL_ARB                          0x200D
#define LOCAL_WGL_SHARE_STENCIL_EXT                          0x200D
#define LOCAL_WGL_STENCIL_BITS_ARB                           0x2023
#define LOCAL_WGL_STENCIL_BITS_EXT                           0x2023
#define LOCAL_WGL_STENCIL_BUFFER_BIT_ARB                     0x00000008
#define LOCAL_WGL_STEREO_ARB                                 0x2012
#define LOCAL_WGL_STEREO_EMITTER_DISABLE_3DL                 0x2056
#define LOCAL_WGL_STEREO_EMITTER_ENABLE_3DL                  0x2055
#define LOCAL_WGL_STEREO_EXT                                 0x2012
#define LOCAL_WGL_STEREO_POLARITY_INVERT_3DL                 0x2058
#define LOCAL_WGL_STEREO_POLARITY_NORMAL_3DL                 0x2057
#define LOCAL_WGL_SUPPORT_GDI_ARB                            0x200F
#define LOCAL_WGL_SUPPORT_GDI_EXT                            0x200F
#define LOCAL_WGL_SUPPORT_OPENGL_ARB                         0x2010
#define LOCAL_WGL_SUPPORT_OPENGL_EXT                         0x2010
#define LOCAL_WGL_SWAP_COPY_ARB                              0x2029
#define LOCAL_WGL_SWAP_COPY_EXT                              0x2029
#define LOCAL_WGL_SWAP_EXCHANGE_ARB                          0x2028
#define LOCAL_WGL_SWAP_EXCHANGE_EXT                          0x2028
#define LOCAL_WGL_SWAP_LAYER_BUFFERS_ARB                     0x2006
#define LOCAL_WGL_SWAP_LAYER_BUFFERS_EXT                     0x2006
#define LOCAL_WGL_SWAP_MAIN_PLANE                            0x00000001
#define LOCAL_WGL_SWAP_METHOD_ARB                            0x2007
#define LOCAL_WGL_SWAP_METHOD_EXT                            0x2007
#define LOCAL_WGL_SWAP_OVERLAY1                              0x00000002
#define LOCAL_WGL_SWAP_OVERLAY10                             0x00000400
#define LOCAL_WGL_SWAP_OVERLAY11                             0x00000800
#define LOCAL_WGL_SWAP_OVERLAY12                             0x00001000
#define LOCAL_WGL_SWAP_OVERLAY13                             0x00002000
#define LOCAL_WGL_SWAP_OVERLAY14                             0x00004000
#define LOCAL_WGL_SWAP_OVERLAY15                             0x00008000
#define LOCAL_WGL_SWAP_OVERLAY2                              0x00000004
#define LOCAL_WGL_SWAP_OVERLAY3                              0x00000008
#define LOCAL_WGL_SWAP_OVERLAY4                              0x00000010
#define LOCAL_WGL_SWAP_OVERLAY5                              0x00000020
#define LOCAL_WGL_SWAP_OVERLAY6                              0x00000040
#define LOCAL_WGL_SWAP_OVERLAY7                              0x00000080
#define LOCAL_WGL_SWAP_OVERLAY8                              0x00000100
#define LOCAL_WGL_SWAP_OVERLAY9                              0x00000200
#define LOCAL_WGL_SWAP_UNDEFINED_ARB                         0x202A
#define LOCAL_WGL_SWAP_UNDEFINED_EXT                         0x202A
#define LOCAL_WGL_SWAP_UNDERLAY1                             0x00010000
#define LOCAL_WGL_SWAP_UNDERLAY10                            0x02000000
#define LOCAL_WGL_SWAP_UNDERLAY11                            0x04000000
#define LOCAL_WGL_SWAP_UNDERLAY12                            0x08000000
#define LOCAL_WGL_SWAP_UNDERLAY13                            0x10000000
#define LOCAL_WGL_SWAP_UNDERLAY14                            0x20000000
#define LOCAL_WGL_SWAP_UNDERLAY15                            0x40000000
#define LOCAL_WGL_SWAP_UNDERLAY2                             0x00020000
#define LOCAL_WGL_SWAP_UNDERLAY3                             0x00040000
#define LOCAL_WGL_SWAP_UNDERLAY4                             0x00080000
#define LOCAL_WGL_SWAP_UNDERLAY5                             0x00100000
#define LOCAL_WGL_SWAP_UNDERLAY6                             0x00200000
#define LOCAL_WGL_SWAP_UNDERLAY7                             0x00400000
#define LOCAL_WGL_SWAP_UNDERLAY8                             0x00800000
#define LOCAL_WGL_SWAP_UNDERLAY9                             0x01000000
#define LOCAL_WGL_TEXTURE_1D_ARB                             0x2079
#define LOCAL_WGL_TEXTURE_2D_ARB                             0x207A
#define LOCAL_WGL_TEXTURE_CUBE_MAP_ARB                       0x2078
#define LOCAL_WGL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB            0x207E
#define LOCAL_WGL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB            0x2080
#define LOCAL_WGL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB            0x2082
#define LOCAL_WGL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB            0x207D
#define LOCAL_WGL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB            0x207F
#define LOCAL_WGL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB            0x2081
#define LOCAL_WGL_TEXTURE_DEPTH_COMPONENT_NV                 0x20A6
#define LOCAL_WGL_TEXTURE_FLOAT_RGBA_NV                      0x20B8
#define LOCAL_WGL_TEXTURE_FLOAT_RGB_NV                       0x20B7
#define LOCAL_WGL_TEXTURE_FLOAT_RG_NV                        0x20B6
#define LOCAL_WGL_TEXTURE_FLOAT_R_NV                         0x20B5
#define LOCAL_WGL_TEXTURE_FORMAT_ARB                         0x2072
#define LOCAL_WGL_TEXTURE_RECTANGLE_ATI                      0x21A5
#define LOCAL_WGL_TEXTURE_RECTANGLE_NV                       0x20A2
#define LOCAL_WGL_TEXTURE_RGBA_ARB                           0x2076
#define LOCAL_WGL_TEXTURE_RGB_ARB                            0x2075
#define LOCAL_WGL_TEXTURE_TARGET_ARB                         0x2073
#define LOCAL_WGL_TRANSPARENT_ALPHA_VALUE_ARB                0x203A
#define LOCAL_WGL_TRANSPARENT_ARB                            0x200A
#define LOCAL_WGL_TRANSPARENT_BLUE_VALUE_ARB                 0x2039
#define LOCAL_WGL_TRANSPARENT_EXT                            0x200A
#define LOCAL_WGL_TRANSPARENT_GREEN_VALUE_ARB                0x2038
#define LOCAL_WGL_TRANSPARENT_INDEX_VALUE_ARB                0x203B
#define LOCAL_WGL_TRANSPARENT_RED_VALUE_ARB                  0x2037
#define LOCAL_WGL_TRANSPARENT_VALUE_EXT                      0x200B
#define LOCAL_WGL_TYPE_COLORINDEX_ARB                        0x202C
#define LOCAL_WGL_TYPE_COLORINDEX_EXT                        0x202C
#define LOCAL_WGL_TYPE_RGBA_ARB                              0x202B
#define LOCAL_WGL_TYPE_RGBA_EXT                              0x202B
#define LOCAL_WGL_TYPE_RGBA_FLOAT_ARB                        0x21A0
#define LOCAL_WGL_TYPE_RGBA_FLOAT_ATI                        0x21A0
#define LOCAL_WGL_TYPE_RGBA_UNSIGNED_FLOAT_EXT               0x20A8
#define LOCAL_WGL_UNIQUE_ID_NV                               0x20CE
#define LOCAL_WGL_VIDEO_OUT_ALPHA_NV                         0x20C4
#define LOCAL_WGL_VIDEO_OUT_COLOR_AND_ALPHA_NV               0x20C6
#define LOCAL_WGL_VIDEO_OUT_COLOR_AND_DEPTH_NV               0x20C7
#define LOCAL_WGL_VIDEO_OUT_COLOR_NV                         0x20C3
#define LOCAL_WGL_VIDEO_OUT_DEPTH_NV                         0x20C5
#define LOCAL_WGL_VIDEO_OUT_FIELD_1                          0x20C9
#define LOCAL_WGL_VIDEO_OUT_FIELD_2                          0x20CA
#define LOCAL_WGL_VIDEO_OUT_FRAME                            0x20C8
#define LOCAL_WGL_VIDEO_OUT_STACKED_FIELDS_1_2               0x20CB
#define LOCAL_WGL_VIDEO_OUT_STACKED_FIELDS_2_1               0x20CC

#endif // GLCONSTS_H_

// clang-format on
