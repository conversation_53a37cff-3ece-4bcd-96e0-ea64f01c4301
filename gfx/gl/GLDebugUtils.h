/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef GLDEBUGUTILS_H_
#define GLDEBUGUTILS_H_

#include "GLTypes.h"

namespace mozilla {
namespace gl {

const char* GLenumToStr(GLenum e);

}  // namespace gl
}  // namespace mozilla

#endif  // !GLDEBUGUTILS_H_
