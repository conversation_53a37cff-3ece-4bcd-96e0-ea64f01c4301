# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

EXPORTS += [
    "gfxConfig.h",
    "gfxFallback.h",
    "gfxFeature.h",
]

EXPORTS.mozilla.gfx += [
    "gfxConfigManager.h",
    "gfxVarReceiver.h",
    "gfxVars.h",
]

UNIFIED_SOURCES += [
    "gfxConfig.cpp",
    "gfxConfigManager.cpp",
    "gfxFeature.cpp",
    "gfxVars.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"
