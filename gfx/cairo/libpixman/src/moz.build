# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

EXPORTS += [
    'pixman-version.h',
    'pixman.h',
]

SOURCES += [
    'pixman-access-accessors.c',
    'pixman-access.c',
    'pixman-arm.c',
    'pixman-bits-image.c',
    'pixman-combine-float.c',
    'pixman-combine32.c',
    'pixman-conical-gradient.c',
    'pixman-edge-accessors.c',
    'pixman-edge.c',
    'pixman-fast-path.c',
    'pixman-filter.c',
    'pixman-general.c',
    'pixman-glyph.c',
    'pixman-gradient-walker.c',
    'pixman-image.c',
    'pixman-implementation.c',
    'pixman-linear-gradient.c',
    'pixman-matrix.c',
    'pixman-mips.c',
    'pixman-noop.c',
    'pixman-ppc.c',
    'pixman-radial-gradient.c',
    'pixman-region16.c',
    'pixman-region32.c',
    'pixman-solid-fill.c',
    'pixman-trap.c',
    'pixman-utils.c',
    'pixman-x86.c',
    'pixman.c',
]

# We allow warnings for third-party code that can be updated from upstream.
AllowCompilerWarnings()

FINAL_LIBRARY = 'xul'
LOCAL_INCLUDES += [
    '../../cairo/src',
]

if CONFIG['MOZ_USE_PTHREADS']:
    DEFINES['HAVE_PTHREADS'] = True

DEFINES['PACKAGE'] = 'mozpixman'

if CONFIG['INTEL_ARCHITECTURE']:
    DEFINES['USE_X86_MMX'] = True
    DEFINES['USE_SSE2'] = True
    DEFINES['USE_SSSE3'] = True
    SOURCES += [
        'pixman-mmx.c',
        'pixman-sse2.c',
        'pixman-ssse3.c',
    ]
    SOURCES['pixman-mmx.c'].flags += CONFIG['MMX_FLAGS']
    SOURCES['pixman-sse2.c'].flags += CONFIG['SSE_FLAGS'] + CONFIG['SSE2_FLAGS']
    SOURCES['pixman-ssse3.c'].flags += CONFIG['SSSE3_FLAGS']
# AArch64 NEON optimizations don't build on Windows and Mac out of the box.
elif CONFIG['TARGET_CPU'] == 'aarch64' and CONFIG['OS_TARGET'] in ('Android', 'Linux', 'Darwin'):
    DEFINES['USE_ARM_A64_NEON'] = True
    if CONFIG['OS_TARGET'] == 'Darwin':
        DEFINES['ASM_LEADING_UNDERSCORE'] = True
    SOURCES += [
        'pixman-arm-neon.c',
        'pixman-arma64-neon-asm-bilinear.S',
        'pixman-arma64-neon-asm.S',
    ]
    SOURCES['pixman-arm-neon.c'].flags += ['-march=armv8-a']
elif CONFIG['TARGET_CPU'] == 'arm':
    if CONFIG['HAVE_ARM_NEON']:
        DEFINES['USE_ARM_NEON'] = True
        SOURCES += [
            'pixman-arm-neon-asm-bilinear.S',
            'pixman-arm-neon-asm.S',
            'pixman-arm-neon.c',
        ]
        SOURCES['pixman-arm-neon.c'].flags += CONFIG['NEON_FLAGS']
    if CONFIG['HAVE_ARM_SIMD']:
        DEFINES['USE_ARM_SIMD'] = True
        SOURCES += [
            'pixman-arm-simd-asm-scaled.S',
            'pixman-arm-simd-asm.S',
            'pixman-arm-simd.c',
        ]
    if CONFIG['HAVE_ARM_NEON'] or CONFIG['HAVE_ARM_SIMD']:
        DEFINES['ASM_HAVE_SYNTAX_UNIFIED'] = True
    if CONFIG['OS_TARGET'] == 'Android':
        # For cpu-features.h
        LOCAL_INCLUDES += [
            '%%%s/sources/android/cpufeatures' % CONFIG['ANDROID_NDK']
        ]
        SOURCES += [
            '%%%s/sources/android/cpufeatures/cpu-features.c' % CONFIG['ANDROID_NDK'],
        ]
elif CONFIG['TARGET_CPU'] in ('ppc', 'ppc64'):
    if CONFIG['CC_TYPE'] in ('clang', 'gcc'):
        DEFINES['USE_VMX'] = True
        SOURCES += ['pixman-vmx.c']
        SOURCES['pixman-vmx.c'].flags += ['-maltivec']

# Suppress warnings in third-party code.
CFLAGS += [
    '-Wno-address',
    '-Wno-braced-scalar-init',
    '-Wno-missing-field-initializers',
    '-Wno-sign-compare',
    '-Wno-incompatible-pointer-types',
    '-Wno-unused',                      # too many unused warnings; ignore
]
if CONFIG['CC_TYPE'] in ('clang', 'clang-cl'):
    CFLAGS += [
        '-Wno-incompatible-pointer-types',
        '-Wno-tautological-compare',
        '-Wno-tautological-constant-out-of-range-compare',
    ]
if CONFIG['CC_TYPE'] == 'clang-cl':
    CFLAGS += [
        '-Wno-unused-variable',
    ]

# See bug 386897.
if CONFIG['OS_TARGET'] == 'Android' and CONFIG['MOZ_OPTIMIZE']:
    CFLAGS += ['-O2']
