diff --git a/gfx/cairo/cairo/src/cairoint.h b/gfx/cairo/cairo/src/cairoint.h
--- a/gfx/cairo/cairo/src/cairoint.h
+++ b/gfx/cairo/cairo/src/cairoint.h
@@ -190,7 +190,11 @@ static inline int cairo_const
 #if __GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
     return __builtin_popcount (mask);
 #else
+#ifdef __cplusplus
+    int y;
+#else
     register int y;
+#endif
 
     y = (mask >> 1) &033333333333;
     y = mask - y - ((y >>1) & 033333333333);
