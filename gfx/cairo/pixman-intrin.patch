diff --git a/gfx/cairo/libpixman/src/pixman-x86.c b/gfx/cairo/libpixman/src/pixman-x86.c
--- a/gfx/cairo/libpixman/src/pixman-x86.c
+++ b/gfx/cairo/libpixman/src/pixman-x86.c
@@ -73,16 +73,20 @@ detect_cpu_features (void)
 }
 
 #else
 
 #if defined (__GNUC__)
 #include <cpuid.h>
 #endif
 
+#ifdef _MSC_VER
+#include <intrin.h> /* for __cpuid */
+#endif
+
 static void
 pixman_cpuid (uint32_t feature,
 	      uint32_t *a, uint32_t *b, uint32_t *c, uint32_t *d)
 {
 #if defined (__GNUC__)
     *a = *b = *c = *d = 0;
     __get_cpuid(feature, a, b, c, d);
 #elif defined (_MSC_VER)
