Snapshots of cairo and glitz for mozilla usage.

We only include the relevant parts of each release (generally, src/*.[ch]),
as we have Makefile.in's that integrate into the Mozilla build system.  For
documentation and similar, please see the official tarballs at
http://www.cairographics.org/.

VERSIONS:

  cairo (1.18.0)
  pixman (0.43.4)

==== Local cairo patches ====

00-cairo_public.patch: allow cairo_public to be predefined

01-xlib-xrender-private.patch: xrender build fix

02-cplusplus-no-register.patch: 'register' keyword not allowed when cairo.h is included by C++ code

03-expose-lcd-filter.patch: expose the LCD filter settings as public API

04-subpixel-aa-api.patch: add API for setting subpixel-AA

05-ft-font-synth-flags-api.patch: enable client to control whether FreeType synthetic styles are allowed

06-shared-ft-face.patch: integrate with Gecko SharedFTFace

07-ft-variations-runtime-check.patch: runtime check for FreeType variation support

09-quartz-surface-additions.patch: add cairo_quartz_surface_get_image

11-quartz-surface-tags.patch: support for LINK tags in the quartz-surface backend

12-quartz-named-destination.patch: support for named destinations in quartz-surface

17-active-edges-crash.patch: avoid potential crash in active_edges

18-quartz-granular-ifdefs.patch: build fixes for iOS

19-ft-color-ifdef.patch: FT_COLOR build fix

20-ios-colorspace.patch: iOS doesn't support per-display color spaces

21-quartz-surface-leak.patch: fix DataSourceSurfaceRawData leak on quartz surface

22-windows-build-fix.patch: build fix for Windows/non-mingw

23-win32-api-additions.patch: Windows API additions for gecko

cff-font-creation.patch: make cairo_cff_font_t allocation consistent

==== pixman patches ====

pixman-armasm.patch: add an ifdef guard around pixman-config.h include

pixman-clangcl.patch: clang-cl compilation fix

pixman-export.patch: make sure pixman symbols are not exported in libxul

pixman-interp.patch: use lower quality interpolation by default on mobile

pixman-intrin.patch: include intrin.h on Windows to fix bustage

pixman-rename.patch: include pixman-rename.h for renaming of external symbols

pixman-neon.patch: fix for a build failure with clang on armhf linux
