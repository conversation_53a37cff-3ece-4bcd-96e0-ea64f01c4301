#define cairo_append_path _moz_cairo_append_path
#define cairo_arc _moz_cairo_arc
#define cairo_arc_negative _moz_cairo_arc_negative
#define cairo_arc_to _moz_cairo_arc_to
#define cairo_beos_surface_create _moz_cairo_beos_surface_create
#define cairo_beos_surface_create_for_bitmap _moz_cairo_beos_surface_create_for_bitmap
#define cairo_clip _moz_cairo_clip
#define cairo_clip_extents _moz_cairo_clip_extents
#define cairo_clip_preserve _moz_cairo_clip_preserve
#define cairo_close_path _moz_cairo_close_path
#define cairo_copy_clip_rectangle_list _moz_cairo_copy_clip_rectangle_list
#define cairo_copy_page _moz_cairo_copy_page
#define cairo_copy_path _moz_cairo_copy_path
#define cairo_copy_path_flat _moz_cairo_copy_path_flat
#define cairo_create _moz_cairo_create
#define cairo_curve_to _moz_cairo_curve_to
#define cairo_d2d_create_device _moz_cairo_d2d_create_device
#define cairo_d2d_create_device_from_d3d10device _moz_cairo_d2d_create_device_from_d3d10device
#define cairo_d2d_device_get_device _moz_cairo_d2d_device_get_device
#define cairo_d2d_get_dc _moz_cairo_d2d_get_dc
#define cairo_d2d_get_image_surface_cache_usage _moz_cairo_d2d_get_image_surface_cache_usage
#define cairo_d2d_get_surface_vram_usage _moz_cairo_d2d_get_surface_vram_usage
#define cairo_d2d_present_backbuffer _moz_cairo_d2d_present_backbuffer
#define cairo_d2d_release_dc _moz_cairo_d2d_release_dc
#define cairo_d2d_scroll _moz_cairo_d2d_scroll
#define cairo_d2d_surface_create _moz_cairo_d2d_surface_create
#define cairo_d2d_surface_create_for_handle _moz_cairo_d2d_surface_create_for_handle
#define cairo_d2d_surface_create_for_hwnd _moz_cairo_d2d_surface_create_for_hwnd
#define cairo_d2d_surface_create_for_texture _moz_cairo_d2d_surface_create_for_texture
#define cairo_d2d_surface_get_height _moz_cairo_d2d_surface_get_height
#define cairo_d2d_surface_get_texture _moz_cairo_d2d_surface_get_texture
#define cairo_d2d_surface_get_width _moz_cairo_d2d_surface_get_width
#define cairo_debug_reset_static_data _moz_cairo_debug_reset_static_data
#define cairo_destroy _moz_cairo_destroy
#define cairo_device_acquire _moz_cairo_device_acquire
#define cairo_device_destroy _moz_cairo_device_destroy
#define cairo_device_finish _moz_cairo_device_finish
#define cairo_device_flush _moz_cairo_device_flush
#define cairo_device_get_reference_count _moz_cairo_device_get_reference_count
#define cairo_device_get_type _moz_cairo_device_get_type
#define cairo_device_get_user_data _moz_cairo_device_get_user_data
#define cairo_device_release _moz_cairo_device_release
#define cairo_device_set_user_data _moz_cairo_device_set_user_data
#define cairo_device_status _moz_cairo_device_status
#define cairo_device_reference _moz_cairo_device_reference
#define cairo_device_to_user _moz_cairo_device_to_user
#define cairo_device_to_user_distance _moz_cairo_device_to_user_distance
#define cairo_directfb_surface_create _moz_cairo_directfb_surface_create
#define cairo_dwrite_font_face_create_for_dwrite_fontface _moz_cairo_dwrite_font_face_create_for_dwrite_fontface
#define cairo_dwrite_get_cleartype_rendering_mode _moz_cairo_dwrite_get_cleartype_rendering_mode
#define cairo_dwrite_scaled_font_get_force_GDI_classic _moz_cairo_dwrite_scaled_font_get_force_GDI_classic
#define cairo_dwrite_scaled_font_set_force_GDI_classic _moz_cairo_dwrite_scaled_font_set_force_GDI_classic
#define cairo_dwrite_set_cleartype_params _moz_cairo_dwrite_set_cleartype_params
#define cairo_fill _moz_cairo_fill
#define cairo_fill_extents _moz_cairo_fill_extents
#define cairo_fill_preserve _moz_cairo_fill_preserve
#define cairo_font_extents _moz_cairo_font_extents
#define cairo_font_face_destroy _moz_cairo_font_face_destroy
#define cairo_font_face_get_reference_count _moz_cairo_font_face_get_reference_count
#define cairo_font_face_get_type _moz_cairo_font_face_get_type
#define cairo_font_face_get_user_data _moz_cairo_font_face_get_user_data
#define cairo_font_face_reference _moz_cairo_font_face_reference
#define cairo_font_face_set_user_data _moz_cairo_font_face_set_user_data
#define cairo_font_face_status _moz_cairo_font_face_status
#define cairo_font_options_copy _moz_cairo_font_options_copy
#define cairo_font_options_create _moz_cairo_font_options_create
#define cairo_font_options_destroy _moz_cairo_font_options_destroy
#define cairo_font_options_equal _moz_cairo_font_options_equal
#define cairo_font_options_get_antialias _moz_cairo_font_options_get_antialias
#define cairo_font_options_get_hint_metrics _moz_cairo_font_options_get_hint_metrics
#define cairo_font_options_get_hint_style _moz_cairo_font_options_get_hint_style
#define cairo_font_options_get_lcd_filter _moz_cairo_font_options_get_lcd_filter
#define cairo_font_options_get_subpixel_order _moz_cairo_font_options_get_subpixel_order
#define cairo_font_options_hash _moz_cairo_font_options_hash
#define cairo_font_options_merge _moz_cairo_font_options_merge
#define cairo_font_options_set_antialias _moz_cairo_font_options_set_antialias
#define cairo_font_options_set_hint_metrics _moz_cairo_font_options_set_hint_metrics
#define cairo_font_options_set_hint_style _moz_cairo_font_options_set_hint_style
#define cairo_font_options_set_lcd_filter _moz_cairo_font_options_set_lcd_filter
#define cairo_font_options_set_subpixel_order _moz_cairo_font_options_set_subpixel_order
#define cairo_font_options_status _moz_cairo_font_options_status
#define cairo_format_stride_for_width _moz_cairo_format_stride_for_width
#define cairo_ft_font_face_create_for_ft_face _moz_cairo_ft_font_face_create_for_ft_face
#define cairo_ft_font_face_create_for_pattern _moz_cairo_ft_font_face_create_for_pattern
#define cairo_ft_font_face_set_synthesize _moz_cairo_ft_font_face_set_synthesize
#define cairo_ft_font_options_substitute _moz_cairo_ft_font_options_substitute
#define cairo_ft_scaled_font_lock_face _moz_cairo_ft_scaled_font_lock_face
#define cairo_ft_scaled_font_unlock_face _moz_cairo_ft_scaled_font_unlock_face
#define cairo_get_antialias _moz_cairo_get_antialias
#define cairo_get_current_point _moz_cairo_get_current_point
#define cairo_get_dash _moz_cairo_get_dash
#define cairo_get_dash_count _moz_cairo_get_dash_count
#define cairo_get_fill_rule _moz_cairo_get_fill_rule
#define cairo_get_font_face _moz_cairo_get_font_face
#define cairo_get_font_matrix _moz_cairo_get_font_matrix
#define cairo_get_font_options _moz_cairo_get_font_options
#define cairo_get_group_target _moz_cairo_get_group_target
#define cairo_get_line_cap _moz_cairo_get_line_cap
#define cairo_get_line_join _moz_cairo_get_line_join
#define cairo_get_line_width _moz_cairo_get_line_width
#define cairo_get_matrix _moz_cairo_get_matrix
#define cairo_get_miter_limit _moz_cairo_get_miter_limit
#define cairo_get_operator _moz_cairo_get_operator
#define cairo_get_reference_count _moz_cairo_get_reference_count
#define cairo_get_scaled_font _moz_cairo_get_scaled_font
#define cairo_get_source _moz_cairo_get_source
#define cairo_get_target _moz_cairo_get_target
#define cairo_get_tolerance _moz_cairo_get_tolerance
#define cairo_get_user_data _moz_cairo_get_user_data
#define cairo_glitz_surface_create _moz_cairo_glitz_surface_create
#define cairo_glyph_allocate _moz_cairo_glyph_allocate
#define cairo_glyph_extents _moz_cairo_glyph_extents
#define cairo_glyph_free _moz_cairo_glyph_free
#define cairo_glyph_path _moz_cairo_glyph_path
#define cairo_has_current_point _moz_cairo_has_current_point
#define cairo_has_show_text_glyphs _moz_cairo_has_show_text_glyphs
#define cairo_identity_matrix _moz_cairo_identity_matrix
#define cairo_image_surface_create _moz_cairo_image_surface_create
#define cairo_image_surface_create_for_data _moz_cairo_image_surface_create_for_data
#define cairo_image_surface_create_from_png _moz_cairo_image_surface_create_from_png
#define cairo_image_surface_create_from_png_stream _moz_cairo_image_surface_create_from_png_stream
#define cairo_image_surface_get_data _moz_cairo_image_surface_get_data
#define cairo_image_surface_get_format _moz_cairo_image_surface_get_format
#define cairo_image_surface_get_height _moz_cairo_image_surface_get_height
#define cairo_image_surface_get_stride _moz_cairo_image_surface_get_stride
#define cairo_image_surface_get_width _moz_cairo_image_surface_get_width
#define cairo_in_clip _moz_cairo_in_clip
#define cairo_in_fill _moz_cairo_in_fill
#define cairo_in_stroke _moz_cairo_in_stroke
#define cairo_line_to _moz_cairo_line_to
#define cairo_mask _moz_cairo_mask
#define cairo_mask_surface _moz_cairo_mask_surface
#define cairo_matrix_init _moz_cairo_matrix_init
#define cairo_matrix_init_identity _moz_cairo_matrix_init_identity
#define cairo_matrix_init_rotate _moz_cairo_matrix_init_rotate
#define cairo_matrix_init_scale _moz_cairo_matrix_init_scale
#define cairo_matrix_init_translate _moz_cairo_matrix_init_translate
#define cairo_matrix_invert _moz_cairo_matrix_invert
#define cairo_matrix_multiply _moz_cairo_matrix_multiply
#define cairo_matrix_rotate _moz_cairo_matrix_rotate
#define cairo_matrix_scale _moz_cairo_matrix_scale
#define cairo_matrix_transform_distance _moz_cairo_matrix_transform_distance
#define cairo_matrix_transform_point _moz_cairo_matrix_transform_point
#define cairo_matrix_translate _moz_cairo_matrix_translate
#define cairo_move_to _moz_cairo_move_to
#define cairo_new_path _moz_cairo_new_path
#define cairo_new_sub_path _moz_cairo_new_sub_path
#define cairo_null_surface_create _moz_cairo_null_surface_create
#define cairo_os2_fini _moz_cairo_os2_fini
#define cairo_os2_init _moz_cairo_os2_init
#define cairo_os2_surface_create _moz_cairo_os2_surface_create
#define cairo_os2_surface_create_for_window _moz_cairo_os2_surface_create_for_window
#define cairo_os2_surface_get_hps _moz_cairo_os2_surface_get_hps
#define cairo_os2_surface_get_manual_window_refresh _moz_cairo_os2_surface_get_manual_window_refresh
#define cairo_os2_surface_refresh_window _moz_cairo_os2_surface_refresh_window
#define cairo_os2_surface_set_hps _moz_cairo_os2_surface_set_hps
#define cairo_os2_surface_set_hwnd _moz_cairo_os2_surface_set_hwnd
#define cairo_os2_surface_set_manual_window_refresh _moz_cairo_os2_surface_set_manual_window_refresh
#define cairo_os2_surface_set_size _moz_cairo_os2_surface_set_size
#define cairo_paint _moz_cairo_paint
#define cairo_paint_with_alpha _moz_cairo_paint_with_alpha
#define cairo_path_destroy _moz_cairo_path_destroy
#define cairo_path_extents _moz_cairo_path_extents
#define cairo_pattern_add_color_stop_rgb _moz_cairo_pattern_add_color_stop_rgb
#define cairo_pattern_add_color_stop_rgba _moz_cairo_pattern_add_color_stop_rgba
#define cairo_pattern_create_for_surface _moz_cairo_pattern_create_for_surface
#define cairo_pattern_create_linear _moz_cairo_pattern_create_linear
#define cairo_pattern_create_radial _moz_cairo_pattern_create_radial
#define cairo_pattern_create_rgb _moz_cairo_pattern_create_rgb
#define cairo_pattern_create_rgba _moz_cairo_pattern_create_rgba
#define cairo_pattern_destroy _moz_cairo_pattern_destroy
#define cairo_pattern_get_color_stop_count _moz_cairo_pattern_get_color_stop_count
#define cairo_pattern_get_color_stop_rgba _moz_cairo_pattern_get_color_stop_rgba
#define cairo_pattern_get_extend _moz_cairo_pattern_get_extend
#define cairo_pattern_get_filter _moz_cairo_pattern_get_filter
#define cairo_pattern_get_linear_points _moz_cairo_pattern_get_linear_points
#define cairo_pattern_get_matrix _moz_cairo_pattern_get_matrix
#define cairo_pattern_get_radial_circles _moz_cairo_pattern_get_radial_circles
#define cairo_pattern_get_reference_count _moz_cairo_pattern_get_reference_count
#define cairo_pattern_get_rgba _moz_cairo_pattern_get_rgba
#define cairo_pattern_get_surface _moz_cairo_pattern_get_surface
#define cairo_pattern_get_type _moz_cairo_pattern_get_type
#define cairo_pattern_get_user_data _moz_cairo_pattern_get_user_data
#define cairo_pattern_reference _moz_cairo_pattern_reference
#define cairo_pattern_set_extend _moz_cairo_pattern_set_extend
#define cairo_pattern_set_filter _moz_cairo_pattern_set_filter
#define cairo_pattern_set_matrix _moz_cairo_pattern_set_matrix
#define cairo_pattern_set_user_data _moz_cairo_pattern_set_user_data
#define cairo_pattern_status _moz_cairo_pattern_status
#define cairo_pdf_get_versions _moz_cairo_pdf_get_versions
#define cairo_pdf_surface_create _moz_cairo_pdf_surface_create
#define cairo_pdf_surface_create_for_stream _moz_cairo_pdf_surface_create_for_stream
#define cairo_pdf_surface_restrict_to_version _moz_cairo_pdf_surface_restrict_to_version
#define cairo_pdf_surface_set_size _moz_cairo_pdf_surface_set_size
#define cairo_pdf_version_to_string _moz_cairo_pdf_version_to_string
#define cairo_pop_group _moz_cairo_pop_group
#define cairo_pop_group_to_source _moz_cairo_pop_group_to_source
#define cairo_ps_get_levels _moz_cairo_ps_get_levels
#define cairo_ps_level_to_string _moz_cairo_ps_level_to_string
#define cairo_ps_surface_create _moz_cairo_ps_surface_create
#define cairo_ps_surface_create_for_stream _moz_cairo_ps_surface_create_for_stream
#define cairo_ps_surface_dsc_begin_page_setup _moz_cairo_ps_surface_dsc_begin_page_setup
#define cairo_ps_surface_dsc_begin_setup _moz_cairo_ps_surface_dsc_begin_setup
#define cairo_ps_surface_dsc_comment _moz_cairo_ps_surface_dsc_comment
#define cairo_ps_surface_get_eps _moz_cairo_ps_surface_get_eps
#define cairo_ps_surface_restrict_to_level _moz_cairo_ps_surface_restrict_to_level
#define cairo_ps_surface_set_eps _moz_cairo_ps_surface_set_eps
#define cairo_ps_surface_set_size _moz_cairo_ps_surface_set_size
#define cairo_push_group _moz_cairo_push_group
#define cairo_push_group_with_content _moz_cairo_push_group_with_content
#define cairo_qpainter_surface_create _moz_cairo_qpainter_surface_create
#define cairo_qpainter_surface_create_with_qimage _moz_cairo_qpainter_surface_create_with_qimage
#define cairo_qpainter_surface_create_with_qpixmap _moz_cairo_qpainter_surface_create_with_qpixmap
#define cairo_qpainter_surface_get_image _moz_cairo_qpainter_surface_get_image
#define cairo_qpainter_surface_get_qimage _moz_cairo_qpainter_surface_get_qimage
#define cairo_qpainter_surface_get_qpainter _moz_cairo_qpainter_surface_get_qpainter
#define cairo_quartz_font_face_create_for_atsu_font_id _moz_cairo_quartz_font_face_create_for_atsu_font_id
#define cairo_quartz_font_face_create_for_cgfont _moz_cairo_quartz_font_face_create_for_cgfont
#define cairo_quartz_image_surface_create _moz_cairo_quartz_image_surface_create
#define cairo_quartz_image_surface_get_image _moz_cairo_quartz_image_surface_get_image
#define cairo_quartz_surface_create _moz_cairo_quartz_surface_create
#define cairo_quartz_surface_create_for_cg_context _moz_cairo_quartz_surface_create_for_cg_context
#define cairo_quartz_surface_get_cg_context _moz_cairo_quartz_surface_get_cg_context
#define cairo_quartz_surface_get_image _moz_cairo_quartz_surface_get_image
#define cairo_recording_surface_create _moz_cairo_recording_surface_create
#define cairo_recording_surface_ink_extents _moz_cairo_recording_surface_ink_extents
#define cairo_rectangle _moz_cairo_rectangle
#define cairo_rectangle_list_destroy _moz_cairo_rectangle_list_destroy
#define cairo_reference _moz_cairo_reference
#define cairo_region_contains_point _moz_cairo_region_contains_point
#define cairo_region_contains_rectangle _moz_cairo_region_contains_rectangle
#define cairo_region_copy _moz_cairo_region_copy
#define cairo_region_create _moz_cairo_region_create
#define cairo_region_create_rectangle _moz_cairo_region_create_rectangle
#define cairo_region_create_rectangles _moz_cairo_region_create_rectangles
#define cairo_region_destroy _moz_cairo_region_destroy
#define cairo_region_equal _moz_cairo_region_equal
#define cairo_region_get_extents _moz_cairo_region_get_extents
#define cairo_region_get_rectangle _moz_cairo_region_get_rectangle
#define cairo_region_intersect _moz_cairo_region_intersect
#define cairo_region_intersect_rectangle _moz_cairo_region_intersect_rectangle
#define cairo_region_is_empty _moz_cairo_region_is_empty
#define cairo_region_num_rectangles _moz_cairo_region_num_rectangles
#define cairo_region_reference _moz_cairo_region_reference
#define cairo_region_status _moz_cairo_region_status
#define cairo_region_subtract _moz_cairo_region_subtract
#define cairo_region_subtract_rectangle _moz_cairo_region_subtract_rectangle
#define cairo_region_translate _moz_cairo_region_translate
#define cairo_region_union _moz_cairo_region_union
#define cairo_region_union_rectangle _moz_cairo_region_union_rectangle
#define cairo_region_xor _moz_cairo_region_xor
#define cairo_region_xor_rectangle _moz_cairo_region_xor_rectangle
#define cairo_rel_curve_to _moz_cairo_rel_curve_to
#define cairo_rel_line_to _moz_cairo_rel_line_to
#define cairo_rel_move_to _moz_cairo_rel_move_to
#define cairo_release_device _moz_cairo_release_device
#define cairo_reset_clip _moz_cairo_reset_clip
#define cairo_restore _moz_cairo_restore
#define cairo_rotate _moz_cairo_rotate
#define cairo_save _moz_cairo_save
#define cairo_scale _moz_cairo_scale
#define cairo_scaled_font_create _moz_cairo_scaled_font_create
#define cairo_scaled_font_destroy _moz_cairo_scaled_font_destroy
#define cairo_scaled_font_extents _moz_cairo_scaled_font_extents
#define cairo_scaled_font_get_ctm _moz_cairo_scaled_font_get_ctm
#define cairo_scaled_font_get_font_face _moz_cairo_scaled_font_get_font_face
#define cairo_scaled_font_get_font_matrix _moz_cairo_scaled_font_get_font_matrix
#define cairo_scaled_font_get_font_options _moz_cairo_scaled_font_get_font_options
#define cairo_scaled_font_get_hint_metrics _moz_cairo_scaled_font_get_hint_metrics
#define cairo_scaled_font_get_reference_count _moz_cairo_scaled_font_get_reference_count
#define cairo_scaled_font_get_scale_matrix _moz_cairo_scaled_font_get_scale_matrix
#define cairo_scaled_font_get_type _moz_cairo_scaled_font_get_type
#define cairo_scaled_font_get_user_data _moz_cairo_scaled_font_get_user_data
#define cairo_scaled_font_glyph_extents _moz_cairo_scaled_font_glyph_extents
#define cairo_scaled_font_reference _moz_cairo_scaled_font_reference
#define cairo_scaled_font_set_user_data _moz_cairo_scaled_font_set_user_data
#define cairo_scaled_font_status _moz_cairo_scaled_font_status
#define cairo_scaled_font_text_extents _moz_cairo_scaled_font_text_extents
#define cairo_scaled_font_text_to_glyphs _moz_cairo_scaled_font_text_to_glyphs
#define cairo_select_font_face _moz_cairo_select_font_face
#define cairo_set_antialias _moz_cairo_set_antialias
#define cairo_set_dash _moz_cairo_set_dash
#define cairo_set_fill_rule _moz_cairo_set_fill_rule
#define cairo_set_font_face _moz_cairo_set_font_face
#define cairo_set_font_matrix _moz_cairo_set_font_matrix
#define cairo_set_font_options _moz_cairo_set_font_options
#define cairo_set_font_size _moz_cairo_set_font_size
#define cairo_set_line_cap _moz_cairo_set_line_cap
#define cairo_set_line_join _moz_cairo_set_line_join
#define cairo_set_line_width _moz_cairo_set_line_width
#define cairo_set_matrix _moz_cairo_set_matrix
#define cairo_set_miter_limit _moz_cairo_set_miter_limit
#define cairo_set_operator _moz_cairo_set_operator
#define cairo_set_scaled_font _moz_cairo_set_scaled_font
#define cairo_set_source _moz_cairo_set_source
#define cairo_set_source_rgb _moz_cairo_set_source_rgb
#define cairo_set_source_rgba _moz_cairo_set_source_rgba
#define cairo_set_source_surface _moz_cairo_set_source_surface
#define cairo_set_tolerance _moz_cairo_set_tolerance
#define cairo_set_user_data _moz_cairo_set_user_data
#define cairo_show_glyphs _moz_cairo_show_glyphs
#define cairo_show_page _moz_cairo_show_page
#define cairo_show_text _moz_cairo_show_text
#define cairo_show_text_glyphs _moz_cairo_show_text_glyphs
#define cairo_status _moz_cairo_status
#define cairo_status_to_string _moz_cairo_status_to_string
#define cairo_stroke _moz_cairo_stroke
#define cairo_stroke_extents _moz_cairo_stroke_extents
#define cairo_stroke_preserve _moz_cairo_stroke_preserve
#define cairo_stroke_to_path _moz_cairo_stroke_to_path
#define cairo_surface_attach_snapshot _moz_cairo_surface_attach_snapshot
#define cairo_surface_copy_page _moz_cairo_surface_copy_page
#define cairo_surface_create_for_rectangle _moz_cairo_surface_create_for_rectangle
#define cairo_surface_create_similar _moz_cairo_surface_create_similar
#define cairo_surface_detach_snapshot _moz_cairo_surface_detach_snapshot
#define cairo_surface_destroy _moz_cairo_surface_destroy
#define cairo_surface_finish _moz_cairo_surface_finish
#define cairo_surface_flush _moz_cairo_surface_flush
#define cairo_surface_get_content _moz_cairo_surface_get_content
#define cairo_surface_get_device _moz_cairo_surface_get_device
#define cairo_surface_get_device_offset _moz_cairo_surface_get_device_offset
#define cairo_surface_get_fallback_resolution _moz_cairo_surface_get_fallback_resolution
#define cairo_surface_get_font_options _moz_cairo_surface_get_font_options
#define cairo_surface_get_mime_data _moz_cairo_surface_get_mime_data
#define cairo_surface_get_reference_count _moz_cairo_surface_get_reference_count
#define cairo_surface_get_subpixel_antialiasing _moz_cairo_surface_get_subpixel_antialiasing
#define cairo_surface_get_type _moz_cairo_surface_get_type
#define cairo_surface_get_user_data _moz_cairo_surface_get_user_data
#define cairo_surface_has_show_text_glyphs _moz_cairo_surface_has_show_text_glyphs
#define cairo_surface_mark_dirty _moz_cairo_surface_mark_dirty
#define cairo_surface_mark_dirty_rectangle _moz_cairo_surface_mark_dirty_rectangle
#define cairo_surface_reference _moz_cairo_surface_reference
#define cairo_surface_set_device_offset _moz_cairo_surface_set_device_offset
#define cairo_surface_set_fallback_resolution _moz_cairo_surface_set_fallback_resolution
#define cairo_surface_set_mime_data _moz_cairo_surface_set_mime_data
#define cairo_surface_set_subpixel_antialiasing _moz_cairo_surface_set_subpixel_antialiasing
#define cairo_surface_set_user_data _moz_cairo_surface_set_user_data
#define cairo_surface_show_page _moz_cairo_surface_show_page
#define cairo_surface_status _moz_cairo_surface_status
#define cairo_surface_write_to_png _moz_cairo_surface_write_to_png
#define cairo_surface_write_to_png_stream _moz_cairo_surface_write_to_png_stream
#define cairo_svg_get_versions _moz_cairo_svg_get_versions
#define cairo_svg_surface_create _moz_cairo_svg_surface_create
#define cairo_svg_surface_create_for_stream _moz_cairo_svg_surface_create_for_stream
#define cairo_svg_surface_restrict_to_version _moz_cairo_svg_surface_restrict_to_version
#define cairo_svg_version_to_string _moz_cairo_svg_version_to_string
#define cairo_tee_surface_add _moz_cairo_tee_surface_add
#define cairo_tee_surface_create _moz_cairo_tee_surface_create
#define cairo_tee_surface_index _moz_cairo_tee_surface_index
#define cairo_tee_surface_remove _moz_cairo_tee_surface_remove
#define cairo_text_cluster_allocate _moz_cairo_text_cluster_allocate
#define cairo_text_cluster_free _moz_cairo_text_cluster_free
#define cairo_text_extents _moz_cairo_text_extents
#define cairo_text_path _moz_cairo_text_path
#define cairo_toy_font_face_create _moz_cairo_toy_font_face_create
#define cairo_toy_font_face_get_family _moz_cairo_toy_font_face_get_family
#define cairo_toy_font_face_get_slant _moz_cairo_toy_font_face_get_slant
#define cairo_toy_font_face_get_weight _moz_cairo_toy_font_face_get_weight
#define cairo_transform _moz_cairo_transform
#define cairo_translate _moz_cairo_translate
#define cairo_user_font_face_create _moz_cairo_user_font_face_create
#define cairo_user_font_face_get_init_func _moz_cairo_user_font_face_get_init_func
#define cairo_user_font_face_get_render_glyph_func _moz_cairo_user_font_face_get_render_glyph_func
#define cairo_user_font_face_get_text_to_glyphs_func _moz_cairo_user_font_face_get_text_to_glyphs_func
#define cairo_user_font_face_get_unicode_to_glyph_func _moz_cairo_user_font_face_get_unicode_to_glyph_func
#define cairo_user_font_face_set_init_func _moz_cairo_user_font_face_set_init_func
#define cairo_user_font_face_set_render_glyph_func _moz_cairo_user_font_face_set_render_glyph_func
#define cairo_user_font_face_set_text_to_glyphs_func _moz_cairo_user_font_face_set_text_to_glyphs_func
#define cairo_user_font_face_set_unicode_to_glyph_func _moz_cairo_user_font_face_set_unicode_to_glyph_func
#define cairo_user_to_device _moz_cairo_user_to_device
#define cairo_user_to_device_distance _moz_cairo_user_to_device_distance
#define cairo_version _moz_cairo_version
#define cairo_version_string _moz_cairo_version_string
#define cairo_win32_get_dc_with_clip _moz_cairo_win32_get_dc_with_clip
#define cairo_win32_get_system_text_quality _moz_cairo_win32_get_system_text_quality
#define cairo_win32_font_face_create_for_hfont _moz_cairo_win32_font_face_create_for_hfont
#define cairo_win32_font_face_create_for_logfontw _moz_cairo_win32_font_face_create_for_logfontw
#define cairo_win32_font_face_create_for_logfontw_hfont _moz_cairo_win32_font_face_create_for_logfontw_hfont
#define cairo_win32_printing_surface_create _moz_cairo_win32_printing_surface_create
#define cairo_win32_scaled_font_done_font _moz_cairo_win32_scaled_font_done_font
#define cairo_win32_scaled_font_get_device_to_logical _moz_cairo_win32_scaled_font_get_device_to_logical
#define cairo_win32_scaled_font_get_logical_to_device _moz_cairo_win32_scaled_font_get_logical_to_device
#define cairo_win32_scaled_font_get_metrics_factor _moz_cairo_win32_scaled_font_get_metrics_factor
#define cairo_win32_scaled_font_select_font _moz_cairo_win32_scaled_font_select_font
#define cairo_win32_surface_create _moz_cairo_win32_surface_create
#define cairo_win32_surface_create_with_format _moz_cairo_win32_surface_create_with_format
#define cairo_win32_surface_create_with_d3dsurface9 _moz_cairo_win32_surface_create_with_d3dsurface9
#define cairo_win32_surface_create_with_ddb _moz_cairo_win32_surface_create_with_ddb
#define cairo_win32_surface_create_with_dib _moz_cairo_win32_surface_create_with_dib
#define cairo_win32_surface_get_dc _moz_cairo_win32_surface_get_dc
#define cairo_win32_surface_get_image _moz_cairo_win32_surface_get_image
#define cairo_win32_surface_get_size _moz_cairo_win32_surface_get_size
#define cairo_win32_surface_set_can_convert_to_dib _moz_cairo_win32_surface_set_can_convert_to_dib
#define cairo_xcb_surface_create _moz_cairo_xcb_surface_create
#define cairo_xcb_surface_create_for_bitmap _moz_cairo_xcb_surface_create_for_bitmap
#define cairo_xcb_surface_create_with_xrender_format _moz_cairo_xcb_surface_create_with_xrender_format
#define cairo_xcb_surface_set_size _moz_cairo_xcb_surface_set_size
#define cairo_xlib_surface_create _moz_cairo_xlib_surface_create
#define cairo_xlib_surface_create_for_bitmap _moz_cairo_xlib_surface_create_for_bitmap
#define cairo_xlib_surface_create_with_xrender_format _moz_cairo_xlib_surface_create_with_xrender_format
#define cairo_xlib_surface_get_depth _moz_cairo_xlib_surface_get_depth
#define cairo_xlib_surface_get_display _moz_cairo_xlib_surface_get_display
#define cairo_xlib_surface_get_drawable _moz_cairo_xlib_surface_get_drawable
#define cairo_xlib_surface_get_height _moz_cairo_xlib_surface_get_height
#define cairo_xlib_surface_get_screen _moz_cairo_xlib_surface_get_screen
#define cairo_xlib_surface_get_visual _moz_cairo_xlib_surface_get_visual
#define cairo_xlib_surface_get_width _moz_cairo_xlib_surface_get_width
#define cairo_xlib_surface_get_xrender_format _moz_cairo_xlib_surface_get_xrender_format
#define cairo_xlib_surface_set_drawable _moz_cairo_xlib_surface_set_drawable
#define cairo_xlib_surface_set_size _moz_cairo_xlib_surface_set_size
