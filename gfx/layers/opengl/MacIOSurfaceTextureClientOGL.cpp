/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "MacIOSurfaceTextureClientOGL.h"
#include "mozilla/gfx/MacIOSurface.h"
#include "MacIOSurfaceHelpers.h"
#include "gfxPlatform.h"

namespace mozilla {
namespace layers {

using namespace gfx;

MacIOSurfaceTextureData::MacIOSurfaceTextureData(MacIOSurface* aSurface,
                                                 BackendType aBackend)
    : mSurface(aSurface), mBackend(aBackend) {
  MOZ_ASSERT(mSurface);
}

MacIOSurfaceTextureData::~MacIOSurfaceTextureData() = default;

// static
MacIOSurfaceTextureData* MacIOSurfaceTextureData::Create(MacIOSurface* aSurface,
                                                         BackendType aBackend) {
  MOZ_ASSERT(aSurface);
  if (!aSurface) {
    return nullptr;
  }
  return new MacIOSurfaceTextureData(aSurface, aBackend);
}

MacIOSurfaceTextureData* MacIOSurfaceTextureData::Create(const IntSize& aSize,
                                                         SurfaceFormat aFormat,
                                                         BackendType aBackend) {
  if (aFormat != SurfaceFormat::B8G8R8A8 &&
      aFormat != SurfaceFormat::B8G8R8X8) {
    return nullptr;
  }

  RefPtr<MacIOSurface> surf = MacIOSurface::CreateIOSurface(
      aSize.width, aSize.height, aFormat == SurfaceFormat::B8G8R8A8);
  if (!surf) {
    return nullptr;
  }

  return Create(surf, aBackend);
}

bool MacIOSurfaceTextureData::Serialize(SurfaceDescriptor& aOutDescriptor) {
  RefPtr<layers::GpuFence> gpuFence;
  aOutDescriptor = SurfaceDescriptorMacIOSurface(
      mSurface->GetIOSurfaceID(), !mSurface->HasAlpha(),
      mSurface->GetYUVColorSpace(), std::move(gpuFence));
  return true;
}

void MacIOSurfaceTextureData::GetSubDescriptor(
    RemoteDecoderVideoSubDescriptor* const aOutDesc) {
  RefPtr<layers::GpuFence> gpuFence;
  *aOutDesc = SurfaceDescriptorMacIOSurface(
      mSurface->GetIOSurfaceID(), !mSurface->HasAlpha(),
      mSurface->GetYUVColorSpace(), std::move(gpuFence));
}

void MacIOSurfaceTextureData::FillInfo(TextureData::Info& aInfo) const {
  aInfo.size = gfx::IntSize(mSurface->GetDevicePixelWidth(),
                            mSurface->GetDevicePixelHeight());
  aInfo.format =
      mSurface->HasAlpha() ? SurfaceFormat::B8G8R8A8 : SurfaceFormat::B8G8R8X8;
  aInfo.hasSynchronization = false;
  aInfo.supportsMoz2D = true;
  aInfo.canExposeMappedData = false;
}

bool MacIOSurfaceTextureData::Lock(OpenMode) { return mSurface->Lock(false); }

void MacIOSurfaceTextureData::Unlock() { mSurface->Unlock(false); }

already_AddRefed<DataSourceSurface> MacIOSurfaceTextureData::GetAsSurface() {
  RefPtr<SourceSurface> surf = CreateSourceSurfaceFromMacIOSurface(mSurface);
  return surf->GetDataSurface();
}

already_AddRefed<DrawTarget> MacIOSurfaceTextureData::BorrowDrawTarget() {
  MOZ_ASSERT(mBackend != BackendType::NONE);
  if (mBackend == BackendType::NONE) {
    // shouldn't happen, but degrade gracefully
    return nullptr;
  }
  return Factory::CreateDrawTargetForData(
      mBackend, (unsigned char*)mSurface->GetBaseAddress(),
      IntSize(mSurface->GetWidth(), mSurface->GetHeight()),
      mSurface->GetBytesPerRow(),
      mSurface->HasAlpha() ? SurfaceFormat::B8G8R8A8 : SurfaceFormat::B8G8R8X8,
      true);
}

void MacIOSurfaceTextureData::Deallocate(LayersIPCChannel*) {
  mSurface = nullptr;
}

void MacIOSurfaceTextureData::Forget(LayersIPCChannel*) { mSurface = nullptr; }

bool MacIOSurfaceTextureData::UpdateFromSurface(gfx::SourceSurface* aSurface) {
  RefPtr<DrawTarget> dt = BorrowDrawTarget();
  if (!dt) {
    return false;
  }

  dt->CopySurface(aSurface, IntRect(IntPoint(), aSurface->GetSize()),
                  IntPoint());
  return true;
}

}  // namespace layers
}  // namespace mozilla
