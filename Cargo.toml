[workspace]

# These are the "root" crates, which we build and test as top-level targets.
# Their transitive dependencies and dev-dependencies are included automatically
# and do not need to be listed here. Their external dependencies are vendored
# into `third_party/rust` by `mach vendor rust`.
members = [
  "browser/app/nmhproxy/",
  "js/src/frontend/smoosh",
  "js/src/rust",
  "netwerk/base/idna_glue",
  "netwerk/test/http3server",
  "security/manager/ssl/builtins",
  "security/manager/ssl/tests/unit/test_builtins",
  "security/manager/ssl/ipcclientcerts",
  "security/manager/ssl/osclientcerts",
  "security/mls/mls_gk",
  "testing/geckodriver",
  "toolkit/components/uniffi-bindgen-gecko-js",
  "toolkit/crashreporter/client/app",
  "toolkit/crashreporter/minidump-analyzer/android/export",
  "toolkit/crashreporter/mozwer-rust",
  "toolkit/crashreporter/rust_minidump_writer_linux",
  "toolkit/library/gtest/rust",
  "toolkit/library/rust/",
]

# Excluded crates may be built as dependencies, but won't be considered members
# of the workspace and their dev-dependencies won't be included.
exclude = [
  # Exclude third-party code vendored into mozilla-central.
  "servo",
  "third_party/rust",

  # Excluded because these crates have their own Cargo workspaces so they can't
  # be included in the top-level one.
  "gfx/wr",

  # Excluded because they are used only as dependencies, not top-level targets,
  # so we don't need to vendor their dev-dependencies.
  "gfx/webrender_bindings",
  "media/mp4parse-rust/mp4parse",
  "media/mp4parse-rust/mp4parse_capi",
  "xpcom/rust/gkrust_utils",
  "toolkit/crashreporter/minidump-analyzer",
  "tools/lint/test/files/clippy",
  "tools/fuzzing/rust",
  "dom/base/rust",
  "dom/origin-trials/ffi",
  "ipc/rust/ipdl_utils",

  # Excluded because we don't want to vendor their dependencies.
  "intl/l10n/rust/l10nregistry-tests",
]

# Use the new dependency resolver to reduce some of the platform-specific dependencies.
# This is required for 'third_party/rust/wgpu-hal'
resolver = "2"

[workspace.dependencies]
# Shared across multiple UniFFI consumers.
uniffi = "0.28.2"
uniffi_bindgen = "0.28.2"
# Shared across multiple application-services consumers.
rusqlite = "0.31.0"
# Shared across multiple glean consumers.
glean = "=63.0.0"

# Explicitly specify what our profiles use.  The opt-level setting here is
# a total fiction; see the setup of MOZ_RUST_DEFAULT_FLAGS for what the
# opt-level setting will be as a result of various other configure flags.
[profile.dev]
opt-level = 1
rpath = false
lto = false
debug-assertions = true
panic = "abort"

[profile.release]
opt-level = 2
rpath = false
debug-assertions = false
panic = "abort"

# Optimize build dependencies, because bindgen and proc macros / style
# compilation take more to run than to build otherwise.
[profile.dev.build-override]
opt-level = 1

[profile.release.build-override]
opt-level = 1

# optimizing glsl more makes a big difference in swgl build times
[profile.dev.package.glsl]
opt-level = 2

[profile.release.package.glsl]
opt-level = 2

[patch.crates-io]
# The build system doesn't want those to be used, but they are pulled anyways (because
# dependencies can't be disabled based on features), but remain unused. We ensure they
# stay unused by overriding them with crates that contain enough to build the current
# code and will fail the build in unwanted cases.
cmake = { path = "build/rust/cmake" }
vcpkg = { path = "build/rust/vcpkg" }

# Helper crate for integration in the gecko build system.
mozbuild = { path = "build/rust/mozbuild" }

# Workspace hack
mozilla-central-workspace-hack = { path = "build/workspace-hack" }

# windows-targets replacement. It avoids the large dependencies of the original
# crate, which we don't really need as we require MSVC anyways.
windows-targets = { path = "build/rust/windows-targets" }

# Patch windows to use a non-vendored local copy of the crate.
windows = { path = "build/rust/windows" }

# Dummy oslog replacement. It's only used by glean in code that is not actually used.
oslog = { path = "build/rust/oslog" }

# Override terminal_size with a dummy crate that returns a fixed 80x25 terminal size.
terminal_size = { path = "build/rust/terminal_size" }

# Patch bitflags 1.0 to 2.0
bitflags = { path = "build/rust/bitflags" }

# Patch redox_users to an empty crate
redox_users = { path = "build/rust/redox_users" }

# Patch redox_syscall to an empty crate
redox_syscall = { path = "build/rust/redox_syscall" }

# Patch hermit-abi to an empty crate
hermit-abi = { path = "build/rust/hermit-abi" }

# Patch base64 0.13 to 0.21
base64 = { path = "build/rust/base64" }

# Patch wasi 0.10 to 0.11
wasi = { path = "build/rust/wasi" }

# tokio 0.29.0 includes an experimental "tracing" feature which requires
# backtrace ^0.3.58 and the `tokio_unstable` flag. We don't use it, and nothing
# else we do use requires backtrace, so dummy it out for now.
backtrace = { path = "build/rust/backtrace" }

# Patch bindgen 0.63 to 0.69
bindgen_0_63 = { package = "bindgen", path = "build/rust/bindgen-0.63" }
# Locally patch bindgen for https://github.com/rust-lang/rust-bindgen/pull/2824
bindgen = { path = "third_party/rust/bindgen" }

# Patch nix 0.26 to 0.29
nix = { path = "build/rust/nix" }

# Patch cfg_aliases 0.1 to 0.2
cfg_aliases = { path = "build/rust/cfg_aliases" }

# Patch indexmap 2.0 to 1.0
indexmap = { path = "build/rust/indexmap" }

# Patch derive_more 0.99 to 1.0.0-beta
derive_more = { path = "build/rust/derive_more" }

# Patch autocfg to hide rustc output. Workaround for https://github.com/cuviper/autocfg/issues/30
autocfg = { path = "third_party/rust/autocfg" }

# Patch goblin 0.7.0 to 0.8
goblin = { path = "build/rust/goblin" }

# Patch memoffset from 0.8.0 to 0.9.0 since it's compatible and it avoids duplication
memoffset = { path = "build/rust/memoffset" }

# Patch `hashbrown` 0.12.* to depend on 0.14.*
hashbrown = { path = "build/rust/hashbrown" }

# Patch `socket2` 0.4 to 0.5
socket2 = { path = "build/rust/socket2" }

# The following overrides point to dummy projects, as a temporary measure until this is resolved:
# https://github.com/rust-lang/cargo/issues/6179
js-sys = { path = "build/rust/dummy-web/js-sys" }
wasm-bindgen = { path = "build/rust/dummy-web/wasm-bindgen" }
web-sys = { path = "build/rust/dummy-web/web-sys" }

# Overrides to allow easier use of common internal crates.
moz_asserts = { path = "mozglue/static/rust/moz_asserts" }

# Patch `rure` to disable building the cdylib and staticlib targets
# Cargo has no way to disable building targets your dependencies provide which
# you don't depend on, and linking the cdylib breaks during instrumentation
# builds.
# Workaround for https://github.com/rust-lang/cargo/issues/11232
rure = { path = "third_party/rust/rure" }

# Patch `plist` to work with `indexmap` 2.*
plist = { path = "third_party/rust/plist" }

# To-be-published changes.
unicode-bidi = { git = "https://github.com/servo/unicode-bidi", rev = "ca612daf1c08c53abe07327cb3e6ef6e0a760f0c" }
nss-gk-api = { git = "https://github.com/beurdouche/nss-gk-api", rev = "e48a946811ffd64abc78de3ee284957d8d1c0d63" }

# Other overrides
any_all_workaround = { git = "https://github.com/hsivonen/any_all_workaround", rev = "7fb1b7034c9f172aade21ee1c8554e8d8a48af80" }
chardetng = { git = "https://github.com/hsivonen/chardetng", rev = "3484d3e3ebdc8931493aa5df4d7ee9360a90e76b" }
chardetng_c = { git = "https://github.com/hsivonen/chardetng_c", rev = "ed8a4c6f900a90d4dbc1d64b856e61490a1c3570" }
coremidi = { git = "https://github.com/chris-zen/coremidi.git", rev = "fc68464b5445caf111e41f643a2e69ccce0b4f83" }
cose = { git = "https://github.com/franziskuskiefer/cose-rust", rev = "43c22248d136c8b38fe42ea709d08da6355cf04b" }
firefox-on-glean = { path = "toolkit/components/glean/api" }
icu_capi = { path = "intl/icu_capi" }
icu_segmenter_data = { path = "intl/icu_segmenter_data" }
libudev-sys = { path = "dom/webauthn/libudev-sys" }
midir = { git = "https://github.com/mozilla/midir.git", rev = "85156e360a37d851734118104619f86bd18e94c6" }
# Allow webrender to have a versioned dependency on the older crate on crates.io
# in order to build standalone.
malloc_size_of_derive = { path = "xpcom/rust/malloc_size_of_derive" }

# application-services overrides to make updating them all simpler.
interrupt-support = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
relevancy = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
sql-support = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
suggest = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
sync15 = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
tabs = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
viaduct = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }
webext-storage = { git = "https://github.com/mozilla/application-services", rev = "03cf4a362408b9caffa6848aae2fcf472a789460" }

allocator-api2 = { path = "third_party/rust/allocator-api2" }
