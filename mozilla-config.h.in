/* List of defines generated by configure. Included with preprocessor flag,
 * -include, to avoid long list of -D defines on the compile command-line.
 * Do not edit.
 */

#ifndef MOZILLA_CONFIG_H
#define MOZILLA_CONFIG_H

#if defined(__clang__)
#pragma clang diagnostic push
#if __has_warning("-Wreserved-id-macro")
#pragma clang diagnostic ignored "-Wreserved-id-macro"
#endif
#endif

/* Expands to all the defines from configure. */
#undef ALLDEFINES

/*
 * The c99 defining the limit macros (UINT32_MAX for example), says:
 *
 *   C++ implementations should define these macros only when
 *   __STDC_LIMIT_MACROS is defined before <stdint.h> is included.
 *
 * The same also occurs with __STDC_CONSTANT_MACROS for the constant macros
 * (INT8_C for example) used to specify a literal constant of the proper type,
 * and with __STDC_FORMAT_MACROS for the format macros (PRId32 for example) used
 * with the fprintf function family.
 */
#define __STDC_LIMIT_MACROS
#if !defined(__STDC_CONSTANT_MACROS)
#define __STDC_CONSTANT_MACROS
#endif
#if !defined(__STDC_FORMAT_MACROS)
#define __STDC_FORMAT_MACROS
#endif

#if defined(__clang__)
#pragma clang diagnostic pop
#endif

/*
 * Force-include sdkdecls.h for building the chromium sandbox code.
 *
 * CHROMIUM_SANDBOX_BUILD is defined in security/sandbox/moz.build.
 * Note that this include path relies on the LOCAL_INCLUDES in that file.
 */
#if defined(CHROMIUM_SANDBOX_BUILD) && defined(XP_WIN)
#include "base/win/sdkdecls.h"

#ifdef __MINGW32__
/*
 * MinGW doesn't support __try / __except. There are a few mechanisms available
 * to hack around it and pseudo-support it, but these are untested in Firefox.
 * What is tested (and works) is replacing them with if(true) and else.
 */
#define __try if(true)
#define __except(x) else
#ifdef GetExceptionCode
#undef GetExceptionCode
#endif
#define GetExceptionCode() 0

#endif /* __MINGW32__ */
#endif /* defined(CHROMIUM_SANDBOX_BUILD) && defined(XP_WIN) */

#endif /* MOZILLA_CONFIG_H */
