pluginManagement {
    if (!gradle.root.hasProperty("mozconfig")){
        apply from: file('mobile/android/gradle/mozconfig.gradle')
    } else {
        gradle.ext.mozconfig = gradle.root.mozconfig
    }

    repositories {
        gradle.mozconfig.substs.GRADLE_MAVEN_REPOSITORIES.each { repository ->
            maven {
                url repository
                if (gradle.mozconfig.substs.ALLOW_INSECURE_GRADLE_REPOSITORIES) {
                    allowInsecureProtocol = true
                }
            }
        }
    }

    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/android-components/plugins/config")
    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/android-components/plugins/dependencies")
    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/android-components/plugins/publicsuffixlist")
    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/fenix/plugins/apksize")
    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/fenix/plugins/fenixdependencies")
    includeBuild("${rootProject.projectDir.absolutePath}/mobile/android/focus-android/plugins/focusdependencies")
}

plugins {
    id "mozac.ConfigPlugin"
    id 'mozac.DependenciesPlugin'
    id 'ApkSizePlugin'
    id 'FenixDependenciesPlugin'
    id 'FocusDependenciesPlugin'
}

// You might think topsrcdir is '.', but that's not true when the Gradle build
// is launched from within IntelliJ.
ext.topsrcdir = rootProject.projectDir.absolutePath

apply from: "${topsrcdir}/mobile/android/shared-settings.gradle"
apply from: "${topsrcdir}/mobile/android/autopublish-settings.gradle"

// Set the Android SDK location.  This is the *least specific* mechanism, which
// is unfortunate: we'd prefer to use the *most specific* mechanism.  That is,
// local.properties (first 'sdk.dir', then 'android.dir') and then the
// environment variable ANDROID_HOME will override this.  That's unfortunate,
// but it's hard to automatically arrange better.
System.setProperty('android.home', gradle.mozconfig.substs.ANDROID_SDK_ROOT)

include ':annotations', ':messaging_example', ':port_messaging_example'
include ':geckoview'
include ':exoplayer2'
include ':mozilla-lint-rules'
include ':android-components'

project(':annotations').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/annotations")
project(':geckoview').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/geckoview")
project(':exoplayer2').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/exoplayer2")
project(':android-components').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/android-components")

if (!gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT || gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT == "fenix") {
    include ':fenix'
    project(':fenix').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/fenix/app")
}
if (!gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT || gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT == "focus") {
    include ':focus-android'
    project(':focus-android').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/focus-android/app")
}
if (!gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT || gradle.mozconfig.substs.MOZ_ANDROID_SUBPROJECT == "geckoview_example") {
    include ':samples-browser'
    include ':geckoview_example'
    include ':test_runner'
    project(':test_runner').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/test_runner")
    project(':geckoview_example').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/geckoview_example")
    project(':samples-browser').projectDir = new File("${gradle.mozconfig.topsrcdir}/mobile/android/android-components/samples/browser")
}

if (hasProperty("androidFormatLintTest")) {
    include ':androidFormatLintTest'
    project(':androidFormatLintTest').projectDir = new File("${gradle.mozconfig.topsrcdir}/tools/lint/test/files/android-format")
}

project(':messaging_example').projectDir = new File('mobile/android/examples/messaging_example/app')
project(':port_messaging_example').projectDir = new File('mobile/android/examples/port_messaging_example/app')
