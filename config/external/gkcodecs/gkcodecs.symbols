# libaom symbols
aom_codec_av1_cx
aom_codec_av1_dx
aom_codec_build_config
aom_codec_control
aom_codec_dec_init_ver
aom_codec_decode
aom_codec_destroy
aom_codec_destroy
aom_codec_enc_config_default
aom_codec_enc_config_set
aom_codec_enc_init_ver
aom_codec_encode
aom_codec_err_to_string
aom_codec_error
aom_codec_error_detail
aom_codec_get_caps
aom_codec_get_cx_data
aom_codec_get_frame
aom_codec_peek_stream_info
aom_codec_set_option
aom_codec_version
aom_codec_version_str
aom_img_free
aom_img_plane_height
aom_img_plane_width
aom_img_wrap
#if defined(X86_WIN64)
aom_winx64_fstcw
#endif
av1_apply_encoding_flags
av1_change_config
av1_convert_sect5obus_to_annexb
av1_copy_new_frame_enc
av1_copy_reference_enc
av1_create_compressor
av1_get_active_map
av1_get_compressed_data
av1_get_global_headers
av1_get_last_show_frame
av1_get_preview_raw_frame
av1_get_quantizer
av1_initialize_enc
av1_qindex_to_quantizer
av1_quantizer_to_qindex
av1_receive_raw_frame
av1_remove_compressor
av1_set_active_map
av1_set_internal_size
av1_set_reference_enc
av1_use_as_reference
# libogg symbols
ogg_calloc_func
ogg_free_func
ogg_malloc_func
ogg_page_bos
ogg_page_granulepos
ogg_page_serialno
ogg_realloc_func
ogg_set_mem_functions
ogg_stream_check
ogg_stream_clear
ogg_stream_eos
ogg_stream_flush
ogg_stream_init
ogg_stream_packetin
ogg_stream_packetout
ogg_stream_pagein
ogg_stream_pageout
ogg_stream_reset
ogg_sync_buffer
ogg_sync_clear
ogg_sync_init
ogg_sync_pageseek
ogg_sync_reset
ogg_sync_wrote
# libopus symbols
opus_decode
opus_decoder_create
opus_decoder_ctl
opus_decoder_destroy
opus_encode
opus_encode_float
opus_encoder_create
opus_encoder_ctl
opus_encoder_destroy
opus_get_version_string
opus_multistream_decode
opus_multistream_decode_float
opus_multistream_decoder_create
opus_multistream_decoder_ctl
opus_multistream_decoder_destroy
opus_multistream_encode
opus_multistream_encoder_create
opus_multistream_encoder_ctl
opus_multistream_encoder_destroy
opus_packet_get_nb_channels
opus_packet_get_nb_frames
opus_packet_get_samples_per_frame
opus_packet_parse
opus_strerror
opus_multistream_encode_float
opus_multistream_surround_encoder_create
vorbis_block_clear
vorbis_block_init
vorbis_comment_clear
vorbis_comment_init
vorbis_dsp_clear
vorbis_info_clear
vorbis_info_init
vorbis_packet_blocksize
vorbis_synthesis
vorbis_synthesis_blockin
vorbis_synthesis_headerin
vorbis_synthesis_init
vorbis_synthesis_pcmout
vorbis_synthesis_read
vorbis_synthesis_restart
vorbis_encode_ctl
vorbis_bitrate_addblock
vorbis_analysis_buffer
vorbis_analysis_blockout
vorbis_encode_setup_vbr
vorbis_analysis
vorbis_analysis_init
vorbis_analysis_headerout
vorbis_encode_setup_init
vorbis_encode_setup_managed
vorbis_comment_add_tag
vorbis_bitrate_flushpacket
vorbis_analysis_wrote
# libvpx symbols
#ifndef MOZ_SYSTEM_LIBVPX
vpx_codec_build_config
vpx_codec_control_
vpx_codec_dec_init_ver
vpx_codec_decode
vpx_codec_destroy
vpx_codec_enc_config_default
vpx_codec_enc_config_set
vpx_codec_enc_init_multi_ver
vpx_codec_enc_init_ver
vpx_codec_encode
vpx_codec_err_to_string
vpx_codec_error
vpx_codec_error_detail
vpx_codec_get_caps
vpx_codec_get_cx_data
vpx_codec_get_frame
vpx_codec_set_frame_buffer_functions
vpx_codec_version
vpx_codec_version_str
vpx_codec_vp8_cx
vpx_codec_vp8_dx
vpx_codec_vp9_cx
vpx_codec_vp9_dx
vpx_img_alloc
vpx_img_free
vpx_img_wrap
#endif
#
