# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Build a library containing the ICU data for use in the JS shell, so that
# JSAPI consumers don't have to deal with setting ICU's data path.
Library("icudata")

LOCAL_INCLUDES += ["."]

prefix = ""
if (CONFIG["OS_ARCH"] == "WINNT" and CONFIG["TARGET_CPU"] == "x86") or CONFIG[
    "OS_ARCH"
] == "Darwin":
    prefix = "_"

data_file = {
    "little": "icudt%sl.dat" % CONFIG["MOZ_ICU_VERSION"],
    "big": "icudt%sb.dat" % CONFIG["MOZ_ICU_VERSION"],
}
data_dir = {
    "little": SRCDIR,
    "big": OBJDIR,
}
endianness = CONFIG["TARGET_ENDIANNESS"] or "little"
DEFINES["ICU_DATA_FILE"] = '"%s/%s"' % (data_dir[endianness], data_file[endianness])
DEFINES["ICU_DATA_SYMBOL"] = "%sicudt%s_dat" % (prefix, CONFIG["MOZ_ICU_VERSION"])
SOURCES += [
    "icu_data.S",
]

if CONFIG["OS_ARCH"] == "WINNT" and CONFIG["CC_TYPE"] == "clang-cl":
    USE_INTEGRATED_CLANGCL_AS = True
    # Work around https://github.com/llvm/llvm-project/issues/92229
    # We don't need debug flags on the command line for this file anyways.
    ASM_FLAGS["DEBUG"] = []

if CONFIG["TARGET_ENDIANNESS"] == "big":
    GeneratedFile(
        data_file["big"], script="convert_icudata.py", inputs=[data_file["little"]]
    )
