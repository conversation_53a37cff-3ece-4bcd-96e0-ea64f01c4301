# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

HostProgram("icupkg")

LOCAL_INCLUDES += [
    "/intl/icu/source/common",
    "/intl/icu/source/i18n",
    "/intl/icu/source/tools/toolutil",
]

include("../defs.mozbuild")
include("sources.mozbuild")

HOST_SOURCES += sources
HOST_SOURCES += [
    "/intl/icu/source/stubdata/stubdata.cpp",
]

HOST_USE_LIBS += [
    "host_icui18n",
    "host_icutoolutil",
    "host_icuuc",
]
