# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Library("icui18n")
FINAL_LIBRARY = "icu"

DEFINES["U_I18N_IMPLEMENTATION"] = True

LOCAL_INCLUDES += ["/intl/icu/source/common"]
LOCAL_INCLUDES += ["/mfbt/double-conversion"]

include("../defs.mozbuild")
include("sources.mozbuild")

SOURCES += sources

if CONFIG["TARGET_ENDIANNESS"] == "big":
    HostLibrary("host_icui18n")
    HOST_DEFINES["U_I18N_IMPLEMENTATION"] = True
    HOST_SOURCES += sources
    HOST_SOURCES += other_sources
