# THIS FILE IS GENERATED BY /intl/icu_sources_data.py DO NOT EDIT
sources = [
    "/intl/icu/source/i18n/astro.cpp",
    "/intl/icu/source/i18n/basictz.cpp",
    "/intl/icu/source/i18n/bocsu.cpp",
    "/intl/icu/source/i18n/buddhcal.cpp",
    "/intl/icu/source/i18n/calendar.cpp",
    "/intl/icu/source/i18n/cecal.cpp",
    "/intl/icu/source/i18n/chnsecal.cpp",
    "/intl/icu/source/i18n/choicfmt.cpp",
    "/intl/icu/source/i18n/coleitr.cpp",
    "/intl/icu/source/i18n/coll.cpp",
    "/intl/icu/source/i18n/collation.cpp",
    "/intl/icu/source/i18n/collationbuilder.cpp",
    "/intl/icu/source/i18n/collationcompare.cpp",
    "/intl/icu/source/i18n/collationdata.cpp",
    "/intl/icu/source/i18n/collationdatabuilder.cpp",
    "/intl/icu/source/i18n/collationdatareader.cpp",
    "/intl/icu/source/i18n/collationdatawriter.cpp",
    "/intl/icu/source/i18n/collationfastlatin.cpp",
    "/intl/icu/source/i18n/collationfastlatinbuilder.cpp",
    "/intl/icu/source/i18n/collationfcd.cpp",
    "/intl/icu/source/i18n/collationiterator.cpp",
    "/intl/icu/source/i18n/collationkeys.cpp",
    "/intl/icu/source/i18n/collationroot.cpp",
    "/intl/icu/source/i18n/collationrootelements.cpp",
    "/intl/icu/source/i18n/collationruleparser.cpp",
    "/intl/icu/source/i18n/collationsets.cpp",
    "/intl/icu/source/i18n/collationsettings.cpp",
    "/intl/icu/source/i18n/collationtailoring.cpp",
    "/intl/icu/source/i18n/collationweights.cpp",
    "/intl/icu/source/i18n/compactdecimalformat.cpp",
    "/intl/icu/source/i18n/coptccal.cpp",
    "/intl/icu/source/i18n/curramt.cpp",
    "/intl/icu/source/i18n/currfmt.cpp",
    "/intl/icu/source/i18n/currpinf.cpp",
    "/intl/icu/source/i18n/currunit.cpp",
    "/intl/icu/source/i18n/dangical.cpp",
    "/intl/icu/source/i18n/datefmt.cpp",
    "/intl/icu/source/i18n/dayperiodrules.cpp",
    "/intl/icu/source/i18n/dcfmtsym.cpp",
    "/intl/icu/source/i18n/decContext.cpp",
    "/intl/icu/source/i18n/decimfmt.cpp",
    "/intl/icu/source/i18n/decNumber.cpp",
    "/intl/icu/source/i18n/displayoptions.cpp",
    "/intl/icu/source/i18n/dtfmtsym.cpp",
    "/intl/icu/source/i18n/dtitvfmt.cpp",
    "/intl/icu/source/i18n/dtitvinf.cpp",
    "/intl/icu/source/i18n/dtptngen.cpp",
    "/intl/icu/source/i18n/dtrule.cpp",
    "/intl/icu/source/i18n/erarules.cpp",
    "/intl/icu/source/i18n/ethpccal.cpp",
    "/intl/icu/source/i18n/fmtable.cpp",
    "/intl/icu/source/i18n/format.cpp",
    "/intl/icu/source/i18n/formatted_string_builder.cpp",
    "/intl/icu/source/i18n/formattedval_iterimpl.cpp",
    "/intl/icu/source/i18n/formattedval_sbimpl.cpp",
    "/intl/icu/source/i18n/formattedvalue.cpp",
    "/intl/icu/source/i18n/fphdlimp.cpp",
    "/intl/icu/source/i18n/fpositer.cpp",
    "/intl/icu/source/i18n/gregocal.cpp",
    "/intl/icu/source/i18n/gregoimp.cpp",
    "/intl/icu/source/i18n/hebrwcal.cpp",
    "/intl/icu/source/i18n/indiancal.cpp",
    "/intl/icu/source/i18n/islamcal.cpp",
    "/intl/icu/source/i18n/iso8601cal.cpp",
    "/intl/icu/source/i18n/japancal.cpp",
    "/intl/icu/source/i18n/listformatter.cpp",
    "/intl/icu/source/i18n/measfmt.cpp",
    "/intl/icu/source/i18n/measunit.cpp",
    "/intl/icu/source/i18n/measunit_extra.cpp",
    "/intl/icu/source/i18n/measure.cpp",
    "/intl/icu/source/i18n/messageformat2.cpp",
    "/intl/icu/source/i18n/messageformat2_arguments.cpp",
    "/intl/icu/source/i18n/messageformat2_checker.cpp",
    "/intl/icu/source/i18n/messageformat2_data_model.cpp",
    "/intl/icu/source/i18n/messageformat2_errors.cpp",
    "/intl/icu/source/i18n/messageformat2_evaluation.cpp",
    "/intl/icu/source/i18n/messageformat2_formattable.cpp",
    "/intl/icu/source/i18n/messageformat2_formatter.cpp",
    "/intl/icu/source/i18n/messageformat2_function_registry.cpp",
    "/intl/icu/source/i18n/messageformat2_parser.cpp",
    "/intl/icu/source/i18n/messageformat2_serializer.cpp",
    "/intl/icu/source/i18n/msgfmt.cpp",
    "/intl/icu/source/i18n/nfrs.cpp",
    "/intl/icu/source/i18n/nfrule.cpp",
    "/intl/icu/source/i18n/nfsubs.cpp",
    "/intl/icu/source/i18n/number_affixutils.cpp",
    "/intl/icu/source/i18n/number_asformat.cpp",
    "/intl/icu/source/i18n/number_capi.cpp",
    "/intl/icu/source/i18n/number_compact.cpp",
    "/intl/icu/source/i18n/number_currencysymbols.cpp",
    "/intl/icu/source/i18n/number_decimalquantity.cpp",
    "/intl/icu/source/i18n/number_decimfmtprops.cpp",
    "/intl/icu/source/i18n/number_fluent.cpp",
    "/intl/icu/source/i18n/number_formatimpl.cpp",
    "/intl/icu/source/i18n/number_grouping.cpp",
    "/intl/icu/source/i18n/number_integerwidth.cpp",
    "/intl/icu/source/i18n/number_longnames.cpp",
    "/intl/icu/source/i18n/number_mapper.cpp",
    "/intl/icu/source/i18n/number_modifiers.cpp",
    "/intl/icu/source/i18n/number_multiplier.cpp",
    "/intl/icu/source/i18n/number_notation.cpp",
    "/intl/icu/source/i18n/number_output.cpp",
    "/intl/icu/source/i18n/number_padding.cpp",
    "/intl/icu/source/i18n/number_patternmodifier.cpp",
    "/intl/icu/source/i18n/number_patternstring.cpp",
    "/intl/icu/source/i18n/number_rounding.cpp",
    "/intl/icu/source/i18n/number_scientific.cpp",
    "/intl/icu/source/i18n/number_simple.cpp",
    "/intl/icu/source/i18n/number_skeletons.cpp",
    "/intl/icu/source/i18n/number_symbolswrapper.cpp",
    "/intl/icu/source/i18n/number_usageprefs.cpp",
    "/intl/icu/source/i18n/number_utils.cpp",
    "/intl/icu/source/i18n/numfmt.cpp",
    "/intl/icu/source/i18n/numparse_affixes.cpp",
    "/intl/icu/source/i18n/numparse_compositions.cpp",
    "/intl/icu/source/i18n/numparse_currency.cpp",
    "/intl/icu/source/i18n/numparse_decimal.cpp",
    "/intl/icu/source/i18n/numparse_impl.cpp",
    "/intl/icu/source/i18n/numparse_parsednumber.cpp",
    "/intl/icu/source/i18n/numparse_scientific.cpp",
    "/intl/icu/source/i18n/numparse_symbols.cpp",
    "/intl/icu/source/i18n/numparse_validators.cpp",
    "/intl/icu/source/i18n/numrange_capi.cpp",
    "/intl/icu/source/i18n/numrange_fluent.cpp",
    "/intl/icu/source/i18n/numrange_impl.cpp",
    "/intl/icu/source/i18n/numsys.cpp",
    "/intl/icu/source/i18n/olsontz.cpp",
    "/intl/icu/source/i18n/persncal.cpp",
    "/intl/icu/source/i18n/pluralranges.cpp",
    "/intl/icu/source/i18n/plurfmt.cpp",
    "/intl/icu/source/i18n/plurrule.cpp",
    "/intl/icu/source/i18n/quantityformatter.cpp",
    "/intl/icu/source/i18n/rbnf.cpp",
    "/intl/icu/source/i18n/rbtz.cpp",
    "/intl/icu/source/i18n/region.cpp",
    "/intl/icu/source/i18n/reldatefmt.cpp",
    "/intl/icu/source/i18n/reldtfmt.cpp",
    "/intl/icu/source/i18n/rulebasedcollator.cpp",
    "/intl/icu/source/i18n/scriptset.cpp",
    "/intl/icu/source/i18n/search.cpp",
    "/intl/icu/source/i18n/selfmt.cpp",
    "/intl/icu/source/i18n/sharedbreakiterator.cpp",
    "/intl/icu/source/i18n/simpletz.cpp",
    "/intl/icu/source/i18n/smpdtfmt.cpp",
    "/intl/icu/source/i18n/smpdtfst.cpp",
    "/intl/icu/source/i18n/sortkey.cpp",
    "/intl/icu/source/i18n/standardplural.cpp",
    "/intl/icu/source/i18n/string_segment.cpp",
    "/intl/icu/source/i18n/stsearch.cpp",
    "/intl/icu/source/i18n/taiwncal.cpp",
    "/intl/icu/source/i18n/timezone.cpp",
    "/intl/icu/source/i18n/tmunit.cpp",
    "/intl/icu/source/i18n/tmutamt.cpp",
    "/intl/icu/source/i18n/tmutfmt.cpp",
    "/intl/icu/source/i18n/tzfmt.cpp",
    "/intl/icu/source/i18n/tzgnames.cpp",
    "/intl/icu/source/i18n/tznames.cpp",
    "/intl/icu/source/i18n/tznames_impl.cpp",
    "/intl/icu/source/i18n/tzrule.cpp",
    "/intl/icu/source/i18n/tztrans.cpp",
    "/intl/icu/source/i18n/ucal.cpp",
    "/intl/icu/source/i18n/ucln_in.cpp",
    "/intl/icu/source/i18n/ucol.cpp",
    "/intl/icu/source/i18n/ucol_res.cpp",
    "/intl/icu/source/i18n/ucol_sit.cpp",
    "/intl/icu/source/i18n/ucoleitr.cpp",
    "/intl/icu/source/i18n/udat.cpp",
    "/intl/icu/source/i18n/udateintervalformat.cpp",
    "/intl/icu/source/i18n/udatpg.cpp",
    "/intl/icu/source/i18n/ufieldpositer.cpp",
    "/intl/icu/source/i18n/uitercollationiterator.cpp",
    "/intl/icu/source/i18n/ulistformatter.cpp",
    "/intl/icu/source/i18n/umsg.cpp",
    "/intl/icu/source/i18n/units_complexconverter.cpp",
    "/intl/icu/source/i18n/units_converter.cpp",
    "/intl/icu/source/i18n/units_data.cpp",
    "/intl/icu/source/i18n/units_router.cpp",
    "/intl/icu/source/i18n/unum.cpp",
    "/intl/icu/source/i18n/unumsys.cpp",
    "/intl/icu/source/i18n/upluralrules.cpp",
    "/intl/icu/source/i18n/usearch.cpp",
    "/intl/icu/source/i18n/uspoof.cpp",
    "/intl/icu/source/i18n/uspoof_impl.cpp",
    "/intl/icu/source/i18n/utf16collationiterator.cpp",
    "/intl/icu/source/i18n/utf8collationiterator.cpp",
    "/intl/icu/source/i18n/utmscale.cpp",
    "/intl/icu/source/i18n/vtzone.cpp",
    "/intl/icu/source/i18n/windtfmt.cpp",
    "/intl/icu/source/i18n/winnmfmt.cpp",
    "/intl/icu/source/i18n/wintzimpl.cpp",
    "/intl/icu/source/i18n/zonemeta.cpp",
]
other_sources = [
    "/intl/icu/source/i18n/alphaindex.cpp",
    "/intl/icu/source/i18n/anytrans.cpp",
    "/intl/icu/source/i18n/brktrans.cpp",
    "/intl/icu/source/i18n/casetrn.cpp",
    "/intl/icu/source/i18n/cpdtrans.cpp",
    "/intl/icu/source/i18n/csdetect.cpp",
    "/intl/icu/source/i18n/csmatch.cpp",
    "/intl/icu/source/i18n/csr2022.cpp",
    "/intl/icu/source/i18n/csrecog.cpp",
    "/intl/icu/source/i18n/csrmbcs.cpp",
    "/intl/icu/source/i18n/csrsbcs.cpp",
    "/intl/icu/source/i18n/csrucode.cpp",
    "/intl/icu/source/i18n/csrutf8.cpp",
    "/intl/icu/source/i18n/double-conversion-bignum-dtoa.cpp",
    "/intl/icu/source/i18n/double-conversion-bignum.cpp",
    "/intl/icu/source/i18n/double-conversion-cached-powers.cpp",
    "/intl/icu/source/i18n/double-conversion-double-to-string.cpp",
    "/intl/icu/source/i18n/double-conversion-fast-dtoa.cpp",
    "/intl/icu/source/i18n/double-conversion-string-to-double.cpp",
    "/intl/icu/source/i18n/double-conversion-strtod.cpp",
    "/intl/icu/source/i18n/esctrn.cpp",
    "/intl/icu/source/i18n/fmtable_cnv.cpp",
    "/intl/icu/source/i18n/funcrepl.cpp",
    "/intl/icu/source/i18n/gender.cpp",
    "/intl/icu/source/i18n/inputext.cpp",
    "/intl/icu/source/i18n/name2uni.cpp",
    "/intl/icu/source/i18n/nortrans.cpp",
    "/intl/icu/source/i18n/nultrans.cpp",
    "/intl/icu/source/i18n/quant.cpp",
    "/intl/icu/source/i18n/rbt.cpp",
    "/intl/icu/source/i18n/rbt_data.cpp",
    "/intl/icu/source/i18n/rbt_pars.cpp",
    "/intl/icu/source/i18n/rbt_rule.cpp",
    "/intl/icu/source/i18n/rbt_set.cpp",
    "/intl/icu/source/i18n/regexcmp.cpp",
    "/intl/icu/source/i18n/regeximp.cpp",
    "/intl/icu/source/i18n/regexst.cpp",
    "/intl/icu/source/i18n/regextxt.cpp",
    "/intl/icu/source/i18n/rematch.cpp",
    "/intl/icu/source/i18n/remtrans.cpp",
    "/intl/icu/source/i18n/repattrn.cpp",
    "/intl/icu/source/i18n/scientificnumberformatter.cpp",
    "/intl/icu/source/i18n/strmatch.cpp",
    "/intl/icu/source/i18n/strrepl.cpp",
    "/intl/icu/source/i18n/titletrn.cpp",
    "/intl/icu/source/i18n/tolowtrn.cpp",
    "/intl/icu/source/i18n/toupptrn.cpp",
    "/intl/icu/source/i18n/translit.cpp",
    "/intl/icu/source/i18n/transreg.cpp",
    "/intl/icu/source/i18n/tridpars.cpp",
    "/intl/icu/source/i18n/ucsdet.cpp",
    "/intl/icu/source/i18n/ulocdata.cpp",
    "/intl/icu/source/i18n/unesctrn.cpp",
    "/intl/icu/source/i18n/uni2name.cpp",
    "/intl/icu/source/i18n/uregex.cpp",
    "/intl/icu/source/i18n/uregexc.cpp",
    "/intl/icu/source/i18n/uregion.cpp",
    "/intl/icu/source/i18n/uspoof_build.cpp",
    "/intl/icu/source/i18n/uspoof_conf.cpp",
    "/intl/icu/source/i18n/utrans.cpp",
    "/intl/icu/source/i18n/vzone.cpp",
    "/intl/icu/source/i18n/zrule.cpp",
    "/intl/icu/source/i18n/ztrans.cpp",
]
EXPORTS.unicode += [
    "/intl/icu/source/i18n/unicode/alphaindex.h",
    "/intl/icu/source/i18n/unicode/basictz.h",
    "/intl/icu/source/i18n/unicode/calendar.h",
    "/intl/icu/source/i18n/unicode/choicfmt.h",
    "/intl/icu/source/i18n/unicode/coleitr.h",
    "/intl/icu/source/i18n/unicode/coll.h",
    "/intl/icu/source/i18n/unicode/compactdecimalformat.h",
    "/intl/icu/source/i18n/unicode/curramt.h",
    "/intl/icu/source/i18n/unicode/currpinf.h",
    "/intl/icu/source/i18n/unicode/currunit.h",
    "/intl/icu/source/i18n/unicode/datefmt.h",
    "/intl/icu/source/i18n/unicode/dcfmtsym.h",
    "/intl/icu/source/i18n/unicode/decimfmt.h",
    "/intl/icu/source/i18n/unicode/displayoptions.h",
    "/intl/icu/source/i18n/unicode/dtfmtsym.h",
    "/intl/icu/source/i18n/unicode/dtitvfmt.h",
    "/intl/icu/source/i18n/unicode/dtitvinf.h",
    "/intl/icu/source/i18n/unicode/dtptngen.h",
    "/intl/icu/source/i18n/unicode/dtrule.h",
    "/intl/icu/source/i18n/unicode/fieldpos.h",
    "/intl/icu/source/i18n/unicode/fmtable.h",
    "/intl/icu/source/i18n/unicode/format.h",
    "/intl/icu/source/i18n/unicode/formattednumber.h",
    "/intl/icu/source/i18n/unicode/formattedvalue.h",
    "/intl/icu/source/i18n/unicode/fpositer.h",
    "/intl/icu/source/i18n/unicode/gender.h",
    "/intl/icu/source/i18n/unicode/gregocal.h",
    "/intl/icu/source/i18n/unicode/listformatter.h",
    "/intl/icu/source/i18n/unicode/measfmt.h",
    "/intl/icu/source/i18n/unicode/measunit.h",
    "/intl/icu/source/i18n/unicode/measure.h",
    "/intl/icu/source/i18n/unicode/messageformat2.h",
    "/intl/icu/source/i18n/unicode/messageformat2_arguments.h",
    "/intl/icu/source/i18n/unicode/messageformat2_data_model.h",
    "/intl/icu/source/i18n/unicode/messageformat2_data_model_names.h",
    "/intl/icu/source/i18n/unicode/messageformat2_formattable.h",
    "/intl/icu/source/i18n/unicode/messageformat2_function_registry.h",
    "/intl/icu/source/i18n/unicode/msgfmt.h",
    "/intl/icu/source/i18n/unicode/nounit.h",
    "/intl/icu/source/i18n/unicode/numberformatter.h",
    "/intl/icu/source/i18n/unicode/numberrangeformatter.h",
    "/intl/icu/source/i18n/unicode/numfmt.h",
    "/intl/icu/source/i18n/unicode/numsys.h",
    "/intl/icu/source/i18n/unicode/plurfmt.h",
    "/intl/icu/source/i18n/unicode/plurrule.h",
    "/intl/icu/source/i18n/unicode/rbnf.h",
    "/intl/icu/source/i18n/unicode/rbtz.h",
    "/intl/icu/source/i18n/unicode/regex.h",
    "/intl/icu/source/i18n/unicode/region.h",
    "/intl/icu/source/i18n/unicode/reldatefmt.h",
    "/intl/icu/source/i18n/unicode/scientificnumberformatter.h",
    "/intl/icu/source/i18n/unicode/search.h",
    "/intl/icu/source/i18n/unicode/selfmt.h",
    "/intl/icu/source/i18n/unicode/simplenumberformatter.h",
    "/intl/icu/source/i18n/unicode/simpletz.h",
    "/intl/icu/source/i18n/unicode/smpdtfmt.h",
    "/intl/icu/source/i18n/unicode/sortkey.h",
    "/intl/icu/source/i18n/unicode/stsearch.h",
    "/intl/icu/source/i18n/unicode/tblcoll.h",
    "/intl/icu/source/i18n/unicode/timezone.h",
    "/intl/icu/source/i18n/unicode/tmunit.h",
    "/intl/icu/source/i18n/unicode/tmutamt.h",
    "/intl/icu/source/i18n/unicode/tmutfmt.h",
    "/intl/icu/source/i18n/unicode/translit.h",
    "/intl/icu/source/i18n/unicode/tzfmt.h",
    "/intl/icu/source/i18n/unicode/tznames.h",
    "/intl/icu/source/i18n/unicode/tzrule.h",
    "/intl/icu/source/i18n/unicode/tztrans.h",
    "/intl/icu/source/i18n/unicode/ucal.h",
    "/intl/icu/source/i18n/unicode/ucol.h",
    "/intl/icu/source/i18n/unicode/ucoleitr.h",
    "/intl/icu/source/i18n/unicode/ucsdet.h",
    "/intl/icu/source/i18n/unicode/udat.h",
    "/intl/icu/source/i18n/unicode/udateintervalformat.h",
    "/intl/icu/source/i18n/unicode/udatpg.h",
    "/intl/icu/source/i18n/unicode/udisplayoptions.h",
    "/intl/icu/source/i18n/unicode/ufieldpositer.h",
    "/intl/icu/source/i18n/unicode/uformattable.h",
    "/intl/icu/source/i18n/unicode/uformattednumber.h",
    "/intl/icu/source/i18n/unicode/uformattedvalue.h",
    "/intl/icu/source/i18n/unicode/ugender.h",
    "/intl/icu/source/i18n/unicode/ulistformatter.h",
    "/intl/icu/source/i18n/unicode/ulocdata.h",
    "/intl/icu/source/i18n/unicode/umsg.h",
    "/intl/icu/source/i18n/unicode/unirepl.h",
    "/intl/icu/source/i18n/unicode/unum.h",
    "/intl/icu/source/i18n/unicode/unumberformatter.h",
    "/intl/icu/source/i18n/unicode/unumberoptions.h",
    "/intl/icu/source/i18n/unicode/unumberrangeformatter.h",
    "/intl/icu/source/i18n/unicode/unumsys.h",
    "/intl/icu/source/i18n/unicode/upluralrules.h",
    "/intl/icu/source/i18n/unicode/uregex.h",
    "/intl/icu/source/i18n/unicode/uregion.h",
    "/intl/icu/source/i18n/unicode/ureldatefmt.h",
    "/intl/icu/source/i18n/unicode/usearch.h",
    "/intl/icu/source/i18n/unicode/usimplenumberformatter.h",
    "/intl/icu/source/i18n/unicode/uspoof.h",
    "/intl/icu/source/i18n/unicode/utmscale.h",
    "/intl/icu/source/i18n/unicode/utrans.h",
    "/intl/icu/source/i18n/unicode/vtzone.h",
]
