# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Library("icuuc")
FINAL_LIBRARY = "icu"

DEFINES["U_COMMON_IMPLEMENTATION"] = True

LOCAL_INCLUDES += ["/intl/icu/source/i18n"]

include("../defs.mozbuild")
include("sources.mozbuild")

SOURCES += sources

if CONFIG["TARGET_ENDIANNESS"] == "big":
    HostLibrary("host_icuuc")
    HOST_DEFINES["U_COMMON_IMPLEMENTATION"] = True
    HOST_SOURCES += sources
    HOST_SOURCES += other_sources

# Clang 16 added an error that can be downgraded, but won't be downgradable
# in clang 17.
# https://unicode-org.atlassian.net/browse/ICU-22113
if (
    CONFIG["CC_TYPE"] in ("clang", "clang-cl")
    and int(CONFIG["CC_VERSION"].split(".")[0]) == 16
):
    SOURCES["/intl/icu/source/common/ubidi.cpp"].flags += [
        "-Wno-error=enum-constexpr-conversion"
    ]
