# THIS FILE IS GENERATED BY /intl/icu_sources_data.py DO NOT EDIT
sources = [
    "/intl/icu/source/common/appendable.cpp",
    "/intl/icu/source/common/bmpset.cpp",
    "/intl/icu/source/common/brkeng.cpp",
    "/intl/icu/source/common/brkiter.cpp",
    "/intl/icu/source/common/bytesinkutil.cpp",
    "/intl/icu/source/common/bytestream.cpp",
    "/intl/icu/source/common/bytestrie.cpp",
    "/intl/icu/source/common/bytestriebuilder.cpp",
    "/intl/icu/source/common/caniter.cpp",
    "/intl/icu/source/common/characterproperties.cpp",
    "/intl/icu/source/common/chariter.cpp",
    "/intl/icu/source/common/charstr.cpp",
    "/intl/icu/source/common/cmemory.cpp",
    "/intl/icu/source/common/cstring.cpp",
    "/intl/icu/source/common/dictbe.cpp",
    "/intl/icu/source/common/dictionarydata.cpp",
    "/intl/icu/source/common/dtintrv.cpp",
    "/intl/icu/source/common/edits.cpp",
    "/intl/icu/source/common/emojiprops.cpp",
    "/intl/icu/source/common/errorcode.cpp",
    "/intl/icu/source/common/filteredbrk.cpp",
    "/intl/icu/source/common/filterednormalizer2.cpp",
    "/intl/icu/source/common/loadednormalizer2impl.cpp",
    "/intl/icu/source/common/localebuilder.cpp",
    "/intl/icu/source/common/localematcher.cpp",
    "/intl/icu/source/common/localeprioritylist.cpp",
    "/intl/icu/source/common/locavailable.cpp",
    "/intl/icu/source/common/locbased.cpp",
    "/intl/icu/source/common/locdispnames.cpp",
    "/intl/icu/source/common/locdistance.cpp",
    "/intl/icu/source/common/locdspnm.cpp",
    "/intl/icu/source/common/locid.cpp",
    "/intl/icu/source/common/loclikely.cpp",
    "/intl/icu/source/common/loclikelysubtags.cpp",
    "/intl/icu/source/common/locmap.cpp",
    "/intl/icu/source/common/locresdata.cpp",
    "/intl/icu/source/common/locutil.cpp",
    "/intl/icu/source/common/lsr.cpp",
    "/intl/icu/source/common/lstmbe.cpp",
    "/intl/icu/source/common/messagepattern.cpp",
    "/intl/icu/source/common/mlbe.cpp",
    "/intl/icu/source/common/normalizer2.cpp",
    "/intl/icu/source/common/normalizer2impl.cpp",
    "/intl/icu/source/common/normlzr.cpp",
    "/intl/icu/source/common/parsepos.cpp",
    "/intl/icu/source/common/patternprops.cpp",
    "/intl/icu/source/common/propname.cpp",
    "/intl/icu/source/common/punycode.cpp",
    "/intl/icu/source/common/putil.cpp",
    "/intl/icu/source/common/rbbi.cpp",
    "/intl/icu/source/common/rbbi_cache.cpp",
    "/intl/icu/source/common/rbbidata.cpp",
    "/intl/icu/source/common/rbbinode.cpp",
    "/intl/icu/source/common/rbbirb.cpp",
    "/intl/icu/source/common/rbbiscan.cpp",
    "/intl/icu/source/common/rbbisetb.cpp",
    "/intl/icu/source/common/rbbistbl.cpp",
    "/intl/icu/source/common/rbbitblb.cpp",
    "/intl/icu/source/common/resbund.cpp",
    "/intl/icu/source/common/resource.cpp",
    "/intl/icu/source/common/restrace.cpp",
    "/intl/icu/source/common/ruleiter.cpp",
    "/intl/icu/source/common/schriter.cpp",
    "/intl/icu/source/common/serv.cpp",
    "/intl/icu/source/common/servlk.cpp",
    "/intl/icu/source/common/servlkf.cpp",
    "/intl/icu/source/common/servls.cpp",
    "/intl/icu/source/common/servnotf.cpp",
    "/intl/icu/source/common/servrbf.cpp",
    "/intl/icu/source/common/servslkf.cpp",
    "/intl/icu/source/common/sharedobject.cpp",
    "/intl/icu/source/common/simpleformatter.cpp",
    "/intl/icu/source/common/static_unicode_sets.cpp",
    "/intl/icu/source/common/stringpiece.cpp",
    "/intl/icu/source/common/stringtriebuilder.cpp",
    "/intl/icu/source/common/uarrsort.cpp",
    "/intl/icu/source/common/ubidi.cpp",
    "/intl/icu/source/common/ubidi_props.cpp",
    "/intl/icu/source/common/ubidiln.cpp",
    "/intl/icu/source/common/ubidiwrt.cpp",
    "/intl/icu/source/common/ubrk.cpp",
    "/intl/icu/source/common/ucase.cpp",
    "/intl/icu/source/common/ucasemap.cpp",
    "/intl/icu/source/common/ucasemap_titlecase_brkiter.cpp",
    "/intl/icu/source/common/uchar.cpp",
    "/intl/icu/source/common/ucharstrie.cpp",
    "/intl/icu/source/common/ucharstriebuilder.cpp",
    "/intl/icu/source/common/ucharstrieiterator.cpp",
    "/intl/icu/source/common/uchriter.cpp",
    "/intl/icu/source/common/ucln_cmn.cpp",
    "/intl/icu/source/common/ucmndata.cpp",
    "/intl/icu/source/common/ucnv.cpp",
    "/intl/icu/source/common/ucnv_bld.cpp",
    "/intl/icu/source/common/ucnv_cb.cpp",
    "/intl/icu/source/common/ucnv_cnv.cpp",
    "/intl/icu/source/common/ucnv_err.cpp",
    "/intl/icu/source/common/ucnv_io.cpp",
    "/intl/icu/source/common/ucnv_u16.cpp",
    "/intl/icu/source/common/ucnv_u32.cpp",
    "/intl/icu/source/common/ucnv_u7.cpp",
    "/intl/icu/source/common/ucnv_u8.cpp",
    "/intl/icu/source/common/ucnvbocu.cpp",
    "/intl/icu/source/common/ucnvlat1.cpp",
    "/intl/icu/source/common/ucnvscsu.cpp",
    "/intl/icu/source/common/ucol_swp.cpp",
    "/intl/icu/source/common/ucptrie.cpp",
    "/intl/icu/source/common/ucurr.cpp",
    "/intl/icu/source/common/udata.cpp",
    "/intl/icu/source/common/udatamem.cpp",
    "/intl/icu/source/common/udataswp.cpp",
    "/intl/icu/source/common/uenum.cpp",
    "/intl/icu/source/common/uhash.cpp",
    "/intl/icu/source/common/uhash_us.cpp",
    "/intl/icu/source/common/uinit.cpp",
    "/intl/icu/source/common/uinvchar.cpp",
    "/intl/icu/source/common/uiter.cpp",
    "/intl/icu/source/common/ulist.cpp",
    "/intl/icu/source/common/uloc.cpp",
    "/intl/icu/source/common/uloc_keytype.cpp",
    "/intl/icu/source/common/uloc_tag.cpp",
    "/intl/icu/source/common/ulocale.cpp",
    "/intl/icu/source/common/ulocbuilder.cpp",
    "/intl/icu/source/common/umapfile.cpp",
    "/intl/icu/source/common/umath.cpp",
    "/intl/icu/source/common/umutablecptrie.cpp",
    "/intl/icu/source/common/umutex.cpp",
    "/intl/icu/source/common/unames.cpp",
    "/intl/icu/source/common/unifiedcache.cpp",
    "/intl/icu/source/common/unifilt.cpp",
    "/intl/icu/source/common/unifunct.cpp",
    "/intl/icu/source/common/uniset.cpp",
    "/intl/icu/source/common/uniset_closure.cpp",
    "/intl/icu/source/common/uniset_props.cpp",
    "/intl/icu/source/common/unisetspan.cpp",
    "/intl/icu/source/common/unistr.cpp",
    "/intl/icu/source/common/unistr_case.cpp",
    "/intl/icu/source/common/unistr_case_locale.cpp",
    "/intl/icu/source/common/unistr_cnv.cpp",
    "/intl/icu/source/common/unistr_props.cpp",
    "/intl/icu/source/common/unistr_titlecase_brkiter.cpp",
    "/intl/icu/source/common/unormcmp.cpp",
    "/intl/icu/source/common/uobject.cpp",
    "/intl/icu/source/common/uprops.cpp",
    "/intl/icu/source/common/uresbund.cpp",
    "/intl/icu/source/common/uresdata.cpp",
    "/intl/icu/source/common/uscript.cpp",
    "/intl/icu/source/common/uscript_props.cpp",
    "/intl/icu/source/common/uset.cpp",
    "/intl/icu/source/common/uset_props.cpp",
    "/intl/icu/source/common/usetiter.cpp",
    "/intl/icu/source/common/usprep.cpp",
    "/intl/icu/source/common/ustack.cpp",
    "/intl/icu/source/common/ustr_cnv.cpp",
    "/intl/icu/source/common/ustr_titlecase_brkiter.cpp",
    "/intl/icu/source/common/ustrcase.cpp",
    "/intl/icu/source/common/ustrcase_locale.cpp",
    "/intl/icu/source/common/ustrenum.cpp",
    "/intl/icu/source/common/ustrfmt.cpp",
    "/intl/icu/source/common/ustring.cpp",
    "/intl/icu/source/common/ustrtrns.cpp",
    "/intl/icu/source/common/utext.cpp",
    "/intl/icu/source/common/utf_impl.cpp",
    "/intl/icu/source/common/util.cpp",
    "/intl/icu/source/common/util_props.cpp",
    "/intl/icu/source/common/utrace.cpp",
    "/intl/icu/source/common/utrie.cpp",
    "/intl/icu/source/common/utrie2.cpp",
    "/intl/icu/source/common/utrie2_builder.cpp",
    "/intl/icu/source/common/utrie_swap.cpp",
    "/intl/icu/source/common/uts46.cpp",
    "/intl/icu/source/common/utypes.cpp",
    "/intl/icu/source/common/uvector.cpp",
    "/intl/icu/source/common/uvectr32.cpp",
    "/intl/icu/source/common/uvectr64.cpp",
    "/intl/icu/source/common/wintz.cpp",
]
other_sources = [
    "/intl/icu/source/common/bytestrieiterator.cpp",
    "/intl/icu/source/common/cstr.cpp",
    "/intl/icu/source/common/cwchar.cpp",
    "/intl/icu/source/common/icudataver.cpp",
    "/intl/icu/source/common/icuplug.cpp",
    "/intl/icu/source/common/pluralmap.cpp",
    "/intl/icu/source/common/propsvec.cpp",
    "/intl/icu/source/common/resbund_cnv.cpp",
    "/intl/icu/source/common/ubiditransform.cpp",
    "/intl/icu/source/common/ucat.cpp",
    "/intl/icu/source/common/ucnv2022.cpp",
    "/intl/icu/source/common/ucnv_ct.cpp",
    "/intl/icu/source/common/ucnv_ext.cpp",
    "/intl/icu/source/common/ucnv_lmb.cpp",
    "/intl/icu/source/common/ucnv_set.cpp",
    "/intl/icu/source/common/ucnvdisp.cpp",
    "/intl/icu/source/common/ucnvhz.cpp",
    "/intl/icu/source/common/ucnvisci.cpp",
    "/intl/icu/source/common/ucnvmbcs.cpp",
    "/intl/icu/source/common/ucnvsel.cpp",
    "/intl/icu/source/common/uidna.cpp",
    "/intl/icu/source/common/unorm.cpp",
    "/intl/icu/source/common/ures_cnv.cpp",
    "/intl/icu/source/common/usc_impl.cpp",
    "/intl/icu/source/common/ushape.cpp",
    "/intl/icu/source/common/ustr_wcs.cpp",
]
EXPORTS.unicode += [
    "/intl/icu/source/common/unicode/appendable.h",
    "/intl/icu/source/common/unicode/brkiter.h",
    "/intl/icu/source/common/unicode/bytestream.h",
    "/intl/icu/source/common/unicode/bytestrie.h",
    "/intl/icu/source/common/unicode/bytestriebuilder.h",
    "/intl/icu/source/common/unicode/caniter.h",
    "/intl/icu/source/common/unicode/casemap.h",
    "/intl/icu/source/common/unicode/char16ptr.h",
    "/intl/icu/source/common/unicode/chariter.h",
    "/intl/icu/source/common/unicode/dbbi.h",
    "/intl/icu/source/common/unicode/docmain.h",
    "/intl/icu/source/common/unicode/dtintrv.h",
    "/intl/icu/source/common/unicode/edits.h",
    "/intl/icu/source/common/unicode/enumset.h",
    "/intl/icu/source/common/unicode/errorcode.h",
    "/intl/icu/source/common/unicode/filteredbrk.h",
    "/intl/icu/source/common/unicode/icudataver.h",
    "/intl/icu/source/common/unicode/icuplug.h",
    "/intl/icu/source/common/unicode/idna.h",
    "/intl/icu/source/common/unicode/localebuilder.h",
    "/intl/icu/source/common/unicode/localematcher.h",
    "/intl/icu/source/common/unicode/localpointer.h",
    "/intl/icu/source/common/unicode/locdspnm.h",
    "/intl/icu/source/common/unicode/locid.h",
    "/intl/icu/source/common/unicode/messagepattern.h",
    "/intl/icu/source/common/unicode/normalizer2.h",
    "/intl/icu/source/common/unicode/normlzr.h",
    "/intl/icu/source/common/unicode/parseerr.h",
    "/intl/icu/source/common/unicode/parsepos.h",
    "/intl/icu/source/common/unicode/platform.h",
    "/intl/icu/source/common/unicode/ptypes.h",
    "/intl/icu/source/common/unicode/putil.h",
    "/intl/icu/source/common/unicode/rbbi.h",
    "/intl/icu/source/common/unicode/rep.h",
    "/intl/icu/source/common/unicode/resbund.h",
    "/intl/icu/source/common/unicode/schriter.h",
    "/intl/icu/source/common/unicode/simpleformatter.h",
    "/intl/icu/source/common/unicode/std_string.h",
    "/intl/icu/source/common/unicode/strenum.h",
    "/intl/icu/source/common/unicode/stringoptions.h",
    "/intl/icu/source/common/unicode/stringpiece.h",
    "/intl/icu/source/common/unicode/stringtriebuilder.h",
    "/intl/icu/source/common/unicode/symtable.h",
    "/intl/icu/source/common/unicode/ubidi.h",
    "/intl/icu/source/common/unicode/ubiditransform.h",
    "/intl/icu/source/common/unicode/ubrk.h",
    "/intl/icu/source/common/unicode/ucasemap.h",
    "/intl/icu/source/common/unicode/ucat.h",
    "/intl/icu/source/common/unicode/uchar.h",
    "/intl/icu/source/common/unicode/ucharstrie.h",
    "/intl/icu/source/common/unicode/ucharstriebuilder.h",
    "/intl/icu/source/common/unicode/uchriter.h",
    "/intl/icu/source/common/unicode/uclean.h",
    "/intl/icu/source/common/unicode/ucnv.h",
    "/intl/icu/source/common/unicode/ucnv_cb.h",
    "/intl/icu/source/common/unicode/ucnv_err.h",
    "/intl/icu/source/common/unicode/ucnvsel.h",
    "/intl/icu/source/common/unicode/uconfig.h",
    "/intl/icu/source/common/unicode/ucpmap.h",
    "/intl/icu/source/common/unicode/ucptrie.h",
    "/intl/icu/source/common/unicode/ucurr.h",
    "/intl/icu/source/common/unicode/udata.h",
    "/intl/icu/source/common/unicode/udisplaycontext.h",
    "/intl/icu/source/common/unicode/uenum.h",
    "/intl/icu/source/common/unicode/uidna.h",
    "/intl/icu/source/common/unicode/uiter.h",
    "/intl/icu/source/common/unicode/uldnames.h",
    "/intl/icu/source/common/unicode/uloc.h",
    "/intl/icu/source/common/unicode/ulocale.h",
    "/intl/icu/source/common/unicode/ulocbuilder.h",
    "/intl/icu/source/common/unicode/umachine.h",
    "/intl/icu/source/common/unicode/umisc.h",
    "/intl/icu/source/common/unicode/umutablecptrie.h",
    "/intl/icu/source/common/unicode/unifilt.h",
    "/intl/icu/source/common/unicode/unifunct.h",
    "/intl/icu/source/common/unicode/unimatch.h",
    "/intl/icu/source/common/unicode/uniset.h",
    "/intl/icu/source/common/unicode/unistr.h",
    "/intl/icu/source/common/unicode/unorm.h",
    "/intl/icu/source/common/unicode/unorm2.h",
    "/intl/icu/source/common/unicode/uobject.h",
    "/intl/icu/source/common/unicode/urename.h",
    "/intl/icu/source/common/unicode/urep.h",
    "/intl/icu/source/common/unicode/ures.h",
    "/intl/icu/source/common/unicode/uscript.h",
    "/intl/icu/source/common/unicode/uset.h",
    "/intl/icu/source/common/unicode/usetiter.h",
    "/intl/icu/source/common/unicode/ushape.h",
    "/intl/icu/source/common/unicode/usprep.h",
    "/intl/icu/source/common/unicode/ustring.h",
    "/intl/icu/source/common/unicode/ustringtrie.h",
    "/intl/icu/source/common/unicode/utext.h",
    "/intl/icu/source/common/unicode/utf.h",
    "/intl/icu/source/common/unicode/utf16.h",
    "/intl/icu/source/common/unicode/utf32.h",
    "/intl/icu/source/common/unicode/utf8.h",
    "/intl/icu/source/common/unicode/utf_old.h",
    "/intl/icu/source/common/unicode/utrace.h",
    "/intl/icu/source/common/unicode/utypes.h",
    "/intl/icu/source/common/unicode/uvernum.h",
    "/intl/icu/source/common/unicode/uversion.h",
]
