# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Security: RLBox")

EXPORTS.mozilla.rlbox += [
    "/third_party/rlbox_wasm2c_sandbox/include/rlbox_wasm2c_sandbox.hpp",
    "/third_party/rlbox_wasm2c_sandbox/include/rlbox_wasm2c_tls.hpp",
]
EXPORTS += [
    "/third_party/rlbox_wasm2c_sandbox/include/wasm2c_rt_mem.h",
    "/third_party/rlbox_wasm2c_sandbox/include/wasm2c_rt_minwasi.h",
]

SOURCES += [
    "/third_party/rlbox_wasm2c_sandbox/src/wasm2c_rt_mem.c",
    "/third_party/rlbox_wasm2c_sandbox/src/wasm2c_rt_minwasi.c",
    "rlbox_wasm2c_thread_locals.cpp",
]

LOCAL_INCLUDES += ["/third_party/wasm2c/wasm2c/"]

FINAL_LIBRARY = "xul"
