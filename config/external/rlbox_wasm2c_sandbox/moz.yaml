schema: 1

bugzilla:
  product: Core
  component: "General"

origin:
  name: rlbox_wasm2c_sandbox
  description: rlbox integration for the wasm2c sandboxed code
  url: https://github.com/PLSysSec/rlbox_wasm2c_sandbox/tree/upstream-wasm2c

  release: 0938ae96c5467bdf2210cf21be47ae576cbcc3cd (2023-08-29T00:00:41Z).
  revision: 0938ae96c5467bdf2210cf21be47ae576cbcc3cd

  license: MIT
  license-file: LICENSE

vendoring:
  url: https://github.com/PLSysSec/rlbox_wasm2c_sandbox
  source-hosting: github
  vendor-directory: third_party/rlbox_wasm2c_sandbox

  exclude:
    # dirs
    - test
    # files
    - ".*"
    - AppSandbox.md
    - CMakeLists.txt
    - LibrarySandbox.md
    - README.md
