# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# The lgpllibs library stores symbols from third-party LGPL licensed libraries,
# such as libav and libsoundtouch. It fulfills the requirement of dynamically
# linking these symbols into gecko.
#
# Any library added here should also be reflected in the about:license page.

GeckoSharedLibrary("lgpllibs", linkage=None)
SHARED_LIBRARY_NAME = "lgpllibs"

# For some reason, mingw builds export way too many symbols.
if CONFIG["TARGET_OS"] == "WINNT" and CONFIG["CC_TYPE"] != "clang-cl":
    SYMBOLS_FILE = "lgpllibs.symbols"
