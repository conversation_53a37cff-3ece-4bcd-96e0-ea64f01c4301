# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

DIRS += ["../../../third_party/sqlite3/src"]
if CONFIG["MOZ_FOLD_LIBS"]:
    Library("sqlite")
    # When folding libraries, sqlite is actually in the nss library.
    USE_LIBS += [
        "nss",
    ]
else:
    SharedLibrary("sqlite")
    SHARED_LIBRARY_NAME = "mozsqlite3"

    SYMBOLS_FILE = "/third_party/sqlite3/src/sqlite.symbols"
