schema: 1

bugzilla:
  product: Core
  component: "General"

origin:
  name: wasm2c_sandbox_compiler
  description: wasm2c fork used for rlbox sandboxing
  url: https://github.com/WebAssembly/wabt

  release: 963f973469b45969ce198e0c86d3af316790a780 (2023-05-12T21:56:46Z).
  revision: 963f973469b45969ce198e0c86d3af316790a780

  license: Apache-2.0
  license-file: LICENSE

vendoring:
  url: https://github.com/WebAssembly/wabt
  source-hosting: github
  vendor-directory: third_party/wasm2c
  exclude:
    # dirs
    - cmake
    - docs
    - fuzz-in
    - include/wabt/interp
    - man
    - scripts
    - src/interp
    - src/template
    - test
    - third_party
    - wasm2c/examples
    # files
    - .*
    - CMakeLists.txt
    - Contributing.md
    - Makefile
    - README.md
    - ubsan.blacklist
    - src/tools/s*
    - src/tools/wasm-*
    - src/tools/wast*
    - src/tools/wat*
    - src/tools/wasm2w*
    - wasm2c/wasm-rt-exceptions*
