# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

system_headers = [
    "A4Stuff.h",
    "activscp.h",
    "AEDataModel.h",
    "AEObjects.h",
    "AEPackObject.h",
    "AERegistry.h",
    "AEUtils.h",
    "afxcmn.h",
    "afxcoll.h",
    "afxcview.h",
    "afxdisp.h",
    "afxdtctl.h",
    "afxext.h",
    "afxmt.h",
    "afxpriv.h",
    "afxtempl.h",
    "afxwin.h",
    "Aliases.h",
    "all.h",
    "alloca.h",
    "alloc.h",
    "alsa/asoundlib.h",
    "ansi_parms.h",
    "a.out.h",
    "app/Cursor.h",
    "Appearance.h",
    "AppFileInfo.h",
    "AppKit.h",
    "AppleEvents.h",
    "Application.h",
    "app/Message.h",
    "app/MessageRunner.h",
    "arpa/inet.h",
    "arpa/nameser.h",
    "array",
    "asm/page.h",
    "asm/sigcontext.h",
    "asm/signal.h",
    "ASRegistry.h",
    "assert.h",
    "atk/atk.h",
    "atlcom.h",
    "atlconv.h",
    "atlctl.cpp",
    "atlctl.h",
    "ATLCTL.H",
    "atlhost.h",
    "atlimpl.cpp",
    "atlwin.cpp",
    "atomic.h",
    "ATSTypes.h",
    "ATSUnicode.h",
    "Balloons.h",
    "base64.h",
    "base/pblock.h",
    "base/PCR_Base.h",
    "base/session.h",
    "basetyps.h",
    "be/app/Application.h",
    "Beep.h",
    "be/kernel/image.h",
    "be/kernel/OS.h",
    "bfd.h",
    "Bitmap.h",
    "bitset",
    "blapi.h",
    "blapit.h",
    "bsd/libc.h",
    "bsd/syscall.h",
    "bstring.h",
    "builtin.h",
    "Button.h",
    "byteswap.h",
    "cairo-atsui.h",
    "cairo-beos.h",
    "cairo-directfb.h",
    "cairo-ft.h",
    "cairo-glitz.h",
    "cairo-gobject.h",
    "cairo.h",
    "cairo-pdf.h",
    "cairo-ps.h",
    "cairo-qpainter.h",
    "cairo-qt.h",
    "cairo-quartz.h",
    "cairo-tee.h",
    "cairo-win32.h",
    "cairo-xlib.h",
    "cairo-xlib-xrender.h",
    "callconv.h",
    "Carbon/Carbon.h",
    "CarbonEvents.h",
    "Carbon.h",
    "c_asm.h",
    "cctype",
    "cderr.h",
    "cerrno",
    "certdb.h",
    "cert.h",
    "certt.h",
    "CFBase.h",
    "CFBundle.h",
    "CFData.h",
    "CFDictionary.h",
    "cf.h",
    "CFNumber.h",
    "CFPlugIn.h",
    "CFPreferences.h",
    "CFString.h",
    "CFURL.h",
    "CGAffineTransform.h",
    "CheckBox.h",
    "ciferfam.h",
    "Clipboard.h",
    "cmmf.h",
    "cmmft.h",
    "cmplrs/stsupport.h",
    "cms.h",
    "cmsreclist.h",
    "cmst.h",
    "Cocoa/Cocoa.h",
    "CodeFragments.h",
    "comdef.h",
    "commctrl.h",
    "COMMCTRL.H",
    "commdlg.h",
    "compat.h",
    "complex",
    "condapi.h",
    "ConditionalMacros.h",
    "conio.h",
    "console.h",
    "ControlDefinitions.h",
    "Controls.h",
    "CoreFoundation/CoreFoundation.h",
    "CoreServices/CoreServices.h",
    "CPalmRec.cpp",
    "Cpalmrec.h",
    "CPCatgry.cpp",
    "CPDbBMgr.h",
    "CPString.cpp",
    "CPString.h",
    "crtdbg.h",
    "crt_externs.h",
    "crypt.h",
    "cryptohi.h",
    "cryptoht.h",
    "cstddef",
    "ctime",
    "ctype.h",
    "curl/curl.h",
    "curl/easy.h",
    "curses.h",
    "cxxabi.h",
    "DateTimeUtils.h",
    "dbus/dbus.h",
    "ddeml.h",
    "Debug.h",
    "dem.h",
    "descrip.h",
    "Devices.h",
    "dfb_types.h",
    "dfiff.h",
    "dgiff.h",
    "Dialogs.h",
    "direct/build.h",
    "direct/clock.h",
    "direct/conf.h",
    "direct/debug.h",
    "direct/direct.h",
    "directfb_keyboard.h",
    "directfb_strings.h",
    "direct.h",
    "direct/hash.h",
    "direct/interface.h",
    "direct/interface_implementation.h",
    "direct/list.h",
    "direct/log.h",
    "direct/memcpy.h",
    "direct/mem.h",
    "direct/messages.h",
    "direct/modules.h",
    "direct/serial.h",
    "direct/signals.h",
    "direct/stream.h",
    "direct/system.h",
    "direct/thread.h",
    "direct/trace.h",
    "direct/tree.h",
    "direct/types.h",
    "direct/utf8.h",
    "direct/util.h",
    "dirent.h",
    "DiskInit.h",
    "dlfcn.h",
    "dlgs.h",
    "dl.h",
    "docobj.h",
    "dos/dosextens.h",
    "dos.h",
    "Drag.h",
    "DriverServices.h",
    "DriverSynchronization.h",
    "DropInPanel.h",
    "dvidef.h",
    "ecl-exp.h",
    "elf.h",
    "endian.h",
    "Entry.h",
    "err.h",
    "errno.h",
    "Errors.h",
    "Events.h",
    "exception",
    "exdisp.h",
    "ExDisp.h",
    "exe386.h",
    "execinfo.h",
    "extras.h",
    "fcntl.h",
    "features.h",
    "fenv.h",
    "ffi.h",
    "fibdef.h",
    "File.h",
    "filehdr.h",
    "files.h",
    "Files.h",
    "FindDirectory.h",
    "Finder.h",
    "FinderRegistry.h",
    "FixMath.h",
    "float.h",
    "fnmatch.h",
    "Folders.h",
    "fontconfig/fcfreetype.h",
    "fontconfig/fontconfig.h",
    "Font.h",
    "Fonts.h",
    "fp.h",
    "fpieee.h",
    "frame/log.h",
    "frame/req.h",
    "freetype/freetype.h",
    "freetype/ftadvanc.h",
    "freetype/ftbitmap.h",
    "freetype/ftcache.h",
    "freetype/ftfntfmt.h",
    "freetype/ftglyph.h",
    "freetype/ftlcdfil.h",
    "freetype/ftoutln.h",
    "freetype/ftsizes.h",
    "freetype/ftsynth.h",
    "freetype/ftxf86.h",
    "freetype.h",
    "freetype/t1tables.h",
    "freetype/ttnameid.h",
    "freetype/tttables.h",
    "fribidi/fribidi.h",
    "FSp_fopen.h",
    "fstream",
    "fstream.h",
    "ft2build.h",
    "ftadvanc.h",
    "ftbitmap.h",
    "ftcache.h",
    "ftfntfmt.h",
    "ftglyph.h",
    "ftlcdfil.h",
    "ftoutln.h",
    "fts.h",
    "ftsizes.h",
    "ftsynth.h",
    "ftxf86.h",
    "fusion/arena.h",
    "fusion/build.h",
    "fusion/call.h",
    "fusion/conf.h",
    "fusion/fusion.h",
    "fusion/fusion_internal.h",
    "fusion/hash.h",
    "fusion/lock.h",
    "fusion/object.h",
    "fusion/property.h",
    "fusion/protocol.h",
    "fusion/reactor.h",
    "fusion/ref.h",
    "fusion/shmalloc.h",
    "fusion/shm/pool.h",
    "fusion/shm/shm.h",
    "fusion/shm/shm_internal.h",
    "fusion/types.h",
    "fusion/vector.h",
    "gconf/gconf-client.h",
    "Gdiplus.h",
    "gdk/gdkdirectfb.h",
    "gdk/gdk.h",
    "gdk/gdkkeysyms.h",
    "gdk/gdkprivate.h",
    "gdk/gdkwayland.h",
    "gdk-pixbuf/gdk-pixbuf.h",
    "Gestalt.h",
    "getopt.h",
    "gio/gdesktopappinfo.h",
    "gio/gio.h",
    "glibconfig.h",
    "glib.h",
    "glib-object.h",
    "glob.h",
    "gmodule.h",
    "gnome.h",
    "gnu/libc-version.h",
    "grp.h",
    "gssapi_generic.h",
    "gssapi/gssapi_generic.h",
    "gssapi/gssapi.h",
    "gssapi.h",
    "gst/app/gstappsink.h",
    "gst/app/gstappsrc.h",
    "gst/gst.h",
    "gst/video/video.h",
    "gtk/gtk.h",
    "gtk/gtkunixprint.h",
    "hasht.h",
    "HIToolbox/HIToolbox.h",
    "hlink.h",
    "ia64/sys/inline.h",
    "Icons.h",
    "iconv.h",
    "ieeefp.h",
    "ifaddrs.h",
    "image.h",
    "imagehlp.h",
    "imm.h",
    "initguid.h",
    "initializer_list",
    "InterfaceDefs.h",
    "InternetConfig.h",
    "IntlResources.h",
    "ints.h",
    "intshcut.h",
    "inttypes.h",
    "iodef.h",
    "io.h",
    "IOKit/IOKitLib.h",
    "IOKit/IOMessage.h",
    "IOKit/pwr_mgt/IOPMLib.h",
    "iomanip",
    "iostream.h",
    "jar-ds.h",
    "jarfile.h",
    "jar.h",
    "JavaControl.h",
    "JavaEmbedding/JavaControl.h",
    "JavaVM/jni.h",
    "JManager.h",
    "JNIEnvTests.h",
    "jni.h",
    "JVMManagerTests.h",
    "Kerberos/Kerberos.h",
    "kernel/image.h",
    "kernel/OS.h",
    "key.h",
    "keyhi.h",
    "keyt.h",
    "keythi.h",
    "kvm.h",
    "LAction.h",
    "langinfo.h",
    "LApplication.h",
    "LArray.h",
    "LArrayIterator.h",
    "LAttachable.h",
    "LAttachment.h",
    "LaunchServices.h",
    "lber.h",
    "LBroadcaster.h",
    "LButton.h",
    "lcache.h",
    "LCaption.h",
    "LCheckBox.h",
    "LCicnButton.h",
    "LClipboard.h",
    "LCommander.h",
    "LComparator.h",
    "LControl.h",
    "ldap.h",
    "ldaplog.h",
    "ldappr.h",
    "ldap_ssl.h",
    "LDataStream.h",
    "ldfcn.h",
    "LDialogBox.h",
    "ldif.h",
    "LDocApplication.h",
    "LDocument.h",
    "LDragAndDrop.h",
    "LDragTask.h",
    "LEditField.h",
    "LEditText.h",
    "LEventDispatcher.h",
    "LFile.h",
    "LFileStream.h",
    "LFileTypeList.h",
    "LFocusBox.h",
    "LGrafPortView.h",
    "LHandleStream.h",
    "libavutil/mem.h",
    "libavutil/cpu.h",
    "libc_r.h",
    "libelf.h",
    "libelf/libelf.h",
    "libgen.h",
    "libgnome/gnome-url.h",
    "libgnome/libgnome.h",
    "libgnomeui/gnome-icon-lookup.h",
    "libgnomeui/gnome-icon-theme.h",
    "libgnomeui/gnome-ui-init.h",
    "libutil.h",
    "limits.h",
    "link.h",
    "linux/ioprio.h",
    "linux/kernel.h",
    "linux/limits.h",
    "linux/rtc.h",
    "linux/version.h",
    "List.h",
    "Lists.h",
    "LListBox.h",
    "LListener.h",
    "LMenuBar.h",
    "LMenu.h",
    "LModelDirector.h",
    "LModelObject.h",
    "LModelProperty.h",
    "loader.h",
    "locale",
    "locale.h",
    "LOffscreenView.h",
    "logkeys.h",
    "logstrng.h",
    "Looper.h",
    "LowMem.h",
    "LPane.h",
    "LPeriodical.h",
    "LPicture.h",
    "LPlaceHolder.h",
    "LPrintout.h",
    "LProgressBar.h",
    "LPushButton.h",
    "LRadioGroup.h",
    "LRadioGroupView.h",
    "LRunArray.h",
    "LScroller.h",
    "LSharable.h",
    "LSingleDoc.h",
    "LStaticText.h",
    "LStdControl.h",
    "LStream.h",
    "LString.h",
    "LTabGroup.h",
    "LTabGroupView.h",
    "LTableArrayStorage.h",
    "LTableMonoGeometry.h",
    "LTableSingleSelector.h",
    "LTableView.h",
    "LTextEditView.h",
    "LTextTableView.h",
    "LUndoer.h",
    "LVariableArray.h",
    "LView.h",
    "LWindow.h",
    "m68881.h",
    "MacErrors.h",
    "MacHeadersCarbon.h",
    "machine/ansi.h",
    "machine/builtins.h",
    "machine/clock.h",
    "machine/endian.h",
    "machine/frame.h",
    "machine/inline.h",
    "machine/limits.h",
    "machine/signal.h",
    "machine/trap.h",
    "mach/mach_host.h",
    "mach/mach_init.h",
    "mach/mach_interface.h",
    "mach/mach_port.h",
    "mach-o/dyld.h",
    "MacLocales.h",
    "MacMemory.h",
    "MacTCP.h",
    "MacTypes.h",
    "MacWindows.h",
    "malloc.h",
    "malloc_np.h",
    "mapicode.h",
    "mapidefs.h",
    "mapiguid.h",
    "mapi.h",
    "mapitags.h",
    "mapiutil.h",
    "mapix.h",
    "Math64.h",
    "math.h",
    "mbstring.h",
    "mem.h",
    "memory.h",
    "Memory.h",
    "MenuBar.h",
    "Menu.h",
    "Menus.h",
    "Message.h",
    "Mime.h",
    "MixedMode.h",
    "mlang.h",
    "mmsystem.h",
    "model.h",
    "Movies.h",
    "mpw/errno.h",
    "mshtmhst.h",
    "mshtml.h",
    "mswsock.h",
    "Multiprocessing.h",
    "mutex.h",
    "Navigation.h",
    "ncompat.h",
    "ncurses.h",
    "netCore.h",
    "netdb.h",
    "net/if.h",
    "netinet/in.h",
    "netinet/in_systm.h",
    "netinet/tcp.h",
    "newexe.h",
    "new.h",
    "nl_types.h",
    "NodeInfo.h",
    "nspr.h",
    "nssb64.h",
    "nssb64t.h",
    "nssbase.h",
    "nssbaset.h",
    "nssck.api",
    "nssckbi.h",
    "nssckepv.h",
    "nssckft.h",
    "nssckfwc.h",
    "nssckfw.h",
    "nssckfwt.h",
    "nssckg.h",
    "nssckmdt.h",
    "nssckt.h",
    "nss.h",
    "nssilckt.h",
    "nssilock.h",
    "nsslocks.h",
    "nssrwlk.h",
    "nssrwlkt.h",
    "nssutil.h",
    "nsswitch.h",
    "objbase.h",
    "objidl.h",
    "Objsafe.h",
    "ocsp.h",
    "ocspt.h",
    "ojiapitests.h",
    "ole2.h",
    "oleidl.h",
    "OpenGL/OpenGL.h",
    "OpenTptInternet.h",
    "OpenTransport.h",
    "OS.h",
    "osreldate.h",
    "OSUtils.h",
    "p12.h",
    "p12plcy.h",
    "p12t.h",
    "Packages.h",
    "Palettes.h",
    "PALM_CMN.H",
    "pango/pango-break.h",
    "pango/pangofc-decoder.h",
    "pango/pangofc-font.h",
    "pango/pangofc-fontmap.h",
    "pango/pango-fontmap.h",
    "pango/pango.h",
    "pango/pango-modules.h",
    "pango/pango-utils.h",
    "pango/pangoxft.h",
    "pascal.h",
    "Patches.h",
    "Path.h",
    "pcfs/pc_dir.h",
    "Pgenerr.h",
    "PGenErr.h",
    "Ph.h",
    "pixman.h",
    "pk11func.h",
    "pk11pqg.h",
    "pk11priv.h",
    "pk11pub.h",
    "pk11sdr.h",
    "pkcs11f.h",
    "pkcs11.h",
    "pkcs11n.h",
    "pkcs11p.h",
    "pkcs11t.h",
    "pkcs11u.h",
    "pkcs12.h",
    "pkcs12t.h",
    "pkcs7t.h",
    "plarena.h",
    "plarenas.h",
    "plbase64.h",
    "plerror.h",
    "plgetopt.h",
    "plhash.h",
    "plstr.h",
    "PLStringFuncs.h",
    "PMApplication.h",
    "pmddim.h",
    "poll.h",
    "Polygon.h",
    "portable.h",
    "port.h",
    "portreg.h",
    "Power.h",
    "PP_ClassHeaders.cp",
    "PP_Constants.h",
    "PPCToolbox.h",
    "PP_DebugHeaders.cp",
    "PP_KeyCodes.h",
    "PP_Macros.h",
    "PP_Messages.h",
    "PP_Prefix.h",
    "PP_Resources.h",
    "PP_Types.h",
    "pratom.h",
    "prbit.h",
    "prclist.h",
    "prcmon.h",
    "prcountr.h",
    "prcpucfg.h",
    "prcvar.h",
    "prdtoa.h",
    "preenc.h",
    "prenv.h",
    "prerr.h",
    "prerror.h",
    "prinet.h",
    "prinit.h",
    "prinrval.h",
    "Printing.h",
    "Print/PMPrintingDialogExtensions.h",
    "prio.h",
    "pripcsem.h",
    "private",
    "prlink.h",
    "prlock.h",
    "prlog.h",
    "prlong.h",
    "prmem.h",
    "prmon.h",
    "prmwait.h",
    "prnetdb.h",
    "Processes.h",
    "process.h",
    "Process.h",
    "prolock.h",
    "proto/dos.h",
    "proto/exec.h",
    "prpdce.h",
    "prprf.h",
    "prproces.h",
    "prrng.h",
    "prrwlock.h",
    "prshma.h",
    "prshm.h",
    "prsystem.h",
    "prthread.h",
    "prtime.h",
    "prtpool.h",
    "prtrace.h",
    "prtypes.h",
    "prvrsion.h",
    "prwin.h",
    "psap.h",
    "Pt.h",
    "pthread.h",
    "pthread_np.h",
    "pulse/pulseaudio.h",
    "pwd.h",
    "Python.h",
    "QDOffscreen.h",
    "queue",
    "Quickdraw.h",
    "QuickDraw.h",
    "QuickTimeComponents.h",
    "quipu/attr.h",
    "regex.h",
    "Region.h",
    "resolv.h",
    "Resources.h",
    "Retrace.h",
    "rld_interface.h",
    "Roster.h",
    "rpc.h",
    "rpcproxy.h",
    "rpc/types.h",
    "sane/sane.h",
    "sane/sanei.h",
    "sane/saneopts.h",
    "sanitizer/asan_interface.h",
    "sanitizer/common_interface_defs.h",
    "sched.h",
    "Scrap.h",
    "Screen.h",
    "Script.h",
    "ScrollBar.h",
    "secasn1.h",
    "secasn1t.h",
    "seccomon.h",
    "secder.h",
    "secdert.h",
    "secdig.h",
    "secdigt.h",
    "secerr.h",
    "sec.h",
    "sechash.h",
    "secitem.h",
    "secmime.h",
    "secmod.h",
    "secmodt.h",
    "secoid.h",
    "secoidt.h",
    "secpkcs5.h",
    "secpkcs7.h",
    "secport.h",
    "secrng.h",
    "security.h",
    "secutil.h",
    "semaphore.h",
    "servprov.h",
    "setjmp.h",
    "SFNTLayoutTypes.h",
    "SFNTTypes.h",
    "sha1.h",
    "share.h",
    "shellapi.h",
    "shlguid.h",
    "shlobj.h",
    "shsign.h",
    "sigcontext.h",
    "signal.h",
    "SimpleGameSound.h",
    "SIOUX.h",
    "size_t.h",
    "smime.h",
    "someincludefile.h",
    "soundcard.h",
    "Sound.h",
    "soundtouch/SoundTouchFactory.h",
    "soundtouch/SoundTouch.h",
    "spawn.h",
    "sqlite3.h",
    "sqlite3_static_ext.h",
    "sslerr.h",
    "ssl.h",
    "sslproto.h",
    "sslt.h",
    "sstream",
    "StandardFile.h",
    "starlet.h",
    "stat.h",
    "statreg.cpp",
    "statreg.h",
    "stdarg.h",
    "stdbool.h",
    "stddef.h",
    "stdint.h",
    "stdio.h",
    "stdlib.h",
    "storage/FindDirectory.h",
    "StorageKit.h",
    "StringCompare.h",
    "string.h",
    "String.h",
    "strings.h",
    "Strings.h",
    "StringView.h",
    "stropts.h",
    "strstrea.h",
    "structs.h",
    "stsdef.h",
    "SupportDefs.h",
    "support/String.h",
    "support/SupportDefs.h",
    "support/TLS.h",
    "svrcore.h",
    "symconst.h",
    "sym.h",
    "synch.h",
    "syncmgr.h",
    "sys/atomic_op.h",
    "sys/bitypes.h",
    "sys/byteorder.h",
    "syscall.h",
    "sys/cdefs.h",
    "sys/cfgodm.h",
    "sys/elf.h",
    "sys/endian.h",
    "sys/epoll.h",
    "sys/errno.h",
    "sys/eventfd.h",
    "sys/fault.h",
    "sys/fcntl.h",
    "sys/file.h",
    "sys/filio.h",
    "sys/frame.h",
    "sys/immu.h",
    "sys/inotify.h",
    "sys/inttypes.h",
    "sys/ioccom.h",
    "sys/ioctl.h",
    "sys/ipc.h",
    "sys/klog.h",
    "sys/ldr.h",
    "sys/link.h",
    "sys/locking.h",
    "syslog.h",
    "sys/lwp.h",
    "sys/machine.h",
    "sys/mman.h",
    "sys/mmu.h",
    "sys/mount.h",
    "sys/mpctl.h",
    "sys/msg.h",
    "sys/param.h",
    "sys/pda.h",
    "sys/poll.h",
    "sys/ppc.h",
    "sys/prctl.h",
    "sys/priv.h",
    "sys/procfs.h",
    "sys/pstat.h",
    "sys/ptrace.h",
    "sys/queue.h",
    "sys/quota.h",
    "sys/reboot.h",
    "sys/reg.h",
    "sys/regset.h",
    "sys/resource.h",
    "sys/sched.h",
    "sys/select.h",
    "sys/sem.h",
    "sys/sendfile.h",
    "sys/shm.h",
    "sys/siginfo.h",
    "sys/signal.h",
    "sys/socket.h",
    "sys/sockio.h",
    "sys/sparc/frame.h",
    "sys/stack.h",
    "sys/statfs.h",
    "sys/stat.h",
    "sys/statvfs.h",
    "sys/syscall.h",
    "sys/sysctl.h",
    "sys/sysinfo.h",
    "sys/sysmacros.h",
    "sys/sysmp.h",
    "sys/syssgi.h",
    "sys/systeminfo.h",
    "sys/system_properties.h",
    "sys/thr.h",
    "sys/timeb.h",
    "sys/time.h",
    "sys/times.h",
    "sys/ttycom.h",
    "sys/types.h",
    "sys/ucontext.h",
    "sys/uio.h",
    "sys/un.h",
    "sys/unistd.h",
    "sys/user.h",
    "sys/utsname.h",
    "sys/vfs.h",
    "sys/wait.h",
    "t1tables.h",
    "tables.h",
    "TArray.h",
    "TArrayIterator.h",
    "task.h",
    "tchar.h",
    "TCHAR.H",
    "termios.h",
    "TextCommon.h",
    "TextEdit.h",
    "TextEncodingConverter.h",
    "TextServices.h",
    "TextUtils.h",
    "TextView.h",
    "th/PCR_Th.h",
    "thread.h",
    "ThreadManagerTests.h",
    "Threads.h",
    "time.h",
    "Timer.h",
    "tlhelp32.h",
    "ToolUtils.h",
    "tr1/functional",
    "trace.h",
    "Traps.h",
    "ttnameid.h",
    "tttables.h",
    "typeinfo",
    "types.h",
    "Types.h",
    "UAppleEventsMgr.h",
    "UAttachments.h",
    "ucontext.h",
    "uconv.h",
    "UCursor.h",
    "UDebugging.h",
    "UDesktop.h",
    "UDrawingState.h",
    "UDrawingUtils.h",
    "UEnvironment.h",
    "UEventMgr.h",
    "UException.h",
    "UExtractFromAEDesc.h",
    "UGWorld.h",
    "UKeyFilters.h",
    "ulocks.h",
    "ulserrno.h",
    "UMemoryMgr.h",
    "UModalDialogs.h",
    "UNavServicesDialogs.h",
    "UnicodeBlockObjects.h",
    "UnicodeConverter.h",
    "UnicodeUtilities.h",
    "unidef.h",
    "unikbd.h",
    "unistd.h",
    "unix.h",
    "unixio.h",
    "unknwn.h",
    "unwind.h",
    "UPrinting.h",
    "UQuickTime.h",
    "UReanimator.h",
    "URegions.h",
    "URegistrar.h",
    "UResourceMgr.h",
    "urlhist.h",
    "urlmon.h",
    "UScrap.h",
    "UScreenPort.h",
    "UTCUtils.h",
    "UTETextAction.h",
    "UTEViewTextAction.h",
    "UTextEdit.h",
    "UTextTraits.h",
    "utilmodt.h",
    "utilpars.h",
    "utilparst.h",
    "utilrename.h",
    "utime.h",
    "UWindows.h",
    "values.h",
    "varargs.h",
    "vcclr.h",
    "View.h",
    "Volume.h",
    "wab.h",
    "wait.h",
    "wchar.h",
    "wctype.h",
    "winbase.h",
    "win/compobj.h",
    "windef.h",
    "Window.h",
    "windows.h",
    "Windows.h",
    "windowsx.h",
    "Wininet.h",
    "winnls.h",
    "winperf.h",
    "winreg.h",
    "Winreg.h",
    "winsock2.h",
    "winsock.h",
    "winspool.h",
    "winsvc.h",
    "winuser.h",
    "winver.h",
    "wmem.h",
    "workbench/startup.h",
    "wtypes.h",
    "wx/image.h",
    "wx/listctrl.h",
    "wx/log.h",
    "wx/toolbar.h",
    "wx/wx.h",
    "wx/xrc/xmlres.h",
    "xlocale.h",
    "zmouse.h",
    "vorbis/codec.h",
    "opus/opus.h",
    "opus/opus_multistream.h",
    "ogg/ogg.h",
    "vpx/svc_context.h",
    "vpx/vp8.h",
    "vpx/vp8cx.h",
    "vpx/vp8dx.h",
    "vpx/vpx_codec.h",
    "vpx/vpx_decoder.h",
    "vpx/vpx_encoder.h",
    "vpx/vpx_frame_buffer.h",
    "vpx/vpx_image.h",
    "vpx_mem/vpx_mem.h",
    "aom/aom_image.h",
    "aom/aomdx.h",
    "aom/aomcx.h",
    "aom/aom_codec.h",
    "aom/aom_decoder.h",
    "aom/aom_encoder.h",
]

if CONFIG["MOZ_X11"]:
    system_headers += [
        "gdk/gdkx.h",
        "gtk/gtkx.h",
        "X11/cursorfont.h",
        "X11/extensions/Print.h",
        "X11/extensions/scrnsaver.h",
        "X11/extensions/shape.h",
        "X11/extensions/Xcomposite.h",
        "X11/extensions/Xdamage.h",
        "X11/extensions/Xfixes.h",
        "X11/extensions/Xrandr.h",
        "X11/extensions/XShm.h",
        "X11/extensions/XTest.h",
        "X11/ImUtil.h",
        "X11/Intrinsic.h",
        "X11/keysymdef.h",
        "X11/keysym.h",
        "X11/Shell.h",
        "X11/StringDefs.h",
        "X11/Xatom.h",
        "X11/Xft/Xft.h",
        "X11/Xfuncproto.h",
        "X11/X.h",
        "X11/XKBlib.h",
        "X11/Xlib.h",
        "X11/Xlibint.h",
        "X11/Xlib-xcb.h",
        "X11/Xlocale.h",
        "X11/Xos.h",
        "X11/Xutil.h",
        "xcb/shm.h",
        "xcb/xcb.h",
    ]

if CONFIG["OS_TARGET"] == "Android":
    system_headers += [
        "android/api-level.h",
        "android/ashmem.h",
        "android_audio/AudioSystem.h",
        "android/log.h",
        "android/looper.h",
        "android/native_window.h",
        "android/native_window_jni.h",
        "audio_effects/effect_aec.h",
        "audio_effects/effect_ns.h",
        "AudioParameter.h",
        "AudioSystem.h",
        "AudioTrack.h",
        "avc_utils.h",
        "binder/Binder.h",
        "binder/BinderService.h",
        "binder/IBinder.h",
        "binder/IInterface.h",
        "binder/IMemory.h",
        "binder/IPCThreadState.h",
        "binder/IPermissionController.h",
        "binder/IServiceManager.h",
        "binder/Parcel.h",
        "binder/ProcessState.h",
        "camera/Camera.h",
        "camera/CameraParameters.h",
        "ColorConverter.h",
        "cutils/android_reboot.h",
        "cutils/atomic.h",
        "cutils/compiler.h",
        "cutils/log.h",
        "cutils/native_handle.h",
        "cutils/properties.h",
        "cutils/sockets.h",
        "foundation/ABase.h",
        "foundation/ABitReader.h",
        "foundation/ABuffer.h",
        "foundation/ADebug.h",
        "foundation/AHandler.h",
        "foundation/AHandlerReflector.h",
        "foundation/ALooper.h",
        "foundation/AMessage.h",
        "foundation/AString.h",
        "foundation/base64.h",
        "foundation/hexdump.h",
        "gui/BufferQueue.h",
        "gui/ConsumerBase.h",
        "gui/GraphicBufferAlloc.h",
        "gui/IConsumerListener.h",
        "gui/IGraphicBufferAlloc.h",
        "gui/IGraphicBufferProducer.h",
        "gui/ISurfaceComposerClient.h",
        "gui/ISurfaceComposer.h",
        "gui/ISurfaceTexture.h",
        "gui/SurfaceComposerClient.h",
        "gui/Surface.h",
        "gui/SurfaceTextureClient.h",
        "hardware/audio.h",
        "hardware/gralloc.h",
        "hardware/hardware.h",
        "hardware/hwcomposer.h",
        "hardware_legacy/power.h",
        "hardware_legacy/uevent.h",
        "hardware_legacy/vibrator.h",
        "hardware/lights.h",
        "hardware/power.h",
        "HTTPBase.h",
        "linux/android_alarm.h",
        "linux/ashmem.h",
        "media/AudioEffect.h",
        "media/AudioSystem.h",
        "media/ICrypto.h",
        "media/IOMX.h",
        "media/MediaProfiles.h",
        "media/MediaRecorderBase.h",
        "media/openmax/OMX_Audio.h",
        "media/stagefright/AACWriter.h",
        "media/stagefright/AMRWriter.h",
        "media/stagefright/AudioSource.h",
        "media/stagefright/DataSource.h",
        "media/stagefright/foundation/ABase.h",
        "media/stagefright/foundation/ABitReader.h",
        "media/stagefright/foundation/ABuffer.h",
        "media/stagefright/foundation/ADebug.h",
        "media/stagefright/foundation/AHandler.h",
        "media/stagefright/foundation/AHandlerReflector.h",
        "media/stagefright/foundation/ALooper.h",
        "media/stagefright/foundation/AMessage.h",
        "media/stagefright/foundation/AString.h",
        "media/stagefright/foundation/base64.h",
        "media/stagefright/foundation/hexdump.h",
        "media/stagefright/MediaBufferGroup.h",
        "media/stagefright/MediaBuffer.h",
        "media/stagefright/MediaCodec.h",
        "media/stagefright/MediaCodecList.h",
        "media/stagefright/MediaCodecSource.h",
        "media/stagefright/MediaDefs.h",
        "media/stagefright/MediaErrors.h",
        "media/stagefright/MediaExtractor.h",
        "media/stagefright/MediaSource.h",
        "media/stagefright/MediaWriter.h",
        "media/stagefright/MetaData.h",
        "media/stagefright/MPEG2TSWriter.h",
        "media/stagefright/MPEG4Writer.h",
        "media/stagefright/OMXClient.h",
        "media/stagefright/OMXCodec.h",
        "media/stagefright/openmax/OMX_Core.h",
        "media/stagefright/openmax/OMX_Index.h",
        "media/stagefright/openmax/OMX_IVCommon.h",
        "media/stagefright/openmax/OMX_Types.h",
        "media/stagefright/openmax/OMX_Video.h",
        "media/stagefright/Utils.h",
        "OMX_Component.h",
        "OMX.h",
        "stagefright/AACWriter.h",
        "stagefright/AMRWriter.h",
        "stagefright/AudioSource.h",
        "stagefright/DataSource.h",
        "stagefright/foundation/ABase.h",
        "stagefright/foundation/ABitReader.h",
        "stagefright/foundation/ABuffer.h",
        "stagefright/foundation/ADebug.h",
        "stagefright/foundation/AHandler.h",
        "stagefright/foundation/AHandlerReflector.h",
        "stagefright/foundation/ALooper.h",
        "stagefright/foundation/AMessage.h",
        "stagefright/foundation/AString.h",
        "stagefright/foundation/base64.h",
        "stagefright/foundation/hexdump.h",
        "stagefright/MediaBufferGroup.h",
        "stagefright/MediaBuffer.h",
        "stagefright/MediaCodec.h",
        "stagefright/MediaDefs.h",
        "stagefright/MediaErrors.h",
        "stagefright/MediaExtractor.h",
        "stagefright/MediaSource.h",
        "stagefright/MediaWriter.h",
        "stagefright/MetaData.h",
        "stagefright/MPEG2TSWriter.h",
        "stagefright/MPEG4Writer.h",
        "stagefright/OMXClient.h",
        "stagefright/OMXCodec.h",
        "stagefright/openmax/OMX_Component.h",
        "stagefright/openmax/OMX_Core.h",
        "stagefright/openmax/OMX_Index.h",
        "stagefright/openmax/OMX_IVCommon.h",
        "stagefright/openmax/OMX_Types.h",
        "stagefright/openmax/OMX_Video.h",
        "stagefright/Utils.h",
        "suspend/autosuspend.h",
        "system/audio.h",
        "system/graphics.h",
        "system/window.h",
        "sysutils/NetlinkEvent.h",
        "ui/ANativeObjectBase.h",
        "ui/egl/android_natives.h",
        "ui/Fence.h",
        "ui/FramebufferNativeWindow.h",
        "ui/GraphicBuffer.h",
        "ui/Rect.h",
        "ui/Region.h",
        "utils/BitSet.h",
        "utils/CallStack.h",
        "utils/Errors.h",
        "utils/FileMap.h",
        "utils/KeyedVector.h",
        "utils/List.h",
        "utils/Log.h",
        "utils/Looper.h",
        "utils/PropertyMap.h",
        "utils/RefBase.h",
        "utils/String16.h",
        "utils/String8.h",
        "utils/TextOutput.h",
        "utils/threads.h",
        "utils/Timers.h",
        "utils/Trace.h",
        "utils/TypeHelpers.h",
        "utils/Unicode.h",
        "utils/Vector.h",
        "utils/VectorImpl.h",
        "vr/gvr/capi/include/gvr_controller.h",
        "vr/gvr/capi/include/gvr.h",
    ]

if CONFIG["MOZ_JACK"]:
    system_headers += [
        "jack/jack.h",
        "jack/statistics.h",
    ]

if CONFIG["MOZ_SNDIO"]:
    system_headers += [
        "sndio.h",
    ]

if CONFIG["MOZ_SYSTEM_JPEG"]:
    system_headers += [
        "jpeglib.h",
    ]

if CONFIG["MOZ_SYSTEM_PNG"]:
    system_headers += [
        "png.h",
    ]

if CONFIG["MOZ_SYSTEM_WEBP"]:
    system_headers += [
        "webp/decode.h",
        "webp/demux.h",
        "webp/mux_types.h",
        "webp/types.h",
    ]

if CONFIG["MOZ_SYSTEM_ZLIB"]:
    system_headers += [
        "zlib.h",
    ]

if CONFIG["MOZ_SYSTEM_LIBEVENT"]:
    system_headers += [
        "event2/event_compat.h",
        "event2/event.h",
        "event2/event_struct.h",
        "event.h",
    ]
else:
    system_headers += [
        "sys/event.h",
    ]

if CONFIG["MOZ_ENABLE_LIBPROXY"]:
    system_headers += [
        "proxy.h",
    ]

if CONFIG["MOZ_SYSTEM_ICU"]:
    system_headers += [
        "unicode/calendar.h",
        "unicode/datefmt.h",
        "unicode/dtfmtsym.h",
        "unicode/locid.h",
        "unicode/numberformatter.h",
        "unicode/numsys.h",
        "unicode/plurrule.h",
        "unicode/putil.h",
        "unicode/timezone.h",
        "unicode/ucal.h",
        "unicode/uchar.h",
        "unicode/uclean.h",
        "unicode/ucol.h",
        "unicode/ucurr.h",
        "unicode/udat.h",
        "unicode/udata.h",
        "unicode/udateintervalformat.h",
        "unicode/udatpg.h",
        "unicode/udisplaycontext.h",
        "unicode/uldnames.h",
        "unicode/ulistformatter.h",
        "unicode/uenum.h",
        "unicode/uformattedvalue.h",
        "unicode/umachine.h",
        "unicode/uniset.h",
        "unicode/unistr.h",
        "unicode/unorm.h",
        "unicode/unum.h",
        "unicode/unumberformatter.h",
        "unicode/uobject.h",
        "unicode/upluralrules.h",
        "unicode/ureldatefmt.h",
        "unicode/ures.h",
        "unicode/ustring.h",
        "unicode/utypes.h",
    ]

if CONFIG["MOZ_WAYLAND"]:
    system_headers += [
        "xkbcommon/xkbcommon.h",
        "wayland-client.h",
        "wayland-egl.h",
        "wayland-util.h",
    ]

if CONFIG["OS_TARGET"] in ("Android", "Linux", "FreeBSD"):
    system_headers += [
        "sys/auxv.h",
    ]

if CONFIG["OS_TARGET"] == "Linux" and CONFIG["TARGET_CPU"].startswith("mips"):
    system_headers += [
        "sys/cachectl.h",
    ]

if CONFIG["OS_TARGET"] == "FreeBSD":
    system_headers += [
        "sys/capsicum.h",
    ]

if CONFIG["MOZ_APP_SYSTEM_HEADERS"]:
    include("../" + CONFIG["MOZ_BUILD_APP"] + "/app-system-headers.mozbuild")

if CONFIG["MOZ_PERFETTO"]:
    system_headers += [
        "sys/timerfd.h",
    ]
