# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# This list contains the of STL headers that have been reviewed for exception
# safety and approved. See
#
#   https://bugzilla.mozilla.org/show_bug.cgi?id=551254
#
# At build time, each header listed here is converted into a "wrapper
# header" that is installed into dist/stl_includes.
#
# If you would like to request a new STL header <foo> be added, please
# file a Core:XPCOM bug with a title like "STL: Review exception
# safety of <foo> for gcc and MSVC".
stl_headers = [
    "new",
    # FIXME: these headers haven't been reviewed yet, but we use them
    # unsafely in Gecko, so we might as well prevent them from
    # throwing exceptions
    "algorithm",
    "atomic",
    "cassert",
    "climits",
    "cmath",
    "condition_variable",
    "cstdarg",
    "cstdio",
    "cstdlib",
    "cstring",
    "cwchar",
    "deque",
    "functional",
    "ios",
    "iosfwd",
    "iostream",
    "istream",
    "iterator",
    "limits",
    "list",
    "map",
    "memory",
    "mutex",
    "ostream",
    "regex",
    "set",
    "shared_mutex",
    "stack",
    "string",
    "thread",
    "tuple",
    "type_traits",
    "unordered_map",
    "unordered_set",
    "utility",
    "vector",
    "xutility",
]
