[tool.ruff]
line-length = 99
# See https://beta.ruff.rs/docs/rules/ for a full list of rules.
lint.select = [
  "E", "W",    # pycodestyle
  "F",         # pyflakes
  "I",         # isort
  "PL",        # pylint
]
lint.ignore = [
  # These should be triaged and either fixed or moved to the list below.
  "E713", "E714",

  # These are intentionally ignored.
  "E741",      # ambiguous-variable-name
  "PLR09",     # too-many-return-statements, too-many-branches, too-many-arguments, too-many-statements
  "PLR2004",   # magic-value-comparison
  "PLC0206",   # dict-index-missing-items

  # These are handled by black.
  "E1", "E4", "E5", "W2", "W5"
]
builtins = ["gdb"]
exclude = [
  # These paths should be triaged and either fixed or moved to the list below.
  "devtools/shared",
  "dom/bindings/Codegen.py",
  "dom/bindings/parser/WebIDL.py",
  "dom/bindings/parser/tests/test_arraybuffer.py",
  "dom/bindings/parser/tests/test_securecontext_extended_attribute.py",
  "gfx/tests",
  "ipc/ipdl/ipdl",
  "layout/base/tests/marionette",
  "layout/reftests/border-image",
  "layout/reftests/fonts",
  "layout/reftests/w3c-css",
  "layout/style",
  "media/libdav1d/generate_source.py",
  "moz.configure",
  "netwerk/dns/prepare_tlds.py",
  "netwerk/protocol/http/make_incoming_tables.py",
  "python/l10n/fluent_migrations",
  "security/manager/ssl/tests/unit",
  "servo/components/style",
  "testing/condprofile/condprof/android.py",
  "testing/condprofile/condprof/creator.py",
  "testing/condprofile/condprof/desktop.py",
  "testing/condprofile/condprof/runner.py",
  "testing/condprofile/condprof/scenarii/heavy.py",
  "testing/condprofile/condprof/scenarii/settled.py",
  "testing/condprofile/condprof/scenarii/synced.p",
  "testing/condprofile/condprof/helpers.py",
  "testing/jsshell/benchmark.py",
  "testing/marionette/mach_commands.py",
  "testing/mozharness/docs",
  "testing/mozharness/examples",
  "testing/mozharness/external_tools",
  "testing/mozharness/mach_commands.py",
  "testing/mozharness/manifestparser",
  "testing/mozharness/mozprocess",
  "testing/mozharness/setup.py",
  "testing/parse_build_tests_ccov.py",
  "testing/runtimes/writeruntimes.py",
  "testing/tools/iceserver/iceserver.py",
  "testing/tools/websocketprocessbridge/websocketprocessbridge.py",
  "toolkit/components/featuregates",
  "toolkit/content/tests/chrome/file_about_networking_wsh.py",
  "toolkit/library/build/dependentlibs.py",
  "toolkit/locales/generate_update_locale.py",
  "toolkit/mozapps",
  "toolkit/moz.configure",
  "toolkit/nss.configure",

  # mako files are not really python files
  "*.mako.py",

  # These paths are intentionally excluded (not necessarily for good reason).
  "build/moz.configure/*.configure",
  "build/pymake/",
  "browser/extensions/mortar/ppapi/",
  "browser/moz.configure",
  "dom/canvas/test/webgl-conf/checkout/closure-library/",
  "editor/libeditor/tests/browserscope/",
  "intl/icu/",
  "ipc/chromium/src/third_party/",
  "js/*.configure",
  "gfx/angle/",
  "gfx/harfbuzz",
  "gfx/skia/",
  "memory/moz.configure",
  "mobile/android/*.configure",
  "node_modules",
  "python/mozbuild/mozbuild/test/configure/data",
  "security/nss/",
  "testing/marionette/harness/marionette_harness/runner/mixins",
  "testing/marionette/harness/marionette_harness/tests",
  "testing/mochitest/pywebsocket3",
  "testing/mozharness/configs/test/test_malformed.py",
  "testing/web-platform/tests",
  "tools/lint/test/files",
  "tools/crashreporter/*.configure",
  ".ycm_extra_conf.py",
]

[tool.ruff.lint.per-file-ignores]
# These paths are intentionally excluded.
"dom/bindings/Configuration.py" = ["PLC3002"]
"ipc/ipdl/*" = ["F403", "F405"]
"layout/tools/reftest/selftest/conftest.py" = ["F811"]
# cpp_eclipse has a lot of multi-line embedded XML which exceeds line length
"python/mozbuild/mozbuild/backend/cpp_eclipse.py" = ["E501"]
"testing/firefox-ui/**/__init__.py" = ["F401"]
"testing/marionette/**/__init__.py" = ["F401"]
"testing/mochitest/tests/python/conftest.py" = ["F811"]
"testing/mozbase/manifestparser/tests/test_filters.py" = ["E731"]
"testing/mozbase/mozinfo/mozinfo/mozinfo.py" = ["PLE0605"]
"testing/mozbase/mozlog/tests/test_formatters.py" = ["E501"]
"testing/mozharness/configs/*" = ["E501"]
"**/*.configure" = ["F821"]
# These paths contain Python-2 only syntax.
"build/compare-mozconfig/compare-mozconfigs.py" = ["F821"]
"build/midl.py" = ["F821"]
"build/pgo/genpgocert.py" = ["F821"]
"config/MozZipFile.py" = ["F821"]
"config/check_source_count.py" = ["F821"]
"config/tests/unitMozZipFile.py" = ["F821"]
"ipc/pull-chromium.py" = ["F633"]
"js/src/**" = ["F633", "F821"]
"python/mozbuild/mozbuild/action/dump_env.py" = ["F821"]
"python/mozbuild/mozbuild/dotproperties.py" = ["F821"]
"python/mozbuild/mozbuild/testing.py" = ["F821"]
"python/mozbuild/mozbuild/util.py" = ["F821"]
"testing/mozharness/mozharness/mozilla/testing/android.py" = ["F821"]
"testing/mochitest/runtests.py" = ["F821"]
