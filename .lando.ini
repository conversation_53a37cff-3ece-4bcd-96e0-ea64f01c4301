[autoformat]
# Set `enabled = True/False` to enable/disable Lando automated code formatting.
enabled = True

[lando-prod]
# Lando production Auth0 configuration.
auth0_domain = auth.mozilla.auth0.com
auth0_client_id = ccpmWbDAMz1pxHIWF4vqkdr0ScdgDyyM
auth0_audience = https://api.lando.services.mozilla.com
auth0_scope = openid email profile lando https://sso.mozilla.com/claim/groups
api_domain = api.lando.services.mozilla.com

[lando-dev]
# Lando dev server Auth0 configuration.
auth0_domain = auth.mozilla.auth0.com
auth0_client_id = TAtuZwbJd4SRtWg0YznfS1YYCatOvvnX
auth0_audience = https://api.lando.devsvcdev.mozaws.net
auth0_scope = openid email profile lando https://sso.mozilla.com/claim/groups
api_domain = api.dev.lando.nonprod.cloudops.mozgcp.net
